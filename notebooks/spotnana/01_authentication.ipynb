{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Spotnana Client Tutorial\n", "\n", "Learn to use the new `SpotnanaClient` for authentication and API calls."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup\n", "\n", "Required environment variables:\n", "- `SPOTNANA_HOST`\n", "- `SPOTNANA_CLIENT_ID` \n", "- `SPOTNANA_CLIENT_SECRET`"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import time\n", "\n", "# Add project root to path (notebook-compatible)\n", "# Look for pyproject.toml to find the project root\n", "current_dir = os.getcwd()\n", "while current_dir != os.path.dirname(current_dir):  # Stop at filesystem root\n", "    if os.path.exists(os.path.join(current_dir, \"pyproject.toml\")):\n", "        sys.path.insert(0, current_dir)\n", "        break\n", "    current_dir = os.path.dirname(current_dir)\n", "else:\n", "    # Fallback: assume we're in notebooks/spotnana/ directory\n", "    sys.path.insert(0, os.path.abspath(\"../..\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize Client"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initializing logger in JSON mode...\n", "Base URL: https://api-ext-sboxmeta.partners.spotnana.com\n", "Company ID: 4ecabb34-5eb3-4192-a1c8-c634a151dc41\n", "Client ID: apiuser@...\n"]}], "source": ["from server.api_clients.spotnana import SpotnanaClient\n", "\n", "# Create client instance\n", "client = SpotnanaClient()\n", "\n", "print(f\"Base URL: {client.base_url}\")\n", "print(f\"Company ID: {client.company_id}\")\n", "print(f\"Client ID: {client.client_id[:8]}...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Authentication"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["{\"event\": \"<PERSON>nana authentication successful, expires at 2025-07-15 14:50:38.977295\", \"timestamp\": \"2025-07-14 14:50:38\", \"level\": \"info\", \"func_name\": \"authenticate\", \"filename\": \"client.py\", \"lineno\": 83}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Authentication time: 0.629s\n", "Authenticated: True\n", "Token expires: 2025-07-15 14:50:38.977295\n"]}], "source": ["# Authenticate\n", "start = time.time()\n", "await client.authenticate()\n", "auth_time = time.time() - start\n", "\n", "print(f\"Authentication time: {auth_time:.3f}s\")\n", "print(f\"Authenticated: {client.is_authenticated}\")\n", "if not client.token_info:\n", "    print(\"No token info\")\n", "else:\n", "    print(f\"Token expires: {client.token_info.expires_at}\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 4}