# Notebooks

This directory contains interactive notebooks for exploring and demonstrating various aspects of the Otto system.

## Structure

- `spotnana/` - Jupyter notebooks showcasing Spotnana API interactions and client usage

## Spotnana Notebooks

The Spotnana notebooks demonstrate:

1. **Authentication & Client Usage** (`01_authentication.ipynb`) - Learn to use `SpotnanaClient` for authentication and API calls

Each notebook provides practical examples for working with the Spotnana API client.