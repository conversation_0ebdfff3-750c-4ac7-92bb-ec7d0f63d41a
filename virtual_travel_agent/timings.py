import time
from collections import defaultdict

from server.utils.logger import logger
from server.utils.settings import settings
from virtual_travel_agent.helpers import (
    pr<PERSON><PERSON>ck,
    pr<PERSON>yan,
    pr<PERSON><PERSON>,
    prL<PERSON><PERSON>ray,
    prLightPurple,
    pr<PERSON><PERSON>ple,
    prRed,
    pr<PERSON><PERSON>w,
)

color_func_map = defaultdict(
    lambda: prBlack,
    {
        "yellow": pr<PERSON><PERSON><PERSON>,
        "red": prRed,
        "green": pr<PERSON><PERSON>,
        "purple": pr<PERSON><PERSON><PERSON>,
        "lightpurple": prLightPurple,
        "cyan": pr<PERSON>yan,
        "gray": prLightGray,
        "black": prBlack,
    },
)


class Timings:
    def __init__(self, name: str):
        self.name = name
        self.start_time = time.perf_counter_ns()

    def get_elapsed_time(self):
        end_time = time.perf_counter_ns()
        duration_ns = end_time - self.start_time
        return duration_ns / 1_000_000_000  # Convert nanoseconds to seconds

    def print_timing(self, color: str):
        duration_s = self.get_elapsed_time()
        prFunc = color_func_map[color.lower()]
        prFunc(f"{self.name} took {duration_s} seconds")


class LatencyTimer:
    def __init__(self, name: str):
        self.name = name
        self.start_time = time.perf_counter_ns()

    def end(self, tag: str | None = None):
        from server.utils.cloudwatch_metrics import metrics_service

        end_time = time.perf_counter_ns()
        duration_ns = end_time - self.start_time
        duration_s = duration_ns / 1_000_000_000  # Convert nanoseconds to seconds
        if settings.is_local:
            logger.info(f"{self.name} {tag} took {duration_s} seconds")
        metrics_service.aws_send_metric_data({self.name: {"data": {"elapsed_time": duration_s, "tag": tag or ""}}})
