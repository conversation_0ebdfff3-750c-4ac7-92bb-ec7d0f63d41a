import math
from dataclasses import dataclass
from typing import Dict, List

from bs4 import BeautifulSoup
from temporalio import activity, workflow

with workflow.unsafe.imports_passed_through():
    import json
    from copy import deepcopy
    from datetime import timed<PERSON><PERSON>
    from typing import Any, cast

    import markdown2
    from jinja2 import Environment, FileSystemLoader

    from baml_client import b
    from front_of_house_agent.common_models import FlightSearchSource
    from front_of_house_agent.front_of_house_agent import FrontOfHouseAgent
    from server.database.models.chat_thread import ChatThread
    from server.database.models.user import User as UserDB
    from server.schemas.authenticate.user import User
    from server.utils.async_requests import make_post_request_with_any_data
    from server.utils.logger import logger
    from server.utils.settings import settings
    from server.utils.websocket_no_op import partial_no_op_with_without_logger


env = Environment(loader=FileSystemLoader("email_templates"))


@dataclass
class MailgunWorkflowInput:
    user_id: int
    thread_id: int
    sender: str
    recipient: str
    subject: str
    stripped_text: str
    body_plain: str | None
    message_id: str
    thread_title: str


def calculate_static_map_zoom(accommodations: List[Dict], map_width_px=654, map_height_px=197) -> int:
    ZOOM_MAX = 17
    ZOOM_MIN = 10
    ZOOM_DEFAULT = 13
    PADDING_RATIO = 0.4

    try:
        lats = [float(a["mapMarker"]["coordinates"]["lat"]) for a in accommodations]
        lngs = [float(a["mapMarker"]["coordinates"]["lng"]) for a in accommodations]

        if len(lats) <= 1:
            return ZOOM_DEFAULT

        min_lat, max_lat = min(lats), max(lats)
        min_lng, max_lng = min(lngs), max(lngs)

        # Calculate the span in degrees
        lat_span = max_lat - min_lat
        lng_span = max_lng - min_lng

        # Add padding
        lat_span *= 1 + PADDING_RATIO
        lng_span *= 1 + PADDING_RATIO

        # Zoom level calculation based on degree spans
        # These values are empirically determined for good visual results
        zoom_for_lat = 1
        zoom_for_lng = 1

        # Calculate zoom needed for latitude span
        if lat_span > 0:
            # At zoom level Z, the visible lat span is roughly 180 / (2^Z)
            zoom_for_lat = math.log2(180 / lat_span)

        # Calculate zoom needed for longitude span
        if lng_span > 0:
            # At zoom level Z, the visible lng span is roughly 360 / (2^Z)
            zoom_for_lng = math.log2(360 / lng_span)

        # Take the minimum to ensure everything fits, then subtract a bit for better spacing
        calculated_zoom = min(zoom_for_lat, zoom_for_lng) - 1

        # Apply reasonable bounds and convert to int
        final_zoom = max(min(int(calculated_zoom), ZOOM_MAX), ZOOM_MIN)

        return final_zoom

    except (ValueError, KeyError) as e:
        logger.error(f"[MAILGUN WEBHOOK] Error calculating static map zoom: {e}")
        return ZOOM_DEFAULT


def style_html_table(markdown_as_html):
    soup = BeautifulSoup(markdown_as_html, "html.parser")
    for table in soup.find_all("table"):
        table["style"] = "border: 1px solid #ccc; border-collapse: collapse; width: 100%;"  # type: ignore

    # Style <th> and <td>
    for tr in soup.find_all("tr"):
        for th in tr.find_all("th"):  # type: ignore
            th["style"] = "border: 1px solid #ccc; padding: 8px; background-color: #f0f0f0;"  # type: ignore
        for td in tr.find_all("td"):  # type: ignore
            td["style"] = "border: 1px solid #ccc; padding: 8px;"  # type: ignore

    return str(soup)


def insert_original_message(response_html: str, original_text: str) -> str:
    soup = BeautifulSoup(response_html, "html.parser")
    blockquote_html = soup.new_tag("blockquote")
    blockquote_html["style"] = "margin:0 0 0 .8ex; border-left:1px #ccc solid; padding-left:1ex;"

    for line in original_text.strip().splitlines():
        p_tag = soup.new_tag("p")
        p_tag.string = line
        blockquote_html.append(p_tag)

    notice = soup.new_tag("p")
    notice.string = "--- Original message below ---"
    notice["style"] = "color: #6b7280; font-size: 0.875rem; margin-top: 24px;"
    body = soup.body
    if body:
        body.append(notice)
        body.append(blockquote_html)

    return str(soup)


@activity.defn
async def execute_foh_agent(input_data: MailgunWorkflowInput) -> dict:
    from server.utils.logger import Logger

    Logger.bind_log_context(
        trip_id=input_data.thread_id,
        user_id=input_data.user_id,
    )
    user_in_db = await UserDB.from_id(input_data.user_id)

    if user_in_db is None:
        logger.error(f"[MAILGUN WORKFLOW] User not found for id: {input_data.user_id}")
        raise ValueError(f"User not found for email: {input_data.sender}")

    trip_user = User.from_orm_user(user_in_db)

    thread = await ChatThread.from_id(input_data.thread_id, include_deleted=True)
    if thread is None:
        logger.error(f"[MAILGUN WORKFLOW] Thread not found: {input_data.thread_id}")
        raise ValueError(f"Thread not found: {input_data.thread_id}")

    agent = FrontOfHouseAgent(
        thread,
        partial_no_op_with_without_logger,
        None,
        trip_user,
        None,
        None,
        email_mode=True,
    )
    agent.travel_context.flight_select_result.search_source = FlightSearchSource.FALLBACK_SPOTNANA

    reply_list = input_data.stripped_text.splitlines()
    reply = "\n".join(reply_list).strip()

    history_messages = await agent.history.persisted_messages
    previous_message = str(history_messages[-1].content) if len(history_messages) > 0 else None

    if settings.EMAIL_REPHRASE_ENABLED and previous_message:
        reply = await b.RephraseEmailReply(previous_message, reply, baml_options={"collector": logger.collector})
        logger.log_baml()

    messages = await agent.run(reply)

    email_cards = {}
    other_options = {"accommodations": [], "flights": []}
    for message in reversed(messages):
        if message.get("isBotMessage", False) and message.get("accommodations", []):
            accommodations = cast(list[dict[str, Any]], message["accommodations"])
            other_options["accommodations"].extend(deepcopy(accommodations))
            for idx, accommodation in enumerate(accommodations[:2]):
                accommodation["rooms"] = accommodation.get("rooms", [])[:1]
                other_options["accommodations"][idx]["rooms"] = other_options["accommodations"][idx]["rooms"][1:]
            email_cards["accommodations"] = message["accommodations"] = accommodations[:2]
        elif message.get("isBotMessage", False) and message.get("flights", []):
            flights = cast(list[dict[str, Any]], message["flights"])
            email_cards["flights"] = message["flights"] = flights[:2]
            other_options["flights"].extend(deepcopy(flights[2:]))
        elif message.get("isBotMessage", False) and message.get("flightCombos", []):
            flight_options = cast(list[dict[str, Any]], message["flightCombos"])
            email_cards["flight_combos"] = flight_options

    logger.info(f"[MAILGUN WORKFLOW] FOH agent completed for thread: {input_data.thread_id}")
    return {"messages": messages, "email_cards": email_cards, "other_options": other_options}


@activity.defn
async def send_email_response(input_data: MailgunWorkflowInput, agent_response: dict) -> dict[str, Any]:
    from server.utils.logger import Logger

    Logger.bind_log_context(
        trip_id=input_data.thread_id,
        user_id=input_data.user_id,
    )

    messages = agent_response["messages"]
    email_cards = agent_response["email_cards"]
    other_options = agent_response["other_options"]

    r = await b.SummaryResponseForEmail(
        original_input=json.dumps(messages),
        table_format=False,
        other_options=json.dumps(other_options),
        self_intro=settings.OTTO_SELF_INTRO,
        baml_options={"collector": logger.collector},
    )
    logger.log_baml()

    summary_as_html = markdown2.markdown(r.summary, extras=["task_list", "tables"])
    summary_as_html = style_html_table(summary_as_html)

    cards_html = ""
    template_mapping = {
        "flights": "flight_oneway_card.html",
        "flight_combos": "flight_roundtrip_card.html",
        "accommodations": "hotel_card.html",
    }

    email_cards["flight_date"] = r.flight_date or ""
    email_cards["hotel_date"] = r.hotel_date or ""

    if "accommodations" in email_cards:
        email_cards["gmap_zoom_level"] = calculate_static_map_zoom(email_cards["accommodations"], 654, 197)
        logger.info(f"[MAILGUN WORKFLOW] Calculated static map zoom level: {email_cards['gmap_zoom_level']}")

    for card_type, template_name in template_mapping.items():
        if email_cards.get(card_type):
            try:
                cards_template = env.get_template(template_name)
                cards_html += cards_template.render(email_cards)
            except Exception as e:
                logger.error(f"[MAILGUN WORKFLOW] Error rendering {card_type} template: {e}")

    ending_as_html = markdown2.markdown(r.ending, extras=["task_list", "tables"])
    ending_as_html = style_html_table(ending_as_html)

    context = {
        "trip_title": input_data.thread_title,
        "trip_id": input_data.thread_id,
        "base_url": settings.CLIENT_DOMAIN,
        "summary_html": summary_as_html,
        "cards_html": cards_html,
        "eding_html": ending_as_html,
    }

    template = env.get_template("response_base.html")
    response_body_as_html = template.render(context)
    response_body_as_html = insert_original_message(response_body_as_html, input_data.stripped_text)

    response = await make_post_request_with_any_data(
        f"https://api.mailgun.net/v3/{settings.MAILGUN_REPLYTO_EMAIL_DOMAIN}/messages",
        data={
            "from": f"Otto <{input_data.recipient}>",
            "to": [input_data.sender],
            "subject": f"Re: {input_data.subject}",
            "html": response_body_as_html,
            "h:In-Reply-To": input_data.message_id,
            "h:References": input_data.message_id,
            "h:Reply-To": f"{settings.MAILGUN_REPLYTO_EMAIL_PREFIX}+{settings.EMAIL_ADDRESS_AFTER_PLUS_SIGN_PREFIX}{input_data.thread_id}@{settings.MAILGUN_REPLYTO_EMAIL_DOMAIN}",
        },
        auth=("api", settings.MAIL_GUN_API_KEY or ""),
    )
    return response


@workflow.defn
class MailgunEmailWorkflow:
    @workflow.run
    async def run(self, input_data: MailgunWorkflowInput) -> dict[str, Any]:
        from server.utils.logger import Logger

        Logger.bind_log_context(
            trip_id=input_data.thread_id,
            user_id=input_data.user_id,
        )
        logger.info(
            f"[MAILGUN WORKFLOW] Starting workflow for user {input_data.user_id}, thread {input_data.thread_id}"
        )

        agent_response = await workflow.execute_activity(
            execute_foh_agent,
            input_data,
            schedule_to_close_timeout=timedelta(minutes=5),
        )

        send_res = await workflow.execute_activity(
            send_email_response,
            args=[input_data, agent_response],
            schedule_to_close_timeout=timedelta(minutes=2),
        )

        logger.info(
            f"[MAILGUN WORKFLOW] Completed workflow for user {input_data.user_id}, thread {input_data.thread_id}"
        )
        return send_res
