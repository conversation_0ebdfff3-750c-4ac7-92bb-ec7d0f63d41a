import asyncio
import json
import uuid
from collections import defaultdict
from io import <PERSON><PERSON>
from typing import Any, Dict, List

from langchain_core.messages import AIMessage, BaseMessage, FunctionMessage

from baml_client import b
from baml_client.types import (
    CancelHotelResponseWithStep,
    HotelBookingResponse,
    HotelOrderPreview,
    PaymentTiming,
)
from hotel_agent.booking_dot_com_models import BookingStatus
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.database.models.bookings import Booking
from server.database.models.user import User as UserDB
from server.database.models.user import UserRole
from server.schemas.authenticate.user import User
from server.services.google_maps_api.get_lat_long import get_lat_long_from_loc_str
from server.services.memory.trips.memory_modules.bookings_memory import BookingsMemory
from server.services.trips.bookings import get_accommodation_booking
from server.services.user.user_preferences import get_user_preferences
from server.utils.logger import logger
from server.utils.message_constants import HOTEL_SKELETON_MESSAGES
from server.utils.settings import settings
from server.utils.spotnana_api import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, spotnana_api
from virtual_travel_agent.common_models import <PERSON>Error
from virtual_travel_agent.helpers import (
    console_masks,
    get_current_date_string,
    get_current_datetime_string,
)
from virtual_travel_agent.timings import Timings

hotel_log_mask = console_masks["hotel"]
one_mile_in_meters = 1609
distance_from_target_location_threshold = one_mile_in_meters * 3
number_of_hotels_considered_by_the_model = 10


class SpotnanaHotelsHelper:
    def __init__(
        self,
        user: User,
        thread_id: int,
        timezone: str | None = None,
    ):
        self.input_tokens_counter = 0
        self.output_tokens_counter = 0
        self.user = user
        self.thread_id = thread_id
        self.timezone = timezone
        self.bookings_memory = BookingsMemory(user_id=str(user.id), thread_id=str(thread_id))

        self.topic: str = "Hotels"
        self.room_title = ""
        self.hotel_name = ""
        self.validated_price = 0
        self.currency: str = "USD"
        self.did_hotel_search = False
        self.gathered_required_information_for_hotel_search = False
        self.traveler_updated_search_criteria = False
        self.hotel_selected = False
        self.room_product_selected = False
        self.did_order_preview = False
        self.did_order_create = False
        self.order_token = None
        self.cancel_hotel_state: CancelHotelResponseWithStep = CancelHotelResponseWithStep()

    def get_did_hotel_search(self) -> bool:
        return self.did_hotel_search

    def get_gathered_required_information_for_hotel_search(self) -> bool:
        return self.gathered_required_information_for_hotel_search

    def get_traveler_updated_search_criteria(self) -> bool:
        return self.traveler_updated_search_criteria

    def get_hotel_selected(self) -> bool:
        return self.hotel_selected

    def get_room_product_selected(self) -> bool:
        return self.room_product_selected

    def get_did_order_preview(self) -> bool:
        return self.did_order_preview

    def get_did_order_create(self) -> bool:
        return self.did_order_create

    # TODO: To make the linter happy, will try moving the spotnana specific methods to a the HotelsHelper class
    async def persist_converse_hotel_state(self):
        pass

    def resolve_lat_long(self, location_latitude_longitude_str):
        try:
            lat_long_dict = {}
            if location_latitude_longitude_str is not None and location_latitude_longitude_str != "":
                if type(location_latitude_longitude_str) is str:
                    logger.info(f"location_latitude_longitude: {location_latitude_longitude_str}", mask=hotel_log_mask)
                    if "," in location_latitude_longitude_str:
                        lat_long_list = location_latitude_longitude_str.split(",", 1)
                        latitude = float(lat_long_list[0])
                        longitude = float(lat_long_list[1])
                        if longitude > 0:  # LLM loses negative value?
                            longitude = longitude * -1
                        lat_long_dict = {"latitude": latitude, "longitude": longitude}

                        return lat_long_dict
                else:
                    raise ValueError(
                        f"location_latitude_longitude is not in str format! {location_latitude_longitude_str}"
                    )
            else:
                raise ValueError("No lat/long provided by the model.")
        except Exception as e:
            logger.error(f"Failed to resolve lat long based on model output, error: {e}", mask=hotel_log_mask)
            pass

        return {}

    async def get_available_hotels(
        self, check_in_date: str, check_out_date: str, lat_long_dict: dict[str, float], radius: float
    ):
        user_id = (await spotnana_api.get_user_by_email(self.user.email)).get("elements", [{}])[0].get("id")

        if user_id is None:
            raise Exception(f"User with email {self.user.email} not found on Spotnana!")

        hotel_search_params = {
            "searchParams": {
                "occupancyDates": {
                    "occupancy": [{"numAdults": 1}],
                    "checkInDate": {"iso8601": f"{check_in_date}T15:00"},
                    "checkOutDate": {"iso8601": f"{check_out_date}T11:00"},
                },
                "searchBy": {
                    "searchType": "COORDINATES",
                    "coordinates": lat_long_dict,
                },
            },
            "filters": {"radius": {"length": radius, "unit": "KM"}},
            "sortOptions": [{"sortBy": "DISTANCE", "sortOrder": "ASCENDING"}],
            "userId": {"userIdType": "USER_ID", "userId": {"id": user_id}},
        }

        with open("search_hotels_request_spotnana.json", "w") as file:
            file.write(json.dumps(hotel_search_params, indent=4))

        hotels = await spotnana_api.search_hotels(hotel_search_params)

        with open("search_hotels_response_spotnana.json", "w") as file:
            file.write(json.dumps(hotels, indent=4))

        return hotels.get("hotels")

    async def get_hotel_rooms_details(self, index, hotel_id):
        hotel_details_params = {"hotelDetailsKey": hotel_id}

        with open(f"hotel_details_request_{index}_spotnana.json", "w") as file:
            file.write(json.dumps(hotel_details_params, indent=4))

        hotel_details = await spotnana_api.get_hotel_details(hotel_details_params)

        with open(f"hotel_details_response_{index}_spotnana.json", "w") as file:
            file.write(json.dumps(hotel_details, indent=4))

        return hotel_details

    async def get_hotels_rooms_details(self, hotel_ids):
        hotels_with_rooms = await asyncio.gather(
            *[self.get_hotel_rooms_details(i, hotel_id) for i, hotel_id in enumerate(hotel_ids)]
        )
        return hotels_with_rooms

    async def validate_price(self, price_validate_key: str):
        hotel_price_validate_params = {"priceValidateKey": price_validate_key}
        with open("hotel_validate_price_request_spotnana.json", "w") as file:
            file.write(json.dumps(hotel_price_validate_params, indent=4))

        response = await spotnana_api.validate_hotel_price(hotel_price_validate_params)

        with open("hotel_validate_price_response_spotnana.json", "w") as file:
            file.write(json.dumps(response, indent=4))

        return response

    async def create_spotnana_trip(self, user_guid: str):
        trip_uuid = uuid.uuid4()
        desc: str = f"Hotel Trip - {trip_uuid}"
        payload = {
            "tripName": desc,
            "tripDescription": desc,
            "userId": {"id": user_guid},
            "registrarId": {"id": user_guid},
        }

        with open("hotel_create_trip_request_spotnana.json", "w") as file:
            file.write(json.dumps(payload, indent=4))

        trip_response = await spotnana_api.create_trip(payload)

        with open("hotel_create_trip_response_spotnana.json", "w") as file:
            file.write(json.dumps(trip_response, indent=4))

        return trip_response.get("id")

    async def create_booking(self, booking_key: str):
        company_admin_user: UserDB | None = None
        if self.user.organization_id:
            company_admin_user = await UserDB.from_organization_id_and_role(
                self.user.organization_id, UserRole.company_admin
            )

        traveler_tasks = [SpotnanaHotelsHelper.get_spotnana_traveler_search_and_read(self.user.email)]
        if company_admin_user:
            traveler_tasks.append(SpotnanaHotelsHelper.get_spotnana_traveler_search_and_read(company_admin_user.email))
        (_, traveler_read), *rest = await asyncio.gather(*traveler_tasks)
        (_, admin_traveler_read) = rest[0] if company_admin_user else (None, None)

        traveler = traveler_read.get("traveler")
        user_guid = traveler_read.get("traveler", {}).get("userOrgId", {}).get("userId", {}).get("id")
        user = await spotnana_api.get_user_details(user_guid)

        assert traveler is not None, "Traveler must present at this point before booking"
        personalInfo = user.get("personalInfo", {})
        businessInfo = user.get("businessInfo", {})
        payment_source_id = SpotnanaHelper.get_payment_source_id(traveler_read, admin_traveler_read)

        trip_id = await self.create_spotnana_trip(user_guid)

        payload = {
            "bookingKey": booking_key,
            "travelers": [
                {
                    "travelerId": {"id": user_guid},
                    "travelerInfo": {"userId": {"id": user_guid}},
                    "travelerType": "ADULT",
                    "title": personalInfo.get("title", None),
                    "name": personalInfo.get("name", ""),
                    "gender": personalInfo.get("gender", ""),
                    "dob": personalInfo.get("dob", ""),
                    "phoneNumber": (
                        personalInfo.get("phoneNumbers", [None])[0] if personalInfo.get("phoneNumbers") else None
                    ),
                    "email": businessInfo.get("email", ""),
                    "address": (personalInfo.get("addresses", [None])[0] if personalInfo.get("addresses") else None),
                }
            ],
            "tripData": {"tripId": {"id": trip_id}},
            "bookingPaymentDetails": {
                "bookingTravelerPaymentDetails": [
                    {
                        "selectedFormOfPayments": [
                            {
                                "paymentItems": [{"fareComponent": []}],
                                "selectedPaymentSources": [{"paymentSourceId": payment_source_id}],
                            }
                        ]
                    }
                ]
            },
            "bookingContact": {
                "emailAddress": businessInfo.get("email", ""),
                "phoneNumber": personalInfo.get("phoneNumbers", [None])[0]
                if personalInfo.get("phoneNumbers")
                else None,
            },
        }

        with open("hotel_create_booking_request_spotnana.json", "w") as file:
            file.write(json.dumps(payload, indent=4))

        response = await spotnana_api.create_hotel_booking(payload)

        with open("hotel_create_booking_response_spotnana.json", "w") as file:
            file.write(json.dumps(response, indent=4))

        return response, trip_id

    def prune_to_cheapest_rooms(self, hotel_rooms: List[dict], number=3) -> List[dict]:
        """Find the N cheapest rooms and get rid of the rest"""
        try:
            new_rooms_list = []
            # clean out rooms that have not product and prices
            for room in hotel_rooms:
                if room.get("price", None) is not None:
                    new_rooms_list.append(room)
                else:
                    # Prune it
                    pass

            sorted_rooms = sorted(new_rooms_list, key=lambda x: x.get("price", {}).get("total", 0))
            return sorted_rooms[:number]

        except Exception as e:
            logger.error(e.args[0], mask=hotel_log_mask)

        return []

    def calculate_total_fare(self, rate_obj: dict[str, Any]) -> float:
        total_fare: float = rate_obj.get("base", {}).get("amount") + rate_obj.get("tax", {}).get("amount")
        if "roomFees" in rate_obj:
            total_fare += sum(
                [float(room_fee.get("amount").get("amount")) for room_fee in rate_obj.get("roomFees", [])]
            )
        return total_fare

    def get_hotel_room_cheapest_rate_option(self, room_rate_options: List[dict[str, Any]]) -> dict | None:
        """Find the cheapest rate option for a room, or the first one if only one is available"""
        if not len(room_rate_options):
            return None
        elif len(room_rate_options) == 1:
            return room_rate_options[0]
        cheapest: float = 0.0
        cheapest_rate_option = None
        for rate_option in room_rate_options:
            total_fare = self.calculate_total_fare(rate_option.get("rateInfo", {}).get("totalRate"))
            if not cheapest_rate_option or cheapest > total_fare:
                cheapest_rate_option = rate_option
                cheapest = total_fare
        if not cheapest_rate_option:
            cheapest_rate_option = room_rate_options[0]
        return cheapest_rate_option

    def map_hotel_room(self, rooms_details):
        rooms_mapped = []
        for room in rooms_details:
            default_room_rate_option = self.get_hotel_room_cheapest_rate_option(room.get("rateOptions", []))
            if not default_room_rate_option:
                logger.warn(f"Skipping room {room.get('description')} as no rate options found", mask=hotel_log_mask)
                continue
            default_room_rate_obj = default_room_rate_option.get("rateInfo", {}).get("totalRate")
            rooms_mapped.append(
                {
                    "id": room.get("roomGroupKey"),
                    "room_id": room.get("roomGroupKey"),
                    "title": room.get("description"),
                    "description": default_room_rate_option.get("description"),
                    "priceValidateKey": default_room_rate_option.get("priceValidateKey"),
                    "price": {
                        "base": default_room_rate_obj.get("base", {}).get("amount", 0),
                        "total": self.calculate_total_fare(default_room_rate_obj),
                    },
                    "room_photo": self.get_primary_image(room.get("imageSets", []), "GUEST_ROOM"),
                }
            )

        return self.prune_to_cheapest_rooms(rooms_mapped)

    def get_primary_image(self, image_sets: List[dict[str, Any]], default_key: str) -> str | None:
        categorized_image_urls: dict[str, List[str]] = defaultdict(list)
        first_image_url: str | None = None
        for image_set in image_sets:
            image_category = image_set.get("category")
            images: List[dict[str, Any]] | None = image_set.get("imageGroup", {}).get("images", [])
            highest_res: dict[str, Any] | None = images[0] if images else None
            if highest_res and images and len(images) > 1:
                for image in images:
                    if image.get("dimensions", {}).get("width", 0) > highest_res.get("dimensions", {}).get("width", 0):
                        highest_res = image
                        break
            image_key: str = image_category or "UNKNOWN CATEGORY"
            if highest_res and highest_res.get("url"):
                categorized_image_urls[image_key].append(str(highest_res.get("url")))
                if not first_image_url:
                    first_image_url = str(highest_res.get("url"))
        # Get the logo one if it exists, otherwise the first one
        primary_image = (
            categorized_image_urls[default_key][0] if default_key in categorized_image_urls else first_image_url
        )
        return primary_image

    def map_hotels_room_details_response(
        self, llm_hotel_options: List[dict[str, Any]], hotels_details: List[dict[str, Any]]
    ):
        hotels_details_dict = {
            int(option.get("hotelSpec", {}).get("hotelId", "").replace("SPOTNANA:", "")): option
            for option in hotels_details
        }

        for option in llm_hotel_options:
            property_id = option["property_id"]
            # Reinject saved metadata
            option["rooms"] = self.map_hotel_room(hotels_details_dict[property_id].get("rooms", []))
            option["dropoff_url"] = None
            option["image_url"] = (
                self.get_primary_image(
                    hotels_details_dict[property_id].get("hotelSpec", {}).get("imageSets", []), "LOGO"
                )
                if len(hotels_details_dict[property_id].get("hotelSpec", {}).get("imageSets")) > 0
                else None
            )
            option["property_name"] = hotels_details_dict[property_id].get("hotelSpec", {}).get("name")
            option["gps_coordinates"] = (
                f"{hotels_details_dict[property_id].get('hotelSpec', {}).get('coordinates', {}).get('latitude')}, {hotels_details_dict[property_id].get('hotelSpec', {}).get('coordinates', {}).get('longitude')}"
            )
            option["description"] = (
                (hotels_details_dict[property_id].get("hotelSpec", {}).get("descriptions", [{}])[0].get("value"))
                if len(hotels_details_dict[property_id].get("hotelSpec", {}).get("descriptions")) > 0
                else None
            )
            option["amenities"] = [
                amenities.get("additionalInfo")
                for amenities in hotels_details_dict[property_id].get("hotelSpec", {}).get("amenities")
            ]
            option["check_in_time"] = (
                hotels_details_dict[property_id].get("hotelSpec", {}).get("checkinTime", {}).get("iso8601")
            )
            option["check_out_time"] = (
                hotels_details_dict[property_id].get("hotelSpec", {}).get("checkoutTime", {}).get("iso8601")
            )
            # probably haves
            option["hotel_class"] = str(
                hotels_details_dict[property_id]
                .get("hotelSpec", {})
                .get("starRating", {})
                .get("starRating", "Not available")
            )
            option["overall_rating"] = "Not available"

        return llm_hotel_options

    def text_from_dict(self, hotels: List[dict]) -> str:
        # Write out the dictionary to a string, one field at a time

        try:
            output = StringIO()

            for i, hotel in enumerate(hotels):
                hotel_name = ""
                try:
                    hotel_name = hotel.get("hotelSpec", {}).get("name")
                    output.write(f"{hotel_name} \n")
                    output.write(f"hotel ID: {hotel['id']} \n")
                    descriptions = hotel.get("hotelSpec", {}).get("descriptions", [])
                    if len(descriptions) > 0 and "value" in descriptions[0]:
                        description = descriptions[0].get("value")
                        output.write(f"{description} \n")
                    output.write(" ".join(hotel.get("hotelSpec", {}).get("address", {}).get("addressLines", [])))
                    output.write(f"{hotel.get('hotelSpec', {}).get('address', {}).get('locality')} \n")
                    output.write(f"{hotel.get('hotelSpec', {}).get('address', {}).get('postalCode')} \n")
                    output.write(
                        f"Lat/Long: {hotel.get('hotelSpec', {}).get('coordinates')['latitude']}, {hotel.get('hotelSpec', {}).get('coordinates')['longitude']} \n"
                    )
                    # districts = hotel.get("districts", None)
                    # if districts is not None:
                    #     districts_str = ", ".join(districts)
                    #     output.write(f"Districts: {districts_str} \n")

                    # output.write(f"Review score: {hotel['rating']['review_score']} \n")
                    output.write(f"Stars: {hotel.get('hotelSpec', {}).get('starRating', {}).get('starRating')} \n")
                    # output.write(f"Check in time: {hotel['checkin_checkout_times']['checkin_from']} \n")
                    # output.write(f"Check out time: {hotel['checkin_checkout_times']['checkout_to']} \n")
                    facilities_str = ", ".join(
                        [amenities.get("additionalInfo") for amenities in hotel.get("hotelSpec", {}).get("amenities")]
                    )
                    output.write(f"Facilities: {facilities_str} \n")
                    output.write(
                        f"Distance: {hotel.get('distance', {}).get('length')} {hotel.get('distance', {}).get('unit')} \n"
                    )
                    output.write("\n")
                except Exception as e:
                    logger.error(
                        f"Ignoring error when processing {i}th hotel ({hotel_name or '<unknown hotel name>'}): {e}",
                        mask=hotel_log_mask,
                    )
                    pass

            # Write the CSV content to a file
            with open("hotel_csvdata_spotnana.txt", "w", newline="") as file:
                file.write(output.getvalue())

            return output.getvalue()

        except Exception as e:
            logger.error(f"text_from_dict failed to process hotel data with error: {e}", mask=hotel_log_mask)

        return ""

    def filter_raw_tool_output(self, hotels: List[dict]) -> str:
        try:
            hotel_list = []
            for hotel in hotels:
                name = hotel.get("hotelSpec", {}).get("name")
                gps = hotel.get("hotelSpec", {}).get("coordinates", {})
                lat = gps.get("latitude")
                long = gps.get("longitude")
                hotel_dict = {
                    "name": name,
                    "price": "No price available",
                    "lat": lat,
                    "long": long,
                }
                hotel_list.append(hotel_dict)
            return_dict = {"hotel_choices": hotel_list}
            json_str = json.dumps(return_dict)
            return json_str
        except Exception as e:
            logger.error(f"Failed to generate filtered raw tool output, error: {e}", mask=hotel_log_mask)
            pass

        return ""

    async def hotels_hitl_runnable_function(self, state):
        response = None
        try:
            messages = state["messages"]

            # Convert our message buffer
            message_buffer_strs = get_message_buffer_as_strings(messages)
            travel_context_str = state["travel_context"].model_dump_json()
            trip_memories = state.get("trip_memories", [])
            t = Timings("BAML: ConverseHotelHITL")
            response = await b.ConverseHotelHITL(
                travel_context=travel_context_str,
                messages=message_buffer_strs,
                current_date=get_current_date_string(self.timezone),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                user_name=self.user.name,
                trip_memories=trip_memories,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("yellow")
            logger.log_baml()

            # Use google maps api to get the lat long of the location insteads
            # of the LLM's guess
            if response.location_neighborhoods_districts_landmarks and response.city:
                location_name = f"{response.location_neighborhoods_districts_landmarks}, {response.city}"
                lat_long_str = await get_lat_long_from_loc_str(location_name)
                logger.info(
                    f"Location lat long of {location_name}:\n"
                    f"from Google Maps API: {lat_long_str}\n"
                    f"from LLM: {response.location_latitude_longitude}",
                    mask=hotel_log_mask,
                )
                if lat_long_str:
                    response.location_latitude_longitude = lat_long_str

            new_message = AIMessage(content="")
            json_representation = response.model_dump_json()
            new_message.additional_kwargs = {
                "function_call": {
                    "arguments": json_representation,
                    "name": "HITLConversationResponse",
                }
            }

            self.gathered_required_information_for_hotel_search = (
                response.gathered_required_information_for_hotel_search
            )
            self.traveler_updated_search_criteria = response.traveler_updated_search_criteria

            # Keep an eye on these - what value gies the model assign before it
            # has a value
            hotel_selection = response.hotel_selection
            if hotel_selection != "":
                self.hotel_selected = True
                self.hotel_name = hotel_selection

            # get the property_id, we'll need it later when booking
            self.property_id = response.property_id  # property_id is an int
            self.check_in_date = response.check_in_date
            self.check_out_date = response.check_out_date

            # get the room_product_id, we'll need it later when booking
            # Keep an eye on these - what value gives the model assign before
            # it has a value
            self.room_product_id = response.room_product_id
            if (
                self.room_product_id is not None
                and self.room_product_id != ""
                and self.room_product_id.lower() != "none"
            ):
                self.room_product_selected = True
                self.room_title = response.room_title

            return {
                "messages": [new_message],
                "current": "hotel_hitl_agent",
                "current_topic": self.topic,
                "model": response,
            }

        except Exception as e:
            # logger.error(e.args[0], mask=hotel_log_mask)
            # error_msg = FunctionMessage(content=str(e.args[0]), name="hotel_hitl")
            # error_msg.additional_kwargs = {
            #     "function_call": {
            #         "arguments": {"error_response": e.args[0]},
            #         "name": "Error Message",
            #     }
            # }
            return {
                "errors": [AgentError.from_exception("hotel_hitl_agent", e)],
                "current": "hotel_hitl_agent",
                "current_topic": self.topic,
                "model": response or {},
            }

    async def hotels_search_runnable_function(self, state):
        response = None
        try:
            messages = state["messages"]
            last_message: BaseMessage = messages[-1]
            tool_input_str = last_message.additional_kwargs["function_call"]["arguments"]

            json_dict: Dict[str, Any] = json.loads(tool_input_str)

            # Will need these later for availability and pricing
            check_in_date = json_dict["check_in_date"]
            check_out_date = json_dict["check_out_date"]

            location_neighborhoods_districts_landmarks = json_dict.get(
                "location_neighborhoods_districts_landmarks", None
            )
            location_latitude_longitude_str = json_dict.get("location_latitude_longitude", None)
            input_hotel_search_radius = json_dict.get("hotel_search_radius", distance_from_target_location_threshold)

            lat_long_dict = self.resolve_lat_long(location_latitude_longitude_str)
            if lat_long_dict == {}:
                raise ValueError(
                    f"Couldn't resolve latitude/longitude coordinates: {location_latitude_longitude_str}, {location_neighborhoods_districts_landmarks}."
                )

            t = Timings("Spotnana: get_available_hotels")
            hotels: List[Dict[str, Any]] = (
                await self.get_available_hotels(
                    check_in_date, check_out_date, lat_long_dict, input_hotel_search_radius / 1000
                )
                or []
            )
            t.print_timing("yellow")
            count_of_available_hotels = len(hotels)
            self.websocket_send_message(
                message={
                    "type": "hotels_skeleton_async",
                    "isBotMessage": True,
                    "expectResponse": False,
                    "text": HOTEL_SKELETON_MESSAGES["NARROWING_DOWN"].format(count=count_of_available_hotels),
                }
            )
            hotels = hotels[:number_of_hotels_considered_by_the_model]

            if len(hotels) == 0:
                raise ValueError(
                    "I couldn’t find any available hotels for your selected dates. Try adjusting your dates to see more options."
                )

            hotels_id_to_details_key_map = {}
            for hotel in hotels:
                hotel_id = int(hotel.get("hotelSpec", {}).get("hotelId", "").replace("SPOTNANA:", ""))
                hotel["id"] = hotel_id
                hotels_id_to_details_key_map[hotel_id] = hotel.get("hotelDetailsKey", "")

            logger.info(f"Sending {len(hotels)} hotels to the model for analysis and selection.", mask=hotel_log_mask)

            hotels_str = self.text_from_dict(hotels)
            if hotels_str is None:
                raise ValueError("Failed to convert JSON to CSV")

            # Convert our message buffer
            message_buffer_strs = get_message_buffer_as_strings(messages)
            travel_context_str = state["travel_context"].model_dump_json()
            user_preferences = await get_user_preferences(self.user.id)
            # JTB:  Note that the hotels string, which is sizable, is never
            # a part of the message stack.
            t = Timings("BAML: ConverseHotelSearch")
            response = await b.ConverseHotelSearch(
                hotel_options_data=hotels_str,
                travel_context=travel_context_str,
                preferences=user_preferences,
                messages=message_buffer_strs,
                current_datetime=get_current_datetime_string(self.timezone),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("yellow")
            logger.log_baml()

            new_message = AIMessage(content="")
            json_representation = response.model_dump_json()

            # Rejoin the rooms data into the hotels chosen by the model
            hotel_search_options_schema: Dict[str, Any] = json.loads(json_representation)
            hotel_options = hotel_search_options_schema.get("hotel_options", [])
            # hotel_options = [o for o in hotel_options if o["property_id"] in saved_data]
            if len(hotel_options) == 0:
                raise ValueError(
                    "I couldn’t find any available hotels for your selected dates. Try adjusting your dates to see more options."
                )
            self.websocket_send_message(
                message={
                    "type": "hotels_skeleton_async",
                    "isBotMessage": True,
                    "expectResponse": False,
                    "text": HOTEL_SKELETON_MESSAGES["ROOM_DETAILS"],
                }
            )

            t = Timings("Spotnana: get_hotels_rooms_details")
            hotels_options_with_rooms = await self.get_hotels_rooms_details(
                [hotels_id_to_details_key_map[option["property_id"]] for option in hotel_options]
            )
            t.print_timing("yellow")

            hotel_search_options_schema["hotel_options"] = self.map_hotels_room_details_response(
                hotel_options, hotels_options_with_rooms
            )

            if len(hotel_search_options_schema["hotel_options"]) == 0:
                raise ValueError(
                    "I couldn’t find any available hotels for your selected dates. Try adjusting your dates to see more options."
                )

            # We need this info to display the location target radius
            hotel_search_options_schema["location_lat_long"] = {
                **lat_long_dict,
                "radius": input_hotel_search_radius,
            }

            results_str = json.dumps(hotel_search_options_schema)

            new_message.additional_kwargs = {
                "function_call": {
                    "arguments": results_str,
                    "name": "HotelSearchOptionsResponse",
                }
            }

            new_message.additional_kwargs["raw_tool_output"] = self.filter_raw_tool_output(hotels)

            self.did_hotel_search = True
            self.did_order_preview = False
            return {
                "messages": [new_message],
                "current": "hotel_search_agent",
                "current_topic": self.topic,
                "model": response,
            }

        except Exception as e:
            # logger.error(e.args[0], mask=hotel_log_mask)
            # error_msg = FunctionMessage(content=str(e.args[0]), name="hotel_search")
            # error_msg.additional_kwargs = {
            #     "function_call": {
            #         "arguments": {"error_response": e.args[0]},
            #         "name": "Error Message",
            #     }
            # }
            return {
                "errors": [AgentError.from_exception("hotel_search_agent", e)],
                "current": "hotel_search_agent",
                "current_topic": self.topic,
                "model": response or {},
            }

    async def hotels_validation_runnable_function(self, state):
        response = None
        try:
            model = state["model"].model_dump()

            validated_price_data = await self.validate_price(model.get("price_validate_key"))
            if validated_price_data is None or "bookingKey" not in validated_price_data:
                raise ValueError(
                    "I have problem getting your hotel order validation for you to review. Ask me to book again."
                )

            # data = preview_payload["data"]
            self.order_token = validated_price_data.get("bookingKey")  # we need this for booking
            preview_payload_str = json.dumps(validated_price_data)

            travel_context_str = state["travel_context"].model_dump_json()

            t = Timings("BAML: ConverseHotelValidation")
            response = await b.ConverseHotelValidation(
                preview_order_data=preview_payload_str,
                travel_context=travel_context_str,
                messages=None,
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                payment_timing=PaymentTiming.PAY_AT_THE_PROPERTY,
                cancellation_type=None,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("yellow")
            logger.log_baml()

            new_message = AIMessage(content="")
            json_representation = response.model_dump_json()
            new_message.additional_kwargs = {
                "function_call": {
                    "arguments": json_representation,
                    "name": "HotelValidationResponse",
                }
            }

            logger.info(json_representation, mask=hotel_log_mask)

            self.did_order_preview = True
            self.validated_price = response.validated_price
            self.currency = response.currency

            return {
                "messages": [new_message],
                "current_topic": self.topic,
                "current": "hotel_validation_agent",
                "model": response,
            }

        except Exception as e:
            logger.error(e.args[0], mask=hotel_log_mask)
            # error_msg = FunctionMessage(content=str(e.args[0]), name="hotel_validation")
            # error_msg.additional_kwargs = {
            #     "function_call": {
            #         "arguments": {"error_response": e.args[0]},
            #         "name": "Error Message",
            #     }
            # }
            return {
                "errors": [AgentError.from_exception("hotel_validation_agent", e)],
                "current": "hotel_validation_agent",
                "current_topic": self.topic,
                "model": response or {},
            }

    async def hotels_booking_runnable_function(self, state):
        response = None
        try:
            assert self.order_token is not None, "Order token is required for booking and should be from validation"
            create_payload, spotnana_trip_id = await self.create_booking(self.order_token)

            if create_payload is None:
                raise ValueError("Create booking failed.")

            new_message = AIMessage(content="")
            response = HotelBookingResponse.model_construct(values={})
            if create_payload:  # actual payload from booking.com
                create_payload_str = json.dumps(create_payload)

                travel_context_str = state["travel_context"].model_dump_json()
                order_preview = HotelOrderPreview(
                    property_id=self.property_id or 0,
                    room_product_id=self.room_product_id or "",
                    hotel_name=self.hotel_name,
                    room_title=self.room_title,
                    validated_price=self.validated_price,
                    currency=self.currency,
                )
                t = Timings("BAML: ConverseHotelBooking")
                response = await b.ConverseHotelBooking(
                    order_preview=order_preview,
                    booking_order_data=create_payload_str,
                    travel_context=travel_context_str,
                    self_intro=settings.OTTO_SELF_INTRO,
                    convo_style=settings.OTTO_CONVO_STYLE,
                    baml_options={"collector": logger.collector},
                )
                t.print_timing("yellow")
                logger.log_baml()

                json_representation = response.model_dump_json()
                json_obj = json.loads(json_representation)
                # Tak on some extra info for a full intinerary
                json_obj["spotnana_trip_id"] = spotnana_trip_id
                json_obj["check_in_date"] = self.check_in_date
                json_obj["check_out_date"] = self.check_out_date
                json_obj.update(order_preview.model_dump())
                # json_obj["validated_price"] = self.validated_price
                # json_obj["property_id"] = self.property_id
                # json_obj["room_product_id"] = self.room_product_id
                # json_obj["room_title"] = self.room_title

                # Set this to True as we're at the end of the hotel booking process
                self.did_order_create = True

            else:  # error payload
                json_obj = {}
                json_obj["status"] = create_payload.get("status")
                json_obj["error_response"] = create_payload.get("error_response")

            json_representation = json.dumps(json_obj)
            logger.info(json_representation, mask=hotel_log_mask)

            new_message.additional_kwargs = {
                "function_call": {
                    "arguments": json_representation,
                    "name": "HotelBookingResponse",
                }
            }

            return {
                "messages": [new_message],
                "current_topic": self.topic,
                "current": "hotel_booking_agent",
                "model": response,
            }

        except Exception as e:
            # logger.error(e.args[0], mask=hotel_log_mask)
            # error_msg = FunctionMessage(content=str(e.args[0]), name="hotel_booking")
            # error_msg.additional_kwargs = {
            #     "function_call": {
            #         "arguments": {"error_response": e.args[0]},
            #         "name": "Error Message",
            #     }
            # }
            return {
                "errors": [AgentError.from_exception("hotel_booking_agent", e)],
                "current": "hotel_booking_agent",
                "current_topic": self.topic,
                "model": response or {},
            }

    # async def hotel_cancel_hitl_agent(self, state):
    #     logger.info("hotel_cancel_hitl_agent", mask=hotel_log_mask)
    #     messages = state["messages"]
    #     message_buffer_strs = get_message_buffer_as_strings(messages)

    #     t = Timings("BAML: DoCancelHotelHITLConversation")
    #     order_id_check = await b.DoCancelHotelHITLConversation(
    #         messages=message_buffer_strs,
    #         travel_context="",
    #         current_date=get_current_date_string(self.timezone),
    #         self_intro=settings.OTTO_SELF_INTRO,
    #         convo_style=settings.OTTO_CONVO_STYLE,
    #         baml_options={"collector": logger.collector},
    #     )
    #     t.print_timing("yellow")
    #     logger.log_baml()

    #     if not order_id_check.order_id:
    #         message = AIMessage(content=order_id_check.agent_response)
    #         return {
    #             "messages": [message],
    #             "current_topic": "CancelHotel",
    #             "model": order_id_check,
    #         }

    #     order_id = order_id_check.order_id

    #     hotel_cancel_context = await self.build_hotel_cancel_context(order_id)
    #     logger.info(hotel_cancel_context, mask=hotel_log_mask)

    #     t = Timings("BAML: DoCancelHotelConversationWithStep")
    #     response = await b.DoCancelHotelConversationWithStep(
    #         cancellation_context=hotel_cancel_context,
    #         messages=message_buffer_strs,
    #         current_date=get_current_date_string(self.timezone),
    #         self_intro=settings.OTTO_SELF_INTRO,
    #         convo_style=settings.OTTO_CONVO_STYLE,
    #         previous_state=self.cancel_hotel_state.model_dump_json(),
    #         user_name=self.user.name,
    #         baml_options={"collector": logger.collector},
    #     )
    #     t.print_timing("yellow")
    #     logger.log_baml()

    #     if response.updated_cancel_hotel_hotel_response:
    #         cancel_state_dict = self.cancel_hotel_state.model_dump()
    #         cancel_state_dict.update(response.updated_cancel_hotel_hotel_response.model_dump())
    #         cancel_state_dict.update({"order_id": order_id})
    #         self.cancel_hotel_state = CancelHotelResponseWithStep(**cancel_state_dict)

    #     if self.cancel_hotel_state.current_step == CancelHotelStep.SUBMIT_CANCELLATION:
    #         message = AIMessage(content=self.cancel_hotel_state.agent_response)
    #         if self.user_facing_agent:
    #             self.user_facing_agent.append_to_in_memory_messages(message)
    #             self.user_facing_agent.add_history_message(message)
    #             self.websocket_send_message(
    #                 {
    #                     "type": "prompt",
    #                     "isBotMessage": True,
    #                     "expectResponse": False,
    #                     "text": self.cancel_hotel_state.agent_response,
    #                 }
    #             )

    #         response = await self.cancel_hotel(order_id, self.cancel_hotel_state.hotel_cancel_reason or "")
    #         if isinstance(response, AIMessage):
    #             message = response
    #         else:
    #             return {
    #                 "errors": [response],
    #                 "current_topic": "CancelHotel",
    #             }
    #     else:
    #         response_str = (
    #             "{}"
    #             if response.updated_cancel_hotel_hotel_response is None
    #             else response.updated_cancel_hotel_hotel_response.model_dump_json()
    #         )
    #         message = FunctionMessage(
    #             content="",
    #             name=response.__class__.__name__,
    #             additional_kwargs={
    #                 "function_call": {
    #                     "name": response.__class__.__name__,
    #                     "arguments": response_str,
    #                 }
    #             },
    #         )

    #     return {
    #         "messages": [message],
    #         "current_topic": "CancelHotel",
    #         "model": response,
    #     }

    async def cancel_hotel_agent(self, state):
        raise NotImplementedError

    async def do_cancel_hotel(self, state):
        raise NotImplementedError

    # TODO: (chengxuan.wang) currenlty only used in async plan POC, consider to remove it.
    async def hotels_search(self, params: dict[str, Any]) -> AIMessage | FunctionMessage:
        try:
            json_dict = params

            # Will need these later for availability and pricing
            check_in_date = json_dict["check_in_date"]
            check_out_date = json_dict["check_out_date"]

            location_neighborhoods_districts_landmarks = json_dict.get(
                "location_neighborhoods_districts_landmarks", None
            )
            location_latitude_longitude_str = json_dict.get("location_latitude_longitude", None)
            input_hotel_search_radius = json_dict.get("hotel_search_radius") or distance_from_target_location_threshold

            lat_long_dict = self.resolve_lat_long(location_latitude_longitude_str)
            if lat_long_dict == {}:
                raise ValueError(
                    f"Couldn't resolve latitude/longitude coordinates: {location_latitude_longitude_str}, {location_neighborhoods_districts_landmarks}."
                )

            hotels = (
                await self.get_available_hotels(
                    check_in_date, check_out_date, lat_long_dict, input_hotel_search_radius / 1000
                )
                or []
            )
            hotels = hotels[:number_of_hotels_considered_by_the_model]

            hotels_id_to_details_key_map = {}
            for hotel in hotels:
                hotel_id = int(hotel.get("hotelSpec", {}).get("hotelId", "").replace("SPOTNANA:", ""))
                hotel["id"] = hotel_id
                hotels_id_to_details_key_map[hotel_id] = hotel.get("hotelDetailsKey", "")

            logger.info(f"Sending {len(hotels)} hotels to the model for analysis and selection.", mask=hotel_log_mask)

            hotels_str = self.text_from_dict(hotels)
            if hotels_str is None:
                raise ValueError("Failed to convert JSON to CSV")

            # Convert our message buffer
            # message_buffer_strs = get_message_buffer_as_strings(messages)
            travel_context_str = json_dict.get("travel_context", "")
            user_preferences = await get_user_preferences(self.user.id)
            # JTB:  Note that the hotels string, which is sizable, is never
            # a part of the message stack.
            t = Timings("BAML: ConverseHotelSearch")
            response = await b.ConverseHotelSearch(
                hotel_options_data=hotels_str,
                travel_context=travel_context_str,
                preferences=user_preferences,
                messages=[],
                current_datetime=get_current_datetime_string(self.timezone),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("yellow")
            logger.log_baml()

            new_message = AIMessage(content="")
            json_representation = response.model_dump_json()

            # Rejoin the rooms data into the hotels chosen by the model
            hotel_search_options_schema = json.loads(json_representation)
            hotel_options = hotel_search_options_schema.get("hotel_options", [])
            # hotel_options = [o for o in hotel_options if o["property_id"] in saved_data]
            if len(hotel_options) == 0:
                raise ValueError("No hotels found around, could be it's not available for your dates.")

            hotels_options_with_rooms = await self.get_hotels_rooms_details(
                [hotels_id_to_details_key_map[option["property_id"]] for option in hotel_options]
            )

            hotel_search_options_schema["hotel_options"] = self.map_hotels_room_details_response(
                hotel_options, hotels_options_with_rooms
            )

            if len(hotel_search_options_schema["hotel_options"]) == 0:
                raise ValueError("No availability found for selected hotels, please try other dates.")

            # We need this info to display the location target radius
            hotel_search_options_schema["location_lat_long"] = {
                **lat_long_dict,
                "radius": input_hotel_search_radius,
            }

            results_str = json.dumps(hotel_search_options_schema)

            new_message.additional_kwargs = {
                "function_call": {
                    "arguments": results_str,
                    "name": "HotelSearchOptionsResponse",
                }
            }

            new_message.additional_kwargs["raw_tool_output"] = self.filter_raw_tool_output(hotels)

            return new_message

        except Exception as e:
            logger.error(e.args[0], mask=hotel_log_mask)
            error_msg = FunctionMessage(content=str(e.args[0]), name="hotel_search")
            error_msg.additional_kwargs = {
                "function_call": {
                    "arguments": {"error_response": e.args[0]},
                    "name": "Error Message",
                }
            }
            return error_msg

    async def build_hotel_cancel_context(self, order_id: str | None) -> str:
        hotel_cancel_context = ""
        if order_id is None:
            hotel_cancel_context = "I'm sorry, Can you specify which hotel or room you'd like to cancel?"
            return hotel_cancel_context

        try:
            booking_details: Booking | None = await Booking.from_query(
                {"thread_id": self.thread_id, "type": "accommodations", "content.order_number": order_id}
            )
            assert booking_details is not None
            assert booking_details.content.get("trip_id") is not None

            trip_details = await spotnana_api.get_trip_details(str(booking_details.content.get("trip_id")))
            booking_status = trip_details.get("tripBookingStatus")

            match booking_status:
                case "CANCELLED_STATUS" | "REFUNDED_STATUS" | "VOIDED_STATUS" | "CANCELLATION_IN_PROGRESS_STATUS":
                    hotel_cancel_context = "This booking can't be cancelled because it is already cancelled."
                case "COMPLETED_STATUS":
                    hotel_cancel_context = "This booking can't be cancelled because the guest has already stayed."
                case "ACTIVE_STATUS" | "CONFIRMED_STATUS":
                    hotel_cancel_context = f"This booking can be cancelled. Here is the cancellation policy: {self.get_cancellation_policy_from_trip(trip_details)}"
                case _:
                    hotel_cancel_context = "This booking can not be cancelled."

            return hotel_cancel_context
        except Exception as e:
            logger.error(f"Could not determine status of booking {order_id}, got error {e}")
            hotel_cancel_context = (
                "I'm sorry, got some issue when checking the order details. Could pls try ask me again?"
            )
            return hotel_cancel_context

    def get_cancellation_policy_from_trip(self, trip_details):
        cancellation_policy: dict[str, str] = (
            trip_details.get("pnrs", [{}])[0]
            .get("data", {})
            .get("hotelPnr", {})
            .get("room", {})
            .get("cancellationPolicy")
        )

        return "/n".join([f"{key}: {value}" for key, value in cancellation_policy.items()])

    async def cancel_hotel(self, order_id: str, hotel_cancel_reason: str | None = None):
        logger.info("do_cancel_hotel", mask=hotel_log_mask)

        if hotel_cancel_reason is None:
            hotel_cancel_reason = "Cancelled by user"

        try:
            await spotnana_api.cancel_hotel_booking(order_id)
            logger.info(f"Successfully cancelled hotel order {order_id}", mask=hotel_log_mask)

            booking = await get_accommodation_booking(order_id)
            if booking:
                await self.update_booking_status(order_id, BookingStatus.CANCELLED)
                # accommodation_data_str = AccommodationBookingMemoryFormatter.format(
                #     booking, operation=BookingOperation.CANCELLED
                # )
                # await self.bookings_memory.store_accommodation_booking_memory(
                #     order_number=order_id,
                #     formatted_message=accommodation_data_str,
                # )
            return AIMessage(
                content=f"Your booking with order ID {order_id} has been successfully cancelled. If you need further assistance, feel free to let me know."
            )
        except Exception as e:
            logger.error(f"Failed to cancel hotel order {order_id}: {e}", mask=hotel_log_mask)
            return AgentError("unknown", str(e))

    async def update_booking_status(self, order_id: str, status: BookingStatus):
        update_query = {
            "thread_id": self.thread_id,
            "type": "accommodations",
            "content.order_number": order_id,
        }
        update_data = {
            "content.status": status.value.lower(),
            "status": status.name,
        }
        await Booking.update_fields(update_query, update_data)

    def websocket_send_message(self, message: dict[str, Any]):
        pass

    @staticmethod
    async def get_spotnana_traveler_search_and_read(email: str):
        traveler_search = await spotnana_api.get_traveler_by_email(email)
        traveler_read = await spotnana_api.traveler_read(traveler_search["results"][0].get("userOrgId"))

        return traveler_search, traveler_read
