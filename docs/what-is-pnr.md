# What is the PNR in corporate travel?

By <PERSON> | December 13, 2023 | Travel 101

## Contents

The Passenger Name Record (PNR) is a unique identifier assigned to an individual passenger or a group of passengers in the airline industry. PNRs are used to store passenger information, including their name, flight itinerary, seat number, ticketing information, and contact details, among other relevant data.

PNRs were first introduced in the 1960s when American Airlines and IBM built Sabre, the first central reservation system for airline tickets. Since then, the PNR has become the industry standard for airlines and Global Distribution Systems (GDSs) when a flight is booked.

Since PNRs were created before databases were commercially available, they store and manage data as unstructured text in a flat data file. In addition, since PNRs are designed to manage only a handful standard data elements, online booking tools (OBTs) and travel management companies (TMCs) have increasingly relied on scripting tools to write and read a large volume of unstructured comments to transmit essential information about a trip, like loyalty information and meal preferences.

While the PNR serves a crucial role in global travel, its limitations are one of the major reasons that travelers have difficulty adjusting trips and often receive lackluster service from corporate travel agents.

## What is a PNR in corporate travel?

A PNR includes a six character code that acts as a booking reference or record locator that a traveler receives once they book and pay for a flight. The code points to a PNR file that is created by an airline's central reservation system or the GDS through which a booking was made.

A PNR file is updated when a change is made to a booking and archived after a flight takes place. Travel itineraries, though, usually include multiple flight bookings and hotel stays. These other bookings may be cobbled together with a passive segment (basically a non-booking PNR entry) used to place bookings into another trip record. This is often done manually by travel agents, which is time consuming and may lead to errors being introduced to PNR remarks by accident.

Complexity ramps up when trips become more complicated or trip elements are changed multiple times. The unstructured comments in a PNR file vary in composition by TMC, since there is no common data standard. User error from travel agents can cause major problems when a trip begins or needs to be serviced. There can be up to 999 unstructured data elements in each PNR file, and errors mount if a trip is improperly changed or updated.

The development of a PNR made sense to solve airline problems in the 1960s, but the travel industry needs solutions to solve the problems and complexity of today.

## Why travel needs a new system of record

The use of PNRs and the complex methods used to get around their limitations are holding back travel and creating poor outcomes for travelers.

If the infrastructure of travel was rebuilt today, it would look very different from the PNR-based structure of the 1960s when database and mainframe technology was in its infancy. What's missing is a structured data model that pulls PNRs and other booking records together in an extensible and open data record for travelers, travel agencies, and travel management companies.

Spotnana has been built from the ground up with a new data model that eliminates the need for PNR comments, enabling better automation and simpler integrations with external systems, all of which contribute to better traveler experiences, enhanced real-time reporting, and increased travel agent efficiency.

Modern airline distribution is evolving into an offer and order paradigm, dubbed One Order by the International Air Transport Association (IATA), where travel buyers receive offers directly from airlines and travelers or travel agents place an order for the option they want. The result is a trip that can be changed by travelers themselves or on their behalf by a travel agent, through a single platform with a structured data model that replaces the PNR.

In corporate travel, the offer and order model simplifies the process of placing bookings and making changes. Spotnana has been designed to support this model since day one. We use a flexible trip container for each trip that can contain as many trip and data elements as needed.

A trip with a flight booked through IATA's New Distribution Capability (NDC) schema and a hotel booked through a booking tool, for instance, appear seamlessly to our users and can be adjusted at will without contacting the supplier by phone or requiring an agent to create a passive segment.

Our structured data model eliminates manual agent work, decreases the chance for human error, and makes it simple to adjust any element of a trip. This drives savings for a travel program and seamless experiences for travelers with complex itineraries.

Source: [What is the PNR in corporate travel?](https://www.spotnana.com/blog/what-is-the-pnr-in-corporate-travel/)