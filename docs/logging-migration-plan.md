# Logging Migration Plan

This document outlines the comprehensive plan to migrate all logging in the Otto codebase to conform to the structured logging guidelines defined in [logging.md](./logging.md).

## Migration Progress

**✅ Phase 1 - Critical Fixes (COMPLETED - PR #2332)**
- Import issues resolved: 3 files
- Print statements replaced: 4 files 
- F-string conversions: 1 file
- Total files modified: 7

**✅ Phase 2 - Critical Infrastructure Migration (COMPLETED - PR #2338)**
- F-string conversions: 25 logging calls across 5 files
- Files modified: virtual_travel_agent/supervisor.py, websocket.py, authentication files
- Total files modified: 5

**🔄 Next Phase: Phase 3 - External API Integration Migration**
- Target: Spotnana integration, hotel APIs
- Estimated effort: M (Medium)

## Executive Summary

**Current State**: <PERSON> has excellent logging infrastructure with ~30% of server files already migrated to structured logging, but 85% of agent files still use f-strings instead of structured logging. **Phase 1 complete** - critical import issues and print statements have been resolved.

**Migration Scope**: XL effort (estimated) - primarily due to extensive f-string replacement across agent directories, plus critical infrastructure upgrades.

**Strategy**: Prioritize critical infrastructure → security/auth → external APIs → agents → supporting services to ensure maximum impact on debugging and monitoring capabilities.

## Analysis Results

### Files Successfully Using Structured Logging
- **Server directory**: 69 out of 231 files (~30%) already use `from server.utils.logger import logger`
- **Import compliance**: Most files correctly import the centralized logger
- **BAML integration**: Well-implemented throughout the codebase

### Critical Issues Identified

#### Import Problems (3 files)
- `server/utils/email_validation.py` - Uses `logging.getLogger(__name__)`
- `server/services/memory/trips/retriever.py` - Uses `from logging import getLogger`
- `front_of_house_agent/sub_agent/exchange_flight_sub_agent.py` - Uses `from asyncio.log import logger` ❌

#### Print Statements (6 files)
- `server/utils/settings.py` - Error fallback prints
- `server/utils/push_notification.py` - Debug prints
- `server/utils/sample_trip_date_shifting.py` - Error prints alongside logger
- `server/services/calendar_api/google_api_base.py` - Error prints (2 instances)

#### F-String Usage in Logging
- **Server files**: 15+ files using f-strings in logging calls
- **Agent files**: 35+ files (85%) using f-strings instead of structured logging
- **High-volume files**: Hotel agent (15+ calls), flight agent (12+ calls), front of house agent (38+ calls)

## Migration Plan

### Phase 1: Critical Fixes (XS - Immediate)

#### Step 1.1: Fix Import Issues
**Files to update:**
```python
# front_of_house_agent/sub_agent/exchange_flight_sub_agent.py
- from asyncio.log import logger
+ from server.utils.logger import logger

# server/utils/email_validation.py  
- import logging
- logger = logging.getLogger(__name__)
+ from server.utils.logger import logger

# server/services/memory/trips/retriever.py
- from logging import getLogger
- logger = getLogger(__name__)
+ from server.utils.logger import logger

# flight_agent/flights_tools.py
- import structlog  # Remove direct structlog usage
+ # Use existing logger import only
```

#### Step 1.2: Remove Print Statements
**Files to update:**
- `server/utils/settings.py`
- `server/utils/push_notification.py`
- `server/utils/sample_trip_date_shifting.py`
- `server/services/calendar_api/google_api_base.py`

**Pattern:**
```python
# Before
print(f"Error: {error_message}")

# After
logger.error("operation_failed", error=str(e), operation="specific_operation")
```

### Phase 2: Critical Infrastructure Migration (S-M)

#### Step 2.1: Tier 1 Critical Files

**`virtual_travel_agent/supervisor.py`**
```python
# Current f-string usage (3 instances):
logger.error(f"Error decoding JSON: {e}", mask=supervisor_log_mask)
logger.info(f"New travel_context: {current_travel_context.model_dump_json()}", mask=supervisor_log_mask)

# Convert to:
logger.error("json_decode_failed", error=str(e), mask=supervisor_log_mask)
logger.info("travel_context_updated", 
            travel_context_data=current_travel_context.model_dump_json(), 
            mask=supervisor_log_mask)
```

**`server/api/v1/endpoints/websocket.py`**
- Add context binding for user_id/trip_id early in connection
- Convert remaining f-string calls to structured logging
- Add comprehensive connection lifecycle logging

#### Step 2.2: Authentication & Security Files
**Target files:**
- `server/services/authenticate/authenticate.py`
- `server/api/v1/endpoints/authenticate/*.py`

**Add comprehensive logging:**
```python
# Context binding for auth operations
logger.bind_log_context(
    user_id=user.id,
    auth_method="google_oauth",
    request_id=request_id
)

logger.info("authentication_attempt", provider="google")
logger.info("authentication_successful", user_id=user.id)
logger.error("authentication_failed", error=str(e), provider="google")
```

### Phase 3: External API Integration Migration (M)

#### Step 3.1: Spotnana Integration
**`server/api_clients/spotnana/client.py`**
- Already well-migrated
- Add context binding for company_id/user_id in API calls

**`server/services/partners/spotnana/webhook_event_handler.py`**
- Convert f-strings to structured logging
- Add booking context to all operations

#### Step 3.2: Hotel Integration
**`hotel_agent/booking_dot_com_tools.py` (15+ f-string calls)**
```python
# Before
logger.info(f"Hotel order details response: {response}")
logger.warning(f"Hotel booking not found for reservation {reservation_number}")

# After  
logger.info("hotel_order_details_received", 
            response_size=len(response),
            status_code=getattr(response, 'status_code', None))
logger.warning("hotel_booking_not_found", 
               reservation_number=reservation_number,
               search_context="order_details")
```

**`hotel_agent/spotnana_hotels_helper.py` (9 f-string calls)**
- Convert all f-string logging to structured format
- Add hotel search session context

### Phase 4: Agent Migration (L)

#### Step 4.1: Front of House Agent
**`front_of_house_agent/front_of_house_agent.py` (8 f-string calls)**
```python
# Add context binding at agent initialization
logger.bind_log_context(
    agent_type="front_of_house",
    user_id=self.user_id,
    trip_id=self.trip_id
)

# Convert f-strings to structured logging
logger.info("agent_processing_message", 
            message_type=message.type,
            conversation_stage=self.current_stage)
```

**`front_of_house_agent/back_of_house_executor/*.py` (8 files)**
- Systematic conversion of extensive f-string usage
- Add execution context to all operations
- Standardize error handling patterns

#### Step 4.2: Flight Agent
**`flight_agent/flights_tools.py` (5 f-string calls)**
**`flight_agent/flights_helper.py` (7 f-string calls)**
```python
# Before
logger.info(f"Flight search results: {len(results)} flights found")

# After
logger.info("flight_search_completed",
            results_count=len(results),
            origin=search_params.origin,
            destination=search_params.destination,
            search_duration_ms=duration_ms)
```

#### Step 4.3: Hotel Agent
**All 5 hotel agent files**
- Convert extensive f-string usage to structured logging
- Add hotel search context binding
- Standardize API response logging

### Phase 5: Supporting Services Migration (M)

#### Step 5.1: Calendar & Google Maps APIs
**`server/services/calendar_api/*.py`**
- Convert f-strings to structured logging
- Add calendar context (user_id, calendar_provider)
- Review email address logging for PII compliance

**`server/services/google_maps_api/*.py`**
- Convert f-strings to structured logging
- Review API response logging for sensitive data

#### Step 5.2: Cron Jobs & Background Tasks
**`server/cron/check_hotel_status.py` (8 f-string instances)**
**`server/cron/hotel_data_cache.py` (3 f-string instances)**
```python
# Add job context binding
logger.bind_log_context(
    job_name="check_hotel_status",
    job_run_id=generate_job_id(),
    scheduled_time=scheduled_time
)

# Convert f-strings
logger.info("hotel_status_check_started", hotel_count=len(hotels_to_check))
logger.info("hotel_status_check_completed", 
            processed_count=processed_count,
            updated_count=updated_count,
            duration_seconds=duration)
```

### Phase 6: Error Handling Standardization (S)

#### Step 6.1: Exception Logging Patterns
**Standardize all exception logging:**
```python
# Standard pattern for all exception handling
try:
    risky_operation()
    logger.info("operation_completed", operation="risky_operation", result_data=result)
except SpecificException as e:
    logger.error("operation_failed",
                 error=str(e),  # Always convert to string
                 operation="risky_operation",
                 user_id=user_id,
                 additional_context=context_data)
    raise
except Exception as e:
    logger.error("unexpected_error",
                 error=str(e),
                 operation="risky_operation", 
                 user_id=user_id)
    raise
```

#### Step 6.2: Context Binding Implementation
**High-traffic endpoints requiring context binding:**

**WebSocket connections:**
```python
# Early in connection lifecycle
logger.bind_log_context(
    user_id=user.id,
    trip_id=current_trip.id,
    session_id=websocket_session.id,
    connection_type="websocket"
)
```

**API endpoints:**
```python
# In middleware or early in request handling
logger.bind_log_context(
    user_id=current_user.id,
    request_id=request.headers.get("x-request-id"),
    endpoint=request.url.path
)
```

**Agent operations:**
```python
# In agent initialization
logger.bind_log_context(
    agent_type=self.__class__.__name__,
    user_id=self.user_id,
    trip_id=self.trip_id,
    agent_session_id=self.session_id
)
```

### Phase 7: Sensitive Data Review (XS)

#### Step 7.1: PII Audit
**Files requiring PII review:**
- Calendar integration files (email addresses)
- Google Maps API responses (addresses, location data)
- Authentication flows (tokens, user data)

**Pattern for PII-safe logging:**
```python
# Good - log IDs instead of full data
logger.info("user_profile_updated", 
            user_id=user.id, 
            fields_updated=["email", "phone"])

# Avoid - don't log sensitive data directly
logger.info("user_updated", user_data=user.dict())  # May contain passwords/tokens
```

#### Step 7.2: Data Classification
**Ensure proper data handling:**
- Use ID references instead of full objects for sensitive data
- Add masking for development environments where needed
- Review all API response logging for sensitive content

## Implementation Guidelines

### Structured Logging Conversion Patterns

#### F-String to Structured Conversion
```python
# Before (f-string)
logger.info(f"Processing {count} items for user {user_id}")
logger.error(f"Failed to process item {item_id}: {error}")

# After (structured)
logger.info("item_processing_started", count=count, user_id=user_id)
logger.error("item_processing_failed", 
             item_id=item_id, 
             error=str(error),
             user_id=user_id)
```

#### Context Binding Patterns
```python
# Request-level context (in middleware or endpoint start)
logger.bind_log_context(
    user_id=user.id,
    request_id=generate_request_id(),
    endpoint=request.path
)

# Operation-level context (in service methods)
logger.bind_log_context(
    operation="flight_search",
    search_id=search_session.id
)

# Agent-level context (in agent initialization)
logger.bind_log_context(
    agent_type="flight_agent",
    user_id=self.user_id,
    trip_id=self.trip_id
)
```

#### Error Handling Patterns
```python
# Exception handling with proper context
try:
    result = external_api_call(params)
    logger.info("api_call_successful", 
                api="external_service",
                response_size=len(result),
                duration_ms=call_duration)
except APIException as e:
    logger.error("api_call_failed",
                 error=str(e),
                 api="external_service", 
                 params=safe_params,  # No sensitive data
                 retry_count=retry_count)
    raise
```

## Effort Estimation

| Phase | Description | T-Shirt Size | Files Affected | Status |
|-------|-------------|--------------|----------------|---------|
| Phase 1 | Critical fixes (imports, prints) | XS | 7 files | ✅ **COMPLETED** |
| Phase 2 | Critical infrastructure | S-M | 5 files | ✅ **COMPLETED** |
| Phase 3 | External API integrations | M | 10-12 files | 🔄 Next |
| Phase 4 | Agent migration | L | 35+ files | ⏳ Pending |
| Phase 5 | Supporting services | M | 15-20 files | ⏳ Pending |
| Phase 6 | Error handling standardization | S | Cross-cutting | ⏳ Pending |
| Phase 7 | Security review | XS | Cross-cutting | ⏳ Pending |

**Total Estimated Effort: XL** (primarily due to Phase 4 agent migration scope)

## Success Criteria

### Completion Metrics
- [x] **Phase 1**: All critical files use `from server.utils.logger import logger` ✅
- [x] **Phase 2**: F-string usage eliminated in critical infrastructure (25 conversions across 5 files) ✅
- [x] **Phase 1**: Zero print statements in critical infrastructure files ✅
- [x] **Phase 1**: Exception logging uses `error=str(e)` pattern in migrated files ✅
- [x] **Phase 2**: Context binding maintained in high-traffic endpoints (WebSocket, auth flows) ✅
- [ ] Context binding implemented in all remaining high-traffic endpoints (Phases 3-6)
- [ ] PII audit completed and documented (Phase 7)

### Quality Metrics
- [x] All critical infrastructure has comprehensive logging ✅
- [ ] All external API integrations have proper error logging
- [ ] All agent operations have contextual logging
- [x] Security-sensitive operations have audit trails ✅
- [ ] Performance impact of logging changes measured and documented

## Testing Strategy

### Validation Steps
1. **Import validation**: Search for old logging imports
2. **F-string validation**: Search for f-strings in logger calls
3. **Print statement validation**: Search for print statements
4. **Context binding validation**: Verify context appears in logs
5. **Error handling validation**: Test exception scenarios

### Commands for Validation
```bash
# Check for old logging imports
rg "logging\.getLogger|from logging import|import logging" --type py

# Check for f-strings in logger calls  
rg "logger\.(info|debug|warning|error|critical)\(f\"" --type py

# Check for print statements
rg "print\(" --type py

# Verify structured logging usage
rg "logger\.(info|debug|warning|error)\(" --type py -A 1
```

## Rollback Plan

### Risk Mitigation
- Implement changes in small batches
- Maintain backward compatibility during transition
- Test thoroughly in development environment
- Monitor log output for missing context or errors

### Emergency Rollback
- Each phase should be committable independently
- Critical infrastructure changes should be easily revertible
- Monitor production logs for issues post-deployment

## Progress Tracking Checklist

### Phase 1: Critical Fixes (XS - Immediate) ✅ **COMPLETED**
#### Import Issues
- [x] `front_of_house_agent/sub_agent/exchange_flight_sub_agent.py` - Fix asyncio.log import ✅
- [x] `server/utils/email_validation.py` - Replace logging.getLogger ✅
- [x] `server/services/memory/trips/retriever.py` - Replace logging import ✅
- [x] `flight_agent/flights_tools.py` - Keep structlog for context vars (firehose logging) ✅

#### Print Statements  
- [x] `server/utils/settings.py` - Replace error fallback prints ✅
- [x] `server/utils/push_notification.py` - Replace debug prints ✅
- [x] `server/utils/sample_trip_date_shifting.py` - Replace error prints ✅
- [x] `server/services/calendar_api/google_api_base.py` - Replace error prints (2 instances) ✅

**Phase 1 Results:**
- **Completed**: 2024-01-17 via PR #2332
- **Files Modified**: 7 total
- **Import Issues Fixed**: 3 files now use centralized logger
- **Print Statements Replaced**: 4 files with 6 print statements converted to structured logging
- **F-String Conversions**: 1 file (exchange_flight_sub_agent.py) converted to structured logging
- **Code Quality**: All files pass linting, formatting, and type checking

**Phase 2 Results:**
- **Completed**: 2024-01-17 via PR #2338
- **Files Modified**: 5 total
- **F-String Conversions**: 25 logging calls converted to structured logging
- **Critical Infrastructure**: supervisor.py, websocket.py, and authentication flows migrated
- **Context Binding**: Maintained in high-traffic WebSocket and authentication endpoints
- **Code Quality**: All files pass linting, formatting, and type checking

### Phase 2: Critical Infrastructure Migration (S-M) ✅ COMPLETED
#### Tier 1 Critical Files
- [x] `virtual_travel_agent/supervisor.py` - Convert 3 f-string calls to structured logging ✅
- [x] `server/api/v1/endpoints/websocket.py` - Add context binding, convert 8 f-strings ✅
- [x] `server/main.py` - Review and enhance existing logging ✅

#### Authentication & Security
- [x] `server/services/authenticate/authenticate.py` - Add comprehensive logging with context ✅
- [x] `server/api/v1/endpoints/authenticate/google.py` - Enhance auth flow logging ✅
- [x] `server/api/v1/endpoints/authenticate/microsoft.py` - Enhance auth flow logging ✅
- [x] `server/api/v1/endpoints/authenticate/apple.py` - Enhance auth flow logging ✅

### Phase 3: External API Integration Migration (M)
#### Spotnana Integration
- [ ] `server/api_clients/spotnana/client.py` - Add context binding for company_id/user_id
- [ ] `server/services/partners/spotnana/webhook_event_handler.py` - Convert f-strings, add booking context

#### Hotel Integration
- [ ] `hotel_agent/booking_dot_com_tools.py` - Convert 15+ f-string calls
- [ ] `hotel_agent/spotnana_hotels_helper.py` - Convert 9 f-string calls
- [ ] `hotel_agent/hotels_helper.py` - Convert 12 f-string calls
- [ ] `hotel_agent/booking_dot_com_helper.py` - Convert f-string calls
- [ ] `hotel_agent/hotel_agent.py` - Convert f-string calls
- [ ] `hotel_agent/booking_dot_com_adapter.py` - Convert f-string calls

### Phase 4: Agent Migration (L)
#### Front of House Agent
- [ ] `front_of_house_agent/front_of_house_agent.py` - Convert 8 f-string calls, add context binding
- [ ] `front_of_house_agent/adapter.py` - Improve error mapping logging
- [ ] `front_of_house_agent/nudge_manager.py` - Add context to capability suggestions

#### Back of House Executors (8 files)
- [ ] `front_of_house_agent/back_of_house_executor/booking_executor_functions.py` - Convert f-strings
- [ ] `front_of_house_agent/back_of_house_executor/flight_executor_functions.py` - Convert f-strings
- [ ] `front_of_house_agent/back_of_house_executor/hotel_executor_functions.py` - Convert f-strings
- [ ] `front_of_house_agent/back_of_house_executor/trip_executor_functions.py` - Convert f-strings
- [ ] `front_of_house_agent/back_of_house_executor/executor_helper.py` - Convert f-strings
- [ ] `front_of_house_agent/back_of_house_executor/calendar_executor_functions.py` - Convert f-strings
- [ ] `front_of_house_agent/back_of_house_executor/general_executor_functions.py` - Convert f-strings
- [ ] `front_of_house_agent/back_of_house_executor/user_executor_functions.py` - Convert f-strings

#### Flight Agent
- [ ] `flight_agent/flights_tools.py` - Convert 5 f-string calls, fix structlog mixing
- [ ] `flight_agent/flights_helper.py` - Convert 7 f-string calls
- [ ] `flight_agent/flight_agent.py` - Convert f-string calls
- [ ] `flight_agent/spotnana_flights_helper.py` - Convert f-string calls
- [ ] `flight_agent/flights_adapter.py` - Convert f-string calls
- [ ] `flight_agent/google_serp_flights_helper.py` - Convert f-string calls

#### Other Agents
- [ ] `guardrail/guardrail.py` - Improve exception handling and logging
- [ ] `virtual_travel_agent/timings.py` - Convert timing f-string calls

### Phase 5: Supporting Services Migration (M)
#### Calendar & Google Maps APIs
- [ ] `server/services/calendar_api/calendar_events_background_task.py` - Convert f-strings
- [ ] `server/services/calendar_api/google_calendar_api.py` - Convert f-strings, add calendar context
- [ ] `server/services/calendar_api/microsoft_calendar_api.py` - Convert f-strings, add calendar context
- [ ] `server/services/google_maps_api/google_maps_client.py` - Convert f-strings, review PII
- [ ] `server/services/google_maps_api/google_maps_helper.py` - Convert f-strings
- [ ] `server/services/google_maps_api/google_maps_adapter.py` - Convert f-strings
- [ ] `server/services/google_maps_api/google_maps_timezone.py` - Convert f-strings

#### Cron Jobs & Background Tasks  
- [ ] `server/cron/check_hotel_status.py` - Convert 8 f-string instances, add job context
- [ ] `server/cron/hotel_data_cache.py` - Convert 3 f-string instances, add job context

#### Analytics & Utilities
- [ ] `server/utils/analytics/mixpanel.py` - Convert f-strings to structured logging

### Phase 6: Error Handling Standardization (S)
- [ ] Review all exception handling patterns across migrated files
- [ ] Ensure all exceptions use `error=str(e)` format
- [ ] Add relevant context (user_id, trip_id, operation) to exception logs
- [ ] Remove generic exception handling where possible
- [ ] Implement context binding in high-traffic endpoints

### Phase 7: Sensitive Data Review (XS)  
- [ ] Audit calendar integration files for email address logging
- [ ] Review Google Maps API responses for sensitive location data
- [ ] Review authentication flows for token/credential logging
- [ ] Ensure sensitive data uses ID references instead of full objects
- [ ] Add masking for development environments where needed

## Instructions for Future Agents

### Getting Started
1. **Read the guidelines first**: Review [logging.md](./logging.md) to understand Otto's structured logging approach
2. **Check current progress**: Review the checklist above to see what's been completed
3. **Pick a phase**: Start with the earliest incomplete phase (prioritize Phase 1-3 over Phase 4-7)
4. **Estimate effort**: Use t-shirt sizing to plan your work session

### Before You Start
```bash
# Verify current logging patterns in target files
rg "logger\.(info|debug|warning|error)\(f\"" path/to/file.py

# Check for print statements  
rg "print\(" path/to/file.py

# Check current import pattern
rg "from.*log" path/to/file.py
```

### Migration Workflow

#### Step 1: Analyze Target File(s)
- Read the file to understand current logging patterns
- Identify f-string usage: `logger.info(f"message {variable}")`
- Identify print statements that should be logging
- Note any context that should be bound (user_id, trip_id, etc.)

#### Step 2: Plan Your Changes  
- Convert f-strings to structured format: `logger.info("event_name", variable=value)`
- Replace print statements with appropriate log levels
- Add context binding if this is a high-traffic file
- Update imports if needed

#### Step 3: Implement Changes
```python
# Pattern 1: F-string to structured
# Before:
logger.info(f"Processing {count} items for user {user_id}")

# After: 
logger.info("item_processing_started", count=count, user_id=user_id)

# Pattern 2: Exception handling
# Before:
logger.error(f"Failed to process: {e}")

# After:
logger.error("processing_failed", error=str(e), operation="specific_operation")

# Pattern 3: Context binding (for high-traffic files)
logger.bind_log_context(
    user_id=user.id,
    trip_id=trip.id,
    operation_context="relevant_context"
)
```

#### Step 4: Validate Changes
```bash
# Ensure no f-strings remain in logger calls
rg "logger\.(info|debug|warning|error)\(f\"" path/to/file.py

# Check syntax
uv run python -m py_compile path/to/file.py

# Run relevant tests
uv run pytest path/to/test_file.py -v
```

#### Step 5: Update Checklist
- Mark completed items with `[x]` in this document
- Commit your changes with a clear message
- Note any deviations or issues in comments

### File Priority Guide

**Start with these if you're new to the migration:**
1. **Phase 1 files** - Simple import fixes and print replacements (XS effort)
2. **Phase 2 critical files** - High impact, moderate complexity (S-M effort)
3. **Single agent files** - Pick one agent directory and complete it (M effort per agent)

**Avoid starting with these unless you have significant time:**
- **Back of house executor files** - Large, complex files with many f-strings (L effort)
- **Phase 4 complete** - Requires substantial time commitment (L effort total)

### Common Patterns to Convert

#### F-Strings with Variables
```python
# Before
logger.info(f"User {user_id} booked flight {flight_id}")

# After  
logger.info("flight_booked", user_id=user_id, flight_id=flight_id)
```

#### F-Strings with API Responses
```python
# Before
logger.info(f"API response: {response}")

# After
logger.info("api_response_received", 
            response_size=len(str(response)),
            status_code=getattr(response, 'status_code', 'unknown'))
```

#### Exception Logging
```python
# Before
logger.error(f"Error in operation: {str(e)}")

# After
logger.error("operation_failed", 
             error=str(e), 
             operation="specific_operation_name")
```

#### Print Statements
```python
# Before
print(f"Debug: {debug_info}")

# After
logger.debug("debug_info", details=debug_info)
```

### Testing Your Changes

#### Unit Tests
```bash
# Run tests for the specific module you changed
uv run pytest path/to/test_module.py -v

# Run agent-specific tests
uv run pytest virtual_travel_agent_tests/ -k "test_related_to_your_changes"
```

#### Integration Testing  
```bash
# Start development server and test logging output
just dev-start

# In another terminal, trigger operations that use your modified files
# Check logs/otto.log for proper structured output
tail -f logs/otto.log | jq .
```

### Getting Help

#### Validation Commands
```bash
# Find remaining f-strings in logging calls
rg "logger\.(info|debug|warning|error|critical)\(f\"" --type py

# Find remaining print statements  
rg "print\(" --type py

# Check import patterns
rg "from.*logging|import logging" --type py

# Verify structured logging usage
rg "logger\.(info|debug|warning|error)\([^f]" --type py -C 2
```

#### Common Issues
- **Import errors**: Make sure you're using `from server.utils.logger import logger`
- **Context not appearing**: Verify context binding is called before logging
- **Tests failing**: Check if tests mock the logger and update mocks if needed
- **Missing context**: Add user_id, trip_id, or operation context to important logs

### Final Notes
- **Mark your progress**: Always update the checklist when you complete items
- **Test thoroughly**: Run relevant tests before marking items complete  
- **Small commits**: Commit changes frequently with clear messages
- **Ask for help**: If you're unsure about a pattern, check existing migrated files for examples
- **Document issues**: If you find patterns not covered here, add them to this guide

---

*This document should be updated as the migration progresses to track completion status and any deviations from the original plan.*
