# Claude Development Knowledge

This file contains commands and workflows specifically for Claude Code development sessions.

## GitHub PR Commands

### Read PR review comments
``` 
gh api \
    -H "Accept: application/vnd.github+json" \
    -H "X-GitHub-Api-Version: 2022-11-28" \
    /repos/ottotheagent/otto-server/pulls/{{PR_NUMBER}}/comments 
```

### View PR details and status
```
gh pr view {{PR_NUMBER}}
```

### View PR with comments (alternative)
```
gh pr view {{PR_NUMBER}} --comments
```

### Create PR
You can ask for JIRA ticket number, and make the title JIRA_NUMBER | Title
```
gh pr create --title "Title" --body "Description"
```

## Development Workflow

### Create feature branch
```bash
git checkout -b feature/descriptive-name
```

### Push branch and create PR
```bash
git push -u origin feature/branch-name
gh pr create --title "feat: Description" --body "Summary of changes"
```

### Check PR status
```bash
gh pr status
```

## Useful Commands

### Run linting and type checking
Run
```bash
just b
```
before running any linting and type checking tools.

### Run tests
```bash
uv run pytest -k "test_name"
```