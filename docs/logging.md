# Logging in Otto

This guide explains how logging works in the Otto travel planning application, designed for new developers getting familiar with the codebase.

## Overview

Otto uses a sophisticated logging system built on top of [structlog](https://www.structlog.org/) that provides structured, JSON-formatted logs for production and colorful console output for local development. The logging system is centralized in `server/utils/logger.py`.

## Quick Start

For most use cases, import and use the singleton logger:

```python
from server.utils.logger import logger

logger.info("User started trip planning", user_id="123", trip_id="abc-456")
logger.error("Failed to book flight", error=str(e), booking_id="xyz-789")
```

## Architecture

### Core Components

- **Logger Class**: A singleton wrapper (`server/utils/logger.py:118-190`) that provides a clean interface
- **Structured Logging**: Uses structlog for JSON-formatted logs with rich metadata
- **BAML Integration**: Special handling for LLM function calls via the BAML framework
- **Environment-Aware**: Different output formats for local vs production

### Log Levels

- `DEBUG`: Detailed debugging information
- `INFO`: General information about application flow
- `WARNING`: Warning messages for potentially harmful situations
- `ERROR`: Error events that might still allow continued operation

## Usage Patterns

### Basic Logging

```python
logger.info("Processing flight search", origin="NYC", destination="LAX", date="2024-03-15")
logger.error("Hotel booking failed", error=error_message, hotel_id="hotel123")
logger.warning("Rate limit approaching", remaining_calls=10)
```

### Context Binding

Bind context variables that apply to all subsequent logs within a request:

```python
logger.bind_log_context(user_id="12345", request_id="req-abc-123")

# All subsequent logs will include these context variables
logger.info("Starting flight search")
logger.debug("Querying airline API")
```

### BAML LLM Logging

The logger automatically handles BAML function calls:

```python
# This happens automatically for BAML function calls
logger.log_baml()  # Logs LLM usage, tokens, and responses
```

## Log Output

### Local Development
- **Console**: Colorful, human-readable output with emoji indicators
- **File**: `logs/otto.log` with full JSON structure, rotated at 100MB with 10 backups

### Production
- **Console**: JSON-formatted logs for log aggregation systems
- **File**: No file logging (console only to prevent deployment issues)

## Log Structure

Logs contain rich metadata:

```json
{
  "event": "flight_search_completed",
  "level": "info",
  "timestamp": "2024-03-15T14:30:00Z",
  "logger": "server.agents.flight_agent",
  "func_name": "search_flights",
  "lineno": 45,
  "filename": "flight_agent.py",
  "origin": "NYC",
  "destination": "LAX",
  "results_count": 15
}
```

## Configuration

### Environment Variables

- `LOG_LEVEL`: Set to DEBUG, INFO, WARNING, or ERROR (default: INFO)
- `OTTO_ENV`: Automatically determines local vs production formatting

### Log Location

- **Directory**: `logs/` (created automatically)
- **Main file**: `logs/otto.log`
- **Rotation**: 100MB files, 10 backups retained

### Filtering

The logging system includes intelligent filtering:
- **Console**: Shows concise BAML previews (200 chars max)
- **File**: Contains full BAML responses and detailed metadata
- **Skipped events**: Filters out verbose BAML internal logs

## Best Practices

### 1. Use Structured Data

```python
# Good
logger.info("flight_search_initiated", origin="NYC", destination="LAX", passengers=2)

# Avoid
logger.info(f"Flight search from {origin} to {destination} for {passengers} passengers")
```

### 2. Include Context

```python
# Bind request-level context
logger.bind_log_context(
    user_id=user.id,
    session_id=session.id,
    request_id=request_id
)

logger.info("processing_booking_request", booking_data=booking.dict())
```

### 3. Error Handling

```python
try:
    result = book_flight(booking_request)
    logger.info("flight_booked", booking_id=result.id, confirmation=result.confirmation_code)
except BookingError as e:
    logger.error("flight_booking_failed", 
                 error=str(e),  # Always convert exception to string
                 user_id=user_id,
                 booking_request=booking_request.dict())
    raise
except Exception as e:
    logger.error("unexpected_error", 
                 error=str(e),
                 operation="book_flight",
                 user_id=user_id)
    raise
```

### 4. Sensitive Data

Be careful with PII and sensitive information:

```python
# Good - log IDs instead of full data
logger.info("user_profile_updated", user_id=user.id, fields_updated=["email", "phone"])

# Avoid - don't log sensitive data
logger.info("user_updated", user_data=user.dict())  # May contain passwords/tokens
```

## Advanced Features

### Local Development Masking

For local development, you can mask sensitive data:

```python
# In local dev, this will show "User ID: ***123" instead of full ID
logger.info(user_id, mask="User ID: ***{}")
```

### Local Exception Handling

In local development, exceptions automatically include full stack traces:

```python
try:
    risky_operation()
except Exception as e:
    logger.error("operation_failed", error=str(e))  # Traceback automatically included in local env
```

**Key points:**
- Always convert exceptions to strings with `str(e)`
- Use `error` keyword argument for consistency
- Tracebacks are automatically included in local environment (`settings.is_local=True`)
- No manual traceback handling needed

## Troubleshooting

### Common Issues

1. **No logs appearing**: Check `LOG_LEVEL` environment variable
2. **Logs too verbose**: Adjust `LOG_LEVEL` to WARNING or ERROR
3. **Missing context**: Ensure `bind_log_context` is called at request start
4. **File permissions**: Ensure `logs/` directory is writable

### Viewing Logs

```bash
# Real-time console logs during development
just dev-start

# View recent logs
tail -f logs/otto.log

# Search logs
grep "user_id:12345" logs/otto.log

# Pretty-print JSON logs
jq . logs/otto.log | less
```

## Integration Points

The logging system integrates with:
- **BAML**: Automatic LLM call logging
- **Sentry**: Error tracking integration
- **Prometheus**: Metrics collection
- **AWS CloudWatch**: Production log aggregation

Remember: The logger is a singleton, so you don't need to create new instances or configure loggers in individual modules.