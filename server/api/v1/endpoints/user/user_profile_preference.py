import json

from fastapi import <PERSON>Router, Depends, HTTPException, Response, status
from starlette.responses import JSONResponse

from baml_client.types import ResponseAllPreferences
from server.schemas.authenticate.user import User
from server.schemas.user.user_profile_remove_preference import PreferenceRequest, UpdatePreferencesRequest
from server.services.authenticate.authenticate import manager
from server.services.user.user_preferences import get_user_preferences, save_user_preferences

router = APIRouter()


@router.post("/remove_preference", response_class=JSONResponse)
async def user_profile_remove_preference(data: PreferenceRequest, user: User = Depends(manager)):
    key = data.key
    value = data.value

    user_preferences = await get_user_preferences(user.id)
    if not user_preferences:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User preferences not found!")

    if getattr(user_preferences, key, None) is None:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="The key does not exist!")

    if isinstance(getattr(user_preferences, key), list):
        if value not in getattr(user_preferences, key):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The value does not exist!",
            )

        setattr(
            user_preferences,
            key,
            [x for x in getattr(user_preferences, key) if x != value],
        )

    elif isinstance(getattr(user_preferences, key), str):
        if getattr(user_preferences, key) != value:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The value does not exist!",
            )

        setattr(user_preferences, key, "")

    else:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Unknown data type!")

    await save_user_preferences(user.id, user_preferences)

    response = Response(content=json.dumps({"status": "updated"}), media_type="application/json")

    return response


@router.post("/update_preferences", response_class=JSONResponse)
async def update_user_preferences(request: UpdatePreferencesRequest, user: User = Depends(manager)):
    try:
        # Get current user preferences
        current_preferences = await get_user_preferences(user.id)

        # Process the request data and update preferences
        preferences_dict = current_preferences.model_dump() if current_preferences else {}

        for item in request.preferences:
            key = item.key
            value = item.value

            if not key or not value:
                continue

            # Handle list fields - append to existing list if not already present
            if key in [
                "preferred_airline_brands",
                "preferred_cabin",
                "preferred_seats",
                "flight_special_service_requests",
                "preferred_hotel_brands",
                "preferred_payment_timings",
                "preferred_travel_misc",
            ]:
                current_list = preferences_dict.get(key, [])
                if isinstance(current_list, list):
                    if value not in current_list:
                        current_list.append(value)
                else:
                    current_list = [value]
                preferences_dict[key] = current_list

            # Handle string fields - replace with new value
            elif key in ["preferred_home_airport"]:
                preferences_dict[key] = value

            # Handle enum fields
            elif key == "should_buy_luggage":
                if value.upper() in ["ALWAYS", "NEVER", "NONE"]:
                    preferences_dict[key] = value.upper()

        # Create ResponseAllPreferences object with updated data
        updated_preferences = ResponseAllPreferences(**preferences_dict)

        # Save the updated preferences
        await save_user_preferences(user.id, updated_preferences)

        return JSONResponse({"status": "success", "message": "Preferences updated successfully"})

    except Exception as e:
        return JSONResponse({"error": f"Failed to update preferences: {str(e)}"}, status_code=500)
