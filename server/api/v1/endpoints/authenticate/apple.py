import asyncio
import json
from datetime import datetime, timezone
from typing import Any, Optional, cast
from urllib.parse import urljoin

import httpx
from fastapi import APIRouter, Depends, HTTPException, Request, Response
from pymongo.collation import Collation
from starlette import status
from starlette.responses import JSONResponse, RedirectResponse
from starlette.status import HTTP_303_SEE_OTHER

from server.database.models.chat_thread import ChatThread
from server.database.models.user import User
from server.database.models.user_profile import LoginMethod, UserProfile
from server.services.authenticate.apple import (
    exchange_code_for_token,
    generate_apple_client_secret,
    get_apple_auth_url,
    persist_state,
)
from server.services.authenticate.authenticate import manager, validate_user
from server.services.one_time_codes.manage import create_otc_email_cookie, redeem_otc
from server.services.organization.organization import OrganizationService
from server.services.user.user_activity import UserActivityType, update_user_activity
from server.utils.analytics.analytics import TrackingEvent, TrackingManager
from server.utils.ip_stack_api import get_client_ip, guess_and_update_home_airport
from server.utils.logger import logger
from server.utils.mongo_connector import users_whitelist_collection
from server.utils.sentry import beta_login_to_sentry, not_invited_login_to_sentry
from server.utils.settings import settings

router = APIRouter()


@router.get("/login")
async def login(state: Optional[str] = None):
    """
    Initiate Apple OAuth login
    """
    auth_url = get_apple_auth_url(state)
    return JSONResponse({"url": auth_url})


@router.post("/logout")
async def logout(user: User = Depends(manager)):
    try:
        user_profile = await UserProfile.from_user_id(user.id)
        if not user_profile:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User profile not found")

        if user_profile.last_login_method != LoginMethod.APPLE:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User is not authenticated with Apple")

        client_secret = generate_apple_client_secret()

        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://appleid.apple.com/auth/revoke",
                data={
                    "client_id": settings.APPLE_CLIENT_ID,
                    "client_secret": client_secret,
                    "token": user_profile.refresh_token,
                    "token_type_hint": "refresh_token",
                },
                headers={"Content-Type": "application/x-www-form-urlencoded"},
            )

        # Check response
        if response.status_code not in (200, 204):
            logger.error("apple_token_revoke_failed", status_code=response.status_code, response_text=response.text)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to revoke Apple authorization"
            )

        response = Response(content=json.dumps({"status": "success"}), media_type="application/json")
        response.delete_cookie(
            "access_token",
            secure=(settings.COOKIES_DOMAIN != "localhost"),
            domain=settings.COOKIES_DOMAIN,
        )
        response.delete_cookie(
            "refresh_token",
            secure=(settings.COOKIES_DOMAIN != "localhost"),
            domain=settings.COOKIES_DOMAIN,
        )

    except Exception as e:
        logger.error("apple_authorization_revoke_error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Error revoking Apple authorization"
        )

    return response


@router.post("/callback")
async def auth_callback(request: Request):
    """
    Handle Apple OAuth callback (form_post response mode)
    """

    form_data = await request.form()
    code = form_data.get("code")
    error = form_data.get("error")
    state = form_data.get("state")
    id_token = form_data.get("id_token")
    user_data = form_data.get("user")

    if error:
        error_description = form_data.get("error_description", error)
        if state == "onboarding":
            assert settings.CLIENT_DOMAIN is not None, "CLIENT_DOMAIN is not set"
            app_redirect_url = (
                urljoin(settings.CLIENT_DOMAIN, "/onboarding") + f"?calendar_granted=false&reason={error_description}"
            )
        else:
            app_redirect_url = f"{settings.CLIENT_DOMAIN}?calendar_granted=false&reason={error_description}"
        return RedirectResponse(url=app_redirect_url, status_code=HTTP_303_SEE_OTHER)

    if not code or not id_token:
        raise HTTPException(status_code=400, detail="Authorization code and ID token are required")

    assert settings.CLIENT_DOMAIN is not None, "CLIENT_DOMAIN is not set"

    try:
        token_response = await exchange_code_for_token(code)
        response = await handle_auth_with_token(request, code, token_response, user_data, state)

        return response
    except HTTPException as e:
        app_redirect_url = settings.CLIENT_DOMAIN
        if state == "onboarding":
            app_redirect_url = urljoin(settings.CLIENT_DOMAIN, "/onboarding") + "?calendar_granted=false"
        response = RedirectResponse(url=app_redirect_url, status_code=HTTP_303_SEE_OTHER)
        raise e


async def handle_auth_with_token(
    request: Request,
    code,
    token_response: dict[str, Any],
    user_data,
    state,
    redirect_response: bool = True,
):
    user_info: dict = cast(dict, token_response.get("user_info"))

    first_name = ""
    last_name = ""

    if user_data is not None:
        try:
            user_json = json.loads(str(user_data))

            name_info = user_json.get("name", {})
            first_name = name_info.get("firstName", "").strip()
            last_name = name_info.get("lastName", "").strip()

        except json.JSONDecodeError:
            logger.error("user_data_parse_failed", user_data=user_data)
        except Exception as e:
            logger.error("name_extraction_error", error=str(e))

    email_lower = user_info["email"].lower()

    is_private_email = is_apple_private_relay(email_lower)
    if is_private_email == "true":
        app_redirect_url = f"{settings.CLIENT_DOMAIN}/login/?is_private_email={is_private_email}"
        return RedirectResponse(url=app_redirect_url, status_code=HTTP_303_SEE_OTHER)

    whitelisted_email = await users_whitelist_collection.find_one(
        {"whitelisted_emails": email_lower}, collation=Collation(locale="en", strength=2)
    )

    otc_code: str | None = request.cookies.get("otc")
    if whitelisted_email is None and otc_code is not None:
        is_valid: bool = await redeem_otc(otc_code, email_lower)
        if is_valid:
            whitelisted_email = email_lower

    if whitelisted_email is None:
        logger.info("email_not_whitelisted_redirect", email=email_lower, reason="not_whitelisted_no_otc")
        not_invited_login_to_sentry(email_lower, "Apple")

        await persist_state(email_lower, {"code": code, "token_response": token_response, "state": state})
        return RedirectResponse(
            url=f"{settings.CLIENT_DOMAIN}/login-denied",
            status_code=HTTP_303_SEE_OTHER,
            headers={"set-cookie": create_otc_email_cookie(email_lower)},
        )
    else:
        beta_login_to_sentry(user_info["email"], "Apple")

    user_profile: UserProfile | None = await UserProfile.from_sub(user_info["sub"])
    user: User | None = None
    refresh_token: str | None = token_response.get("refresh_token")

    if refresh_token is None:
        raise HTTPException(status_code=400, detail="No refresh token available")

    # Generate avatar URL based on name (empty if no name provided)
    avatar_url = generate_avatar_url(first_name, last_name)

    if user_profile is not None:
        await user_profile.update_refresh_token(refresh_token, LoginMethod.APPLE)
        user = await validate_user(user_profile.users_id)

    # Check if user logged in with email and connect to the apple account
    if user is None:
        user = await User.from_email(user_info["email"])

        if user is not None:
            user_profile = UserProfile(user_info["sub"], user.id, refresh_token, None, LoginMethod.APPLE)
            await UserProfile.new_user_profile(user_profile)

    if user is None:
        user = User(
            user_info.get("email", ""),
            first_name,
            last_name,
            profile_picture=avatar_url,
        )

        user_profile = await UserProfile.new_user(user_info["sub"], refresh_token, None, LoginMethod.APPLE, user=user)

        # First time user login, let's guess their home airport
        client_ip = get_client_ip(request)
        asyncio.create_task(guess_and_update_home_airport(client_ip, user_profile.users_id))

        await TrackingManager.log_event_in_background(
            event_type=TrackingEvent.FIRST_LOGIN,
            user_id=str(user.id),
            user_email=user.email,
            event_properties={"login_method": "Apple"},
        )

        await ChatThread.create_initial_chat_threads(user.id)
    else:
        if (first_name or last_name) and (first_name != user.first_name or last_name != user.last_name):
            await user.refresh_fields(
                {
                    "first_name": first_name,
                    "last_name": last_name,
                    "profile_picture": avatar_url,
                }
            )

    assert user_profile is not None, "user_profile should not be None at this point"

    is_organisation_assigned, message = await OrganizationService.auto_assign_user_to_organization(user)
    if not is_organisation_assigned:
        # TODO Needs to implement an warnings in case of errors.
        pass

    # Apple doesn't have calendar integration like Microsoft
    # But we'll maintain a similar flow for consistency
    payload: dict[str, str] = user_profile.get_token_payload(user=user)
    access_token, refresh_token = manager.create_custom_jwt(payload)

    assert settings.CLIENT_DOMAIN is not None, "CLIENT_DOMAIN is not set"
    app_redirect_url = settings.CLIENT_DOMAIN
    if state == "onboarding":
        app_redirect_url = urljoin(settings.CLIENT_DOMAIN, "/onboarding") + "?calendar_granted=false"

    # Check if user needs to set preferred name
    if not user.name:
        app_redirect_url = urljoin(settings.CLIENT_DOMAIN, "/preferred-name")

    await update_user_activity(user_id=str(user.id), activity_type=UserActivityType.SIGN_IN)

    response = (
        RedirectResponse(url=app_redirect_url, status_code=HTTP_303_SEE_OTHER) if redirect_response else Response()
    )
    response.set_cookie(
        key="access_token",
        value=access_token,
        samesite="none",
        secure=True,
        httponly=False,
        domain=settings.COOKIES_DOMAIN,
        expires=datetime.now(tz=timezone.utc) + settings.ACCESS_TOKEN_LIFESPAN,
    )
    response.set_cookie(
        key="refresh_token",
        value=refresh_token,
        samesite="none",
        secure=True,
        httponly=True,
        domain=settings.COOKIES_DOMAIN,
        expires=datetime.now(tz=timezone.utc) + settings.REFRESH_TOKEN_LIFESPAN,
    )

    if otc_code is not None:
        response.delete_cookie(
            "otc",
            secure=True,
            domain=settings.COOKIES_DOMAIN,
        )

    return response


def is_apple_private_relay(email):
    if not isinstance(email, str):
        return "false"

    if "privaterelay.appleid.com" in email.lower():
        return "true"

    # Check for Apple's newer hide.email domain as well
    if "hide.email" in email.lower():
        return "true"

    return "false"


def generate_avatar_url(first_name: str, last_name: str) -> str:
    """
    Generate avatar URL from ui-avatars.com based on user's name.
    Returns a default avatar if both names are empty.
    """
    if not first_name and not last_name:
        return f"{settings.SERVER_DNS}/static/default-avatar-icon.jpg"

    name_param = f"{first_name}+{last_name}".strip("+")
    return f"https://ui-avatars.com/api/?name={name_param}"
