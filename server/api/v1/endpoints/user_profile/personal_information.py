import json
from typing import Any, get_args

from fastapi import APIRouter, Depends, Response

from server.database.models.user import User as UserDbModel
from server.schemas.authenticate.user import User
from server.schemas.spotnana.user import PersonalInfo
from server.schemas.user_profile.personal_information import (
    PersonalInformationBaseRequest,
    PersonalInformationExtendedRequest,
)
from server.services.authenticate.authenticate import manager
from server.services.payment_profile.spotnana_profile import get_spotnana_user_by_email
from server.services.user_profile.personal_information import (
    get_user_profile_personal_information,
    is_user_profile_personal_information_complete,
    save_personal_information_spotnana,
)
from server.utils.mongo_connector import user_profile_personal_information_collection

router = APIRouter()


@router.get("/personal-information")
async def get_user_personal_information(user: User = Depends(manager)):
    user_personal_information = await get_user_profile_personal_information(user.id)

    if user_personal_information is not None:
        user_personal_information["citizenship"] = user.citizenship

    response = Response(
        content=json.dumps(
            {
                "personal_information": user_personal_information,
                "dropdown_values": {
                    "title": [
                        {
                            "value": v,
                            "text": v.capitalize(),
                        }
                        for v in get_args(get_args(PersonalInfo.__annotations__["title"])[0])
                        if v != "TITLE_UNKNOWN"  # Skip the TITLE_UNKNOWN value entirely
                    ],
                    "gender": [
                        {"value": v, "text": v.capitalize()}
                        for v in get_args(get_args(PersonalInfo.__annotations__["gender"])[0])
                    ],
                },
            }
        ),
        media_type="application/json",
    )

    return response


@router.post("/personal-information")
async def save_user_personal_information(
    user_personal_information: PersonalInformationExtendedRequest | PersonalInformationBaseRequest,
    user: User = Depends(manager),
):
    user_personal_information_db = await user_profile_personal_information_collection.find_one({"users_id": user.id})
    user_personal_information_db = user_personal_information_db if user_personal_information_db is not None else {}

    personal_information_extended: PersonalInformationExtendedRequest | PersonalInformationBaseRequest = (
        user_personal_information
    )
    try:
        personal_information_extended = PersonalInformationExtendedRequest(
            **{**user_personal_information_db, **user_personal_information.model_dump()}
        )
    except Exception:
        pass

    if isinstance(personal_information_extended, PersonalInformationExtendedRequest):
        if (
            personal_information_extended.citizenship is not None
            and personal_information_extended.citizenship != user_personal_information_db.get("citizenship")
        ):
            await UserDbModel.update_citizenship(user.id, personal_information_extended.citizenship)

        await UserDbModel.update_names_from_profile(
            user.id, personal_information_extended.first_name, personal_information_extended.last_name
        )

        user_related_fields = [
            "title",
            "first_name",
            "last_name",
            "phone",
            "dob",
            "gender",
            "traveler_number",
            "redress_number",
        ]
        is_user_updated = [
            getattr(personal_information_extended, field) != user_personal_information_db.get(field, None)
            for field in user_related_fields
        ].count(True) > 0

        spotnana_user = await get_spotnana_user_by_email(user.email)
        spotnana_user_id = spotnana_user.get("id", None)
        spotnana_original_user_persona = spotnana_user.get("persona", "GUEST")

        if is_user_profile_personal_information_complete(personal_information_extended.model_dump(), False) and (
            is_user_updated or spotnana_user_id is None
        ):
            await save_personal_information_spotnana(
                personal_information_extended, user, spotnana_user_id, spotnana_original_user_persona
            )
    elif isinstance(personal_information_extended, PersonalInformationBaseRequest):
        user_related_fields = [
            "title",
            "first_name",
            "last_name",
            "phone",
        ]
        is_user_updated = [
            getattr(personal_information_extended, field) != user_personal_information_db.get(field, None)
            for field in user_related_fields
        ].count(True) > 0

        spotnana_user = await get_spotnana_user_by_email(user.email)
        spotnana_user_id = spotnana_user.get("id", None)
        spotnana_original_user_persona = spotnana_user.get("persona", "GUEST")

        if is_user_profile_personal_information_complete(personal_information_extended.model_dump(), True) and (
            is_user_updated or spotnana_user_id is None
        ):
            await save_personal_information_spotnana(
                personal_information_extended, user, spotnana_user_id, spotnana_original_user_persona
            )

    # Save user account info to database
    query: dict[str, Any] = {"users_id": user.id}
    update: dict[str, Any] = {"$set": {"users_id": user.id, **personal_information_extended.model_dump()}}
    await user_profile_personal_information_collection.update_one(query, update, upsert=True)

    response = Response(content=json.dumps({"status": "success"}), media_type="application/json")

    return response
