# Spotnana API Client

This module provides a comprehensive client library for interacting with the Spotnana Travel API. It offers authentication, request logging, error handling, and specialized clients for different API domains.

## Architecture Overview

The Spotnana client is designed with a modular architecture:

- **Core Client** (`client.py`) - Base HTTP client with authentication and rate limiting
- **Specialized Clients** (`trip.py`) - Domain-specific clients for different API areas
- **Decorators** (`decorators.py`) - Automatic request/response logging and error handling
- **Exception Handling** (`exceptions.py`) - Comprehensive error handling with status code mapping
- **Configuration** (`logging_config.py`) - Configurable logging for files and firehose
- **Models** (`models.py`) - Internal data models for client state management

## Quick Start

### Basic Client Usage

```python
from server.api_clients.spotnana.client import SpotnanaClient

# Initialize client (uses environment variables for configuration)
client = SpotnanaClient()

# Make authenticated requests
response = await client.get("v2/trips/123")
data = await client.post("v2/trips", data={"name": "Business Trip"})
```

### Trip Management

```python
from server.api_clients.spotnana.trip import get_trips_client

# Get singleton trips client
trips_client = get_trips_client()

# Create a new trip
trip = await trips_client.create(
    name="Business Trip",
    user_id="user-123",
    company_id="company-456"
)

# Get trip details
details = await trips_client.get_details(trip_id="trip-789")
```

## Authentication

The client handles OAuth 2.0 authentication automatically:

- **Automatic Token Management** - Tokens are cached and refreshed as needed
- **Thread-Safe** - Uses async locks for concurrent token refresh
- **Configuration** - Uses environment variables for credentials:
  - `SPOTNANA_HOST` - API base URL
  - `SPOTNANA_CLIENT_ID` - OAuth client ID
  - `SPOTNANA_CLIENT_SECRET` - OAuth client secret
  - `SPOTNANA_COMPANY_GUID` - Company identifier
  - `SPOTNANA_API_KEY` - API key for additional authentication

## Request Logging

The client provides comprehensive logging through decorators:

### File Logging

Configurable file logging for specific endpoints:

```python
# Configuration in logging_config.py
FILE_LOGGING_CONFIG = {
    "endpoints": [
        "air/selected-itinerary",
        "traveler/search",
    ],
    "log_all_endpoints": False,  # Set to True to log all requests
}
```

- Logging is disabled in server context.
- Local logging is logged to `api_log/spotnana` by default.


### Firehose Integration

Automatic logging to AWS Firehose when in server context:

```python
@spotnana_http_logger("GET")
async def search_flights(self, endpoint: str, params: dict):
    # Automatically logs to both files and firehose
    return await self.get(endpoint, params=params)
```

## Error Handling

Comprehensive exception hierarchy with automatic status code mapping:

```python
from server.api_clients.spotnana.exceptions import (
    SpotnanaException,
    SpotnanaAuthenticationError,
    SpotnanaValidationError,
    SpotnanaNotFoundError,
    SpotnanaRateLimitError,
)

try:
    result = await client.get("v2/trips/invalid-id")
except SpotnanaNotFoundError as e:
    print(f"Trip not found: {e}")
except SpotnanaAuthenticationError as e:
    print(f"Authentication failed: {e}")
except SpotnanaException as e:
    print(f"General API error: {e}")
```

### Exception Features

- **Status Code Mapping** - Automatic exception type selection based on HTTP status
- **Structured Error Messages** - Extracts error details from Spotnana response format
- **Debug Information** - Includes debug identifiers and error codes
- **Fallback Handling** - Graceful degradation for unknown error formats

## Decorators

### HTTP Method Decorator

```python
@spotnana_http_logger("GET")
async def get(self, endpoint: str, params: dict | None = None, **kwargs):
    # Automatic request/response logging
    # Error handling and conversion
    # Firehose integration
    pass
```


## Configuration

### Environment Variables

Required configuration:

```bash
SPOTNANA_HOST=https://api.spotnana.com
SPOTNANA_CLIENT_ID=your-client-id
SPOTNANA_CLIENT_SECRET=your-client-secret
SPOTNANA_COMPANY_GUID=your-company-guid
SPOTNANA_API_KEY=your-api-key
```

Optional configuration:

```bash
SPOTNANA_LOG_ROOT_DIR=/path/to/logs  # Default: ROOT_DIR/api_log/spotnana
RUN_CONTEXT=server  # Enables firehose logging
```

### Logging Configuration

File logging is controlled by `FILE_LOGGING_CONFIG` in `logging_config.py`:

- **Endpoint-specific** - Enable logging for specific API endpoints
- **Namespace-based** - Enable logging for entire API namespaces
- **Global override** - Enable/disable logging for all endpoints

## Testing

Comprehensive test coverage is provided:

```bash
# Run all Spotnana client tests
uv run pytest tests/api_clients/spotnana/ -v

# Run specific test files
uv run pytest tests/api_clients/spotnana/test_client.py -v
uv run pytest tests/api_clients/spotnana/test_decorators.py -v
uv run pytest tests/api_clients/spotnana/test_logging_config.py -v
```

## Best Practices

### Client Usage

1. **Use Singleton Pattern** - For specialized clients like trips
2. **Handle Exceptions** - Always catch and handle specific exception types
3. **Context Variables** - Set trip_id and user_id in structlog context for logging
4. **Rate Limiting** - The client handles rate limiting automatically

### Error Handling

1. **Specific Exceptions** - Catch specific exception types when possible
2. **Fallback Handling** - Always include a catch-all for `SpotnanaException`
3. **Debug Information** - Log debug identifiers for troubleshooting
4. **Retry Logic** - Implement retry for transient errors

### Logging

1. **Selective Logging** - Only enable file logging for endpoints you need
2. **Context Variables** - Always set user_id and trip_id in context
3. **Structured Logging** - Use structlog for consistent log formatting
4. **Performance** - File logging is async and non-blocking

## API Reference

### SpotnanaClient

Core HTTP client with authentication:

- `get(endpoint, params=None, **kwargs)` - GET request
- `post(endpoint, data=None, **kwargs)` - POST request
- `put(endpoint, data=None, **kwargs)` - PUT request
- `delete(endpoint, **kwargs)` - DELETE request
- `authenticate()` - Manual authentication (usually automatic)

### SpotnanaTripsClient

Specialized client for trip operations:

- `create(name, user_id, company_id, **kwargs)` - Create new trip
- `get_details(trip_id)` - Get trip details
- `create_flight_trip(search_params)` - Create flight-specific trip

### Decorators

- `@spotnana_http_logger(method)` - HTTP method logging

### Exceptions

- `SpotnanaException` - Base exception class
- `SpotnanaAuthenticationError` - 401 errors
- `SpotnanaValidationError` - 400 errors
- `SpotnanaNotFoundError` - 404 errors
- `SpotnanaRateLimitError` - 429 errors
- `SpotnanaServerError` - 5xx errors
- `SpotnanaConflictError` - 409 errors
- `SpotnanaForbiddenError` - 403 errors

## Migration Guide

This client replaces the legacy `SpotnanaApi` class:

### Before (Legacy)

```python
from spotnana_api import SpotnanaApi

api = SpotnanaApi()
result = await api.get_trip_details(trip_id)
```

### After (New Client)

```python
from server.api_clients.spotnana.trip import get_trips_client

trips_client = get_trips_client()
result = await trips_client.get_details(trip_id)
```

## Contributing

When adding new functionality:

1. **Follow Patterns** - Use existing decorator and exception patterns
2. **Add Tests** - Include comprehensive test coverage
3. **Update Documentation** - Keep this README current
4. **Logging Configuration** - Update `FILE_LOGGING_CONFIG` if needed
5. **Type Hints** - Use proper type annotations throughout