"""
Spotnana API client package.

This package provides a clean, typed interface to the Spotnana API organized
by functional areas following the Spotnana OpenAPI structure.
"""

from .client import SpotnanaClient
from .exceptions import SpotnanaAuthenticationError, SpotnanaException, SpotnanaRateLimitError
from .trip import SpotnanaTripsClient, get_trips_client

__all__ = [
    "SpotnanaClient",
    "SpotnanaTripsClient",
    "get_trips_client",
    "SpotnanaException",
    "SpotnanaAuthenticationError",
    "SpotnanaRateLimitError",
]
