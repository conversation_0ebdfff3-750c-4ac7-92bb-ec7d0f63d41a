"""
Client-specific models for Spotnana API interactions.
These models are used internally by the SpotnanaClient for handling responses

Anything related to Spotnana API and used by other packages should be placed under models/api/spotnana
"""

from datetime import datetime

from pydantic import BaseModel


class TokenInfo(BaseModel):
    """Authentication token information for internal client state management."""

    token: str
    expire: float
    expires_at: datetime | None = None
