"""
Core Spotnana API client with authentication and base functionality.
"""

import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any

from fastapi.exceptions import HTTPException

from models.api.spotnana.auth.models import AuthenticationRequest, AuthenticationResponse
from server.utils.async_requests import make_delete_request, make_get_request, make_post_request, make_put_request
from server.utils.logger import logger
from server.utils.settings import settings

from .decorators import spotnana_http_logger
from .exceptions import SpotnanaAuthenticationError, SpotnanaException
from .models import TokenInfo


class SpotnanaClient:
    """
    Core Spotnana API client with authentication and base HTTP operations.

    This client handles:
    - Token-based authentication with automatic refresh
    - Rate limiting and retry logic
    - Error handling and logging
    - Base HTTP methods for API interactions
    """

    _instance = None
    _instance_lock = asyncio.Lock()

    @classmethod
    async def get_instance(cls, base_url: str | None = None):
        """Get or create a singleton instance in a coroutine-safe way."""
        async with cls._instance_lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance.__init__(base_url)
            return cls._instance

    def __init__(self, base_url: str | None = None):
        self.__validate_settings()

        self.base_url: str = str(base_url or settings.SPOTNANA_HOST)
        self.client_id: str = str(settings.SPOTNANA_CLIENT_ID)
        self.client_secret: str = str(settings.SPOTNANA_CLIENT_SECRET)
        self.company_id: str = str(settings.SPOTNANA_COMPANY_GUID)
        self.api_key: str = str(settings.SPOTNANA_API_KEY)

        self._token_info: TokenInfo | None = None
        self._auth_lock = asyncio.Lock()

    def __validate_settings(self) -> None:
        """Validate settings."""
        if not settings.SPOTNANA_HOST:
            raise SpotnanaException(default_message="SPOTNANA_HOST is not set")
        if not settings.SPOTNANA_CLIENT_ID:
            raise SpotnanaException(default_message="SPOTNANA_CLIENT_ID is not set")
        if not settings.SPOTNANA_CLIENT_SECRET:
            raise SpotnanaException(default_message="SPOTNANA_CLIENT_SECRET is not set")
        if not settings.SPOTNANA_COMPANY_GUID:
            raise SpotnanaException(default_message="SPOTNANA_COMPANY_GUID is not set")
        if not settings.SPOTNANA_API_KEY:
            raise SpotnanaException(default_message="SPOTNANA_API_KEY is not set")

    async def authenticate(self) -> None:
        """
        Authenticate with Spotnana API and store token information.

        Migrated from: SpotnanaApi.authorize()
        """
        async with self._auth_lock:
            if not self._is_token_expired():
                return

            try:
                # Type cast is safe because we validated non-None in __init__
                auth_request = AuthenticationRequest(clientId=self.client_id, clientSecret=self.client_secret)

                response = await make_post_request(
                    f"{self.base_url}/get-auth-token",
                    headers={"Content-Type": "application/json"},
                    data=auth_request.model_dump(by_alias=True),
                )

                auth_response = AuthenticationResponse(**response)
                expire_time = datetime.now() + timedelta(seconds=auth_response.expiry_time_in_seconds)

                self._token_info = TokenInfo(
                    token=auth_response.token, expire=expire_time.timestamp(), expires_at=expire_time
                )

                logger.info("Spotnana authentication successful", expires_at=expire_time.isoformat())

            except HTTPException as e:
                logger.error("Spotnana authentication failed", status_code=e.status_code, detail=str(e.detail))
                response_data = self._extract_error_detail(e.detail)
                raise SpotnanaAuthenticationError(status_code=e.status_code, response_data=response_data)
            except Exception as e:
                logger.error(
                    "Spotnana authentication failed",
                    error=str(e),
                    base_url=self.base_url,
                    client_id_prefix=self.client_id[:8],
                )
                raise SpotnanaException(default_message=f"Authentication failed: {str(e)}")

    def _extract_error_detail(self, detail: Any) -> dict[str, Any]:
        """Extract error detail from HTTPException detail."""
        return detail if isinstance(detail, dict) else {"message": str(detail)}

    def _is_token_expired(self) -> bool:
        """
        Check if the current token is expired.

        Migrated from: SpotnanaApi.is_token_expired()
        """
        if not self._token_info:
            return True

        current_time = datetime.now().timestamp()
        # Add 30 second buffer to avoid edge cases
        return current_time >= (self._token_info.expire - 30)

    async def _get_auth_headers(self) -> dict[str, str]:
        """Get authenticated headers for API requests."""
        await self.authenticate()

        if not self._token_info:
            raise SpotnanaAuthenticationError()

        # Include both Bearer token and API key for compatibility with original implementation
        return {
            "Authorization": f"Bearer {self._token_info.token}",
            "Content-Type": "application/json",
            "x-spotnana-api-key": self.api_key,
        }

    @spotnana_http_logger("GET")
    async def get(self, endpoint: str, params: dict[str, Any] | None = None, **kwargs) -> Any:
        """
        Make authenticated GET request to Spotnana API.

        Enhanced version of the generic get method from SpotnanaApi.
        Error handling and logging are handled by the decorator.
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = await self._get_auth_headers()

        response = await make_get_request(url, headers=headers, params=params, **kwargs)
        return response

    @spotnana_http_logger("POST")
    async def post(self, endpoint: str, data: dict[str, Any] | None = None, **kwargs) -> Any:
        """
        Make authenticated POST request to Spotnana API.

        Enhanced version of the generic post method from SpotnanaApi.
        Error handling and logging are handled by the decorator.
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = await self._get_auth_headers()

        response = await make_post_request(url, headers=headers, data=data, **kwargs)
        return response

    @spotnana_http_logger("PUT")
    async def put(self, endpoint: str, data: dict[str, Any] | None = None, **kwargs) -> Any:
        """
        Make authenticated PUT request to Spotnana API.
        Error handling and logging are handled by the decorator.
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = await self._get_auth_headers()

        response = await make_put_request(url, headers=headers, data=data, **kwargs)
        return response

    @spotnana_http_logger("DELETE")
    async def delete(self, endpoint: str, **kwargs) -> Any:
        """
        Make authenticated DELETE request to Spotnana API.
        Error handling and logging are handled by the decorator.
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = await self._get_auth_headers()

        response = await make_delete_request(url, headers=headers, **kwargs)
        return response

    @property
    def token_info(self) -> TokenInfo | None:
        """Get current token information."""
        return self._token_info

    @property
    def is_authenticated(self) -> bool:
        """Check if client is currently authenticated."""
        return self._token_info is not None and not self._is_token_expired()
