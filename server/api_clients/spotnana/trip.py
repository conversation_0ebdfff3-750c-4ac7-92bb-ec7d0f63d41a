"""
Spotnana Trip API client.

This module provides a clean interface for Spotnana Trip API operations including:
- Trip creation and management
- Trip details retrieval
- Flight-specific trip operations

Functions migrated from:
- SpotnanaApi.get_trip_details() → SpotnanaTripsClient.get_details()
- SpotnanaApi.create_trip() → SpotnanaTripsClient.create()
- FlightSearchTools.create_trip_spotnana() → SpotnanaTripsClient.create_flight_trip()
- FlightSearchTools.get_trip_details_spotnana() → Use SpotnanaTripsClient.get_details()
"""

import json
import uuid
from typing import TYPE_CHECKING
from uuid import UUID

from models.adapters.spotnana.trip_adapter import TripCreateAdapter, TripDetailsAdapter
from models.api.spotnana.trip.models import (
    CreateTripRequest,
    EntityNonUUIDId,
    TripV3DetailsResponse,
    UserId,
)
from server.utils.logger import logger

from .client import SpotnanaClient
from .exceptions import SpotnanaException

if TYPE_CHECKING:
    from typing import Optional


# Module-level singleton instance
if TYPE_CHECKING:
    _trips_client_instance: Optional["SpotnanaTripsClient"] = None
else:
    _trips_client_instance = None


def get_trips_client() -> "SpotnanaTripsClient":
    """
    Get shared instance of SpotnanaTripsClient.

    This factory function provides a singleton instance of the trips client,
    which is efficient for sharing authentication and reducing resource usage.

    Returns:
        SpotnanaTripsClient: Shared singleton instance
    """
    global _trips_client_instance
    if _trips_client_instance is None:
        _trips_client_instance = SpotnanaTripsClient()
    return _trips_client_instance


class SpotnanaTripsClient:
    """
    Spotnana Trip API client for trip management operations.

    This client handles:
    - Trip creation with proper validation
    - Trip details retrieval with type-safe adapters
    - Flight-specific trip operations
    - Proper error handling and logging
    """

    def __init__(self, base_client: SpotnanaClient | None = None):
        """
        Initialize Spotnana Trips client.

        Args:
            base_client: Optional pre-configured SpotnanaClient instance.
                        If None, a new client will be created.
        """
        self._client = base_client or SpotnanaClient()

    def _create_trip_details_adapter(self, response: dict) -> TripDetailsAdapter:
        """
        Create TripDetailsAdapter from API response.

        Args:
            response: Raw API response

        Returns:
            TripDetailsAdapter: Type-safe adapter for trip details
        """
        try:
            # Convert raw response to Pydantic model first, then wrap in adapter
            trip_model = TripV3DetailsResponse.model_validate(response)
            return TripDetailsAdapter(trip_model)
        except Exception as e:
            logger.warning("trip_details_adapter_creation_failed", error=str(e), response_type=type(response).__name__)
            # For backward compatibility, create a mock model if validation fails
            raise e

    def _create_trip_create_adapter(self, response: dict) -> TripCreateAdapter:
        """
        Create TripCreateAdapter from API response.

        Args:
            response: Raw API response

        Returns:
            TripCreateAdapter: Type-safe adapter for trip creation
        """
        try:
            # Convert raw response to Pydantic model first, then wrap in adapter
            create_model = EntityNonUUIDId.model_validate(response)
            return TripCreateAdapter(create_model)
        except Exception as e:
            logger.warning("trip_create_adapter_creation_failed", error=str(e), response_type=type(response).__name__)
            # For backward compatibility, create a mock model if validation fails
            raise e

    async def get_details(self, trip_id: str) -> TripDetailsAdapter:
        """
        Get detailed trip information by trip ID.

        Migrated from: SpotnanaApi.get_trip_details()
        Also replaces: FlightSearchTools.get_trip_details_spotnana()

        Args:
            trip_id: Spotnana trip identifier

        Returns:
            TripDetailsAdapter: Type-safe adapter for detailed trip information including PNRs and status

        Raises:
            SpotnanaException: When API call fails or trip not found
        """
        try:
            logger.info("trip_details_fetch_started", spotnana_trip_id=trip_id)

            # Get raw response from base client
            response = await self._client.get(f"v3/trips/{trip_id}/detail")

            # Create adapter from response
            adapter = self._create_trip_details_adapter(response)

            logger.info("trip_details_fetch_completed", spotnana_trip_id=trip_id)
            return adapter

        except Exception as e:
            logger.error("trip_details_fetch_failed", spotnana_trip_id=trip_id, error=str(e))
            if isinstance(e, SpotnanaException):
                raise
            raise SpotnanaException(default_message=f"Failed to get trip details: {str(e)}")

    async def create(
        self,
        trip_name: str,
        trip_description: str | None = None,
        spotnana_user_id: UUID | None = None,
        registrar_id: UUID | None = None,
    ) -> TripCreateAdapter:
        """
        Create a new trip with the provided parameters.

        Migrated from: SpotnanaApi.create_trip()

        Args:
            trip_name: Name of the trip
            trip_description: Optional trip description
            user_id: User UUID
            registrar_id: Registrar UUID

        Returns:
            TripCreateAdapter: Type-safe adapter for trip creation response with trip ID

        Raises:
            SpotnanaException: When API call fails or validation errors occur
        """
        user_id_for_log = str(spotnana_user_id) if spotnana_user_id else "null"
        try:
            logger.info("trip_creation_started", spotnana_user_id=user_id_for_log)

            # Create request model with validation
            request_data = CreateTripRequest(
                tripName=trip_name,
                tripDescription=trip_description,
                userId=UserId(id=spotnana_user_id) if spotnana_user_id else None,
                registrarId=UserId(id=registrar_id) if registrar_id else None,
            )

            # Convert to API format with UUID serialization
            api_params = request_data.model_dump(exclude_none=True, mode="json")

            # Get raw response from base client
            response = await self._client.post("v2/trips", data=api_params)

            # Create adapter from response
            adapter = self._create_trip_create_adapter(response)

            logger.info(
                "trip_creation_completed",
                spotnana_trip_id=adapter.trip_id,
                spotnana_user_id=user_id_for_log,
            )
            return adapter

        except Exception as e:
            logger.error("trip_creation_failed", spotnana_user_id=user_id_for_log, error=str(e))
            if isinstance(e, SpotnanaException):
                raise
            raise SpotnanaException(default_message=f"Failed to create trip: {str(e)}")

    async def create_flight_trip(self, flight_params: str, user_guid: UUID) -> TripCreateAdapter:
        """
        Create a flight-specific trip with generated trip name and description.

        Migrated from: FlightSearchTools.create_trip_spotnana()

        Args:
            flight_params: JSON string containing flight parameters including arrival_airport_code
            user_guid: User UUID for both userId and registrarId

        Returns:
            TripCreateAdapter: Type-safe adapter for trip creation response with trip ID

        Raises:
            SpotnanaException: When API call fails, JSON parsing fails, or validation errors occur
        """
        try:
            logger.info("flight_trip_creation_started", user_id=str(user_guid))

            # Parse flight parameters
            try:
                flight_params_dict = json.loads(flight_params)
            except json.JSONDecodeError as e:
                logger.error("flight_params_parse_failed", user_id=str(user_guid), error=str(e))
                raise SpotnanaException(default_message=f"Invalid flight_params JSON: {str(e)}")

            # Generate trip identifiers
            trip_uuid = uuid.uuid4()
            arrival_airport = flight_params_dict.get("arrival_airport_code") or "Unknown"
            trip_description = f"{arrival_airport} - {str(trip_uuid)}"

            # Use the standard create method with explicit parameters
            adapter = await self.create(
                trip_name=trip_description,
                trip_description=trip_description,
                spotnana_user_id=user_guid,
                registrar_id=user_guid,
            )

            logger.info(
                "flight_trip_creation_completed",
                spotnana_user_id=str(user_guid),
                trip_id=adapter.trip_id,
                arrival_airport=arrival_airport,
            )
            return adapter

        except Exception as e:
            logger.error("flight_trip_creation_failed", user_id=str(user_guid), error=str(e))
            if isinstance(e, SpotnanaException):
                raise
            raise SpotnanaException(default_message=f"Failed to create flight trip: {str(e)}")
