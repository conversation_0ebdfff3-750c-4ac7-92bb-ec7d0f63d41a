from __future__ import annotations

from datetime import datetime
from typing import Any, Sequence, Tuple

from sqlalchemy import Foreign<PERSON>ey, delete, func, select, update
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.engine.row import Row
from sqlalchemy.orm import Mapped, mapped_column

from server.database.models.chat_thread import ChatThread
from server.utils.pg_connector import Base, async_session


class Checkpoint(Base):
    __tablename__ = "checkpoints"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    thread_id: Mapped[int] = mapped_column(ForeignKey(ChatThread.id))
    created_date: Mapped[datetime] = mapped_column(server_default=func.current_timestamp(), nullable=False)
    data: Mapped[dict | list] = mapped_column(type_=JSONB)
    input_tokens: Mapped[int | None] = mapped_column(nullable=True)
    output_tokens: Mapped[int | None] = mapped_column(nullable=True)

    def __init__(
        self,
        thread_id: int,
        data: dict[str, Any] | list = [],
        input_tokens: int | None = None,
        output_tokens: int | None = None,
    ):
        self.thread_id = thread_id
        self.data = data
        self.input_tokens = input_tokens
        self.output_tokens = output_tokens

    @staticmethod
    async def from_thread_id(
        thread_id: int, limit: int | None = None
    ) -> Sequence[Row[Tuple[Checkpoint]]] | Row[Tuple[Checkpoint]] | None:
        async with async_session() as session:
            async with session.begin():
                query = (
                    select(Checkpoint)
                    .where(Checkpoint.thread_id == thread_id)
                    .order_by(Checkpoint.id.asc())
                    .limit(limit)
                )
                result = (await session.execute(query)).fetchall()

                if limit == 1 and result is not None:
                    return result[0]

                return result

    @staticmethod
    async def from_id(id: int) -> Row[Tuple[Checkpoint]] | None:
        async with async_session() as session:
            async with session.begin():
                query = select(Checkpoint).where(Checkpoint.id == id)
                result = (await session.execute(query)).fetchone()

                return result

    @staticmethod
    async def new_checkpoint(checkpoint: Checkpoint):
        async with async_session() as session:
            async with session.begin():
                session.add(checkpoint)

    @staticmethod
    async def new_checkpoint_batch(checkpoints: list[Checkpoint]):
        async with async_session() as session:
            async with session.begin():
                session.add_all(checkpoints)

    @staticmethod
    async def delete_threads(threads_ids: list[int]):
        async with async_session() as session:
            async with session.begin():
                query = delete(Checkpoint).where(Checkpoint.thread_id.in_(threads_ids))
                await session.execute(query)
                query = delete(ChatThread).where(ChatThread.id.in_(threads_ids))
                await session.execute(query)

    @staticmethod
    async def update_field(message_id: int, data: dict):
        async with async_session() as session:
            async with session.begin():
                query = update(Checkpoint).where(Checkpoint.id == message_id).values(data=data)
                await session.execute(query)
