from typing import Any, Optional

from server.utils.async_requests import make_get_request
from server.utils.logger import logger
from server.utils.settings import settings


class FlightAwareApi:
    def __init__(self) -> None:
        self.base_url = "https://aeroapi.flightaware.com/aeroapi"
        self.headers = {"x-apikey": settings.FLIGHTAWARE_API_KEY, "Content-Type": "application/json"}

    async def get_flight_info(self, flight_ident: str) -> Optional[dict[str, Any]]:
        """
        Get flight information using FlightAware AeroAPI

        Args:
            flight_ident: Flight identifier (e.g., "UAL123", "N12345")

        Returns:
            Flight information dict or None if not found
        """
        try:
            url = f"{self.base_url}/flights/{flight_ident}"
            response = await make_get_request(url=url, headers=self.headers)
            return response
        except Exception as e:
            logger.error(f"FlightAware API error for flight {flight_ident}: {e}")
            return None

    async def get_airport_weather_observations(
        self, airport_id: str, params: dict[str, Any] | None = None
    ) -> Optional[dict[str, Any]]:
        """
        Get weather observations for an airport using FlightAware AeroAPI

        Args:
            airport_id: Airport identifier (e.g., "KSFO", "KLAX")
            params: Optional query parameters (start_date, end_date, etc.)

        Returns:
            Weather observations dict or None if not found
        """
        try:
            url = f"{self.base_url}/airports/{airport_id}/weather/observations"
            response = await make_get_request(url=url, headers=self.headers, params=params)
            return response
        except Exception as e:
            logger.error(f"FlightAware weather observations API error for airport {airport_id}: {e}")
            return None

    async def get_airport_weather_forecast(
        self, airport_id: str, params: dict[str, Any] | None = None
    ) -> Optional[dict[str, Any]]:
        """
        Get weather forecast for an airport using FlightAware AeroAPI

        Args:
            airport_id: Airport identifier (e.g., "KSFO", "KLAX")
            params: Optional query parameters (start_date, end_date, etc.)

        Returns:
            Weather forecast dict or None if not found
        """
        try:
            url = f"{self.base_url}/airports/{airport_id}/weather/forecast"
            response = await make_get_request(url=url, headers=self.headers, params=params)
            return response
        except Exception as e:
            logger.error(f"FlightAware weather forecast API error for airport {airport_id}: {e}")
            return None


flightaware_api = FlightAwareApi()
