"""
Utility functions for handling enum serialization consistently across adapters.

This module provides shared utilities to eliminate code duplication in adapter classes,
particularly for enum value serialization patterns.
"""

from typing import Any


def serialize_enum_value(enum_value: Any) -> str | None:
    """
    Safely serialize an enum value to string.

    Handles different enum types consistently across the application:
    - Pydantic enums with .value attribute
    - Standard Python enums
    - String values (pass-through)
    - None values

    Args:
        enum_value: The enum value to serialize (can be None)

    Returns:
        String representation of the enum value, or None if input is None

    Example:
        >>> serialize_enum_value(TripStatus.CONFIRMED)
        "CONFIRMED"
        >>> serialize_enum_value(None)
        None
        >>> serialize_enum_value("already_string")
        "already_string"
    """
    if enum_value is None:
        return None

    # Handle enums with .value attribute (most Pydantic enums)
    if hasattr(enum_value, "value"):
        return str(enum_value.value)

    # Fallback to string conversion for other types
    return str(enum_value)


def serialize_enum_value_required(enum_value: Any, field_name: str = "field") -> str:
    """
    Serialize an enum value to string, raising an error if None.

    Use this when the enum value is required and None would indicate a data error.

    Args:
        enum_value: The enum value to serialize (must not be None)
        field_name: Name of the field for error messages

    Returns:
        String representation of the enum value

    Raises:
        ValueError: If enum_value is None

    Example:
        >>> serialize_enum_value_required(TripStatus.CONFIRMED, "trip_status")
        "CONFIRMED"
        >>> serialize_enum_value_required(None, "trip_status")
        ValueError: Required field 'trip_status' cannot be None
    """
    if enum_value is None:
        msg = f"Required field '{field_name}' cannot be None"
        raise ValueError(msg)

    result = serialize_enum_value(enum_value)
    if result is None:
        msg = f"Required field '{field_name}' serialized to None"
        raise ValueError(msg)
    return result
