from enum import Enum

import emoji

from server.utils.async_requests import make_post_request
from server.utils.logger import logger
from server.utils.settings import settings


class MOBILE_PLATFORMS(Enum):
    ANDROID = "Android"
    IOS = "iOS"


class PushNotification:
    ONESIGNAL_URL = "https://api.onesignal.com/notifications"

    IOS_APP_URL = "ottotheagent://app.ottotheagent.com"
    HEADERS_IOS = {
        "Content-Type": "application/json; charset=utf-8",
        "Authorization": f"Bearer {settings.IOS_AUTH_KEY}",
    }

    ANDROID_APP_URL = "ottotheagent://app.ottotheagent.com"
    HEADERS_ANDROID = {
        "Content-Type": "application/json; charset=utf-8",
        "Authorization": f"Bearer {settings.ANDROID_AUTH_KEY}",
    }

    def __init__(self, email: str, title: str, description: str, deeplink: str | None = None):
        self.email = email
        self.title = title
        self.description = description
        self.deeplink = deeplink

    async def send(self, platform: MOBILE_PLATFORMS | None = None):
        self._update_emoji()

        if platform is None or platform == MOBILE_PLATFORMS.ANDROID:
            payload_android = self._prepare_payload(MOBILE_PLATFORMS.ANDROID)
            logger.debug(
                "push_notification_payload_prepared", platform="android", payload_size=len(str(payload_android))
            )
            await make_post_request(self.ONESIGNAL_URL, self.HEADERS_ANDROID, payload_android)

        if platform is None or platform == MOBILE_PLATFORMS.IOS:
            payload_ios = self._prepare_payload(MOBILE_PLATFORMS.IOS)
            await make_post_request(self.ONESIGNAL_URL, self.HEADERS_IOS, payload_ios)

    def _update_emoji(self):
        self.title = emoji.emojize(self.title)
        self.description = emoji.emojize(self.description)

    def _prepare_payload(self, platform: MOBILE_PLATFORMS):
        payload = {
            "app_id": settings.APP_ID_ANDROID if platform == MOBILE_PLATFORMS.ANDROID else settings.APP_ID_IOS,
            "headings": {"en": self.title},
            "contents": {"en": self.description},
            "target_channel": "push",
            "include_aliases": {"external_id": [self.email]},
        }

        if self.deeplink:
            base_url: str = self.ANDROID_APP_URL if platform == MOBILE_PLATFORMS.ANDROID else self.IOS_APP_URL
            payload["url"] = f"{base_url}/{self.deeplink}"

        return payload
