import asyncio

from motor.motor_asyncio import (
    AsyncIOMotorClient,
    AsyncIOMotorCollection,
    AsyncIOMotorDatabase,
)

from server.utils.settings import settings

MONGO_DETAILS = (
    f"mongodb://{settings.MONGO_USER}:{settings.MONGO_PASSWORD}@{settings.MONGO_HOST}:{settings.MONGO_PORT}/"
)


mongo_client: AsyncIOMotorClient = AsyncIOMotorClient(MONGO_DETAILS)
mongo_client.get_io_loop = asyncio.get_running_loop

mongo_database: AsyncIOMotorDatabase = mongo_client.get_database(settings.MONGO_DATABASE)
user_preferences_collection: AsyncIOMotorCollection = mongo_database.get_collection("user_profile_data")
bookings_collection: AsyncIOMotorCollection = mongo_database.get_collection("bookings")
google_maps_cache_collection: AsyncIOMotorCollection = mongo_database.get_collection("gogole_maps_cache")
trip_travel_context_collection: AsyncIOMotorCollection = mongo_database.get_collection("trip_travel_context")
google_calendar_events_collection: AsyncIOMotorCollection = mongo_database.get_collection("google_calendar_events")
test_vgs_collection: AsyncIOMotorCollection = mongo_database.get_collection("test_vgs")
payment_profile_collection: AsyncIOMotorCollection = mongo_database.get_collection("payment_profile")
users_whitelist_collection: AsyncIOMotorCollection = mongo_database.get_collection("users_whitelist")
hotels_data_cache_collection: AsyncIOMotorCollection = mongo_database.get_collection("hotels_data_cache")
spotnana_webhook_events_collection: AsyncIOMotorCollection = mongo_database.get_collection("spotnana_webhook_events")
user_feature_flags_collection: AsyncIOMotorCollection = mongo_database.get_collection("user_feature_flags")
app_config_collection: AsyncIOMotorCollection = mongo_database.get_collection("app_config")
trip_context_v2_collection: AsyncIOMotorCollection = mongo_database.get_collection("trip_context_v2")
user_activities: AsyncIOMotorCollection = mongo_database.get_collection("user_activities")

flight_credits_collection: AsyncIOMotorCollection = mongo_database.get_collection("flight_credits")
converse_hotel_state_collection: AsyncIOMotorCollection = mongo_database.get_collection("converse_hotel_state")
preferred_airline_per_airport_collection: AsyncIOMotorCollection = mongo_database.get_collection(
    "preferred_airline_per_airport"
)

sample_trips_collection: AsyncIOMotorCollection = mongo_database.get_collection("sample_trips")

hotels_data_cache_dev_collection: AsyncIOMotorCollection | None = None
hotels_data_cache_stg_collection: AsyncIOMotorCollection | None = None
flight_search_evaluation_collection: AsyncIOMotorCollection | None = None

if settings.all_secrets_dict.get("MONGO_STG_USER") and settings.all_secrets_dict.get("MONGO_STG_PASSWORD"):
    MONGO_STG_DETAILS = f"mongodb://{settings.all_secrets_dict.get('MONGO_STG_USER')}:{settings.all_secrets_dict.get('MONGO_STG_PASSWORD')}@{settings.all_secrets_dict.get('MONGO_STG_HOST')}:{settings.all_secrets_dict.get('MONGO_PORT')}/"
    mongo_stg_client: AsyncIOMotorClient = AsyncIOMotorClient(MONGO_STG_DETAILS)
    mongo_stg_client.get_io_loop = asyncio.get_running_loop
    flight_search_evaluation_collection = mongo_stg_client.get_database(
        settings.all_secrets_dict.get("MONGO_DATABASE")
    ).get_collection("flight_search_evaluation")

if settings.all_secrets_dict.get("MONGO_DEV_USER") and settings.is_live:
    MONGO_DEV_DETAILS = f"mongodb://{settings.all_secrets_dict.get('MONGO_DEV_USER')}:{settings.all_secrets_dict.get('MONGO_DEV_PASSWORD')}@{settings.all_secrets_dict.get('MONGO_DEV_HOST')}:{settings.all_secrets_dict.get('MONGO_DEV_PORT')}/"
    mongo_dev_client: AsyncIOMotorClient = AsyncIOMotorClient(MONGO_DEV_DETAILS)
    mongo_dev_client.get_io_loop = asyncio.get_running_loop
    hotels_data_cache_dev_collection = mongo_dev_client.get_database(
        settings.all_secrets_dict.get("MONGO_DEV_DATABASE")
    ).get_collection("hotels_data_cache")

if settings.all_secrets_dict.get("MONGO_STG_USER") and settings.is_live:
    MONGO_STG_DETAILS_HOTELS = f"mongodb://{settings.all_secrets_dict.get('MONGO_STG_USER')}:{settings.all_secrets_dict.get('MONGO_STG_PASSWORD')}@{settings.all_secrets_dict.get('MONGO_STG_HOST')}:{settings.all_secrets_dict.get('MONGO_STG_PORT')}/"
    mongo_stg_hotels_client: AsyncIOMotorClient = AsyncIOMotorClient(MONGO_STG_DETAILS_HOTELS)
    mongo_stg_hotels_client.get_io_loop = asyncio.get_running_loop
    hotels_data_cache_stg_collection = mongo_stg_hotels_client.get_database(
        settings.all_secrets_dict.get("MONGO_STG_DATABASE")
    ).get_collection("hotels_data_cache")

user_profile_personal_information_collection: AsyncIOMotorCollection = mongo_database.get_collection(
    "user_profile_personal_information"
)
user_profile_payment_information_collection: AsyncIOMotorCollection = mongo_database.get_collection(
    "user_profile_payment_information"
)

user_profile_loyalty_programs_collection: AsyncIOMotorCollection = mongo_database.get_collection(
    "user_profile_loyalty_programs"
)
