import json
import ssl
import time
from typing import Any

import aiohttp
from aiohttp_retry import ExponentialRetry, RetryClient
from fastapi.exceptions import HTTPException

from server.utils.logger import logger
from server.utils.request_metrics import track_active_requests, track_request_metrics
from server.utils.settings import settings

# Define retry strategy once for all requests
RETRY_OPTIONS = ExponentialRetry(
    attempts=5,
    start_timeout=0.5,
    max_timeout=5,
    factor=2,
    statuses={429, 500, 502, 503, 504},
)


OTTO_DEFAULT_TIMEOUT = aiohttp.ClientTimeout(total=60)  # Default timeout for all requests: 60s


async def make_get_request(
    url: str,
    headers: dict[str, str] | None = None,
    params: dict[str, Any] | None = None,
    proxy: str | None = None,
) -> dict[str, Any]:
    # Start tracking active request
    track_active_requests(method="GET", url=url)
    start_time = time.time()

    final_proxy = proxy
    if settings.AWS_SPOTNANA_PROXY and proxy is None:
        final_proxy = settings.AWS_SPOTNANA_PROXY

    try:
        async with RetryClient(retry_options=RETRY_OPTIONS, timeout=OTTO_DEFAULT_TIMEOUT) as session:
            async with session.get(url, headers=headers, params=params, proxy=final_proxy) as res:
                raw_res_txt = await res.text()
                raw_res = {}
                try:
                    raw_res = await res.json()
                except (json.JSONDecodeError, aiohttp.ContentTypeError):
                    raw_res = {"error": raw_res_txt}

                # Calculate request size (params as query string)
                request_size = len(str(params)) if params else 0
                # Calculate response size
                response_size = len(raw_res_txt)

                # Track metrics
                await track_request_metrics(
                    method="GET",
                    url=url,
                    status_code=res.status,
                    duration=time.time() - start_time,
                    request_size=request_size,
                    response_size=response_size,
                )

                res.raise_for_status()
                return raw_res
    except aiohttp.ClientResponseError as e:
        logger.error(f"Error: {e.message}")

        # Track metrics for failed request
        await track_request_metrics(
            method="GET",
            url=url,
            status_code=e.status,
            duration=time.time() - start_time,
            request_size=len(str(params)) if params else 0,
            response_size=0,
        )

        raise HTTPException(status_code=e.status, detail=str(e))
    except TimeoutError as e:
        logger.error(f"Timeout error: {e}")
        # Handle timeout error
        await track_request_metrics(
            method="GET",
            url=url,
            status_code=521,
            duration=time.time() - start_time,
            request_size=len(str(params)) if params else 0,
            response_size=0,
        )
        raise HTTPException(status_code=504, detail="Request timed out")
    finally:
        # Decrement active request counter
        track_active_requests(method="GET", url=url, should_decrement=True)


async def make_post_request_with_any_data(
    url: str,
    headers: dict[str, str] | None = None,
    data: Any | None = None,
    proxy: str | None = None,
    ssl_cert_path: str | None = None,
    auth: tuple[str, str] | None = None,
):
    ssl_context = None
    if ssl_cert_path is not None:
        ssl_context = ssl.create_default_context()
        ssl_context.load_verify_locations(cafile=ssl_cert_path)

    final_proxy = proxy
    if settings.AWS_SPOTNANA_PROXY and proxy is None:
        final_proxy = settings.AWS_SPOTNANA_PROXY

    raw_res = {}
    raw_res_txt = ""

    # Start tracking active request
    track_active_requests(method="POST", url=url)
    start_time = time.time()

    try:
        basic_auth = None
        if auth:
            basic_auth = aiohttp.BasicAuth(auth[0], auth[1])
        async with RetryClient(retry_options=RETRY_OPTIONS, timeout=OTTO_DEFAULT_TIMEOUT) as session:
            async with session.post(
                url,
                headers=headers,
                auth=basic_auth if basic_auth else None,
                data=data,
                proxy=final_proxy,
                ssl=ssl_context,
            ) as res:
                raw_res_txt = await res.text()
                try:
                    raw_res = await res.json()
                except (json.JSONDecodeError, aiohttp.ContentTypeError):
                    raw_res = {"error": raw_res_txt}  # Wrap non-JSON response in a dict

                # Calculate request size
                request_size = len(str(data)) if data else 0
                # Calculate response size
                response_size = len(raw_res_txt)

                # Track metrics
                await track_request_metrics(
                    method="POST",
                    url=url,
                    status_code=res.status,
                    duration=time.time() - start_time,
                    request_size=request_size,
                    response_size=response_size,
                )

                res.raise_for_status()
                return raw_res
    except aiohttp.ClientResponseError as e:
        logger.error(f"Error: {e.message}, Raw response text: {raw_res_txt}")
        error_response = raw_res if isinstance(raw_res, dict) else raw_res_txt

        # Track metrics for failed request
        await track_request_metrics(
            method="POST",
            url=url,
            status_code=e.status,
            duration=time.time() - start_time,
            request_size=len(str(data)) if data else 0,
            response_size=len(raw_res_txt),
        )

        raise HTTPException(status_code=e.status, detail=error_response)
    finally:
        # Decrement active request counter
        track_active_requests(method="POST", url=url, should_decrement=True)


async def make_post_request(
    url: str,
    headers: dict[str, str] | None = None,
    data: dict[str, Any] | None = None,
    proxy: str | None = None,
    ssl_cert_path: str | None = None,
    request_filename: str | None = None,
    response_filename: str | None = None,
):
    ssl_context = None
    if ssl_cert_path is not None:
        ssl_context = ssl.create_default_context()
        ssl_context.load_verify_locations(cafile=ssl_cert_path)

    raw_res: dict[str, Any] = {}
    raw_res_txt = ""

    # Start tracking active request
    track_active_requests(method="POST", url=url)
    start_time = time.time()

    final_proxy = proxy
    if settings.AWS_SPOTNANA_PROXY and proxy is None:
        final_proxy = settings.AWS_SPOTNANA_PROXY

    if settings.is_local:
        if request_filename:
            with open(request_filename, "w") as file:
                if data:
                    file.write(json.dumps(data, indent=4))

    try:
        async with RetryClient(retry_options=RETRY_OPTIONS, timeout=OTTO_DEFAULT_TIMEOUT) as session:
            async with session.post(
                url,
                headers=headers,
                data=json.dumps(data),
                proxy=final_proxy,
                ssl=ssl_context,
            ) as res:
                raw_res_txt = await res.text()
                try:
                    raw_res = await res.json()
                except (json.JSONDecodeError, aiohttp.ContentTypeError):
                    raw_res = {"error": raw_res_txt}
                if settings.is_local:
                    if response_filename:
                        with open(response_filename, "w") as file:
                            file.write(json.dumps(raw_res, indent=4))

                # Calculate request size
                request_size = len(json.dumps(data)) if data else 0
                # Calculate response size
                response_size = len(raw_res_txt)

                # Track metrics
                await track_request_metrics(
                    method="POST",
                    url=url,
                    status_code=res.status,
                    duration=time.time() - start_time,
                    request_size=request_size,
                    response_size=response_size,
                )

                res.raise_for_status()
                return raw_res
    except aiohttp.ClientResponseError as e:
        logger.error(f"Error: {e.message}, Raw response text: {raw_res_txt}")
        error_response = raw_res if isinstance(raw_res, dict) else raw_res_txt

        # Track metrics for failed request
        await track_request_metrics(
            method="POST",
            url=url,
            status_code=e.status,
            duration=time.time() - start_time,
            request_size=len(json.dumps(data)) if data else 0,
            response_size=len(raw_res_txt),
        )

        raise HTTPException(status_code=e.status, detail=error_response)
    finally:
        # Decrement active request counter
        track_active_requests(method="POST", url=url, should_decrement=True)


async def make_put_request(
    url: str, headers: dict[str, str] | None = None, data: dict[str, Any] | None = None, proxy: str | None = None
):
    final_proxy = proxy
    if settings.AWS_SPOTNANA_PROXY and proxy is None:
        final_proxy = settings.AWS_SPOTNANA_PROXY

    raw_res = {}
    raw_res_txt = ""

    # Start tracking active request
    track_active_requests(method="PUT", url=url)
    start_time = time.time()

    try:
        async with RetryClient(retry_options=RETRY_OPTIONS, timeout=OTTO_DEFAULT_TIMEOUT) as session:
            async with session.put(url, headers=headers, data=json.dumps(data), proxy=final_proxy) as res:
                raw_res_txt = await res.text()
                try:
                    raw_res = await res.json()
                except (json.JSONDecodeError, aiohttp.ContentTypeError):
                    raw_res = {"error": raw_res_txt}

                # Calculate request size
                request_size = len(json.dumps(data)) if data else 0
                # Calculate response size
                response_size = len(raw_res_txt)

                # Track metrics
                await track_request_metrics(
                    method="PUT",
                    url=url,
                    status_code=res.status,
                    duration=time.time() - start_time,
                    request_size=request_size,
                    response_size=response_size,
                )

                res.raise_for_status()
                return raw_res
    except aiohttp.ClientResponseError as e:
        logger.error(f"Error: {e.message}, Raw response text: {raw_res_txt}")
        error_response = raw_res if isinstance(raw_res, dict) else raw_res_txt

        # Track metrics for failed request
        await track_request_metrics(
            method="PUT",
            url=url,
            status_code=e.status,
            duration=time.time() - start_time,
            request_size=len(json.dumps(data)) if data else 0,
            response_size=len(raw_res_txt),
        )

        raise HTTPException(status_code=e.status, detail=error_response)
    finally:
        # Decrement active request counter
        track_active_requests(method="PUT", url=url, should_decrement=True)


async def make_delete_request(url: str, headers: dict[str, str] | None = None):
    raw_res = {}
    raw_res_txt = ""

    # Start tracking active request
    track_active_requests(method="DELETE", url=url)
    start_time = time.time()

    try:
        async with RetryClient(retry_options=RETRY_OPTIONS, timeout=OTTO_DEFAULT_TIMEOUT) as session:
            async with session.delete(url, headers=headers) as res:
                raw_res_txt = await res.text()
                try:
                    raw_res = await res.json()
                except (json.JSONDecodeError, aiohttp.ContentTypeError):
                    raw_res = {"error": raw_res_txt}

                # Calculate request size (headers only)
                request_size = len(str(headers)) if headers else 0
                # Calculate response size
                response_size = len(raw_res_txt)

                # Track metrics
                await track_request_metrics(
                    method="DELETE",
                    url=url,
                    status_code=res.status,
                    duration=time.time() - start_time,
                    request_size=request_size,
                    response_size=response_size,
                )

                res.raise_for_status()
                return raw_res
    except aiohttp.ClientResponseError as e:
        logger.error(f"Error: {e.message}, Raw response text: {raw_res_txt}")
        error_response = raw_res if isinstance(raw_res, dict) else raw_res_txt

        # Track metrics for failed request
        await track_request_metrics(
            method="DELETE",
            url=url,
            status_code=e.status,
            duration=time.time() - start_time,
            request_size=len(str(headers)) if headers else 0,
            response_size=len(raw_res_txt),
        )

        raise HTTPException(status_code=e.status, detail=error_response)
    finally:
        # Decrement active request counter
        track_active_requests(method="DELETE", url=url, should_decrement=True)
