import asyncio
import io
import json
from collections import defaultdict
from typing import Any, Coroutine, Dict, List, Optional, Union

import boto3
from botocore.config import Config
from botocore.response import StreamingBody

from server.services.task_manager.task_manager import GlobalTaskManager
from server.utils.logger import logger
from server.utils.settings import settings

BATCH_INTERVAL_SECONDS = 5
BATCH_SIZE = 20
S3_UPLOAD_RETRY_COUNT = 3


class S3Utils:
    """Utility class for S3 operations."""

    _instance = None

    def __new__(cls):
        """Implement singleton pattern."""
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self) -> None:
        """Initialize S3 client using settings."""
        if not hasattr(self, "initialized"):
            retry_config = Config(
                retries={
                    "max_attempts": S3_UPLOAD_RETRY_COUNT,
                    "mode": "standard",  # Standard includes exponential backoff
                }
            )
            self.s3_client = boto3.client(service_name="s3", config=retry_config)
            self._upload_queue = defaultdict(list)
            self._upload_task = None
            self._batch_interval = BATCH_INTERVAL_SECONDS  # seconds
            self._batch_size = BATCH_SIZE  # max files per batch
            self._is_worker_running = False
            self._queue_lock = asyncio.Lock()
            self.initialized = True

    async def log_trip_artifact_to_s3(
        self,
        root_folder: str,
        file_name: str,
        content: Union[str, bytes, Dict[str, Any], List[Any]],
        bucket_name: Optional[str] = None,
        file_type: str = "json",
        extra_path: Optional[str] = None,
        queue_for_batch: bool = True,
    ) -> Optional[str]:
        """
        Asynchronously write a trip artifact to S3 under the /<otto_env>/<root_folder>/ folder structure.
        If queue_for_batch is True, the file will be added to a queue for batch processing.
        Otherwise, it will be uploaded immediately in a background task.

        Args:
            bucket_name: The name of the S3 bucket
            root_folder: Root folder, could be `trip_432` or `user_32`
            file_name: The name of the file to create (without extension)
            content: The content to write to the file
            file_type: The type of file to create ("json", "csv", "log")
            extra_path: Optional additional path elements between root_folder and filename
            queue_for_batch: Whether to queue the file for batch processing or upload immediately

        Returns:
            Optional[str]: The S3 URI of the uploaded file if successful, None otherwise
        """
        try:
            extension = file_type.lower()

            if file_type == "json":
                if isinstance(content, (dict, list)):
                    content_to_upload = json.dumps(content, indent=2, default=str)
                else:
                    content_to_upload = str(content)
            else:
                if not isinstance(content, (str, bytes)):
                    content_to_upload = str(content)
                else:
                    content_to_upload = content

            if isinstance(content_to_upload, str):
                content_to_upload = content_to_upload.encode("utf-8")

            base_path = f"{settings.OTTO_ENV.lower()}/{root_folder}"
            if extra_path:
                full_path = f"{base_path}/{extra_path}/{file_name}.{extension}"
            else:
                full_path = f"{base_path}/{file_name}.{extension}"

            bucket_name = bucket_name or settings.TRIP_ARTIFACT_BUCKET_NAME
            assert bucket_name, "Bucket name must be provided"
            s3_uri = f"s3://{bucket_name}/{full_path}"

            if queue_for_batch:
                async with self._queue_lock:
                    self._upload_queue[bucket_name].append({"key": full_path, "body": content_to_upload, "uri": s3_uri})
                    self._ensure_worker_running()
                return s3_uri
            else:
                try:
                    task = asyncio.create_task(
                        self._upload_single_file(bucket_name, full_path, content_to_upload, s3_uri),
                        name=f"s3_upload_{bucket_name}_{full_path}",
                    )
                    GlobalTaskManager.register_task(task)
                    return s3_uri
                except Exception as e:
                    logger.error(f"Error creating upload task for S3: {e}")
                    return None
        except Exception as e:
            logger.error(f"Error preparing file for upload to S3: {e}")
            return None

    async def save_csv_to_s3(
        self,
        root_folder: str,
        file_name: str,
        csv_data: Union[str, List[Dict[str, Any]]],
        bucket_name: Optional[str] = None,
        extra_path: Optional[str] = None,
        queue_for_batch: bool = True,
    ) -> Optional[str]:
        try:
            # Convert list of dictionaries to CSV if needed
            if isinstance(csv_data, list) and csv_data and isinstance(csv_data[0], dict):
                import csv
                from io import StringIO

                output = StringIO()
                if csv_data:
                    csv_writer = csv.writer(output)

                    # Write header row
                    header = csv_data[0].keys()
                    csv_writer.writerow(header)

                    # Write data rows
                    for row in csv_data:
                        csv_writer.writerow(row.values())

                    content_to_upload = output.getvalue()
                    output.close()
                else:
                    content_to_upload = ""
            else:
                content_to_upload = csv_data

            # Use the existing log_trip_artifact_to_s3 method with file_type="csv"
            s3_uri = await self.log_trip_artifact_to_s3(
                root_folder=root_folder,
                file_name=file_name,
                content=content_to_upload,
                bucket_name=bucket_name,
                file_type="csv",
                extra_path=extra_path,
                queue_for_batch=queue_for_batch,
            )

            logger.info(f"Successfully queued CSV file for upload to S3: {file_name}.csv")
            return s3_uri
        except Exception as e:
            logger.error(f"Error preparing CSV file for upload to S3: {e}")
            return None

    def _ensure_worker_running(self) -> None:
        """Ensure the background worker task is running."""
        if not self._is_worker_running:
            self._is_worker_running = True
            task = asyncio.create_task(self._batch_upload_worker(), name="s3_batch_upload_worker")
            GlobalTaskManager.register_task(task)
            self._upload_task = task

    async def _batch_upload_worker(self) -> None:
        """Background worker that processes the upload queue at intervals."""
        try:
            while True:
                files_to_upload = {}

                async with self._queue_lock:
                    buckets_to_process = list(self._upload_queue.keys())

                    if not buckets_to_process:
                        self._is_worker_running = False
                        return

                    for bucket in buckets_to_process:
                        batch = self._upload_queue[bucket][: self._batch_size]
                        files_to_upload[bucket] = batch

                        self._upload_queue[bucket] = self._upload_queue[bucket][len(batch) :]

                        if not self._upload_queue[bucket]:
                            del self._upload_queue[bucket]

                upload_tasks: List[Coroutine[Any, Any, Dict[str, Any]]] = []
                for bucket, files in files_to_upload.items():
                    for file_info in files:
                        upload_tasks.append(
                            self._upload_single_file(bucket, file_info["key"], file_info["body"], file_info["uri"])
                        )

                if upload_tasks:
                    results = await asyncio.gather(*upload_tasks, return_exceptions=True)
                    for result in results:
                        if isinstance(result, BaseException):
                            logger.error(f"Exception escaped try...catch while uploading file to S3: {str(result)}")
                        elif not result["success"]:
                            logger.error(
                                f"Failed to upload file to S3 ({result['uri']}): {result.get('error', 'Unknown error')}"
                            )

                await asyncio.sleep(self._batch_interval)
        except Exception as e:
            logger.error(f"Error in S3 batch upload worker: {e}")
            self._is_worker_running = False

    async def _upload_single_file(self, bucket: str, key: str, content: bytes, uri: str) -> Dict[str, Any]:
        """Upload a single file to S3 asynchronously."""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None, lambda: self.s3_client.put_object(Bucket=bucket, Key=key, Body=io.BytesIO(content))
            )
            logger.debug(f"Successfully uploaded file to {uri}")
            return {"success": True, "uri": uri, "content": content}
        except Exception as e:
            logger.error(f"Error uploading file to S3 ({uri}): {e}")
            return {"success": False, "uri": uri, "content": content, "error": str(e)}

    async def download_from_s3_uri(self, s3_uri: str) -> Optional[bytes]:
        """
        Download a file from S3 given the full s3://bucket/key path.
        Returns file content as bytes if successful, None otherwise.
        """
        try:
            if not s3_uri.startswith("s3://"):
                logger.error(f"Invalid S3 URI: {s3_uri}")
                return None
            path = s3_uri[5:]
            bucket, _, key = path.partition("/")
            if not bucket or not key:
                logger.error(f"Invalid S3 URI format: {s3_uri}")
                return None
            return await self.download_single_file(bucket, key)
        except Exception as e:
            logger.error(f"Error downloading from S3 URI ({s3_uri}): {e}")
            return None

    async def download_single_file(self, bucket_name: str, key: str) -> Optional[bytes]:
        """
        Asynchronously download a file from S3 and return its content as bytes.

        Args:
            bucket_name (str): The name of the S3 bucket.
            key (str): The S3 object key (path to the file).

        Returns:
            Optional[bytes]: The file content as bytes if successful, None otherwise.
        """
        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(None, lambda: self.s3_client.get_object(Bucket=bucket_name, Key=key))
            content = response["Body"].read()
            logger.debug(f"Successfully downloaded file from s3://{bucket_name}/{key}")
            return content
        except Exception as e:
            logger.error(f"Error downloading file from S3 (s3://{bucket_name}/{key}): {e}")
            return None

    async def stream_single_file(self, bucket_name: str, key: str) -> StreamingBody:
        """
        Asynchronously stream a file from S3.

        Args:
            bucket_name (str): The name of the S3 bucket.
            key (str): The S3 object key (path to the file).

        Returns:
            Optional[bytes]: The file content as bytes if successful, None otherwise.
        """
        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(None, lambda: self.s3_client.get_object(Bucket=bucket_name, Key=key))
            stream = response["Body"]  # StreamingBody

            logger.debug(f"Successfully got the stream file from s3://{bucket_name}/{key}")
            return stream
        except Exception as e:
            logger.error(f"Error got the stream file from S3 (s3://{bucket_name}/{key}): {e}")
            raise

    async def upload_single_file(self, bucket: str, key: str, content: bytes, uri: str):
        return await self._upload_single_file(bucket, key, content, uri)

    @staticmethod
    def construct_aws_url(bucket: str, key: str):
        return f"https://{bucket}.s3.amazonaws.com/{key}"


s3_utils = S3Utils()
