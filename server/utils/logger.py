"""
Otto Logging System

This module provides a centralized logging system for the Otto travel planning application.
It combines structured logging (via structlog) with BAML (Boundary ML) integration for
LLM function call tracing.

Key Features:
- Console output with optional file logging in local environment
- BAML integration for LLM function call logging with preview/full content separation
- Configurable via environment variables
- Automatic fallback mechanisms for robust deployment
- Context binding for structured logging across the application

Environment Variables:
- LOG_LEVEL: Set logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- LOG_MAX_FILE_SIZE: Maximum log file size in bytes (default: 100MB)
- LOG_BACKUP_COUNT: Number of backup log files to keep (default: 10)
- LOG_PREVIEW_LENGTH: Length of BAML response preview in console (default: 200)

Usage:
    from server.utils.logger import logger

    # Basic logging
    logger.info("User logged in", user_id=123)

    # With context binding
    logger.bind_log_context(user_id=123, trip_id=456)
    logger.info("Trip created")  # Will include bound context

    # BAML function logging (automatic after LLM calls)
    logger.log_baml()
"""

import json
import logging
import logging.handlers
import os
import sys
import traceback
from pathlib import Path
from typing import Any

import structlog
from baml_py import Collector

from baml_client.tracing import set_tags
from server.utils.settings import settings

# Configuration constants - These can be overridden via environment variables
LOG_FILE_NAME = "otto.log"  # Main log file name
LOG_DIR = "logs"  # Directory for log files (relative to current working directory)
DEFAULT_LOG_LEVEL = "INFO"  # Default log level if LOG_LEVEL env var not set

# Configurable logging parameters via environment variables
MAX_FILE_SIZE = int(os.getenv("LOG_MAX_FILE_SIZE", "104857600"))  # 100MB default
BACKUP_COUNT = int(os.getenv("LOG_BACKUP_COUNT", "10"))  # Number of backup files
PREVIEW_LENGTH = int(os.getenv("LOG_PREVIEW_LENGTH", "200"))  # BAML preview length

# BAML logging event names - Used to differentiate between preview and full logs
BAML_CALL_EVENT = "baml_call"  # Short preview for console output
BAML_CALL_FULL_EVENT = "baml_call_full"  # Full response for file output only

# Global configuration state - ensures logging is configured only once
_configured = False


class _SkipFullFilter(logging.Filter):
    """
    Filter to skip full BAML call logs from console output.

    This prevents verbose LLM response content from cluttering the console.
    Only the preview version (BAML_CALL_EVENT) is shown in console output.

    Used by: Console handler to keep terminal output clean
    """

    def filter(self, record: logging.LogRecord) -> bool:
        # Handle both structlog dict messages and plain string messages
        if isinstance(record.msg, dict):
            return record.msg.get("event", "") != BAML_CALL_FULL_EVENT
        return record.msg != BAML_CALL_FULL_EVENT


class _SkipPreviewFilter(logging.Filter):
    """
    Filter to skip BAML call preview logs from file output.

    This prevents duplicate BAML logging in files. Only the full version
    (BAML_CALL_FULL_EVENT) with complete metadata is written to files.

    Used by: File handler to avoid duplicate BAML entries
    """

    def filter(self, record: logging.LogRecord) -> bool:
        # Handle both structlog dict messages and plain string messages
        if isinstance(record.msg, dict):
            return record.msg.get("event", "") != BAML_CALL_EVENT
        return record.msg != BAML_CALL_EVENT


def _create_log_directory() -> Path:
    """
    Create log directory with fallback mechanism.

    Attempts to create the log directory in the current working directory.
    If that fails (e.g., permissions, read-only filesystem), falls back to
    the system temp directory to ensure logging continues to work.

    Returns:
        Path: The created log directory path

    Fallback behavior:
        Primary: {cwd}/logs/
        Fallback: {temp}/otto-logs/
    """
    # Primary location: current working directory + LOG_DIR
    log_dir = Path.cwd() / LOG_DIR
    try:
        log_dir.mkdir(parents=True, exist_ok=True)
        return log_dir
    except OSError as e:
        # Fallback to temp directory if current directory is not writable
        # This ensures logging works even in restricted environments
        import tempfile

        fallback_dir = Path(tempfile.gettempdir()) / "otto-logs"
        fallback_dir.mkdir(parents=True, exist_ok=True)
        print(f"Warning: Could not create log directory at {log_dir}, using {fallback_dir}. Error: {e}")
        return fallback_dir


def _get_log_level() -> int:
    """
    Get log level from LOG_LEVEL environment variable with validation.

    Validates the log level string and provides a safe fallback to INFO
    if an invalid level is specified.

    Returns:
        int: Python logging level constant (e.g., logging.INFO = 20)

    Environment Variable:
        LOG_LEVEL: One of DEBUG, INFO, WARNING, ERROR, CRITICAL (case-insensitive)
    """
    log_level_str = os.getenv("LOG_LEVEL", DEFAULT_LOG_LEVEL).upper()

    # Validate against Python's standard logging levels
    valid_levels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
    if log_level_str not in valid_levels:
        print(f"Warning: Invalid log level '{log_level_str}', using INFO")
        log_level_str = "INFO"

    # Convert string to logging constant (e.g., "INFO" -> 20)
    return getattr(logging, log_level_str)


def _setup_structlog_processors() -> list[Any]:
    """
    Configure shared structlog processors for structured logging.

    These processors are applied to all log messages and add consistent
    metadata that helps with debugging and monitoring.

    Returns:
        List of structlog processors that will be applied in order

    Processors added:
        - TimeStamper: Adds timestamp in YYYY-MM-DD HH:MM:SS format
        - add_log_level: Adds log level (DEBUG, INFO, etc.) to output
        - add_logger_name: Adds logger name for identification
        - CallsiteParameterAdder: Adds function name, line number, and filename of actual caller
          (ignores logger wrapper to show the real calling location)
    """
    return [
        # Add timestamp to all log messages
        structlog.processors.TimeStamper(fmt="%Y-%m-%d %H:%M:%S"),
        # Add log level information
        structlog.stdlib.add_log_level,
        # Add logger name for identification
        structlog.stdlib.add_logger_name,
        # Merge context variables (like trip_id) into log entries
        structlog.contextvars.merge_contextvars,
        # Add source code information for debugging
        structlog.processors.CallsiteParameterAdder(
            {
                structlog.processors.CallsiteParameter.FUNC_NAME,  # Function name
                structlog.processors.CallsiteParameter.LINENO,  # Line number
                structlog.processors.CallsiteParameter.FILENAME,  # File name
            },
            additional_ignores=["server.utils.logger"],  # Skip logger wrapper to show actual caller
        ),
    ]


def _setup_formatters() -> tuple[structlog.stdlib.ProcessorFormatter, structlog.stdlib.ProcessorFormatter]:
    """
    Setup console and file formatters with environment-appropriate rendering.

    Console formatter:
        - Local environment: Colored, human-readable format
        - Production environment: JSON format for log aggregation

    File formatter:
        - Always JSON format for structured parsing and analysis

    Returns:
        Tuple of (console_formatter, file_formatter)
    """
    # Console formatter: colored for local development, JSON for production
    console_formatter = structlog.stdlib.ProcessorFormatter(
        processor=structlog.dev.ConsoleRenderer(colors=True)  # Colored output for readability
        if settings.is_local
        else structlog.processors.JSONRenderer(),  # JSON for production log aggregation
    )

    # File formatter: always JSON for structured log parsing
    file_formatter = structlog.stdlib.ProcessorFormatter(
        processor=structlog.processors.JSONRenderer(),  # Structured format for analysis
    )

    return console_formatter, file_formatter


def _setup_handlers(log_dir: Path, console_formatter: Any, file_formatter: Any) -> list[logging.Handler]:
    """
    Setup logging handlers with proper error handling and filtering.

    Creates console handler always, and file handler only in local environment.
    This matches development branch behavior while providing local file logging.

    Args:
        log_dir: Directory where log files should be created (used in local env)
        console_formatter: Formatter for console output
        file_formatter: Formatter for file output (used in local env)

    Returns:
        List of configured logging handlers

    Behavior:
        - Local environment (is_local=True): Console + File logging
        - Production environment (is_local=False): Console only
    """
    handlers = []

    # Console handler (always active)
    # Filters out full BAML logs to keep console output clean
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(console_formatter)
    console_handler.addFilter(_SkipFullFilter())  # Skip verbose BAML full logs
    handlers.append(console_handler)

    # File handler only in local environment for development/debugging
    if settings.is_local:
        try:
            file_handler = logging.handlers.RotatingFileHandler(
                log_dir / LOG_FILE_NAME,
                maxBytes=MAX_FILE_SIZE,  # Rotate when file reaches this size
                backupCount=BACKUP_COUNT,  # Keep this many backup files
            )
            file_handler.setFormatter(file_formatter)
            file_handler.addFilter(_SkipPreviewFilter())  # Skip BAML preview logs
            handlers.append(file_handler)
        except OSError as e:
            # If file logging fails in local env, continue with console only
            print(f"Warning: Could not create file handler: {e}. Logging to console only.")

    return handlers


def _configure_loggers(handlers: list[logging.Handler], log_level: int) -> None:
    """
    Configure root and uvicorn loggers with the provided handlers.

    Sets up the Python logging system to use our custom handlers and formatting.
    This ensures all logging throughout the application uses our structured format.

    Args:
        handlers: List of logging handlers to attach to loggers
        log_level: Minimum log level to process

    Loggers configured:
        - Root logger: Catches all logging calls throughout the application
        - Uvicorn logger: Ensures web server logs use our formatting
    """
    # Configure root logger - this catches all logging calls in the application
    root_logger = logging.getLogger()
    root_logger.handlers.clear()  # Remove any existing handlers to avoid duplicates
    for handler in handlers:
        root_logger.addHandler(handler)
    root_logger.setLevel(log_level)

    # Configure uvicorn logger - ensures web server logs use our formatting
    uvicorn_logger = logging.getLogger("uvicorn.access")
    uvicorn_logger.handlers.clear()  # Remove default uvicorn handlers
    for handler in handlers:
        uvicorn_logger.addHandler(handler)


def _setup_logging() -> None:
    """
    Configure the entire logging system exactly once.

    This is the main entry point for logging configuration. It orchestrates
    the setup of all logging components and ensures the system is configured
    only once during the application lifetime.

    Configuration steps:
    1. Create log directory with fallback handling
    2. Validate and set log level from environment
    3. Configure structlog processors and settings
    4. Create formatters for console and file output
    5. Setup handlers with environment-based filtering
    6. Configure Python loggers to use our system

    Thread-safe: Uses global _configured flag to prevent multiple configuration
    """
    global _configured
    if _configured:
        return  # Already configured, skip to avoid duplicate setup
    _configured = True

    # Step 1: Setup directory and log level
    log_dir = _create_log_directory()  # Create with fallback handling
    log_level = _get_log_level()  # Validate environment variable

    # Step 2: Configure structlog with our processors
    shared_processors = _setup_structlog_processors()
    structlog.configure(
        processors=shared_processors + [structlog.stdlib.ProcessorFormatter.wrap_for_formatter],
        logger_factory=structlog.stdlib.LoggerFactory(),  # Use standard logging factory
        wrapper_class=structlog.stdlib.BoundLogger,  # Enable context binding
        cache_logger_on_first_use=True,  # Performance optimization
    )

    # Step 3: Setup formatters and handlers
    console_formatter, file_formatter = _setup_formatters()
    handlers = _setup_handlers(log_dir, console_formatter, file_formatter)

    # Step 4: Configure Python loggers to use our system
    _configure_loggers(handlers, log_level)


class Logger:
    """
    Main logger class that provides a simplified interface for application logging.

    Features:
    - Structured logging with context binding
    - BAML (Boundary ML) integration for LLM function call tracing
    - Automatic masking of sensitive information in local environment
    - Consistent formatting across console and file outputs

    Usage:
        logger = Logger()  # Or use the singleton instance
        logger.info("User action", user_id=123, action="login")
        logger.error("Operation failed", error_code=500)
    """

    def __init__(self) -> None:
        """
        Initialize the logger with BAML integration.

        Sets up:
        - Logging configuration (called once globally)
        - BAML collector for LLM function tracing
        - Structlog logger instance
        """
        _setup_logging()  # Ensure logging is configured
        self.collector = Collector("OTTO")  # BAML collector for LLM tracing
        self.logger = structlog.get_logger()  # Get configured structlog logger

    def _apply_mask(self, msg: str, mask: str | None) -> str:
        """
        Apply mask formatting to sensitive information in local environment.

        In local development, sensitive information can be masked using format strings.
        In production, messages are logged as-is for proper monitoring.

        Args:
            msg: The original message
            mask: Optional format string for masking (e.g., "User: {0}")

        Returns:
            Formatted message if mask provided and in local environment, otherwise original

        Example:
            logger.info("secret_key_123", mask="API Key: ***{0}***")
            # Local: logs "API Key: ***secret_key_123***"
            # Production: logs "secret_key_123"
        """
        if mask is not None and settings.is_local:
            return mask.format(msg)
        return msg

    def debug(self, msg: str, mask: str | None = None, **kwargs: Any) -> None:
        """
        Log debug message with optional masking.

        Args:
            msg: The message to log
            mask: Optional format string for sensitive information masking
            **kwargs: Additional structured data to include in log

        Example:
            logger.debug("Database query", query="SELECT * FROM users", duration=0.5)
        """
        formatted_msg = self._apply_mask(msg, mask)
        self.logger.debug(formatted_msg, **kwargs)

    def info(self, msg: str, mask: str | None = None, **kwargs: Any) -> None:
        """
        Log info message with optional masking.

        Args:
            msg: The message to log
            mask: Optional format string for sensitive information masking
            **kwargs: Additional structured data to include in log

        Example:
            logger.info("User logged in", user_id=123, ip_address="***********")
        """
        formatted_msg = self._apply_mask(msg, mask)
        self.logger.info(formatted_msg, **kwargs)

    def warn(self, msg: str, mask: str | None = None, **kwargs: Any) -> None:
        """
        Log warning message with optional masking. Alias for warning().

        Args:
            msg: The message to log
            mask: Optional format string for sensitive information masking
            **kwargs: Additional structured data to include in log
        """
        self.warning(msg, mask, **kwargs)

    def warning(self, msg: str, mask: str | None = None, **kwargs: Any) -> None:
        """
        Log warning message with optional masking.

        Args:
            msg: The message to log
            mask: Optional format string for sensitive information masking
            **kwargs: Additional structured data to include in log

        Example:
            logger.warning("Rate limit approaching", current_requests=95, limit=100)
        """
        formatted_msg = self._apply_mask(msg, mask)
        self.logger.warning(formatted_msg, **kwargs)

    def error(self, msg: str, mask: str | None = None, **kwargs: Any) -> None:
        """
        Log error message with optional masking and traceback in console mode.

        In console mode, automatically includes traceback information if an exception
        is currently being handled, which aids in debugging.

        Args:
            msg: The message to log
            mask: Optional format string for sensitive information masking
            **kwargs: Additional structured data to include in log

        Example:
            try:
                risky_operation()
            except Exception as e:
                logger.error("Operation failed", error=str(e), operation="risky_operation")
        """
        formatted_msg = self._apply_mask(msg, mask)

        # Add traceback info in console mode for better debugging
        if settings.is_local:
            # Check if we're currently handling an exception
            if sys.exc_info()[1] is not None:
                formatted_msg = f"{formatted_msg}\n{traceback.format_exc()}"

        self.logger.error(formatted_msg, **kwargs)

    def prepare_long_text(self, text: str) -> str:
        """
        Prepare long text for logging.

        Args:
            text: The text to prepare
        """
        if not settings.is_local:
            # Log long text if we are not in local environment
            return text

        if len(text) > PREVIEW_LENGTH:
            return text[:PREVIEW_LENGTH] + "..."
        return text

    def log_baml(self) -> None:
        """
        Log BAML (Boundary ML) function call with structured data.

        This method logs LLM function calls with dual output:
        1. Console: Short preview for readability
        2. File: Full response with metadata for analysis

        The collector automatically captures information about the last LLM call,
        including token usage, model information, and response content.

        Call this method after LLM function calls to capture performance metrics
        and response data for monitoring and debugging.

        Example:
            # After a BAML function call
            result = await some_llm_function(prompt)
            logger.log_baml()  # Logs the function call details
        """
        # Get the last collected BAML function call
        if not (baml_log := self.collector.last):
            return  # No BAML call to log

        usage = baml_log.usage
        raw_request = ""
        request_preview = ""

        # Base data structure shared between preview and full logs
        base_data = {
            "function": baml_log.function_name,  # BAML function name
            "tokens": {
                "input": usage.input_tokens or 0,  # Tokens in prompt
                "output": usage.output_tokens or 0,  # Tokens in response
                "total": (usage.input_tokens or 0) + (usage.output_tokens or 0),  # Total cost
            },
            "metadata": baml_log.metadata,
        }

        # Add model information if available
        if baml_call := baml_log.selected_call:
            base_data["model"] = baml_call.client_name  # e.g., "gpt-4", "claude-3"

            if request := baml_call.http_request:
                raw_request = request.body.json()
                # create preview for request
                request_preview = self.prepare_long_text(json.dumps(raw_request))

        # Create truncated preview for console output
        raw_response = str(baml_log.raw_llm_response)
        response_preview = self.prepare_long_text(raw_response)

        # Log concise version to console (and file, but file filters it out)
        self.logger.info(
            BAML_CALL_EVENT, **base_data, response_preview=response_preview, request_preview=request_preview
        )

        # Log full version to file only (console handler filters this out)
        self.logger.info(BAML_CALL_FULL_EVENT, **base_data, full_response=raw_response, full_request=raw_request)

    @staticmethod
    def bind_log_context(**kwargs: Any) -> None:
        """
        Bind context variables for structured logging across the application.

        Once bound, these variables will be automatically included in all subsequent
        log messages within the same context (e.g., request, thread).

        This is particularly useful for tracking user actions, trip context, or
        other request-scoped information throughout the application.

        Args:
            **kwargs: Key-value pairs to bind to the logging context

        Example:
            # Bind context at the start of a request
            logger.bind_log_context(user_id=123, trip_id=456, request_id="req_abc")

            # All subsequent logs will include this context
            logger.info("Flight searched")  # Will include user_id, trip_id, request_id
            logger.error("Booking failed")  # Will include user_id, trip_id, request_id
        """
        # Bind to structlog context variables
        structlog.contextvars.bind_contextvars(**kwargs)
        # Also bind to BAML tracing system
        set_tags(**kwargs)


# Singleton instance - Import and use this throughout the application
# from server.utils.logger import logger
logger = Logger()
