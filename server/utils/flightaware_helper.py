from typing import Any

from server.utils.flightaware_api import flightaware_api
from server.utils.logger import logger


async def get_flight_info_flightaware(flight_ident: str) -> dict[str, Any] | None:
    """Get flight information from FlightAware AeroAPI."""
    try:
        response = await flightaware_api.get_flight_info(flight_ident)
        return response
    except Exception as e:
        logger.error(f"Error getting FlightAware flight info for {flight_ident}: {e}")
        return None


def extract_flight_ident_from_booking(trip_details: dict[str, Any]) -> str | None:
    """Extract flight identifier from trip details for FlightAware lookup."""
    try:
        pnrs = trip_details.get("pnrs", [])
        if not pnrs:
            return None

        air_pnr = pnrs[0].get("data", {}).get("airPnr", {})
        segments = air_pnr.get("segments", [])
        if not segments:
            return None

        first_segment = segments[0]
        airline_code = first_segment.get("airlineCode", "")
        flight_number = first_segment.get("flightNumber", "")

        if airline_code and flight_number:
            return f"{airline_code}{flight_number}"
        return None
    except Exception as e:
        logger.error(f"Error extracting flight ident: {e}")
        return None


async def get_airport_weather_observations_flightaware(
    airport_id: str, params: dict[str, Any] | None = None
) -> dict[str, Any] | None:
    """Get airport weather observations from FlightAware AeroAPI."""
    try:
        response = await flightaware_api.get_airport_weather_observations(airport_id, params)
        return response
    except Exception as e:
        logger.error(f"Error getting FlightAware weather observations for airport {airport_id}: {e}")
        return None


async def get_airport_weather_forecast_flightaware(
    airport_id: str, params: dict[str, Any] | None = None
) -> dict[str, Any] | None:
    """Get airport weather forecast from FlightAware AeroAPI."""
    try:
        response = await flightaware_api.get_airport_weather_forecast(airport_id, params)
        return response
    except Exception as e:
        logger.error(f"Error getting FlightAware weather forecast for airport {airport_id}: {e}")
        return None


def extract_airport_codes_from_booking(trip_details: dict[str, Any]) -> dict[str, str] | None:
    """Extract departure and arrival airport codes from trip details for weather lookup."""
    try:
        pnrs = trip_details.get("pnrs", [])
        if not pnrs:
            return None

        air_pnr = pnrs[0].get("data", {}).get("airPnr", {})
        segments = air_pnr.get("segments", [])
        if not segments:
            return None

        first_segment = segments[0]
        departure_airport = first_segment.get("departureAirport", "")
        arrival_airport = first_segment.get("arrivalAirport", "")

        if departure_airport and arrival_airport:
            return {"departure": departure_airport, "arrival": arrival_airport}
        return None
    except Exception as e:
        logger.error(f"Error extracting airport codes: {e}")
        return None
