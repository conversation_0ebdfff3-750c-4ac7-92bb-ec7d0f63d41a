import json
import urllib.parse
from datetime import datetime, timezone
from enum import StrEnum
from typing import Any, Dict, List, Optional

from server.utils.mongo_connector import user_activities
from server.utils.settings import settings


class UserActivityType(StrEnum):
    SIGN_IN = "sign_in"
    FLIGHT_SEARCH = "flight_search"
    HOTEL_SEARCH = "hotel_search"
    CALENDAR_UPDATE = "calendar_update"
    FLIGHT_CANCEL_ATTEMPTED = "flight_cancelled"
    FLIGHT_CHANGE_ATTEMPTED = "flight_changed"
    HOTEL_CANCEL_ATTEMPTED = "hotel_cancelled"


async def update_user_activity(
    user_id: str, activity_type: UserActivityType, data: Optional[Dict[str, Any]] = None
) -> None:
    """
    Update user activity tracking document
    """
    now = datetime.now(timezone.utc)

    data = data or {}

    update_ops = {
        "$set": {
            "last_updated": now,
        },
        "$min": {},
        "$inc": {},
    }

    if activity_type == UserActivityType.SIGN_IN:
        update_ops["$set"]["last_sign_in"] = now

    elif activity_type == UserActivityType.FLIGHT_SEARCH:
        update_ops.update(
            {
                "$set": {
                    "last_flight_search": now,
                },
                "$min": {"first_flight_search": now},
            }
        )
        if "has_company_policy" in data:
            update_ops["$set"]["has_company_policy"] = data["has_company_policy"]

    elif activity_type == UserActivityType.HOTEL_SEARCH:
        update_ops.update(
            {
                "$set": {
                    "last_hotel_search": now,
                },
                "$min": {"first_hotel_search": now},
            }
        )
        if "has_company_policy" in data:
            update_ops["$set"]["has_company_policy"] = data["has_company_policy"]

    elif activity_type == UserActivityType.FLIGHT_CANCEL_ATTEMPTED:
        update_ops["$set"]["last_flight_cancel_attempted"] = now
        update_ops["$min"]["first_flight_cancel_attempted"] = now
        update_ops["$inc"]["flight_cancel_attempted_count"] = 1

    elif activity_type == UserActivityType.FLIGHT_CHANGE_ATTEMPTED:
        update_ops["$set"]["last_flight_change_attempted"] = now
        update_ops["$min"]["first_flight_change_attempted"] = now
        update_ops["$inc"]["flight_change_attempted_count"] = 1

    elif activity_type == UserActivityType.HOTEL_CANCEL_ATTEMPTED:
        update_ops["$set"]["last_hotel_cancel_attempted"] = now
        update_ops["$min"]["first_hotel_cancel_attempted"] = now
        update_ops["$inc"]["hotel_cancel_attempted_count"] = 1

    for key, value in data.items():
        if key not in ["has_company_policy"]:
            update_ops["$set"][f"metadata.{key}"] = value

    await user_activities.update_one({"user_id": user_id}, update_ops, upsert=True)


async def update_otto_ruid(user_id, otto_ruid):
    """
    Update user's otto_ruid if it doesn't exist or has changed

    Args:
        user_id: Unique identifier for the user
        otto_ruid: RUID tracking data containing referral information
    """

    decoded_str = urllib.parse.unquote(otto_ruid)
    otto_ruid = json.loads(decoded_str)

    existing_user = await user_activities.find_one({"user_id": user_id}, {"otto_ruid": 1})

    if not existing_user:
        await user_activities.update_one(
            {"user_id": user_id}, {"$set": {"otto_ruid": otto_ruid, "last_updated": datetime.utcnow()}}, upsert=True
        )
    else:
        existing_ruid = existing_user.get("otto_ruid", {})
        if existing_ruid.get("RUID") != otto_ruid.get("RUID"):
            await user_activities.update_one(
                {"user_id": user_id}, {"$set": {"otto_ruid": otto_ruid, "last_updated": datetime.utcnow()}}
            )


async def get_user_activity(user_id: str) -> Dict[str, Any]:
    doc = await user_activities.find_one({"user_id": user_id})
    if not doc:
        return {}

    if "last_suggested_capabilities" in doc:
        now = datetime.now(timezone.utc)
        expiration_cutoff = now - settings.CAPABILITY_SUGGESTION_EXPIRATION_PERIOD

        filtered_capabilities = []
        for item in doc.get("last_suggested_capabilities", []):
            if isinstance(item, str):
                pass
            elif isinstance(item, dict) and "category" in item and "timestamp" in item:
                suggestion_time = datetime.fromtimestamp(item["timestamp"], timezone.utc)
                if suggestion_time >= expiration_cutoff:
                    filtered_capabilities.append(item["category"])

        doc["last_suggested_capabilities"] = filtered_capabilities

    doc["flight_cancel_attempted_count"] = doc.get("flight_cancel_attempted_count") or 0
    doc["flight_change_attempted_count"] = doc.get("flight_change_attempted_count") or 0
    doc["hotel_cancel_attempted_count"] = doc.get("hotel_cancel_attempted_count") or 0

    return doc


async def get_users_activities(user_ids: List[str]) -> Dict[str, Dict[str, Any]]:
    """
    Fetch activities for multiple users efficiently.

    Args:
        user_ids: List of user IDs to fetch activities for
    """

    cursor = user_activities.find({"user_id": {"$in": user_ids}})
    activities = await cursor.to_list(length=None)
    return {act["user_id"]: act for act in activities}


async def update_suggested_capability(user_id: str, suggested_capability: str):
    """
    Update user's suggested capability tracking

    Args:
        user_id: Unique identifier for the user
        suggested_capability: The capability that was suggested
    """
    now = datetime.now(timezone.utc)

    update_ops = {
        "$set": {
            "last_timestamp_of_suggested_capability": now.timestamp(),
        },
        "$push": {
            "last_suggested_capabilities": {
                "$each": [{"category": suggested_capability, "timestamp": now.timestamp()}],
                "$slice": -5,  # Keep only the last 5 items
            }
        },
    }

    await user_activities.update_one({"user_id": user_id}, update_ops, upsert=True)


async def get_suggested_capabilities(user_id: str) -> Dict[str, Any]:
    """
    Get user's suggested capability tracking data

    Args:
        user_id: Unique identifier for the user

    Returns:
        Dictionary containing suggested capability tracking data
    """
    doc = await user_activities.find_one({"user_id": user_id})
    return doc.get("last_suggested_capabilities") if doc else {}
