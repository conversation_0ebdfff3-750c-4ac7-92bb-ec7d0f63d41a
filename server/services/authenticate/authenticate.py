from abc import ABC, abstractmethod
from typing import Any

from server.database.models.user import User, UserRole
from server.dependencies import admin_manager, manager, manager_request_with_header_tokens, organization_admin_manager
from server.utils.custom_login_manager import PermissionDeniedException
from server.utils.logger import logger


class AuthenticationProvider(ABC):
    @abstractmethod
    async def authorize(self) -> tuple[tuple[str, str], int, str]:
        pass


@manager.user_loader()
async def validate_user(user_id: Any) -> User | None:
    """
    Validates a user
    It returns the user from database if it exists, else None
    """

    verified_user = await User.from_id(int(user_id))
    return verified_user


@admin_manager.user_loader()
async def validate_admin_user(user_id: Any) -> Any | None:
    """
    Validates a user
    It returns the user from database if it exists, else None
    """

    verified_user = await User.from_id(int(user_id))
    if verified_user is not None and verified_user.role != UserRole.admin:
        logger.warning(
            "admin_validation_failed",
            user_id=user_id,
            user_role=verified_user.role,
            reason="insufficient_permissions",
        )
        raise PermissionDeniedException

    return verified_user


@organization_admin_manager.user_loader()
async def validate_company_admin_user(user_id: Any) -> Any | None:
    """
    Validates a user
    It returns the user from database if it exists, else None
    """

    verified_user = await User.from_id(int(user_id))
    if verified_user is not None and verified_user.role != UserRole.company_admin:
        logger.warning(
            "company_admin_validation_failed",
            user_id=user_id,
            user_role=verified_user.role,
            reason="insufficient_permissions",
        )
        raise PermissionDeniedException

    return verified_user


@manager_request_with_header_tokens.user_loader()
async def validate_user_request_with_header_tokens(user_id: Any) -> User | None:
    """
    Validates a user
    It returns the user from database if it exists, else None
    """

    verified_user = await User.from_id(int(user_id))
    return verified_user
