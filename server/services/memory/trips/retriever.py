from typing import Any, Dict, List

from langchain_core.messages import BaseMessage

from server.services.memory.memory_store import MemoryIsolationKeys
from server.services.memory.trips.processor import TripMemoryProcessor
from server.services.memory.utils import AccommodationBooking<PERSON><PERSON><PERSON><PERSON>ormatter, FlightBookingMemoryFormatter
from server.services.trips.bookings import (
    get_accommodation_booking,
    get_bookings_from_airline_confirmation_code_with_pnr_id,
)
from server.utils.logger import logger
from virtual_travel_agent.timings import Timings


class TripMemoryRetriever:
    def __init__(self, user_id: str, thread_id: str):
        self.memory_processor = TripMemoryProcessor.get_instance()
        self.user_id = user_id
        self.thread_id = thread_id
        self.score_threshold = 0.55
        self.max_memory_length = 3

    async def search(self, messages: List[BaseMessage]) -> List[str]:
        """
        Search relevant memories from memory processor based on the messages

        Args:
            messages: List of message contents to use as query

        Returns:
            List of relevant memory contexts
        """
        t = Timings("TripMemoryRetriever: search")
        try:
            last_two_messages = messages[-2:]
            query = "\n".join([str(message.content) for message in last_two_messages])
            memories = await self.memory_processor.search_trip_memories(
                user_id=self.user_id,
                thread_id=self.thread_id,
                query=query,
                relevance_score_threshold=self.score_threshold,
                limit=self.max_memory_length,
            )

            results = []

            for m in memories:
                if m:
                    results.append(f"\n'{m.model_dump_json()}'\n")

            t.print_timing("green")
            return results

        except Exception as e:
            logger.error(f"Error getting context from memory processor: {e}")
            t.print_timing("red")
            return []

    async def _process_memory_with_metadata(self, memory: str, metadata: Dict[str, Any]) -> str:
        """
        Process memory based on metadata

        Args:
            memory: Raw memory string
            metadata: Associated metadata

        Returns:
            Processed memory string
        """
        if MemoryIsolationKeys.FLIGHT_BOOKING.name in metadata.get("memory_isolation_key", ""):
            return await self._construct_flight_booking_memory(memory, metadata)
        elif MemoryIsolationKeys.ACCOMMODATION_BOOKING.name in metadata.get("memory_isolation_key", ""):
            return await self._construct_accommodation_booking_memory(memory, metadata)
        return memory

    async def _construct_flight_booking_memory(self, memory: str, metadata: Dict[str, Any]) -> str:
        """
        Construct flight booking memory string

        Args:
            memory: Raw memory string
            metadata: Associated metadata

        Returns:
            Formatted flight booking memory string
        """
        confirmation_id = metadata.get("confirmation_id")
        pnr_id = metadata.get("airline_confirmation_number")
        if not confirmation_id or not pnr_id:
            return memory

        booking = await get_bookings_from_airline_confirmation_code_with_pnr_id(pnr_id, confirmation_id)

        flight_data = booking.get("flight")
        flight_data_str = ""
        if flight_data:
            flight_data_str = FlightBookingMemoryFormatter.format(flight_data)

        return f"{memory}\n{flight_data_str}\n"

    async def _construct_accommodation_booking_memory(self, memory: str, metadata: Dict[str, Any]) -> str:
        """
        Construct accommodation booking memory string

        Args:
            memory: Raw memory string
            metadata: Associated metadata

        Returns:
            Formatted accommodation booking memory string
        """
        accommodation_order_number = metadata.get("order_number")
        if not accommodation_order_number:
            return memory

        booking = await get_accommodation_booking(accommodation_order_number)
        accommodation_data_str = ""
        if booking:
            accommodation_data_str = AccommodationBookingMemoryFormatter.format(booking)

        return f"{memory}\n{accommodation_data_str}\n"
