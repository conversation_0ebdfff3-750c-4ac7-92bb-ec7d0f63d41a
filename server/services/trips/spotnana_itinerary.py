from typing import Any, Dict

from server.services.trips.flight_card import construct_flight_itinerary_dict
from server.services.trips.models import FlightItinerary, FlightLeg, FlightStop
from server.utils.spotnana_api import spotnana_api


def get_spotnana_flight_itinerary(trip_details: Dict[str, Any]) -> FlightItinerary:
    flights = trip_details.get("pnrs", [{}])[0].get("data", {}).get("airPnr", {})
    airport_code_to_name_map = {
        info["airportCode"]: info["airportName"]
        for info in trip_details.get("pnrs", [{}])[0]
        .get("data", {})
        .get("additionalMetadata", {})
        .get("airportInfo", [])
        if "airportCode" in info and "airportName" in info
    }
    airline_code_to_name_map = {
        info["airlineCode"]: info["airlineName"]
        for info in trip_details.get("pnrs", [{}])[0]
        .get("data", {})
        .get("additionalMetadata", {})
        .get("airlineInfo", [])
        if "airlineCode" in info and "airlineName" in info
    }

    travelers_info = flights.get("travelerInfos", [])
    seats = []
    if travelers_info:
        seats = travelers_info[0].get("booking", {}).get("seats", [])

    flight_legs = []
    total_seat_price = 0
    for leg_idx, leg in enumerate(flights.get("legs", [])):
        totalFare = trip_details.get("tripPaymentInfo", {}).get("totalFareAmount", {})

        # Extract all flight stops in the leg
        flight_stops: list[FlightStop] = []
        for flight_idx, flight in enumerate(leg.get("flights", [])):
            seat_info = next(
                (seat for seat in seats if seat.get("legIdx") == leg_idx and seat.get("flightIdx") == flight_idx), {}
            )

            if seat_info and seat_info.get("amount") and seat_info["amount"].get("amount"):
                total_seat_price += seat_info["amount"]["amount"]

            flight_stop = FlightStop(
                origin=flight["origin"],
                origin_name=airport_code_to_name_map.get(flight["origin"], ""),
                destination=flight["destination"],
                destination_name=airport_code_to_name_map.get(flight["destination"], ""),
                departure_time=flight["departureDateTime"]["iso8601"],
                arrival_time=flight["arrivalDateTime"]["iso8601"],
                airline_code=flight["marketing"]["airlineCode"],
                airline_name=airline_code_to_name_map.get(flight["marketing"]["airlineCode"], ""),
                flight_number=flight["marketing"]["num"],
                duration=flight.get("duration", {}).get("iso8601", ""),
                booking_code=flight.get("bookingCode"),
                cabin=flight.get("cabin", ""),
                aircraft_iata_code=flight.get("equipment", {}).get("code"),
                aircraft_name=flight.get("equipment", {}).get("name"),
                seat_number=seat_info.get("number"),
                vendor_confirmation_number=flight.get("vendorConfirmationNumber"),
                operating_airline_code=flight.get("operating", {}).get("airlineCode"),
                operating_flight_number=flight.get("operating", {}).get("num"),
            )
            flight_stops.append(flight_stop)

        if not flight_stops:
            continue

        first_flight = flight_stops[0]
        last_flight = flight_stops[-1]

        flight_leg = FlightLeg(
            stops=flight_stops,
            origin=first_flight.origin,
            origin_name=first_flight.origin_name,
            destination=last_flight.destination,
            destination_name=last_flight.destination_name,
            departure_time=first_flight.departure_time,
            arrival_time=last_flight.arrival_time,
            airline_code=first_flight.airline_code,
            airline_name=first_flight.airline_name,
            flight_number=first_flight.flight_number,
            booking_code=first_flight.booking_code,
            duration=first_flight.duration,
            cabin=first_flight.cabin
            if first_flight.cabin == last_flight.cabin
            else f"{first_flight.cabin} / {last_flight.cabin}",
            price=round(
                round(totalFare.get("base", {}).get("amount", 0), 2)
                + round(totalFare.get("tax", {}).get("amount", 0), 2)
                + round(total_seat_price, 2),
                2,
            ),
            currency=totalFare.get("base", {}).get("currencyCode", ""),
            base_price=round(totalFare.get("base", {}).get("amount", 0), 2),
            base_currency=totalFare.get("base", {}).get("currencyCode", ""),
            tax_price=round(totalFare.get("tax", {}).get("amount", 0), 2),
            tax_currency=totalFare.get("tax", {}).get("currencyCode", ""),
            total_seat_price=round(total_seat_price, 2) if total_seat_price != 0 else None,
            fare_option_name=leg.get("brandName"),
            cancelled=(leg.get("legStatus") or "UNKNOWN") == "CANCELLED_STATUS",
        )
        flight_legs.append(flight_leg)

    return FlightItinerary(legs=flight_legs)


async def get_spotnana_itinerary(trip_id: str, confirmation_id: str):
    trip_details = await spotnana_api.get_trip_details(trip_id)
    flight_itinerary = get_spotnana_flight_itinerary(trip_details)

    # Convert FlightItinerary to the format expected by construct_flight_itinerary_dict
    flight_options = flight_itinerary.to_flight_option_dict_list()

    return construct_flight_itinerary_dict(flight_options, confirmation_id)
