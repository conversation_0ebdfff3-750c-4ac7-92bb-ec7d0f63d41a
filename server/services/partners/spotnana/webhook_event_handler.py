import asyncio
from datetime import datetime
from typing import Any, Callable, Coroutine

import aiohttp

from in_trip.in_trip import InTripAgent
from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.database.models.user import User
from server.schemas.authenticate.user import User as UserSchema
from server.schemas.partners.spotnana.travel_delivery import SpotnanaTravelDelivery
from server.schemas.receipt.receipt_flight_data import FlightLeg, FlightPayment, FlightTraveler, ReceiptFlightData
from server.schemas.spotnana.flight_statuses import FlightStatuses
from server.services.feature_flags.feature_flag import FeatureFlags, is_feature_flag_enabled
from server.services.partners.spotnana.webhook_event_handler_cancel_flight import handle_webhook_ticket_refunded
from server.services.partners.spotnana.webhook_event_handler_exchange_flight import (
    handle_webhook_booking_other_update,
)
from server.services.trips.flight_card import construct_flight_itinerary_dict
from server.services.trips.spotnana_itinerary import get_spotnana_flight_itinerary
from server.utils.booking_utils import extract_booking_dates_and_status
from server.utils.logger import logger
from server.utils.receipt.receipt_generator import ReceiptGenerator
from server.utils.s3_utils import s3_utils
from server.utils.settings import airports_info, settings


async def handle_spotnana_webhook_event(
    data: SpotnanaTravelDelivery, route_to_staging: Callable[[SpotnanaTravelDelivery], Coroutine[Any, Any, None]]
):
    payload = data.payload
    trip_id: str | None = payload.get("tripId", None)

    if trip_id is None:
        logger.error(f"[SPOTNANA WEBHOOK] Trip id not found in payload {data.model_dump()}")

    existing_trip: Booking | None = await Booking.from_query({"type": "flight", "content.trip_id": trip_id})
    if existing_trip is None:
        if not settings.is_live:
            logger.error(f"[SPOTNANA WEBHOOK] Trip id {trip_id} not found in database, might be an issue.")
        else:
            logger.warning(
                f"[SPOTNANA WEBHOOK] Trip id {trip_id} not found in database, might be in staging, redicting to staging"
            )
            asyncio.create_task(route_to_staging(data))
    else:
        match data.event_type:
            case "PNR_V3":
                match data.operation:
                    case "TICKET_REFUNDED" | "FLIGHT_CANCELLED" | "TICKET_VOIDED":
                        await handle_webhook_ticket_refunded(data.payload)

                    case "BOOKING_OTHER_UPDATE":
                        # Check if this is an exchange by looking at the first (latest) ticket
                        traveler_infos = data.payload.get("airPnr", {}).get("travelerInfos", [])
                        ticket = traveler_infos[0].get("tickets", [{}])[0] if traveler_infos else {}

                        if ticket.get("status") == "ISSUED" and "exchangeInfo" in ticket:
                            # Flight exchanged, update the data in mongo and google calendar
                            await handle_webhook_booking_other_update(data.payload)
                            logger.info(
                                f"[SPOTNANA WEBHOOK] Flight exchanged, trip id {data.payload.get('tripId', None)}"
                            )
                        else:
                            # Handle other booking updates (non-exchange)
                            logger.info(
                                f"[SPOTNANA WEBHOOK] Non-exchange booking update, trip id {data.payload.get('tripId', None)}"
                            )

                    case "BOOKING_TICKETED" | "SERVICE_FEE":
                        if data.payload.get("bookingHistory", [{}])[0].get("bookingInfo", {}).get("status") == "BOOKED":
                            # Booking confirmed
                            await handle_webhook_booking_confirmed(data.payload)
                            logger.info(
                                f"[SPOTNANA WEBHOOK] Booking confirmed, trip id {data.payload.get('tripId', None)}"
                            )

                    case "INVOICE_GENERATED":
                        await handle_webhook_invoice_generated(payload)

        if data.event_type == "PNR_V3" and data.operation in [
            "BOOKING_CANCELED_BY_VENDOR",
            # "FLIGHT_SCHEDULE_CHANGE_PENDING", # This indicates the flight schedule change has not been confirmed, we should not notify the user with pending updates
            "FLIGHT_SCHEDULE_CHANGE_CLOSED",
            "TICKET_VOIDED",
        ]:
            await handle_webhook_flight_status_change(data)


async def handle_webhook_flight_status_change(data: SpotnanaTravelDelivery):
    payload = data.payload
    trip_id: str = payload.get("tripId", None)

    booking: Booking | None = await Booking.from_query({"type": "flight", "content.trip_id": trip_id})
    if booking is None:
        logger.error(f"[SPOTNANA WEBHOOK] Booking not found for trip id {trip_id}")
        return

    thread_info: ChatThread | None = await ChatThread.from_id(booking.thread_id)
    if thread_info is None:
        logger.error(f"[SPOTNANA WEBHOOK] Thread not found for trip id {trip_id}")
        return

    user = await User.from_id(thread_info.users_id)
    if user is None:
        logger.error(f"[SPOTNANA WEBHOOK] User not found for trip id {trip_id}")
        return

    in_trip_enabled = await is_feature_flag_enabled(user.id, FeatureFlags.ENABLE_IN_TRIP)
    is_in_trip, in_trip_message = await InTripAgent.get_in_trip_status_and_message(booking)

    if not in_trip_enabled or not is_in_trip:
        logger.info(
            f"[SPOTNANA WEBHOOK] In trip not enabled for user {user.id} or trip {booking.thread_id} is not in trip, skipping."
        )
        return

    user = UserSchema.from_orm_user(user)
    in_trip_agent = InTripAgent(user, thread_info)

    if in_trip_message:
        from langchain_core.messages import AIMessage

        msg = AIMessage(content=in_trip_message)
        msg.additional_kwargs = {"agent_classification": "InTrip", "is_in_trip_opening": True, "message_type": "prompt"}
        await in_trip_agent.persist_messages([msg])

    await in_trip_agent.handle_flight_changes_for_in_trip(data, booking, thread_info, user)


async def handle_webhook_booking_confirmed(payload):
    trip_id: str = payload.get("tripId", None)

    booking: Booking | None = await Booking.from_query({"type": "flight", "content.trip_id": trip_id})
    if booking is None:
        return

    # Check if the booking is already confirmed
    if booking.content.get("status") != FlightStatuses.BOOKED.value:
        flat_trip_details = get_spotnana_flight_itinerary(
            {"pnrs": [{"data": payload}], "tripPaymentInfo": {"totalFareAmount": payload.get("totalFareAmount", {})}}
        )
        updated_content = {
            **booking.content,
            **construct_flight_itinerary_dict(
                flat_trip_details.to_flight_option_dict_list(), booking.content.get("confirmation_id")
            ),
            "status": FlightStatuses.BOOKED.value,
        }
        start_date, end_date, status = extract_booking_dates_and_status("flight", updated_content)
        await Booking.update_fields(
            {"id": booking.id},
            {
                "content": updated_content,
                "start_date": start_date,
                "end_date": end_date,
                "status": status.name if status else None,
            },
        )
        logger.info(f"Trip {trip_id} flight confirmed!")


async def handle_webhook_invoice_generated(payload):
    trip_id: str = payload.get("tripId", None)

    booking: Booking | None = await Booking.from_query({"type": "flight", "content.trip_id": trip_id})
    if booking is None:
        return

    invoices: list[str] = booking.content.get("invoices", [])
    receipts: list[str] = booking.content.get("receipts", [])

    for document in payload.get("documents", [{}]):
        document_metadata = document.get("documentMetadata", {})
        filename_no_ext = document_metadata.get("name", "invoice").removesuffix(".pdf")

        # Invoice
        s3_key = f"{booking.thread_id}/{filename_no_ext}.pdf"
        aws_url: str = s3_utils.construct_aws_url(settings.INVOICE_S3_BUCKET, s3_key)

        # Receipt
        traveler_last_name: str = payload.get("travelers", [{}])[0].get("user", {}).get("name", {}).get("family1", "")
        confirmation_number: str = booking.content.get("airline_confirmation_number", "")
        receipt_s3_key = f"{booking.thread_id}/{traveler_last_name}_{confirmation_number}.pdf"
        receipt_aws_url: str = s3_utils.construct_aws_url(settings.INVOICE_S3_BUCKET, receipt_s3_key)

        # Do not re-upload
        if aws_url in invoices:
            continue

        if document.get("url", None) and document_metadata.get("documentType", None) == "INVOICE":
            document_url = document.get("url", "")
            logger.info(f"Receive invoice file for trip {trip_id} at {document_url}")

            # Invoice
            await upload_invoice_to_s3(document_url=document_url, trip_id=trip_id, s3_key=s3_key, aws_uri=aws_url)
            invoices.append(aws_url)

            # Receipt
            await upload_receipt_to_s3(booking=booking, payload=payload, s3_key=receipt_s3_key, aws_uri=receipt_aws_url)
            receipts.append(receipt_s3_key)

    # Update if we have new invoices
    if len(invoices) > len(booking.content.get("invoices", [])):
        await Booking.update_fields({"id": booking.id}, {"content.invoices": invoices, "content.receipts": receipts})


async def upload_invoice_to_s3(document_url: str, trip_id: str, s3_key: str, aws_uri: str):
    if not document_url:
        logger.error(f"Empty document_url {document_url} for the invoice of trip {trip_id}")
        return

    async with aiohttp.ClientSession() as session:
        async with session.get(document_url) as response:
            if response.status == 200:
                pdf_bytes = await response.read()

                bucket = settings.INVOICE_S3_BUCKET
                await s3_utils.upload_single_file(bucket, s3_key, pdf_bytes, aws_uri)
                logger.info(f"Uploaded invoice PDF to s3://{bucket}/{s3_key}")
            else:
                logger.error(f"Failed to download invoice PDF from {document_url}: {response.status} {response.reason}")


async def upload_receipt_to_s3(booking: Booking, payload: dict[str, Any], s3_key: str, aws_uri: str):
    flight_legs: list[FlightLeg] = []
    for leg in booking.content.get("legs", []):
        origin_code: str = leg.get("flight_segments", [{}])[0].get("flight_stops", [{}])[0].get("origin_code", "")
        origin_city: str = airports_info.get(origin_code, {}).get("city", "")

        destination_code: str = (
            leg.get("flight_segments", [{}])[0].get("flight_stops", [{}])[0].get("destination_code", "")
        )
        destination_city: str = airports_info.get(destination_code, {}).get("city", "")

        airline_name: str = (
            leg.get("flight_segments", [{}])[0].get("flight_stops", [{}])[0].get("operating_airline_code", "")
        )
        flight_number: str = (
            leg.get("flight_segments", [{}])[0].get("flight_stops", [{}])[0].get("operating_flight_number", "")
        )

        flight_legs.append(
            FlightLeg(
                airline_name=leg.get("flight_segments", [{}])[0].get("flight_stops", [{}])[0].get("airline_name", ""),
                arrival_time=datetime.fromisoformat(
                    leg.get("flight_segments", [{}])[0].get("flight_stops", [{}])[0].get("arrival", "")
                ).strftime("%b %d, %Y - %I:%M%p"),
                cabin=leg.get("cabin", ""),
                confirmation_number=booking.content.get("airline_confirmation_number", ""),
                departure_time=datetime.fromisoformat(
                    leg.get("flight_segments", [{}])[0].get("flight_stops", [{}])[0].get("departure", "")
                ).strftime("%b %d, %Y - %I:%M%p"),
                destination=f"{destination_city} ({destination_code})",
                origin=f"{origin_city} ({origin_code})",
                flight_number=f"{airline_name}{flight_number}",
            )
        )

    flight_data: ReceiptFlightData = ReceiptFlightData(
        title=s3_key.split("/")[-1],
        pnr=booking.content.get("airline_confirmation_number", ""),
        flight_type="One Way Flight" if len(booking.content.get("legs", [])) == 1 else "Round Trip Flight",
        legs=flight_legs,
        traveler=FlightTraveler(
            name=f"{payload.get('travelers', [{}])[0].get('user', {}).get('name', {}).get('given')} {payload.get('travelers', [{}])[0].get('user', {}).get('name', {}).get('family1')}",
            ticket_number=payload.get("airPnr", {})
            .get("travelerInfos", [{}])[0]
            .get("tickets", [{}])[0]
            .get("ticketNumber", ""),
        ),
        payment=FlightPayment(
            base_fare=f"${booking.content.get('price_summary', {}).get('base', {}).get('amount', ''):.2f} {booking.content.get('price_summary', {}).get('base', {}).get('currency', '')}",
            method=f"{payload.get('paymentInfo', [{}])[0].get('fop', {}).get('card', {}).get('company', '').capitalize()} **** {payload.get('paymentInfo', [{}])[0].get('fop', {}).get('card', {}).get('number', '')[-4:]}",
            taxes_fees=f"${booking.content.get('price_summary', {}).get('tax', {}).get('amount', ''):.2f} {booking.content.get('price_summary', {}).get('tax', {}).get('currency', '')}",
            total=f"${booking.content.get('price_summary', {}).get('total', {}).get('amount', ''):.2f} {booking.content.get('price_summary', {}).get('total', {}).get('currency', '')}",
            transaction_date=datetime.fromisoformat(
                payload.get("transactions", [{}])[0]
                .get("itemGroups", [{}])[0]
                .get("transactionDateTime", {})
                .get("iso8601", "")
            ).strftime("%b %d, %Y"),
        ),
    )
    pdf_bytes: bytes | None = ReceiptGenerator().generate_flight_receipt(flight_data)

    if pdf_bytes is not None:
        bucket = settings.INVOICE_S3_BUCKET
        await s3_utils.upload_single_file(bucket, s3_key, pdf_bytes, aws_uri)
        logger.info(f"Uploaded receipt PDF to s3://{bucket}/{s3_key}")
    else:
        logger.error(f"Failed to generate receipt PDF trip id {booking.thread_id}")
