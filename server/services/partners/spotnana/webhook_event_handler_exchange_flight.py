from typing import Any

from flight_agent.flight_booking_util import FlightBookingUtil
from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.database.models.user_profile import UserProfile
from server.services.calendar_api.calendar_provider import CalendarProviderManager
from server.services.memory.trips.memory_modules.bookings_memory import BookingsMemory
from server.services.memory.utils import BookingOperation
from server.services.trips.flight_card import construct_flight_itinerary_dict
from server.services.trips.spotnana_itinerary import get_spotnana_flight_itinerary
from server.utils.booking_utils import extract_booking_dates_and_status
from server.utils.logger import logger


async def handle_webhook_booking_other_update(payload):
    trip_id: str = payload.get("tripId", None)

    booking: Booking | None = await Booking.from_query({"type": "flight", "content.trip_id": trip_id})
    if booking is None:
        return

    # Update the content with the event payload
    flat_trip_details = get_spotnana_flight_itinerary(
        {"pnrs": [{"data": payload}], "tripPaymentInfo": {"totalFareAmount": payload.get("totalFareAmount", {})}}
    )
    updated_content = {
        **booking.content,
        **construct_flight_itinerary_dict(
            flat_trip_details.to_flight_option_dict_list(), booking.content.get("confirmation_id")
        ),
    }
    start_date, end_date, status = extract_booking_dates_and_status("flight", updated_content)
    print("start_date", start_date)
    print("end_date", end_date)
    print("status", updated_content)
    # await Booking.update_fields(
    #    {"id": booking.id},
    #    {
    #        "flight_exchanges": [booking.content],
    #        "content": updated_content,
    #        "start_date": start_date,
    #        "end_date": end_date,
    #        "status": status.name if status else None,
    #    },
    # )

    # Update the google calendar events
    thread_id: int = booking.thread_id
    thread_info: ChatThread | None = await ChatThread.from_id(thread_id)
    if thread_info is not None:
        user_profile = await UserProfile.from_user_id(thread_info.users_id)
        if user_profile:
            google_calendar_api = CalendarProviderManager(user_profile=user_profile)

            if booking.content.get("legs", None):
                print("update_calendar_legs_event")
                # update_calendar_legs_event(booking, updated_content, thread_info, google_calendar_api)
            else:
                # update_calendar_outbound_event(booking, updated_content, thread_info, google_calendar_api)
                if booking.content.get("return", None):
                    update_calendar_return_event(booking, updated_content, thread_info, google_calendar_api)
            # await handle_flight_exchange_memory(str(user_profile.users_id), thread_id, updated_content)


async def handle_flight_exchange_memory(user_id: str, thread_id, updated_booking_content: dict[str, Any]):
    try:
        confirmation_id = updated_booking_content.get("confirmation_id")
        airline_confirmation_number = updated_booking_content.get("airline_confirmation_number")
        if confirmation_id and airline_confirmation_number:
            bookings_memory = BookingsMemory(user_id=user_id, thread_id=thread_id)
            await bookings_memory.store_flight_booking_memory(
                confirmation_id=confirmation_id,
                airline_confirmation_number=airline_confirmation_number,
                flight_booking_details=updated_booking_content,
                operation=BookingOperation.EXCHANGE,
            )
    except Exception as e:
        logger.error(f"Error storing flight exchange memory: {e}")


def _get_flight_info(booking: Booking, flight_key: str):
    content = booking.content

    if "legs" in content and flight_key == "legs":
        legs = content.get("legs", [])
        if legs:
            last_segment = legs[-1].get("flight_segments", [])
            first_segment = legs[0].get("flight_segments", [])

            airline_code = None
            if last_segment:
                last_stops = last_segment[-1].get("flight_stops", [])
                airline_code = last_stops[-1].get("airline_code") if last_stops else None

            flight_number = None
            if first_segment:
                first_stops = first_segment[0].get("flight_stops", [])
                flight_number = first_stops[0].get("flight_number") if first_stops else None

            return airline_code, flight_number

    if "legs" not in content:
        if flight_key == "outbound" and "outbound" in content:
            outbound_flight_segments = content.get("outbound", {}).get("flight_segments") or []
            if outbound_flight_segments:
                last_segment_stops = outbound_flight_segments[-1].get("flight_stops") or []
                airline_code = last_segment_stops[-1].get("airline_code") if last_segment_stops else None

                first_segment_stops = outbound_flight_segments[0].get("flight_stops") or []
                flight_number = first_segment_stops[0].get("flight_number") if first_segment_stops else None

                return airline_code, flight_number

        elif flight_key == "return" and "return" in content:
            return_flight_segments = content.get("return", {}).get("flight_segments") or []
            if return_flight_segments:
                first_segment_stops = return_flight_segments[0].get("flight_stops") or []
                airline_code = first_segment_stops[0].get("airline_code") if first_segment_stops else None
                flight_number = first_segment_stops[0].get("flight_number") if first_segment_stops else None

                return airline_code, flight_number

    return None, None


def _update_calendar_event(
    booking: Booking,
    updated_content: dict[str, Any],
    thread_info: ChatThread,
    google_calendar_api: CalendarProviderManager,
    flight_key: str,
):
    airline_code, flight_number = _get_flight_info(booking, flight_key)

    if not airline_code or not flight_number:
        return

    event_title = f"Flight to {thread_info.title} ({airline_code} {flight_number})"
    event_description_partial = f"Airline confirmation number: {booking.content.get('airline_confirmation_number', '')}"

    events = google_calendar_api.search_event(f"{event_title} {event_description_partial}")

    if len(events) == 1:
        event_id = events[0].get("id")

        flight_data = updated_content.get(flight_key)
        assert flight_data is not None

        (title, departure_date, arrival_date, description, departure_timezone, arrival_timezone, location) = (
            FlightBookingUtil.map_flight_calendar_event(
                thread_info,
                flight_data,
                booking.content.get("airline_confirmation_number", ""),
            )
        )

        google_calendar_api.update_event_overwrite(
            event_id, title, departure_date, arrival_date, description, departure_timezone, arrival_timezone
        )

        logger.info(f"Updating google calendar event with id {event_id} and title {title}")


def update_calendar_legs_event(
    booking: Booking,
    updated_content: dict[str, Any],
    thread_info: ChatThread,
    google_calendar_api: CalendarProviderManager,
):
    _update_calendar_event(booking, updated_content, thread_info, google_calendar_api, "legs")


def update_calendar_outbound_event(
    booking: Booking,
    updated_content: dict[str, Any],
    thread_info: ChatThread,
    google_calendar_api: CalendarProviderManager,
):
    _update_calendar_event(booking, updated_content, thread_info, google_calendar_api, "outbound")


def update_calendar_return_event(
    booking: Booking,
    updated_content: dict[str, Any],
    thread_info: ChatThread,
    google_calendar_api: CalendarProviderManager,
):
    _update_calendar_event(booking, updated_content, thread_info, google_calendar_api, "return")
