{"_id": {"$oid": "6866e52ab22a260dcac30eb8"}, "timestamp": "2025-07-03T20:16:42.223600180Z", "event_type": "PNR_V3", "operation": "BOOKING_OTHER_UPDATE", "payload": {"version": 24, "createdVia": "OBT", "initialVersionCreatedVia": "OBT", "sourceInfo": {"sourcePnrId": "CHBFBG", "bookingSource": "SABRE", "thirdParty": "SABRE", "bookingDateTime": {"iso8601": "2025-05-05T16:22:07Z"}, "posDescriptor": "0UIL", "iataNumber": ""}, "invoiceDelayedBooking": false, "travelers": [{"travelerPersonalInfo": {"loyaltyInfos": [{"appliedTo": ["AS"], "id": "54254701", "issuedBy": "AS", "type": "AIR"}], "travelPref": {}}, "user": {"dob": {"iso8601": "1976-01-26"}, "email": "micha<PERSON>@gulmann.com", "gender": "MALE", "name": {"family1": "<PERSON><PERSON><PERSON>", "family2": "", "given": "<PERSON>", "middle": "", "preferred": ""}, "paymentInfos": [{"applicableTo": [], "card": {"id": "3dc940d6-f88f-4d3a-81f2-81d50632f8c1", "type": "CREDIT", "company": "MASTERCARD", "name": "<PERSON>", "address": {"addressLines": ["999 3rd ave"], "postalCode": "98104", "regionCode": "US"}, "number": "5XXXXXXXXXXX0890", "expiryMonth": 7, "expiryYear": 2027, "cvv": "", "label": "OTTO credit card"}, "accessType": "PERSONAL", "access": {"accessType": "PERSONAL", "entityIds": ["78c7197c-f5df-4e45-a831-c216a8247b5b"], "entities": [{"entityId": "78c7197c-f5df-4e45-a831-c216a8247b5b", "centralCardAccessLevel": "UNKNOWN"}]}}], "phoneNumbers": [{"countryCode": 1, "countryCodeSource": "UNSPECIFIED", "extension": "", "isoCountryCode": "US", "italianLeadingZero": false, "nationalNumber": 0, "numberOfLeadingZeros": 0, "preferredDomesticCarrierCode": "", "rawInput": "**********", "type": "MOBILE"}]}, "userBusinessInfo": {"designation": "", "email": "micha<PERSON>@gulmann.com", "employeeId": "", "legalEntityId": {"id": "a20af512-83a9-4dc7-b839-2ea0a3c78adb"}, "organizationId": {"id": "e3f72b2d-053d-415b-8edc-a3acbbaea31c"}}, "userOrgId": {"organizationAgencyId": {"id": "e3f72b2d-053d-415b-8edc-a3acbbaea31c"}, "organizationId": {"id": "e3f72b2d-053d-415b-8edc-a3acbbaea31c"}, "userId": {"id": "78c7197c-f5df-4e45-a831-c216a8247b5b"}, "tmcInfo": {"id": {"id": "9aafd1d6-e99b-4860-853a-3671270b3a63"}, "primaryServiceProviderTmc": {"tmcId": {"id": "9aafd1d6-e99b-4860-853a-3671270b3a63"}}, "secondaryServiceProviderTmcs": [], "partnerTmcId": {"id": "e3f72b2d-053d-415b-8edc-a3acbbaea31c"}}, "tmcBasicInfo": {"contractingTmc": {"id": {"id": "e3f72b2d-053d-415b-8edc-a3acbbaea31c"}}, "bookingTmc": {"id": {"id": "9aafd1d6-e99b-4860-853a-3671270b3a63"}}}}, "persona": "GUEST", "isActive": true, "tier": "BASIC"}], "pnrTravelers": [{"userId": {"id": "78c7197c-f5df-4e45-a831-c216a8247b5b"}, "travelerInfo": {"userId": {"id": "78c7197c-f5df-4e45-a831-c216a8247b5b"}, "adhocTravelerInfo": {}}, "personalInfo": {"dob": {"iso8601": "1976-01-26"}, "email": "micha<PERSON>@gulmann.com", "gender": "MALE", "name": {"family1": "<PERSON><PERSON><PERSON>", "family2": "", "given": "<PERSON>", "middle": "", "preferred": ""}, "phoneNumbers": [{"countryCode": 1, "countryCodeSource": "UNSPECIFIED", "extension": "", "isoCountryCode": "US", "italianLeadingZero": false, "nationalNumber": 0, "numberOfLeadingZeros": 0, "preferredDomesticCarrierCode": "", "rawInput": "**********", "type": "MOBILE"}]}, "loyalties": [{"appliedTo": ["AS"], "id": "54254701", "issuedBy": "AS", "type": "AIR"}], "persona": "GUEST", "businessInfo": {"legalEntity": {"id": "a20af512-83a9-4dc7-b839-2ea0a3c78adb", "name": "Otto Trip, Inc.", "ein": "", "externalId": "", "companySpecifiedAttributes": []}, "companyId": {"id": "e3f72b2d-053d-415b-8edc-a3acbbaea31c"}, "companyInfo": {"id": {"id": "e3f72b2d-053d-415b-8edc-a3acbbaea31c"}, "name": "<PERSON>", "externalId": ""}}, "tier": "BASIC"}], "costOfGoodsSold": {"payments": [{"travelerIndices": [0], "userIds": [{"id": "78c7197c-f5df-4e45-a831-c216a8247b5b"}], "fop": {"type": "CARD", "card": {"id": "3dc940d6-f88f-4d3a-81f2-81d50632f8c1", "type": "CREDIT", "company": "MASTERCARD", "name": "<PERSON>", "address": {"addressLines": ["999 3rd ave"], "administrativeArea": "", "administrativeAreaName": "", "description": "", "isDefault": false, "languageCode": "", "locality": "seattle", "locationCode": "", "organization": "", "postalCode": "98104", "continentCode": "", "recipients": [], "regionCode": "US", "regionName": "", "revision": 0, "sortingCode": "", "sublocality": "", "timezone": ""}, "number": "5XXXXXXXXXXX0890", "expiryMonth": 7, "expiryYear": 2027, "cvv": "", "label": "OTTO credit card", "currency": "", "externalId": "", "vaultId": "00000000-0000-0000-0000-000000000000", "expiry": {}}, "additionalInfo": "", "accessType": {"accessType": "PERSONAL", "entityIds": ["78c7197c-f5df-4e45-a831-c216a8247b5b"], "entities": [{"entityId": "78c7197c-f5df-4e45-a831-c216a8247b5b", "centralCardAccessLevel": "UNKNOWN"}]}, "paymentMethod": "CREDIT_CARD", "paymentMetadata": {"cardMetadata": {"card": {"id": "3dc940d6-f88f-4d3a-81f2-81d50632f8c1", "type": "CREDIT", "company": "MASTERCARD", "name": "<PERSON>", "address": {"addressLines": ["999 3rd ave"], "administrativeArea": "", "administrativeAreaName": "", "description": "", "isDefault": false, "languageCode": "", "locality": "seattle", "locationCode": "", "organization": "", "postalCode": "98104", "continentCode": "", "recipients": [], "regionCode": "US", "regionName": "", "revision": 0, "sortingCode": "", "sublocality": "", "timezone": ""}, "number": "5XXXXXXXXXXX0890", "expiryMonth": 7, "expiryYear": 2027, "cvv": "", "label": "OTTO credit card", "currency": "", "externalId": "", "vaultId": "00000000-0000-0000-0000-000000000000", "expiry": {}}, "accessType": {"accessType": "PERSONAL", "entityIds": ["78c7197c-f5df-4e45-a831-c216a8247b5b"], "entities": [{"entityId": "78c7197c-f5df-4e45-a831-c216a8247b5b", "centralCardAccessLevel": "UNKNOWN"}]}, "isLodgeCard": false}}, "paymentSourceType": "CARD"}, "paymentReference": "", "paymentType": "FLIGHTS", "paymentThirdParty": "UNKNOWN_PARTY", "paymentId": "", "paymentGateway": "PAYMENT_GATEWAY_UNKNOWN", "isRefunded": false, "networkTransactionId": ""}]}, "isFinalized": true, "policyInfo": {"outOfPolicy": false, "reasonCode": "UNKNOWN_CHECKOUT_ANSWER_TYPE", "reason": "", "appliedPolicyInfo": {"policies": [{"id": "4ea33e55-ba5f-41d0-8624-84075ffa3d5f", "version": "0"}], "ruleResultInfos": []}}, "airPnr": {"legs": [{"flights": [{"departureDateTime": {"iso8601": "2025-05-14T07:00:00"}, "arrivalDateTime": {"iso8601": "2025-05-14T09:52:00"}, "duration": {"iso8601": "PT2H52M"}, "flightId": "CgNTRUESA1NBThoVChMyMDI1LTA1LTE0VDA3OjAwOjAwIhUKEzIwMjUtMDUtMTRUMDk6NTI6MDA=", "origin": "SEA", "destination": "SAN", "departureGate": {"gate": "", "terminal": ""}, "arrivalGate": {"gate": "", "terminal": ""}, "marketing": {"num": "1258", "airlineCode": "AS"}, "operating": {"num": "1258", "airlineCode": "AS"}, "operatingAirlineName": "", "hiddenStops": [], "vendorConfirmationNumber": "CGGESU", "cabin": "ECONOMY", "bookingCode": "L", "flightStatus": "CONFIRMED", "otherStatuses": [], "co2EmissionDetail": {"emissionValue": 0.212168, "averageEmissionValue": 0.2411, "flightDistanceKm": 1788, "isApproximate": false}, "restrictions": [], "sourceStatus": "HK", "equipment": {"code": "73J", "type": "", "name": "Boeing 737-900 Winglets"}, "flightWaiverCodes": [], "amenities": [], "flightIndex": 0}], "brandName": "", "validatingAirlineCode": "AS", "legStatus": "COMPLETED_STATUS", "sortingPriority": 0, "travelerRestrictions": [{"userId": {"id": "78c7197c-f5df-4e45-a831-c216a8247b5b"}, "restrictions": ["SEAT_EDIT_NOT_ALLOWED", "LOYALTY_EDIT_NOT_ALLOWED", "KTN_EDIT_NOT_ALLOWED", "REDRESS_EDIT_NOT_ALLOWED"]}], "fareOffers": [], "legId": "CgNTRUESA1NBThoKNzYxNzU3MTc2OQ==", "rateType": "PUBLISHED", "preferredTypes": [], "preferences": [], "legIndex": 0}, {"flights": [{"departureDateTime": {"iso8601": "2025-07-04T08:10:00"}, "arrivalDateTime": {"iso8601": "2025-07-04T09:37:00"}, "duration": {"iso8601": "PT1H27M"}, "flightId": "CgNTQU4SA0xBUxoVChMyMDI1LTA3LTA0VDA4OjEwOjAwIhUKEzIwMjUtMDctMDRUMDk6Mzc6MDA=", "origin": "SAN", "destination": "LAS", "departureGate": {"gate": "", "terminal": "TERMINAL 2"}, "arrivalGate": {"gate": "3", "terminal": "TERMINAL 3"}, "marketing": {"num": "3314", "airlineCode": "AS"}, "operating": {"num": "3314", "airlineCode": "AS"}, "operatingAirlineName": "SKYWEST AIRLINES AS ALASKASKYWEST", "hiddenStops": [], "vendorConfirmationNumber": "CGGESU", "cabin": "ECONOMY", "bookingCode": "Q", "flightStatus": "CANCELLED_BY_VENDOR", "otherStatuses": [], "co2EmissionDetail": {"emissionValue": 0.058336965660000004, "averageEmissionValue": 0.058336965660000004, "flightDistanceKm": 415.20972, "isApproximate": true}, "restrictions": [], "sourceStatus": "HX", "equipment": {"code": "E75", "type": "", "name": "Embraer 175 (short wing)"}, "distance": {"length": 258, "unit": "MILE"}, "flightWaiverCodes": [], "amenities": [{}, {}, {}, {}, {}, {}, {}, {}], "flightIndex": 0}], "brandName": "Refundable Main", "validatingAirlineCode": "AS", "legStatus": "CANCELLED_STATUS", "sortingPriority": 0, "travelerRestrictions": [{"userId": {"id": "78c7197c-f5df-4e45-a831-c216a8247b5b"}, "restrictions": ["SEAT_EDIT_NOT_ALLOWED", "LOYALTY_EDIT_NOT_ALLOWED", "KTN_EDIT_NOT_ALLOWED", "REDRESS_EDIT_NOT_ALLOWED"]}], "fareOffers": [{"userId": {"id": "78c7197c-f5df-4e45-a831-c216a8247b5b"}, "baggagePolicy": {"checkedIn": [{"description": "1 checked bag, 23 kgs (35 USD)"}, {"description": "+1 checked bag, 23 kgs (45 USD)"}], "carryOn": [{"description": "1 carry-on bag"}]}}], "legId": "CgNTQU4SA0xBUxoKNzYxNzU3MTc2OQ==", "rateType": "PUBLISHED", "preferredTypes": [], "preferences": [], "legIndex": 1}, {"flights": [{"departureDateTime": {"iso8601": "2025-09-09T08:05:00"}, "arrivalDateTime": {"iso8601": "2025-09-09T09:37:00"}, "duration": {"iso8601": "PT1H32M"}, "flightId": "CgNTQU4SA0xBUxoVChMyMDI1LTA5LTA5VDA4OjA1OjAwIhUKEzIwMjUtMDktMDlUMDk6Mzc6MDA=", "origin": "SAN", "destination": "LAS", "departureGate": {"gate": "", "terminal": "TERMINAL 2"}, "arrivalGate": {"gate": "3", "terminal": "TERMINAL 3"}, "marketing": {"num": "3211", "airlineCode": "AS"}, "operating": {"num": "3211", "airlineCode": "AS"}, "operatingAirlineName": "SKYWEST AIRLINES AS ALASKASKYWEST", "hiddenStops": [], "vendorConfirmationNumber": "CGGESU", "cabin": "ECONOMY", "bookingCode": "G", "flightStatus": "CONFIRMED", "otherStatuses": [{"cabin": "ECONOMY", "status": "CANCELLED_BY_VENDOR"}], "co2EmissionDetail": {"emissionValue": 0.058336965660000004, "averageEmissionValue": 0.058336965660000004, "flightDistanceKm": 415.20972, "isApproximate": true}, "restrictions": [], "sourceStatus": "HK", "equipment": {"code": "E75", "type": "", "name": "Embraer 175 (short wing)"}, "distance": {"length": 258, "unit": "MILE"}, "flightWaiverCodes": [], "amenities": [{}, {}, {}, {}, {}, {}, {}, {}], "flightIndex": 0}], "brandName": "", "validatingAirlineCode": "", "legStatus": "CONFIRMED_STATUS", "sortingPriority": 0, "travelerRestrictions": [], "fareOffers": [{"userId": {"id": "78c7197c-f5df-4e45-a831-c216a8247b5b"}, "baggagePolicy": {"checkedIn": [], "carryOn": []}}], "legId": "CgNTQU4SA0xBUxoKNzYxNzU3MTc2OQ==", "rateType": "PUBLISHED", "preferredTypes": [], "preferences": [], "legIndex": 2}], "airPnrRemarks": [{"remarkString": "OTTO TRIP, INC."}, {"remarkString": "WORKFLOWID 0C79CED8A3AEA8C0"}, {"remarkString": "S*UD212 OTTO TRIP INC."}, {"remarkString": "  AUTH-CA<PERSON> SECURITY CODE NOT SUPPLIED BY MERCHANT"}, {"remarkString": "SEATTLE WA US 98104"}, {"remarkString": "AEBT/0277244759757/0UIL*AWS/0UIL*AWS"}, {"remarkString": "TRAVELERORGID E3F72B2D-053D-415B-8EDC-A3ACBBAEA31C"}, {"remarkString": "PNRTYPE AIR"}, {"remarkString": "  AUTH-AVS NOT SUPPLIED BY MERCHANT/M             "}, {"remarkString": "AUTH-MPGS/CA0890/22MAY/01491747935271220144       "}, {"remarkString": "XXTAW/"}, {"remarkString": "ISPASSIVEPNR FALSE"}, {"remarkString": "S*UD3 9178451822"}, {"remarkString": "PNRID 7617571769"}, {"remarkString": "S*UD78 IN POLICY"}, {"remarkString": "S*SA804"}, {"remarkString": "XXAUTH/00519J   *Z/CA0890"}, {"remarkString": "  AUTH-APV/02243J/00/USD210.00                    "}, {"remarkString": "AUT-/00519J/511.6"}, {"remarkString": "2-SABREPROFILES¥OTTO TRIP INC."}, {"remarkString": "BOOKEDBYUSERID 61E5B5B8-3FEC-487A-B441-7978CC729B97"}, {"remarkString": "*CA5XXXXXXXXXXX0890¥07/27-XN"}, {"remarkString": "TRIPID 9178451822"}, {"remarkString": "ASD/SP/PNR/SP PNR 7617571769 PCC 0UIL"}, {"remarkString": "TRACEID B41CEC5644665F29"}, {"remarkString": "XXAUTH/02243J/CA5XXXXXXXXXXX0890/AS/USD210.00/22MAY/S"}, {"remarkString": "PPT DOB-01/26/1976 GULMANN/MICHAEL -M"}, {"remarkString": "NO EMAIL"}, {"remarkString": "S*UD166 SPOTNANA"}, {"remarkString": "AEBT/0277244759756/0UIL*AWS/0UIL*AWS"}, {"remarkString": "ENVIRONMENT PROD"}, {"remarkString": "AIR-SEQ1"}, {"remarkString": "BOOKEDBYORGID E3F72B2D-053D-415B-8EDC-A3ACBBAEA31C"}, {"remarkString": "TRAVELERPID 78C7197C-F5DF-4E45-A831-C216A8247B5B"}, {"remarkString": "999 3RD AVE - STE 3300"}], "travelerInfos": [{"airVendorCancellationInfo": {"airVendorCancellationObjects": []}, "createdMcos": [], "travelerIdx": 0, "userId": {"id": "78c7197c-f5df-4e45-a831-c216a8247b5b"}, "paxType": "ADULT", "tickets": [{"ticketNumber": "0272110424480", "ticketType": "FLIGHT", "issuedDateTime": {"iso8601": "2025-07-03T15:15:00"}, "status": "ISSUED", "amount": {"base": {"amount": 447.44, "currencyCode": "USD", "convertedAmount": 447.44, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 61.37, "currencyCode": "USD", "convertedAmount": 61.37, "convertedCurrency": "USD", "otherCoinage": []}}, "flightCoupons": [{"legIdx": 2, "flightIdx": 0, "status": "NOT_FLOWN"}], "ancillaries": [], "validatingAirlineCode": "AS", "exchangeInfo": {"originalTicketNumber": "0277244759757", "addCollectAmount": {"base": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}}, "originalTicketDetails": {"ticketNumber": "0277244759757", "amount": {"base": {"amount": 447.44, "currencyCode": "USD", "convertedAmount": 447.44, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 64.16, "currencyCode": "USD", "convertedAmount": 64.16, "convertedCurrency": "USD", "otherCoinage": []}}, "taxBreakdown": {"tax": [{"amount": {"amount": 33.56, "currencyCode": "USD", "convertedAmount": 33.56, "convertedCurrency": "USD", "otherCoinage": []}, "taxCode": "US"}, {"amount": {"amount": 10.4, "currencyCode": "USD", "convertedAmount": 10.4, "convertedCurrency": "USD", "otherCoinage": []}, "taxCode": "ZP"}, {"amount": {"amount": 20.2, "currencyCode": "USD", "convertedAmount": 20.2, "convertedCurrency": "USD", "otherCoinage": []}, "taxCode": "XT"}]}, "conjunctionTicketSuffix": "", "ctcRefund": {}}}, "taxBreakdown": {"tax": [{"amount": {"amount": 30.77, "currencyCode": "USD", "convertedAmount": 30.77, "convertedCurrency": "USD", "otherCoinage": []}, "taxCode": "US"}, {"amount": {"amount": 10.4, "currencyCode": "USD", "convertedAmount": 10.4, "convertedCurrency": "USD", "otherCoinage": []}, "taxCode": "ZP"}, {"amount": {"amount": 11.2, "currencyCode": "USD", "convertedAmount": 11.2, "convertedCurrency": "USD", "otherCoinage": []}, "taxCode": "AY"}, {"amount": {"amount": 9, "currencyCode": "USD", "convertedAmount": 9, "convertedCurrency": "USD", "otherCoinage": []}, "taxCode": "XF"}]}, "iataNumber": "55280002", "fareCalculation": "SEA AS SAN319.07AS LAS91.16USD410.23END ZPSEASAN XT11.20AY9.00XFSEA4.5SAN4.5", "paymentDetails": [], "ticketSettlement": "UNKNOWN_TICKET_SETTLEMENT", "publishedFare": {"base": {"amount": 410.23, "currencyCode": "USD", "convertedAmount": 410.23, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 61.37, "currencyCode": "USD", "convertedAmount": 61.37, "convertedCurrency": "USD", "otherCoinage": []}}, "vendorCancellationId": "", "conjunctionTicketSuffix": [], "pcc": "", "ticketIncompleteReasons": ["SYS_TICKET"], "associatedTicketNumber": "", "rficCode": ""}, {"ticketNumber": "0277244759753", "ticketType": "FLIGHT", "issuedDateTime": {"iso8601": "2025-05-05T12:43:00"}, "status": "EXCHANGED", "amount": {"base": {"amount": 447.44, "currencyCode": "USD", "convertedAmount": 447.44, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 64.16, "currencyCode": "USD", "convertedAmount": 64.16, "convertedCurrency": "USD", "otherCoinage": []}}, "flightCoupons": [{"legIdx": 0, "flightIdx": 0, "status": "UNKNOWN"}], "ancillaries": [], "validatingAirlineCode": "AS", "exchangePolicy": {"exchangePenalty": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "isExchangeable": true, "isCat16": false, "isConditional": true}, "refundPolicy": {"refundPenalty": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "isRefundable": true, "isRefundableByObt": false, "isCat16": false, "isConditional": true}, "refundInfo": {"refundAmount": {"base": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}}}, "commission": {"amount": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "percent": 0}, "iataNumber": "", "fareCalculation": "", "updateDateTime": {"iso8601": "2025-05-22T13:34:00"}, "paymentDetails": [{"amount": {"base": {"amount": 447.44, "currencyCode": "USD", "convertedAmount": 447.44, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 64.16, "currencyCode": "USD", "convertedAmount": 64.16, "convertedCurrency": "USD", "otherCoinage": []}}, "fop": {"type": "CARD", "card": {"id": "3dc940d6-f88f-4d3a-81f2-81d50632f8c1", "type": "CREDIT", "company": "MASTERCARD", "name": "<PERSON>", "address": {"addressLines": ["999 3rd ave"], "administrativeArea": "", "administrativeAreaName": "", "description": "", "isDefault": false, "languageCode": "", "locality": "", "locationCode": "", "organization": "", "postalCode": "98104", "continentCode": "", "recipients": [], "regionCode": "US", "regionName": "", "revision": 0, "sortingCode": "", "sublocality": "", "timezone": ""}, "number": "5XXXXXXXXXXX0890", "expiryMonth": 7, "expiryYear": 2027, "cvv": "", "label": "OTTO credit card", "currency": "", "externalId": ""}, "additionalInfo": "", "accessType": {"accessType": "PERSONAL", "entityIds": ["78c7197c-f5df-4e45-a831-c216a8247b5b"], "entities": [{"entityId": "78c7197c-f5df-4e45-a831-c216a8247b5b", "centralCardAccessLevel": "UNKNOWN"}]}, "paymentMethod": "CREDIT_CARD"}, "isRefunded": false}], "ticketSettlement": "ARC_TICKET", "publishedFare": {"base": {"amount": 447.44, "currencyCode": "USD", "convertedAmount": 447.44, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 64.16, "currencyCode": "USD", "convertedAmount": 64.16, "convertedCurrency": "USD", "otherCoinage": []}}, "vendorCancellationId": "", "conjunctionTicketSuffix": [], "pcc": "0UIL", "ticketIncompleteReasons": [], "associatedTicketNumber": "", "rficCode": ""}, {"ticketNumber": "0277244759756", "ticketType": "FLIGHT", "issuedDateTime": {"iso8601": "2025-05-22T13:34:00"}, "status": "EXCHANGED", "amount": {"base": {"amount": 642.79, "currencyCode": "USD", "convertedAmount": 642.79, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 78.81, "currencyCode": "USD", "convertedAmount": 78.81, "convertedCurrency": "USD", "otherCoinage": []}}, "flightCoupons": [], "ancillaries": [], "validatingAirlineCode": "AS", "exchangePolicy": {"exchangePenalty": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "isExchangeable": true, "isCat16": false, "isConditional": true}, "exchangeInfo": {"originalTicketNumber": "0277244759753", "addCollectAmount": {"base": {"amount": 195.35, "currencyCode": "USD", "convertedAmount": 195.35, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 14.65, "currencyCode": "USD", "convertedAmount": 14.65, "convertedCurrency": "USD", "otherCoinage": []}}}, "refundPolicy": {"refundPenalty": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "isRefundable": true, "isRefundableByObt": false, "isCat16": false, "isConditional": true}, "refundInfo": {"refundAmount": {"base": {"amount": 195.35, "currencyCode": "USD", "convertedAmount": 195.35, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 14.65, "currencyCode": "USD", "convertedAmount": 14.65, "convertedCurrency": "USD", "otherCoinage": []}}}, "commission": {"amount": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "percent": 0}, "iataNumber": "", "fareCalculation": "", "updateDateTime": {"iso8601": "2025-05-22T16:16:00"}, "paymentDetails": [{"amount": {"base": {"amount": 195.35, "currencyCode": "USD", "convertedAmount": 195.35, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 14.65, "currencyCode": "USD", "convertedAmount": 14.65, "convertedCurrency": "USD", "otherCoinage": []}}, "fop": {"type": "CARD", "card": {"id": "3dc940d6-f88f-4d3a-81f2-81d50632f8c1", "type": "CREDIT", "company": "MASTERCARD", "name": "<PERSON>", "address": {"addressLines": ["999 3rd ave"], "administrativeArea": "", "administrativeAreaName": "", "description": "", "isDefault": false, "languageCode": "", "locality": "", "locationCode": "", "organization": "", "postalCode": "98104", "continentCode": "", "recipients": [], "regionCode": "US", "regionName": "", "revision": 0, "sortingCode": "", "sublocality": "", "timezone": ""}, "number": "5XXXXXXXXXXX0890", "expiryMonth": 7, "expiryYear": 2027, "cvv": "", "label": "OTTO credit card", "currency": "", "externalId": ""}, "additionalInfo": "", "accessType": {"accessType": "PERSONAL", "entityIds": ["78c7197c-f5df-4e45-a831-c216a8247b5b"], "entities": [{"entityId": "78c7197c-f5df-4e45-a831-c216a8247b5b", "centralCardAccessLevel": "UNKNOWN"}]}, "paymentMethod": "CREDIT_CARD"}, "isRefunded": false}, {"amount": {"base": {"amount": 195.35, "currencyCode": "USD", "convertedAmount": 195.35, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 14.65, "currencyCode": "USD", "convertedAmount": 14.65, "convertedCurrency": "USD", "otherCoinage": []}}, "fop": {"type": "CARD", "card": {"id": "3dc940d6-f88f-4d3a-81f2-81d50632f8c1", "type": "CREDIT", "company": "MASTERCARD", "name": "<PERSON>", "address": {"addressLines": ["999 3rd ave"], "administrativeArea": "", "administrativeAreaName": "", "description": "", "isDefault": false, "languageCode": "", "locality": "", "locationCode": "", "organization": "", "postalCode": "98104", "continentCode": "", "recipients": [], "regionCode": "US", "regionName": "", "revision": 0, "sortingCode": "", "sublocality": "", "timezone": ""}, "number": "5XXXXXXXXXXX0890", "expiryMonth": 7, "expiryYear": 2027, "cvv": "", "label": "OTTO credit card", "currency": "", "externalId": ""}, "additionalInfo": "", "accessType": {"accessType": "PERSONAL", "entityIds": ["78c7197c-f5df-4e45-a831-c216a8247b5b"], "entities": [{"entityId": "78c7197c-f5df-4e45-a831-c216a8247b5b", "centralCardAccessLevel": "UNKNOWN"}]}, "paymentMethod": "CREDIT_CARD"}, "isRefunded": true}], "ticketSettlement": "ARC_TICKET", "publishedFare": {"base": {"amount": 642.79, "currencyCode": "USD", "convertedAmount": 642.79, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 78.81, "currencyCode": "USD", "convertedAmount": 78.81, "convertedCurrency": "USD", "otherCoinage": []}}, "vendorCancellationId": "", "conjunctionTicketSuffix": [], "pcc": "0UIL", "ticketIncompleteReasons": [], "associatedTicketNumber": "", "rficCode": ""}, {"ticketNumber": "0277244759757", "ticketType": "FLIGHT", "issuedDateTime": {"iso8601": "2025-05-22T16:16:00"}, "status": "EXCHANGED", "amount": {"base": {"amount": 447.44, "currencyCode": "USD", "convertedAmount": 447.44, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 64.16, "currencyCode": "USD", "convertedAmount": 64.16, "convertedCurrency": "USD", "otherCoinage": []}}, "flightCoupons": [{"legIdx": 1, "flightIdx": 0, "status": "EXCHANGED"}], "ancillaries": [], "validatingAirlineCode": "AS", "exchangePolicy": {"exchangePenalty": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "isExchangeable": true, "isCat16": false, "isConditional": true}, "exchangeInfo": {"originalTicketNumber": "0277244759756", "addCollectAmount": {"base": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}}, "originalTicketDetails": {"ticketNumber": "0277244759756", "amount": {"base": {"amount": 642.79, "currencyCode": "USD", "convertedAmount": 642.79, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 78.81, "currencyCode": "USD", "convertedAmount": 78.81, "convertedCurrency": "USD", "otherCoinage": []}}, "taxBreakdown": {"tax": []}, "conjunctionTicketSuffix": "", "ctcRefund": {"base": {"amount": 195.35, "currencyCode": "USD", "convertedAmount": 195.35, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 14.65, "currencyCode": "USD", "convertedAmount": 14.65, "convertedCurrency": "USD", "otherCoinage": []}}}, "residueType": "REFUNDED"}, "refundPolicy": {"refundPenalty": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "isRefundable": true, "isRefundableByObt": false, "isCat16": false, "isConditional": true}, "taxBreakdown": {"tax": [{"amount": {"amount": 33.56, "currencyCode": "USD", "convertedAmount": 33.56, "convertedCurrency": "USD", "otherCoinage": []}, "taxCode": "US"}, {"amount": {"amount": 10.4, "currencyCode": "USD", "convertedAmount": 10.4, "convertedCurrency": "USD", "otherCoinage": []}, "taxCode": "ZP"}, {"amount": {"amount": 20.2, "currencyCode": "USD", "convertedAmount": 20.2, "convertedCurrency": "USD", "otherCoinage": []}, "taxCode": "XT"}]}, "commission": {"amount": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "percent": 0}, "iataNumber": "45526666", "fareCalculation": "SEA AS SAN319.07AS LAS128.37USD447.44END ZPSEASAN XT11.20AY9.00XFSEA4.5SAN4.5", "updateDateTime": {"iso8601": "2025-07-03T15:15:00"}, "paymentDetails": [{"amount": {"base": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}}, "fop": {"type": "CARD", "card": {"id": "3dc940d6-f88f-4d3a-81f2-81d50632f8c1", "type": "CREDIT", "company": "MASTERCARD", "name": "<PERSON>", "address": {"addressLines": ["999 3rd ave"], "administrativeArea": "", "administrativeAreaName": "", "description": "", "isDefault": false, "languageCode": "", "locality": "", "locationCode": "", "organization": "", "postalCode": "98104", "continentCode": "", "recipients": [], "regionCode": "US", "regionName": "", "revision": 0, "sortingCode": "", "sublocality": "", "timezone": ""}, "number": "5XXXXXXXXXXX0890", "expiryMonth": 7, "expiryYear": 2027, "cvv": "", "label": "OTTO credit card", "currency": "", "externalId": ""}, "additionalInfo": "", "accessType": {"accessType": "PERSONAL", "entityIds": ["78c7197c-f5df-4e45-a831-c216a8247b5b"], "entities": [{"entityId": "78c7197c-f5df-4e45-a831-c216a8247b5b", "centralCardAccessLevel": "UNKNOWN"}]}, "paymentMethod": "CREDIT_CARD"}, "isRefunded": false}], "ticketSettlement": "ARC_TICKET", "publishedFare": {"base": {"amount": 447.44, "currencyCode": "USD", "convertedAmount": 447.44, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 64.16, "currencyCode": "USD", "convertedAmount": 64.16, "convertedCurrency": "USD", "otherCoinage": []}}, "vendorCancellationId": "", "conjunctionTicketSuffix": [], "pcc": "0UIL", "ticketIncompleteReasons": [], "associatedTicketNumber": "", "rficCode": ""}], "boardingPass": [], "booking": {"seats": [{"legIdx": 0, "flightIdx": 0, "number": "25F", "status": "CONFIRMED", "sourceStatus": "KK"}], "luggageDetails": [], "otherAncillaries": [], "itinerary": {"totalFare": {"base": {"amount": 447.44, "currencyCode": "USD", "convertedAmount": 447.44, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 64.16, "currencyCode": "USD", "convertedAmount": 64.16, "convertedCurrency": "USD", "otherCoinage": []}}, "totalFlightsFare": {"base": {"amount": 447.44, "currencyCode": "USD", "convertedAmount": 447.44, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 64.16, "currencyCode": "USD", "convertedAmount": 64.16, "convertedCurrency": "USD", "otherCoinage": []}}, "flightFareBreakup": [{"legIndices": [0, 1, 2], "flightsFare": {"base": {"amount": 447.44, "currencyCode": "USD", "convertedAmount": 447.44, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 64.16, "currencyCode": "USD", "convertedAmount": 64.16, "convertedCurrency": "USD", "otherCoinage": []}}}], "fareComponents": [{"fareBasisCode": "LH7OASMR", "tourCode": "", "ticketDesignator": "", "baseFare": {"amount": 319.07, "currencyCode": "USD", "convertedAmount": 319.07, "convertedCurrency": "USD", "otherCoinage": []}, "flightIds": [{"legIdx": 0, "flightIdx": 0}], "farePaxType": "ADULT"}, {"fareBasisCode": "QH2OASMR", "tourCode": "", "ticketDesignator": "", "baseFare": {"amount": 128.37, "currencyCode": "USD", "convertedAmount": 128.37, "convertedCurrency": "USD", "otherCoinage": []}, "flightIds": [{"legIdx": 1, "flightIdx": 0}], "farePaxType": "ADULT"}, {"fareBasisCode": "", "tourCode": "", "ticketDesignator": "", "flightIds": [{"legIdx": 2, "flightIdx": 0}], "farePaxType": "UNKNOWN_PASSENGER_TYPE"}], "otherAncillaryFares": []}, "otherCharges": []}, "appliedCredits": [], "specialServiceRequestInfos": []}], "bookingMetadata": {"fareStatistics": {"statisticsItems": [{"statisticType": "MINIMUM", "totalFare": {"base": {"amount": 132.16, "currencyCode": "USD", "convertedAmount": 132.16, "convertedCurrency": "USD"}, "tax": {"amount": 59.91, "currencyCode": "USD", "convertedAmount": 59.91, "convertedCurrency": "USD"}}}, {"statisticType": "MEDIAN", "totalFare": {"base": {"amount": 396.28, "currencyCode": "USD", "convertedAmount": 396.28, "convertedCurrency": "USD"}, "tax": {"amount": 60.32, "currencyCode": "USD", "convertedAmount": 60.32, "convertedCurrency": "USD"}}}, {"statisticType": "MAXIMUM", "totalFare": {"base": {"amount": 1444.99, "currencyCode": "USD", "convertedAmount": 1444.99, "convertedCurrency": "USD"}, "tax": {"amount": 158.37, "currencyCode": "USD", "convertedAmount": 158.37, "convertedCurrency": "USD"}}}]}}, "otherServiceInfos": [{"customText": "NO MATCH 05MAY25 1148", "flightIndexes": [{"flightIndex": 0, "legIndex": 0}, {"flightIndex": 0, "legIndex": 1}, {"flightIndex": 0, "legIndex": 2}]}, {"customText": "IRROPP **********", "flightIndexes": [{"flightIndex": 0, "legIndex": 0}, {"flightIndex": 0, "legIndex": 1}, {"flightIndex": 0, "legIndex": 2}]}, {"customText": "IRROPE MICHAEL/GULMANN.COM", "flightIndexes": [{"flightIndex": 0, "legIndex": 0}, {"flightIndex": 0, "legIndex": 1}, {"flightIndex": 0, "legIndex": 2}]}], "disruptedFlightDetails": [{"departureDateTime": {"iso8601": "2025-05-14T07:00:00"}, "arrivalDateTime": {"iso8601": "2025-05-14T09:52:00"}, "cabin": "ECONOMY", "originAirportCode": "SEA", "destinationAirportCode": "SAN", "marketing": {"num": "1258"}, "operating": {"num": "1258"}}, {"departureDateTime": {"iso8601": "2025-07-04T08:10:00"}, "arrivalDateTime": {"iso8601": "2025-07-04T09:37:00"}, "cabin": "ECONOMY", "originAirportCode": "SAN", "destinationAirportCode": "LAS", "marketing": {"num": "3314"}, "operating": {"num": "3314"}}]}, "additionalMetadata": {"airportInfo": [{"airportCode": "SAN", "airportName": "San Diego International Airport", "cityName": "San Diego", "countryName": "United States", "countryCode": "US", "zoneName": "America/Los_Angeles", "stateCode": "CA"}, {"airportCode": "SEA", "airportName": "Seattle–Tacoma International Airport", "cityName": "Seattle", "countryName": "United States", "countryCode": "US", "zoneName": "America/Los_Angeles", "stateCode": "WA"}, {"airportCode": "LAS", "airportName": "<PERSON> International Airport", "cityName": "Las Vegas", "countryName": "United States", "countryCode": "US", "zoneName": "America/Los_Angeles", "stateCode": "NV"}], "airlineInfo": [{"airlineCode": "AS", "airlineName": "Alaska Airlines, Inc."}]}, "preBookAnswers": {"answers": []}, "customFields": [], "customFieldsV3Responses": [], "bookingHistory": [{"bookerInfo": {"name": "ApiUser Otto", "email": "<EMAIL>", "role": "AGENT", "tmcName": ""}, "bookingInfo": {"updatedDateTime": {"iso8601": "2025-06-02T20:16:20Z"}, "status": "UPDATED", "bookingSourceClient": "WEB"}}, {"bookerInfo": {"name": "ApiUser Otto", "email": "<EMAIL>", "role": "AGENT", "tmcName": ""}, "bookingInfo": {"updatedDateTime": {"iso8601": "2025-05-26T19:45:59Z"}, "status": "EXCHANGED", "bookingSourceClient": "WEB"}}, {"bookerInfo": {"name": "ApiUser Otto", "email": "<EMAIL>", "role": "AGENT", "tmcName": ""}, "bookingInfo": {"updatedDateTime": {"iso8601": "2025-05-22T20:16:20Z"}, "status": "EXCHANGED", "bookingSourceClient": "WEB"}}, {"bookerInfo": {"name": "ApiUser Otto", "email": "<EMAIL>", "role": "AGENT", "tmcName": ""}, "bookingInfo": {"updatedDateTime": {"iso8601": "2025-05-22T17:34:07Z"}, "status": "EXCHANGED", "bookingSourceClient": "WEB"}}, {"bookerInfo": {"name": "ApiUser Otto", "email": "<EMAIL>", "role": "AGENT", "tmcName": ""}, "bookingInfo": {"updatedDateTime": {"iso8601": "2025-05-05T16:22:07Z"}, "status": "BOOKED", "bookingSourceClient": "WEB"}}], "totalFare": {"amount": 511.6, "currencyCode": "USD", "convertedAmount": 511.6, "convertedCurrency": "USD"}, "serviceFees": [], "paymentInfo": [{"fop": {"type": "CARD", "card": {"id": "3dc940d6-f88f-4d3a-81f2-81d50632f8c1", "type": "CREDIT", "company": "MASTERCARD", "name": "<PERSON>", "address": {"addressLines": ["999 3rd ave"], "administrativeArea": "", "administrativeAreaName": "", "description": "", "isDefault": false, "languageCode": "", "locality": "", "locationCode": "", "organization": "", "postalCode": "98104", "continentCode": "", "recipients": [], "regionCode": "US", "regionName": "", "revision": 0, "sortingCode": "", "sublocality": "", "timezone": ""}, "number": "5XXXXXXXXXXX0890", "expiryMonth": 7, "expiryYear": 2027, "cvv": "", "label": "OTTO credit card", "currency": "", "externalId": ""}, "additionalInfo": "", "accessType": {"accessType": "PERSONAL", "entityIds": ["78c7197c-f5df-4e45-a831-c216a8247b5b"], "entities": [{"entityId": "78c7197c-f5df-4e45-a831-c216a8247b5b", "centralCardAccessLevel": "UNKNOWN"}]}, "paymentMethod": "CREDIT_CARD"}, "totalCharge": {"amount": 721.6, "currencyCode": "USD", "convertedAmount": 721.6, "convertedCurrency": "USD", "otherCoinage": []}, "totalRefund": {"amount": 210, "currencyCode": "USD", "convertedAmount": 210, "convertedCurrency": "USD", "otherCoinage": []}, "netCharge": {"amount": 511.6, "currencyCode": "USD", "convertedAmount": 511.6, "convertedCurrency": "USD", "otherCoinage": []}}], "bookingStatus": "CONFIRMED_STATUS", "contactSupport": true, "travelerPnrVisibilityStatus": "VISIBLE", "pnrCreationDetails": {}, "tripId": "9178451822", "documents": [], "pnrId": "7617571769", "totalFareAmount": {"base": {"amount": 447.44, "currencyCode": "USD", "convertedAmount": 447.44, "convertedCurrency": "USD"}, "tax": {"amount": 64.16, "currencyCode": "USD", "convertedAmount": 64.16, "convertedCurrency": "USD"}}, "dkNumber": "SPOT0UILUS", "savingsFare": {"fareAmount": {"base": {"amount": 447.44, "currencyCode": "USD", "convertedAmount": 447.44, "convertedCurrency": "USD"}, "tax": {"amount": 64.16, "currencyCode": "USD", "convertedAmount": 64.16, "convertedCurrency": "USD"}}, "isTaxIncluded": true}, "tripUsageMetadata": {"tripUsageType": "STANDARD"}, "owningPccInfo": {"zoneId": "US/Eastern"}}, "operationSummary": {"pnrUpdateSummary": {}}}