import traceback

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
)

from baml_client.async_client import b as b_async
from front_of_house_agent.sample_trip import get_sample_trips
from llm_utils.llm_utils import (
    get_message_buffer_as_strings,
)
from server.utils.logger import logger
from server.utils.settings import settings
from virtual_travel_agent.helpers import get_current_date_string
from virtual_travel_agent.timings import LatencyTimer

MAX_MESSAGE_SIZE = 4


class Guardrail:
    def __init__(self, user_id: int, timezone: str | None = None):
        self.timezone = timezone
        self.user_id = user_id

    async def run(
        self, messages: list[BaseMessage] = [], message_type: str = "", enable_trip_planning: bool = False
    ) -> tuple[bool, AIMessage | None]:
        t = LatencyTimer("GuardrailAgent:run")

        if messages is None or len(messages) == 0 or message_type != "prompt":
            t.end(tag="NotPromptMessage")
            return True, None

        try:
            response = await self.guardrail_runnable_function(messages, enable_trip_planning)
            is_capable, guardrail_message = response
            if is_capable:
                t.end(tag="IsCapable")
                return True, None

            t.end(tag="NotCapable")
            return False, guardrail_message
        except Exception as e:
            logger.warning(f"Error running guardrail agent: {e}, {traceback.format_exc()}")
            t.end(tag="Error")
            return False, None

    async def guardrail_runnable_function(
        self, messages: list[BaseMessage], enable_trip_planning: bool
    ) -> tuple[bool, AIMessage | None]:
        message_buffer_strs = get_message_buffer_as_strings(messages[-MAX_MESSAGE_SIZE:])
        response = b_async.stream.CapabilitiesCheck(
            messages=message_buffer_strs,
            multi_date_search=enable_trip_planning,
            current_date=get_current_date_string(self.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )

        async for p in response:
            if p.is_capable is None:
                continue

            if p.is_capable:
                logger.log_baml()
                return True, None

        logger.log_baml()

        has_sample_trips_in_messages = any(message.additional_kwargs.get("sample_trips") for message in messages)

        ai_response = (await response.get_final_response()).response or ""
        if has_sample_trips_in_messages:
            return False, AIMessage(
                content=ai_response,
            )

        return False, AIMessage(
            content=ai_response,
            additional_kwargs={
                "agent_classification": "GUARDRAIL",
                "sample_trips": (await get_sample_trips(self.user_id)),
            },
        )
