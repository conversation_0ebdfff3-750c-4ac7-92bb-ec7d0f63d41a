import json
from typing import TYPE_CHECKING, Any, Optional

from langchain_core.messages import BaseMessage

from baml_client import b
from baml_client.types import (
    FlightSearchCoreCriteria,
    FlightType,
    ResponseAllPreferences,
)

# Add imports for the check logic
from front_of_house_agent.common_models import TravelContext
from front_of_house_agent.front_of_house_agent import FrontOfHouseWorkAgentType
from front_of_house_agent.sub_agent.trip_planner_sub_agent import (
    handle_flight_planner_v2,
)
from front_of_house_agent.worker_agents.agent import (
    AgentValidationResponse,
    ValidationIssue,
    ValidationIssueType,
    WorkerAgent,
)
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.database.models.chat_thread import ChatThread
from server.utils.settings import AgentTypes
from virtual_travel_agent.helpers import (
    get_current_date_string,
)

if TYPE_CHECKING:
    import front_of_house_agent.front_of_house_agent as fha


class FlightAgentResponse(AgentValidationResponse):
    def __init__(
        self,
        flight_search_core_criteria: FlightSearchCoreCriteria,
    ):
        self.flight_search_core_criteria = flight_search_core_criteria

    def get_agent_type(self) -> AgentTypes:
        return AgentTypes.FLIGHTS

    def extract_validation_issues(self) -> list[ValidationIssue]:
        validation_issues = []
        criteria = self.flight_search_core_criteria

        if not criteria.outbound_date:
            validation_issues.append(
                ValidationIssue(
                    type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                    name="outbound_date",
                    description="The departure date for the flight",
                )
            )
        if not criteria.departure_airport_code:
            validation_issues.append(
                ValidationIssue(
                    type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                    name="departure_airport_code",
                    description="The departure airport for the flight",
                )
            )
        if not criteria.arrival_airport_code:
            validation_issues.append(
                ValidationIssue(
                    type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                    name="arrival_airport_code",
                    description="The arrival airport for the flight",
                )
            )

        if criteria.flight_type == FlightType.RoundTrip and not criteria.return_date:
            validation_issues.append(
                ValidationIssue(
                    type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                    name="return_date",
                    description="The return date for the flight",
                )
            )

        if criteria.flight_type == FlightType.MultiLegs:
            if not criteria.flight_segments:
                validation_issues.append(
                    ValidationIssue(
                        type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                        name="flight_segments",
                        description="The flight segments for the multi-leg trip",
                    )
                )
            else:
                for i, segment in enumerate(criteria.flight_segments):
                    if not segment.departure_airport_code:
                        validation_issues.append(
                            ValidationIssue(
                                type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                                name="departure_airport_code",
                                description=f"The departure airport for flight segment {i + 1}",
                            )
                        )
                    if not segment.arrival_airport_code:
                        validation_issues.append(
                            ValidationIssue(
                                type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                                name="arrival_airport_code",
                                description=f"The arrival airport for flight segment {i + 1}",
                            )
                        )
                    if not segment.outbound_date:
                        validation_issues.append(
                            ValidationIssue(
                                type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                                name="outbound_date",
                                description=f"The departure date for flight segment {i + 1}",
                            )
                        )

        return validation_issues


class FlightAgent(WorkerAgent):
    def __init__(
        self,
        timezone: Optional[str],
        thread: ChatThread,
    ):
        self.timezone = timezone
        self.thread = thread

    async def validate(
        self,
        current_work_type: FrontOfHouseWorkAgentType,
        travel_context: TravelContext,
        user_preferences: ResponseAllPreferences,
        messages: list[BaseMessage],
    ) -> AgentValidationResponse:
        message_buffer_strs = get_message_buffer_as_strings(messages)

        flight_search_core_criteria = await b.FlightSearchCriteriaExtractor(
            travel_preference=user_preferences.model_dump_json(exclude_none=True),
            flight_search_core_criteria=travel_context.flight_search_core_criteria.model_dump_json(exclude_none=True),
            selected_flight_itinerary=json.dumps(travel_context.to_selected_flights()),
            messages=message_buffer_strs,
            current_date=get_current_date_string(self.timezone),
        )

        return FlightAgentResponse(
            flight_search_core_criteria=flight_search_core_criteria,
        )

    async def execute_plan(
        self,
        foh: "fha.FrontOfHouseAgent",
        classified_type_from_foh: FrontOfHouseWorkAgentType,
        messages: list[BaseMessage],
        extra_context: dict[str, Any],
        tool_call_id: str,
    ) -> dict[str, Any]:
        return await handle_flight_planner_v2(foh, classified_type_from_foh, messages, extra_context, tool_call_id)

    def get_agent_type(self) -> AgentTypes:
        return AgentTypes.FLIGHTS
