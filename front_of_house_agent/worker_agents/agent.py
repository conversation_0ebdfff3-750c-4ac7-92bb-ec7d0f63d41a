from abc import ABC, abstractmethod
from enum import Enum

from langchain_core.messages import BaseMessage
from pydantic import BaseModel

from baml_client.types import ResponseAllPreferences
from front_of_house_agent.common_models import (
    TravelContext,
)
from front_of_house_agent.front_of_house_agent import FrontOfHouseWorkAgentType
from server.utils.settings import AgentTypes


class ValidationIssueType(Enum):
    MISSING_REQUIRED_FIELD = "missing_required_field"
    OPERATION_NOT_FEASIBLE = "operation_not_feasible"


class ValidationIssue(BaseModel):
    type: ValidationIssueType
    name: str
    description: str


class AgentValidationResponse(ABC):
    @abstractmethod
    def extract_validation_issues(self) -> list[ValidationIssue]:
        pass

    @abstractmethod
    def get_agent_type(self) -> AgentTypes:
        pass


class WorkerAgent(ABC):
    @abstractmethod
    async def validate(
        self,
        current_work_type: FrontOfHouseWorkAgentType,
        travel_context: TravelContext,
        user_preferences: ResponseAllPreferences,
        messages: list[BaseMessage],
    ) -> AgentValidationResponse:
        pass

    @abstractmethod
    def get_agent_type(self) -> AgentTypes:
        pass
