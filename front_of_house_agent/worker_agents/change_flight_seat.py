from typing import TYPE_CHECKING, Any, Optional

from langchain_core.messages import BaseMessage

from baml_client.types import (
    ResponseAllPreferences,
)

# Add imports for the check logic
from front_of_house_agent.common_models import TravelContext
from front_of_house_agent.front_of_house_agent import FrontOfHouseWorkAgentType
from front_of_house_agent.sub_agent.change_seat_sub_agent import handle_change_seat_v2
from front_of_house_agent.worker_agents.agent import (
    AgentValidationResponse,
    ValidationIssue,
    ValidationIssueType,
    WorkerAgent,
)
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.database.models.chat_thread import ChatThread
from server.utils.settings import AgentTypes

if TYPE_CHECKING:
    import front_of_house_agent.front_of_house_agent as fha


class ChangeFlightSeatAgentResponse(AgentValidationResponse):
    def __init__(
        self,
        flight_confirmation_id: str,
        flight_trip_id: str,
    ):
        self.flight_confirmation_id = flight_confirmation_id
        self.flight_trip_id = flight_trip_id

    def get_agent_type(self) -> AgentTypes:
        return AgentTypes.CHANGE_SEAT

    def extract_validation_issues(self) -> list[ValidationIssue]:
        validation_issues = []
        if not self.flight_confirmation_id:
            validation_issues.append(
                ValidationIssue(
                    type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                    name="flight_confirmation_id",
                    description="The flight confirmation id",
                )
            )
        if not self.flight_trip_id:
            validation_issues.append(
                ValidationIssue(
                    type=ValidationIssueType.MISSING_REQUIRED_FIELD,
                    name="flight_trip_id",
                    description="The flight trip id",
                )
            )
        return validation_issues


class ChangeFlightSeatAgent(WorkerAgent):
    def __init__(
        self,
        timezone: Optional[str],
        thread: ChatThread,
    ):
        self.timezone = timezone
        self.thread = thread

    async def validate(
        self,
        current_work_type: FrontOfHouseWorkAgentType,
        travel_context: TravelContext,
        user_preferences: ResponseAllPreferences,
        messages: list[BaseMessage],
    ) -> AgentValidationResponse:
        return ChangeFlightSeatAgentResponse(
            flight_confirmation_id=travel_context.latest_flight_confirmation_id or "",
            flight_trip_id=travel_context.latest_flight_trip_id or "",
        )

    async def execute_plan(
        self,
        foh: "fha.FrontOfHouseAgent",
        classified_type_from_foh: FrontOfHouseWorkAgentType,
        messages: list[BaseMessage],
        extra_context: dict[str, Any],
        tool_call_id: str,
    ) -> dict[str, Any]:
        message_buffer_strs = get_message_buffer_as_strings(messages)
        return await handle_change_seat_v2(foh, message_buffer_strs, tool_call_id)

    def get_agent_type(self) -> AgentTypes:
        return AgentTypes.CHANGE_SEAT
