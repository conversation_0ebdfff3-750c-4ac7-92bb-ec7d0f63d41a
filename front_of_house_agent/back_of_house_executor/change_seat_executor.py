import asyncio
from functools import partial
from typing import Any, Callable, Coroutine

from langchain_core.messages import BaseMessage

from baml_client.types import SeatSelectionChangeStep
from flight_agent.flights_helper import Flight<PERSON>aml<PERSON><PERSON>per
from front_of_house_agent.adapter import map_websocket_message
from front_of_house_agent.back_of_house_executor.executor import Agent<PERSON><PERSON><PERSON><PERSON>
from front_of_house_agent.common_models import EnrichedChangeSeatState
from server.database.models.chat_thread import ChatThread
from server.services.trips.bookings import get_bookings_from_airline_confirmation_code
from server.utils.message_constants import SEAT_CHANGE_MESSAGES


class ChangeSeatExecutor(AgentExecutor):
    def __init__(
        self,
        flight_helper: FlightB<PERSON>lH<PERSON>per,
        message_sender: partial[Coroutine[Any, Any, None]],
        thread: ChatThread,
    ):
        super().__init__(message_sender)
        self.flight_helper = flight_helper
        self.message_sender = message_sender
        self.thread = thread

    async def execute(
        self,
        change_seat_state: EnrichedChangeSeatState,
        message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
        # current we only need {"seat_preference": ["window", "aisle"]}
        extral_params: dict[str, Any],
    ):
        if not change_seat_state.flight_confirmation_id:
            if change_seat_state.airline_confirmation_number:
                booking = await get_bookings_from_airline_confirmation_code(
                    change_seat_state.airline_confirmation_number
                )
                change_seat_state.flight_trip_id = booking.get("flight", {}).get("trip_id") or ""
                change_seat_state.flight_confirmation_id = booking.get("flight", {}).get("confirmation_id") or ""
        if change_seat_state.current_step == SeatSelectionChangeStep.START_SEAT_SELECTION:
            task_id = f"{self.thread.id}_{SeatSelectionChangeStep.START_SEAT_SELECTION.name}"
            if task_id in self.task_map and not self.task_map[task_id].done():
                pass
            else:
                asyncio.create_task(
                    self.message_sender(
                        message={
                            "type": "search_update",
                            "text": SEAT_CHANGE_MESSAGES["SEAT_CHANGE_START"],
                            "isBotMessage": True,
                            "expectResponse": False,
                        },
                    )
                )

                async def __seat_change_all_handler():
                    message = await self.flight_helper.seat_selection_for_booked(
                        change_seat_state.flight_confirmation_id,
                        change_seat_state.flight_trip_id,
                        change_seat_state.preferred_seat_types or extral_params.get("seat_preference") or [],
                        change_seat_state.is_seat_number_provide_by_traveler or False,
                        list(
                            map(
                                lambda x: x.model_dump(),
                                change_seat_state.selected_seats_want_add_or_change or [],
                            )
                        ),
                    )
                    await message_persistor([message])
                    return_message = await map_websocket_message(
                        message,
                        None,
                        None,
                    )
                    await self.message_sender(message={**return_message[0]})

                task = self.wrap_task(
                    asyncio.create_task(
                        __seat_change_all_handler(),
                        name=task_id,
                    )
                )
                self.task_map[task_id] = task
        elif change_seat_state.current_step == SeatSelectionChangeStep.SELECT_SEAT_FOR_STOPS:
            pass
        elif change_seat_state.current_step == SeatSelectionChangeStep.ASK_TO_SHOW_AVAILABLE_SEATS:
            task_id = f"{self.thread.id}_{SeatSelectionChangeStep.ASK_TO_SHOW_AVAILABLE_SEATS.name}"
            if task_id in self.task_map and not self.task_map[task_id].done():
                pass
            else:

                async def __get_all_seats_handler():
                    asyncio.create_task(
                        self.message_sender(
                            message={
                                "type": "search_update",
                                "text": SEAT_CHANGE_MESSAGES["SEAT_AVAILABILITY_CHECK"],
                                "isBotMessage": True,
                                "expectResponse": False,
                            }
                        )
                    )
                    message = await self.flight_helper.get_matched_preference_available_seats(
                        preferred_seat=extral_params.get("seat_preference") or [],
                        confirmation_id=change_seat_state.flight_confirmation_id,
                        trip_id=change_seat_state.flight_trip_id,
                        flight_index=change_seat_state.current_flight_index_to_select_seat,
                    )
                    await message_persistor([message])
                    return_message = await map_websocket_message(
                        message,
                        None,
                        None,
                    )
                    await self.message_sender(message={**return_message[0]})

                task = self.wrap_task(
                    asyncio.create_task(
                        __get_all_seats_handler(),
                        name=task_id,
                    )
                )
                self.task_map[task_id] = task
        elif change_seat_state.current_step == SeatSelectionChangeStep.SUBMIT_SEAT_SELECTION:
            task_id = f"{self.thread.id}_{SeatSelectionChangeStep.SUBMIT_SEAT_SELECTION.name}"
            if task_id in self.task_map and not self.task_map[task_id].done():
                pass
            else:

                async def __submit_seat_change_handler():
                    asyncio.create_task(
                        self.message_sender(
                            message={
                                "type": "search_update",
                                "text": SEAT_CHANGE_MESSAGES["SEAT_CHANGE_START"],
                                "isBotMessage": True,
                                "expectResponse": False,
                            }
                        )
                    )
                    message = await self.flight_helper.update_seat(
                        change_seat_state.flight_confirmation_id,
                        list(
                            map(
                                lambda x: x.model_dump(),
                                change_seat_state.selected_seats_want_add_or_change or [],
                            )
                        ),
                    )
                    await message_persistor([message])
                    return_message = await map_websocket_message(
                        message,
                        None,
                        None,
                    )
                    await self.message_sender(message={**return_message[0]})

                task = self.wrap_task(
                    asyncio.create_task(
                        __submit_seat_change_handler(),
                        name=task_id,
                    )
                )
                self.task_map[task_id] = task
