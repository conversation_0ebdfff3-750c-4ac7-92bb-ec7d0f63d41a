import asyncio
from datetime import datetime, timezone
from functools import partial
from typing import Any, Coroutine

from langchain_core.messages import BaseMessage

from baml_client import b
from baml_client.types import OttoCapabilityCategory, ResponseAllPreferences
from front_of_house_agent.common_models import Travel<PERSON>ontext
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.database.models.bookings import Booking
from server.database.models.user import User as UserDB
from server.database.models.user import UserRole
from server.database.models.user_company_travel_policy import UserCompanyTravelPolicy
from server.database.models.user_profile import UserProfile
from server.schemas.authenticate.user import User
from server.services.calendar_api.calendar_provider import CalendarProviderManager
from server.services.user.user_activity import get_user_activity, update_suggested_capability
from server.services.user.user_preferences import (
    get_user_preferences_with_updated_time,
)
from server.services.user_profile.loyalty_programs import get_user_profile_flights_loyalty_programs
from server.utils.logger import logger
from server.utils.settings import settings


class NudgeManager:
    """Manages capability nudge logic and tracks back of house execution completion."""

    def __init__(
        self,
        user: User,
        websocket_send_message: partial[Coroutine[Any, Any, None]],
        user_preferences: ResponseAllPreferences,
        travel_context: TravelContext,
        messages: list[BaseMessage],
        user_profile: UserProfile | None,
    ):
        self.user = user
        self.websocket_send_message = websocket_send_message
        self.user_preferences = user_preferences
        self.travel_context = travel_context
        self.messages = messages
        self.user_profile = user_profile

        # Define critical steps that indicate significant BOH completion
        self.critical_steps = {
            "FLIGHT_BOOKING",
            "HOTEL_BOOKING",
            "FLIGHT_VALIDATION",
            "HOTEL_VALIDATION",
            "FLIGHT_SEARCH",
            "HOTEL_SEARCH",
            "FLIGHT_CHECKOUT",
            "HOTEL_CHECKOUT",
        }

    async def on_step_complete(self, step_name: str):
        """Called when a back of house step completes."""

        logger.info(f"NudgeManager: Triggering nudge after step completion - {step_name}")
        await self._trigger_capability_nudge(step_name)

    async def _trigger_capability_nudge(self, step_name: str):
        try:
            now = datetime.now(timezone.utc)

            user_data = await get_user_activity(str(self.user.id))
            latest_suggested_capability = user_data.get("last_suggested_capabilities") or []
            if user_data and "last_timestamp_of_suggested_capability" in user_data:
                last_timestamp = user_data["last_timestamp_of_suggested_capability"]
                last_datetime = datetime.fromtimestamp(last_timestamp, timezone.utc)

                if (now - last_datetime) < settings.CAPABILITY_SUGGESTION_PERIOD:
                    return

            # Get user preferences
            user_preferences = await get_user_preferences_with_updated_time(self.user.id)
            preferences_json = user_preferences.model_dump_json(
                exclude={
                    "triggered_flight_search_amount",
                    "triggered_hotel_search_amount",
                    "hide_sample_trips",
                    "updated_at",
                }
            )

            has_calendar_access = False
            if self.user_profile is not None:
                calendar_api = CalendarProviderManager(user_profile=self.user_profile)
                has_calendar_access = calendar_api.has_calendar_access()

            user_profile_flights_loyalty_programs = await get_user_profile_flights_loyalty_programs(self.user.id)
            loyalty_programs = (
                user_profile_flights_loyalty_programs.get("loyaltyPrograms") or []
                if user_profile_flights_loyalty_programs
                else []
            )
            has_ffn = bool(loyalty_programs and len(loyalty_programs) > 0)

            company_admin = (
                await UserDB.from_organization_id_and_role(self.user.organization_id, UserRole.company_admin)
                if self.user.organization_id
                else None
            )
            policy_user_id = company_admin.id if company_admin else self.user.id
            company_policy = await UserCompanyTravelPolicy.from_user_id(policy_user_id)

            has_company_travel_policy = (
                company_policy is not None
                and company_policy.parsed_travel_policy is not None
                and (
                    company_policy.parsed_travel_policy.get("flight_policy") is not None
                    or company_policy.parsed_travel_policy.get("hotel_policy") is not None
                )
            )

            booked_cnt = await Booking.get_user_booking_counts(user_id=self.user.id)
            capability_suggestion = await b.SuggestCapability(
                chat_history=get_message_buffer_as_strings(self.messages),
                preferences=preferences_json,
                self_intro=settings.OTTO_SELF_INTRO,
                recent_suggested_capabilities=latest_suggested_capability,
                connected_to_calendar=has_calendar_access,
                current_action=step_name,
                flight_search_count=user_preferences.triggered_flight_search_amount,
                hotel_search_count=user_preferences.triggered_hotel_search_amount,
                flight_booked_count=booked_cnt.flight_cnt,
                hotel_booked_count=booked_cnt.hotel_cnt,
                is_international_flight=self.travel_context.flight_search_core_criteria.is_international_flight_trip
                or False,
                has_ffn=has_ffn,
                has_company_travel_policy=has_company_travel_policy,
                flight_cancel_attempted_count=user_data.get("flight_cancel_attempted_count", 0),
                flight_change_attempted_count=user_data.get("flight_change_attempted_count", 0),
                hotel_cancel_attempted_count=user_data.get("hotel_cancel_attempted_count", 0),
                baml_options={"collector": logger.collector},
            )
            logger.log_baml()

            if (
                capability_suggestion.suggested_capability
                and capability_suggestion.suggested_category
                and capability_suggestion.suggested_category != OttoCapabilityCategory.OTHERS
            ):
                logger.info(f"Suggested unused capability: {capability_suggestion.suggested_capability}")

                await update_suggested_capability(
                    user_id=str(self.user.id), suggested_capability=capability_suggestion.suggested_category
                )

                # Send nudge with 30-second delay only for calendar integration
                if capability_suggestion.suggested_category == OttoCapabilityCategory.CALENDAR_INTEGRATION:
                    await asyncio.sleep(30)

                await self.websocket_send_message(
                    message={
                        "type": "suggested_capability",
                        "capability_type": capability_suggestion.suggested_category.name,
                        "isBotMessage": True,
                    }
                )

        except Exception as e:
            logger.error(f"Error when suggesting capability: {e}")
