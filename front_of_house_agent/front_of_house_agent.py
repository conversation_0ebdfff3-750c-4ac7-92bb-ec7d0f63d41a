import asyncio
import json
import traceback
import uuid
from datetime import datetime
from functools import partial
from typing import Any, Coroutine

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    HumanMessage,
    message_to_dict,
)

import front_of_house_agent.preferences_agents as preferences_agents
from agent.agent import Agent, StopResponse
from baml_client import b
from baml_client.type_builder import TypeBuilder
from baml_client.types import (
    ChangeSeatState,
    FlightSearchAdditionalCriteria,
    FlightSearchCoreCriteria,
    FlightStatusCheckResponse,
    FrontOfHouseClassifierResponse,
    FrontOfHouseSupervisorResponse,
    FrontOfHouseWorkAgentType,
    FrontOfHouseWorkType,
    HotelSearchAdditionalCriteria,
    HotelSearchCoreCriteria,
    HotelSelectResult,
    OtherConversationStateSchema,
    ResponseAllPreferences,
    SeatSelectionForFlight,
)
from flight_agent.flights_helper import FlightBamlHelper
from front_of_house_agent.adapter import map_websocket_message
from front_of_house_agent.back_of_house_executor.change_seat_executor import ChangeSeatExecutor
from front_of_house_agent.back_of_house_executor.exchange_flight_executor import ExchangeFlightExecutor
from front_of_house_agent.back_of_house_executor.flight_and_hotel_executor import TripPlanExecutor
from front_of_house_agent.back_of_house_executor.trip_status_executor import handle_flight_status
from front_of_house_agent.cancellation_agent import CancellationAgent
from front_of_house_agent.common_models import (
    EnrichedExchangeFlightState,
    EnrichedFlightSelectResult,
    FlightOption,
    FlightValidationResult,
    HotelValidationResult,
    TravelContext,
)
from front_of_house_agent.nudge_manager import NudgeManager
from front_of_house_agent.sample_trip import get_sample_trips
from front_of_house_agent.sub_agent.change_seat_sub_agent import handle_change_seat
from front_of_house_agent.sub_agent.entry_requirment_sub_agent import handle_entry_requirment
from front_of_house_agent.sub_agent.exchange_flight_sub_agent import handle_exchange_flight
from front_of_house_agent.sub_agent.profile_sub_agent import handle_profile
from front_of_house_agent.sub_agent.trip_pausing_sub_agent import handle_trip_pausing
from front_of_house_agent.sub_agent.trip_planner_sub_agent import (
    handle_flight_planner,
    handle_hotel_planner,
)
from front_of_house_agent.trip_coordinator.trip_coordinator_agent import TripCoordinatorAgent
from front_of_house_agent.worker_agents.agent import ValidationIssue
from front_of_house_agent.worker_agents.change_flight_seat import ChangeFlightSeatAgent
from front_of_house_agent.worker_agents.exchange_flight import ExchangeFlightAgent
from front_of_house_agent.worker_agents.flight import FlightAgent
from front_of_house_agent.worker_agents.hotel import HotelAgent
from guardrail.guardrail import Guardrail
from hotel_agent.hotels_helper import HotelsHelper
from in_trip.in_trip import InTripAgent
from llm_utils.llm_utils import get_message_buffer_as_strings
from message.model import MessageType
from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.database.models.user_profile import UserProfile
from server.schemas.authenticate.user import User
from server.services.feature_flags.feature_flag import FeatureFlags, is_feature_flag_enabled
from server.services.memory.trips.retriever import TripMemoryRetriever
from server.services.user.user_activity import UserActivityType, update_user_activity
from server.services.user.user_preferences import (
    get_user_preferences,
    set_hide_sample_trips,
)
from server.services.user.user_session_history import get_thread_current_bookings
from server.utils.logger import logger
from server.utils.message_constants import TRIP_MESSAGES
from server.utils.mongo_connector import trip_context_v2_collection
from server.utils.settings import AgentTypes, settings
from virtual_travel_agent.helpers import get_current_date_string
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory

opening_string = "Hi{user_name}. Let's get started—where are you headed and what are your travel dates?"

opening_string_trying_mode = """
👋 Hi {user_name}

**Ready, Set, Otto.**  
*The more you ask, the better I get.*

Let's try planning a trip. Pick one of these examples or just type your own.
"""

in_trip_string = "Hello! I see you're currently on this trip. Can I help you with something?"


class FrontOfHouseAgent(Agent):
    def __init__(
        self,
        thread: ChatThread,
        websocket_send_message: partial[Coroutine[Any, Any, None]],
        user_preferences: ResponseAllPreferences | None,
        user: User,
        user_profile: UserProfile | None,
        timezone: str | None = None,
        is_mobile: bool = False,
        trying_mode_enabled: bool = False,
        enable_trip_planning: bool = False,
        email_mode: bool = False,
        custom_opening_message: str | None = None,
    ):
        super().__init__(
            user=user,
            history=PostgresChatMessageHistory(thread_id=thread.id),
            mem_loader=TripMemoryRetriever(user_id=str(user.id), thread_id=str(thread.id)),
            websocket_send_message=websocket_send_message,
        )
        self.email_mode = email_mode
        self.thread = thread
        self.user_profile = user_profile
        self.hotel_helper = HotelsHelper(user=user, timezone=timezone, thread_id=thread.id)
        self.guardrail = Guardrail(user_id=user.id)

        self.trying_mode_enabled = trying_mode_enabled
        self.enable_trip_planning = enable_trip_planning
        self.custom_opening_message = custom_opening_message

        self.flight_helper = FlightBamlHelper(
            user=user, timezone=timezone, thread=thread, existing_user_preferences=None
        )

        self.exchange_flight_executor = ExchangeFlightExecutor(
            user=user, flight_helper=self.flight_helper, message_sender=websocket_send_message, thread=thread
        )

        self.change_seat_executor = ChangeSeatExecutor(
            flight_helper=self.flight_helper, message_sender=websocket_send_message, thread=thread
        )

        self.trip_plan_executor = TripPlanExecutor(
            flight_helper=self.flight_helper,
            message_sender=websocket_send_message,
            thread=thread,
            hotel_helper=self.hotel_helper,
            user=user,
            user_profile=user_profile,
            timezone=timezone,
        )

        self.hotel_worker_agent = HotelAgent(timezone=timezone)
        self.flight_worker_agent = FlightAgent(timezone=timezone, thread=thread)
        self.exchange_flight_worker_agent = ExchangeFlightAgent(timezone=timezone, thread=thread)
        self.change_flight_seat_worker_agent = ChangeFlightSeatAgent(timezone=timezone, thread=thread)

        self.executors.extend(
            [
                self.exchange_flight_executor,
                self.change_seat_executor,
                self.trip_plan_executor,
            ]
        )

        self.timezone = timezone
        # use the user_preferences if possible as the overarching travel preferences
        initial_preferences = (
            ResponseAllPreferences(**user_preferences.model_dump(exclude_none=True, exclude={"updated_at"}))
            if user_preferences is not None
            else ResponseAllPreferences(
                preferred_airline_brands=[],
                preferred_seats=[],
                preferred_cabin=["economy"],
                preferred_hotel_brands=[],
                preferred_travel_misc=[],
            )
        )
        self.user_preferences = initial_preferences
        self.travel_context: TravelContext = TravelContext(is_mobile=is_mobile)

        # Initialize nudge manager
        self.nudge_manager = NudgeManager(
            user=user,
            websocket_send_message=websocket_send_message,
            user_preferences=initial_preferences,
            travel_context=self.travel_context,
            messages=self.messages,
            user_profile=user_profile,
        )

        # Register nudge manager callback with all executors
        for executor in self.executors:
            executor.add_step_callback(self.nudge_manager.on_step_complete)

        self.cancel_agent = CancellationAgent(
            self.user,
            self.travel_context,
            self.messages,
            self.history,
            self.thread,
            self.websocket_send_message,
            self.timezone,
            self.email_mode,
        )

        # exchange flight state
        self.exchange_flight_state: EnrichedExchangeFlightState = EnrichedExchangeFlightState(
            agent_response="", flight_confirmation_id="", flight_trip_id=""
        )

        # change seat state
        self.change_seat_state: ChangeSeatState = ChangeSeatState(agent_response="")

        self.trip_planning_agent = TripCoordinatorAgent(
            user=self.user,
            user_profile=self.user_profile,
            thread=self.thread,
            websocket_send_message=self.websocket_send_message,
            timezone=self.timezone,
            flight_helper=self.flight_helper,
            hotel_helper=self.hotel_helper,
            user_preferences=self.user_preferences,
            travel_context=self.travel_context,
            messages=self.messages,
        )

    async def run(
        self, message=None, message_type="text", extra_payload=None
    ) -> list[dict[str, str | bool | list[dict[str, Any]] | None]]:
        await self.reinit_context()
        to_send: list[dict[str, str | bool | list[dict[str, Any]] | None]] = []
        if self.email_mode:
            await self.get_history_messages(
                lambda message: map_websocket_message(
                    message,
                    self.travel_context.hotel_search_core_criteria.check_in_date,
                    self.travel_context.hotel_search_core_criteria.check_out_date,
                )
            )

        # Thread init
        if message is None and message_type == "text":
            to_send = await self.get_history_messages(
                lambda message: map_websocket_message(
                    message,
                    self.travel_context.hotel_search_core_criteria.check_in_date,
                    self.travel_context.hotel_search_core_criteria.check_out_date,
                )
            )

            if len(self.messages) == 0:
                # The messages list is empty, send the opening message
                await self.add_timestamp_message(send_ws_message=True)

                if self.custom_opening_message:
                    import html

                    agent_response = AIMessage(
                        content=html.escape(self.custom_opening_message),
                        additional_kwargs={
                            "agent_classification": "FOH",
                        },
                    )
                elif self.trying_mode_enabled:
                    agent_response = AIMessage(
                        content=opening_string_trying_mode.format(
                            user_name=" " + self.user.name if self.user.name else ""
                        ).replace("\n\n", "&NewLine;&NewLine;"),
                        additional_kwargs={
                            "agent_classification": "FOH",
                            "sample_trips": (await get_sample_trips(self.user.id)),
                        },
                    )
                else:
                    agent_response = AIMessage(
                        content=opening_string.format(user_name=" " + self.user.name if self.user.name else ""),
                        additional_kwargs={
                            "agent_classification": "FOH",
                        },
                    )

                self.add_history_message(agent_response)
                self.messages.append(agent_response)

                to_send += await map_websocket_message(
                    agent_response,
                    self.travel_context.hotel_search_core_criteria.check_in_date,
                    self.travel_context.hotel_search_core_criteria.check_out_date,
                )
                return to_send
            else:
                in_trip_enabled = await is_feature_flag_enabled(self.user.id, FeatureFlags.ENABLE_IN_TRIP)
                if not in_trip_enabled:
                    to_send[-1]["expectResponse"] = True
                    return to_send

                last_message_is_in_trip_opening = False
                if self.messages and isinstance(self.messages[-1], AIMessage):
                    last_message_is_in_trip_opening = self.messages[-1].additional_kwargs.get(
                        "is_in_trip_openning", False
                    )

                if not last_message_is_in_trip_opening:
                    in_trip_bookings = await get_thread_current_bookings(self.thread.id)
                    if not in_trip_bookings:
                        to_send[-1]["expectResponse"] = True
                        return to_send

                    _, message = await InTripAgent.get_in_trip_status_and_message(in_trip_bookings[0], self.timezone)
                    if in_trip_bookings:
                        agent_response = AIMessage(
                            content=message or in_trip_string,
                            additional_kwargs={
                                "agent_classification": "FOH",
                                "is_in_trip_openning": True,
                            },
                        )
                        to_send += await map_websocket_message(agent_response)
                        self.add_history_message(agent_response)
                        self.messages.append(agent_response)
                to_send[-1]["expectResponse"] = True
                return to_send

        elif message_type == "agent_resume":
            agent_response = self.messages[-1]
        else:
            message_str = ", ".join(message) if isinstance(message, list) else str(message)
            if message_type == "update":
                agent_response = HumanMessage(
                    content=message_str,
                    additional_kwargs={"is_card_update": True},
                )

            elif message_type == "silent_prompt":
                if extra_payload:
                    agent_response = HumanMessage(content=message_str, additional_kwargs=extra_payload)
                else:
                    agent_response = HumanMessage(content=message_str)

                agent_response.additional_kwargs["message_type"] = message_type
            else:
                agent_response = HumanMessage(content=message_str)

            await self.persist_messages([agent_response])

        try:
            is_capable, guardrail_message = await self.guardrail.run(
                messages=self.messages, message_type=message_type, enable_trip_planning=self.enable_trip_planning
            )
            # Skip the rest of the logic if the guardrail agent returns a message
            if not is_capable and guardrail_message is not None:
                await self.persist_messages([guardrail_message])
                to_send += await map_websocket_message(
                    guardrail_message,
                    None,
                    None,
                )
                asyncio.create_task(self.reset_sample_trip_state())
                return to_send

            extra = extra_payload or {}
            if self.enable_trip_planning:
                to_send.extend(await self.front_of_house_runnable_function_with_trip_plan(extra=extra))
            else:
                response_list = None
                single_chat_agent_enabled = await is_feature_flag_enabled(
                    self.user.id, FeatureFlags.ENABLE_SINGLE_CHAT_AGENT
                )
                if single_chat_agent_enabled:
                    response_list = await self.front_of_house_runnable_function_v2(extra=extra)
                else:
                    response_list = await self.front_of_house_runnable_function(extra=extra)

                assert response_list is not None
                asyncio.create_task(self.post_processing(extra))

                for response in response_list:
                    if not response:
                        continue
                    for message in response.get("messages", []):
                        self.messages.append(message)
                        self.add_history_message(message)
                        to_send += await map_websocket_message(
                            message,
                            self.travel_context.hotel_search_core_criteria.check_in_date,
                            self.travel_context.hotel_search_core_criteria.check_out_date,
                        )

            return to_send
        except asyncio.exceptions.CancelledError as e:
            raise e
        except BaseException as e:
            logger.error(f"Error running agent: {e}")
            logger.error(traceback.format_exc())

            ask_to_retry_msg = "Apologies, something didn't go as planned. Could you try asking again with a bit more detail or perhaps in a different way?"
            to_send.append(
                {
                    "status": "error",
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP[AgentTypes.ERROR],
                    "reason": (
                        "\n".join(traceback.format_exc().split("\n") + [ask_to_retry_msg])
                        if not settings.is_live
                        else ask_to_retry_msg
                    ),
                }
            )
            return to_send

    async def _refresh_user_preferences(self):
        self.user_preferences = await get_user_preferences(self.user.id)

    async def front_of_house_runnable_function_with_trip_plan(self, extra: dict[str, Any]):
        return await self.trip_planning_agent.run()

    def _format_validation_issues(self, validation_issues: dict[AgentTypes, list[ValidationIssue]]) -> tuple[str, str]:
        """Format validation_issues into a readable string for the LLM prompt."""
        if not validation_issues:
            return "", ""

        formatted_sections = []

        missing_information_str = ""
        operation_issues_str = ""
        for agent_type, issues in validation_issues.items():
            if not issues:
                continue

            # Separate issues by type
            missing_fields = [issue for issue in issues if issue.type.value == "missing_required_field"]
            operation_issues = [issue for issue in issues if issue.type.value == "operation_not_feasible"]

            # Format missing fields
            if missing_fields:
                section_title = f"Missing information for {agent_type.value}:"
                field_descriptions = []
                for issue in missing_fields:
                    field_descriptions.append(f"- {issue.name}: {issue.description}")
                formatted_sections.append(f"{section_title}\n" + "\n".join(field_descriptions))
                missing_information_str = "\n\n".join(formatted_sections)

            # Format operation issues
            if operation_issues:
                section_title = f"Operation not feasible for {agent_type.value}:"
                operation_descriptions = []
                for issue in operation_issues:
                    operation_descriptions.append(f"- {issue.name}: {issue.description}")
                formatted_sections.append(f"{section_title}\n" + "\n".join(operation_descriptions))
                operation_issues_str = "\n\n".join(formatted_sections)

        return missing_information_str, operation_issues_str

    async def _classify_work_types(self, message_buffer_strs: list[str]) -> list[FrontOfHouseWorkAgentType]:
        """
        Classify the work types and validate required fields for each work type.

        Returns:
            list: (classifier_response, missed_objects)
        """
        classifier_response: FrontOfHouseClassifierResponse = await b.FrontOfHouseClassifier(
            messages=message_buffer_strs,
            current_date=get_current_date_string(self.timezone),
            travel_context=self.travel_context.model_dump_json(exclude_none=True),
            previous_destination=self.travel_context.flight_search_core_criteria.arrival_airport_code,
            previous_trip_dates=f"{self.travel_context.flight_search_core_criteria.outbound_date}-{self.travel_context.flight_search_core_criteria.return_date}",
            has_concluded_trip=bool(
                self.travel_context.latest_flight_confirmation_id or self.travel_context.hotel_order_id
            ),
            baml_options={
                "collector": logger.collector,
            },
        )
        logger.log_baml()
        return [
            FrontOfHouseWorkAgentType(work_type) if isinstance(work_type, str) else work_type
            for work_type in classifier_response.work_types
        ]

    async def _extract_validation_issues(
        self, work_types: list[FrontOfHouseWorkAgentType], messages: list[BaseMessage]
    ) -> dict[AgentTypes, list[ValidationIssue]]:
        """
        Check if the work types are ready for task and identify any validation issues.

        Returns:
            dict: validation_issues_by_agent
        """
        validation_issues_by_agent: dict[AgentTypes, list[ValidationIssue]] = {}
        for work_type in work_types:
            match work_type:
                case FrontOfHouseWorkAgentType.HotelSearch | FrontOfHouseWorkAgentType.HotelConfirmation:
                    hotel_agent_response = await self.hotel_worker_agent.validate(
                        work_type,
                        self.travel_context,
                        self.user_preferences,
                        messages,
                    )
                    validation_issues = hotel_agent_response.extract_validation_issues()
                    if validation_issues:
                        validation_issues_by_agent[self.hotel_worker_agent.get_agent_type()] = validation_issues
                case FrontOfHouseWorkAgentType.FlightSearch | FrontOfHouseWorkAgentType.FlightConfirmation:
                    flight_agent_response = await self.flight_worker_agent.validate(
                        work_type,
                        self.travel_context,
                        self.user_preferences,
                        messages,
                    )
                    validation_issues = flight_agent_response.extract_validation_issues()
                    if validation_issues:
                        validation_issues_by_agent[self.flight_worker_agent.get_agent_type()] = validation_issues
                case FrontOfHouseWorkAgentType.ChangeFlight:
                    change_flight_agent_response = await self.exchange_flight_worker_agent.validate(
                        work_type,
                        self.travel_context,
                        self.user_preferences,
                        messages,
                    )
                    validation_issues = change_flight_agent_response.extract_validation_issues()
                    if validation_issues:
                        validation_issues_by_agent[self.exchange_flight_worker_agent.get_agent_type()] = (
                            validation_issues
                        )
                case FrontOfHouseWorkAgentType.ChangeFlightSeat:
                    change_flight_seat_agent_response = await self.change_flight_seat_worker_agent.validate(
                        work_type,
                        self.travel_context,
                        self.user_preferences,
                        messages,
                    )
                    validation_issues = change_flight_seat_agent_response.extract_validation_issues()
                    if validation_issues:
                        validation_issues_by_agent[self.change_flight_seat_worker_agent.get_agent_type()] = (
                            validation_issues
                        )

        return validation_issues_by_agent

    async def front_of_house_runnable_function(self, extra: dict[str, Any]):
        messages = self.messages
        message_buffer_strs = get_message_buffer_as_strings(messages)

        flight_search_core_criteria_str = self.travel_context.flight_search_core_criteria.model_dump_json(
            exclude_none=True
        )
        flight_search_additional_criteria_str = self.travel_context.flight_search_additional_criteria.model_dump_json(
            exclude_none=True
        )

        flight_select_result_str = self.travel_context.flight_select_result.model_dump_json()

        saved_trip_enabled = await is_feature_flag_enabled(self.user.id, FeatureFlags.ENABLE_SAVED_TRIP)

        is_new_trip = bool(self.thread.title in [settings.NEW_TRIP_TITLE, settings.GETTING_STARTED_TITLE])

        tb = TypeBuilder()
        if saved_trip_enabled:
            tb.FrontOfHouseWorkType.add_value("TripPausing").description(
                "When the user indicates they want to pause planning and save their progress for later. Examples: 'Let me think about it', 'Maybe later', 'Not ready to book', 'Save this for later'. Only set to this if there are selected flights or hotels that can be saved."
            )
        if not is_new_trip:
            tb.FrontOfHouseWorkType.add_value("CreateNewTrip").description(
                """Determine if the user has shifted to planning a completely new trip, requiring a new conversation thread. This should only be triggered if the topic has *drastically* changed from the previous conversation.
                A drastic change is defined as **any one** of the following:
                1. **Explicit Intent**:
                The user clearly states they want to start over or begin a different trip.
                Examples: “Let’s start over,” “Book me a new trip,” “This is for a different trip.”
                2. **Location Shift**:
                - The new destination city is different, with no reasonable continuation from the prior trip (e.g., not part of a multi-leg itinerary).
                - The new origin city is not the previous destination, and it doesn’t align with a likely round-trip continuation.
                - Note: Airport changes within the same metro area (e.g., JFK to EWR) **do not** count as a new trip.
                3. **Time Shift**:
                - The requested travel date changes by **more than 10 days** from the previous trip.
                - OR, the prior trip conversation has been concluded (i.e., trip was booked or canceled).
                This classification helps separate unrelated trip planning into new threads."""
            )

        supervisor_response: FrontOfHouseSupervisorResponse = await b.FrontOfHouseSupervisorDoConverse(
            messages=message_buffer_strs,
            current_date=get_current_date_string(self.timezone),
            travel_context=self.travel_context.model_dump_json(exclude_none=True),
            previous_destination=self.travel_context.flight_search_core_criteria.arrival_airport_code,
            previous_trip_dates=f"{self.travel_context.flight_search_core_criteria.outbound_date}-{self.travel_context.flight_search_core_criteria.return_date}",
            has_concluded_trip=bool(
                self.travel_context.latest_flight_confirmation_id or self.travel_context.hotel_order_id
            ),
            is_new_trip=is_new_trip,
            baml_options={
                "collector": logger.collector,
                "tb": tb,
            },
        )
        logger.log_baml()

        responses_list = []
        for work_type in supervisor_response.work_types:
            response = None
            match work_type:
                case FrontOfHouseWorkType.Inquiry:
                    travel_context = self.travel_context.to_dict()
                    travel_context.update(self.user_preferences.model_dump(exclude_none=True))
                    inquiry_final_response: (
                        OtherConversationStateSchema | FlightStatusCheckResponse | StopResponse
                    ) = await self.process_streaming(
                        partial(
                            b.stream.ConverseOtherTopics,
                            travel_context=json.dumps(travel_context, default=str),
                            trip_memories=[],
                            messages=message_buffer_strs,
                            current_date=get_current_date_string(self.timezone),
                            self_intro=settings.OTTO_SELF_INTRO,
                            convo_style=settings.OTTO_CONVO_STYLE,
                            user_name=self.user.name,
                        ),
                        lambda x: x.agent_response,
                        lambda x: x.agent_response,
                    )
                    if isinstance(inquiry_final_response, StopResponse):
                        response = {
                            "messages": [
                                AIMessage(
                                    content=inquiry_final_response.last_text or "",
                                    additional_kwargs={"is_stopped": True},
                                )
                            ],
                            "current_topic": "Other",
                            "model": None,
                        }

                    else:
                        final_message = AIMessage(
                            content="",
                            name=inquiry_final_response.__class__.__name__,
                            additional_kwargs={
                                "agent_classification": AgentTypes.FOH,
                                "function_call": {
                                    "name": inquiry_final_response.__class__.__name__,
                                    "arguments": inquiry_final_response.model_dump_json(),
                                },
                            },
                        )

                        response = {
                            "messages": [final_message],
                            "current_topic": "Other",
                            "model": inquiry_final_response,
                        }
                        if isinstance(inquiry_final_response, FlightStatusCheckResponse):
                            asyncio.create_task(
                                handle_flight_status(
                                    self,
                                    inquiry_final_response,
                                )
                            )

                case FrontOfHouseWorkType.FlightPlanning:
                    await self._refresh_user_preferences()
                    response = await handle_flight_planner(
                        self,
                        messages,
                        message_buffer_strs,
                        extra,
                        flight_search_core_criteria_str,
                        flight_search_additional_criteria_str,
                        flight_select_result_str,
                        partial(
                            preferences_agents.prompt_new_preferences,
                            self.user,
                            self.messages,
                            self.websocket_send_message,
                            self.persist_messages,
                        ),
                        streaming=False if self.email_mode else True,
                    )

                case FrontOfHouseWorkType.HotelPlanning:
                    await self._refresh_user_preferences()
                    response = await handle_hotel_planner(
                        self,
                        messages,
                        message_buffer_strs,
                        self.travel_context,
                        extra,
                        partial(
                            preferences_agents.prompt_new_preferences,
                            self.user,
                            self.messages,
                            self.websocket_send_message,
                            self.persist_messages,
                        ),
                        streaming=False if self.email_mode else True,
                    )
                case FrontOfHouseWorkType.ChangeFlight:
                    confirmation_code = self.travel_context.latest_flight_airline_confirmation_number or ""
                    asyncio.create_task(
                        update_user_activity(
                            str(self.user.id),
                            UserActivityType.FLIGHT_CHANGE_ATTEMPTED,
                            data={"confirmation_code": confirmation_code},
                        )
                    )
                    response = await handle_exchange_flight(self, message_buffer_strs)
                case FrontOfHouseWorkType.ChangeFlightSeat:
                    response = await handle_change_seat(self, message_buffer_strs)
                case FrontOfHouseWorkType.Cancellation:
                    response = await self.cancel_agent.handle_cancellation_plan()
                case FrontOfHouseWorkType.ProfileHandler:
                    response = await handle_profile(self, message_buffer_strs)
                case FrontOfHouseWorkType.UpdatePreferences:
                    response = await preferences_agents.update_user_preferences(
                        self.user, self.messages, self.user_preferences, self.history
                    )
                case FrontOfHouseWorkType.InternationalInfoCheck:
                    response = await handle_entry_requirment(self, message_buffer_strs)
                case "CreateNewTrip":
                    new_thread = ChatThread(
                        users_id=self.user.id,
                        title="New Trip",
                        extra={"createdFromTrip": self.thread.id},
                    )
                    await ChatThread.new_chat_thread(new_thread)

                    opening_message = AIMessage(
                        content=TRIP_MESSAGES["FORK_NEW_TRIP_OPENING"],
                    )
                    new_checkpoint = Checkpoint(
                        thread_id=new_thread.id,
                        data=message_to_dict(opening_message),
                        input_tokens=None,
                        output_tokens=None,
                    )
                    await Checkpoint.new_checkpoint(new_checkpoint)

                    asyncio.create_task(
                        self.websocket_send_message(
                            message={
                                "type": "trip_update",
                            }
                        )
                    )

                    # find last human message in messages
                    last_human_message: HumanMessage | None = next(
                        (msg for msg in reversed(self.messages) if isinstance(msg, HumanMessage)),
                        None,
                    )
                    asyncio.create_task(
                        self.websocket_send_message(
                            message={
                                "type": "redirect_to_trip",
                                "text": TRIP_MESSAGES["REDIRECT_TO_TRIP"],
                                "isBotMessage": True,
                                "expectResponse": False,
                                "newTripId": new_thread.id,
                                "lastHumanMessage": last_human_message.content if last_human_message else None,
                            }
                        )
                    )

                    response = {
                        "messages": [
                            AIMessage(
                                content=TRIP_MESSAGES["FORK_NEW_TRIP_RESPONSE"],
                                additional_kwargs={
                                    "message_type": MessageType.FORK_NEW_TRIP.value,
                                    "new_trip_id": new_thread.id,
                                },
                            )
                        ],
                    }
                case "TripPausing":
                    response = await handle_trip_pausing(self, message_buffer_strs)
                case _:
                    logger.error(f"Unknown work type: {work_type}")
                    response = {"agent_response": "I'm not sure how to help with that. Could you please clarify?"}

            if response:
                responses_list.append(response)

        return responses_list

    async def front_of_house_runnable_function_v2(self, extra: dict[str, Any]):
        messages = self.messages
        message_buffer_strs = get_message_buffer_as_strings(messages)

        # Classify and validation
        work_types = await self._classify_work_types(message_buffer_strs)
        validation_issues = await self._extract_validation_issues(work_types, messages)

        all_task_responses: list[Any] = []
        sub_task: asyncio.Task[dict[str, dict[str, Any]]] | None = None
        if not validation_issues:

            async def execute_tasks() -> dict[str, dict[str, Any]]:
                if (
                    FrontOfHouseWorkAgentType.HotelSearch in work_types
                    or FrontOfHouseWorkAgentType.HotelConfirmation in work_types
                    or FrontOfHouseWorkAgentType.FlightSearch in work_types
                    or FrontOfHouseWorkAgentType.FlightConfirmation in work_types
                ):
                    await self._refresh_user_preferences()

                task_responses: dict[str, dict[str, Any]] = {}
                tool_call_id = str(uuid.uuid4())

                for work_type in work_types:
                    match work_type:
                        case FrontOfHouseWorkAgentType.HotelSearch | FrontOfHouseWorkAgentType.HotelConfirmation:
                            response = await self.hotel_worker_agent.execute_plan(
                                self,
                                work_type,
                                messages,
                                partial(
                                    preferences_agents.prompt_new_preferences,
                                    self.user,
                                    self.messages,
                                    self.websocket_send_message,
                                    self.persist_messages,
                                ),
                                tool_call_id,
                            )
                            task_responses[work_type.value] = response

                        case FrontOfHouseWorkAgentType.FlightSearch | FrontOfHouseWorkAgentType.FlightConfirmation:
                            response = await self.flight_worker_agent.execute_plan(
                                self,
                                work_type,
                                messages,
                                extra,
                                tool_call_id,
                            )
                            task_responses[work_type.value] = response
                        case FrontOfHouseWorkAgentType.ChangeFlight:
                            response = await self.exchange_flight_worker_agent.execute_plan(
                                self,
                                work_type,
                                messages,
                                extra,
                                tool_call_id,
                            )
                            task_responses[work_type.value] = response
                        case FrontOfHouseWorkAgentType.ChangeFlightSeat:
                            response = await self.change_flight_seat_worker_agent.execute_plan(
                                self,
                                work_type,
                                messages,
                                extra,
                                tool_call_id,
                            )
                            task_responses[work_type.value] = response
                        case _:
                            logger.error(f"Unknown work type: {work_type}")

                return task_responses

            sub_task = asyncio.create_task(execute_tasks())

        sub_task_responses: dict[str, dict[str, Any]] = {}
        if sub_task:
            sub_task_responses = await sub_task

        # Format validation_issues into a readable string
        missing_information_str, operation_issues_str = self._format_validation_issues(validation_issues)

        sub_task_responses_str = []
        for work_type in sub_task_responses:
            model = sub_task_responses[work_type].get("model")
            if model is not None:
                sub_task_responses_str.append(f"{work_type}: {model.model_dump_json()}")
            else:
                sub_task_responses_str.append(f"{work_type}: ")

        travel_context = self.travel_context.to_dict()
        travel_context.update(self.user_preferences.model_dump(exclude_none=True))
        final_response: (
            OtherConversationStateSchema | FlightStatusCheckResponse | StopResponse
        ) = await self.process_streaming(
            partial(
                b.stream.FrontOfHouseDoConverV2,
                travel_context=json.dumps(travel_context, default=str),
                messages=message_buffer_strs,
                current_date=get_current_date_string(self.timezone),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                classifier_response=[work_type.value for work_type in work_types],
                user_name=self.user.name,
                missing_objects=missing_information_str if missing_information_str else None,
                operation_issues=operation_issues_str if operation_issues_str else None,
                has_validation_issues=bool(validation_issues) or bool(operation_issues_str),
                sub_task_responses=sub_task_responses_str,
            ),
            lambda x: x.agent_response,
            lambda x: x.agent_response,
        )
        if isinstance(final_response, StopResponse):
            response = {
                "messages": [
                    AIMessage(
                        content=final_response.last_text or "",
                        additional_kwargs={"is_stopped": True},
                    )
                ],
                "current_topic": "Other",
                "model": None,
            }

        else:
            final_message = AIMessage(
                content="",
                name=final_response.__class__.__name__,
                additional_kwargs={
                    "agent_classification": AgentTypes.FOH,
                    "function_call": {
                        "name": final_response.__class__.__name__,
                        "arguments": final_response.model_dump_json(),
                    },
                },
            )

            response = {
                "messages": [final_message],
                "current_topic": "Other",
                "model": final_response,
            }
            if isinstance(final_response, FlightStatusCheckResponse):
                asyncio.create_task(
                    handle_flight_status(
                        self,
                        final_response,
                    )
                )

        all_task_responses.append(response)
        all_task_responses.extend(sub_task_responses.values())

        return all_task_responses

    async def reinit_context(self):
        trip_context = await trip_context_v2_collection.find_one({"thread_id": self.thread.id})

        if trip_context:
            if trip_context.get("flight_search_core_criteria"):
                self.travel_context.flight_search_core_criteria = FlightSearchCoreCriteria(
                    **trip_context["flight_search_core_criteria"]
                )
            if trip_context.get("flight_search_additional_criteria"):
                self.travel_context.flight_search_additional_criteria = FlightSearchAdditionalCriteria(
                    **trip_context["flight_search_additional_criteria"]
                )
            if trip_context.get("hotel_search_core_criteria"):
                self.travel_context.hotel_search_core_criteria = HotelSearchCoreCriteria(
                    **trip_context["hotel_search_core_criteria"]
                )
            if trip_context.get("hotel_search_additional_criteria"):
                self.travel_context.hotel_search_additional_criteria = HotelSearchAdditionalCriteria(
                    **trip_context["hotel_search_additional_criteria"]
                )
            if trip_context.get("flight_select_result"):
                value_in_mongo = trip_context["flight_select_result"]
                # For backward compatibility: old is dict[str, str] and new is dict[str, object]
                if isinstance(value_in_mongo, dict):
                    if value_in_mongo.get("seat_selection"):
                        seat_selection_in_mongo = value_in_mongo["seat_selection"]
                        if isinstance(next(iter(seat_selection_in_mongo.values())), str):
                            value_in_mongo["seat_selection"] = {
                                key: {
                                    "seat_number": value,
                                    "price": 0,
                                    "airline_code": "",
                                    "flight_number": "",
                                }
                                for key, value in seat_selection_in_mongo.items()
                            }
                        if isinstance(next(iter(seat_selection_in_mongo.values())), list):
                            value_in_mongo["seat_selection"] = {
                                key: {
                                    "seat_number": value[0],
                                    "price": value[1],
                                    "airline_code": "",
                                    "flight_number": "",
                                }
                                for key, value in seat_selection_in_mongo.items()
                            }
                    self.travel_context.flight_select_result = EnrichedFlightSelectResult(**value_in_mongo)

            if trip_context.get("hotel_select_result"):
                self.travel_context.hotel_select_result = HotelSelectResult(**trip_context["hotel_select_result"])

            if trip_context.get("flight_validation_result"):
                self.travel_context.flight_validation_result = FlightValidationResult(
                    **trip_context["flight_validation_result"]
                )
            if trip_context.get("hotel_validation_result"):
                self.travel_context.hotel_validation_result = HotelValidationResult(
                    **trip_context["hotel_validation_result"]
                )

            if trip_context.get("exchange_flight_state"):
                self.exchange_flight_state = EnrichedExchangeFlightState(**trip_context["exchange_flight_state"])

            if trip_context.get("change_seat_state"):
                # Ensure 'agent_response' is present in the change_seat_state dict, default to '' if missing
                # TODO @yuchen.liu: remove this once we switch to change seat state v2
                change_seat_state_dict = dict(trip_context["change_seat_state"])
                if "agent_response" not in change_seat_state_dict:
                    change_seat_state_dict["agent_response"] = ""
                self.change_seat_state = ChangeSeatState(**change_seat_state_dict)

            if trip_context.get("selected_outbound_flight"):
                self.travel_context.selected_outbound_flight = FlightOption(**trip_context["selected_outbound_flight"])

            if trip_context.get("selected_return_flight"):
                self.travel_context.selected_return_flight = FlightOption(**trip_context["selected_return_flight"])

            if trip_context.get("flight_confirmation_id"):
                self.travel_context.latest_flight_confirmation_id = trip_context["flight_confirmation_id"]

            if trip_context.get("flight_trip_id"):
                self.travel_context.latest_flight_trip_id = trip_context["flight_trip_id"]

            if trip_context.get("flight_airline_confirmation_number"):
                self.travel_context.latest_flight_airline_confirmation_number = trip_context[
                    "flight_airline_confirmation_number"
                ]

            if trip_context.get("user_provided_citizenship"):
                self.travel_context.user_provided_citizenship = trip_context["user_provided_citizenship"]

            if trip_context.get("user_responsed_entry_requirment"):
                self.travel_context.user_responsed_entry_requirment = trip_context["user_responsed_entry_requirment"]

            if trip_context.get("selected_hotel_for_segment"):
                selected_hotel_for_segment_in_mongo = trip_context["selected_hotel_for_segment"]
                res = {}
                for key, value in selected_hotel_for_segment_in_mongo.items():
                    res[key] = HotelSelectResult(**value)
                self.travel_context.selected_hotel_for_segment = res

            if trip_context.get("hotel_validation_for_segment"):
                hotel_validation_for_segment_in_mongo = trip_context["hotel_validation_for_segment"]
                res = {}
                for key, value in hotel_validation_for_segment_in_mongo.items():
                    res[key] = HotelValidationResult(**value)
                self.travel_context.hotel_validation_for_segment = res

            if trip_context.get("selected_flight_for_segment"):
                selected_flight_for_segment_in_mongo = trip_context["selected_flight_for_segment"]
                res = {}
                for key, value in selected_flight_for_segment_in_mongo.items():
                    res[key] = FlightOption(**value)
                self.travel_context.selected_flight_for_segment = res

            if trip_context.get("selected_hotel_option_for_segment"):
                selected_hotel_option_for_segment_in_mongo = trip_context["selected_hotel_option_for_segment"]
                if selected_hotel_option_for_segment_in_mongo:
                    self.travel_context.selected_hotel_option_for_segment = selected_hotel_option_for_segment_in_mongo

            if trip_context.get("flight_seat_selection"):
                self.travel_context.flight_seat_selection = [
                    SeatSelectionForFlight(**v) for v in trip_context["flight_seat_selection"]
                ]
            if trip_context.get("special_service_requests"):
                self.travel_context.special_service_requests = trip_context.get("special_service_requests")

        exisiting_booked_hotel = await Booking.from_query({"thread_id": self.thread.id, "type": "accommodations"})
        if exisiting_booked_hotel:
            self.travel_context.hotel_order_id = exisiting_booked_hotel.content.get("order_number", None)

    async def reset_sample_trip_state(self):
        await set_hide_sample_trips(self.user.id, False)
        await self.websocket_send_message(
            message={
                "type": "profile_update",
                "expectResponse": True,
            }
        )

    async def post_processing(self, extra: dict[str, Any]):
        asyncio.create_task(self.update_trip_metadata(extra))

    async def update_trip_metadata(self, extra: dict[str, Any]):
        """Update the trip metadata (title, date, etc)."""
        try:
            trip_brief = await b.FrontOfHouseSupervisorPostProcessing(
                travel_context=self.travel_context.model_dump_json(
                    include={
                        "flight_search_core_criteria",
                        "flight_search_additional_criteria",
                        "hotel_search_core_criteria",
                        "hotel_search_additional_criteria",
                    },
                    exclude_none=True,
                    exclude_unset=True,
                ),
                change_flight_context=self.exchange_flight_state.model_dump_json(
                    include={"departure_flight_want_change_to", "return_flight_want_change_to"},
                    exclude_none=True,
                    exclude_unset=True,
                ),
                baml_options={"collector": logger.collector},
            )

            logger.log_baml()
            logger.info(f"New trip_brief: {trip_brief.model_dump_json()}")

            update_fields = {}

            isSampleTrip = extra.get("isSampleTrip", False)
            if isSampleTrip:
                update_fields["extra"] = {"isSampleTrip": isSampleTrip}

            if trip_brief and trip_brief.title:
                update_fields["title"] = trip_brief.title
                update_fields["date_start"] = (
                    datetime.strptime(trip_brief.start_date, "%Y-%m-%d") if trip_brief.start_date else None
                )
                update_fields["date_end"] = (
                    datetime.strptime(trip_brief.end_date, "%Y-%m-%d") if trip_brief.end_date else None
                )
            else:
                logger.error("Trip brief is empty or city names are not available in post processing.")

            if update_fields:
                await self.thread.update_fields(update_fields)
                await self.websocket_send_message(message={"type": "trip_update"})

        except Exception as e:
            logger.error(f"Error when updating chat title: {e}")

    async def persist_messages(self, messages: list[BaseMessage]):
        self.messages.extend(messages)
        for message in messages:
            self.add_history_message(message)
        await self.history.apersist()
