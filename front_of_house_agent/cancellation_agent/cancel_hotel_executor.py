from functools import partial
from typing import Any, Coroutine

from langchain_core.messages import AIMessage

from baml_client.types import CancelHotelResponseWithStep, CancellationTaskStatus
from front_of_house_agent.back_of_house_executor.executor import <PERSON><PERSON><PERSON>cutor
from hotel_agent.booking_dot_com_models import ApiResponseStatus
from hotel_agent.hotels_helper import HotelsHelper
from server.database.models.chat_thread import ChatThread
from server.utils.message_constants import CANCELLATION_MESSAGES
from server.utils.settings import AgentTypes


class HotelCancelExecutor(AgentExecutor):
    def __init__(
        self,
        hotel_helper: HotelsHelper,
        message_sender: partial[Coroutine[Any, Any, None]],
        thread: ChatThread,
    ):
        super().__init__(message_sender)
        self.hotel_helper = hotel_helper
        self.message_sender = message_sender
        self.thread = thread

    async def execute(self, cancel_hotel_state: CancelHotelResponseWithStep, status: CancellationTaskStatus):
        message = None
        if status == CancellationTaskStatus.TODO:
            await self.message_sender(
                message={
                    "type": "search_update",
                    "text": CANCELLATION_MESSAGES["HOTEL_CANCELLATION_CHECK"],
                    "isBotMessage": True,
                    "expectResponse": False,
                }
            )
            cancel_context = await self.hotel_helper.build_hotel_cancel_context(cancel_hotel_state.order_number)

            message = AIMessage(
                content=cancel_context,
                additional_kwargs={
                    "agent_classification": AgentTypes.CANCEL_HOTEL,
                },
            )
        elif status == CancellationTaskStatus.APPROVED:
            await self.message_sender(
                message={
                    "type": "search_update",
                    "text": CANCELLATION_MESSAGES["HOTEL_CANCELLATION_SUBMIT"],
                    "isBotMessage": True,
                    "expectResponse": False,
                }
            )
            response = await self.hotel_helper.cancel_hotel(
                cancel_hotel_state.order_number or "",
                cancel_hotel_state.hotel_cancel_reason or "",
            )

            if response.status == ApiResponseStatus.SUCCESS:
                message = AIMessage(
                    content=f"Your booking with order ID {cancel_hotel_state.order_number} has been successfully cancelled. If you need further assistance, feel free to let me know.",
                    additional_kwargs={"agent_classification": AgentTypes.CANCEL_HOTEL},
                )
                await self.message_sender(message={"type": "itinerary_update"})
                await self.message_sender(message={"type": "trip_update"})
            else:
                message = AIMessage(
                    content=f"Unfortunately, I couldn't cancel your booking with order ID {cancel_hotel_state.order_number}. Please try again later or contact customer support for assistance.",
                    additional_kwargs={
                        "agent_classification": AgentTypes.CANCEL_HOTEL,
                    },
                )

        return message
