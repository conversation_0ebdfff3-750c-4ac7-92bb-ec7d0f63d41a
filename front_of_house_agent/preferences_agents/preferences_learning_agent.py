import asyncio
import j<PERSON>
from typing import Any, Callable, Coroutine, Set

from langchain_core.messages import AIMessage, BaseMessage

from baml_client import b
from baml_client.types import PreferencesConfidence
from front_of_house_agent.adapter import map_websocket_message
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.api.v1.endpoints.websocket_message import LearnedPreferences, MessageType
from server.schemas.authenticate.user import User
from server.services.user.user_preferences import get_user_preferences
from server.utils.logger import logger
from server.utils.redis_storage import RedisStorage
from server.utils.settings import AgentTypes

# from server.services.memory.memory_store import MemoryIsolationKeys, MemoryStore, MessageRole

one_month = 60 * 60 * 24 * 30
three_month = one_month * 3


async def prompt_new_preferences(
    user: User,
    messages: list,
    websocket_send_message,
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
):
    """
    Extracts new preferences, checks against recently prompted ones in Redis,
    and prompts the user only for unprompted preferences.
    """
    message_buffer = get_message_buffer_as_strings(messages)
    redis_storage = RedisStorage()
    redis_key = f"prompted_preferences:{user.id}"

    # Retrieve already prompted preferences from Redis
    prompted_data_dict = await redis_storage.retrieve_from_redis(redis_key)
    prompted_prefs_set = set(prompted_data_dict.get("prompted_list", []) if prompted_data_dict else [])
    logger.info(f"Prompted preferences from redis: {prompted_prefs_set}")

    # Get current preferences and extract potential new ones
    current_preferences = await get_user_preferences(user.id)
    extraction_result = await b.ExtractNewPreferencesFromCurrentConversation(
        current_preferences=current_preferences,
        messages=message_buffer,
        baml_options={"collector": logger.collector},
    )
    logger.log_baml()
    if not extraction_result.should_update or not extraction_result.new_preferences:
        return {}

    # Generate strings for new preferences
    potential_new_prefs_set = _generate_preference_strings(extraction_result.confidence)

    # Filter out preferences that have already been prompted
    unprompted_prefs_set = potential_new_prefs_set - prompted_prefs_set

    # Update Redis with the newly prompted preferences, storing the list within a dictionary
    updated_prompted_set = unprompted_prefs_set.union(prompted_prefs_set - potential_new_prefs_set)
    asyncio.create_task(
        redis_storage.save_to_redis(
            redis_key,
            {"prompted_list": list(updated_prompted_set)},  # Wrap the list in a dict
            expire_timedelta=three_month,
        )
    )

    if not updated_prompted_set:
        return {}

    response = await b.AskUserToUpdatePreferences(
        user_name=user.name,
        new_preferences=_trim_prompted_preferences(extraction_result.confidence, prompted_prefs_set),
        baml_options={"collector": logger.collector},
    )
    logger.log_baml()

    if not response.new_preferences:
        return {}

    preferences_to_update = LearnedPreferences.from_Preferences(response.new_preferences)

    new_message = AIMessage(
        content=response.agent_response,
        additional_kwargs={
            "agent_classification": AgentTypes.PREFERENCES,
            "message_type": MessageType.PROMPT.value,
            "function_call": {
                "arguments": json.dumps(
                    {
                        "suggested_preferences": preferences_to_update.model_dump(),
                    }
                )
            },
        },
    )

    await message_persistor([new_message])
    message = await map_websocket_message(new_message)
    await websocket_send_message(message={**message[0]})


def _generate_preference_strings(preferences: list[PreferencesConfidence]) -> Set[str]:
    """Converts a ResponseAllPreferences object into a set of unique strings."""
    pref_strings = set()
    for pref in preferences:
        pref_strings.add(f"{pref.category}:{pref.preference_value}")
    return pref_strings


def _trim_prompted_preferences(
    new_preferences: list[PreferencesConfidence], prompted: Set[str]
) -> list[PreferencesConfidence]:
    return [pref for pref in new_preferences if f"{pref.category}:{pref.preference_value}" not in prompted]
