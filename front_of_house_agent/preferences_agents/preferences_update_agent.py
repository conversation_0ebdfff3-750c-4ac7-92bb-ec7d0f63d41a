import json

from langchain_core.messages import AIMessage, BaseMessage

from baml_client import b
from baml_client.types import ResponseAllPreferences
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.database.models.user import User as UserDB
from server.schemas.authenticate.user import User
from server.services.user.user_preferences import save_user_preferences
from server.utils.logger import logger
from server.utils.settings import AgentTypes
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory


async def update_user_preferences(
    user: User, messages: list[BaseMessage], preferences, history: PostgresChatMessageHistory
):
    update = await b.UpdateUserPreferences(
        current_preferences=preferences,
        messages=get_message_buffer_as_strings(messages),
        baml_options={"collector": logger.collector},
    )
    logger.log_baml()
    new_preferences = update.new_preferences
    preferred_name = update.preffered_name

    messages_to_send = []

    if new_preferences:
        logger.info(f"current preferences: {preferences}")
        # Merge new preferences with current preferences
        merged_preferences = _merge_new_preferences(preferences, new_preferences)
        await save_user_preferences(user.id, merged_preferences)
        preferences = merged_preferences  # update current session's preferences

        logger.info(f"updated preferences: {preferences}")
        # find the suggested_preferences message, update the selected status
        for message in reversed(await history.persisted_messages):
            res = json.loads((message.additional_kwargs.get("function_call") or {}).get("arguments") or "{}")
            suggested_preferences = res.get("suggested_preferences") or message.additional_kwargs.get(
                "suggested_preferences"
            )

            if suggested_preferences:
                # suggested_preferences["options"]
                suggested_preferences["submitted"] = True
                if message_id := message.id:
                    message.additional_kwargs.get("function_call", {}).get("arguments", {}).update(
                        {"suggested_preferences": suggested_preferences}
                    )
                    await history.aupdate_message(message=message, message_id=int(message_id))
                break

    if preferred_name:
        user.preferred_name = preferred_name
        if user_db := await UserDB.from_id(user.id):
            await user_db.refresh_fields({"preferred_name": preferred_name})
            logger.info(f"user's preferred name updated: {user.preferred_name}")

    messages_to_send.append(
        AIMessage(
            content=update.agent_response,
            additional_kwargs={
                "message_type": "prompt",
                "agent_classification": AgentTypes.PREFERENCES,
            },
        )
    )
    return {"messages": messages_to_send}


def _merge_new_preferences(current_preferences: ResponseAllPreferences, new_preferences: ResponseAllPreferences):
    """
    Merges new preferences with current preferences.
    For string fields, if the new preferences have a value, use that value; otherwise, keep the current value.
    For list fields, append new values to the existing list, avoiding duplicates.

    Args:
        current_preferences: The current preferences (note the typo in parameter name)
        new_preferences: The new preferences to merge

    Returns:
        ResponseAllPreferences: The merged preferences
    """
    # Create a copy of the current preferences to avoid modifying the original
    merged_preferences = ResponseAllPreferences(**current_preferences.model_dump())

    # For each field in the new preferences, update the merged preferences appropriately
    new_prefs_dict = new_preferences.model_dump()
    for field, value in new_prefs_dict.items():
        if value is not None:
            # For string fields, replace if new value exists
            if isinstance(value, str) and value:
                setattr(merged_preferences, field, value)
            # For list fields, append new values to existing list, avoiding duplicates
            elif isinstance(value, list) and value:
                current_list = getattr(merged_preferences, field) or []
                # Add new items that aren't already in the list
                for item in value:
                    if item not in current_list:
                        current_list.append(item)
                setattr(merged_preferences, field, current_list)

    return merged_preferences
