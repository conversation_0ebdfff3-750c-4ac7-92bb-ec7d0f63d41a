from functools import partial
from typing import Any

from langchain_core.messages import (
    AIMessage,
    FunctionMessage,
    ToolMessage,
)

import front_of_house_agent.front_of_house_agent as fha
from agent.agent import StopResponse
from baml_client import b
from baml_client.types import (
    ChangeSeatState,
    DeltaUpdateOfChangeSeatState,
)
from front_of_house_agent.common_models import EnrichedChangeSeatState
from server.utils.mongo_connector import trip_context_v2_collection
from server.utils.settings import AgentTypes, settings


async def handle_change_seat(foh_agent: "fha.FrontOfHouseAgent", message_buffer_strs: list[str]) -> dict[str, Any]:
    delta_change_seat_state: DeltaUpdateOfChangeSeatState | StopResponse = await foh_agent.process_streaming(
        partial(
            b.stream.DoChangeSeatSelectionConversion,
            travel_context=foh_agent.travel_context.flight_search_additional_criteria.model_dump_json(
                exclude_none=True
            ),
            messages=message_buffer_strs,
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            previous_change_seat_state=foh_agent.change_seat_state.model_dump_json(exclude_none=True),
            user_name=foh_agent.user.name,
        ),
        lambda x: x.updated_change_flight_state and x.updated_change_flight_state.agent_response,
        lambda x: x.updated_change_flight_state.agent_response,
    )

    if isinstance(delta_change_seat_state, StopResponse):
        return {
            "messages": [
                AIMessage(content=delta_change_seat_state.last_text or "", additional_kwargs={"is_stopped": True})
            ],
            "current_topic": AgentTypes.CHANGE_SEAT,
            "model": None,
        }

    if delta_change_seat_state.updated_change_flight_state:
        current_change_seat_state_dict = foh_agent.change_seat_state.model_dump()
        current_change_seat_state_dict.update(
            delta_change_seat_state.updated_change_flight_state.model_dump(exclude_none=True)
        )
        foh_agent.change_seat_state = ChangeSeatState(**current_change_seat_state_dict)
        await trip_context_v2_collection.update_one(
            {"thread_id": foh_agent.thread.id},
            {"$set": {"change_seat_state": foh_agent.change_seat_state.model_dump()}},
            upsert=True,
        )

    stat_dict = foh_agent.change_seat_state.model_dump()
    stat_dict.update(
        {
            "flight_confirmation_id": foh_agent.travel_context.latest_flight_confirmation_id or "",
            "flight_trip_id": foh_agent.travel_context.latest_flight_trip_id or "",
        }
    )

    enriched_change_seat_state = EnrichedChangeSeatState(**stat_dict)

    await foh_agent.change_seat_executor.execute(
        enriched_change_seat_state,
        partial(foh_agent.persist_messages),
        {"seat_preference": foh_agent.travel_context.flight_search_additional_criteria.seat_types or []},
    )
    final_message = FunctionMessage(
        content="",
        name=delta_change_seat_state.__class__.__name__,
        additional_kwargs={
            "agent_classification": AgentTypes.CHANGE_SEAT,
            "function_call": {
                "name": delta_change_seat_state.__class__.__name__,
                "arguments": foh_agent.change_seat_state.model_dump_json(),
            },
        },
    )
    return {"messages": [final_message], "current_topic": AgentTypes.CHANGE_SEAT, "model": delta_change_seat_state}


async def handle_change_seat_v2(
    foh_agent: "fha.FrontOfHouseAgent", message_buffer_strs: list[str], tool_call_id: str
) -> dict[str, Any]:
    delta_change_seat_state = await b.DoChangeSeatSelectionConversionV2(
        travel_context=foh_agent.travel_context.flight_search_additional_criteria.model_dump_json(exclude_none=True),
        messages=message_buffer_strs,
        previous_change_seat_state=foh_agent.change_seat_state.model_dump_json(exclude_none=True),
    )

    if delta_change_seat_state.updated_change_flight_state:
        current_change_seat_state_dict = foh_agent.change_seat_state.model_dump()
        current_change_seat_state_dict.update(
            delta_change_seat_state.updated_change_flight_state.model_dump(exclude_none=True)
        )
        foh_agent.change_seat_state = ChangeSeatState(**current_change_seat_state_dict, agent_response="")
        await trip_context_v2_collection.update_one(
            {"thread_id": foh_agent.thread.id},
            {"$set": {"change_seat_state": foh_agent.change_seat_state.model_dump()}},
            upsert=True,
        )

    stat_dict = foh_agent.change_seat_state.model_dump()
    stat_dict.update(
        {
            "flight_confirmation_id": foh_agent.travel_context.latest_flight_confirmation_id or "",
            "flight_trip_id": foh_agent.travel_context.latest_flight_trip_id or "",
        }
    )

    enriched_change_seat_state = EnrichedChangeSeatState(**stat_dict, agent_response="")

    await foh_agent.change_seat_executor.execute(
        enriched_change_seat_state,
        partial(foh_agent.persist_messages),
        {"seat_preference": foh_agent.travel_context.flight_search_additional_criteria.seat_types or []},
    )
    final_message = ToolMessage(
        content="",
        tool_call_id=tool_call_id,
        name=delta_change_seat_state.__class__.__name__,
        additional_kwargs={
            "agent_classification": AgentTypes.CHANGE_SEAT,
            "function_call": {
                "name": delta_change_seat_state.__class__.__name__,
                "arguments": foh_agent.change_seat_state.model_dump_json(),
            },
        },
    )
    return {"messages": [final_message], "current_topic": AgentTypes.CHANGE_SEAT, "model": delta_change_seat_state}
