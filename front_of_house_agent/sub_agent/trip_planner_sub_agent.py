import json
from functools import partial
from typing import TYPE_CHECKING, Any, Callable, Union

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    FunctionMessage,
    ToolMessage,
)

from server.utils.message_constants import FLIGHT_SKELETON_MESSAGES

if TYPE_CHECKING:
    import front_of_house_agent.front_of_house_agent as fha
    from front_of_house_agent.trip_coordinator.trip_coordinator_agent import TripCoordinatorAgent
from agent.agent import StopResponse
from baml_client import b
from baml_client.type_builder import TypeBuilder
from baml_client.types import (
    BuyAction,
    FlightPlanningStep,
    FlightPlanResponse,
    FlightPlanResponseV2,
    FlightSearchAdditionalCriteria,
    FlightSearchCoreCriteria,
    FlightType,
    FrontOfHouseWorkAgentType,
    HotelPlanningStep,
    HotelPlanResponse,
    HotelPlanResponseV2,
    HotelSearchAdditionalCriteria,
    HotelSearchCoreCriteria,
    HotelSelectResult,
)
from front_of_house_agent.adapter import map_websocket_message
from front_of_house_agent.common_models import (
    EnrichedFlightSelectResult,
    FlightCheckoutMissField,
    FlightOption,
    FlightSearchSource,
    HotelValidationResult,
    SeatSelectionForFlight,
    TravelContext,
)
from llm_utils.llm_utils import (
    get_flight_detail_from_history,
    get_hotel_detail_from_history,
    get_message_buffer_as_strings,
)
from message.model import AIHumanMessage
from server.database.models.user import User
from server.services.feature_flags.feature_flag import FeatureFlags, is_feature_flag_enabled
from server.services.trips.flight_credits_api import flight_credits_api
from server.services.user_profile.loyalty_programs import (
    get_user_profile_flights_loyalty_programs,
    update_user_profile_flights_loyalty_programs,
)
from server.utils.mongo_connector import trip_context_v2_collection
from server.utils.settings import AgentTypes, settings
from virtual_travel_agent.helpers import (
    get_current_date_string,
)

__all__ = ["handle_flight_planner", "handle_hotel_planner"]


async def _update_hotel_travel_context_from_response(
    foh: "fha.FrontOfHouseAgent | TripCoordinatorAgent",
    final_response: Union[HotelPlanResponse, HotelPlanResponseV2],
    messages: list[BaseMessage],
):
    if final_response.updated_hotel_search_core_criteria:
        if final_response.current_step == HotelPlanningStep.HOTEL_SEARCH:
            # If the user is in hotel search step, we need to reset the hotel select result
            foh.travel_context.hotel_select_result = HotelSelectResult()
            if final_response.updated_hotel_search_core_criteria.hotel_segments:
                working_hotel_segment_index = final_response.model_dump().get("current_searching_segment_index") or 0
                original_hotel_segment = (
                    foh.travel_context.hotel_search_core_criteria.hotel_segments[working_hotel_segment_index]
                    if foh.travel_context.hotel_search_core_criteria.hotel_segments
                    else None
                )
                current_hotel_segment = next(
                    filter(
                        lambda x: x.segment_index == working_hotel_segment_index,
                        final_response.updated_hotel_search_core_criteria.hotel_segments or [],
                    )
                )
                if original_hotel_segment and current_hotel_segment and original_hotel_segment != current_hotel_segment:
                    # If the hotel segment is different, we need to reset the hotel select result
                    if foh.travel_context.selected_hotel_for_segment:
                        foh.travel_context.selected_hotel_for_segment.pop(str(working_hotel_segment_index), None)
        hotel_core_dict = foh.travel_context.hotel_search_core_criteria.model_dump(exclude_none=True)
        hotel_core_dict.update(final_response.updated_hotel_search_core_criteria.model_dump(exclude_none=True))

        foh.travel_context.hotel_search_core_criteria = HotelSearchCoreCriteria(**hotel_core_dict)

    if final_response.updated_hotel_search_additional_criteria:
        hotel_additional_dict = foh.travel_context.hotel_search_additional_criteria.model_dump(exclude_none=True)
        hotel_additional_dict.update(
            final_response.updated_hotel_search_additional_criteria.model_dump(exclude_none=True)
        )
        foh.travel_context.hotel_search_additional_criteria = HotelSearchAdditionalCriteria(**hotel_additional_dict)
        if final_response.current_step == HotelPlanningStep.HOTEL_SEARCH:
            # If the user is in hotel search step, we need to reset the hotel select result
            foh.travel_context.hotel_select_result = HotelSelectResult()

    if final_response.updated_hotel_select_result:
        hotel_select_result_dict = foh.travel_context.hotel_select_result.model_dump()

        hotel_select_result_dict.update(final_response.updated_hotel_select_result.model_dump(exclude_none=True))
        foh.travel_context.hotel_select_result = HotelSelectResult(**hotel_select_result_dict)
        hotel_choice_dict, room_dict = get_hotel_detail_from_history(
            messages=messages,
            property_id=foh.travel_context.hotel_select_result.property_id or 0,
            room_product_id=foh.travel_context.hotel_select_result.room_product_id,
        )

        if hotel_choice_dict:
            hotel_choice_dict["rooms"] = [room_dict] if room_dict else []

            # here must be segment index 0.
            if foh.travel_context.selected_hotel_option_for_segment is None:
                foh.travel_context.selected_hotel_option_for_segment = {}
            foh.travel_context.selected_hotel_option_for_segment["0"] = hotel_choice_dict

    if final_response.updated_hotel_select_result_for_multi_city:
        for segment_selection in final_response.updated_hotel_select_result_for_multi_city or []:
            original_selected_hotel = (
                foh.travel_context.selected_hotel_for_segment.get(str(segment_selection.segment_index))
                if foh.travel_context.selected_hotel_for_segment
                else None
            )
            original_selected_hotel_dict = {}
            if original_selected_hotel:
                original_selected_hotel_dict = original_selected_hotel.model_dump()

            original_selected_hotel_dict.update(segment_selection.model_dump())
            if foh.travel_context.selected_hotel_for_segment is None:
                foh.travel_context.selected_hotel_for_segment = {}
            foh.travel_context.selected_hotel_for_segment[str(segment_selection.segment_index)] = HotelSelectResult(
                **original_selected_hotel_dict
            )

            hotel_choice_dict, room_dict = get_hotel_detail_from_history(
                messages=messages,
                property_id=original_selected_hotel_dict.get("property_id") or 0,
                room_product_id=original_selected_hotel_dict.get("room_product_id"),
            )
            if hotel_choice_dict:
                hotel_choice_dict["rooms"] = [room_dict] if room_dict else []
                if foh.travel_context.selected_hotel_option_for_segment is None:
                    foh.travel_context.selected_hotel_option_for_segment = {}
                foh.travel_context.selected_hotel_option_for_segment[str(segment_selection.segment_index)] = (
                    hotel_choice_dict
                )
    await persist_travel_context(foh)


async def persist_travel_context(foh: "fha.FrontOfHouseAgent | TripCoordinatorAgent"):
    """Persist the travel context to the database."""
    await trip_context_v2_collection.update_one(
        {"thread_id": foh.thread.id},
        {
            "$set": {
                "thread_id": foh.thread.id,
                **foh.travel_context.model_dump(exclude_none=True),
            }
        },
        upsert=True,
    )


async def handle_flight_planner_v2(
    foh: "fha.FrontOfHouseAgent | TripCoordinatorAgent",
    classified_type_from_foh: FrontOfHouseWorkAgentType,
    messages: list[BaseMessage],
    extra_user_input: dict[str, Any],
    tool_call_id: str,
) -> dict[str, Any]:
    has_credits_for_selected_airline = False
    airline_codes = []
    flight_search_core_criteria_str = foh.travel_context.flight_search_core_criteria.model_dump_json(exclude_none=True)
    flight_search_additional_criteria_str = foh.travel_context.flight_search_additional_criteria.model_dump_json(
        exclude_none=True
    )
    flight_select_result_str = foh.travel_context.flight_select_result.model_dump_json(exclude_none=True)

    message_buffer_strs = get_message_buffer_as_strings(messages)

    if foh.travel_context.selected_outbound_flight:
        for stop in foh.travel_context.selected_outbound_flight.stops:
            airline_codes.append(stop.airline_code)

        if foh.travel_context.selected_return_flight:
            for stop in foh.travel_context.selected_return_flight.stops:
                airline_codes.append(stop.airline_code)
        if foh.travel_context.selected_flight_for_segment:
            for segment in foh.travel_context.selected_flight_for_segment.values():
                for stop in segment.stops:
                    airline_codes.append(stop.airline_code)
        if airline_codes:
            user_unused_credits = await flight_credits_api.get_user_unused_credits(foh.user.email)
            for credit in user_unused_credits:
                credit_airline_code = credit.get("extra", {}).get("airlineInfo", {}).get("airlineCode", "")
                if credit_airline_code in airline_codes:
                    has_credits_for_selected_airline = True
                    break

    pay_baggage_preference = None
    baggage_check_enabled = await is_feature_flag_enabled(foh.user.id, FeatureFlags.ENABLE_BAGGAGE_CHECK)
    if baggage_check_enabled:
        pay_baggage_preference = (
            foh.user_preferences.should_buy_luggage.value if foh.user_preferences.should_buy_luggage else None
        )
    else:
        pay_baggage_preference = BuyAction.NEVER.value

    final_response: FlightPlanResponseV2 | StopResponse
    final_response = await b.FlightPlannerV2(
        travel_preference=foh.user_preferences.model_dump_json(
            exclude_none=True, exclude={k for k, v in foh.user_preferences.model_dump().items() if v == []}
        ),
        flight_search_core_criteria=flight_search_core_criteria_str,
        flight_search_additional_criteria=flight_search_additional_criteria_str,
        messages=message_buffer_strs,
        current_date=get_current_date_string(foh.timezone),
        flight_select_result=flight_select_result_str,
        has_credit=has_credits_for_selected_airline,
        pay_baggage=pay_baggage_preference,
        foh_work_type=classified_type_from_foh.value,
    )

    if (
        final_response.updated_flight_select_result
        and final_response.updated_flight_select_result.frequent_flier_number
    ):
        user_profile_flights_loyalty_programs = await get_user_profile_flights_loyalty_programs(foh.user.id) or {}
        ffns = user_profile_flights_loyalty_programs.get("loyaltyPrograms") or []
        ffns = [] if ffns is None else ffns
        stored_airline_codes = {ff["IATACode"] for ff in ffns if ff.get("IATACode")}

        for ffn in final_response.updated_flight_select_result.frequent_flier_number:
            if ffn.airline_code not in stored_airline_codes:
                ffns.append({"IATACode": ffn.airline_code, "number": ffn.number})

        user_profile_flights_loyalty_programs["loyaltyPrograms"] = ffns
        await update_user_profile_flights_loyalty_programs(user_profile_flights_loyalty_programs, foh.user.id)

    if final_response.updated_flight_search_core_criteria:
        flight_core_dict = foh.travel_context.flight_search_core_criteria.model_dump()
        flight_core_dict.update(final_response.updated_flight_search_core_criteria.model_dump(exclude_none=True))
        foh.travel_context.flight_search_core_criteria = FlightSearchCoreCriteria(**flight_core_dict)

    if final_response.updated_flight_search_additional_criteria:
        flight_additional_dict = foh.travel_context.flight_search_additional_criteria.model_dump(exclude_none=True)
        flight_additional_dict.update(
            final_response.updated_flight_search_additional_criteria.model_dump(exclude_none=True)
        )
        foh.travel_context.flight_search_additional_criteria = FlightSearchAdditionalCriteria(**flight_additional_dict)

    if final_response.user_responsed_entry_requirment:
        foh.travel_context.user_responsed_entry_requirment = final_response.user_responsed_entry_requirment

    if final_response.updated_ctizenship_info:
        citizenships_to_update = final_response.updated_ctizenship_info
        current_citizenship = foh.user.citizenship or []
        for citizenship_to_update in citizenships_to_update or []:
            if citizenship_to_update not in current_citizenship:
                current_citizenship.append(citizenship_to_update or "")
        foh.user.citizenship = current_citizenship
        await User.update_citizenship(foh.user.id, current_citizenship)

        foh.travel_context.user_provided_citizenship = final_response.updated_ctizenship_info

    if final_response.updated_flight_select_result:
        if foh.travel_context.flight_search_core_criteria.flight_type == FlightType.OneWay:
            foh.travel_context.selected_return_flight = None
        flight_select_result_dict = foh.travel_context.flight_select_result.model_dump()
        flight_select_result_dict.update(final_response.updated_flight_select_result.model_dump(exclude_none=True))
        if final_response.current_step == FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH:
            flight_select_result_dict["selected_return_flight_id"] = None
            flight_select_result_dict["selected_outbound_flight_id"] = None
        elif final_response.current_step == FlightPlanningStep.RETURN_FLIGHT_SEARCH:
            flight_select_result_dict["selected_return_flight_id"] = None
        associated_search_id = None
        associated_search_source = None
        # FE will send the selected flight id to BE, so we need to update the selected flight id in the response
        # It is to handle the case that sometimes LLM will inferred wrong flgiht id.
        if extra_user_input.get(
            "return_flight_id"
        ) and final_response.updated_flight_select_result.selected_return_flight_id != extra_user_input.get(
            "return_flight_id"
        ):
            final_response.updated_flight_select_result.selected_return_flight_id = extra_user_input.get(
                "return_flight_id"
            )
        if extra_user_input.get(
            "outbound_flight_id"
        ) and final_response.updated_flight_select_result.selected_outbound_flight_id != extra_user_input.get(
            "outbound_flight_id"
        ):
            final_response.updated_flight_select_result.selected_outbound_flight_id = extra_user_input.get(
                "outbound_flight_id"
            )

        flight_id_to_find_search_id = (
            final_response.updated_flight_select_result.selected_outbound_flight_id
            if FlightPlanningStep.RETURN_FLIGHT_SEARCH in (final_response.current_step or [])
            else final_response.updated_flight_select_result.selected_return_flight_id
            or final_response.updated_flight_select_result.selected_outbound_flight_id
        )

        if flight_id_to_find_search_id:
            _, associated_search_id, associated_search_source = get_flight_detail_from_history(
                foh.messages, flight_id_to_find_search_id
            )
        foh.travel_context.flight_select_result = EnrichedFlightSelectResult(**flight_select_result_dict)
        if associated_search_id:
            foh.travel_context.flight_select_result.search_id = associated_search_id
        if associated_search_source:
            foh.travel_context.flight_select_result.search_source = FlightSearchSource(associated_search_source)
        if final_response.updated_flight_select_result.selected_outbound_flight_id:
            candidate, _, _ = get_flight_detail_from_history(
                foh.messages, final_response.updated_flight_select_result.selected_outbound_flight_id
            )
            if candidate:
                foh.travel_context.selected_outbound_flight = FlightOption.from_fe_display_dict(candidate)

        if final_response.updated_flight_select_result.selected_return_flight_id:
            candidate, _, _ = get_flight_detail_from_history(
                foh.messages, final_response.updated_flight_select_result.selected_return_flight_id
            )
            if candidate:
                foh.travel_context.selected_return_flight = FlightOption.from_fe_display_dict(candidate)

        for segment_selection in final_response.updated_flight_select_result.selected_flight_for_segment or []:
            flight_id_to_search = segment_selection.selected_flight_id

            if flight_id_to_search:
                candidate, _, _ = get_flight_detail_from_history(foh.messages, flight_id_to_search)
                if candidate:
                    if not foh.travel_context.selected_flight_for_segment:
                        foh.travel_context.selected_flight_for_segment = {}
                    foh.travel_context.selected_flight_for_segment[str(segment_selection.segment_index)] = (
                        FlightOption.from_fe_display_dict(candidate)
                    )
    if final_response.seat_selections:
        foh.travel_context.flight_seat_selection = final_response.seat_selections or []
    if final_response.special_service_requests:
        foh.travel_context.special_service_requests = final_response.special_service_requests

    await persist_travel_context(foh)

    async def handle_search(search_id: str, flight_source: FlightSearchSource | None):
        foh.travel_context.flight_select_result.search_id = search_id
        foh.travel_context.flight_select_result.search_source = flight_source
        await trip_context_v2_collection.update_one(
            {"thread_id": foh.thread.id},
            {"$set": {"flight_select_result": foh.travel_context.flight_select_result.model_dump()}},
            upsert=True,
        )

    async def handle_flight_checkout(
        search_id: str | None,
        iterinary_id: str | None,
        selected_seat_to_store: list[SeatSelectionForFlight],
    ):
        foh.travel_context.flight_seat_selection = selected_seat_to_store
        if search_id:
            foh.travel_context.flight_select_result.search_id = search_id
        if iterinary_id:
            foh.travel_context.flight_select_result.matched_flight_id = iterinary_id

        await trip_context_v2_collection.update_one(
            {"thread_id": foh.thread.id},
            {
                "$set": {
                    "flight_select_result": foh.travel_context.flight_select_result.model_dump(),
                }
            },
            upsert=True,
        )

    async def handle_booking(trip_id: str, confirmation_id: str, confirmation_number: str):
        foh.travel_context.latest_flight_trip_id = trip_id
        foh.travel_context.latest_flight_confirmation_id = confirmation_id
        foh.travel_context.latest_flight_airline_confirmation_number = confirmation_number

        await trip_context_v2_collection.update_one(
            {"thread_id": foh.thread.id},
            {
                "$set": {
                    "flight_trip_id": foh.travel_context.latest_flight_trip_id,
                    "flight_confirmation_id": foh.travel_context.latest_flight_confirmation_id,
                    "flight_airline_confirmation_number": foh.travel_context.latest_flight_airline_confirmation_number,
                }
            },
            upsert=True,
        )

    callback_handlers = {
        FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH.name: handle_search,
        FlightPlanningStep.RETURN_FLIGHT_SEARCH.name: handle_search,
        FlightPlanningStep.MULTI_LEG_FLIGHT_SEARCH.name: handle_search,
        FlightPlanningStep.FLIGHT_CHECKOUT.name: handle_flight_checkout,
        FlightPlanningStep.FLIGHT_BOOKING.name: handle_booking,
    }

    core_criteria = foh.travel_context.flight_search_core_criteria
    if foh.travel_context.flight_search_core_criteria.flight_type == FlightType.MultiLegs:
        core_dict = core_criteria.model_dump(exclude_none=True)
        core_dict.update(
            foh.travel_context.flight_search_core_criteria.flight_segments[
                final_response.current_searching_segment_index or 0
            ].model_dump(exclude_none=True)
            if foh.travel_context.flight_search_core_criteria
            and foh.travel_context.flight_search_core_criteria.flight_segments
            else {}
        )
        if foh.travel_context.flight_search_core_criteria.flight_type:
            core_dict["flight_type"] = foh.travel_context.flight_search_core_criteria.flight_type
        if foh.travel_context.flight_search_core_criteria.is_international_flight_trip:
            core_dict["is_international_flight_trip"] = (
                foh.travel_context.flight_search_core_criteria.is_international_flight_trip
            )

        core_criteria = FlightSearchCoreCriteria(**core_dict)
        foh.travel_context.flight_search_core_criteria = core_criteria

    task = await foh.trip_plan_executor.execute_flight_plan(
        FlightPlanResponse(**final_response.model_dump(exclude_none=True), agent_response=""),
        foh.travel_context,
        partial(foh.persist_messages),
        {
            "message_strs": message_buffer_strs,
            "preferred_seat_types": foh.travel_context.flight_search_additional_criteria.seat_types
            or foh.user_preferences.preferred_seats,
            "messages": messages,
        },
        callback_handlers,
    )

    final_message = ToolMessage(
        content="",
        name=final_response.__class__.__name__,
        tool_call_id=tool_call_id,
        additional_kwargs={
            "agent_classification": AgentTypes.FLIGHTS,
            "function_call": {
                "name": final_response.__class__.__name__,
                "arguments": final_response.model_dump_json(),
            },
        },
    )

    if foh.email_mode:
        await foh.persist_messages([final_message])
        if task:
            message = await task
            if isinstance(message, AIMessage) or isinstance(message, FunctionMessage):
                step_name = message.additional_kwargs.get("step")
                if step_name == FlightPlanningStep.FLIGHT_CHECKOUT.value:
                    if not message.additional_kwargs.get("miss_info"):
                        user_input = AIHumanMessage(
                            content="I think the traveler want to select free seat if there is free seat and proceed to next step. otherwise, just proceed to next step without select seat."
                        )
                    else:
                        human_responses = []
                        for key in message.additional_kwargs.get("miss_info", {}).keys():
                            if key == FlightCheckoutMissField.PREFFERED_SEAT_TYPE.value:
                                human_responses.append("I think the traveler likes any type of seat")
                            elif key == FlightCheckoutMissField.FFN.value:
                                human_responses.append(
                                    "I think the traveler does not need to provide frequent flyer number right now"
                                )
                        user_input = AIHumanMessage(content=",".join(human_responses))
                    await foh.persist_messages([user_input])
                    return await handle_flight_planner(
                        foh,
                        foh.messages,
                        get_message_buffer_as_strings(foh.messages),
                        extra_user_input={},
                        flight_search_core_criteria_str=foh.travel_context.flight_search_core_criteria.model_dump_json(
                            exclude_none=True
                        ),
                        flight_search_additional_criteria_str=foh.travel_context.flight_search_additional_criteria.model_dump_json(
                            exclude_none=True
                        ),
                        flight_select_result_str=foh.travel_context.flight_select_result.model_dump_json(
                            exclude_none=True
                        ),
                        prompt_user_preferences=None,
                    )
                elif step_name == FlightPlanningStep.FLIGHT_CREDIT_CHECK.value:
                    user_input = AIHumanMessage(content="I think the traveler does not need to use flight credit")
                    await foh.persist_messages([user_input])
                    return await handle_flight_planner(
                        foh,
                        foh.messages,
                        get_message_buffer_as_strings(foh.messages),
                        extra_user_input={},
                        flight_search_core_criteria_str=foh.travel_context.flight_search_core_criteria.model_dump_json(
                            exclude_none=True
                        ),
                        flight_search_additional_criteria_str=foh.travel_context.flight_search_additional_criteria.model_dump_json(
                            exclude_none=True
                        ),
                        flight_select_result_str=foh.travel_context.flight_select_result.model_dump_json(
                            exclude_none=True
                        ),
                        prompt_user_preferences=None,
                    )

                elif step_name == FlightPlanningStep.FLIGHT_VALIDATION.value:
                    user_input = AIHumanMessage(
                        content="I think the traveler should confirm this flight, so let's proceed."
                    )
                    await foh.persist_messages([user_input])
                    return await handle_flight_planner(
                        foh,
                        foh.messages,
                        get_message_buffer_as_strings(foh.messages),
                        extra_user_input={},
                        flight_search_core_criteria_str=foh.travel_context.flight_search_core_criteria.model_dump_json(
                            exclude_none=True
                        ),
                        flight_search_additional_criteria_str=foh.travel_context.flight_search_additional_criteria.model_dump_json(
                            exclude_none=True
                        ),
                        flight_select_result_str=foh.travel_context.flight_select_result.model_dump_json(
                            exclude_none=True
                        ),
                        prompt_user_preferences=None,
                    )
                elif (
                    step_name == FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH.value
                    and foh.travel_context.flight_search_core_criteria.flight_type == FlightType.RoundTrip
                ):
                    return await handle_flight_search_combo(foh, message, streaming=False)

            return {
                "messages": [message],
                "current_topic": "Flights",
                "model": final_response,
            }
    return {"messages": [final_message], "current_topic": "Flights", "model": final_response}


async def handle_flight_planner(
    foh: "fha.FrontOfHouseAgent | TripCoordinatorAgent",
    messages: list[BaseMessage],
    message_buffer_strs: list[str],
    extra_user_input: dict[str, Any],
    flight_search_core_criteria_str: str,
    flight_search_additional_criteria_str: str,
    flight_select_result_str: str,
    prompt_user_preferences: Callable | None,
    streaming: bool = True,
) -> dict[str, Any]:
    has_credits_for_selected_airline = False
    airline_codes = []

    if foh.travel_context.selected_outbound_flight:
        for stop in foh.travel_context.selected_outbound_flight.stops:
            airline_codes.append(stop.airline_code)

        if foh.travel_context.selected_return_flight:
            for stop in foh.travel_context.selected_return_flight.stops:
                airline_codes.append(stop.airline_code)
        if foh.travel_context.selected_flight_for_segment:
            for segment in foh.travel_context.selected_flight_for_segment.values():
                for stop in segment.stops:
                    airline_codes.append(stop.airline_code)
        if airline_codes:
            user_unused_credits = await flight_credits_api.get_user_unused_credits(foh.user.email)
            for credit in user_unused_credits:
                credit_airline_code = credit.get("extra", {}).get("airlineInfo", {}).get("airlineCode", "")
                if credit_airline_code in airline_codes:
                    has_credits_for_selected_airline = True
                    break

    pay_baggage_preference = None
    baggage_check_enabled = await is_feature_flag_enabled(foh.user.id, FeatureFlags.ENABLE_BAGGAGE_CHECK)
    if baggage_check_enabled:
        pay_baggage_preference = (
            foh.user_preferences.should_buy_luggage.value if foh.user_preferences.should_buy_luggage else None
        )
    else:
        pay_baggage_preference = BuyAction.NEVER.value

    final_response: FlightPlanResponse | StopResponse
    if streaming:
        final_response = await foh.process_streaming(
            partial(
                b.stream.FlightPlanner,
                travel_preference=foh.user_preferences.model_dump_json(
                    exclude_none=True, exclude={k for k, v in foh.user_preferences.model_dump().items() if v == []}
                ),
                flight_search_core_criteria=flight_search_core_criteria_str,
                flight_search_additional_criteria=flight_search_additional_criteria_str,
                messages=message_buffer_strs,
                current_date=get_current_date_string(foh.timezone),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                flight_select_result=flight_select_result_str,
                has_credit=has_credits_for_selected_airline,
                pay_baggage=pay_baggage_preference,
                user_name=foh.user.name,
            ),
            lambda x: x.agent_response,
            lambda x: x.agent_response,
        )
        if isinstance(final_response, StopResponse):
            return {
                "messages": [AIMessage(content=final_response.last_text or "", additional_kwargs={"is_stopped": True})],
                "current_topic": AgentTypes.FLIGHTS,
                "model": None,
            }
    else:
        final_response = await b.FlightPlanner(
            travel_preference=foh.user_preferences.model_dump_json(
                exclude_none=True, exclude={k for k, v in foh.user_preferences.model_dump().items() if v == []}
            ),
            flight_search_core_criteria=flight_search_core_criteria_str,
            flight_search_additional_criteria=flight_search_additional_criteria_str,
            messages=message_buffer_strs,
            current_date=get_current_date_string(foh.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            flight_select_result=flight_select_result_str,
            web_search_knowledge=foh.travel_context.web_search_knowledge,
            trip_optimization_knowledge=foh.travel_context.trip_optimization_knowledge,
            has_credit=has_credits_for_selected_airline,
            pay_baggage=pay_baggage_preference,
            user_name=foh.user.name,
        )

    if (
        final_response.updated_flight_select_result
        and final_response.updated_flight_select_result.frequent_flier_number
    ):
        user_profile_flights_loyalty_programs = await get_user_profile_flights_loyalty_programs(foh.user.id) or {}
        ffns = user_profile_flights_loyalty_programs.get("loyaltyPrograms") or []
        ffns = [] if ffns is None else ffns
        stored_airline_codes = {ff["IATACode"] for ff in ffns if ff.get("IATACode")}

        for ffn in final_response.updated_flight_select_result.frequent_flier_number:
            if ffn.airline_code not in stored_airline_codes:
                ffns.append({"IATACode": ffn.airline_code, "number": ffn.number})

        user_profile_flights_loyalty_programs["loyaltyPrograms"] = ffns
        await update_user_profile_flights_loyalty_programs(user_profile_flights_loyalty_programs, foh.user.id)

    if final_response.updated_flight_search_core_criteria:
        flight_core_dict = foh.travel_context.flight_search_core_criteria.model_dump()
        flight_core_dict.update(final_response.updated_flight_search_core_criteria.model_dump(exclude_none=True))
        foh.travel_context.flight_search_core_criteria = FlightSearchCoreCriteria(**flight_core_dict)

    if final_response.updated_flight_search_additional_criteria:
        flight_additional_dict = foh.travel_context.flight_search_additional_criteria.model_dump(exclude_none=True)
        flight_additional_dict.update(
            final_response.updated_flight_search_additional_criteria.model_dump(exclude_none=True)
        )
        foh.travel_context.flight_search_additional_criteria = FlightSearchAdditionalCriteria(**flight_additional_dict)

    if final_response.user_responsed_entry_requirment:
        foh.travel_context.user_responsed_entry_requirment = final_response.user_responsed_entry_requirment

    if final_response.updated_ctizenship_info:
        citizenships_to_update = final_response.updated_ctizenship_info
        current_citizenship = foh.user.citizenship or []
        for citizenship_to_update in citizenships_to_update or []:
            if citizenship_to_update not in current_citizenship:
                current_citizenship.append(citizenship_to_update or "")
        foh.user.citizenship = current_citizenship
        await User.update_citizenship(foh.user.id, current_citizenship)

        foh.travel_context.user_provided_citizenship = final_response.updated_ctizenship_info

    if final_response.updated_flight_select_result:
        if foh.travel_context.flight_search_core_criteria.flight_type == FlightType.OneWay:
            foh.travel_context.selected_return_flight = None
        flight_select_result_dict = foh.travel_context.flight_select_result.model_dump()
        flight_select_result_dict.update(final_response.updated_flight_select_result.model_dump(exclude_none=True))
        if final_response.current_step == FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH:
            flight_select_result_dict["selected_return_flight_id"] = None
            flight_select_result_dict["selected_outbound_flight_id"] = None
        elif final_response.current_step == FlightPlanningStep.RETURN_FLIGHT_SEARCH:
            flight_select_result_dict["selected_return_flight_id"] = None
        associated_search_id = None
        associated_search_source = None
        # FE will send the selected flight id to BE, so we need to update the selected flight id in the response
        # It is to handle the case that sometimes LLM will inferred wrong flgiht id.
        if extra_user_input.get(
            "return_flight_id"
        ) and final_response.updated_flight_select_result.selected_return_flight_id != extra_user_input.get(
            "return_flight_id"
        ):
            final_response.updated_flight_select_result.selected_return_flight_id = extra_user_input.get(
                "return_flight_id"
            )
        if extra_user_input.get(
            "outbound_flight_id"
        ) and final_response.updated_flight_select_result.selected_outbound_flight_id != extra_user_input.get(
            "outbound_flight_id"
        ):
            final_response.updated_flight_select_result.selected_outbound_flight_id = extra_user_input.get(
                "outbound_flight_id"
            )

        flight_id_to_find_search_id = (
            final_response.updated_flight_select_result.selected_outbound_flight_id
            if FlightPlanningStep.RETURN_FLIGHT_SEARCH in (final_response.current_step or [])
            else final_response.updated_flight_select_result.selected_return_flight_id
            or final_response.updated_flight_select_result.selected_outbound_flight_id
        )

        if flight_id_to_find_search_id:
            _, associated_search_id, associated_search_source = get_flight_detail_from_history(
                foh.messages, flight_id_to_find_search_id
            )
        foh.travel_context.flight_select_result = EnrichedFlightSelectResult(**flight_select_result_dict)
        if associated_search_id:
            foh.travel_context.flight_select_result.search_id = associated_search_id
        if associated_search_source:
            foh.travel_context.flight_select_result.search_source = FlightSearchSource(associated_search_source)
        if final_response.updated_flight_select_result.selected_outbound_flight_id:
            candidate, _, _ = get_flight_detail_from_history(
                foh.messages, final_response.updated_flight_select_result.selected_outbound_flight_id
            )
            if candidate:
                foh.travel_context.selected_outbound_flight = FlightOption.from_fe_display_dict(candidate)

        if final_response.updated_flight_select_result.selected_return_flight_id:
            candidate, _, _ = get_flight_detail_from_history(
                foh.messages, final_response.updated_flight_select_result.selected_return_flight_id
            )
            if candidate:
                foh.travel_context.selected_return_flight = FlightOption.from_fe_display_dict(candidate)

        for segment_selection in final_response.updated_flight_select_result.selected_flight_for_segment or []:
            flight_id_to_search = segment_selection.selected_flight_id

            if flight_id_to_search:
                candidate, _, _ = get_flight_detail_from_history(foh.messages, flight_id_to_search)
                if candidate:
                    if not foh.travel_context.selected_flight_for_segment:
                        foh.travel_context.selected_flight_for_segment = {}
                    foh.travel_context.selected_flight_for_segment[str(segment_selection.segment_index)] = (
                        FlightOption.from_fe_display_dict(candidate)
                    )
    if final_response.seat_selections:
        foh.travel_context.flight_seat_selection = final_response.seat_selections or []
    if final_response.special_service_requests:
        foh.travel_context.special_service_requests = final_response.special_service_requests

    await persist_travel_context(foh)

    async def handle_search(search_id: str, flight_source: FlightSearchSource | None):
        foh.travel_context.flight_select_result.search_id = search_id
        foh.travel_context.flight_select_result.search_source = flight_source
        await trip_context_v2_collection.update_one(
            {"thread_id": foh.thread.id},
            {"$set": {"flight_select_result": foh.travel_context.flight_select_result.model_dump()}},
            upsert=True,
        )

    async def handle_flight_checkout(
        search_id: str | None,
        iterinary_id: str | None,
        selected_seat_to_store: list[SeatSelectionForFlight],
    ):
        foh.travel_context.flight_seat_selection = selected_seat_to_store
        if search_id:
            foh.travel_context.flight_select_result.search_id = search_id
        if iterinary_id:
            foh.travel_context.flight_select_result.matched_flight_id = iterinary_id

        await trip_context_v2_collection.update_one(
            {"thread_id": foh.thread.id},
            {
                "$set": {
                    "flight_select_result": foh.travel_context.flight_select_result.model_dump(),
                }
            },
            upsert=True,
        )

    async def handle_booking(trip_id: str, confirmation_id: str, confirmation_number: str):
        foh.travel_context.latest_flight_trip_id = trip_id
        foh.travel_context.latest_flight_confirmation_id = confirmation_id
        foh.travel_context.latest_flight_airline_confirmation_number = confirmation_number

        await trip_context_v2_collection.update_one(
            {"thread_id": foh.thread.id},
            {
                "$set": {
                    "flight_trip_id": foh.travel_context.latest_flight_trip_id,
                    "flight_confirmation_id": foh.travel_context.latest_flight_confirmation_id,
                    "flight_airline_confirmation_number": foh.travel_context.latest_flight_airline_confirmation_number,
                }
            },
            upsert=True,
        )
        if prompt_user_preferences:
            await prompt_user_preferences()

    callback_handlers = {
        FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH.name: handle_search,
        FlightPlanningStep.RETURN_FLIGHT_SEARCH.name: handle_search,
        FlightPlanningStep.MULTI_LEG_FLIGHT_SEARCH.name: handle_search,
        FlightPlanningStep.FLIGHT_CHECKOUT.name: handle_flight_checkout,
        FlightPlanningStep.FLIGHT_BOOKING.name: handle_booking,
    }

    core_criteria = foh.travel_context.flight_search_core_criteria
    if foh.travel_context.flight_search_core_criteria.flight_type == FlightType.MultiLegs:
        core_dict = core_criteria.model_dump(exclude_none=True)
        core_dict.update(
            foh.travel_context.flight_search_core_criteria.flight_segments[
                final_response.current_searching_segment_index or 0
            ].model_dump(exclude_none=True)
            if foh.travel_context.flight_search_core_criteria
            and foh.travel_context.flight_search_core_criteria.flight_segments
            else {}
        )
        if foh.travel_context.flight_search_core_criteria.flight_type:
            core_dict["flight_type"] = foh.travel_context.flight_search_core_criteria.flight_type
        if foh.travel_context.flight_search_core_criteria.is_international_flight_trip:
            core_dict["is_international_flight_trip"] = (
                foh.travel_context.flight_search_core_criteria.is_international_flight_trip
            )

        core_criteria = FlightSearchCoreCriteria(**core_dict)
        foh.travel_context.flight_search_core_criteria = core_criteria

    task = await foh.trip_plan_executor.execute_flight_plan(
        final_response,
        foh.travel_context,
        partial(foh.persist_messages),
        {
            "message_strs": message_buffer_strs,
            "preferred_seat_types": foh.travel_context.flight_search_additional_criteria.seat_types
            or foh.user_preferences.preferred_seats,
            "messages": messages,
        },
        callback_handlers,
    )

    final_message = FunctionMessage(
        content="",
        name=final_response.__class__.__name__,
        additional_kwargs={
            "agent_classification": AgentTypes.FLIGHTS,
            "function_call": {
                "name": final_response.__class__.__name__,
                "arguments": final_response.model_dump_json(),
            },
        },
    )

    if foh.email_mode:
        await foh.persist_messages([final_message])
        if task:
            message = await task
            if isinstance(message, AIMessage) or isinstance(message, FunctionMessage):
                step_name = message.additional_kwargs.get("step")
                if step_name == FlightPlanningStep.FLIGHT_CHECKOUT.value:
                    if not message.additional_kwargs.get("miss_info"):
                        user_input = AIHumanMessage(
                            content="I think the traveler want to select free seat if there is free seat and proceed to next step. otherwise, just proceed to next step without select seat."
                        )
                    else:
                        human_responses = []
                        for key in message.additional_kwargs.get("miss_info", {}).keys():
                            if key == FlightCheckoutMissField.PREFFERED_SEAT_TYPE.value:
                                human_responses.append("I think the traveler likes any type of seat")
                            elif key == FlightCheckoutMissField.FFN.value:
                                human_responses.append(
                                    "I think the traveler does not need to provide frequent flyer number right now"
                                )
                        user_input = AIHumanMessage(content=",".join(human_responses))
                    await foh.persist_messages([user_input])
                    return await handle_flight_planner(
                        foh,
                        foh.messages,
                        get_message_buffer_as_strings(foh.messages),
                        extra_user_input={},
                        flight_search_core_criteria_str=foh.travel_context.flight_search_core_criteria.model_dump_json(
                            exclude_none=True
                        ),
                        flight_search_additional_criteria_str=foh.travel_context.flight_search_additional_criteria.model_dump_json(
                            exclude_none=True
                        ),
                        flight_select_result_str=foh.travel_context.flight_select_result.model_dump_json(
                            exclude_none=True
                        ),
                        prompt_user_preferences=None,
                        streaming=streaming,
                    )
                elif step_name == FlightPlanningStep.FLIGHT_CREDIT_CHECK.value:
                    user_input = AIHumanMessage(content="I think the traveler does not need to use flight credit")
                    await foh.persist_messages([user_input])
                    return await handle_flight_planner(
                        foh,
                        foh.messages,
                        get_message_buffer_as_strings(foh.messages),
                        extra_user_input={},
                        flight_search_core_criteria_str=foh.travel_context.flight_search_core_criteria.model_dump_json(
                            exclude_none=True
                        ),
                        flight_search_additional_criteria_str=foh.travel_context.flight_search_additional_criteria.model_dump_json(
                            exclude_none=True
                        ),
                        flight_select_result_str=foh.travel_context.flight_select_result.model_dump_json(
                            exclude_none=True
                        ),
                        prompt_user_preferences=None,
                        streaming=streaming,
                    )

                elif step_name == FlightPlanningStep.FLIGHT_VALIDATION.value:
                    user_input = AIHumanMessage(
                        content="I think the traveler should confirm this flight, so let's proceed."
                    )
                    await foh.persist_messages([user_input])
                    return await handle_flight_planner(
                        foh,
                        foh.messages,
                        get_message_buffer_as_strings(foh.messages),
                        extra_user_input={},
                        flight_search_core_criteria_str=foh.travel_context.flight_search_core_criteria.model_dump_json(
                            exclude_none=True
                        ),
                        flight_search_additional_criteria_str=foh.travel_context.flight_search_additional_criteria.model_dump_json(
                            exclude_none=True
                        ),
                        flight_select_result_str=foh.travel_context.flight_select_result.model_dump_json(
                            exclude_none=True
                        ),
                        prompt_user_preferences=None,
                        streaming=streaming,
                    )
                elif (
                    step_name == FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH.value
                    and foh.travel_context.flight_search_core_criteria.flight_type == FlightType.RoundTrip
                ):
                    return await handle_flight_search_combo(foh, message, streaming=streaming)

            return {
                "messages": [message],
                "current_topic": "Flights",
                "model": final_response,
            }
    return {"messages": [final_message], "current_topic": "Flights", "model": final_response}


async def handle_flight_search_combo(
    foh: "fha.FrontOfHouseAgent | TripCoordinatorAgent", outbound: BaseMessage, streaming: bool = True
) -> dict[str, Any]:
    outbound_flights = (await map_websocket_message(outbound))[0].get("flights")

    if not outbound_flights:
        empty_flight_message = AIMessage(content=FLIGHT_SKELETON_MESSAGES["NO_FLIGHTS"])
        await foh.persist_messages([empty_flight_message])
        return {"messages": [empty_flight_message], "current_topic": "Flights"}

    human = AIHumanMessage(
        content="I think the traveler want to see the return flight options for the first outbound flight, so let's select the first outbound flight and search return flight options corresponding to it."
    )
    await foh.persist_messages([human])
    first_return = (
        await handle_flight_planner(
            foh,
            foh.messages,
            get_message_buffer_as_strings(foh.messages),
            extra_user_input={},
            flight_search_core_criteria_str=foh.travel_context.flight_search_core_criteria.model_dump_json(
                exclude_none=True
            ),
            flight_search_additional_criteria_str=foh.travel_context.flight_search_additional_criteria.model_dump_json(
                exclude_none=True
            ),
            flight_select_result_str=foh.travel_context.flight_select_result.model_dump_json(exclude_none=True),
            prompt_user_preferences=None,
            streaming=streaming,
        )
    ).get("messages", [])[0]

    first_return_flights = (await map_websocket_message(first_return))[0].get("flights")

    if outbound_flights and len(outbound_flights) > 1:
        human = AIHumanMessage(
            content="I think the traveler also want to see the return flight options for the second outbound flight, so let's select the second outbound flight and search return flight options corresponding to it."
        )
        await foh.persist_messages([human])
        second_return = (
            await handle_flight_planner(
                foh,
                foh.messages,
                get_message_buffer_as_strings(foh.messages),
                extra_user_input={},
                flight_search_core_criteria_str=foh.travel_context.flight_search_core_criteria.model_dump_json(
                    exclude_none=True
                ),
                flight_search_additional_criteria_str=foh.travel_context.flight_search_additional_criteria.model_dump_json(
                    exclude_none=True
                ),
                flight_select_result_str=foh.travel_context.flight_select_result.model_dump_json(exclude_none=True),
                prompt_user_preferences=None,
                streaming=streaming,
            )
        ).get("messages", [])[0]
        second_return_flights = (await map_websocket_message(second_return))[0].get("flights")
    else:
        second_return_flights = None

    first_outbound_flight = outbound_flights[0] if outbound_flights else None
    second_outbound_flight = outbound_flights[1] if outbound_flights and len(outbound_flights) > 1 else None

    if second_return_flights:
        first_return_flight = first_return_flights[0] if first_return_flights else None
        second_return_flight = second_return_flights[0] if second_return_flights else None
    else:
        first_return_flight = first_return_flights[0] if first_return_flights else None
        second_return_flight = (
            first_return_flights[1]
            if first_return_flights and len(first_return_flights) > 1
            else first_return_flights[0]
            if first_return_flights
            else None
        )

    content = {
        "first_round_trip_combo": {
            "alias": ["option 1", "first option", "1st option", "combo 1"],
            "outbound_flight": first_outbound_flight,
            "return_flight": first_return_flight,
        },
        "second_round_trip_combo": {
            "alias": ["option 2", "second option", "2nd option", "combo 2"],
            "outbound_flight": second_outbound_flight,
            "return_flight": second_return_flight,
        },
    }

    tb = TypeBuilder()
    tb.EmailResponse.add_property("table", tb.string().optional()).description(
        "The table of the flight or hotel options in GitHub Flavored Markdown format if applicable."
    )
    r = await b.SummaryResponseForEmail(
        original_input=json.dumps(content),
        table_format=True,
        other_options="",
        self_intro=settings.OTTO_SELF_INTRO,
        baml_options={"tb": tb},
    )
    r_dict = r.model_dump()
    msg_content: str = r_dict["summary"]
    if r_dict["table"]:
        msg_content += f"\n\n{r_dict['table']}"
    msg = AIMessage(
        content=msg_content,
        additional_kwargs={
            "round_trip_combos": json.dumps(content),
            "agent_classification": AgentTypes.FLIGHTS,
        },
    )
    await foh.persist_messages([msg])

    # Email case, we mock flight selection to get final combo, clean up the flight select result.
    foh.travel_context.flight_select_result.selected_outbound_flight_id = None
    foh.travel_context.flight_select_result.selected_return_flight_id = None
    foh.travel_context.flight_select_result.search_id = None
    foh.travel_context.selected_outbound_flight = None
    foh.travel_context.selected_return_flight = None
    await trip_context_v2_collection.update_one(
        {"thread_id": foh.thread.id},
        {
            "$set": {
                "flight_select_result": foh.travel_context.flight_select_result.model_dump(),
                "selected_outbound_flight": foh.travel_context.selected_return_flight,
                "selected_return_flight": foh.travel_context.selected_return_flight,
            }
        },
        upsert=True,
    )

    return_message = FunctionMessage(
        content="",
        name="FlightComboSummary",
        additional_kwargs={
            "function_call": {
                "name": "FlightComboSummary",
                "arguments": json.dumps({"flight_combos": content}),
            },
            "agent_classification": AgentTypes.FLIGHTS,
        },
    )
    return {"messages": [return_message], "current_topic": "Flights"}


async def handle_hotel_planner_v2(
    foh: "fha.FrontOfHouseAgent | TripCoordinatorAgent",
    classified_type_from_foh: FrontOfHouseWorkAgentType,
    messages: list[BaseMessage],
    prompt_user_preferences: Callable | None,
    tool_call_id: str,
) -> dict[str, Any]:
    message_buffer_strs = get_message_buffer_as_strings(messages)
    hotel_search_core_criteria_str = foh.travel_context.hotel_search_core_criteria.model_dump_json()
    hotel_search_additional_criteria_str = foh.travel_context.hotel_search_additional_criteria.model_dump_json(
        exclude_none=True
    )
    hotel_select_result_str = foh.travel_context.hotel_select_result.model_dump_json()
    selected_flight_itinerary_str = json.dumps(foh.travel_context.to_selected_flights())

    final_response: HotelPlanResponseV2

    final_response = await b.HotelPlannerV2(
        travel_preference=foh.user_preferences.model_dump_json(exclude_none=True),
        hotel_search_core_criteria=hotel_search_core_criteria_str,
        hotel_search_additional_criteria=hotel_search_additional_criteria_str,
        selected_flight_itinerary=selected_flight_itinerary_str,
        messages=message_buffer_strs,
        current_date=get_current_date_string(foh.timezone),
        hotel_select_result=hotel_select_result_str,
        foh_work_type=classified_type_from_foh.value,
    )

    await _update_hotel_travel_context_from_response(foh, final_response, messages)

    async def on_hotel_validated(
        order_token: str | None,
        validated_price: float | None,
        currency: str | None,
        hotel_validation_for_segment: dict[str, HotelValidationResult] | None,
    ):
        if order_token:
            foh.travel_context.hotel_validation_result.order_token = order_token
        if validated_price:
            foh.travel_context.hotel_validation_result.validated_price = validated_price
        if currency:
            foh.travel_context.hotel_validation_result.currency = currency

        validation_per_segment = None
        if hotel_validation_for_segment:
            foh.travel_context.hotel_validation_for_segment = hotel_validation_for_segment
            validation_per_segment = {}
            for segment_index, hotel_validation_result in hotel_validation_for_segment.items():
                validation_per_segment[segment_index] = hotel_validation_result.model_dump()

        await trip_context_v2_collection.update_one(
            {"thread_id": foh.thread.id},
            {
                "$set": {
                    "hotel_validation_result": foh.travel_context.hotel_validation_result.model_dump(),
                    "hotel_validation_for_segment": validation_per_segment,
                }
            },
            upsert=True,
        )

    async def on_hotel_booked():
        # prompt user to update preferences
        if prompt_user_preferences:
            await prompt_user_preferences()

    core_search_criteria = foh.travel_context.hotel_search_core_criteria
    hotel_selection = foh.travel_context.hotel_select_result
    if foh.travel_context.hotel_search_core_criteria.hotel_segments:
        core_dict = core_search_criteria.model_dump(exclude_none=True)
        core_search_segment_dict = (
            foh.travel_context.hotel_search_core_criteria.hotel_segments[
                final_response.current_searching_segment_index or 0
            ].model_dump(exclude_none=True)
            if foh.travel_context.hotel_search_core_criteria
            and foh.travel_context.hotel_search_core_criteria.hotel_segments
            else {}
        )
        core_dict.update(core_search_segment_dict)

        core_search_criteria = HotelSearchCoreCriteria(**core_dict)
        foh.travel_context.hotel_search_core_criteria = core_search_criteria
        hotel_selection = (
            foh.travel_context.selected_hotel_for_segment.get(str(final_response.current_searching_segment_index or 0))
            if foh.travel_context.selected_hotel_for_segment
            else None
        )

    task = await foh.trip_plan_executor.execute_hotel_plan(
        HotelPlanResponse(**final_response.model_dump(), agent_response=""),
        foh.travel_context.hotel_search_core_criteria.hotel_segments,
        core_search_criteria,
        foh.travel_context.hotel_search_additional_criteria,
        foh.travel_context.hotel_validation_result,
        foh.travel_context.hotel_validation_for_segment,
        hotel_selection or HotelSelectResult(),
        foh.travel_context.selected_hotel_for_segment,
        foh.travel_context.selected_hotel_option_for_segment,
        partial(foh.persist_messages),
        {
            "message_strs": message_buffer_strs,
            "messages": messages,
        },
        on_hotel_validated,
        on_hotel_booked,
        foh.travel_context.is_mobile,
    )

    final_message = ToolMessage(
        content="",
        name=final_response.__class__.__name__,
        tool_call_id=tool_call_id,
        additional_kwargs={
            "agent_classification": AgentTypes.HOTELS,
            "function_call": {
                "name": final_response.__class__.__name__,
                "arguments": final_response.model_dump_json(),
            },
        },
    )
    if foh.email_mode:
        await foh.persist_messages([final_message])
        if task:
            message = await task
            if isinstance(message, AIMessage) or isinstance(message, FunctionMessage):
                step_name = message.additional_kwargs.get("step")
                if step_name == HotelPlanningStep.HOTEL_VALIDATION:
                    user_input = AIHumanMessage(content="looks good, proceed to next step.")
                    await foh.persist_messages([user_input])
                    return await handle_hotel_planner(
                        foh,
                        foh.messages,
                        get_message_buffer_as_strings(foh.messages),
                        travel_context=foh.travel_context,
                        extra_user_input={},
                        prompt_user_preferences=None,
                        streaming=False,
                    )

            return {
                "messages": [message],
                "current_topic": AgentTypes.HOTELS,
                "model": final_response,
            }
    return {"messages": [final_message], "current_topic": AgentTypes.HOTELS, "model": final_response}


async def handle_hotel_planner(
    foh: "fha.FrontOfHouseAgent | TripCoordinatorAgent",
    messages: list[BaseMessage],
    message_buffer_strs: list[str],
    travel_context: TravelContext,
    extra_user_input: dict[str, Any],
    prompt_user_preferences: Callable | None,
    streaming: bool = True,
) -> dict[str, Any]:
    hotel_search_core_criteria_str = travel_context.hotel_search_core_criteria.model_dump_json()
    hotel_search_additional_criteria_str = travel_context.hotel_search_additional_criteria.model_dump_json(
        exclude_none=True
    )
    hotel_select_result_str = travel_context.hotel_select_result.model_dump_json()
    selected_flight_itinerary_str = json.dumps(travel_context.to_selected_flights())

    final_response: HotelPlanResponse | StopResponse
    if streaming:
        final_response = await foh.process_streaming(
            partial(
                b.stream.HotelPlanner,
                travel_preference=foh.user_preferences.model_dump_json(exclude_none=True),
                hotel_search_core_criteria=hotel_search_core_criteria_str,
                hotel_search_additional_criteria=hotel_search_additional_criteria_str,
                selected_flight_itinerary=selected_flight_itinerary_str,
                messages=message_buffer_strs,
                current_date=get_current_date_string(foh.timezone),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                hotel_select_result=hotel_select_result_str,
                user_name=foh.user.name,
            ),
            lambda x: x.agent_response,
            lambda x: x.agent_response,
        )
        if isinstance(final_response, StopResponse):
            return {
                "messages": [AIMessage(content=final_response.last_text or "", additional_kwargs={"is_stopped": True})],
                "current_topic": AgentTypes.HOTELS,
                "model": None,
            }
    else:
        final_response = await b.HotelPlanner(
            travel_preference=foh.user_preferences.model_dump_json(exclude_none=True),
            hotel_search_core_criteria=hotel_search_core_criteria_str,
            hotel_search_additional_criteria=hotel_search_additional_criteria_str,
            selected_flight_itinerary=selected_flight_itinerary_str,
            messages=message_buffer_strs,
            current_date=get_current_date_string(foh.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            hotel_select_result=hotel_select_result_str,
            user_name=foh.user.name,
            web_search_knowledge=travel_context.web_search_knowledge,
            trip_optimization_knowledge=travel_context.trip_optimization_knowledge,
        )

    await _update_hotel_travel_context_from_response(foh, final_response, messages)

    async def on_hotel_validated(
        order_token: str | None,
        validated_price: float | None,
        currency: str | None,
        hotel_validation_for_segment: dict[str, HotelValidationResult] | None,
    ):
        if order_token:
            foh.travel_context.hotel_validation_result.order_token = order_token
        if validated_price:
            foh.travel_context.hotel_validation_result.validated_price = validated_price
        if currency:
            foh.travel_context.hotel_validation_result.currency = currency

        validation_per_segment = None
        if hotel_validation_for_segment:
            foh.travel_context.hotel_validation_for_segment = hotel_validation_for_segment
            validation_per_segment = {}
            for segment_index, hotel_validation_result in hotel_validation_for_segment.items():
                validation_per_segment[segment_index] = hotel_validation_result.model_dump()

        await trip_context_v2_collection.update_one(
            {"thread_id": foh.thread.id},
            {
                "$set": {
                    "hotel_validation_result": foh.travel_context.hotel_validation_result.model_dump(),
                    "hotel_validation_for_segment": validation_per_segment,
                }
            },
            upsert=True,
        )

    async def on_hotel_booked():
        # prompt user to update preferences
        if prompt_user_preferences:
            await prompt_user_preferences()

    core_search_criteria = foh.travel_context.hotel_search_core_criteria
    hotel_selection = foh.travel_context.hotel_select_result
    if foh.travel_context.hotel_search_core_criteria.hotel_segments:
        core_dict = core_search_criteria.model_dump(exclude_none=True)
        core_search_segment_dict = (
            foh.travel_context.hotel_search_core_criteria.hotel_segments[
                final_response.current_searching_segment_index or 0
            ].model_dump(exclude_none=True)
            if foh.travel_context.hotel_search_core_criteria
            and foh.travel_context.hotel_search_core_criteria.hotel_segments
            else {}
        )
        core_dict.update(core_search_segment_dict)

        core_search_criteria = HotelSearchCoreCriteria(**core_dict)
        foh.travel_context.hotel_search_core_criteria = core_search_criteria
        hotel_selection = (
            foh.travel_context.selected_hotel_for_segment.get(str(final_response.current_searching_segment_index or 0))
            if foh.travel_context.selected_hotel_for_segment
            else None
        )

    task = await foh.trip_plan_executor.execute_hotel_plan(
        final_response,
        foh.travel_context.hotel_search_core_criteria.hotel_segments,
        core_search_criteria,
        foh.travel_context.hotel_search_additional_criteria,
        foh.travel_context.hotel_validation_result,
        foh.travel_context.hotel_validation_for_segment,
        hotel_selection or HotelSelectResult(),
        foh.travel_context.selected_hotel_for_segment,
        foh.travel_context.selected_hotel_option_for_segment,
        partial(foh.persist_messages),
        {
            "message_strs": message_buffer_strs,
            "messages": messages,
        },
        on_hotel_validated,
        on_hotel_booked,
        travel_context.is_mobile,
    )

    final_message = FunctionMessage(
        content="",
        name=final_response.__class__.__name__,
        additional_kwargs={
            "agent_classification": AgentTypes.HOTELS,
            "function_call": {
                "name": final_response.__class__.__name__,
                "arguments": final_response.model_dump_json(),
            },
        },
    )
    if foh.email_mode:
        await foh.persist_messages([final_message])
        if task:
            message = await task
            if isinstance(message, AIMessage) or isinstance(message, FunctionMessage):
                step_name = message.additional_kwargs.get("step")
                if step_name == HotelPlanningStep.HOTEL_VALIDATION:
                    user_input = AIHumanMessage(content="looks good, proceed to next step.")
                    await foh.persist_messages([user_input])
                    return await handle_hotel_planner(
                        foh,
                        foh.messages,
                        get_message_buffer_as_strings(foh.messages),
                        travel_context=foh.travel_context,
                        extra_user_input={},
                        prompt_user_preferences=None,
                        streaming=streaming,
                    )

            return {
                "messages": [message],
                "current_topic": AgentTypes.HOTELS,
                "model": final_response,
            }
    return {"messages": [final_message], "current_topic": AgentTypes.HOTELS, "model": final_response}
