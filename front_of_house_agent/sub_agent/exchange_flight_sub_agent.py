import json
from functools import partial
from typing import Any

from langchain_core.messages import (
    AIMessage,
    FunctionMessage,
    ToolMessage,
)

import front_of_house_agent.front_of_house_agent as fha
from agent.agent import StopResponse
from baml_client import b
from baml_client.types import (
    DeltaUpdateOfExchangeFlightConversationStateSchema,
    DeltaUpdateOfExchangeFlightConversationStateSchemaV2,
    ExchangeFlightStep,
)
from flight_agent.flights_helper import FlightBamlHelper
from front_of_house_agent.common_models import EnrichedExchangeFlightState
from server.utils.logger import logger
from server.utils.mongo_connector import trip_context_v2_collection
from server.utils.settings import AgentTypes, settings
from virtual_travel_agent.helpers import (
    get_current_date_string,
)


async def handle_exchange_flight(foh_agent: "fha.FrontOfHouseAgent", message_buffer_strs: list[str]) -> dict[str, Any]:
    existing_legs = None
    exchange_info = None
    trip_id = foh_agent.travel_context.latest_flight_trip_id
    confirmation_id = foh_agent.travel_context.latest_flight_confirmation_id

    if trip_id and confirmation_id:
        try:
            _, existing_legs, exchange_info = await FlightBamlHelper.get_flight_details_and_exchange_info(
                trip_id,
                confirmation_id,
                FlightBamlHelper.get_exchangibiity,
            )
        except Exception as e:
            logger.error(
                "failed_to_get_flight_details_and_exchange_info",
                trip_id=trip_id,
                confirmation_id=confirmation_id,
                error=str(e),
            )

    exchange_agent_reponse: (
        DeltaUpdateOfExchangeFlightConversationStateSchema | StopResponse
    ) = await foh_agent.process_streaming(
        partial(
            b.stream.DoExchangeFlightConversationWithStep,
            messages=message_buffer_strs,
            current_date=get_current_date_string(foh_agent.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            previous_exchange_state=foh_agent.exchange_flight_state.model_dump_json(
                exclude_none=True,
                exclude={
                    "departure_flight_want_change_to",
                    "return_flight_want_change_to",
                    "current_step",
                    "agent_response",
                },
            ),
            user_name=foh_agent.user.name,
            existing_legs=existing_legs,
            exchange_info=json.dumps(exchange_info) if exchange_info else None,
        ),
        lambda x: x.updated_exchange_flight_conversation_state_schema
        and x.updated_exchange_flight_conversation_state_schema.agent_response,
        lambda x: x.updated_exchange_flight_conversation_state_schema.agent_response,
    )

    if isinstance(exchange_agent_reponse, StopResponse):
        return {
            "messages": [
                AIMessage(content=exchange_agent_reponse.last_text or "", additional_kwargs={"is_stopped": True})
            ],
            "current_topic": AgentTypes.EXCHANGE_FLIGHTS,
            "model": None,
        }

    if exchange_agent_reponse.updated_exchange_flight_conversation_state_schema:
        current_exchange_state_dict = foh_agent.exchange_flight_state.model_dump()
        current_exchange_state_dict.update(
            exchange_agent_reponse.updated_exchange_flight_conversation_state_schema.model_dump(exclude_none=True)
        )
        current_exchange_state_dict["flight_confirmation_id"] = (
            foh_agent.travel_context.latest_flight_confirmation_id or ""
        )
        current_exchange_state_dict["flight_trip_id"] = foh_agent.travel_context.latest_flight_trip_id or ""
        foh_agent.exchange_flight_state = EnrichedExchangeFlightState(
            **current_exchange_state_dict,
        )
        if not exchange_agent_reponse.updated_exchange_flight_conversation_state_schema.departure_flight_want_change_to:
            foh_agent.exchange_flight_state.departure_flight_want_change_to = None
        if not exchange_agent_reponse.updated_exchange_flight_conversation_state_schema.return_flight_want_change_to:
            foh_agent.exchange_flight_state.return_flight_want_change_to = None

        await trip_context_v2_collection.update_one(
            {"thread_id": foh_agent.thread.id},
            {"$set": {"exchange_flight_state": foh_agent.exchange_flight_state.model_dump(exclude={"current_step"})}},
            upsert=True,
        )

    async def exchange_flight_search_callback(search_id: str):
        foh_agent.exchange_flight_state.change_flight_search_id = search_id
        await trip_context_v2_collection.update_one(
            {"thread_id": foh_agent.thread.id},
            {"$set": {"exchange_flight_state": foh_agent.exchange_flight_state.model_dump()}},
            upsert=True,
        )

    async def exchange_flight_validation_callback(change_flight_booking_id: str):
        foh_agent.exchange_flight_state.change_flight_booking_id = change_flight_booking_id
        await trip_context_v2_collection.update_one(
            {"thread_id": foh_agent.thread.id},
            {"$set": {"exchange_flight_state": foh_agent.exchange_flight_state.model_dump()}},
            upsert=True,
        )

    await foh_agent.exchange_flight_executor.execute(
        foh_agent.exchange_flight_state,
        partial(foh_agent.persist_messages),
        {
            "message_strs": message_buffer_strs,
            "travel_context": foh_agent.travel_context.flight_search_additional_criteria.model_dump_json(
                exclude_none=True
            )
            + ", "
            + foh_agent.travel_context.flight_search_core_criteria.model_dump_json(exclude_none=True),
        },
        {
            ExchangeFlightStep.CHANGE_OUTBOUND_FLIGHT_SEARCH: exchange_flight_search_callback,
            ExchangeFlightStep.CHANGE_RETURN_FLIGHT_SEARCH: exchange_flight_search_callback,
            ExchangeFlightStep.CHANGE_FLIGHT_VALIDATION: exchange_flight_validation_callback,
        },
    )

    final_message = FunctionMessage(
        content="",
        name=exchange_agent_reponse.__class__.__name__,
        additional_kwargs={
            "agent_classification": AgentTypes.EXCHANGE_FLIGHTS,
            "function_call": {
                "name": exchange_agent_reponse.__class__.__name__,
                "arguments": foh_agent.exchange_flight_state.model_dump_json(),
            },
        },
    )
    return {"messages": [final_message], "current_topic": AgentTypes.EXCHANGE_FLIGHTS, "model": exchange_agent_reponse}


async def handle_exchange_flight_v2(
    foh_agent: "fha.FrontOfHouseAgent", message_buffer_strs: list[str], tool_call_id: str
) -> dict[str, Any]:
    existing_legs = None
    exchange_info = None
    trip_id = foh_agent.travel_context.latest_flight_trip_id
    confirmation_id = foh_agent.travel_context.latest_flight_confirmation_id

    if trip_id and confirmation_id:
        try:
            _, existing_legs, exchange_info = await FlightBamlHelper.get_flight_details_and_exchange_info(
                trip_id,
                confirmation_id,
                FlightBamlHelper.get_exchangibiity,
            )
        except Exception as e:
            logger.error(
                f"Failed to get flight details and exchange info for trip {trip_id} with confirmation id {confirmation_id}, error: {e}"
            )

    exchange_agent_reponse: DeltaUpdateOfExchangeFlightConversationStateSchemaV2 = (
        await b.DoExchangeFlightConversationWithStepV2(
            messages=message_buffer_strs,
            current_date=get_current_date_string(foh_agent.timezone),
            previous_exchange_state=foh_agent.exchange_flight_state.model_dump_json(
                exclude_none=True,
                exclude={
                    "departure_flight_want_change_to",
                    "return_flight_want_change_to",
                    "current_step",
                    "agent_response",
                },
            ),
            existing_legs=existing_legs,
            exchange_info=json.dumps(exchange_info) if exchange_info else None,
        )
    )

    if exchange_agent_reponse.updated_exchange_flight_conversation_state_schema:
        current_exchange_state_dict = foh_agent.exchange_flight_state.model_dump()
        current_exchange_state_dict.update(
            exchange_agent_reponse.updated_exchange_flight_conversation_state_schema.model_dump(exclude_none=True)
        )
        current_exchange_state_dict["flight_confirmation_id"] = (
            foh_agent.travel_context.latest_flight_confirmation_id or ""
        )
        current_exchange_state_dict["flight_trip_id"] = foh_agent.travel_context.latest_flight_trip_id or ""
        foh_agent.exchange_flight_state = EnrichedExchangeFlightState(
            **current_exchange_state_dict,
        )
        if not exchange_agent_reponse.updated_exchange_flight_conversation_state_schema.departure_flight_want_change_to:
            foh_agent.exchange_flight_state.departure_flight_want_change_to = None
        if not exchange_agent_reponse.updated_exchange_flight_conversation_state_schema.return_flight_want_change_to:
            foh_agent.exchange_flight_state.return_flight_want_change_to = None

        await trip_context_v2_collection.update_one(
            {"thread_id": foh_agent.thread.id},
            {"$set": {"exchange_flight_state": foh_agent.exchange_flight_state.model_dump(exclude={"current_step"})}},
            upsert=True,
        )

    async def exchange_flight_search_callback(search_id: str):
        foh_agent.exchange_flight_state.change_flight_search_id = search_id
        await trip_context_v2_collection.update_one(
            {"thread_id": foh_agent.thread.id},
            {"$set": {"exchange_flight_state": foh_agent.exchange_flight_state.model_dump()}},
            upsert=True,
        )

    async def exchange_flight_validation_callback(change_flight_booking_id: str):
        foh_agent.exchange_flight_state.change_flight_booking_id = change_flight_booking_id
        await trip_context_v2_collection.update_one(
            {"thread_id": foh_agent.thread.id},
            {"$set": {"exchange_flight_state": foh_agent.exchange_flight_state.model_dump()}},
            upsert=True,
        )

    await foh_agent.exchange_flight_executor.execute(
        foh_agent.exchange_flight_state,
        partial(foh_agent.persist_messages),
        {
            "message_strs": message_buffer_strs,
            "travel_context": foh_agent.travel_context.flight_search_additional_criteria.model_dump_json(
                exclude_none=True
            )
            + ", "
            + foh_agent.travel_context.flight_search_core_criteria.model_dump_json(exclude_none=True),
        },
        {
            ExchangeFlightStep.CHANGE_OUTBOUND_FLIGHT_SEARCH: exchange_flight_search_callback,
            ExchangeFlightStep.CHANGE_RETURN_FLIGHT_SEARCH: exchange_flight_search_callback,
            ExchangeFlightStep.CHANGE_FLIGHT_VALIDATION: exchange_flight_validation_callback,
        },
    )

    final_message = ToolMessage(
        content="",
        name=exchange_agent_reponse.__class__.__name__,
        tool_call_id=tool_call_id,
        additional_kwargs={
            "agent_classification": AgentTypes.EXCHANGE_FLIGHTS,
            "function_call": {
                "name": exchange_agent_reponse.__class__.__name__,
                "arguments": foh_agent.exchange_flight_state.model_dump_json(),
            },
        },
    )
    return {"messages": [final_message], "current_topic": AgentTypes.EXCHANGE_FLIGHTS, "model": exchange_agent_reponse}
