# This file is for trip planning prototyping and is not used in production.
from functools import partial
from typing import TYPE_CHECKING, Any, Callable

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    FunctionMessage,
)

from front_of_house_agent.back_of_house_executor.flight_and_hotel_executor import TripPlanExecutor
from server.database.models.chat_thread import Chat<PERSON>hread

if TYPE_CHECKING:
    pass
from agent.agent import StopResponse
from baml_client import b
from baml_client.types import (
    BuyAction,
    FlightPlanningStep,
    FlightPlanResponse,
    FlightSearchAdditionalCriteria,
    FlightSearchCoreCriteria,
    FlightType,
    ResponseAllPreferences,
)
from front_of_house_agent.common_models import (
    EnrichedFlightSelectResult,
    FlightOption,
    FlightSearchSource,
    SeatSelectionForFlight,
    TravelContext,
)
from llm_utils.llm_utils import (
    get_flight_detail_from_history,
)
from server.database.models.user import User as DBUser
from server.schemas.authenticate.user import User
from server.services.feature_flags.feature_flag import FeatureFlags, is_feature_flag_enabled
from server.services.trips.flight_credits_api import flight_credits_api
from server.services.user_profile.loyalty_programs import (
    get_user_profile_flights_loyalty_programs,
    update_user_profile_flights_loyalty_programs,
)
from server.utils.mongo_connector import trip_context_v2_collection
from server.utils.settings import AgentTypes, settings
from virtual_travel_agent.helpers import (
    get_current_date_string,
)


class FlightWorker:
    def __init__(self, trip_plan_executor: TripPlanExecutor):
        self.trip_plan_executor = trip_plan_executor

    async def execute_flight_booking_workflow(
        self,
        travel_context: TravelContext,
        user_preferences: ResponseAllPreferences,
        messages: list[BaseMessage],
        message_buffer_strs: list[str],
        extra_user_input: dict[str, Any],
        persist_messages: Callable[[list[BaseMessage]], Any],
    ):
        """Execute flight booking workflow using existing TripPlanExecutor."""
        if not travel_context or not travel_context.flight_select_result:
            error_message = AIMessage(
                content="No flight selection found. Please select a flight first through trip planning.",
                additional_kwargs={"agent_classification": AgentTypes.FOH},
            )
            return error_message

        return await self.handle_flight_planner(
            travel_context,
            user_preferences,
            self.trip_plan_executor.timezone,
            self.trip_plan_executor.user,
            messages,
            message_buffer_strs,
            extra_user_input,
            flight_search_core_criteria_str=travel_context.flight_search_core_criteria.model_dump_json()
            if travel_context and travel_context.flight_search_core_criteria
            else "{}",
            flight_search_additional_criteria_str="{}",
            flight_select_result_str="{}",
            thread=self.trip_plan_executor.thread,
            persist_messages=persist_messages,
            prompt_user_preferences=None,
        )

    async def handle_flight_planner(
        self,
        travel_context: TravelContext,
        user_preferences: ResponseAllPreferences,
        timezone: str | None,
        user: User,
        messages: list[BaseMessage],
        message_buffer_strs: list[str],
        extra_user_input: dict[str, Any],
        flight_search_core_criteria_str: str,
        flight_search_additional_criteria_str: str,
        flight_select_result_str: str,
        prompt_user_preferences: Callable | None,
        thread: ChatThread,
        persist_messages: Callable[[list[BaseMessage]], Any],
    ):
        has_credits_for_selected_airline = False
        airline_codes = []

        if travel_context.selected_outbound_flight:
            for stop in travel_context.selected_outbound_flight.stops:
                airline_codes.append(stop.airline_code)

            if travel_context.selected_return_flight:
                for stop in travel_context.selected_return_flight.stops:
                    airline_codes.append(stop.airline_code)
            if travel_context.selected_flight_for_segment:
                for segment in travel_context.selected_flight_for_segment.values():
                    for stop in segment.stops:
                        airline_codes.append(stop.airline_code)
            if airline_codes:
                user_unused_credits = await flight_credits_api.get_user_unused_credits(user.email)
                for credit in user_unused_credits:
                    credit_airline_code = credit.get("extra", {}).get("airlineInfo", {}).get("airlineCode", "")
                    if credit_airline_code in airline_codes:
                        has_credits_for_selected_airline = True
                        break

        pay_baggage_preference = None
        baggage_check_enabled = await is_feature_flag_enabled(user.id, FeatureFlags.ENABLE_BAGGAGE_CHECK)
        if baggage_check_enabled:
            pay_baggage_preference = (
                user_preferences.should_buy_luggage.value if user_preferences.should_buy_luggage else None
            )
        else:
            pay_baggage_preference = BuyAction.NEVER.value

        final_response: FlightPlanResponse | StopResponse

        final_response = await b.FlightPlanner(
            travel_preference=user_preferences.model_dump_json(
                exclude_none=True, exclude={k for k, v in user_preferences.model_dump().items() if v == []}
            ),
            flight_search_core_criteria=flight_search_core_criteria_str,
            flight_search_additional_criteria=flight_search_additional_criteria_str,
            messages=message_buffer_strs,
            current_date=get_current_date_string(timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            flight_select_result=flight_select_result_str,
            has_credit=has_credits_for_selected_airline,
            pay_baggage=pay_baggage_preference,
            user_name=user.name,
        )

        if (
            final_response.updated_flight_select_result
            and final_response.updated_flight_select_result.frequent_flier_number
        ):
            user_profile_flights_loyalty_programs = await get_user_profile_flights_loyalty_programs(user.id) or {}
            ffns = user_profile_flights_loyalty_programs.get("loyaltyPrograms") or []
            ffns = [] if ffns is None else ffns
            stored_airline_codes = {ff["IATACode"] for ff in ffns if ff.get("IATACode")}

            for ffn in final_response.updated_flight_select_result.frequent_flier_number:
                if ffn.airline_code not in stored_airline_codes:
                    ffns.append({"IATACode": ffn.airline_code, "number": ffn.number})

            user_profile_flights_loyalty_programs["loyaltyPrograms"] = ffns
            await update_user_profile_flights_loyalty_programs(user_profile_flights_loyalty_programs, user.id)

        if final_response.updated_flight_search_core_criteria:
            flight_core_dict = travel_context.flight_search_core_criteria.model_dump()
            flight_core_dict.update(final_response.updated_flight_search_core_criteria.model_dump(exclude_none=True))
            travel_context.flight_search_core_criteria = FlightSearchCoreCriteria(**flight_core_dict)

        if final_response.updated_flight_search_additional_criteria:
            flight_additional_dict = travel_context.flight_search_additional_criteria.model_dump(exclude_none=True)
            flight_additional_dict.update(
                final_response.updated_flight_search_additional_criteria.model_dump(exclude_none=True)
            )
            travel_context.flight_search_additional_criteria = FlightSearchAdditionalCriteria(**flight_additional_dict)

        if final_response.user_responsed_entry_requirment:
            travel_context.user_responsed_entry_requirment = final_response.user_responsed_entry_requirment

        if final_response.updated_ctizenship_info:
            citizenships_to_update = final_response.updated_ctizenship_info
            current_citizenship = user.citizenship or []
            for citizenship_to_update in citizenships_to_update or []:
                if citizenship_to_update not in current_citizenship:
                    current_citizenship.append(citizenship_to_update or "")
            user.citizenship = current_citizenship
            await DBUser.update_citizenship(user.id, current_citizenship)

            travel_context.user_provided_citizenship = final_response.updated_ctizenship_info

        if final_response.updated_flight_select_result:
            if travel_context.flight_search_core_criteria.flight_type == FlightType.OneWay:
                travel_context.selected_return_flight = None
            flight_select_result_dict = travel_context.flight_select_result.model_dump()
            flight_select_result_dict.update(final_response.updated_flight_select_result.model_dump(exclude_none=True))
            if final_response.current_step == FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH:
                flight_select_result_dict["selected_return_flight_id"] = None
                flight_select_result_dict["selected_outbound_flight_id"] = None
            elif final_response.current_step == FlightPlanningStep.RETURN_FLIGHT_SEARCH:
                flight_select_result_dict["selected_return_flight_id"] = None
            associated_search_id = None
            associated_search_source = None
            # FE will send the selected flight id to BE, so we need to update the selected flight id in the response
            # It is to handle the case that sometimes LLM will inferred wrong flgiht id.
            if extra_user_input.get(
                "return_flight_id"
            ) and final_response.updated_flight_select_result.selected_return_flight_id != extra_user_input.get(
                "return_flight_id"
            ):
                final_response.updated_flight_select_result.selected_return_flight_id = extra_user_input.get(
                    "return_flight_id"
                )
            if extra_user_input.get(
                "outbound_flight_id"
            ) and final_response.updated_flight_select_result.selected_outbound_flight_id != extra_user_input.get(
                "outbound_flight_id"
            ):
                final_response.updated_flight_select_result.selected_outbound_flight_id = extra_user_input.get(
                    "outbound_flight_id"
                )

            flight_id_to_find_search_id = (
                final_response.updated_flight_select_result.selected_outbound_flight_id
                if FlightPlanningStep.RETURN_FLIGHT_SEARCH in (final_response.current_step or [])
                else final_response.updated_flight_select_result.selected_return_flight_id
                or final_response.updated_flight_select_result.selected_outbound_flight_id
            )

            if flight_id_to_find_search_id:
                _, associated_search_id, associated_search_source = get_flight_detail_from_history(
                    messages, flight_id_to_find_search_id
                )
            travel_context.flight_select_result = EnrichedFlightSelectResult(**flight_select_result_dict)
            if associated_search_id:
                travel_context.flight_select_result.search_id = associated_search_id
            if associated_search_source:
                travel_context.flight_select_result.search_source = FlightSearchSource(associated_search_source)
            if final_response.updated_flight_select_result.selected_outbound_flight_id:
                candidate, _, _ = get_flight_detail_from_history(
                    messages, final_response.updated_flight_select_result.selected_outbound_flight_id
                )
                if candidate:
                    travel_context.selected_outbound_flight = FlightOption.from_fe_display_dict(candidate)

            if final_response.updated_flight_select_result.selected_return_flight_id:
                candidate, _, _ = get_flight_detail_from_history(
                    messages, final_response.updated_flight_select_result.selected_return_flight_id
                )
                if candidate:
                    travel_context.selected_return_flight = FlightOption.from_fe_display_dict(candidate)

            for segment_selection in final_response.updated_flight_select_result.selected_flight_for_segment or []:
                flight_id_to_search = segment_selection.selected_flight_id

                if flight_id_to_search:
                    candidate, _, _ = get_flight_detail_from_history(messages, flight_id_to_search)
                    if candidate:
                        if not travel_context.selected_flight_for_segment:
                            travel_context.selected_flight_for_segment = {}
                        travel_context.selected_flight_for_segment[str(segment_selection.segment_index)] = (
                            FlightOption.from_fe_display_dict(candidate)
                        )
        if final_response.seat_selections:
            travel_context.flight_seat_selection = final_response.seat_selections or []
        if final_response.special_service_requests:
            travel_context.special_service_requests = final_response.special_service_requests

        await self.persist_travel_context(thread.id, travel_context)

        async def handle_search(search_id: str, flight_source: FlightSearchSource | None):
            travel_context.flight_select_result.search_id = search_id
            travel_context.flight_select_result.search_source = flight_source
            await trip_context_v2_collection.update_one(
                {"thread_id": thread.id},
                {"$set": {"flight_select_result": travel_context.flight_select_result.model_dump()}},
                upsert=True,
            )

        async def handle_flight_checkout(
            search_id: str | None,
            iterinary_id: str | None,
            selected_seat_to_store: list[SeatSelectionForFlight],
        ):
            travel_context.flight_seat_selection = selected_seat_to_store
            if search_id:
                travel_context.flight_select_result.search_id = search_id
            if iterinary_id:
                travel_context.flight_select_result.matched_flight_id = iterinary_id

            await trip_context_v2_collection.update_one(
                {"thread_id": thread.id},
                {
                    "$set": {
                        "flight_select_result": travel_context.flight_select_result.model_dump(),
                    }
                },
                upsert=True,
            )

        async def handle_booking(trip_id: str, confirmation_id: str, confirmation_number: str):
            travel_context.latest_flight_trip_id = trip_id
            travel_context.latest_flight_confirmation_id = confirmation_id
            travel_context.latest_flight_airline_confirmation_number = confirmation_number

            await trip_context_v2_collection.update_one(
                {"thread_id": thread.id},
                {
                    "$set": {
                        "flight_trip_id": travel_context.latest_flight_trip_id,
                        "flight_confirmation_id": travel_context.latest_flight_confirmation_id,
                        "flight_airline_confirmation_number": travel_context.latest_flight_airline_confirmation_number,
                    }
                },
                upsert=True,
            )
            if prompt_user_preferences:
                await prompt_user_preferences()

        callback_handlers = {
            FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH.name: handle_search,
            FlightPlanningStep.RETURN_FLIGHT_SEARCH.name: handle_search,
            FlightPlanningStep.MULTI_LEG_FLIGHT_SEARCH.name: handle_search,
            FlightPlanningStep.FLIGHT_CHECKOUT.name: handle_flight_checkout,
            FlightPlanningStep.FLIGHT_BOOKING.name: handle_booking,
        }

        core_criteria = travel_context.flight_search_core_criteria
        if travel_context.flight_search_core_criteria.flight_type == FlightType.MultiLegs:
            core_dict = core_criteria.model_dump(exclude_none=True)
            core_dict.update(
                travel_context.flight_search_core_criteria.flight_segments[
                    final_response.current_searching_segment_index or 0
                ].model_dump(exclude_none=True)
                if travel_context.flight_search_core_criteria
                and travel_context.flight_search_core_criteria.flight_segments
                else {}
            )
            if travel_context.flight_search_core_criteria.flight_type:
                core_dict["flight_type"] = travel_context.flight_search_core_criteria.flight_type
            if travel_context.flight_search_core_criteria.is_international_flight_trip:
                core_dict["is_international_flight_trip"] = (
                    travel_context.flight_search_core_criteria.is_international_flight_trip
                )

            core_criteria = FlightSearchCoreCriteria(**core_dict)
            travel_context.flight_search_core_criteria = core_criteria

        task = await self.trip_plan_executor.execute_flight_plan(
            final_response,
            travel_context,
            partial(persist_messages),
            {
                "message_strs": message_buffer_strs,
                "preferred_seat_types": travel_context.flight_search_additional_criteria.seat_types
                or user_preferences.preferred_seats,
                "messages": messages,
            },
            callback_handlers,
        )
        if task:
            message = await task
        else:
            message = FunctionMessage(
                content="",
                name=final_response.__class__.__name__,
                additional_kwargs={
                    "agent_classification": AgentTypes.FLIGHTS,
                    "function_call": {
                        "name": final_response.__class__.__name__,
                        "arguments": final_response.model_dump_json(),
                    },
                },
            )

        return message

    async def persist_travel_context(self, thread_id: int, travel_context: TravelContext):
        """Persist the travel context to the database."""
        await trip_context_v2_collection.update_one(
            {"thread_id": thread_id},
            {
                "$set": {
                    "thread_id": thread_id,
                    **travel_context.model_dump(exclude_none=True),
                }
            },
            upsert=True,
        )
