name: PR Checks
run-name: PR Checks - ${{ github.event.pull_request.title }}

on:
  pull_request:
    branches: [development, main]

jobs:
  pr-checks:
    name: PR Checks
    runs-on: ubuntu-latest

    services:
      mongodb:
        image: mongo:5.0
        ports:
          - 27017:27017
        env:
          MONGO_INITDB_ROOT_USERNAME: testuser
          MONGO_INITDB_ROOT_PASSWORD: testpass
        options: >-
          --health-cmd "mongosh --eval 'db.runCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true

      - name: Install dependencies
        run: uv sync

      - name: Create test .env file
        run: |
          cat > .env << EOF
          OTTO_ENV=${{ vars.OTTO_ENV }}
          OTTO_APP_SECRET=${{ secrets.OTTO_APP_SECRET }}
          PG_USER=postgres
          PG_PASSWORD=
          PG_HOST=localhost
          PG_PORT=5432
          PG_DATABASE=test
          MONGO_USER=testuser
          MONGO_PASSWORD=testpass
          MONGO_HOST=localhost
          MONGO_PORT=27017
          MONGO_DATABASE=test
          AWS_DEFAULT_REGION=us-east-1
          EOF

      - name: Generate BAML client
        run: uv run baml-cli generate

      - name: Run Pyright
        run: uv run pyright .

      - name: Run unit tests
        run: uv run pytest tests/ -m "not manual"
        env:
          PYTHONPATH: .
