
class CancelOptionResponse {
  agent_response string
  refund_type string? @description(#"refund in cash or flight credits."#)
}

class CancelFlightConversationStateSchemaWithStep {
  airline_confirmation_number string? @description("The airline confirmation number of the already booked flight.")
  airline_confirmation_id string? @description("The airline confirmation ID of the already booked flight.")
  cancel_option_id string? @description("The cancel option id selected by the traveler.")
}

class CancelHotelResponseWithStep {
  hotel_cancel_reason string? @description("The reason why user want to cancel the hotel.")

  order_number string? @description("The order number or order id of the already booked hotel.") 
}

enum CancellationTaskStatus {
  TODO @description("Task is pending and needs to be verified.")
  APPROVED @description("User has agreed to cancel the booking.")
  REJECTED @description("User has rejected the cancellation request.")
  DONE @description("Task is completed, either canceled or not canceled.")
}

class CancellationTask {
    status CancellationTaskStatus
    type string @description("`flight` or `hotel`")
    flight_info CancelFlightConversationStateSchemaWithStep?
    hotel_info CancelHotelResponseWithStep?
}

class CancellationPlan {
    tasks CancellationTask[]?
    index int? @description(#"Currently working on task index, starting from 0."#)
    finished bool
    agent_response string
}

function PlanForCancellation(bookings: string, messages: string[], self_intro: string?, convo_style: string?, user_name: string) -> CancellationPlan {
    client GPT41
    prompt #"

{{ ConversationHistory(messages, 0) }}

# Role
{{ self_intro | default(GetSelfIntro()) }}

# Background:
- {{ convo_style | default(GetConvoStyle()) }}

# Goal
Create and maintain a cancellation task list for travel bookings, ensuring each task's status is up-to-date.

# Rules
1. Identify all bookings, tickets, or reservations related to the trip. Reading from Bookings section.
2. Compile these into a task list and plan to complete them.
3. For each task:
    - Complete tasks in two steps: verifying and submitting cancellation.
    - Infer the status from the chat history.
    - Start all tasks with the status `todo`, and go to verify if the booking can be canceled.
    - If the user agrees to cancel the booking after verifying, mark the task `approved`.
    - If the task has been submitted or has been sent to the agent, consider it done.
    - If the order cannot be canceled, or already canceled, consider it done.
    - If the task is successful or failed, consider it done.
4. Inform the user which task you're working on.
5. When all user-required tasks are done, suggest canceling other related bookings that can be canceled, ask the user if they want to cancel them.

# Constraints
- If there's no information for the cancellation item, do NOT generate a task.
- Do not repeat the cancellation details.
- Process one task at a time.
- If there are multiple items to cancel, prioritize canceling the flight.
- If there are no bookings left to cancel, inform the user that the whole trip has been canceled.
- If there are no bookings to cancel, please ask the user if they can provide flight confirmation number or hotel order number to proceed with the cancellation.

# Bookings
{{ bookings }}
---

{{ ctx.output_format }}
"#
}


function ProcessCancelOptionResults(
  cancel_state: string,
  cancel_options: string,
  cancel_details: string,
  current_date: string,
  self_intro: string?,
  convo_style: string?) -> CancelOptionResponse {
  client GPT4o
  prompt #"

    # Role
    {{ self_intro | default(GetSelfIntro()) }}
    
    # Background
    - {{ convo_style | default(GetConvoStyle()) }}
    - Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.

    # Goal
    You are tasked with processing the cancellation options for the traveler's flight booking based on the provided options.

    # Cancellation state
    {{ cancel_state }}

    # Cancellation options
    {{ cancel_options }}

    # Cancellation details
    {{ cancel_details }}

    # Rules
    1. If the ticket cannot be canceled, inform the traveler and tell them the reason.
    2. If the ticket can be canceled, provide the refund amount and type, include any cancellation fees, and ask the traveler if they would like to proceed with the cancellation.
    3. The cancellation options are provided in the `cancel_options` variable.
    4. Use the provided `cancel_details` to give context about the trip.
    
    Here are the possible cancellation statuses:
    ============================================
    NON_CANCELLABLE: Booking is non-cancellable.
    CANCELLATION_IN_PROGRESS: Cancellation request is in progress via OBT.
    CANCELLATION_BY_AGENT_REQUESTED: Cancellation request is in progress via agent.
    CANCELLED: Already cancelled.
    CANCELLATION_INFO_NOT_AVAILABLE: Cancellation information not available.
    ============================================

    # Cancellation Not Supported Reasons
    - If the cancellation is not supported, provide the reason in `cancellationNotSupportedReasons`.
    - If the reason is `CURRENT_TIME_CLOSE_TO_FLIGHT_DEPARTURE`, respond with: I'm sorry, but since the flight is departing soon, you will have to contact the airline to cancel the flight.


    {{ ctx.output_format }}
  "#
}


class CancelResponse {
  agent_response string @description(#"
    Text for summary of the cancellation result.
  "#)
}

function ProcessCancelResults(
  messages: string[],
  status: string,
  details: string,
  travel_context: string,
  current_date: string,
  self_intro: string?,
  convo_style: string?) -> CancelResponse {
  client GPT4o
  prompt #"
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.
    
    Your task is to process the cancellation results based on the provided status and details.
    If the cancellation has failed, inform the traveler about the failure and provide the details.
    
    If the status is AGENT_TASK_CREATED, inform the traveler that the cancellation request has been sent to the agent.
    If the status is CANCELLED:
      - Inform the traveler that the booking has been successfully canceled.
      - If you find "createdUnusedCredit" in the details, extract the credit amount, airline company, and expiration date. Inform the traveler about the unused credit.
      - If you don't find "createdUnusedCredit" in the details, it means the traveler will receive a monetary refund. Inform the traveler that the refund will be processed within 7 days.
    
    Cancellation Status: {{ status }}

    Trip Details: 
    {{ details }}

    Provide a summary of the cancellation result to the traveler.
    
    Example: Your flight has been canceled, and a refund of $300 will be processed within 7 days.
    Example: Your flight has been canceled, and a travel credit of $350 has been issued by Delta. It has been saved to your profile and will expire on March 15, 2025.
    Example: The cancellation request has been sent to the agent, and a refund of $300 will be processed within 7 days.
    Example: The cancellation request has been sent to the agent, and you'll receive a travel credit of $350 for future use.
    Example: Your cancellation request has been submitted, please check back in a few hours. You will receive an email when the booking is successfully canceled.

    ---
    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}
