client<llm> GPT4o {
  provider fallback
  retry_policy RapidRetryPolicy
  options {
    strategy [
      OpenaiGPT41
      OpenaiGPT4o
      AzureGPT4o
    ]
  }
}

client<llm> GPT41 {
  provider fallback
  retry_policy RapidRetryPolicy
  options {
    strategy [
      OpenaiGPT41
      AzureGPT41
    ]
  }
}

client<llm> GPT4o_AzureFirst {
  provider fallback
  retry_policy RapidRetryPolicy
  options {
    strategy [
      AzureGPT4o
      OpenaiGPT41
      OpenaiGPT4o
    ]
  }
}

client<llm> GPT4oMini {
  provider fallback
  retry_policy RapidRetryPolicy
  options {
    strategy [
      OpenaiGPT4oMini
      AzureGPT4oMini
    ]
  }
}

client<llm> GPT4oMini_AzureFirst {
  provider fallback
  retry_policy RapidRetryPolicy
  options {
    strategy [
      AzureGPT4oMini
      OpenaiGPT4oMini
    ]
  }
}

client<llm> OpenaiGPT4o {
  provider openai
  options {
    model "gpt-4o"
    api_key env.OPENAI_API_KEY
    temperature 0.0
  }
}

client<llm> OpenaiGPT4oMini {
  provider openai
  options {
    model "gpt-4o-mini"
    api_key env.OPENAI_API_KEY
    temperature 0.0
  }
}

client<llm> Openai_o1 {
  provider openai
  options {
    model "o1-2024-12-17"
    api_key env.OPENAI_API_KEY
  }
}

client<llm> Openai_o1Mini {
  provider openai
  options {
    model "o1-mini-2024-09-12"
    api_key env.OPENAI_API_KEY
  }
}

client<llm> OpenaiO3Mini {
  provider openai
  options {
    model "o3-mini"
    api_key env.OPENAI_API_KEY
  }
}

client<llm> OpenaiGPT41 {
  provider openai
  options {
    model "gpt-4.1-2025-04-14"
    api_key env.OPENAI_API_KEY
    temperature 0
  }
}

client<llm> OpenaiGPT4OSearch {
  provider openai
  options {
    model "gpt-4o-search-preview"
    api_key env.OPENAI_API_KEY
    web_search_options {}
  }
}

client<llm> AzureGPT41 {
  provider azure-openai
  options {
    resource_name "cd-m33l3g17-eastus2"
    deployment_id "gpt-4.1"
    api_version "2025-01-01-preview"
    temperature 0
    headers {
      api-key env.AZURE_EASTUS_OPENAI_API_KEY
    }
  }
}


client<llm> AzureGPT4o {
  provider azure-openai
  options {
    resource_name "otto-alpha"
    deployment_id "gpt-4o"
    api_version "2024-08-01-preview"
    temperature 0
    headers {
      api-key env.AZURE_OPENAI_API_KEY
    }
  }
}

client<llm> AzureGPT4oMini {
  provider azure-openai
  options {
    resource_name "otto-alpha"
    deployment_id "gpt-4o-mini"
    api_version "2024-08-01-preview"
    temperature 0.0
    headers {
      api-key env.AZURE_OPENAI_API_KEY
    }
  }
}

client<llm> AzureGPT4oForTest {
  provider azure-openai
  retry_policy FullRetryPolicy
  options {
    resource_name "otto-alpha"
    deployment_id "gpt-4o"
    api_version "2024-08-01-preview"
    temperature 0.7
    headers {
      api-key env.AZURE_OPENAI_API_KEY
    }
  }
}

retry_policy FullRetryPolicy {
  max_retries 3
  strategy {
    type exponential_backoff
    delay_ms 1000
  }
}

retry_policy RapidRetryPolicy {
  max_retries 3
  strategy {
    type constant_delay
    delay_ms 200
  }
}


client<llm> GeminiPro {
  provider google-ai
  options {
    model "gemini-2.5-pro"
    api_key env.GOOGLE_GEMINI_API_KEY
  }
}

client<llm> GeminiFlash {
  provider google-ai
  options {
    model "gemini-2.5-flash"
    api_key env.GOOGLE_GEMINI_API_KEY
  }
}

// All Aws clients requires AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY and AWS_REGION
client<llm> AwsSonnet {
  provider aws-bedrock
  options {
    model_id "arn:aws:bedrock:us-east-2:891377138838:inference-profile/us.anthropic.claude-3-5-sonnet-20240620-v1:0"
  }
}

client<llm> AwsHaiku {
  provider aws-bedrock
  options {
    model_id "arn:aws:bedrock:us-east-2:891377138838:inference-profile/us.anthropic.claude-3-haiku-20240307-v1:0"
  }
}

client<llm> AwsLlama32_3B {
  provider aws-bedrock
  options {
    model_id "arn:aws:bedrock:us-east-2:891377138838:inference-profile/us.meta.llama3-2-3b-instruct-v1:0"
  }
}

client<llm> AwsLlama32_90B {
  provider aws-bedrock
  options {
    model_id "arn:aws:bedrock:us-east-2:891377138838:inference-profile/us.meta.llama3-2-90b-instruct-v1:0"
  }
}

client OpenRounter_DeepseekR1 {
  provider openai-generic
  options {
    model "deepseek/deepseek-r1"
    base_url "https://openrouter.ai/api/v1"
    api_key env.OPENROUTER_API_KEY
  }
}

client<llm> OpenRounter_Deepseekv3 {
  provider openai-generic
  options {
    model "deepseek/deepseek-chat"
    base_url "https://openrouter.ai/api/v1"
    api_key env.OPENROUTER_API_KEY
  }
}

client<llm> ClaudeFirst {
  provider fallback
  retry_policy RapidRetryPolicy
  options {
    strategy [
      Sonnet37
      OpenaiGPT4o
    ]
  }
}

client<llm> Haiku35 {
  provider anthropic
  options {
    model "claude-3-5-haiku-20241022"
    temperature 0
    api_key env.ANTHROPIC_API_KEY
  }
}

client<llm> Sonnet35 {
  provider anthropic
  options {
    model claude-3-5-sonnet-20240620
    // model "claude-3-5-sonnet-20241022"
    temperature 0
    api_key env.ANTHROPIC_API_KEY
  }
}

client<llm> Sonnet37 {
  provider anthropic
  options {
    model "claude-3-7-sonnet-20250219"
    temperature 0
    api_key env.ANTHROPIC_API_KEY
  }
}

// Not tested yet
client<llm> HuggingFace {
  provider openai-generic
  options {
    base_url "https://api-inference.huggingface.co/v1"
    api_key env.HUGGINGFACE_API_KEY
  }
}
