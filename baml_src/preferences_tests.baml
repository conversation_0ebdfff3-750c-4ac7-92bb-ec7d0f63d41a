test mg_calendar_preferences_analysis {
  functions [ConversePreferences]
  args {
    existing_preference null
    messages []
    prompt #"

    Role: Travel Agent

    Goal: Help the traveler express their travel preferences.

    Background:
        - You are an expert travel agent helping a business traveler you have never met before.
        - You have looked at all of the travelers calendar events for the last 2 years, focusing on events associated with their business travel (e.g., flight bookings, hotels stays, etc.)
        - You will use this preferences analysis to gather feedback from the traveler on the quality of the analysis and whether they have any changes, additions or deletions thay want to make.

    Procedure:
        - Present your current understanding of the traveler's preferences (including the categories that contain no information) as a bulleted list - 1 bullet • per preference category.
        - If we have no preferences gathered from calendar events, immediately ask the traveler for their preferences: "It seems we don't have any preferences gathered from your calendar events. I’ll just ask you a few quick questions so I can learn about your travel style and preferences. Let’s get started. First, do you have preferred home airport that you usually fly out of?".
        - Each time the traveler provides you with new information or feedback, acknowledge the details of the information in a friendly manner.
        - Remind the traveler: "\n\n Are you finished updating your preferences?"

    Handling Off-Topic Queries:
    - For ANY unrelated questions (e.g., poems, weather, general chat):
      • Response: "I'm your travel assistant focused on helping with travel arrangements. Let's stay focused on collecting your travel preferences.
    - Never generate content unrelated to travel booking services        

"#
    cal_events #"[{"summary": "Flight to Kahului (DL 481)", "description": "", "location": "Seattle SEA", "start": {"dateTime": "2023-02-18T15:47:00.0000000"}, "end": {"dateTime": "2023-02-18T22:06:00.0000000"}, "is_new": false}, {"summary": "Flight to Los Angeles (UA 706)", "description": "", "location": "Kahului OGG", "start": {"dateTime": "2023-02-23T16:35:00.0000000"}, "end": {"dateTime": "2023-02-23T21:51:00.0000000"}, "is_new": false}, {"summary": "Flight DL1569", "description": "Confirmation #: GVZ8LX", "location": "SEA \u25b8 LAX", "start": {"dateTime": "2023-02-23T17:32:00.0000000"}, "end": {"dateTime": "2023-02-23T20:10:00.0000000"}, "is_new": false}, {"summary": "Flight to Seattle (DL 1714)", "description": "", "location": "Los Angeles LAX", "start": {"dateTime": "2023-02-26T09:57:00.0000000"}, "end": {"dateTime": "2023-02-26T12:55:00.0000000"}, "is_new": false}, {"summary": "Flight DL1714", "description": "Confirmation #: GVZ8LX", "location": "LAX \u25b8 SEA", "start": {"dateTime": "2023-02-26T09:57:00.0000000"}, "end": {"dateTime": "2023-02-26T12:55:00.0000000"}, "is_new": false}, {"summary": "Stay at Venetian tower", "description": "", "location": "Paradise, Nevada, United States", "start": {"dateTime": "2023-03-20T00:00:00.0000000"}, "end": {"dateTime": "2023-03-24T00:00:00.0000000"}, "is_new": false}, {"summary": "Flight to Houston (UA 2379)", "description": "", "location": "Seattle SEA", "start": {"dateTime": "2023-04-09T23:59:00.0000000"}, "end": {"dateTime": "2023-04-10T04:16:00.0000000"}, "is_new": false}, {"summary": "Stay at The Westin Reserva Conchal, an All-Inclusive Golf Resort & Spa", "description": "", "location": "Playa Conchal Guanacaste Costa Rica", "start": {"dateTime": "2023-04-10T00:00:00.0000000"}, "end": {"dateTime": "2023-04-18T00:00:00.0000000"}, "is_new": false}, {"summary": "Flight to Liberia (UA 1512)", "description": "", "location": "Houston IAH", "start": {"dateTime": "2023-04-10T07:41:00.0000000"}, "end": {"dateTime": "2023-04-10T11:10:00.0000000"}, "is_new": false}, {"summary": "Flight to Houston (UA 1516)", "description": "", "location": "Liberia LIR", "start": {"dateTime": "2023-04-17T12:14:00.0000000"}, "end": {"dateTime": "2023-04-17T15:51:00.0000000"}, "is_new": false}, {"summary": "Flight to Seattle (UA 1905)", "description": "", "location": "Houston IAH", "start": {"dateTime": "2023-04-17T18:08:00.0000000"}, "end": {"dateTime": "2023-04-17T22:53:00.0000000"}, "is_new": false}, {"summary": "Flight to Palm Springs (AS 1068)", "description": "", "location": "Seattle SEA", "start": {"dateTime": "2023-05-10T12:55:00.0000000"}, "end": {"dateTime": "2023-05-10T15:35:00.0000000"}, "is_new": false}, {"summary": "Flight to Seattle (AS 1257)", "description": "", "location": "Palm Springs PSP", "start": {"dateTime": "2023-05-14T10:30:00.0000000"}, "end": {"dateTime": "2023-05-14T13:23:00.0000000"}, "is_new": false}, {"summary": "Flight to New York (AS 26)", "description": "", "location": "Seattle SEA", "start": {"dateTime": "2023-06-17T23:35:00.0000000"}, "end": {"dateTime": "2023-06-18T04:52:00.0000000"}, "is_new": false}, {"summary": "Flight to New York (AS 26)", "description": "", "location": "Seattle SEA", "start": {"dateTime": "2023-06-27T23:35:00.0000000"}, "end": {"dateTime": "2023-06-28T04:52:00.0000000"}, "is_new": false}, {"summary": "Flight to Seattle (AS 17)", "description": "", "location": "New York JFK", "start": {"dateTime": "2023-07-02T13:55:00.0000000"}, "end": {"dateTime": "2023-07-02T20:20:00.0000000"}, "is_new": false}, {"summary": "Flight to Boston (B6 498)", "description": "", "location": "Seattle SEA", "start": {"dateTime": "2023-07-02T21:10:00.0000000"}, "end": {"dateTime": "2023-07-03T02:23:00.0000000"}, "is_new": false}, {"summary": "Flight DL1517", "description": "Confirmation #: GBY5PX", "location": "SEA \u25b8 SFO", "start": {"dateTime": "2023-07-03T11:24:00.0000000"}, "end": {"dateTime": "2023-07-03T13:38:00.0000000"}, "is_new": false}, {"summary": "Stay at Best Western Woodburn", "description": "", "location": "Best Western Woodburn, Woodburn", "start": {"dateTime": "2023-07-07T00:00:00.0000000"}, "end": {"dateTime": "2023-07-09T00:00:00.0000000"}, "is_new": false}, {"summary": "Flight DL3842", "description": "Confirmation #: GB9D3X", "location": "MFR \u25b8 SEA", "start": {"dateTime": "2023-07-08T11:57:00.0000000"}, "end": {"dateTime": "2023-07-08T13:25:00.0000000"}, "is_new": false}, {"summary": "Flight to Seattle (B6 197)", "description": "", "location": "Boston BOS", "start": {"dateTime": "2023-07-12T13:20:00.0000000"}, "end": {"dateTime": "2023-07-12T19:46:00.0000000"}, "is_new": false}, {"summary": "SEA - LHR Flight BA0052 departs Mon, 24 Jul 2023 13:35 (Seattle time)", "description": "BA0052 Departs Seattle-Tacoma International (WA) on Mon, 24 Jul 2023 13:35 (Seattle time). Arrives Heathrow (London)&nbsp; terminal 5 on Tue, 25 Jul 2023 06:50 (London time). Your booking reference is N6VR9Z.", "location": "Seattle-Tacoma International (WA)", "start": {"dateTime": "2023-07-24T13:35:00.0000000"}, "end": {"dateTime": "2023-07-24T22:50:00.0000000"}, "is_new": false}, {"summary": "Trip Bari Centrale-Venezia S. Lucia, Train Frecciarossa 8816, Coach 1, Position, 5B, 5A, 6B, 6A, 6D, CP 134001, 134001, 134001, 134001, 134001, PNR W3XPAN,  ", "description": "Bari Centrale-Venezia S. Lucia;Train: Frecciarossa 8816, departing from Bari Centrale Hours: 08:30; arriving at Venezia S. Lucia Hours: 16:08 Coach 1, Position 5B, Position 5A, Position 6B, Position 6A, Position\r\n6D, CP 134001, CP 134001, CP 134001, CP 134001, CP 134001; pnr code W3XPAN", "location": "Bari Centrale", "start": {"dateTime": "2023-08-05T08:30:00.0000000"}, "end": {"dateTime": "2023-08-05T16:08:00.0000000"}, "is_new": false}, {"summary": "Flight to Paris (DL 8567)", "description": "", "location": "Venice VCE", "start": {"dateTime": "2023-08-10T01:35:00.0000000"}, "end": {"dateTime": "2023-08-10T03:10:00.0000000"}, "is_new": false}, {"summary": "LHR - SEA Flight BA0049 departs Thu, 10 Aug 2023 16:10 (London time)", "description": "BA0049 Departs Heathrow (London)&nbsp; terminal 5 on Thu, 10 Aug 2023 16:10 (London time). Arrives Seattle-Tacoma International (WA) on Thu, 10 Aug 2023 17:55 (Seattle time). Your booking reference is N6VR9Z.", "location": "Heathrow (London)", "start": {"dateTime": "2023-08-10T08:10:00.0000000"}, "end": {"dateTime": "2023-08-10T17:55:00.0000000"}, "is_new": false}]"#
  }
  @@assert(extract_at_least_home_airport, {{this.response_all_preferences.preferred_home_airport == 'SEA'}})
  @@assert(extract_airlines, {{this.response_all_preferences.preferred_airline_brands | length > 0}})
}

test test_analyze_calendar_highlight {
  functions [ConversePreferencesWithoutAnalysis]
  args {    
    existing_preference #"
      {
        "preferred_home_airport": null,
        "preferred_airline_brands": ["United Airlines"],
        "preferred_seats": null,
        "preferred_hotel_brands": null,
        "preferred_travel_misc": null
      }
    "#
    messages [
      "assistant Hi Chengxuan! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user please explain ",
      "assistant {\"agent_response\": \"\nGreat! Here's a bit about how I can make your life easier:\n&NewLine;\n\nI\u2019m like your expert-level travel agent, here to handle flights and hotels in the US while following your company\u2019s Travel Policy. Need help finding the best seat on the plane? I\u2019ve got you. Plans change? I\u2019ll handle cancellations or rebookings for you.\n&NewLine;\n\nWhen you share information like your Frequent Flyer number or Credit Card, I\u2019ll securely save it so you won\u2019t have to provide it again\u2014I\u2019ll use it to make booking fast and hassle-free.\n&NewLine;\n\nThat said, I specialize in booking your work travel, so I won\u2019t be able to help with your family vacation plans (at least not yet). Down the road, I\u2019m hoping to assist with car rentals or even restaurant reservations, but for now, it\u2019s just flights and hotels.\n&NewLine;\n\nWhen I recommend travel options, I\u2019ll prioritize your preferences and business travel style\u2014whether it\u2019s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I\u2019ll always let you know if certain options would exceed your company\u2019s Travel Policy.\n&NewLine;\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n\", \"step\": \"CustomerWantKnowMore\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "user dive in",
      "assistant {\"agent_response\": \"\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n&NewLine;\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"AskForCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "user sure",
      "assistant {\"agent_response\": \"Great! I'll start the process to connect to your calendar. Please follow the prompts to grant access.\", \"step\": \"GettingCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "assistant I already have your calendar access. analyzing your calendar, please wait for a moment.",
      "assistant {\"agent_response\": \"Based on your calendar events, here's what I have gathered about your travel preferences:\n\n\u2022 Preferred home airport: None\n\u2022 Preferred airline brands: **United Airlines**\n\u2022 Preferred cabin: None\n\u2022 Preferred seats: None\n\u2022 Preferred hotel brands: None\n\u2022 Preferred travel misc: None\n\nIt seems we don't have complete preferences gathered from your calendar events. I\u2019ll just ask you a few quick questions so I can learn about your travel style and preferences. Let\u2019s get started. First, do you have a preferred home airport that you usually fly out of?\", \"preference\": {\"preferred_home_airport\": null, \"preferred_airline_brands\": [\"United Airlines\"], \"preferred_cabin\": null, \"preferred_seats\": null, \"preferred_hotel_brands\": null, \"preferred_travel_misc\": null}}",
      "user I usually fly out of Seattle"
    ]
    missing_preferences []
  }
  @@assert(extract_at_least_home_airport, {{this.response_all_preferences.preferred_home_airport | regex_match(".*SEA.*")}})
  @@assert(bold_changed_preference, {{this.agent_response | regex_match("(.*?)\*\*(.*?)\*\*(.*?)") }})
  @@assert(extract_airlines, {{this.response_all_preferences.preferred_airline_brands | length > 0}})
}

test test_start_with_existing_preferences {
  functions [ConversePreferences]
  args {
    existing_preference #"
      {
        "preferred_home_airport": "SEA",
        "preferred_airline_brands": ["United Airlines"],
        "preferred_seats": null,
        "preferred_hotel_brands": null,
        "preferred_travel_misc": null
      }
    "#
    messages []
    prompt #"

    Role: Travel Agent

    Goal: Collect info about the traveler's preferences by asking them a series of questions.

    Backstory:
        - You are an expert travel agent helping a business traveler you have never met before.
        - When collecting information from the traveler be friendly, only ask one question at a time.

    Procedure:
        - Collect the traveler's preferences
        - If the traveler has preferences before, you need to summarize them and ask the traveler if they want to update them. 
        - Keep asking questions until you have collected all of the required information.
        - Continuously ask the traveler if they have any additional additions, deletions or changes to their preferences or just want to get started.
        - Once the traveler approves the preferences or they want to get started, the conversation is finished.
        - When the conversation is finished, thank the traveler for their input and feedback.
    
    
    Please also present your current understanding of the traveler's preferences every time. (including the categories that contain no information) as a bulleted list - 1 bullet • per preference category.
    categories include: preferred_home_airport, preferred_airline_brands, preferred_seats, preferred_hotel_brands, preferred_travel_misc

    Handling Off-Topic Queries:
    - For ANY unrelated questions (e.g., poems, weather, general chat):
      • Response: "I'm your travel assistant focused on helping with travel arrangements. Let's stay focused on collecting your travel preferences.
    - Never generate content unrelated to travel booking services     
    "#
    cal_events "[]"
  }
  @@assert(extract_at_least_home_airport, {{this.response_all_preferences.preferred_home_airport == 'SEA'}})
  @@assert(extract_airlines, {{this.response_all_preferences.preferred_airline_brands | length > 0}})
}

test first_time_user_open_preference_tab {
  functions [ConversePreferencesWithoutAnalysis]
  args {
    existing_preference "{\"preferred_home_airport\":null,\"preferred_airline_brands\":null,\"preferred_cabin\":null,\"preferred_seats\":null,\"preferred_hotel_brands\":null,\"preferred_travel_misc\":null}"
    user_name "Chundong"
    missing_preferences []
    messages []
  }
}

test update_preferences_in_the_middle_of_conversaton {
  functions [UpdateUserPreferences]
  args {
    current_preferences {
      preferred_home_airport "JFK"
      // preferred_seats ["window"]
    }

    messages ["assistant I'll start planning a new trip for you. Please tell me where you are going and your travel dates. {\"agent_classification\": \"FOH\", \"id\": 128525}", 
    "user find me a one way DL flight JFK - LAX on May 3", 
    "assistant {\"current_step\":\"OUTBOUND_FLIGHT_SEARCH\",\"updated_flight_search_core_criteria\":{\"departure_airport_code\":\"JFK\",\"is_departure_iata_city_code\":false,\"arrival_airport_code\":\"LAX\",\"is_arrival_iata_city_code\":false,\"outbound_date\":\"2025-05-03\",\"return_date\":null,\"flight_type\":\"OneWay\"},\"updated_flight_search_additional_criteria\":{\"seat_types\":null,\"preferred_airline_codes\":[\"DL\"],\"cabin\":null,\"outbound_departure_time\":null,\"outbound_arrival_time\":null,\"return_departure_time\":null,\"return_arrival_time\":null,\"number_of_stops\":null,\"other_not_categorized_preferences\":null},\"updated_flight_select_result\":null,\"agent_response\":\"Got it! Searching for a one-way Delta flight from JFK to LAX on May 3, 2025. I'll provide the options shortly.\"}", 
    "assistant {\"preferred_flights\": [6, 8, 12, 10, 0, 2], \"preferred_flights_reasons\": [\"Flight 713 was selected because it departs within the specified morning time window, is in economy class, and is operated by your preferred airline, Delta.\", \"Flight 773 was chosen for its alignment with the morning time window, economy fare class, and operation by Delta, your preferred airline.\", \"Flight 747 was selected for its early afternoon departure, economy class, and operation by Delta, matching your preferences.\", \"Flight 738 was chosen for its mid-afternoon departure, economy class, and operation by Delta, adhering to your preferences.\", \"Flight 771 was selected for its late afternoon departure, economy class, and operation by Delta, matching your preferences.\", \"Flight 707 was chosen for its early evening departure, economy class, and operation by Delta, aligning with your preferences.\"], \"error_response\": null, \"presentation_message\": \"I prioritized non-stop flights operated by Delta, your preferred airline, and ensured they matched your requested economy fare class and departure time windows. All selected flights are exchangeable and meet your preferences for a smooth travel experience.\", \"is_outbound_flight_choices\": true, \"flight_search_type\": \"ONE_WAY\", \"flight_choices\": [{\"airline_code\": \"DL\", \"id_token_key\": \"67f00c008d9f153eb48798bc_67f00bfee6dd21dfe6646992_1\", \"price\": 229.0, \"currency\": \"USD\", \"cabin\": \"Main Cabin\", \"total_distance_miles\": null, \"total_duration\": 380.0, \"exchange_policy\": \"Free change, possible fare difference\", \"cancellation_policy\": null, \"seat_selection_policy\": \"Free seat selection\", \"fare_option_name\": \"Main Cabin\", \"fs0_departure_time\": \"2025-05-03 08:40\", \"fs0_arrival_time\": \"2025-05-03 12:00\", \"fs0_airline_code\": \"DL\", \"fs0_airline_name\": \"Delta\", \"fs0_flight_number\": \"713\", \"fs0_origin\": \"JFK\", \"fs0_origin_name\": \"John F. Kennedy International Airport\", \"fs0_destination\": \"LAX\", \"fs0_destination_name\": \"Los Angeles International Airport\", \"fs0_cabin\": \"Main Cabin\", \"selection_reason\": \"Flight 713 was selected because it departs within the specified morning time window, is in economy class, and is operated by your preferred airline, Delta.\"}, {\"airline_code\": \"DL\", \"id_token_key\": \"67f00c0022670582e34eeb96_67f00bfee6dd21dfe6646992_1\", \"price\": 229.0, \"currency\": \"USD\", \"cabin\": \"Main Cabin\", \"total_distance_miles\": null, \"total_duration\": 380.0, \"exchange_policy\": \"Free change, possible fare difference\", \"cancellation_policy\": null, \"seat_selection_policy\": \"Free seat selection\", \"fare_option_name\": \"Main Cabin\", \"fs0_departure_time\": \"2025-05-03 09:55\", \"fs0_arrival_time\": \"2025-05-03 13:15\", \"fs0_airline_code\": \"DL\", \"fs0_airline_name\": \"Delta\", \"fs0_flight_number\": \"773\", \"fs0_origin\": \"JFK\", \"fs0_origin_name\": \"John F. Kennedy International Airport\", \"fs0_destination\": \"LAX\", \"fs0_destination_name\": \"Los Angeles International Airport\", \"fs0_cabin\": \"Main Cabin\", \"selection_reason\": \"Flight 773 was chosen for its alignment with the morning time window, economy fare class, and operation by Delta, your preferred airline.\"}, {\"airline_code\": \"DL\", \"id_token_key\": \"67f00c00bac788bb4f392b79_67f00bfee6dd21dfe6646992_1\", \"price\": 239.0, \"currency\": \"USD\", \"cabin\": \"Main Cabin\", \"total_distance_miles\": null, \"total_duration\": 378.0, \"exchange_policy\": \"Free change, possible fare difference\", \"cancellation_policy\": null, \"seat_selection_policy\": \"Free seat selection\", \"fare_option_name\": \"Main Cabin\", \"fs0_departure_time\": \"2025-05-03 13:55\", \"fs0_arrival_time\": \"2025-05-03 17:13\", \"fs0_airline_code\": \"DL\", \"fs0_airline_name\": \"Delta\", \"fs0_flight_number\": \"747\", \"fs0_origin\": \"JFK\", \"fs0_origin_name\": \"John F. Kennedy International Airport\", \"fs0_destination\": \"LAX\", \"fs0_destination_name\": \"Los Angeles International Airport\", \"fs0_cabin\": \"Main Cabin\", \"selection_reason\": \"Flight 747 was selected for its early afternoon departure, economy class, and operation by Delta, matching your preferences.\"}, {\"airline_code\": \"DL\", \"id_token_key\": \"67f00c0079d223723acdb4f2_67f00bfee6dd21dfe6646992_1\", \"price\": 229.0, \"currency\": \"USD\", \"cabin\": \"Main Cabin\", \"total_distance_miles\": null, \"total_duration\": 375.0, \"exchange_policy\": \"Free change, possible fare difference\", \"cancellation_policy\": null, \"seat_selection_policy\": \"Free seat selection\", \"fare_option_name\": \"Main Cabin\", \"fs0_departure_time\": \"2025-05-03 15:00\", \"fs0_arrival_time\": \"2025-05-03 18:15\", \"fs0_airline_code\": \"DL\", \"fs0_airline_name\": \"Delta\", \"fs0_flight_number\": \"738\", \"fs0_origin\": \"JFK\", \"fs0_origin_name\": \"John F. Kennedy International Airport\", \"fs0_destination\": \"LAX\", \"fs0_destination_name\": \"Los Angeles International Airport\", \"fs0_cabin\": \"Main Cabin\", \"selection_reason\": \"Flight 738 was chosen for its mid-afternoon departure, economy class, and operation by Delta, adhering to your preferences.\"}, {\"airline_code\": \"DL\", \"id_token_key\": \"67f00c003027ad7f712df572_67f00bfee6dd21dfe6646992_1\", \"price\": 194.0, \"currency\": \"USD\", \"cabin\": \"Main Cabin\", \"total_distance_miles\": null, \"total_duration\": 388.0, \"exchange_policy\": \"Free change, possible fare difference\", \"cancellation_policy\": null, \"seat_selection_policy\": \"Free seat selection\", \"fare_option_name\": \"Main Cabin\", \"fs0_departure_time\": \"2025-05-03 16:40\", \"fs0_arrival_time\": \"2025-05-03 20:08\", \"fs0_airline_code\": \"DL\", \"fs0_airline_name\": \"Delta\", \"fs0_flight_number\": \"771\", \"fs0_origin\": \"JFK\", \"fs0_origin_name\": \"John F. Kennedy International Airport\", \"fs0_destination\": \"LAX\", \"fs0_destination_name\": \"Los Angeles International Airport\", \"fs0_cabin\": \"Main Cabin\", \"selection_reason\": \"Flight 771 was selected for its late afternoon departure, economy class, and operation by Delta, matching your preferences.\"}, {\"airline_code\": \"DL\", \"id_token_key\": \"67f00c00413b303e0bf119c5_67f00bfee6dd21dfe6646992_1\", \"price\": 194.0, \"currency\": \"USD\", \"cabin\": \"Main Cabin\", \"total_distance_miles\": null, \"total_duration\": 393.0, \"exchange_policy\": \"Free change, possible fare difference\", \"cancellation_policy\": null, \"seat_selection_policy\": \"Free seat selection\", \"fare_option_name\": \"Main Cabin\", \"fs0_departure_time\": \"2025-05-03 18:59\", \"fs0_arrival_time\": \"2025-05-03 22:32\", \"fs0_airline_code\": \"DL\", \"fs0_airline_name\": \"Delta\", \"fs0_flight_number\": \"707\", \"fs0_origin\": \"JFK\", \"fs0_origin_name\": \"John F. Kennedy International Airport\", \"fs0_destination\": \"LAX\", \"fs0_destination_name\": \"Los Angeles International Airport\", \"fs0_cabin\": \"Main Cabin\", \"selection_reason\": \"Flight 707 was chosen for its early evening departure, economy class, and operation by Delta, aligning with your preferences.\"}]}", 
    "user For my outbound I choose flight id: 67f00c008d9f153eb48798bc_67f00bfee6dd21dfe6646992_1.", 
    "assistant {\"current_step\":\"FLIGHT_VALIDATION\",\"updated_flight_search_core_criteria\":null,\"updated_flight_search_additional_criteria\":null,\"updated_flight_select_result\":{\"selected_outbound_flight_id\":\"67f00c008d9f153eb48798bc_67f00bfee6dd21dfe6646992_1\",\"selected_return_flight_id\":null,\"frequent_flier_number\":null,\"apply_flight_credits\":null,\"flight_credits_ticket_numbers\":null},\"agent_response\":\"Got it! You have selected Delta flight 713 departing from JFK at 8:40 AM and arriving at LAX at 12:00 PM on May 3, 2025. I will now validate this selection and ensure everything is ready for booking. Let me know if you have any seat preferences or additional details you'd like to provide.\"}", 
    "assistant Before we proceed, I need to gather some additional information. Could you please provide your preferred seat types? Additionally, we currently do not have a SkyMiles number on file for you. If you have a SkyMiles number or a Frequent Flyer number from a partner airline, please share that with me. If you do not have one, just let me know. This information will be helpful for seat selection.{\"id\": 128531}", 
    "user 123456",
    "assistant {\"current_step\":\"FLIGHT_VALIDATION\",\"updated_flight_search_core_criteria\":null,\"updated_flight_search_additional_criteria\":null,\"updated_flight_select_result\":{\"selected_outbound_flight_id\":null,\"selected_return_flight_id\":null,\"frequent_flier_number\":[{\"airline_code\":\"DL\",\"number\":\"123456\"}],\"apply_flight_credits\":null,\"flight_credits_ticket_numbers\":null},\"agent_response\":\"Thank you! I have added your Delta SkyMiles number (123456) to your profile for this booking. You have selected Delta flight 713 departing from JFK at 8:40 AM and arriving at LAX at 12:00 PM on May 3, 2025. I will now validate this selection and ensure everything is ready for booking. Let me know if you have any seat preferences or additional details you'd like to provide.\"}", 
    "assistant Before we proceed, could you please provide your preferred seat types? This information is currently missing.{\"id\": 128546}", 
    "user window seats"]
  }
}

test prompt_user_to_update_preferences {
  functions [PromptUserToUpdatePreferencesAfterSearch]
  args {
    search_type "flight"
    preferences null
    search_criteria #"
          {'current_step': 'OUTBOUND_FLIGHT_SEARCH', 'search_purpose': 'OUTBOUND_FLIGHT_SEARCH', 'departure_airport_code': 'SEA', 'is_departure_iata_city_code': False, 'arrival_airport_code': 'SFO', 'is_arrival_iata_city_code': False, 'outbound_date': '2025-06-25', 'return_date': None, 'flight_type': <FlightType.OneWay: 'OneWay'>, 'preferred_airline_codes': ['DL'], 'default_airline_brands': None, 'preferred_cabin': ['premium_economy'], 'travel_context': '{"seat_types": ["exit row"], "preferred_airline_codes": [""], "cabin": ["premium_economy"], "refundable": "not_specified"}', 'number_of_stops': None, 'search_id': None, 'selected_outbound_flight_id': None, 'outbound_arrival_time': None, 'outbound_departure_time': None, 'return_arrival_time': None, 'return_departure_time': None, 'search_segments': None}
    "#
  }
}

test determine_home_airport_from_ip_info_netherlands {
  functions [DetermineHomeAirportFromIpInfo]
  args {
    ip_info "{\"ip\":\"************\",\"continent_code\":\"EU\",\"continent_name\":\"Europe\",\"country_code\":\"NL\",\"country_name\":\"Netherlands\",\"region_code\":\"OV\",\"region_name\":\"Overijssel\",\"city\":\"Ijpelo\",\"zip\":\"7468\"}"
  }

  @@assert(extract_at_least_home_airport, {{ not this.airport == 'ENS' }})
}

test determine_home_airport_from_ip_info_texas {
  functions [DetermineHomeAirportFromIpInfo]
  args {
    ip_info "{\"ip\":\"***********\",\"continent_code\":\"NA\",\"continent_name\":\"North America\",\"country_code\":\"US\",\"country_name\":\"United States\",\"region_code\":\"TX\",\"region_name\":\"Texas\",\"city\":\"Houston\",\"zip\":\"77007\"}"
  }

  @@assert(extract_at_least_home_airport, {{ this.airport == 'IAH' }})
}

test determine_home_airport_from_ip_info_sea {
  functions [DetermineHomeAirportFromIpInfo]
  args {
    ip_info "{\"ip\":\"**************\",\"continent_code\":\"NA\",\"continent_name\":\"North America\",\"country_code\":\"US\",\"country_name\":\"United States\",\"region_code\":\"WA\",\"region_name\":\"Washington\",\"city\":\"Seattle\",\"zip\":\"98199\"}"
  }

  @@assert(extract_at_least_home_airport, {{ this.airport == 'SEA' }})
}

test test_cal_analyze_new_flow {
  functions [ConversePreferences]
  args {
    cal_events #"[{"summary": "Stay at Residence Inn by Marriott New York Manhattan/Times Square", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOXeUXvQzCqZL48sXQGE7yAbJFX_Qww3RM", "location": "Residence Inn by Marriott New York Manhattan/Times Square, New York", "start": {"date": "2023-11-17"}, "end": {"date": "2023-11-22"}, "is_new": false}, {"summary": "Delta Air Lines flight: DL 0528 from Seattle (SEA) to New York (JFK)", "description": "~~~~~~~~~~~~~~~~~~~~~~~~~\\nDelta Air Lines flight: DL 0528 from Seattle (SEA) to New York (JFK)\\n~~~~~~~~~~~~~~~~~~~~~~~~~\\nDeparture: 11/17 3:36 PM\\nArrival: 11/17 11:59 PM\\nClass: Economy\\nAircraft:32Q\\nAirline Confirmation Code:HYJYZT\\nRecordLocator: EYRTCY\\n~~~~~~~~~~~~~~~~~~~~~~~~~\\n\\nView the full itinerary here: https://app.navan.com/app/user/trips/9c497d6e-ce06-41d1-b03a-42d418d2d35c/itinerary", "location": "SEA", "start": {"dateTime": "2023-11-17T15:36:00-08:00"}, "end": {"dateTime": "2023-11-17T20:59:00-08:00"}, "is_new": false}, {"summary": "Stay at SpringHill Suites by Marriott New York Midtown Manhattan/Fifth Avenue", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNlzOkGAADZogDzYM5gDrCvrq4_F5diexw", "location": "SpringHill Suites by Marriott New York Midtown Manhattan/Fifth Avenue, New York", "start": {"date": "2023-11-21"}, "end": {"date": "2023-11-24"}, "is_new": false}, {"summary": "Delta Air Lines flight: DL 0491 from New York (JFK) to Seattle (SEA)", "description": "~~~~~~~~~~~~~~~~~~~~~~~~~\\nDelta Air Lines flight: DL 0491 from New York (JFK) to Seattle (SEA)\\n~~~~~~~~~~~~~~~~~~~~~~~~~\\nDeparture: 11/21 11:00 AM\\nArrival: 11/21 2:20 PM\\nClass: Economy\\nAircraft:32Q\\nAirline Confirmation Code:HYJYZT\\nRecordLocator: EYRTCY\\n~~~~~~~~~~~~~~~~~~~~~~~~~\\n\\nView the full itinerary here: https://app.navan.com/app/user/trips/9c497d6e-ce06-41d1-b03a-42d418d2d35c/itinerary", "location": "JFK", "start": {"dateTime": "2023-11-21T08:00:00-08:00"}, "end": {"dateTime": "2023-11-21T14:20:00-08:00"}, "is_new": false}, {"summary": "Stay at The Wayfarer Downtown LA, Tapestry Collection by Hilton", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOrpLcc4MMKlIRPxNbUzcDHBmXQk-i2zxQ", "location": "The Wayfarer Downtown LA, Tapestry Collection by Hilton, Los Angeles", "start": {"date": "2024-01-16"}, "end": {"date": "2024-01-20"}, "is_new": false}, {"summary": "CLock", "description": "", "location": "", "start": {"dateTime": "2024-01-24T14:00:00-08:00"}, "end": {"dateTime": "2024-01-24T15:00:00-08:00"}, "is_new": false}, {"summary": "Stay: Residence Inn New York Manhattan/Times Square, New York Confirmation R4695248892", "description": "*Changes/updates to this event are added in real time, and any additional notes you add may be replaced.\\n\\nResidence Inn New York Manhattan/Times Square, New York\\nPhone: ******-768-0007\\n\\nHotel confirmation/record locator: R4695248892\\n\\nCheck-in: Feb 26, 2024 after 4:00 PM ET\\nCheck-out: Feb 29, 2024 before 11:00 AM ET\\n\\nNavan booking ID: 47313634518\\n\\nTo view, change or cancel your hotel:\\nhttps://app.navan.com/app/user/trips/32d02423-b04d-4606-a2c7-daa701cd07fb/itinerary?hotelUuid=2f5397a3-dca0-45a3-941d-1c04319302bf\\n\\nThis is a Navan-created calendar event.", "location": "Residence Inn New York Manhattan/Times Square, New York", "start": {"date": "2024-02-26"}, "end": {"date": "2024-03-01"}, "is_new": false}, {"summary": "Flight to New York (DL 1713)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPgsHBqpwYbhFS7IXGaFbicFpCebE1xosY", "location": "Seattle SEA", "start": {"dateTime": "2024-02-26T13:35:00-08:00"}, "end": {"dateTime": "2024-02-26T18:56:00-08:00"}, "is_new": false}, {"summary": "Stay: Broadway Plaza Hotel, New York Confirmation R4695288635", "description": "*Changes/updates to this event are added in real time, and any additional notes you add may be replaced.\\n\\nBroadway Plaza Hotel, New York\\nPhone: ******-679-7665\\n\\nHotel confirmation/record locator: R4695288635\\n\\nCheck-in: Feb 29, 2024 after 3:00 PM ET\\nCheck-out: Mar 2, 2024 before 12:00 PM ET\\n\\nNavan booking ID: 47314875518\\n\\nTo view, change or cancel your hotel:\\nhttps://app.navan.com/app/user/trips/32d02423-b04d-4606-a2c7-daa701cd07fb/itinerary?hotelUuid=9d51e81b-80ca-42e7-8fdd-375d07cacae8\\n\\nThis is a Navan-created calendar event.", "location": "Broadway Plaza Hotel, New York", "start": {"date": "2024-02-29"}, "end": {"date": "2024-03-03"}, "is_new": false}, {"summary": "Flight to Seattle (DL 356)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPgsHBqpwYbhFS7IXGaFbicFpCebE1xosY", "location": "New York JFK", "start": {"dateTime": "2024-03-02T14:15:00-08:00"}, "end": {"dateTime": "2024-03-02T20:44:00-08:00"}, "is_new": false}, {"summary": "Flight to Seattle (DL 1334)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOSKf_9rvA87wYoy9PQJ1y_V3-5iQzIc9w", "location": "Los Angeles LAX", "start": {"dateTime": "2024-04-12T20:15:00-07:00"}, "end": {"dateTime": "2024-04-12T23:12:00-07:00"}, "is_new": false}, {"summary": "Flight to Las Vegas (NK 422)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMKBW84cVMOQWG1pEJ1WZKBDN5Cz0QYuII", "location": "Seattle SEA", "start": {"dateTime": "2024-05-24T09:35:00-07:00"}, "end": {"dateTime": "2024-05-24T12:07:00-07:00"}, "is_new": false}, {"summary": "Flight to Seattle (F9 4203)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOXWD4IDJKqhuRXzyKpk5UarZCLEE-OgDg", "location": "Las Vegas LAS", "start": {"dateTime": "2024-05-26T19:07:00-07:00"}, "end": {"dateTime": "2024-05-26T21:59:00-07:00"}, "is_new": false}, {"summary": "Stay at Hampton Inn Manhattan/Times Square Central", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMSL2nIVoFP6xkThhz4M9W_HQ9PLJdW_7Q", "location": "Hampton Inn Manhattan/Times Square Central, New York", "start": {"date": "2024-06-09"}, "end": {"date": "2024-06-14"}, "is_new": false}, {"summary": "Flight to Seattle (DL 688)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOm6SkkSpRGJaaojGaO0OjV-QtrhVVpV1s", "location": "New York JFK", "start": {"dateTime": "2024-06-13T15:58:00-07:00"}, "end": {"dateTime": "2024-06-13T22:25:00-07:00"}, "is_new": false}, {"summary": "Stay at Riverfront Retreat, Hot Tub, 3 Unique Floors", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPWwHZ4dYPEGCV17XNWS396z1myoTPKZjQ", "location": "Riverfront Retreat, Hot Tub, 3 Unique Floors, Gold Bar", "start": {"date": "2024-07-05"}, "end": {"date": "2024-07-07"}, "is_new": false}, {"summary": "Stay at Riverfront Retreat, Hot Tub, 3 Unique Floors!", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPceIaNRdMMviTfry8rD5Q8PHG8hm19rgk", "location": "Riverfront Retreat, Hot Tub, 3 Unique Floors!, Gold Bar", "start": {"date": "2024-07-05"}, "end": {"date": "2024-07-07"}, "is_new": false}, {"summary": "Flight to Shanghai (DL 281)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPnnzt1QFKBJlw0Ou6hQvaTeqbEFEvqiB4", "location": "Seattle SEA", "start": {"dateTime": "2024-11-04T11:15:00-08:00"}, "end": {"dateTime": "2024-11-05T00:55:00-08:00"}, "is_new": false}, {"summary": "Stay at JW Marriott, Anaheim Resort", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNBtXF0fLMqAI_WbcEYRR8zbAGJEeqvR7U", "location": "1775 South Clementine Street, Anaheim, California 92802, United States", "start": {"date": "2024-11-09"}, "end": {"date": "2024-11-12"}, "is_new": false}, {"summary": "Flight to Burbank (AS 1168)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPp81G6xlE0DFlOgzi0-QpNkP91FuMi7oQ", "location": "Seattle SEA", "start": {"dateTime": "2024-11-09T19:31:00-08:00"}, "end": {"dateTime": "2024-11-09T21:59:00-08:00"}, "is_new": false}, {"summary": "Flight to Seattle (AS 1399)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNp1zllLRyYQITwfvNpCRp-vpcPyzQjvnU", "location": "Ontario ONT", "start": {"dateTime": "2024-11-11T18:30:00-08:00"}, "end": {"dateTime": "2024-11-11T21:15:00-08:00"}, "is_new": false}, {"summary": "Flight to Phoenix (DL 2442)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPrbiNZyCK01fvLsUfDO1thLkoSrOX9Q20", "location": "Seattle SEA", "start": {"dateTime": "2024-11-18T13:30:00-08:00"}, "end": {"dateTime": "2024-11-18T16:26:00-08:00"}, "is_new": false}, {"summary": "Flight to Seattle (DL 918)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPrbiNZyCK01fvLsUfDO1thLkoSrOX9Q20", "location": "Phoenix PHX", "start": {"dateTime": "2024-11-21T17:20:00-08:00"}, "end": {"dateTime": "2024-11-21T20:39:00-08:00"}, "is_new": false}, {"summary": "Flight to LAX (DL 1628)", "description": "Here are your outbound flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight number: 1628\\n\\tConfirmation number: TKOTEW", "location": "", "start": {"dateTime": "2024-11-25T07:20:00-08:00"}, "end": {"dateTime": "2024-11-25T10:03:00-08:00"}, "is_new": false}, {"summary": "Flight to SFO (DL 1012)", "description": "Here are your outbound flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight number: 1012\\n\\tConfirmation number: 5712239686", "location": "", "start": {"dateTime": "2024-11-25T21:20:00-08:00"}, "end": {"dateTime": "2024-11-25T23:37:00-08:00"}, "is_new": false}, {"summary": "Flight to NYC (DL 429)", "description": "Here are your outbound flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight number: 429\\n\\tConfirmation number: SOXKBM", "location": "", "start": {"dateTime": "2024-11-26T07:20:00-08:00"}, "end": {"dateTime": "2024-11-26T12:47:00-08:00"}, "is_new": false}, {"summary": "Flight to Chicago (DL 1578)", "description": "Here are your outbound flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight number: 1578\\n\\tConfirmation number: SSBALD", "location": "", "start": {"dateTime": "2024-11-26T11:45:00-08:00"}, "end": {"dateTime": "2024-11-26T15:43:00-08:00"}, "is_new": false}, {"summary": "Flight to Chicago (DL 2041)", "description": "Here are your return flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight number: 2041\\n\\tConfirmation number: SSBALD", "location": "", "start": {"dateTime": "2024-11-28T04:00:00-08:00"}, "end": {"dateTime": "2024-11-28T08:53:00-08:00"}, "is_new": false}, {"summary": "Flight to SFO (DL 2809)", "description": "Here are your return flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight number: 2809\\n\\tConfirmation number: 5712239686", "location": "", "start": {"dateTime": "2024-11-29T06:15:00-08:00"}, "end": {"dateTime": "2024-11-29T08:36:00-08:00"}, "is_new": false}, {"summary": "Flight to LAX (DL 2830)", "description": "Here are your outbound flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight number: 2830\\n\\tConfirmation number: 5281261794", "location": "", "start": {"dateTime": "2024-12-12T14:10:00-08:00"}, "end": {"dateTime": "2024-12-12T16:57:00-08:00"}, "is_new": false}, {"summary": "Stay at Agoda price guarantee", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOByqHqUWj0jlz96P-hLynlaJrVHXJHYto", "location": "app only link note Clicking this button on your phone will take you to the \\nAgoda app. Note to the property: Allotment under booking.com reservation \\nnumber 4493643142 Payment for this booking will be collected by the \\nproperty. \\u82d3\\u65c5\\u842c\\u5e74-LIN INN Wan Nian 3.5 stars rating out of five property image \\nFloor 7., No.70., Xining S. Road, Taipei, Taiwan, 108 \\u53f0\\u5317\\u5e02, \\u53f0\\u7063", "start": {"date": "2024-12-20"}, "end": {"date": "2024-12-22"}, "is_new": false}, {"summary": "Flight to Taipei City (CI 21)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM1GsvSTZM5Qth6KGL2MQbw9weMZ9WxJ_I", "location": "Seattle SEA", "start": {"dateTime": "2024-12-20T00:10:00-08:00"}, "end": {"dateTime": "2024-12-20T13:20:00-08:00"}, "is_new": false}, {"summary": "Flight to \\u4e0a\\u6d77 (CZ 3096)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMU-71EGswhvGeP806EkwvofY1xgz6ccfc", "location": "\\u53f0\\u5317 TPE", "start": {"dateTime": "2024-12-24T01:55:00-08:00"}, "end": {"dateTime": "2024-12-24T03:55:00-08:00"}, "is_new": false}, {"summary": "Flight to \\u53f0\\u5317 (CZ 3095)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMU-71EGswhvGeP806EkwvofY1xgz6ccfc", "location": "\\u4e0a\\u6d77 PVG", "start": {"dateTime": "2025-01-06T22:35:00-08:00"}, "end": {"dateTime": "2025-01-07T00:50:00-08:00"}, "is_new": false}, {"summary": "Flight to Seattle (CI 22)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM1GsvSTZM5Qth6KGL2MQbw9weMZ9WxJ_I", "location": "Taipei City TPE", "start": {"dateTime": "2025-01-07T07:35:00-08:00"}, "end": {"dateTime": "2025-01-07T18:15:00-08:00"}, "is_new": false}, {"summary": "Flight to Seattle (DL 280)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPnnzt1QFKBJlw0Ou6hQvaTeqbEFEvqiB4", "location": "Shanghai PVG", "start": {"dateTime": "2025-01-14T03:05:00-08:00"}, "end": {"dateTime": "2025-01-14T14:03:00-08:00"}, "is_new": false}, {"summary": "Flight to San Francisco (UA 1403)", "description": "Here are your outbound flight details:\\n\\tAirline: United Airlines\\n\\tFlight number: 1403\\n\\tAirline confirmation number: J5L5TF", "location": "", "start": {"dateTime": "2025-02-08T07:00:00-08:00"}, "end": {"dateTime": "2025-02-08T09:20:00-08:00"}, "is_new": false}, {"summary": "Flight to Miami (DL 506)", "description": "Here are your outbound flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight number: 506\\n\\tAirline confirmation number: None", "location": "", "start": {"dateTime": "2025-02-12T08:00:00-08:00"}, "end": {"dateTime": "2025-02-12T13:55:00-08:00"}, "is_new": false}, {"summary": "Flight to NYC (AS 280)", "description": "Here are your outbound flight details:\\n\\tAirline: Alaska Airlines, Inc.\\n\\tFlight number: 280\\n\\tAirline confirmation number: PEMCVC", "location": "", "start": {"dateTime": "2025-02-14T06:30:00-08:00"}, "end": {"dateTime": "2025-02-14T11:55:00-08:00"}, "is_new": false}, {"summary": "Flight to San Francisco (AA 76)", "description": "Here are your outbound flight details:\\n\\tAirline: American Airlines\\n\\tFlight number: 76\\n\\tAirline confirmation number: IETRYY", "location": "", "start": {"dateTime": "2025-02-15T03:00:00-08:00"}, "end": {"dateTime": "2025-02-15T09:45:00-08:00"}, "is_new": false}, {"summary": "Flight to SFO (UA 1403)", "description": "Here are your outbound flight details:\\n\\tAirline: United Airlines\\n\\tFlight number: 1403\\n\\tAirline confirmation number: B36NVR", "location": "", "start": {"dateTime": "2025-02-15T07:00:00-08:00"}, "end": {"dateTime": "2025-02-15T09:16:00-08:00"}, "is_new": false}, {"summary": "Flight to Miami (AA 305)", "description": "Here are your outbound flight details:\\n\\tAirline: American Airlines\\n\\tFlight number: 305\\n\\tAirline confirmation number: None", "location": "", "start": {"dateTime": "2025-02-15T16:30:00-08:00"}, "end": {"dateTime": "2025-02-15T23:24:00-08:00"}, "is_new": false}, {"summary": "Flight to NYC (AS 281)", "description": "Here are your outbound flight details:\\n\\tAirline: Alaska Airlines, Inc.\\n\\tFlight number: 281\\n\\tAirline confirmation number: PEMCVC", "location": "", "start": {"dateTime": "2025-02-17T13:00:00-08:00"}, "end": {"dateTime": "2025-02-17T19:21:00-08:00"}, "is_new": false}, {"summary": "Flight to SFO (UA 1403)", "description": "Here are your outbound flight details:\\n\\tAirline: United Airlines\\n\\tFlight number: 1403\\n\\tAirline confirmation number: E2QDK6", "location": "", "start": {"dateTime": "2025-02-18T07:00:00-08:00"}, "end": {"dateTime": "2025-02-18T09:16:00-08:00"}, "is_new": false}, {"summary": "Flight to Nashville (DL 1128)", "description": "Here are your outbound flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight number: 1128\\n\\tAirline confirmation number: GT8584", "location": "", "start": {"dateTime": "2025-02-21T05:30:00-08:00"}, "end": {"dateTime": "2025-02-21T12:17:00-08:00"}, "is_new": false}, {"summary": "Flight to New York City (UA 5328)", "description": "Here are your outbound flight details:\\n\\tAirline: United Airlines\\n\\tFlight number: 5328\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-02-25T06:00:00-08:00"}, "end": {"dateTime": "2025-02-25T12:47:00-08:00"}, "is_new": false}, {"summary": "Flight to Chicago (UA 2207)", "description": "Here are your outbound flight details:\\n\\tAirline: United Airlines\\n\\tFlight number: 2207\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-02-25T06:15:00-08:00"}, "end": {"dateTime": "2025-02-25T10:38:00-08:00"}, "is_new": false}, {"summary": "Flight to Bozeman, MT (B6 947)", "description": "Here are your outbound flight details:\\n\\tAirline: JetBlue Airways\\n\\tFlight number: 947\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-02-25T08:40:00-08:00"}, "end": {"dateTime": "2025-02-25T13:47:00-08:00"}, "is_new": false}, {"summary": "Flight to Chicago (UA 1387)", "description": "Here are your outbound flight details:\\n\\tAirline: United Airlines\\n\\tFlight number: 1387\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-02-27T06:45:00-08:00"}, "end": {"dateTime": "2025-02-27T11:40:00-08:00"}, "is_new": false}, {"summary": "Flight to Culver City (DL 1628)", "description": "Here are your outbound flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight number: 1628\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-02-27T07:10:00-08:00"}, "end": {"dateTime": "2025-02-27T09:55:00-08:00"}, "is_new": false}, {"summary": "Flight to Culver City (DL 1551)", "description": "Here are your outbound flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight number: 1551\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-02-28T20:30:00-08:00"}, "end": {"dateTime": "2025-02-28T23:31:00-08:00"}, "is_new": false}, {"summary": "Flight to Phoenix (DL 2698)", "description": "Here are your outbound flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight number: 2698\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-03-07T20:30:00-08:00"}, "end": {"dateTime": "2025-03-07T23:24:00-08:00"}, "is_new": false}, {"summary": "Flight to Boise (UA 608)", "description": "Here are your outbound flight details:\\n\\tAirline: United Airlines\\n\\tFlight number: 608\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-03-08T05:00:00-08:00"}, "end": {"dateTime": "2025-03-08T10:25:00-08:00"}, "is_new": false}, {"summary": "Flight to Menlo Park (DL 3994)", "description": "Here are your outbound flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight numbers: DL 3994\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-03-10T19:50:00-07:00"}, "end": {"dateTime": "2025-03-10T22:10:00-07:00"}, "is_new": false}, {"summary": "Flight to Chicago (AA 2382)", "description": "Here are your outbound flight details:\\n\\tAirline: American Airlines\\n\\tFlight number: 2382\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-03-12T04:04:00-07:00"}, "end": {"dateTime": "2025-03-12T06:25:00-07:00"}, "is_new": false}, {"summary": "Flight to Menlo Park (DL 3840)", "description": "Here are your outbound flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight numbers: DL 3840\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-03-14T18:25:00-07:00"}, "end": {"dateTime": "2025-03-14T20:45:00-07:00"}, "is_new": false}, {"summary": "Flight to San Diego (AS 1156)", "description": "Here are your outbound flight details:\\n\\tAirline: Alaska Airlines, Inc.\\n\\tFlight numbers: AS 1156\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-04-14T19:54:00-07:00"}, "end": {"dateTime": "2025-04-14T22:44:00-07:00"}, "is_new": false}, {"summary": "Stay at Waterfront Escape-Olalla Bay Getaway-Kayaks-Buoy", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\\n\\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNwhsbJ6S1DuFj_e6W50sxgwJZfdSSEcFA", "location": "Waterfront Escape-Olalla Bay Getaway-Kayaks-Buoy, Olalla", "start": {"date": "2025-04-16"}, "end": {"date": "2025-04-19"}, "is_new": false}, {"summary": "Flight to MCO (DL 772, DL 573)", "description": "Here are your outbound flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight numbers: DL 772, DL 573\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-04-26T06:00:00-07:00"}, "end": {"dateTime": "2025-04-26T13:55:00-07:00"}, "is_new": false}, {"summary": "Flight to MCO (DL 1691, DL 1091)", "description": "Here are your outbound flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight numbers: DL 1691, DL 1091\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-04-27T16:00:00-07:00"}, "end": {"dateTime": "2025-04-28T00:06:00-07:00"}, "is_new": false}, {"summary": "Flight to YYZ (AS 972)", "description": "Here are your outbound flight details:\\n\\tAirline: Alaska Airlines, Inc.\\n\\tFlight numbers: AS 972\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-04-28T07:15:00-07:00"}, "end": {"dateTime": "2025-04-28T12:00:00-07:00"}, "is_new": false}, {"summary": "Flight to YVR (AC 8845)", "description": "Here are your outbound flight details:\\n\\tAirline: Air Canada\\n\\tFlight numbers: AC 8845\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-04-29T13:00:00-07:00"}, "end": {"dateTime": "2025-04-29T15:21:00-07:00"}, "is_new": false}, {"summary": "Flight to LON (BA 52)", "description": "Here are your outbound flight details:\\n\\tAirline: British Airways\\n\\tFlight numbers: BA 52\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-04-29T13:30:00-07:00"}, "end": {"dateTime": "2025-04-29T22:50:00-07:00"}, "is_new": false}, {"summary": "Flight to San Diego (AS 1096)", "description": "Here are your outbound flight details:\\n\\tAirline: Alaska Airlines, Inc.\\n\\tFlight numbers: AS 1096\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-05-02T11:45:00-07:00"}, "end": {"dateTime": "2025-05-02T14:34:00-07:00"}, "is_new": false}, {"summary": "Flight to Los Angeles (UA 646)", "description": "Here are your outbound flight details:\\n\\tAirline: United Airlines\\n\\tFlight numbers: UA 646\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-05-02T17:55:00-07:00"}, "end": {"dateTime": "2025-05-02T19:30:00-07:00"}, "is_new": false}, {"summary": "Flight to BOS (UA 2647)", "description": "Here are your outbound flight details:\\n\\tAirline: United Airlines\\n\\tFlight numbers: UA 2647\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-05-03T07:00:00-07:00"}, "end": {"dateTime": "2025-05-03T12:48:00-07:00"}, "is_new": false}, {"summary": "Flight to Chicago (UA 793)", "description": "Here are your outbound flight details:\\n\\tAirline: United Airlines\\n\\tFlight numbers: UA 793\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-05-04T08:30:00-07:00"}, "end": {"dateTime": "2025-05-04T12:49:00-07:00"}, "is_new": false}, {"summary": "Flight to San Diego (DL 2613)", "description": "Here are your outbound flight details:\\n\\tAirline: Delta Air Lines\\n\\tFlight numbers: DL 2613\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-05-04T18:59:00-07:00"}, "end": {"dateTime": "2025-05-04T22:17:00-07:00"}, "is_new": false}, {"summary": "Flight to MEX (UA 821)", "description": "Here are your outbound flight details:\\n\\tAirline: United Airlines\\n\\tFlight numbers: UA 821\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-05-10T13:16:00-07:00"}, "end": {"dateTime": "2025-05-10T17:45:00-07:00"}, "is_new": false}, {"summary": "Flight to MEX (UA 718)", "description": "Here are your outbound flight details:\\n\\tAirline: United Airlines\\n\\tFlight numbers: UA 718\\n\\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-05-12T06:30:00-07:00"}, "end": {"dateTime": "2025-05-12T11:21:00-07:00"}, "is_new": false}]"#
    messages []
  }
  @@assert(extract_airlines, {{this.response_all_preferences.preferred_airline_brands | length > 0}})
  @@assert(extract_cabin, {{this.response_all_preferences.preferred_cabin | length > 0}})
  @@assert(extract_hotels, {{this.response_all_preferences.preferred_hotel_brands | length > 0}})
}

test analyze_calendar_with_existing_preferences {
  functions [ConversePreferences]
  args {
    existing_preference #"{"preferred_home_airport":"SEA","preferred_airline_brands":["Delta"]}"#
    cal_events #"[{"summary": "Flight to Seattle (B6 197)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM5hGQCVC-Iy6MTwWCCXPg553L_Q2tNj0k", "location": "Boston BOS", "start": {"dateTime": "2023-07-12T13:20:00-07:00"}, "end": {"dateTime": "2023-07-12T19:46:00-07:00"}, "is_new": false}, {"summary": "SEA - LHR Flight BA0052 departs Mon, 24 Jul 2023 13:35 (Seattle time)", "description": "BA0052 Departs Seattle-Tacoma International (WA) on Mon, 24 Jul 2023 13:35 (Seattle time). Arrives Heathrow (London)  terminal 5 on Tue, 25 Jul 2023 06:50 (London time). Your booking reference is N6VR9Z.", "location": "Seattle-Tacoma International (WA)", "start": {"dateTime": "2023-07-24T13:35:00-07:00"}, "end": {"dateTime": "2023-07-24T22:50:00-07:00"}, "is_new": false}, {"summary": "Trip Bari Centrale-Venezia S. Lucia, Train Frecciarossa 8816, Coach 1, Position, 5B, 5A, 6B, 6A, 6D, CP 134001, 134001, 134001, 134001, 134001, PNR W3XPAN,  ", "description": "Bari Centrale-Venezia S. Lucia;Train: Frecciarossa 8816, departing from Bari Centrale Hours: 08:30; arriving at Venezia S. Lucia Hours: 16:08 Coach 1, Position 5B, Position 5A, Position 6B, Position 6A, Position 6D, CP 134001, CP 134001, CP 134001, CP 134001, CP 134001; pnr code W3XPAN", "location": "Bari Centrale", "start": {"dateTime": "2023-08-05T08:30:00-07:00"}, "end": {"dateTime": "2023-08-05T16:08:00-07:00"}, "is_new": false}, {"summary": "LHR - SEA Flight BA0049 departs Thu, 10 Aug 2023 16:10 (London time)", "description": "BA0049 Departs Heathrow (London)  terminal 5 on Thu, 10 Aug 2023 16:10 (London time). Arrives Seattle-Tacoma International (WA) on Thu, 10 Aug 2023 17:55 (Seattle time). Your booking reference is N6VR9Z.", "location": "Heathrow (London)", "start": {"dateTime": "2023-08-10T08:10:00-07:00"}, "end": {"dateTime": "2023-08-10T17:55:00-07:00"}, "is_new": false}, {"summary": "Empire strips back", "description": "", "location": "", "start": {"dateTime": "2023-08-18T17:00:00-07:00"}, "end": {"dateTime": "2023-08-18T23:00:00-07:00"}, "is_new": false}, {"summary": "St. Jude TAC : On-site Meeting", "description": "", "location": "Memphis, TN", "start": {"dateTime": "2023-09-20T06:30:00-07:00"}, "end": {"dateTime": "2023-09-20T14:00:00-07:00"}, "is_new": false}, {"summary": "Michael Gulmann - JPMC Introduction", "description": "", "location": "Zoom Meeting ID: 954 7166 1606", "start": {"dateTime": "2023-11-16T11:00:00-08:00"}, "end": {"dateTime": "2023-11-16T11:30:00-08:00"}, "is_new": false}, {"summary": "Reservation: Wild Blue", "description": "", "location": "Wild Blue Restaurant + Bar, Across from the Aava Hotel, 4005 Whistler Way, Whistler, BC V8B 1J1, Canada", "start": {"dateTime": "2024-01-06T18:00:00-08:00"}, "end": {"dateTime": "2024-01-06T19:00:00-08:00"}, "is_new": false}, {"summary": "Stay at Hotel Mousai", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMUhg17R6pX3-pNJq08P5-SU2y1baz1ZbQ", "location": "Carr. A Barra de Navidad KM 7.5 Zona Hotelera Sur, Puerto Vallarta", "start": {"date": "2024-01-25"}, "end": {"date": "2024-01-29"}, "is_new": false}, {"summary": "Flight to Puerto Vallarta (DL 1921)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPUrE6Nbh7aZhFVhacoJuPvy0TdIRud24g", "location": "Seattle SEA", "start": {"dateTime": "2024-01-25T07:10:00-08:00"}, "end": {"dateTime": "2024-01-25T12:05:00-08:00"}, "is_new": false}, {"summary": "Flight to Seattle (DL 1922)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPUrE6Nbh7aZhFVhacoJuPvy0TdIRud24g", "location": "Puerto Vallarta PVR", "start": {"dateTime": "2024-01-28T13:15:00-08:00"}, "end": {"dateTime": "2024-01-28T18:24:00-08:00"}, "is_new": false}, {"summary": "Stay at Moxy NYC East Village", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPcWPOyf8GmrK_vSdP6BMBvEDfu-0Wnxao", "location": "Moxy NYC East Village, New York", "start": {"date": "2024-02-06"}, "end": {"date": "2024-02-09"}, "is_new": false}, {"summary": "Flight to New York (DL 927)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP8AkfRX_Z9AlhNkxOAM9GU2GWRyrpT6J8", "location": "Seattle SEA", "start": {"dateTime": "2024-02-06T07:30:00-08:00"}, "end": {"dateTime": "2024-02-06T13:00:00-08:00"}, "is_new": false}, {"summary": "Flight to Seattle (DL 1504)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP8AkfRX_Z9AlhNkxOAM9GU2GWRyrpT6J8", "location": "New York JFK", "start": {"dateTime": "2024-02-08T04:10:00-08:00"}, "end": {"dateTime": "2024-02-08T10:40:00-08:00"}, "is_new": false}, {"summary": "Flight", "description": "", "location": "", "start": {"dateTime": "2024-04-02T07:10:00-07:00"}, "end": {"dateTime": "2024-04-02T11:40:00-07:00"}, "is_new": false}, {"summary": "SEA \u27a4 SBP Alaska Flight 3375", "description": "Find the most recent information on the Alaska Airlines app.", "location": "Seattle/Tacoma Intl. Airport", "start": {"dateTime": "2024-04-09T07:30:00-07:00"}, "end": {"dateTime": "2024-04-09T10:02:00-07:00"}, "is_new": false}, {"summary": "SFO \u27a4 JFK Alaska Flight 38", "description": "Find the most recent information on the Alaska Airlines app.", "location": "San Francisco Intl. Airport", "start": {"dateTime": "2024-04-10T22:25:00-07:00"}, "end": {"dateTime": "2024-04-11T04:00:00-07:00"}, "is_new": false}, {"summary": "Stay at Campus Inn & Suites Eugene Downtown", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNKgxPRvzZXHw8r2D6uE2Dy_WYugXe9Ajs", "location": "Campus Inn & Suites Eugene Downtown, Eugene", "start": {"date": "2024-04-13"}, "end": {"date": "2024-04-15"}, "is_new": false}, {"summary": "Stay at Campus Inn and Suites", "description": "", "location": "Campus Inn & Suites Eugene Downtown, 390 E Broadway, Eugene, OR 97401, USA", "start": {"dateTime": "2024-04-13T14:00:00-07:00"}, "end": {"dateTime": "2024-04-13T15:00:00-07:00"}, "is_new": false}, {"summary": "SEA \u27a4 PSP Alaska Flight 1256", "description": "Find the most recent information on the Alaska Airlines app.", "location": "Seattle/Tacoma Intl. Airport", "start": {"dateTime": "2024-05-03T07:00:00-07:00"}, "end": {"dateTime": "2024-05-03T09:43:00-07:00"}, "is_new": false}, {"summary": "Flight to Palm Springs (AS 1256)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMv6lFx2oV00B5snFSLyEkRDCGwllq6HSc", "location": "Seattle SEA", "start": {"dateTime": "2024-05-03T07:00:00-07:00"}, "end": {"dateTime": "2024-05-03T09:43:00-07:00"}, "is_new": false}, {"summary": "PSP \u27a4 SEA Alaska Flight 1233", "description": "Find the most recent information on the Alaska Airlines app.", "location": "Palm Springs International Airport Airport", "start": {"dateTime": "2024-05-05T18:15:00-07:00"}, "end": {"dateTime": "2024-05-05T21:10:00-07:00"}, "is_new": false}, {"summary": "Holiday Inn Portland Stay", "description": "Confirmation #23722981 \nTelephone 1-503-2834466 \nManage Reservation https://www.ihg.com/hotels/us/en/reservation/singlereservationsummary?lastName=Gulmann&confNumber=23722981", "location": "909 N. Hayden Island Drive, Portland, OR, 97217, United States", "start": {"date": "2024-05-17"}, "end": {"date": "2024-05-20"}, "is_new": false}, {"summary": "Stay at Holiday Inn Portland - Columbia Riverfront, an IHG Hotel", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMxDMPVKrbxESJBSVgNr3OMZ61cdjFRN_k", "location": "Holiday Inn Portland - Columbia Riverfront, an IHG Hotel, Portland", "start": {"date": "2024-05-17"}, "end": {"date": "2024-05-20"}, "is_new": false}, {"summary": "SEA/XNA  ", "description": "Record Locator: NGRLMQ\n\n ITINERARY\n\nAA Flight 1667\nDepart: SEA - Seattle on Tue 28 May 2024 at 07:17AM\nArrive: DFW - Dallas/ Fort Worth on Tue 28 May 2024 at 01:21PM\nBooking Code: L\nMeals Offered: Food For Purchase\nTraveler Information: MICHAEL GULMANN - Coach - 19F\nCheck in at https://www.aa.com within 24 hours of departure.\n\nAA OPERATED BY ENVOY AIR AS AMERICAN EAGLE Flight 3583\nDepart: DFW - Dallas/ Fort Worth on Tue 28 May 2024 at 02:40PM\nArrive: XNA - Bentonville/ Fayetteville on Tue 28 May 2024 at 03:54PM\nBooking Code: L\nTraveler Information: MICHAEL GULMANN - Coach - 21D\nCheck in at https://www.aa.com within 24 hours of departure.\n\nAA OPERATED BY ENVOY AIR AS AMERICAN EAGLE Flight 3317\nDepart: XNA - Bentonville/ Fayetteville on Thu 30 May 2024 at 06:04AM\nArrive: DFW - Dallas/ Fort Worth on...", "location": " Seattle (SEA) 07:17AM local time", "start": {"dateTime": "2024-05-28T07:17:00-07:00"}, "end": {"dateTime": "2024-05-28T11:21:00-07:00"}, "is_new": false}, {"summary": "SEA/XNA  ", "description": "Record Locator: NGRLMQ\n\n ITINERARY\n\nAA Flight 1667\nDepart: SEA - Seattle on Tue 28 May 2024 at 07:17AM\nArrive: DFW - Dallas/ Fort Worth on Tue 28 May 2024 at 01:21PM\nBooking Code: L\nMeals Offered: Food For Purchase\nTraveler Information: MICHAEL GULMANN - Coach - 19F\nCheck in at https://www.aa.com within 24 hours of departure.\n\nAA OPERATED BY ENVOY AIR AS AMERICAN EAGLE Flight 3583\nDepart: DFW - Dallas/ Fort Worth on Tue 28 May 2024 at 02:40PM\nArrive: XNA - Bentonville/ Fayetteville on Tue 28 May 2024 at 03:54PM\nBooking Code: L\nTraveler Information: MICHAEL GULMANN - Coach - 21D\nCheck in at https://www.aa.com within 24 hours of departure.\n\nAA OPERATED BY ENVOY AIR AS AMERICAN EAGLE Flight 3317\nDepart: XNA - Bentonville/ Fayetteville on Thu 30 May 2024 at 06:04AM\nArrive: DFW - Dallas/ F...", "location": " Dallas/ Fort Worth (DFW) 02:40PM local time", "start": {"dateTime": "2024-05-28T12:40:00-07:00"}, "end": {"dateTime": "2024-05-28T13:54:00-07:00"}, "is_new": false}, {"summary": "Flight  UA578 SEA to DEN", "description": "United Airlines Confirmation #: EYH7C4", "location": "Departing Seattle, WA (SEA)", "start": {"dateTime": "2024-05-29T08:58:00-07:00"}, "end": {"dateTime": "2024-05-29T11:45:00-07:00"}, "is_new": false}, {"summary": "Flight  UA5475 DEN to XNA", "description": "United Airlines Confirmation #: EYH7C4", "location": "Departing Denver, CO (DEN)", "start": {"dateTime": "2024-05-29T12:30:00-07:00"}, "end": {"dateTime": "2024-05-29T14:24:00-07:00"}, "is_new": false}, {"summary": "Hotel", "description": "21C MUSEUM HOTEL BENTONVILLE\n\nVendor confirmation: B505YES626", "location": "21c museum hotel", "start": {"dateTime": "2024-05-29T15:00:00-07:00"}, "end": {"dateTime": "2024-05-29T16:00:00-07:00"}, "is_new": false}, {"summary": "Flight  UA4325 XNA to IAH", "description": "United Airlines Confirmation #: EYH7C4", "location": "Departing Bentonville/Fayetteville, AR (XNA)", "start": {"dateTime": "2024-05-31T04:55:00-07:00"}, "end": {"dateTime": "2024-05-31T06:47:00-07:00"}, "is_new": false}, {"summary": "Flight  UA2606 IAH to SEA", "description": "United Airlines Confirmation #: EYH7C4", "location": "Departing Houston, TX (IAH)", "start": {"dateTime": "2024-05-31T08:00:00-07:00"}, "end": {"dateTime": "2024-05-31T12:43:00-07:00"}, "is_new": false}, {"summary": "Flight to Athens (A3 347)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMEERwKeEqhfWP8VMuF7_f9WfvHUuj21bs", "location": "Santorini JTR", "start": {"dateTime": "2024-06-26T21:15:00-07:00"}, "end": {"dateTime": "2024-06-26T22:00:00-07:00"}, "is_new": false}, {"summary": "Flight to Boston (DL 241)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNdSOieyaTK-yN9S6dFbgghr38BKeZZmMs", "location": "Athens ATH", "start": {"dateTime": "2024-06-27T00:20:00-07:00"}, "end": {"dateTime": "2024-06-27T19:12:00-07:00"}, "is_new": false}, {"summary": "Flight to Detroit (DL 812)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOzVoT0elqY4Z3vD2Kc2g2l4vja1tbAYh4", "location": "Seattle SEA", "start": {"dateTime": "2024-06-30T06:00:00-07:00"}, "end": {"dateTime": "2024-06-30T13:16:00-07:00"}, "is_new": false}, {"summary": "Flight to Chattanooga (DL 5117)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOzVoT0elqY4Z3vD2Kc2g2l4vja1tbAYh4", "location": "Detroit DTW", "start": {"dateTime": "2024-06-30T11:35:00-07:00"}, "end": {"dateTime": "2024-06-30T13:16:00-07:00"}, "is_new": false}, {"summary": "Stay at Hilton San Diego Gaslamp Quarter", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOin5RTQtgXGesrKEyFwLL1BfrgZkA6NHc", "location": "Hilton San Diego Gaslamp Quarter, San Diego", "start": {"date": "2024-07-05"}, "end": {"date": "2024-07-08"}, "is_new": false}, {"summary": "Flight to San Diego (DL 2533)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM2MuqpZnLPs7nnXG4EtkfJAZMe7jWxm8s", "location": "Seattle SEA", "start": {"dateTime": "2024-07-05T09:44:00-07:00"}, "end": {"dateTime": "2024-07-05T12:34:00-07:00"}, "is_new": false}, {"summary": "Flight to Seattle (DL 2508)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM2MuqpZnLPs7nnXG4EtkfJAZMe7jWxm8s", "location": "San Diego SAN", "start": {"dateTime": "2024-07-07T11:10:00-07:00"}, "end": {"dateTime": "2024-07-07T14:17:00-07:00"}, "is_new": false}, {"summary": "Flight to Chicago (UA 5498)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOJnDM-XkZn9kmAj3pGC917hkYKe8OzW9E", "location": "Chattanooga CHA", "start": {"dateTime": "2024-07-13T04:05:00-07:00"}, "end": {"dateTime": "2024-07-13T06:05:00-07:00"}, "is_new": false}, {"summary": "Flight  UA5498 CHA to ORD", "description": "United Airlines Confirmation #: D9N3K2", "location": "Departing Chattanooga, TN (CHA)", "start": {"dateTime": "2024-07-13T04:05:00-07:00"}, "end": {"dateTime": "2024-07-13T06:05:00-07:00"}, "is_new": false}, {"summary": "Flight  UA1705 ORD to SEA", "description": "United Airlines Confirmation #: D9N3K2", "location": "Departing Chicago, IL (ORD)", "start": {"dateTime": "2024-07-13T06:45:00-07:00"}, "end": {"dateTime": "2024-07-13T11:24:00-07:00"}, "is_new": false}, {"summary": "Flight to Seattle (UA 1705)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOJnDM-XkZn9kmAj3pGC917hkYKe8OzW9E", "location": "Chicago ORD", "start": {"dateTime": "2024-07-13T06:45:00-07:00"}, "end": {"dateTime": "2024-07-13T11:24:00-07:00"}, "is_new": false}, {"summary": "Whistler boys trip", "description": "", "location": "", "start": {"date": "2024-08-01"}, "end": {"date": "2024-08-05"}, "is_new": false}, {"summary": "Flight  UA1726 SEA to EWR", "description": "United Airlines Confirmation #: ETFLX6", "location": "Departing Seattle, WA (SEA)", "start": {"dateTime": "2024-08-20T06:00:00-07:00"}, "end": {"dateTime": "2024-08-20T11:26:00-07:00"}, "is_new": false}, {"summary": "Flight to New York (DL 452)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNVtSmHZs2SaAj3LevgT8rk2Bt1F3EQ-Yg", "location": "Seattle SEA", "start": {"dateTime": "2024-09-16T22:59:00-07:00"}, "end": {"dateTime": "2024-09-17T04:25:00-07:00"}, "is_new": false}, {"summary": "Stay at Staypineapple, An Artful Hotel, Midtown New York", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPttbj568c4Nh4jIYzXA_j7lrkgp-Y8nho", "location": "Staypineapple, An Artful Hotel, Midtown New York, New York", "start": {"date": "2024-09-17"}, "end": {"date": "2024-09-20"}, "is_new": false}, {"summary": "Flight to Seattle (DL 740)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNVtSmHZs2SaAj3LevgT8rk2Bt1F3EQ-Yg", "location": "New York JFK", "start": {"dateTime": "2024-09-19T13:39:00-07:00"}, "end": {"dateTime": "2024-09-19T19:59:00-07:00"}, "is_new": false}, {"summary": "Stay at NOMO SOHO", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPYCsfBBvSWGrq0kAsqgEK8ZBatfDF4Sz8", "location": "NOMO SOHO, New York", "start": {"date": "2024-10-11"}, "end": {"date": "2024-10-16"}, "is_new": false}, {"summary": "Flight to New York (DL 429)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPjOyrR55H4CtDawZJuv2F50osAm5KJqls", "location": "Seattle SEA", "start": {"dateTime": "2024-10-11T07:20:00-07:00"}, "end": {"dateTime": "2024-10-11T12:49:00-07:00"}, "is_new": false}, {"summary": "Flight to Seattle (B6 263)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOua4D3ZCphpkyE-eR54hmfXh7zOqOOaKU", "location": "New York JFK", "start": {"dateTime": "2024-10-15T16:10:00-07:00"}, "end": {"dateTime": "2024-10-15T22:37:00-07:00"}, "is_new": false}, {"summary": "Flight to Phoenix (DL 2442)", "description": "DELTA flight 2442\nSeattle SEA 1:30pm (local time) - Phoenix PHX 5:26pm (local time)\n\nConfirmation number: GAUJL7", "location": "Seattle SEA", "start": {"dateTime": "2024-11-18T13:30:00-08:00"}, "end": {"dateTime": "2024-11-18T16:26:00-08:00"}, "is_new": false}, {"summary": "Flight to Phoenix (DL 2442)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOWW8gtLbpYol8RCX4wjGJUz4y4mLrNOrI", "location": "Seattle SEA", "start": {"dateTime": "2024-11-18T13:30:00-08:00"}, "end": {"dateTime": "2024-11-18T16:26:00-08:00"}, "is_new": false}, {"summary": "Flight to Seattle (DL 2449)", "description": "DELTA flight 2449\nPhoenix PHX 4:10pm (local time) - Seattle SEA 6:25pm (local time)\n\nConfirmation number: GAUJL7", "location": "Phoenix PHX", "start": {"dateTime": "2024-11-21T15:10:00-08:00"}, "end": {"dateTime": "2024-11-21T18:25:00-08:00"}, "is_new": false}, {"summary": "Flight to Seattle (DL 2449)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOWW8gtLbpYol8RCX4wjGJUz4y4mLrNOrI", "location": "Phoenix PHX", "start": {"dateTime": "2024-11-21T15:10:00-08:00"}, "end": {"dateTime": "2024-11-21T18:25:00-08:00"}, "is_new": false}, {"summary": "Caroline flight home", "description": "", "location": "B6374/B6497 - YYOZRO", "start": {"dateTime": "2024-11-23T02:01:00-08:00"}, "end": {"dateTime": "2024-11-23T10:25:00-08:00"}, "is_new": false}, {"summary": "Caroline flight to SYR", "description": "", "location": "B6498/B6845 - YYOZRO", "start": {"dateTime": "2024-11-30T11:35:00-08:00"}, "end": {"dateTime": "2024-11-30T20:48:00-08:00"}, "is_new": false}, {"summary": "Flight to Portland (DL 3953)", "description": "Here are your outbound flight details:\n\tAirline: Delta Air Lines\n\tFlight number: 3953\n\tConfirmation number: 7269174231", "location": "", "start": {"dateTime": "2024-12-11T06:15:00-08:00"}, "end": {"dateTime": "2024-12-11T07:19:00-08:00"}, "is_new": false}, {"summary": "Flight to Portland (DL 3899)", "description": "Here are your return flight details:\n\tAirline: Delta Air Lines\n\tFlight number: 3899\n\tConfirmation number: 7269174231", "location": "", "start": {"dateTime": "2024-12-12T17:50:00-08:00"}, "end": {"dateTime": "2024-12-12T19:00:00-08:00"}, "is_new": false}, {"summary": "Flight to Atlanta (DL 1108)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPTa7zdpnt58VaMlZiqe9HTFha3-kxQpYY", "location": "Syracuse SYR", "start": {"dateTime": "2024-12-14T03:25:00-08:00"}, "end": {"dateTime": "2024-12-14T06:05:00-08:00"}, "is_new": false}, {"summary": "Flight to Seattle (DL 774)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPTa7zdpnt58VaMlZiqe9HTFha3-kxQpYY", "location": "Atlanta ATL", "start": {"dateTime": "2024-12-14T06:40:00-08:00"}, "end": {"dateTime": "2024-12-14T12:35:00-08:00"}, "is_new": false}, {"summary": "Flight to Chicago (DL 2108)", "description": "Here are your outbound flight details:\n\tAirline: Delta Air Lines\n\tFlight number: 2108\n\tAirline confirmation number: HR7QW8", "location": "", "start": {"dateTime": "2025-01-08T18:10:00-08:00"}, "end": {"dateTime": "2025-01-08T22:11:00-08:00"}, "is_new": false}, {"summary": "Flight to Newark (UA 341)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DO_tvArhsE-LEK-xDe20QcIbIujajRJEn0", "location": "Seattle SEA", "start": {"dateTime": "2025-01-11T23:17:00-08:00"}, "end": {"dateTime": "2025-01-12T04:35:00-08:00"}, "is_new": false}, {"summary": "Flight to Syracuse (UA 3605)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DO_tvArhsE-LEK-xDe20QcIbIujajRJEn0", "location": "Newark EWR", "start": {"dateTime": "2025-01-12T05:29:00-08:00"}, "end": {"dateTime": "2025-01-12T06:48:00-08:00"}, "is_new": false}, {"summary": "Flight to SFO (DL 675)", "description": "Here are your outbound flight details:\n\tAirline: Delta Air Lines\n\tFlight number: 675\n\tAirline confirmation number: HH9IBV", "location": "", "start": {"dateTime": "2025-01-13T13:35:00-08:00"}, "end": {"dateTime": "2025-01-13T15:47:00-08:00"}, "is_new": false}, {"summary": "Flight to PHX (DL 2419)", "description": "Here are your outbound flight details:\n\tAirline: Delta Air Lines\n\tFlight number: 2419\n\tConfirmation number: 5315334714", "location": "", "start": {"dateTime": "2025-01-22T07:15:00-08:00"}, "end": {"dateTime": "2025-01-22T10:09:00-08:00"}, "is_new": false}, {"summary": "Flight to PHX (DL 2444)", "description": "Here are your return flight details:\n\tAirline: Delta Air Lines\n\tFlight number: 2444\n\tAirline PNR: None", "location": "", "start": {"dateTime": "2025-01-25T05:15:00-08:00"}, "end": {"dateTime": "2025-01-25T08:35:00-08:00"}, "is_new": false}, {"summary": "Flight to Boise (DL 3959)", "description": "Delta flight 3959\nSeattle SEA 1:20pm (local time) - Boise BOI 4:00pm (local time)\n\nConfirmation number: HXTENJ", "location": "Seattle SEA", "start": {"dateTime": "2025-03-20T13:30:00-07:00"}, "end": {"dateTime": "2025-03-20T15:00:00-07:00"}, "is_new": false}, {"summary": "Flight to Seattle (DL 4127)", "description": "Delta flight 3740\nBoise BOI 9:25am (local time) - Seattle SEA 10:20am (local time)\n\nConfirmation number: HXTENJ", "location": "Boise BOI", "start": {"dateTime": "2025-03-24T13:05:00-07:00"}, "end": {"dateTime": "2025-03-24T14:45:00-07:00"}, "is_new": false}, {"summary": "Flight to SEA - SAN (AS 1258)", "description": "Here are your outbound flight details:\n\tAirline: Alaska Airlines, Inc.\n\tFlight numbers: AS 1258\n\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-05-14T07:00:00-07:00"}, "end": {"dateTime": "2025-05-14T09:52:00-07:00"}, "is_new": false}, {"summary": "Flight to SAN - SEA (DL 2613)", "description": "Here are your outbound flight details:\n\tAirline: Delta Air Lines\n\tFlight numbers: DL 2613\n\tAirline confirmation number:", "location": "", "start": {"dateTime": "2025-05-14T19:15:00-07:00"}, "end": {"dateTime": "2025-05-14T22:25:00-07:00"}, "is_new": false}, {"summary": "Flight to Portland (AS 2346)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNUgNYO2xVDXhPxH4zGep8osZeqsRZCNBI", "location": "Seattle SEA", "start": {"dateTime": "2025-06-22T09:55:00-07:00"}, "end": {"dateTime": "2025-06-22T10:47:00-07:00"}, "is_new": false}, {"summary": "Flight SF (DL 3636)", "description": "Delta Air Lines (DL 3636)\nSeattle\u2013Tacoma International Airport - San Francisco International Airport\nSeat number: None\nCabin: FIRST\nConfirmation number: GNDJ6K", "location": "Seattle\u2013Tacoma International Airport", "start": {"dateTime": "2025-06-25T06:05:00-07:00"}, "end": {"dateTime": "2025-06-25T08:22:00-07:00"}, "is_new": false}, {"summary": "Flight to South Bend (SEA to SBN)", "description": "Leg 1: American Airlines Flight AA 2402 Depart SEA (Seattle): 11:32 PM PDT on June 28, 2025 Arrive DFW (Dallas/Fort Worth): 5:24 AM CDT on June 29, 2025 Seat: 28F Leg 2: American Airlines Flight AA 6261 (Operated by SkyWest Airlines as American Eagle) Depart DFW (Dallas/Fort Worth): 8:30 AM CDT on June 29, 2025 Arrive SBN (South Bend): 12:09 PM EDT on June 29, 2025 Seat: 10D Confirmation Code: DCUTLP", "location": "", "start": {"dateTime": "2025-06-28T23:32:00-07:00"}, "end": {"dateTime": "2025-06-29T09:09:00-07:00"}, "is_new": false}]"#
    messages []
    self_intro #"You are Otto, a professional corporate travel consultant assisting frequent business travelers. You support busy executives with travel planning across flights, hotels, and visas."#
    convo_style #"You speak with efficiency, confidence, and experience—like someone who has booked thousands of trips and knows what matters. You recommend options clearly and proactively, and you avoid generic language like “good option” or “smooth itinerary.” Instead, explain why something works well. Your tone is polished but natural, like a trusted human assistant on the phone. Never repeat yourself or over-explain. Anticipate what the traveler needs next and move the process forward without prompting. Prioritize clarity, timing, and business convenience in every recommendation. You respond with the language the user used in the last message. If no further instruction is given, keep responses under 25 words."#
  }
}

test extract_preferences {
  functions [ExtractNewPreferencesFromCurrentConversation]
  args {
    messages [
      #"
        assistant 
        👋 Hi  Yale&NewLine;&NewLine;**Ready, Set, Otto.**  
        *The more you ask, the better I get.*&NewLine;&NewLine;Let's try planning a trip. Pick one of these examples or just type your own.
        
      "#,
      #"user Reserve a hotel in Paris near the business center from Jul 21st to Jul 26th, with free Wi‑Fi"#,
      #"assistant {"updated_hotel_search_core_criteria":{"city":"Paris","hotel_search_radius":null,"location_latitude_longitude":null,"check_in_date":"2025-07-21","check_out_date":"2025-07-26","is_multi_city_trip":null,"hotel_segments":null},"updated_hotel_search_additional_criteria":{"location_neighborhoods_districts_landmarks":"business center","street_name":null,"general_hotel_preferences":["free Wi‑Fi"],"brand_hotel_preferences":null,"trip_specific_hotel_amenity_preferences":null,"preferred_payment_timings":null,"price_range":null},"updated_hotel_select_result":null,"updated_hotel_select_result_for_multi_city":null,"current_step":"HOTEL_SEARCH","current_searching_segment_index":null,"agent_response":"Sure, here’s what I have for your Paris hotel search:\n\n- City: Paris\n- Check-in: July 21, 2025\n- Check-out: July 26, 2025\n- Preferred area: Near the business center\n- Hotel preference: Free Wi‑Fi\n\nI’m searching for hotels that match these details now. I’ll present tailored options shortly."}"#,
      #"assistant {"presentation_message": "I've selected hotels in Paris's business center with free Wi\u2011Fi and fully refundable rates. Each is within walking distance of major business and cultural landmarks. Let me know if you have a preferred neighborhood or any other specific requirements.", "error_response": "", "hotel_options": [{"selection_reason": {"choice_characteristic": "Luxury Icon", "preference_alignment_reason": "A 5-star hotel in the business center with free Wi\u2011Fi and fully refundable rates.", "distinction_reason_one": "Historic elegance", "distinction_reason_two": "Views of Tuileries and Louvre"}, "nearby_places": "Tuileries Gardens, Louvre Museum, Place Vend\u00f4me, Mus\u00e9e d\u2019Orsay", "property_id": 177304, "rooms": [{"room_id": 17730410, "title": "Eiffel Tower Junior Suite", "description": "This suite features views of the Eiffel Tower, Louver Museum, Grand Palais glass roof and the Tuileries Gardens. Please state your preference for a double or twin bed configuration when booking, this is subject to availability. This room can accommodate a baby cot.", "amenities": ["Trash cans", "Hairdryer", "Sitting area", "Satellite channels", "Shampoo", "Accessible by elevator", "Iron", "Wake-up service", "Landmark view", "Shower cap", "Conditioner", "Wardrobe or closet", "Socket near the bed", "Toilet", "Bathtub or shower", "Toilet paper", "Slippers", "Soundproof", "Free toiletries", "Smoke Alarm", "Radio", "Flat-screen TV", "Walk-in closet", "Private bathroom", "Heating", "Body soap", "Desk", "View", "Linens", "Bathrobe", "Towels", "Upper floors accessible by elevator", "Bottle of water", "Air conditioning", "Sofa", "Telephone", "Minibar", "Ironing facilities", "Electric kettle", "Hand sanitizer", "Adapter", "Carpeted", "Safe", "Tea/Coffee maker"], "product_id": "17730410_362653515_2_2_0_1087617", "price": {"base": 5707.02, "book": 5707.02, "extra_charges": {"conditional": [], "excluded": [{"charge": 22, "mode": "per_person_per_night", "percentage": null, "total_amount": 66.46, "unit_amount": 13.29}, {"charge": 21, "mode": "percentage", "percentage": 10.0, "total_amount": 570.7, "unit_amount": null}], "included": []}, "total": 6344.18}, "policies": {"cancellation": {"free_cancellation_until": "2025-07-18T21:59:59+00:00", "schedule": [{"from": "now", "price": 0.0}, {"from": "2025-07-18T22:00:00+00:00", "price": 1255.54}], "type": "free_cancellation"}, "meal_plan": {"meals": [], "plan": "no_plan"}, "payment": {"prepayment_required": true, "timings": ["pay_online_later", "pay_online_now"]}}, "ranking_reason": "Eiffel Tower Junior Suite offers iconic landmark views, flexible bed configuration, and a lower total price than the Prestige Suite.", "rank": 1}, {"room_id": 17730406, "title": "Prestige Suite", "description": "Inner courtyard view, decorated in a regency style, this suite features views of the courtyard. It is composed of a living area, a large bedroom and 2 bathrooms. Please state your preference for a double or twin bed configuration when booking, this is subject to availability. This room can accommodate a baby cot.", "amenities": ["Childrens' cribs", "Trash cans", "Hairdryer", "Satellite channels", "Shampoo", "Carpeted", "Accessible by elevator", "Iron", "Wake-up service", "Shower cap", "Conditioner", "Wardrobe or closet", "Socket near the bed", "Toilet", "Toilet paper", "Slippers", "Soundproof", "Sofa bed", "Free toiletries", "Smoke Alarm", "Radio", "Shower", "Inner courtyard view", "Flat-screen TV", "Walk-in closet", "Private bathroom", "Heating", "Body soap", "Desk", "Linens", "Bathrobe", "Towels", "Upper floors accessible by elevator", "Bottle of water", "Air conditioning", "Sofa", "Telephone", "Minibar", "Ironing facilities", "Electric kettle", "Additional bathroom", "Hand sanitizer", "Adapter", "Bathtub", "Safe", "Tea/Coffee maker"], "product_id": "17730406_362653515_3_2_0_1087617", "price": {"base": 6823.92, "book": 6823.92, "extra_charges": {"conditional": [], "excluded": [{"charge": 22, "mode": "per_person_per_night", "percentage": null, "total_amount": 66.46, "unit_amount": 13.29}, {"charge": 21, "mode": "percentage", "percentage": 10.0, "total_amount": 682.39, "unit_amount": null}], "included": []}, "total": 7572.77}, "policies": {"cancellation": {"free_cancellation_until": "2025-07-18T21:59:59+00:00", "schedule": [{"from": "now", "price": 0.0}, {"from": "2025-07-18T22:00:00+00:00", "price": 1501.26}], "type": "free_cancellation"}, "meal_plan": {"meals": [], "plan": "no_plan"}, "payment": {"prepayment_required": true, "timings": ["pay_online_later", "pay_online_now"]}}, "ranking_reason": "Prestige Suite provides a spacious layout with separate living area, two bathrooms, and flexible bed setup, though at a higher price point.", "rank": 2}], "property_name": "H\u00f4tel Regina Louvre", "gps_coordinates": "48.863842, 2.332416", "description": "The luxurious H\u00f4tel Regina Louver is located in the heart of Paris in the fashion, shopping and museum district, overlooking the Louver. It first opened in 1900 for the world's fair and is located opposite Tuileries Gardens.\n\nDesigned in an elegant style with period furniture, the air-conditioned rooms feature a flat-screen TV with satellite channels, telephone and safe. Some rooms have views of Tuileries Gardens and the Louver. Free WiFi access is available throughout the hotel and facilities for making coffee and tea can be found in each room.\n\nBoth continental and buffet breakfasts are prepared every day. Children from 6 until 12 years old can enjoy the buffet breakfast at a reduced cost. Guests can enjoy French, seasonal cuisine at the restaurant Salon Rivoli and dine on the terrace on sunny days. You can relax with a refreshing drink in the lounge bar, which has a characteristic British decor or indulge with luxurious teas and pastries.\n\nRoom service, a limousine service and a 24-hour reception with concierge are among the additional services available at this hotel. Pets under 6 kg are also welcome.\n\nThe hotel is just a 2-minute walk from Tuileries Metro Station, which provides direct access to the Opera Garnier and the Champs-Elysees. Place Vend\u00f4me and its luxury boutiques are located 2461 feet away. The Mus\u00e9e d\u2019Orsay is a 15-minute walk from the hotel.", "amenities": ["Valet parking", "Currency exchange", "Shuttle service (additional charge)", "Terrace", "Airport pickup", "Parking", "Breakfast in the room", "Airport shuttle (additional charge)", "Soundproof rooms", "Concierge", "Shuttle service", "Dry cleaning", "Free Wifi", "Restaurant", "Fitness center", "Airport shuttle", "Airport drop-off", "Ironing service", "Fax/Photocopying", "Contactless check-in/out", "Car rental", "Bar", "Special diet meals (on request)", "Meeting/Banquet facilities"], "check_in_time": "15:00:00", "check_out_time": "12:00:00", "hotel_class": "5.0", "overall_rating": "8.8", "check_in_date": "2025-07-21", "check_out_date": "2025-07-26"}, {"selection_reason": {"choice_characteristic": "Boutique Comfort", "preference_alignment_reason": "A 4-star boutique in the business center with free Wi\u2011Fi and fully refundable rates.", "distinction_reason_one": "Prime central location", "distinction_reason_two": "Personalized service"}, "nearby_places": "Tuileries Gardens, Louvre Museum, Place de la Concorde", "property_id": 330533, "rooms": [{"room_id": 33053301, "title": "Tradition Double Room", "description": "This air-conditioned and soundproofed room features a flat-screen TV with satellite channels. The private bathroom has a hairdryer, free toiletries and slippers.", "amenities": ["Childrens' cribs", "Hairdryer", "Satellite channels", "Wake-up service", "Shower cap", "Toilet", "Bathtub or shower", "Toilet paper", "Single-room AC for guest accommodation", "Slippers", "Soundproof", "Free toiletries", "Flat-screen TV", "Private bathroom", "Heating", "Body soap", "Desk", "Bathrobe", "Air conditioning", "Laptop safe", "Telephone", "Minibar", "Hand sanitizer", "Carpeted", "Safe"], "product_id": "33053301_396331324_0_2_0", "price": {"base": 2107.8, "book": 2107.8, "extra_charges": {"conditional": [], "excluded": [{"charge": 22, "mode": "per_person_per_night", "percentage": null, "total_amount": 49.35, "unit_amount": 9.87}, {"charge": 21, "mode": "percentage", "percentage": 10.0, "total_amount": 210.78, "unit_amount": null}], "included": []}, "total": 2367.93}, "policies": {"cancellation": {"free_cancellation_until": "2025-07-18T21:59:59+00:00", "schedule": [{"from": "now", "price": 0.0}, {"from": "2025-07-18T22:00:00+00:00", "price": 2318.58}], "type": "free_cancellation"}, "meal_plan": {"meals": [], "plan": "no_plan"}, "payment": {"prepayment_required": false, "timings": ["pay_at_the_property", "pay_online_later", "pay_online_now"]}}, "ranking_reason": "Best value: Tradition Double Room offers a comfortable layout, soundproofing, and all key amenities at the lowest price with flexible payment options.", "rank": 1}, {"room_id": 33053304, "title": "Deluxe Suite", "description": "This soundproofed and air-conditioned suite features a seating area and a flat-screen TV with satellite channels. The private bathroom has a hairdryer, free toiletries, bathrobes and slippers.", "amenities": ["Childrens' cribs", "Hairdryer", "Sitting area", "Satellite channels", "Wake-up service", "Shower cap", "Toilet", "Bathtub or shower", "Toilet paper", "Single-room AC for guest accommodation", "Slippers", "Soundproof", "Free toiletries", "Flat-screen TV", "Private bathroom", "Heating", "Body soap", "Desk", "Bathrobe", "Air conditioning", "Laptop safe", "Telephone", "Minibar", "Hand sanitizer", "Carpeted", "Safe"], "product_id": "33053304_396331324_0_2_0", "price": {"base": 2691.82, "book": 2691.82, "extra_charges": {"conditional": [], "excluded": [{"charge": 22, "mode": "per_person_per_night", "percentage": null, "total_amount": 49.35, "unit_amount": 9.87}, {"charge": 21, "mode": "percentage", "percentage": 10.0, "total_amount": 269.18, "unit_amount": null}], "included": []}, "total": 3010.36}, "policies": {"cancellation": {"free_cancellation_until": "2025-07-18T21:59:59+00:00", "schedule": [{"from": "now", "price": 0.0}, {"from": "2025-07-18T22:00:00+00:00", "price": 2961.01}], "type": "free_cancellation"}, "meal_plan": {"meals": [], "plan": "no_plan"}, "payment": {"prepayment_required": false, "timings": ["pay_at_the_property", "pay_online_later", "pay_online_now"]}}, "ranking_reason": "Deluxe Suite provides extra space with a dedicated seating area, upgraded amenities, and flexible payment, at a moderate price point.", "rank": 2}, {"room_id": 33053311, "title": "Family Suite", "description": "This suite comes with 1 living room, 1 separate bedroom and 1 bathroom with a bath and free toiletries. The suite has carpeted floors, a seating area with a flat-screen TV with satellite channels, air conditioning, soundproof walls, as well as a mini-bar. The unit has 2 beds.", "amenities": ["Childrens' cribs", "Hairdryer", "Sitting area", "Satellite channels", "Wake-up service", "Shower cap", "Toilet", "Toilet paper", "Single-room AC for guest accommodation", "Slippers", "Soundproof", "Free toiletries", "Flat-screen TV", "Private bathroom", "Heating", "Body soap", "Desk", "Bathrobe", "Bathtub", "Air conditioning", "Laptop safe", "Telephone", "Minibar", "Hand sanitizer", "Carpeted", "Safe"], "product_id": "33053311_396331324_0_2_0", "price": {"base": 3700.59, "book": 3700.59, "extra_charges": {"conditional": [], "excluded": [{"charge": 22, "mode": "per_person_per_night", "percentage": null, "total_amount": 49.35, "unit_amount": 9.87}, {"charge": 21, "mode": "percentage", "percentage": 10.0, "total_amount": 370.06, "unit_amount": null}], "included": []}, "total": 4120.0}, "policies": {"cancellation": {"free_cancellation_until": "2025-07-18T21:59:59+00:00", "schedule": [{"from": "now", "price": 0.0}, {"from": "2025-07-18T22:00:00+00:00", "price": 4070.65}], "type": "free_cancellation"}, "meal_plan": {"meals": [], "plan": "no_plan"}, "payment": {"prepayment_required": false, "timings": ["pay_at_the_property", "pay_online_later", "pay_online_now"]}}, "ranking_reason": "Family Suite is the most spacious, featuring a separate living room and two beds, suitable for those needing extra room or traveling with a companion.", "rank": 3}], "property_name": "Le Pradey", "gps_coordinates": "48.864436, 2.331605", "description": "Set in the very center of Paris, this 4-star hotel has a prime location, just a 5-minute walk from the Louver Museum. The Tuileries Gardens are just across the street and the closest metro station is 328 feet away.\n\nAll rooms have free Wi-Fi access and some suites have a separate seating area and views of the city. A mini-bar and a flat-screen TV with satellite channels are standard facilities in the rooms and suites at Le Pradey. Some rooms feature a private balcony.\n\nGuests can enjoy the continental breakfast from the comfort of their room or select from the buffet in the breakfast room. A large selection of bars, cafes and restaurants is available in the surrounding area.\n\nThe local Tuileries Metro Station is on the capital\u2019s central line and leads directly to the Marais and La D\u00e9fense business district. Guests can also walk the 10 minutes to Place de la Concorde and continue up Avenue Champs Elys\u00e9es.", "amenities": ["Breakfast in the room", "Free Wifi", "Concierge", "Airport shuttle (additional charge)", "Private check-in/out", "Shuttle service", "Express check-in/out", "Airport shuttle", "Airport drop-off", "Shuttle service (additional charge)", "Bar", "Airport pickup"], "check_in_time": "15:00:00", "check_out_time": "12:00:00", "hotel_class": "4.0", "overall_rating": "8.7", "check_in_date": "2025-07-21", "check_out_date": "2025-07-26"}, {"selection_reason": {"choice_characteristic": "Business Friendly", "preference_alignment_reason": "A 4-star hotel in the business center with free Wi\u2011Fi and fully refundable rates.", "distinction_reason_one": "Quiet atmosphere", "distinction_reason_two": "Easy metro access"}, "nearby_places": "Tuileries Gardens, Place Vend\u00f4me, Louvre Museum", "property_id": 53517, "rooms": [{"room_id": 5351714, "title": "Comfort Double Room", "description": "This air-conditioned room offers carpeted floors, a flat-screen TV with satellite channels and a private bathroom with free toiletries and a bath. A Nespresso coffee machine and free capsules are included.\n\nPlease note that children's cots are not available in this room.", "amenities": ["Entire unit located on ground floor", "City view", "Trash cans", "Childrens' cribs", "Hairdryer", "Satellite channels", "Shampoo", "Accessible by elevator", "Walk-in shower", "Wake-up service", "TV", "Wardrobe or closet", "Socket near the bed", "Toilet", "Bathtub or shower", "Toilet paper", "Single-room AC for guest accommodation", "Streaming service (like Netflix)", "Quiet street view", "Free toiletries", "Smoke Alarm", "Shower", "Inner courtyard view", "Flat-screen TV", "Private bathroom", "Concierge services", "Wake-up service/Alarm clock", "Heating", "Body soap", "Coffee machine", "Desk", "View", "Linens", "Key card access", "Free newspapers and magazines in the executive lounge", "Towels", "Upper floors accessible by elevator", "Bottle of water", "Air conditioning", "Non-feather pillow", "Telephone", "Minibar", "Mobile hotspot device", "Electric kettle", "Pay-per-view channels", "Carpeted", "Safe", "Tea/Coffee maker"], "product_id": "5351714_414419051_2_2_0", "price": {"base": 1992.59, "book": 1992.59, "extra_charges": {"conditional": [], "excluded": [{"charge": 22, "mode": "per_person_per_night", "percentage": null, "total_amount": 49.35, "unit_amount": 9.87}, {"charge": 21, "mode": "percentage", "percentage": 10.0, "total_amount": 199.26, "unit_amount": null}], "included": []}, "total": 2241.2}, "policies": {"cancellation": {"free_cancellation_until": "2025-07-19T21:59:59+00:00", "schedule": [{"from": "now", "price": 0.0}, {"from": "2025-07-19T22:00:00+00:00", "price": 438.37}], "type": "free_cancellation"}, "meal_plan": {"meals": [], "plan": "no_plan"}, "payment": {"prepayment_required": true, "timings": ["pay_online_later", "pay_online_now"]}}, "ranking_reason": "Comfort Double Room offers a larger bed, air conditioning, and Nespresso machine; flexible payment and cancellation; best for comfort and amenities.", "rank": 1}, {"room_id": 5351701, "title": "Single Room", "description": "This room includes a private bathroom with free toiletries and a bath or a shower. A Nespresso coffee machine and a free welcome capsule is included.\n\nPlease note that children's cots are not available in this room.", "amenities": ["City view", "Trash cans", "Hairdryer", "Satellite channels", "Shampoo", "Accessible by elevator", "Walk-in shower", "Wake-up service", "TV", "Wardrobe or closet", "Socket near the bed", "Toilet", "Bathtub or shower", "Toilet paper", "Single-room AC for guest accommodation", "Streaming service (like Netflix)", "Quiet street view", "Free toiletries", "Smoke Alarm", "Shower", "Inner courtyard view", "Flat-screen TV", "Private bathroom", "Concierge services", "Wake-up service/Alarm clock", "Heating", "Body soap", "Coffee machine", "Desk", "View", "Linens", "Key card access", "Free newspapers and magazines in the executive lounge", "Towels", "Upper floors accessible by elevator", "Bottle of water", "Air conditioning", "Non-feather pillow", "Telephone", "Minibar", "Mobile hotspot device", "Electric kettle", "Pay-per-view channels", "Carpeted", "Safe", "Tea/Coffee maker"], "product_id": "5351701_414419051_1_2_0", "price": {"base": 1701.11, "book": 1701.11, "extra_charges": {"conditional": [], "excluded": [{"charge": 22, "mode": "per_person_per_night", "percentage": null, "total_amount": 49.35, "unit_amount": 9.87}, {"charge": 21, "mode": "percentage", "percentage": 10.0, "total_amount": 170.11, "unit_amount": null}], "included": []}, "total": 1920.57}, "policies": {"cancellation": {"free_cancellation_until": "2025-07-19T21:59:59+00:00", "schedule": [{"from": "now", "price": 0.0}, {"from": "2025-07-19T22:00:00+00:00", "price": 374.24}], "type": "free_cancellation"}, "meal_plan": {"meals": [], "plan": "no_plan"}, "payment": {"prepayment_required": true, "timings": ["pay_online_later", "pay_online_now"]}}, "ranking_reason": "Single Room provides a lower price point, private bath, Nespresso machine, and flexible payment; suitable for solo travelers seeking value.", "rank": 2}], "property_name": "Hotel Duminy-Vendome", "gps_coordinates": "48.865382, 2.329406", "description": "This 4-star hotel is in the center of Paris just steps from the Tuileries Garden. It features air-conditioned rooms, a bar and business center.\n\nThe rooms at Hotel Duminy-Vendome come equipped with a telephone, satellite TV and private bathroom.\n\nDuminy-Vendome serves a buffet breakfast each morning in the vaulted breakfast room. Guests can relax with a drink in the hotel\u2019s bar where international newspapers are available. A number of restaurants are nearby.\n\nDuminy-Vendome is 650 ft from Tuileries Metro (line 1) and less than half a mile from Pyramides Metro (lines 7 and 14). The Louvre Museum and Champs Elysees are within walking distance of the property.", "amenities": ["Breakfast in the room", "Contactless check-in/out", "Free Wifi", "Soundproof rooms", "Concierge", "Private check-in/out", "Bar", "Dry cleaning", "Special diet meals (on request)", "Ironing service"], "check_in_time": "15:00:00", "check_out_time": "12:00:00", "hotel_class": "4.0", "overall_rating": "8.9", "check_in_date": "2025-07-21", "check_out_date": "2025-07-26"}, {"selection_reason": {"choice_characteristic": "Central Value", "preference_alignment_reason": "A 3-star hotel near the business center with free Wi\u2011Fi and fully refundable rates.", "distinction_reason_one": "Historic building", "distinction_reason_two": "Charming Parisian ambiance"}, "nearby_places": "Quatre-Septembre Metro, Louvre Museum, Palais-Royal", "property_id": 57988, "rooms": [{"room_id": 5798811, "title": "Deluxe Triple Room", "description": "This triple room offers a mini-bar, a TV and a quiet street view. The unit has 3 beds.", "amenities": ["Toilet", "TV", "Telephone", "Minibar", "Quiet street view", "Free toiletries", "Desk", "Bathtub"], "product_id": "5798811_344330244_3_2_0", "price": {"base": 1694.73, "book": 1694.73, "extra_charges": {"conditional": [], "excluded": [{"charge": 22, "mode": "per_person_per_night", "percentage": null, "total_amount": 32.3, "unit_amount": 6.46}, {"charge": 21, "mode": "percentage", "percentage": 10.0, "total_amount": 169.47, "unit_amount": null}], "included": []}, "total": 1896.5}, "policies": {"cancellation": {"free_cancellation_until": "2025-07-18T21:59:59+00:00", "schedule": [{"from": "now", "price": 0.0}, {"from": "2025-07-18T22:00:00+00:00", "price": 1864.21}], "type": "free_cancellation"}, "meal_plan": {"meals": [], "plan": "no_plan"}, "payment": {"prepayment_required": false, "timings": ["pay_at_the_property"]}}, "ranking_reason": "Triple room with three beds, quiet street view, minibar, and pay-at-property option offers flexibility and comfort for a longer stay.", "rank": 1}], "property_name": "H\u00f4tel Baudelaire Op\u00e9ra", "gps_coordinates": "48.867947, 2.336198", "description": "In a 17th-century building, H\u00f4tel Baudelaire Op\u00e9ra is in a lively district of Paris and offers elegant rooms with a balcony opening onto the Parisian roofs. Free Wi-Fi is available in the entire property.\n\nAccessible via a majestic stair, rooms at H\u00f4tel Baudelaire Op\u00e9ra  feature a private bathroom, minibar and TV. Some of them are more spacious and are set on 2 levels.\n\nServed in the vaulted room, the continental breakfast has coffee, hot drinks, orange juice, handmade pastries, French baguettes, butter and jams.\n\nQuatre-Septembre Metro Station is 250 yards away, the Louvre Museum is less than half a mile away and the Th\u00e9\u00e2tre du Palais-Royal is a 5 minute walk from the hotel.", "amenities": ["Free Wifi"], "check_in_time": "15:00:00", "check_out_time": "12:00:00", "hotel_class": "3.0", "overall_rating": "8.1", "check_in_date": "2025-07-21", "check_out_date": "2025-07-26"}], "location_lat_long": {"latitude": 48.864106400000004, "longitude": 2.3348895, "radius": 4827}}"#,
      #"assistant What do you think of these hotels? If you have a favorite brand, like Marriott or Hyatt, just tell me and I'll remember that for next time."#,
      #"user I choose hotel Le Pradey, room id 33053301."#,
      #"assistant {"updated_hotel_search_core_criteria":null,"updated_hotel_search_additional_criteria":null,"updated_hotel_select_result":{"property_id":330533,"room_product_id":"33053301_396331324_0_2_0","hotel_name":"Le Pradey","room_title":"Tradition Double Room","payment_timing":"PAY_AT_THE_PROPERTY","cancellation_type":"FREE_CANCELLATION"},"updated_hotel_select_result_for_multi_city":null,"current_step":"HOTEL_VALIDATION","current_searching_segment_index":null,"agent_response":"Great choice. Le Pradey’s Tradition Double Room offers a prime central location, free Wi‑Fi, and flexible payment with free cancellation. I'm confirming your hotel selection. Please hold a moment."}"#,
      #"assistant {"room_product_id":"33053301_396331324_0_2_0","property_id":330533,"agent_response":"- Hotel Name: Le Pradey\n- Check-in: July 21, 2025\n- Check-out: July 26, 2025\n- Room: Tradition Double Room\n- Payment: Multiple options (pay at property, online now/later)\n- Cancellation: Free until July 18, 2025\n**Total hotel price: $2367.93**\n\nPlease confirm the price and details before I proceed to booking.","validated_price":2367.93,"currency":"USD"}"#,
      #"user confirm"#,
      #"assistant {"updated_hotel_search_core_criteria":null,"updated_hotel_search_additional_criteria":null,"updated_hotel_select_result":null,"updated_hotel_select_result_for_multi_city":null,"current_step":"HOTEL_BOOKING","current_searching_segment_index":null,"agent_response":"I'm processing your hotel booking now — hold tight...."}"#,
      #"assistant {"accomodation_reservation_number": "1111111111", "accomodation_order_number": "2793056516", "accomodation_pincode_number": "7909", "hotel_selection": "Le Pradey", "property_id": 330533, "room_product_id": "33053301_396331324_0_2_0", "agent_response": "Your booking at the Le Pradey has been successfully completed through our hotel partner.\n- Order number: 2793056516\n- Reservation number: 1111111111\n- Hotel: Le Pradey\n- Room: Tradition Double Room\n- Price: $2367.93\nI\u2019ll add this to your calendar. You\u2019ll also receive a confirmation email from Booking.com. Any further questions?", "validated_price": 2367.93, "currency": "USD", "check_in_date": "2025-07-21", "check_out_date": "2025-07-26", "room_title": "Tradition Double Room", "payment_timing": "pay_at_the_property"}"#
    ]
    current_preferences {
      preferred_airline_brands [

      ]
      preferred_cabin [

      ]
      preferred_home_airport #"SEA"#
      preferred_hotel_brands [

      ]
      preferred_seats [

      ]
    }
  }
}
