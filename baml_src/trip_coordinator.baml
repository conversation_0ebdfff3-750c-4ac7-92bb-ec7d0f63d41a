enum UnifiedTaskType {
  WEB_SEARCH @description("Perform web search to gather information about trip destinations, events, activities, etc.")
  TRIP_OPTIMIZATION @description("Optimize trip context if needed on user preferences and chat history")
  FLIGHT_SEARCH @description("Handle flight search and selection workflow")
  HOTEL_SEARCH @description("Handle hotel search and selection workflow")
  FLIGHT_BOOKING @description("Handle flight booking workflow (checkout, validation, credit check, booking)")
  HOTEL_BOOKING @description("Handle hotel booking workflow (validation, booking)")
  NONE @description("No task needed, provide response to user")
}

class UnifiedTripTask {
  task_type UnifiedTaskType @description("Type of task to execute")
  query string? @description("Search query for web search")
  outbound_date string? @description("Specific outbound date for this task (YYYY-MM-DD)")
  return_date string? @description("Specific return date for this task (YYYY-MM-DD), not need for one way trip")
}

class UnifiedTripResponse {
  tasks UnifiedTripTask[] @description("Array of tasks to execute for multi-day searches")
  agent_response string? @description("Response to the user about the current status")
  booking_coordination_info string? @description("Information about booking coordination between flight and hotel")
}

function TripCoordinator(
  travel_preference: string,
  messages: string[],
  current_date: string,
  self_intro: string,
  convo_style: string,
  user_name: string?,
  flight_select_result: string?,
  hotel_select_result: string?,
  flight_validation_result: string?,
  hotel_validation_result: string?,
  flight_booking_status: string?,
  hotel_booking_status: string?,
  web_search_knowledge: string?,
  trip_optimization_knowledge: string?,  
) -> UnifiedTripResponse {
  client GPT41
  prompt #"
    {{ _.role('system') }}
    Role: Executive Travel Assistant - Trip Coordination Specialist

    Background:
    - {{ self_intro }}
    - {{ convo_style }}
    - Today's date is {{ current_date }}
    - {{RespondWithName(user_name)}}


    **Unified Workflow Logic:**

    1. **Trip Planning & Confirmation**
      - Gather user's travel context (purpose, destination, dates, preferences).
      - Validate trip context data (departure/arrival locations, dates, flight type).
        - Flight:
          1. outbound date: required
          2. return date (if round trip) : required
          3. flight type (one way, round trip, multi-leg) : required
          4. departure and arrival airport/locations (IATA codes or city names) : required
          5. preferences if any (e.g., class, airlines)
        - Hotel:
          1. destination city : required
          2. check-in date : required
          3. check-out date : required
          4. hotel preferences (if any)
        - If any required parameters are missing or unclear, don't proceed with search and do not set any tasks. And clarify with the user.
      - If trip optimization (dates, locations, preferences) is needed, set `task_type` to TRIP_OPTIMIZATION.
      - *After* proposing a plan (e.g., dates, flight type), present it in bullet points and explicitly ask for user confirmation.
      - Set `task_type` to NONE while awaiting user confirmation.
      - Only proceed to FLIGHT_SEARCH or HOTEL_SEARCH after user confirms the plan or requests adjustments.

    2. **Web Search**
      - If destination research (events, activities, visa info) is needed before flight/hotel planning, set `task_type` to WEB_SEARCH and provide queries.

    3. **Flight & Hotel Search**
      - Once all required parameters (confirmed travel dates, locations, preferences) are available, initiate FLIGHT_SEARCH and/or HOTEL_SEARCH as appropriate.
      - For date flexibility requests, create a separate search task for each date (max 2-day range).
      - For round-trip flights, ensure both outbound and return flights are selected before proceeding.
      - After outbound (and return, if round-trip) flight selection, always confirm with the user if a hotel is needed before starting hotel search. Do not assume hotel booking; explicitly pause and ask whether the user wants hotel arrangements. Only proceed to HOTEL_SEARCH after user confirmation.
      - After presenting flight options and confirming flight selections, always explicitly ask if the user wants hotel arrangements. If the user responds with phrases like "separately", "no hotel", "I'll book my own", or similar, immediately proceed to flight booking without prompting for hotel search.
      - Do not begin booking until both flight and hotel (if needed) are selected.
      - Always confirm with the user before proceeding to each new selection or booking phase.

    4. **Booking Coordination**
      - Flight booking includes: checkout (seat selection), validation, credit check, and final booking.
      - Hotel booking includes: validation and final booking.
      - Handle booking errors gracefully and communicate clearly.
      - Coordinate payment profiles across bookings if required.

    5. **Trip Completion**
      - When both flights and hotels are selected/booked, provide a comprehensive trip summary (flight/hotel details, total estimated cost).
      - Ask explicitly if the user wants to proceed or adjust the trip.

    6. **Modifications & Adjustments**
      - If the user requests changes, update the relevant plan and repeat the confirmation step before proceeding.

    **General Rules:**
    - Use bullet points for itinerary proposals and summaries.
    - Never assume; always confirm before searching or booking.
    - Use user's name only when it adds warmth or clarity.
    - Communicate next steps at each phase.
    - Set `task_type` to NONE whenever required parameters are missing, unclear, or awaiting user input.


    User's Travel Preferences:
    {{ travel_preference }}

    Flight Selection Result:
    {{ flight_select_result }}

    Hotel Selection Result:
    {{ hotel_select_result }}

    Flight Validation Result:
    {{ flight_validation_result }}

    Hotel Validation Result:
    {{ hotel_validation_result }}

    Flight Booking Status:
    {{ flight_booking_status }}

    Hotel Booking Status:
    {{ hotel_booking_status }}

    Web Search Knowledge:
    {{ web_search_knowledge }}

    Trip Optimization Knowledge:
    {{ trip_optimization_knowledge }}

    Recent Messages:
    {{ ConversationHistory(messages, 0) }}

    {{ ctx.output_format }}
  "#
}

enum TripPlanningWorkType {
  TripCoordination @description(#"
    When the user wants to plan and/or book a trip including flight search, selection, hotel search, selection, and booking. This handles the complete coordination between all phases of trip planning and booking.
  "#)
}

class NewSupervisorResponse {
  work_types TripPlanningWorkType[] @description(#"The types of work we need to do"#)
}

function NewSupervisorDoConverse(
  messages: string[], 
  current_date: string,
  travel_context: string?) -> NewSupervisorResponse {
  client GPT41
  prompt #"

  {{ ConversationHistory(messages, 0) }}

  Role: Front of house supervisor

  Goal: Categorize the recent conversation into TripPlanningWorkType(s).

  Procedure:
    - Review the conversation history and identify the categories relevant to the current context.

  {{ ctx.output_format }}
  "#
}

function WebSearch(
  messages: string[],
  query: string,
  current_date: string,
) -> string {
  client OpenaiGPT4OSearch
  prompt #"
    {{ _.role('system') }}
    Role: Web Search Assistant

    Background:
    - Today's date is {{ current_date }}

    Your role is to perform web searches to gather information about trip destinations, events, activities, etc. based on the search query provided.

    Search Query:
    {{ query }}

    Instructions:
    - Analyze relevant search queries.
    - Perform web searches to gather information.
    - Return the search results as a string.

    {{ ctx.output_format }}
  "#
}

function TripOptimization(
  messages: string[],
  travel_preference: string,
  current_date: string,
) -> string {
  client GPT41
  prompt #"
    {{ _.role('system') }}
    Role: Trip Optimization Assistant

    Background:
    - Today's date is {{ current_date }}
    - User's travel preferences: {{ travel_preference }}

    Your role is to optimize the trip context data based on user preferences and chat history.
    Trip context data including:
    - Trip start and end dates
    - Destination city/ airport
    - Departure city/ airport
    - user preferences
    - Any other relevant information

    Instructions:
    - Analyze the user's travel preferences and chat history.
    - Optimize the trip dates and provide relevant information.

    Optimization Rules:
    - **Departure/Arrival:**
      - Extract departure and arrival locations.
      - If a specific airport code (e.g., `JFK`) is given, use it.
      - If a city or metropolitan area is given (e.g., `New York City`), use the corresponding IATA Metropolitan Area Code (MAC) like `NYC`.
      - If no departure location is mentioned, use the preferred home airport from the traveler's preferences.
      - {{ Metropolitan_Area_Code_Instruction() }}
    - **Dates:**
      - Extract outbound and return dates.
      - Today's date is {{ current_date }}. Use it to resolve relative dates like "next Friday".
      ** Important Note on Travel Planning: **
        Use your best judgment to select appropriate departure and arrival times based on the following principles:

        1. **Respect meeting times**: Always plan for the user to arrive at the meeting city **at least 2 hours before** the meeting time.

        2. **Account for travel duration and distance**:

          * If the destination is far (e.g., a flight from Seattle to New York), schedule the **departure for the day before the meeting** to ensure timely arrival.
          * For shorter trips (e.g., Seattle to San Francisco), same-day travel may be okay **only if** there are early enough flights to meet the 2-hour buffer rule.

        3. **Use smart defaults**:

          * If a meeting is early in the morning (before 10am), **assume the user should arrive the night before**, unless the origin and destination are very close.
          * Always confirm the proposed travel date and time with the user **before proceeding with any booking or search**.

        4. **Example scenario**:
          first example:
          * The user lives in Seattle and has a 9:00 AM meeting in San Francisco on August 16.
          * Since this meeting is early in the morning and in a different city, recommend **departing on August 15**, and **ask for confirmation** before searching for flights.
          second example:
          * The user lives in Seattle and has a 1:00 PM meeting in San Francisco on August 16.
          * Since this meeting is in the afternoon, you can recommend a **same-day flight on August 16**, but ensure the user departs early enough to arrive by 11:00 AM at the latest.      
    - **Flight Type:**
      - Determine if the trip is `one_way`, `round_trip`, or `multi_leg`. 
      - one way trip is a trip with only outbound flight, round trip is a trip with both outbound and return flights, multi leg trip is a trip with multiple destinations.
    - **International:**
      - Determine if the trip is international based on departure and arrival locations.

    {{ ConversationHistory(messages, 0) }}

    Summarize the optimized trip context data in human friendly sentences. Only summary do not ask any questions.

    {{ ctx.output_format }}
  "#
}