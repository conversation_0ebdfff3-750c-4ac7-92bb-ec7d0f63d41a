class SeatMatchingResult {
  free_seat string? @description(#"
    The seat that was selected by the process in the format of ROW, COLUMN. e.g. 1A
    the price of free seat is 0.
  "#)
  paid_seat string? @description(#"
    The paid seat that was selected by the process in the format of ROW, COLUMN. e.g. 1A
    the price of paid seat is not 0.
  "#)
  agent_response string @description(#"
    A message to summarize the result of the seat matching for the flight.
    e.g."I found two seat options for you: A free window seat 19C in row 19, or a paid extra-legroom aisle seat 10B in row 10 for $48. Which would you prefer for Flight AA123 from SEA to LAX? Ok to proceed to select seat for next flight: Flight AA456 from LAX to SFO?"
    If we can't find any available seats matching the preference, the message should be "We can't find any available seats matching your preference. ok to proceed to select seat for next flight: Flight AA456 from LAX to SFO?"
    If there is no next flight, the message should be "I found two seat options for you: A free window seat 19C in row 19, or a paid extra-legroom aisle seat 10B in row 10 for $48. Which would you prefer for Flight AA123 from SEA to LAX? Are you okay to proceed to next step?"
    If there is old seat selection, and seat map is not blank, return something like "Your original seat selection is 19C. I found two new options for you: A free window seat 19D, or a paid extra-legroom aisle seat 10B for $48. Which would you prefer? Ok to proceed to select seat for next flight: Flight AA456 from LAX to SFO?"
    If there is old seat selection, but seat map is blank, return something like "Your original seat selection is 19C. There're no available seats to change. ok to proceed to select seat for next flight: Flight AA456 from LAX to SFO?"
    if the seat_map is blank, the agent_response should be "There're no available seats for Flight AA123 from SEA to LAX. Proceed to select seat for next flight: Flight AA456 from LAX to SFO?"
    Always clearly indicate which seats are free and which are paid, including the price for paid seats.
  "#)
  seat_selection_reason string? @description(#"
    The reason for selecting the seat up to 10 words. e.g. "best forward aisle seat"
  "#)
}

function DoSeatSelection(
  seat_map: string,
  preferred_seat: string,
  current_flight: string,
  all_flights: string,
  allow_paid_seat: bool?,
  next_flight: string?,
  self_intro: string?,
  convo_style: string?,
) -> SeatMatchingResult {

  client GPT4o
  prompt #"
    {{ _.role("system")}}
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Your job is select best seat for the passenger match the preferred seat.
    All flights are {{ all_flights }}.
    Current flight to select seat is {{ current_flight }}.
    Next flight to select seat is {{ next_flight }}.
    The preferred seat is {{ preferred_seat }}. 

    ---- 
    The seat map is in the format of a string with rows and columns with following content:
    {{ seat_map }}
    ----

    You need to find the best seat that matches the preferred seat. Consider both free and paid options, but only return ONE best match in the seat field.
    In your agent_response, present both free and paid seat options to the traveler, clearly indicating which is which and including the price for paid seats.
    Include information about the traveler's company travel policy when showing paid seat options.
    {% if allow_paid_seat %}
    The company travel policy allows paid seat selection.
    {% elif allow_paid_seat == None %}
    You don't know if the company travel policy allows paid seat selection.
    {% else %}
    The company travel policy does not allow paid seat selection.
    {% endif %}
    For example: "I found two seat options for you: A free window seat 19C in row 19, or a paid extra-legroom aisle seat 10B in row 10 for $48. Please note, your company travel policy doesn't allow paid seat upgrades. Which would you prefer?"
    
    For the seat field in the result, select only ONE best match based on the traveler's preferences. If there are both free and paid seats that match the preference equally well, prioritize the free seat.
    Make sure to set the is_paid_seat field to true if the selected seat is paid, and false if it's free.
    Set the seat_price field to the price of the seat if it's paid, or 0 if it's free.

    The paid seat should be materially better than the best available free seat (e.g., closer to the front, aisle/window preference, exit row, extra legroom).
    
    If exact preference match unavailable, return best partial match with brief explanation of trade-offs made and why selected seat is next best option.
    If you can't find seat match traveler's seat preference, then return something like "We can't find any available seats matching your preference."

    There're also some special cases:
    If there is old seat selected in current flight, but seat map is blank, then we can say "There're no available seats to change. You selected seat: 19C before. Proceed to select seat for next flight : Flight AA789 SEA to SFO?"
    If there is old seat selected in current flight, we still want to re-select seat for current flight, based on the preferred seat.
    If the seat map is blank, then tell the traveler "For this flight, the airline does not allow people to choose their seats." and ask the traveler to proceed to select seat for next flight.

    --------
    For example:
    the seat map could be:
    ROW, COLUMN, SEAT_TYPE, LOCATION, IS_WING, PRICE
    1, A, WINDOW, FRONT, FALSE, 0
    2, E, AISLE, MIDDLE, FALSE, 25

    And traveler's preferred seat type is WINDOW, then you should:
    1. In the agent_response, present both free and paid window seat options: "I found two seat options for you: A free window seat 1A in row 1, or a paid extra-legroom window seat 2B in row 2 for $25."
    2. For the seat field, select only ONE best match: seat 1A (since it's free and matches the preference)
    3. Set is_paid_seat to false
    4. Set seat_price to 0
    
    If the traveler's preference was AISLE and there were no free aisle seats, you would:
    1. In the agent_response, present the paid aisle seat option: "I found a paid aisle seat 2E in row 2 for $25 that matches your preference."
    2. For the seat field, select seat 2E
    3. Set is_paid_seat to true
    4. Set seat_price to 25
    --------


    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}

class AvailableSeatMatchingResult {
  agent_response string @description(#"
    message to summarize the result of all available seats in organized manner.
  "#)
}

function DoAvaiableSeatSelection(
  seat_map: string?,
  preferred_seat: string,
  current_flight: string,
  self_intro: string?,
  messages: string[],
  convo_style: string?) -> AvailableSeatMatchingResult {

  client GPT4o
  prompt #"
    {{ _.role("system")}}
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}

    {{ ConversationHistory(messages, -5) }}

    Your job is to identify and present all available seats for the passenger on the flight. The passenger's preferred seat type is {{ preferred_seat }}.

    The seat map is provided in the following format:
    ```
    ROW, COLUMN, SEAT_TYPE, LOCATION, IS_WING
    {{ seat_map }}
    ```

    If the `seat_map` is blank, null, or 'None', please respond with a message indicating that we don't have information about the seats for this flight.

    Otherwise, process the `seat_map` to identify and present **all available seats**. If there is too many seats, only list top 20 best seats. For each available seat, you must clearly indicate whether it matches the passenger's `preferred_seat` type.

    Your response should:
    1.  **List all available seats** parsed from the `seat_map`.
    2.  For each seat, clearly present its properties: `ROW`, `COLUMN`, `SEAT_TYPE`, `LOCATION`, `IS_WING` and `Facilities`.
    3.  Clearly mark which of these seats **match the passenger's preferred seat type** (e.g., "PREFERRED MATCH").
    4.  Present the information in a clear, organized, and readable format, such as a table or a structured list.
    5.  If user asks question about the seats, please respond.

    **Example of desired output structure (if seats are available):**

    ```
    Available Seats for Flight {{ current_flight }} (Preferred Type: {{ preferred_seat }}):

    | Row | Column | Seat Type | Location | Is Wing | Preferred Match |
    |-----|--------|-----------|----------|---------|-----------------|
    | 1   | A      | WINDOW    | FRONT    | FALSE   | YES             |
    | 1   | B      | MIDDLE    | FRONT    | FALSE   | NO              |
    | 1   | C      | AISLE     | FRONT    | FALSE   | NO              |
    | 2   | D      | AISLE     | MIDDLE   | FALSE   | NO              |
    | 2   | E      | WINDOW    | MIDDLE   | FALSE   | YES             |
    | ... | ...    | ...       | ...      | ...     | ...             |
    ```


    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}


function SummarizeSeatSelection(
  selected_seats: string,
  self_intro: string?,
  convo_style: string?) -> string {

  client GPT4o
  prompt #"
    {{ _.role("system")}}
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Summarize the seat selection and reason for all flights and ask the traveler to confirm the seat selection.
    for exmaple: 
    ---
    I have selected your seats as follows:
    Flight AA123 from NYC to LAX - Seat 19C because it is the best aisle seat available.
    Flight AA456 from LAX to SFO - Seat 19C because it is the best aisle seat available.
    Are you okay to proceed to next step?
    ---

    If no seat is selected, return something like "We can't find any available seats for you. you can still book the flight without seat selection. Are you okay to proceed to next step?"

    flight with selected seats are {{ selected_seats }}

    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}

function SummarizeSeatSelectionForChange(
  selected_seats: string,
  all_flights: string,
  self_intro: string?,
  convo_style: string?) -> string {

  client GPT4o
  prompt #"
    {{ _.role("system")}}
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Summarize the seat selection and reason for all flights.

    The response can be something like:
    ---
    I have selected your seats as follows:
    Flight AA123 from NYC to LAX - Seat 19C because it is the best aisle seat available.
    Flight AA456 from LAX to SFO - Seat 19C because it is the best aisle seat available.
    Please confirm the seat change.
    ---

    If no seat is selected, return something like "We can't find any available seats for you to change."
    Always clearly indicate which seats are free and which are paid, including the price for paid seats.
    If all flights do not have any available seats, return "We can't find any available seats for you to change."

    all flights are {{ all_flights }}.
    selected seats are {{ selected_seats }}

    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}

class SelectedSeat {
  flight_index int @description(#"
    The index of flight stop in the trip for which the traveler has selected a seat. start from 0.
    for example: if outbound flight contains SEA-SFO, SFO-LAX, then the index of SEA-SFO is 0, SFO-LAX is 1.
                 and if there is return flight as well, which contains LAX-SFO, SFO-SEA, then the index of LAX-SFO is 2, SFO-SEA is 3.
  "#)
  seat_number string @description(#"
    The seat number that the traveler has newly selected or changed for the flight stop.
    The seat number is in the format of ROW, COLUMN. e.g. 1A, 2B
    other format is not allowed.
  "#)
  price float? @description(#"
      The price of the selected seat.
  "#)  
}

function SummarizeSeatUpdateResult(
  result: string,
  self_intro: string?,
  convo_style: string?) -> string {
  
    client GPT4o
    prompt #"
      {{ _.role("system")}}
      {{ self_intro | default(GetSelfIntro()) }}
      {{ convo_style | default(GetConvoStyle()) }}
      Summarize the result of the seat update.
      for exmaple: 
      The seat selection is updated. 
      or if there is error: The seat selection is not updated. The error is Bad gateway from external service, please try again.
      if the result is empty, for example : "{}", it means the seat selection is updated successfully.
  
      ---
      result is {{ result }}.
      ---
  
      {{ _.role("system")}}
      {{ ctx.output_format }}
    "#
}

enum SeatSelectionChangeStep {
  NONE @description("If you are missing any required information, set to this value.")
  START_SEAT_SELECTION
  ASK_TO_SHOW_AVAILABLE_SEATS
  SELECT_SEAT_FOR_STOPS
  CONFIRM_SEAT_SELECTION
  SUBMIT_SEAT_SELECTION
}

class ChangeSeatState {
  agent_response string @description(#"
      If travler want to change seat, then tell the traveler that you are starting the seat change process.
      or tell the traveler that you are submitting the seat selection.
      or If traveler is asking available seats for a flight stop, tell the traveler that you are searching for available seats for the flight stop.    
  "#)
  airline_confirmation_number string? @description(#"
      The airline confirmation number for the flight.
      It is required to start the seat selection process.
      If it is not provided, then you need to ask the traveler to provide the airline confirmation number.
    "#)
  preferred_seat_types string[]? @description("The traveler's preferred seat types. for example: ['window', 'aisle', 'middle', 'front of plane', 'middle of plane', 'any seat'] etc. any seat (or 'no preference' or similar words) means traveler prefer any seats")
  current_step SeatSelectionChangeStep? @description(#"
      The current step of the seat selection process.
    "#)
  current_flight_index_to_select_seat int? @description(#"
      The index of flight stop in the trip for which the traveler wants to select a seat. start from 0.
      for example: if the trip contains stops: SEA-SFO, SFO-LAX, then SEA-SFO is 0, SFO-LAX is 1.
    "#)
  is_seat_number_provide_by_traveler bool? @description(#"
      does the traveler provide the seat for a flight stop?
    "#)
  selected_seats_want_add_or_change SelectedSeat[]? @description(#"
      The seats that the traveler has selected for the flight stops in the trip. 
      It is either the newly added seat selection or the change the existing seat selection.
      Only include the changed or added seats after the traveler booked the flight.
      Don't include already updated seats.
    "#)
}

class DeltaUpdateOfChangeSeatState {
  updated_change_flight_state ChangeSeatState? @description(#"
    The DELTA update of the change flight state.
  "#)
}

function DoChangeSeatSelectionConversion(
  travel_context: string,
  self_intro: string,
  convo_style: string?,
  previous_change_seat_state: string?,
  messages: string[],
  user_name: string?,
  ) -> DeltaUpdateOfChangeSeatState {
  client AzureGPT4o
  prompt #"
    {{ ConversationHistory(messages, 0) }}

    {{ _.role("system")}}
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    {{RespondWithName(user_name)}}
    ---
    The main steps for the seat change are:
    1. Collect required airline confirmation id. making sure the airline confirmation id is not blank or null.
    2. We start to add seat or chagne exisiting seat selection process.
        There're 2 scenarios for seat selection:
        1. If you don't have seat types preference of the traveler, usually it is first time traveler to select seat. You need to ask the traveler to provide seat types preference.
        2. If you have seat types preference of the traveler, you start select seats for all flight stops in the trip together and provide the selection if available.
    3. summarize the seat selection for all flight stops and ask the traveler to confirm.
    4. submit the seat selection.    

    The order of these steps must be followed.

    There is some special cases:
    1. You support both free and paid seat selection. When selecting seats, consider both free and paid options that match the traveler's preferences.
    2. If User asks to see a seat map during the seat selection process. can tell the traveler that you can't show a seat map right now. ask the traveler if they want to see some other available seats?
       If yes, you can list some available seats for the traveler to choose from.
    3. The traveler also can just ask to do seat selection for a specific flight stop.
    ---
    Previous change seat state is {{ previous_change_seat_state }}.
    ---
    {{ _.role("system")}}
    Please provide the DELTA update of the change flight state.
    {{ ctx.output_format }}
  "#
}


class ChangeSeatStateV2 {
  airline_confirmation_number string? @description(#"
      The airline confirmation number for the flight.
      It is required to start the seat selection process.
      If it is not provided, then you need to ask the traveler to provide the airline confirmation number.
    "#)
  preferred_seat_types string[]? @description("The traveler's preferred seat types. for example: ['window', 'aisle', 'middle', 'front of plane', 'middle of plane', 'any seat'] etc. any seat (or 'no preference' or similar words) means traveler prefer any seats")
  current_step SeatSelectionChangeStep? @description(#"
      The current step of the seat selection process.
    "#)
  current_flight_index_to_select_seat int? @description(#"
      The index of flight stop in the trip for which the traveler wants to select a seat. start from 0.
      for example: if the trip contains stops: SEA-SFO, SFO-LAX, then SEA-SFO is 0, SFO-LAX is 1.
    "#)
  is_seat_number_provide_by_traveler bool? @description(#"
      does the traveler provide the seat for a flight stop?
    "#)
  selected_seats_want_add_or_change SelectedSeat[]? @description(#"
      The seats that the traveler has selected for the flight stops in the trip. 
      It is either the newly added seat selection or the change the existing seat selection.
      Only include the changed or added seats after the traveler booked the flight.
      Don't include already updated seats.
    "#)
  follow_up_question string? @description(#"
      A short, concise follow-up question to ask the traveler ONLY when additional input is needed to proceed.
      Leave empty/null if the user has provided sufficient information or given clear instructions.

      Examples when to include:
      - Need preferences: 'What's your seat preference - window, aisle, or any location?'
      - Need choice: 'Which seat - 12A (free window) or 8C (paid aisle, $35)?'
      - Need confirmation: 'Ready to confirm these seat selections?'

      Keep under 15 words and directly actionable.
  "#)
}

class DeltaUpdateOfChangeSeatStateV2 {
  updated_change_flight_state ChangeSeatStateV2? @description(#"
    The DELTA update of the change flight state.
  "#)
}

function DoChangeSeatSelectionConversionV2(
  travel_context: string,
  previous_change_seat_state: string?,
  messages: string[],
  ) -> DeltaUpdateOfChangeSeatStateV2 {
  client GPT41
  prompt #"
      {{ ConversationHistory(messages, 0) }}

      {{ _.role("system")}}
      You are managing the seat change conversation flow. Follow this process:

      **Seat Change Process:**
      1. **Start seat selection** (START_SEAT_SELECTION) - Begin the seat change process
      2. **Collect seat preferences** - If this is the traveler's first seat selection, ask for their preferred seat types (window, aisle, front of plane, etc.)
      3. **Find and present options** - Once you have preferences, search all flight segments and present the best available seats (both free and paid options)
      4. **Confirm selection** - Summarize all selected seats across flight segments and ask for traveler confirmation
      5. **Submit changes** - Process the final seat selection updates

      **Important Guidelines:**
      - Always consider both free and paid seat options that match traveler preferences
      - Present paid options with clear pricing and note any company travel policy restrictions
      - If traveler asks for a seat map, explain you can't show visual maps but offer to list available seats instead
      - Support requests for specific flight segments (e.g., "change my seat on the first flight only")
      - Maintain conversational flow - don't restart the process if already in progress

      **Special Scenarios:**
      - **No preferences yet**: Ask "What's your seat preference - window, aisle, or any specific location on the plane?"
      - **Has preferences**: Immediately search and present options: "I found these seat options for your flights..."
      - **Seat map request**: "I can't show a visual seat map, but I can list all available seats. Would you like to see your options?"

      ---
      Previous seat change state: {{ previous_change_seat_state }}
      ---

      {{ _.role("system")}}
      Provide the DELTA update for the seat change state based on the conversation.
      {{ ctx.output_format }}
  "#
}

function SummarizeSeatSelectionResponse(
  agent_response: string,
  self_intro: string?,
  convo_style: string?) -> string {

  client GPT41
  prompt #"
    {{ _.role("system")}}
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Rephrase the original seat selection response by following below example:
    "The best free aisle seat available is 33D. However, there's an aisle exit row seat, 18D, available for $50 that offers more legroom. Which one would you prefer?"
    ----
    Original seat selection response: {{ agent_response }}
    ----
    Note:
    - only keep one question in the response.
    
    {{ ctx.output_format }}
  "#
}
