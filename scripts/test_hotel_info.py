#!/usr/bin/env python3
import os
import sys

from in_trip.in_trip import InTripHotelBooking

project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)


def test_hotel_info_creation():
    """Test creating InTripHotelInfo objects."""

    hotel_info = InTripHotelBooking(
        id=1,
        hotel_name="Executive Hotel Pacific",
        check_in_date="2025-07-21",
        check_in_time="15:00:00",
        check_out_date="2025-07-23",
        check_out_time="11:00:00",
        room_type="Superior King",
        address="400 Spring Street, Seattle, WA 98104",
        confirmation_number="1917",
    )

    print("✅ InTripHotelInfo created successfully!")
    print(f"Hotel: {hotel_info.hotel_name}")
    print(f"Check-in: {hotel_info.check_in_date} at {hotel_info.check_in_time}")
    print(f"Check-out: {hotel_info.check_out_date} at {hotel_info.check_out_time}")
    print(f"Room: {hotel_info.room_type}")
    print(f"Address: {hotel_info.address}")
    print(f"Confirmation: {hotel_info.confirmation_number}")

    assert hotel_info.hotel_name == "Executive Hotel Pacific"
    assert hotel_info.check_in_date == "2025-07-21"
    assert hotel_info.room_type == "Superior King"

    print("✅ All field access tests passed!")


if __name__ == "__main__":
    test_hotel_info_creation()
    print("\n🎉 All hotel info tests completed successfully!")
