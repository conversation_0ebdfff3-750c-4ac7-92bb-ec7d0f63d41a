#!/usr/bin/env python3
"""
Script to run handle_spotnana_webhook_event with production database connection.
This script reads a JSON file and processes it through the webhook handler.
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from server.schemas.partners.spotnana.travel_delivery import SpotnanaTravelDelivery
from server.services.partners.spotnana.webhook_event_handler import handle_spotnana_webhook_event
from server.utils.logger import logger


async def dummy_route_to_staging(data: SpotnanaTravelDelivery):
    """
    Dummy function to replace route_to_staging since we want to process in prod.
    This function does nothing - we want all processing to happen in production.
    """
    logger.info(f"[SCRIPT] Dummy route_to_staging called for trip_id: {data.payload.get('tripId', 'unknown')}")
    pass


async def main():
    """
    Main function to run the webhook event handler with production database.
    """
    # Check if file path is provided
    if len(sys.argv) != 2:
        print("Usage: python scripts/run_spotnana_webhook_prod.py <json_file_path>")
        print("Example: python scripts/run_spotnana_webhook_prod.py in_trip/Flight_booking_other_update_sample.json")
        sys.exit(1)
    
    json_file_path = sys.argv[1]
    
    # Check if file exists
    if not os.path.exists(json_file_path):
        print(f"Error: File '{json_file_path}' not found")
        sys.exit(1)
    
    # Set environment to production
    os.environ["OTTO_ENV"] = "LIVE"
    
    logger.info(f"[SCRIPT] Starting webhook processing for file: {json_file_path}")
    logger.info(f"[SCRIPT] Environment set to: {os.environ.get('OTTO_ENV', 'not set')}")
    
    try:
        # Read JSON file
        with open(json_file_path, "r") as file:
            json_content = file.read()
        
        # Parse JSON
        event_data = json.loads(json_content)
        
        # Remove MongoDB ObjectId if present (it's not part of the actual webhook data)
        if "_id" in event_data:
            del event_data["_id"]
        
        # Create SpotnanaTravelDelivery object
        data = SpotnanaTravelDelivery(**event_data)
        
        # Log the trip ID we're processing
        trip_id = data.payload.get("tripId", "unknown")
        logger.info(f"[SCRIPT] Processing webhook event for trip_id: {trip_id}")
        logger.info(f"[SCRIPT] Event type: {data.event_type}, Operation: {data.operation}")
        
        # Process the webhook event
        await handle_spotnana_webhook_event(data, dummy_route_to_staging)
        
        logger.info(f"[SCRIPT] Successfully processed webhook event for trip_id: {trip_id}")
        
    except json.JSONDecodeError as e:
        logger.error(f"[SCRIPT] Failed to parse JSON file: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"[SCRIPT] Error processing webhook event: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
