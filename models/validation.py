"""
Validation utilities for DTO models.

Provides common validation patterns, custom validators, and data sanitization
utilities used across all DTO layers.
"""

import re
from datetime import date, datetime
from decimal import Decimal, InvalidOperation
from typing import Any

import structlog
from pydantic import Field

logger = structlog.get_logger(__name__)


class ValidationError(Exception):
    """Custom validation error for DTO models."""

    pass


class DataSanitizer:
    """
    Utility class for sanitizing and normalizing data from external APIs.
    """

    @staticmethod
    def normalize_currency_amount(value: Any) -> float | None:
        """
        Normalize currency amount from various formats to float.

        Args:
            value: Currency value in various formats (string, int, float, Decimal)

        Returns:
            Normalized float value or None if invalid
        """
        if value is None or value == "":
            return None

        try:
            # Handle string representations
            if isinstance(value, str):
                # Remove currency symbols and whitespace
                cleaned = re.sub(r"[^\d.-]", "", value.strip())
                if not cleaned:
                    return None
                value = cleaned

            # Convert to Decimal for precision, then to float
            decimal_value = Decimal(str(value))
            return float(decimal_value)

        except (ValueError, InvalidOperation, TypeError):
            logger.warning("Failed to normalize currency amount", value=value, value_type=type(value).__name__)
            return None

    @staticmethod
    def normalize_airport_code(code: Any) -> str | None:
        """
        Normalize airport code to standard 3-letter IATA format.

        Args:
            code: Airport code in various formats

        Returns:
            Normalized 3-letter uppercase code or None if invalid
        """
        if not code:
            return None

        try:
            normalized = str(code).strip().upper()
            # Remove common prefixes/suffixes
            normalized = re.sub(r"^(AIRPORT_|AP_)", "", normalized)
            normalized = re.sub(r"(_AIRPORT|_AP)$", "", normalized)

            # Validate 3-letter IATA code format
            if len(normalized) == 3 and normalized.isalpha():
                return normalized

            logger.warning("Invalid airport code format", code=code, normalized=normalized)
            return None

        except Exception as e:
            logger.warning("Failed to normalize airport code", code=code, error=str(e))
            return None

    @staticmethod
    def normalize_flight_number(flight_number: Any) -> str | None:
        """
        Normalize flight number to standard format.

        Args:
            flight_number: Flight number in various formats

        Returns:
            Normalized flight number or None if invalid
        """
        if not flight_number:
            return None

        try:
            # Convert to string and remove whitespace
            normalized = str(flight_number).strip()

            # Remove common prefixes and normalize format
            # Example: "UA 1234" -> "UA1234", "Flight UA1234" -> "UA1234"
            normalized = re.sub(r"^(FLIGHT\s+)", "", normalized, flags=re.IGNORECASE)
            normalized = re.sub(r"\s+", "", normalized)

            # Validate basic format (2-3 letter airline code + numbers)
            if re.match(r"^[A-Z]{2,3}\d+$", normalized.upper()):
                return normalized.upper()

            logger.warning("Invalid flight number format", flight_number=flight_number)
            return None

        except Exception as e:
            logger.warning("Failed to normalize flight number", flight_number=flight_number, error=str(e))
            return None

    @staticmethod
    def normalize_datetime_string(dt_string: Any, format_hint: str | None = None) -> str | None:
        """
        Normalize datetime string to ISO format.

        Args:
            dt_string: Datetime string in various formats
            format_hint: Optional format hint for parsing

        Returns:
            ISO format datetime string or None if invalid
        """
        if not dt_string:
            return None

        try:
            dt_str = str(dt_string).strip()

            # Common datetime formats from APIs
            formats = [
                "%Y-%m-%dT%H:%M:%S.%fZ",  # ISO with microseconds
                "%Y-%m-%dT%H:%M:%SZ",  # ISO without microseconds
                "%Y-%m-%dT%H:%M:%S",  # ISO without timezone
                "%Y-%m-%d %H:%M:%S",  # Space separated
                "%Y-%m-%d",  # Date only
            ]

            if format_hint:
                formats.insert(0, format_hint)

            for fmt in formats:
                try:
                    dt = datetime.strptime(dt_str, fmt)
                    return dt.isoformat() + "Z" if not dt_str.endswith("Z") else dt.isoformat()
                except ValueError:
                    continue

            logger.warning("Failed to parse datetime string", dt_string=dt_string)
            return None

        except Exception as e:
            logger.warning("Failed to normalize datetime", dt_string=dt_string, error=str(e))
            return None

    @staticmethod
    def normalize_phone_number(phone: Any) -> str | None:
        """
        Normalize phone number to E.164 format when possible.

        Args:
            phone: Phone number in various formats

        Returns:
            Normalized phone number or original if normalization fails
        """
        if not phone:
            return None

        try:
            # Remove all non-digit characters except +
            normalized = re.sub(r"[^\d+]", "", str(phone).strip())

            # Ensure it starts with + for international format
            if normalized and not normalized.startswith("+"):
                # Assume US number if 10 digits
                if len(normalized) == 10:
                    normalized = "+1" + normalized
                elif len(normalized) == 11 and normalized.startswith("1"):
                    normalized = "+" + normalized
                else:
                    normalized = "+" + normalized

            return normalized if len(normalized) >= 8 else None

        except Exception as e:
            logger.warning("Failed to normalize phone number", phone=phone, error=str(e))
            return str(phone) if phone else None


class CommonValidators:
    """
    Collection of common Pydantic validators for DTO models.
    """

    @classmethod
    def empty_str_to_none(cls, v):
        """Convert empty strings to None."""
        if isinstance(v, str) and v.strip() == "":
            return None
        return v

    @classmethod
    def normalize_string(cls, v):
        """Normalize string values by stripping whitespace."""
        if isinstance(v, str):
            return v.strip()
        return v

    @classmethod
    def validate_currency_amount(cls, v):
        """Validate and normalize currency amounts."""
        return DataSanitizer.normalize_currency_amount(v)

    @classmethod
    def validate_airport_code(cls, v):
        """Validate and normalize airport codes."""
        return DataSanitizer.normalize_airport_code(v)

    @classmethod
    def validate_flight_number(cls, v):
        """Validate and normalize flight numbers."""
        return DataSanitizer.normalize_flight_number(v)

    @classmethod
    def validate_positive_number(cls, v):
        """Validate that number is positive."""
        if v is not None and v <= 0:
            raise ValueError("Value must be positive")
        return v

    @classmethod
    def validate_email(cls, v):
        """Basic email validation."""
        if v and not re.match(r"^[^@]+@[^@]+\.[^@]+$", v):
            raise ValueError("Invalid email format")
        return v


class BusinessRuleValidators:
    """
    Business logic validators for domain models.
    """

    @staticmethod
    def validate_flight_timing(departure: datetime, arrival: datetime) -> bool:
        """
        Validate that flight timing is logical.

        Args:
            departure: Departure datetime
            arrival: Arrival datetime

        Returns:
            True if valid, raises ValidationError if not
        """
        if departure >= arrival:
            raise ValidationError(f"Invalid flight timing: departure {departure} must be before arrival {arrival}")

        # Check for unrealistic flight duration (more than 20 hours)
        duration_hours = (arrival - departure).total_seconds() / 3600
        if duration_hours > 20:
            logger.warning(
                "Unusually long flight duration detected",
                departure=departure,
                arrival=arrival,
                duration_hours=duration_hours,
            )

        return True

    @staticmethod
    def validate_price_consistency(base_fare: float, taxes: float, total: float, tolerance: float = 0.01) -> bool:
        """
        Validate that pricing components are consistent.

        Args:
            base_fare: Base fare amount
            taxes: Tax amount
            total: Total price
            tolerance: Allowed difference tolerance

        Returns:
            True if consistent, raises ValidationError if not
        """
        calculated_total = base_fare + taxes
        difference = abs(calculated_total - total)

        if difference > tolerance:
            raise ValidationError(
                f"Price inconsistency: base({base_fare}) + taxes({taxes}) = {calculated_total}, "
                f"but total is {total} (difference: {difference})"
            )

        return True

    @staticmethod
    def validate_passenger_count(adults: int, children: int = 0, infants: int = 0) -> bool:
        """
        Validate passenger count constraints.

        Args:
            adults: Number of adult passengers
            children: Number of child passengers
            infants: Number of infant passengers

        Returns:
            True if valid, raises ValidationError if not
        """
        if adults < 1:
            raise ValidationError("At least one adult passenger is required")

        if infants > adults:
            raise ValidationError("Number of infants cannot exceed number of adults")

        total_passengers = adults + children + infants
        if total_passengers > 9:
            raise ValidationError("Maximum 9 passengers allowed per booking")

        return True

    @staticmethod
    def validate_date_range(start_date: date, end_date: date | None = None) -> bool:
        """
        Validate travel date range.

        Args:
            start_date: Travel start date
            end_date: Travel end date (optional for one-way)

        Returns:
            True if valid, raises ValidationError if not
        """
        today = date.today()

        # Check if start date is not in the past
        if start_date < today:
            raise ValidationError("Travel date cannot be in the past")

        # Check if start date is not too far in the future (2 years)
        max_future_date = date(today.year + 2, today.month, today.day)
        if start_date > max_future_date:
            raise ValidationError("Travel date cannot be more than 2 years in the future")

        # Validate return date if provided
        if end_date:
            if end_date < start_date:
                raise ValidationError("Return date must be after departure date")

            if end_date > max_future_date:
                raise ValidationError("Return date cannot be more than 2 years in the future")

        return True


# Pre-configured field validators for common use cases
CurrencyField = Field(..., description="Currency amount in USD", ge=0, json_schema_extra={"example": 299.99})

AirportCodeField = Field(
    ...,
    description="3-letter IATA airport code",
    min_length=3,
    max_length=3,
    pattern=r"^[A-Z]{3}$",
    json_schema_extra={"example": "LAX"},
)

FlightNumberField = Field(
    ...,
    description="Flight number in format AA1234",
    pattern=r"^[A-Z]{2,3}\d+$",
    json_schema_extra={"example": "UA1234"},
)

EmailField = Field(
    ...,
    description="Valid email address",
    pattern=r"^[^@]+@[^@]+\.[^@]+$",
    json_schema_extra={"example": "<EMAIL>"},
)
