# Code generated from OpenAPI spec. DO NOT EDIT.
# Source: UsersApi.yaml
# Generated by: datamodel-code-generator
# 
# This file is automatically generated from the Spotnana Users API OpenAPI specification.
# Any manual changes will be overwritten when the models are regenerated.
# 
# To regenerate: uv run python manage_spotnana_models.py generate

# generated by datamodel-codegen:
#   filename:  UsersApi.yaml
#   timestamp: 2025-07-15T00:32:34+00:00

from __future__ import annotations

from collections.abc import Sequence
from enum import Enum
from typing import Literal
from uuid import UUID

from pydantic import BaseModel, ConfigDict, EmailStr, Field, RootModel, conint, constr


class FlightType(Enum):
    UNKNOWN_FLIGHT_TYPE = 'UNKNOWN_FLIGHT_TYPE'
    DOMESTIC = 'DOMESTIC'
    INTERNATIONAL = 'INTERNATIONAL'
    ALL = 'ALL'


class AirlinePref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlines: Sequence[str] | None = None
    flightType: FlightType | None = Field(None, examples=['DOMESTIC'])


class Alliance(Enum):
    UNKNOWN_ALLIANCE = 'UNKNOWN_ALLIANCE'
    STAR_ALLIANCE = 'STAR_ALLIANCE'
    ONEWORLD = 'ONEWORLD'
    SKYTEAM = 'SKYTEAM'
    VANILLA_ALLIANCE = 'VANILLA_ALLIANCE'
    U_FLY_ALLIANCE = 'U_FLY_ALLIANCE'
    VALUE_ALLIANCE = 'VALUE_ALLIANCE'


class AlliancePref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    alliances: Sequence[Alliance]


class CarType(Enum):
    OTHER = 'OTHER'
    MINI = 'MINI'
    ECONOMY = 'ECONOMY'
    COMPACT = 'COMPACT'
    MID_SIZE = 'MID_SIZE'
    STANDARD = 'STANDARD'
    FULL_SIZE = 'FULL_SIZE'
    PREMIUM = 'PREMIUM'
    LUXURY = 'LUXURY'
    CONVERTIBLE = 'CONVERTIBLE'
    MINIVAN = 'MINIVAN'
    SUV = 'SUV'
    VAN = 'VAN'
    PICKUP = 'PICKUP'
    SPORTS = 'SPORTS'
    SPECIAL = 'SPECIAL'
    RECREATIONAL_VEHICLE = 'RECREATIONAL_VEHICLE'
    WAGON = 'WAGON'


class CarVendor(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    code: str = Field(..., description='Vendor code', examples=['ZE'])
    name: str = Field(..., description='Vendor name', examples=['HERTZ'])
    isPresentInPreferredVendors: bool | None = Field(
        None,
        description='Whether the car vendor is present in preferred vendor list. This is an optional field which gets populated only in the preferred vendor autocomplete API.',
        examples=[True],
    )


class CoachPref(Enum):
    SMOKING = 'SMOKING'
    NON_SMOKING = 'NON_SMOKING'
    PETS_ALLOWED = 'PETS_ALLOWED'
    RESTAURANT = 'RESTAURANT'
    QUIET = 'QUIET'


class CompanyId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID = Field(..., examples=['f49d00fe-1eda-4304-ba79-a980f565281d'])


class CompanySpecifiedAttribute(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fixedColumnName: str = Field(..., examples=['contingentType'])
    value: str = Field(..., examples=['FSTV'])


class ConditionalRate(Enum):
    MILITARY = 'MILITARY'
    AAA = 'AAA'
    GOVERNMENT = 'GOVERNMENT'


class DateModel(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$') = (
        Field(..., examples=['2017-07-21'])
    )


class DateTimeLocal(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(
        pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$'
    ) = Field(..., examples=['2017-07-21T17:32'])


class DeckLevel(Enum):
    UPPER_DECK = 'UPPER_DECK'
    LOWER_DECK = 'LOWER_DECK'


class Dimensions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    height: int | None = Field(None, examples=[120])
    width: int | None = Field(None, examples=[240])


class Duration(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: str | None = Field(
        None,
        description='Durations define the amount of intervening time in a time interval and are represented by the\nformat P[n]Y[n]M[n]DT[n]H[n]M[n]S.\nThe [n] is replaced by the value for each of the date and time elements that follow the [n].\nLeading zeros are not required. The capital letters P, Y, M, W, D, T, H, M, and S are\ndesignators for each of the date and time elements and are not replaced. P is the duration\ndesignator (for period) placed at the start of the duration representation.\nY is the year designator.\nM is the month designator.\nW is the week designator.\nD is the day designator.\nT is the time designator.\nH is the hour designator.\nM is the minute designator.\nS is the second designator and can include decimal digits with arbitrary precision.\n',
        examples=['PT19H55M'],
    )


class Relation(Enum):
    RELATION_UNKNOWN = 'RELATION_UNKNOWN'
    SPOUSE = 'SPOUSE'
    PARENT = 'PARENT'
    SIBLING = 'SIBLING'
    CHILD = 'CHILD'
    FRIEND = 'FRIEND'
    RELATIVE = 'RELATIVE'
    COLLEAGUE = 'COLLEAGUE'
    OTHER = 'OTHER'


class EngineType(Enum):
    UNKNOWN_ENGINE = 'UNKNOWN_ENGINE'
    PETROL = 'PETROL'
    DIESEL = 'DIESEL'
    ELECTRIC = 'ELECTRIC'
    CNG = 'CNG'
    HYBRID = 'HYBRID'
    HYDROGEN = 'HYDROGEN'
    MULTI_FUEL = 'MULTI_FUEL'
    ETHANOL = 'ETHANOL'


class EntityId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID


class ErrorParameter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str | None = Field(None, description='Parameter name')
    value: str | None = Field(None, description='Parameter value')


class ErrorMessage(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    errorCode: str | None = Field(
        None, description='Error code to identify the specific errors.'
    )
    message: str | None = Field(
        None, description='Message containing details of error.'
    )
    errorParameters: Sequence[ErrorParameter] | None = Field(
        None, description='Error message parameters.'
    )
    errorDetail: str | None = Field(None, description='More details about the error.')


class ErrorResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    debugIdentifier: str | None = Field(
        None, description='Link to debug the error internally.'
    )
    errorMessages: Sequence[ErrorMessage] | None = None


class ExternalIdObject(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    externalId: str = Field(
        ...,
        description='A partner-assigned user identifier. This value must be unique for all travelers within a PNR.',
        examples=['user-1'],
    )


class FareType(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    CHANGEABLE = 'CHANGEABLE'
    REFUNDABLE = 'REFUNDABLE'


class FarePref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fareTypes: Sequence[FareType]


class Gender(Enum):
    MALE = 'MALE'
    FEMALE = 'FEMALE'
    UNSPECIFIED = 'UNSPECIFIED'
    UNDISCLOSED = 'UNDISCLOSED'


class HotelBrand(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    brandCode: str | None = Field(
        None, description='The code of hotel brand.', examples=['HY']
    )
    brandName: str | None = Field(
        None, description='The name of hotel brand.', examples=['Global Hytt Corp.']
    )


class HotelChain(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    chainCode: str | None = Field(
        None, description='The code of hotel chain.', examples=['EM']
    )
    chainName: str | None = Field(
        None, description='The name of hotel chain.', examples=['Mariott']
    )


class HotelPrefAmenity(Enum):
    PARKING = 'PARKING'
    FREE_PARKING = 'FREE_PARKING'
    FREE_BREAKFAST = 'FREE_BREAKFAST'
    POOL = 'POOL'
    WIFI = 'WIFI'
    FITNESS_CENTER = 'FITNESS_CENTER'
    FAMILY_FRIENDLY = 'FAMILY_FRIENDLY'
    RECEPTION = 'RECEPTION'
    SPA = 'SPA'
    RESTAURANT = 'RESTAURANT'
    BAR = 'BAR'
    TRANSPORTATION = 'TRANSPORTATION'
    PET_FRIENDLY = 'PET_FRIENDLY'
    BUSINESS_CENTER = 'BUSINESS_CENTER'
    AIR_CONDITIONING = 'AIR_CONDITIONING'
    BEACH_ACCESS = 'BEACH_ACCESS'
    LAUNDRY_SERVICES = 'LAUNDRY_SERVICES'
    ROOM_SERVICE = 'ROOM_SERVICE'
    ACCESSIBLE = 'ACCESSIBLE'


class Image(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    data: str | None = Field(None, examples=['6935813e12584abda0e43d71cd2ea260'])
    dimensions: Dimensions | None = None
    url: str | None = Field(
        None,
        examples=[
            'https://static.wixstatic.com/media/73f2e2_6935813e12584abda0e43d71cd2ea260~mv2.png/v1/fill/w_630,h_94,al_c,q_85,usm_0.66_1.00_0.01/Spotnana%403x.webp'
        ],
    )


class Type(Enum):
    UNKNOWN = 'UNKNOWN'
    VISA = 'VISA'


class ImmigrationDocument(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    authorizedStayDuration: Duration | None = Field(
        None, description='Duration of the stay authorized by the immigration document.'
    )
    docId: str = Field(
        ...,
        description='The ID of the immigration document.',
        examples=['ImmigrationDocumentID'],
    )
    expiryDate: DateModel = Field(
        ..., description='The date on which the immigration document expires.'
    )
    issueCountry: str = Field(
        ...,
        description='The country that issued the immigration document.',
        examples=['IN'],
    )
    issuedDate: DateModel | None = Field(
        None, description='The date on which the immigration document was issued.'
    )
    nationalityCountry: str | None = Field(None, examples=['IN'])
    reentryRequirementDuration: Duration | None = None
    type: Type | None = Field(None, examples=['VISA'])


class ImmigrationDocumentWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    immigrationDoc: ImmigrationDocument | None = None


class KnownTravelerNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    number: str = Field(..., examples=['12345'])
    issueCountry: str = Field(..., examples=['US'])


class KnownTravelerNumberWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ktn: KnownTravelerNumber | None = None


class Latlng(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    latitude: float = Field(
        ..., description='Latitude of the Location', examples=[77.1025]
    )
    longitude: float = Field(
        ..., description='Longitude of the Location', examples=[28.7041]
    )


class ListUsersV3CompanyId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyId: EntityId = Field(..., description='Company ID')


class UserStatusFilter(Enum):
    ACTIVE = 'ACTIVE'
    INACTIVE = 'INACTIVE'
    ALL = 'ALL'


class ListUsersV3TmcId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tmcId: EntityId = Field(..., description='TMC ID')


class Type1(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    CAR = 'CAR'
    RAIL = 'RAIL'


class LoyaltyInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    appliedTo: Sequence[str] | None = None
    id: str = Field(..., examples=['firstId'])
    issuedBy: str = Field(..., examples=['firstIssuedBy'])
    type: Type1 = Field(..., examples=['AIR'])


class MealType(Enum):
    UNKNOWN_MEAL = 'UNKNOWN_MEAL'
    AVML = 'AVML'
    BBML = 'BBML'
    BLML = 'BLML'
    CHML = 'CHML'
    DBML = 'DBML'
    FPML = 'FPML'
    GFML = 'GFML'
    HFML = 'HFML'
    HNML = 'HNML'
    KSML = 'KSML'
    LCML = 'LCML'
    LFML = 'LFML'
    LPML = 'LPML'
    LSML = 'LSML'
    MOML = 'MOML'
    NLML = 'NLML'
    NSML = 'NSML'
    ORML = 'ORML'
    PFML = 'PFML'
    RVML = 'RVML'
    SFML = 'SFML'
    SPML = 'SPML'
    VGML = 'VGML'
    VJML = 'VJML'
    VLML = 'VLML'
    VOML = 'VOML'


class MembershipInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    membershipInfos: Sequence[LoyaltyInfo] | None = None
    isLoyaltyBlocked: bool | None = Field(
        None,
        description='Whether user is blocked to use loyalty programs. This is a read only field.',
        examples=[False],
    )


class NameSuffix(Enum):
    NAME_SUFFIX_UNKNOWN = 'NAME_SUFFIX_UNKNOWN'
    SR = 'SR'
    JR = 'JR'
    MD = 'MD'
    PHD = 'PHD'
    II = 'II'
    III = 'III'
    IV = 'IV'
    DO = 'DO'
    ATTY = 'ATTY'
    V = 'V'
    VI = 'VI'
    ESQ = 'ESQ'
    DC = 'DC'
    DDS = 'DDS'
    VM = 'VM'
    JD = 'JD'
    SECOND = 'SECOND'
    THIRD = 'THIRD'


class Type2(Enum):
    DNI = 'DNI'
    NIE = 'NIE'


class NationalDoc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    docId: str = Field(
        ...,
        description='Unique id identifying the national document.',
        examples=['NationalDocId'],
    )
    issueCountry: str = Field(
        ...,
        description='IS0 2 letter country code of the country issuing this id.',
        examples=['IN'],
    )
    issuedDate: DateModel | None = None
    expiryDate: DateModel | None = None
    type: Type2 | None = Field(None, examples=['DNI'])


class NationalDocWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    nationalDoc: NationalDoc | None = None


class NotificationEmailPreference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    enabled: bool | None = Field(
        True, description='Whether the email preference is enabled or not.'
    )
    ccEmails: Sequence[EmailStr] | None = Field(
        None, description='List of cc emails for the email preference.'
    )


class NotificationType(Enum):
    FLIGHT_NOTIFICATION = 'FLIGHT_NOTIFICATION'
    BOOKING_NOTIFICATION = 'BOOKING_NOTIFICATION'


class NumStopsPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numOfStops: int = Field(..., examples=[34])


class OffsetBasedPaginationRequestParams(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    offset: conint(ge=0) | None = Field(
        0,
        description='The starting index in the list from which results are returned. The value must be greater than or equal to 0.',
    )
    limit: conint(ge=1) | None = Field(
        100, description='Maximum number of results to be fetched.'
    )


class OffsetBasedPaginationResponseParams(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalNumResults: int = Field(..., description='Total number of results.')


class OrganizationAgencyId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str


class OrganizationId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str


class Type3(Enum):
    UNKNOWN = 'UNKNOWN'
    REGULAR = 'REGULAR'


class Passport(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    docId: str = Field(..., examples=['PassportID'])
    expiryDate: DateModel
    issueCountry: str = Field(..., examples=['IN'])
    issuedDate: DateModel | None = None
    nationalityCountry: str = Field(..., examples=['IN'])
    type: Type3 | None = Field(None, examples=['REGULAR'])


class PassportWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    passport: Passport | None = None


class Persona(Enum):
    UNKNOWN_PERSONA = 'UNKNOWN_PERSONA'
    EMPLOYEE = 'EMPLOYEE'
    GUEST = 'GUEST'
    PERSONAL = 'PERSONAL'
    RELATIVE = 'RELATIVE'
    ADHOC = 'ADHOC'


class CountryCodeSource(Enum):
    UNSPECIFIED = 'UNSPECIFIED'
    FROM_NUMBER_WITH_PLUS_SIGN = 'FROM_NUMBER_WITH_PLUS_SIGN'
    FROM_NUMBER_WITH_IDD = 'FROM_NUMBER_WITH_IDD'
    FROM_NUMBER_WITHOUT_PLUS_SIGN = 'FROM_NUMBER_WITHOUT_PLUS_SIGN'
    FROM_DEFAULT_COUNTRY = 'FROM_DEFAULT_COUNTRY'


class Type4(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    MOBILE = 'MOBILE'
    LANDLINE = 'LANDLINE'


class PhoneNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    countryCode: int | None = Field(
        None, description='two digit country code', examples=[91]
    )
    countryCodeSource: CountryCodeSource | None = Field(
        None, examples=['FROM_NUMBER_WITH_PLUS_SIGN']
    )
    extension: str | None = Field(
        None, description='phone number extension', examples=['222']
    )
    isoCountryCode: str | None = Field(
        None, description='ISO alpha-2 code', examples=['IN']
    )
    italianLeadingZero: bool | None = Field(False, examples=[True])
    nationalNumber: int | None = Field(None, examples=[8150])
    numberOfLeadingZeros: int | None = Field(0, examples=[1])
    preferredDomesticCarrierCode: str | None = Field(None, examples=['7'])
    rawInput: str | None = Field(None, examples=['77777'])
    type: Type4 | None = Field(None, examples=['MOBILE'])


class PolicyBasicInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(
        ...,
        description='Id for policy.',
        examples=['348fcc1c-c33a-4942-8834-a7888913dd66'],
    )
    name: str = Field(..., description='Name of the policy', examples=['HR Policy'])


class PostalAddress(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    addressLines: Sequence[str] = Field(..., description='Address lines')
    administrativeArea: str | None = Field(
        None,
        description='Code of administrative area. For example: DL for Delhi, India.\nHighest administrative subdivision which is used for postal\naddresses of a country or region.\nFor example, this can be a state, a province, an oblast, or a prefecture.\nSpecifically, for Spain this is the province and not the autonomous\ncommunity (e.g. "Barcelona" and not "Catalonia").\nMany countries don\'t use an administrative area in postal addresses. E.g.\nin Switzerland this should be left unpopulated.\n',
        examples=['CA'],
    )
    administrativeAreaName: str | None = Field(
        None,
        description='Name of administrative area. This is full name corresponding to administrativeArea. \nLike Delhi for DL area code. For some places, code and name maybe same as well like Tokyo.\n',
        examples=['California'],
    )
    description: str | None = Field(
        None, description='Address description', examples=['San Francisco Home']
    )
    isDefault: bool | None = Field(
        None,
        description='Whether this address is default address in case multiple addresses are specified.',
        examples=[True],
    )
    languageCode: str | None = Field(
        None,
        description='BCP-47 language code of the contents of this address (if known). This is often the UI \nlanguage of the input form or is expected to match one of the languages used in the \naddress\' country/region, or their transliterated equivalents.\nThis can affect formatting in certain countries, but is not critical to the correctness \nof the data and will never affect any validation or other non-formatting related operations.\nExamples: "zh-Hant", "ja", "ja-Latn", "en".\n',
        examples=['en'],
    )
    locality: str | None = Field(
        None,
        description='Generally refers to the city/town portion of the address.',
        examples=['San Francisco'],
    )
    locationCode: str | None = Field(
        None,
        description='IATA 3-letter location code. See https://www.iata.org/en/services/codes.',
        examples=['LAX'],
    )
    organization: str | None = Field(
        None,
        description='The name of the organization at the address.',
        examples=['Spotnana'],
    )
    postalCode: str | None = Field(
        None,
        description='Postal code of the address. This is a required field when setting for a user/legal entity/company etc.',
        examples=['94130'],
    )
    continentCode: str | None = Field(
        None,
        description='2 letter continent code of the continent this address falls in.',
        examples=['AF'],
    )
    recipients: Sequence[str] | None = Field(
        None, description='The recipient at the address.'
    )
    regionCode: str = Field(
        ...,
        description='Region code of the country/region of the address.',
        examples=['US'],
    )
    regionName: str | None = Field(
        None,
        description='Region name of the country/region of the address.',
        examples=['America'],
    )
    revision: int | None = Field(None, examples=[1])
    sortingCode: str | None = Field(
        None,
        description='Additional, country-specific, sorting code. This is not used\nin most regions. Where it is used, the value is either a string like\n"CEDEX", optionally followed by a number (e.g. "CEDEX 7"), or just a number\nalone, representing the "sector code" (Jamaica), "delivery area indicator"\n(Malawi) or "post office indicator" (e.g. Côte d\'Ivoire).\n',
        examples=['Jamaica'],
    )
    sublocality: str | None = Field(
        None,
        description='Sublocality of the address. This can be neighborhoods, boroughs, districts.',
    )
    timezone: str | None = Field(
        None, description='Time zone of the address.', examples=['America/Los_Angeles']
    )
    coordinates: Latlng | None = Field(
        None, description='Map coordinates of the address.'
    )


class PreferredLocationLabel(Enum):
    HOME = 'HOME'
    WORK = 'WORK'
    OTHER = 'OTHER'


class PreferredPronoun(Enum):
    SHE_HER_HERS = 'SHE_HER_HERS'
    HE_HIM_HIS = 'HE_HIM_HIS'
    THEY_THEM_THEIRS = 'THEY_THEM_THEIRS'


class PreferredRailStation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    stationName: str | None = Field(
        None, description='Rail station name.', examples=['Chicago Union Station']
    )
    stationCode: str = Field(..., description='Rail station code.', examples=['CHI'])
    cityName: str | None = Field(
        None,
        description='Name of city where the rail station is located.',
        examples=['Chicago'],
    )
    countryCode: str | None = Field(
        None, description='Alpha-2 country code where the rail station is located.'
    )
    label: PreferredLocationLabel


class PrimaryServiceProviderTmc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tmcId: CompanyId = Field(..., description='Id of the service provider TMC.')


class RailCard(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cardNumber: str | None = Field(None, description='Number of card')
    expiryDate: DateModel | None = Field(
        None, description='Expiry date of the Rail Card.'
    )
    name: str = Field(
        ..., description='Name of the Rail Card.', examples=['Veterans Railcard']
    )
    spotnanaCode: str = Field(
        ...,
        description='Unique Spotnana code/identifier for Rail Card.',
        examples=['VET'],
    )
    vendor: str = Field(..., description='Vendor Name.', examples=['ATOC'])


class RailTravelClass(Enum):
    FIRST = 'FIRST'
    STANDARD = 'STANDARD'
    BUSINESS = 'BUSINESS'
    SLEEPER = 'SLEEPER'
    STANDARD_PREMIUM = 'STANDARD_PREMIUM'
    BUSINESS_PREMIUM = 'BUSINESS_PREMIUM'
    COACH = 'COACH'
    ROOM = 'ROOM'
    EXECUTIVE = 'EXECUTIVE'


class RedressNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    number: str = Field(..., examples=['12345'])
    issueCountry: str = Field(..., examples=['US'])


class RedressNumberWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    redress: RedressNumber | None = None


class Reference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID
    name: str | None = None


class Relation1(Enum):
    SPOUSE = 'SPOUSE'
    CHILD = 'CHILD'
    FRIEND = 'FRIEND'
    PARENT = 'PARENT'
    OTHER = 'OTHER'


class RelativeOf(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userRef: Reference | None = None
    relation: Relation1 | None = None


class RoleType(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    COMPANY_ADMIN = 'COMPANY_ADMIN'
    COMPANY_TRAVEL_ARRANGER = 'COMPANY_TRAVEL_ARRANGER'
    TRAVEL_ARRANGER = 'TRAVEL_ARRANGER'
    COMPANY_REPORT_ADMIN = 'COMPANY_REPORT_ADMIN'
    GLOBAL_ADMIN = 'GLOBAL_ADMIN'
    GLOBAL_AGENT = 'GLOBAL_AGENT'
    TMC_AGENT = 'TMC_AGENT'
    TMC_ADMIN = 'TMC_ADMIN'


class RoleTypeEnum(Enum):
    COMPANY_ADMIN = 'COMPANY_ADMIN'
    COMPANY_TRAVEL_ARRANGER = 'COMPANY_TRAVEL_ARRANGER'
    TRAVEL_ARRANGER = 'TRAVEL_ARRANGER'
    COMPANY_REPORT_ADMIN = 'COMPANY_REPORT_ADMIN'
    TMC_ADMIN = 'TMC_ADMIN'
    TMC_AGENT = 'TMC_AGENT'
    GLOBAL_ADMIN = 'GLOBAL_ADMIN'
    GLOBAL_AGENT = 'GLOBAL_AGENT'


class BedCount(Enum):
    ONE_BED = 'ONE_BED'
    TWO_BEDS = 'TWO_BEDS'


class RoomType(Enum):
    SMOKING = 'SMOKING'
    NON_SMOKING = 'NON_SMOKING'


class MostImportantFact(Enum):
    ROOM_TYPE = 'ROOM_TYPE'
    BED_COUNT = 'BED_COUNT'
    ROOM_LOCATION = 'ROOM_LOCATION'


class RoomLocation(Enum):
    HIGH_FLOOR = 'HIGH_FLOOR'
    LOW_FLOOR = 'LOW_FLOOR'
    NEAR_ELEVATOR = 'NEAR_ELEVATOR'


class PillowType(Enum):
    FOAM = 'FOAM'
    EXTRA_FOAM = 'EXTRA_FOAM'
    EXTRA_FEATHER = 'EXTRA_FEATHER'


class RoomAmenityPref(Enum):
    FEATHER_FREE_ROOM = 'FEATHER_FREE_ROOM'
    EXTRA_TOWELS = 'EXTRA_TOWELS'
    REFRIGERATOR = 'REFRIGERATOR'


class RoomPreference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    isMobilityAccessible: bool | None = Field(
        False,
        description='Whether or not mobility accessible room, tub.',
        examples=[False],
    )
    bedCount: BedCount | None = Field(
        None, description='The number of bed in the room.', examples=['ONE_BED']
    )
    roomType: RoomType | None = Field(
        None, description='Single selection of type of room.', examples=['SMOKING']
    )
    mostImportantFact: MostImportantFact | None = Field(
        None,
        description='Single selection of the most import fact.',
        examples=['BED_COUNT'],
    )
    roomLocation: RoomLocation | None = Field(
        None, description='Location of the hotel room', examples=['HIGH_FLOOR']
    )
    pillowType: PillowType | None = Field(
        None, description='The type of pillow in hotel room.', examples=['FOAM']
    )
    roomAmenityPrefs: Sequence[RoomAmenityPref] | None = None


class SeatAmenityType(Enum):
    UNKNOWN_AIR_SEAT_AMENITY_TYPE = 'UNKNOWN_AIR_SEAT_AMENITY_TYPE'
    FLAT_BED = 'FLAT_BED'
    WIFI = 'WIFI'
    IN_SEAT_POWER = 'IN_SEAT_POWER'


class SeatAmenityPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    seatAmenityTypes: Sequence[SeatAmenityType]


class Cabin(Enum):
    UNKNOWN_CABIN = 'UNKNOWN_CABIN'
    ECONOMY = 'ECONOMY'
    PREMIUM_ECONOMY = 'PREMIUM_ECONOMY'
    BUSINESS = 'BUSINESS'
    FIRST = 'FIRST'


class Position(Enum):
    UNKNOWN_POSITION = 'UNKNOWN_POSITION'
    AISLE = 'AISLE'
    WINDOW = 'WINDOW'
    AISLE_OR_WINDOW = 'AISLE_OR_WINDOW'


class SeatLocationPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cabins: Sequence[Cabin] | None = None
    isBulkHeadPref: bool | None = Field(None, examples=[False])
    maxFlightDurationInHours: int | None = Field(None, examples=[3])
    position: Position | None = Field(None, examples=['WINDOW'])


class SeatPrefDirection(Enum):
    FORWARD = 'FORWARD'
    BACKWARD = 'BACKWARD'


class SeatPrefLocation(Enum):
    AISLE = 'AISLE'
    WINDOW = 'WINDOW'
    SOLO = 'SOLO'


class SeatPrefType(Enum):
    SLEEPER_BED = 'SLEEPER_BED'
    NORMAL = 'NORMAL'
    TABLE_SEAT = 'TABLE_SEAT'


class SupplierType(Enum):
    SABRE = 'SABRE'
    AMADEUS = 'AMADEUS'
    TRAVEL_FUSION = 'TRAVEL_FUSION'
    FARELOGIX_NDC = 'FARELOGIX_NDC'
    ATPCO_NDC = 'ATPCO_NDC'
    TRAINLINE = 'TRAINLINE'
    AVIA = 'AVIA'
    QBR = 'QBR'
    BCD = 'BCD'
    QANTAS_HOTELS = 'QANTAS_HOTELS'
    SOUTHWEST = 'SOUTHWEST'
    EXPEDIA = 'EXPEDIA'
    HOTEL_HUB = 'HOTEL_HUB'
    NDC = 'NDC'
    MARRIOTT = 'MARRIOTT'
    CLEARTRIP = 'CLEARTRIP'
    KYTE = 'KYTE'
    GROUNDSPAN = 'GROUNDSPAN'
    SABRE_NDC = 'SABRE_NDC'
    BOOKING_COM = 'BOOKING_COM'


class Tier(Enum):
    BASIC = 'BASIC'
    SEAT1A = 'SEAT1A'


class TmcRoleInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tmcRef: Reference
    roleType: Literal['TMC_ADMIN', 'TMC_AGENT'] = Field(
        ..., description='Role type for which tmc reference metadata is specified.'
    )


class TransmissionSearchFilter(Enum):
    MANUAL = 'MANUAL'
    AUTOMATIC = 'AUTOMATIC'


class TravelType(Enum):
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    CAR = 'CAR'
    RAIL = 'RAIL'
    LIMO = 'LIMO'
    MISC = 'MISC'
    ALL = 'ALL'


class TravelerArrangerStatus(Enum):
    PENDING = 'PENDING'
    ACCEPTED = 'ACCEPTED'
    DENIED = 'DENIED'


class TravelerConfigForArranger(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UUID | None = None
    sendConfirmationEmail: bool | None = Field(
        False,
        description='Whether or not to send confirmation emails to user.',
        examples=[False],
    )
    sendFlightStatsNotificationEmail: bool | None = Field(
        False,
        description='Whether or not to send flight stat notifications to user.',
        examples=[False],
    )


class UserApplicablePolicies(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    policies: Sequence[PolicyBasicInfo] | None = None


class UserId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID


class UserListFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legalEntityIds: Sequence[EntityId] | None = Field(
        None, description='Filter by legal entity ids.'
    )
    officeIds: Sequence[EntityId] | None = Field(None, description='Filter by offices.')
    departmentIds: Sequence[EntityId] | None = Field(
        None, description='Filter by department IDs.'
    )
    costCenterIds: Sequence[EntityId] | None = Field(
        None, description='Filter by cost center IDs.'
    )
    userIds: Sequence[EntityId] | None = Field(None, description='Filter by user IDs.')
    emails: Sequence[str] | None = Field(None, description='Filter by user email IDs.')
    externalIds: Sequence[str] | None = Field(
        None, description='Filter by partner-assigned external identifiers for users.'
    )
    personas: Sequence[Persona] | None = Field(
        None, description='Filter by user personas.'
    )
    roles: Sequence[RoleType] | None = Field(None, description='Filter by user roles.')
    designations: Sequence[str] | None = Field(
        None, description='Filter by job titles or designations.'
    )
    tiers: Sequence[Tier] | None = Field(None, description='Filter by tiers.')


class SortOrder(Enum):
    ASCENDING = 'ASCENDING'
    DESCENDING = 'DESCENDING'


class UserSort(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    sortBy: Literal['NAME'] = Field(
        'NAME',
        description='The column on which the sorting should be applied.',
        examples=['NAME'],
    )
    sortOrder: SortOrder = Field(
        ...,
        description='Order of sorting ie ascending or descending.',
        examples=['DESCENDING'],
    )


class UserSummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID
    email: EmailStr | None = None
    externalId: str | None = Field(
        None, description='The partner-assigned user identifier.', examples=['user-1']
    )
    persona: Persona | None = None
    isActive: bool | None = Field(
        None, description='Whether the user is active or not.'
    )


class UserSummaryList(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    length: int | None = None
    elements: Sequence[UserSummary] | None = Field(
        None, description='List of references containing id and name.'
    )


class UserTitle(Enum):
    TITLE_UNKNOWN = 'TITLE_UNKNOWN'
    MR = 'MR'
    MS = 'MS'
    MRS = 'MRS'
    MX = 'MX'
    MASTER = 'MASTER'
    MISS = 'MISS'
    DR = 'DR'
    PROFESSOR = 'PROFESSOR'
    CAPTAIN = 'CAPTAIN'
    REVEREND = 'REVEREND'
    HONOURABLE = 'HONOURABLE'
    SIR = 'SIR'
    LADY = 'LADY'
    AMBASSADOR = 'AMBASSADOR'
    LORD = 'LORD'
    BRIGADIER = 'BRIGADIER'
    SENATOR = 'SENATOR'
    DAME = 'DAME'
    JUSTICE = 'JUSTICE'
    UK = 'UK'


class WorkerType(Enum):
    EMPLOYEE = 'EMPLOYEE'
    CONTINGENT = 'CONTINGENT'
    SEASONAL = 'SEASONAL'
    INTERN = 'INTERN'
    GUEST = 'GUEST'


class BusinessDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legalEntity: Reference = Field(
        ..., description='The legal entity associated with the user.'
    )
    office: Reference | None = Field(
        None, description='The office associated with the user.'
    )
    department: Reference | None = Field(
        None, description='The department associated with the user.'
    )
    costCenter: Reference | None = Field(
        None, description='The cost center associated with the user.'
    )
    businessEmail: str | None = Field(None, description='Business email of the user.')
    designation: str | None = Field(
        None, description='Designation or job title of the user.'
    )
    employeeId: str | None = Field(None, description='Employee ID of the user.')


class BusinessInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    departmentRef: Reference | None = None
    designation: str | None = Field(None, examples=['MANAGER'])
    email: EmailStr | None = Field(None, examples=['<EMAIL>'])
    employeeId: str | None = Field(
        None,
        description="Unique employee id. Can use email if a company don't use employee ids.",
        examples=['101'],
    )
    gradeRef: Reference | None = None
    legalEntityRef: Reference
    managerRef: Reference | None = None
    officeRef: Reference | None = None
    organizationRef: Reference
    phoneNumbers: Sequence[PhoneNumber] | None = None
    costCenterRef: Reference | None = None
    countryCode: str | None = Field(
        None, description='alpha-2 or alpha-3 ISO country code.', examples=['USA']
    )
    workerType: WorkerType | None = None
    accountingCode: str | None = Field(
        None, description='Code used for accounting.', examples=['123']
    )
    companySpecifiedAttributes: Sequence[CompanySpecifiedAttribute] | None = None
    designatedApproverRefs: Sequence[Reference] | None = Field(
        None, description='A list of references for designated approvers.'
    )
    authorizerEmail: str | None = Field(
        None,
        description='Email address to be used as approval authorizer, when a manager is not present.',
        examples=['<EMAIL>'],
    )


class CarPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendors: Sequence[CarVendor] | None = Field(
        None, description='A list of car vendors.'
    )
    carTypes: Sequence[CarType] | None = Field(
        None, description='A list of types of car.'
    )
    engineTypes: Sequence[EngineType] | None = Field(
        None, description='A list of types of engine.'
    )
    transmissionTypes: Sequence[TransmissionSearchFilter] | None = Field(
        None, description='A list of types of transmission.'
    )
    conditionalRates: Sequence[ConditionalRate] | None = Field(
        None, description='A list of conditional rates for rail.'
    )


class CompanyRef(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CompanyId
    name: str | None = None
    logo: Image | None = Field(None, description='Company logo')


class CompanyRoleInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyRef: Reference
    tmcRef: Reference | None = None
    roleType: Literal[
        'COMPANY_ADMIN', 'COMPANY_TRAVEL_ARRANGER', 'COMPANY_REPORT_ADMIN'
    ] = Field(
        ..., description='Role type for which company reference metadata is specified.'
    )


class EmergencyContact(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Full name of contact.', examples=['John Smith'])
    email: EmailStr | None = Field(
        None,
        description='Email address of contact.',
        examples=['<EMAIL>'],
    )
    designation: str | None = Field(
        None, description='Job title of contact.', examples=['MANAGER']
    )
    relation: Relation | None = Field(
        None, description='Relation of contact to user.', examples=['SPOUSE']
    )
    phoneNumbers: Sequence[PhoneNumber] = Field(
        ..., description='Phone numbers of contact.'
    )
    preferredLanguage: str | None = Field(
        None, description='Language preferred by user.', examples=['en-US']
    )


class HotelPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelParentChains: Sequence[HotelChain] | None = Field(
        None, description='A list of hotel parent chains.'
    )
    hotelBrands: Sequence[HotelBrand] | None = Field(
        None, description='A list of hotel brands.'
    )
    hotelAmenityTypes: Sequence[HotelPrefAmenity] | None = Field(
        None, description='A list of HotelAmenities.'
    )
    roomPreference: RoomPreference | None = None
    conditionalRates: Sequence[ConditionalRate] | None = Field(
        None, description='A list of conditional rates for rail.'
    )


class IdentityDocument(
    RootModel[
        PassportWrapper
        | ImmigrationDocumentWrapper
        | RedressNumberWrapper
        | KnownTravelerNumberWrapper
        | NationalDocWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        PassportWrapper
        | ImmigrationDocumentWrapper
        | RedressNumberWrapper
        | KnownTravelerNumberWrapper
        | NationalDocWrapper
    ) = Field(
        ...,
        description='Identity document details. Currently supported documents are passport, immigration document, \nknown traveler number, redress number and national document.\n',
        title='IdentityDocument',
    )


class ListUsersV3Request1(ListUsersV3TmcId):
    model_config = ConfigDict(
        frozen=True,
    )
    userStatusFilter: UserStatusFilter | None = Field(
        'ACTIVE',
        description='Retrieve active, inactive, or all users.',
        examples=['INACTIVE'],
    )
    pagination: OffsetBasedPaginationRequestParams
    filters: Sequence[UserListFilter] | None = Field(
        None,
        description='Filters to refine the list of users returned. Users matching with any of the filters would be returned.',
    )
    sort: UserSort | None = None


class ListUsersV3Request2(ListUsersV3CompanyId):
    model_config = ConfigDict(
        frozen=True,
    )
    userStatusFilter: UserStatusFilter | None = Field(
        'ACTIVE',
        description='Retrieve active, inactive, or all users.',
        examples=['INACTIVE'],
    )
    pagination: OffsetBasedPaginationRequestParams
    filters: Sequence[UserListFilter] | None = Field(
        None,
        description='Filters to refine the list of users returned. Users matching with any of the filters would be returned.',
    )
    sort: UserSort | None = None


class ListUsersV3Request(RootModel[ListUsersV3Request1 | ListUsersV3Request2]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: ListUsersV3Request1 | ListUsersV3Request2 = Field(
        ..., description='List users request', title='ListUsersV3Request'
    )


class MealPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    exclMealPrefs: Sequence[MealType] | None = None
    inclMealPrefs: Sequence[MealType] | None = None
    specialMealDescription: str | None = Field(None, examples=['Veg only meal'])


class Name(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    family1: str = Field(..., description='Last (family) name.', examples=['Gandas'])
    family2: str | None = Field(None, examples=['FamilyTwo'])
    given: str = Field(..., description='First (given) name.', examples=['Vichitr'])
    middle: str | None = Field(None, description='Middle name.', examples=['Kumar'])
    suffix: NameSuffix | None = Field(
        None,
        description='Suffix used with the name. For example SR or JR.',
        examples=['SR'],
    )
    preferred: str | None = Field(
        None,
        description='Informal preferred name added by traveler. This is not used on any PNR or tickets',
        examples=['Don'],
    )


class NotificationPreferencePerType(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    notificationType: NotificationType | None = None
    emailPreference: NotificationEmailPreference | None = None


class NotificationPreferences(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preferences: Sequence[NotificationPreferencePerType] | None = None


class PersonalDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: Name | None = None
    title: UserTitle | None = None
    gender: Gender | None = None
    pronoun: PreferredPronoun | None = None
    preferredLanguage: str | None = Field(
        None, description='Preferred language of the user'
    )
    nationality: str | None = Field(None, description='Nationality of the user')


class PreferredAirport(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airportName: str | None = Field(
        None,
        description='Airport name.',
        examples=['San Francisco International Airport'],
    )
    airportCode: str = Field(..., description='IATA airport code.', examples=['SFO'])
    label: PreferredLocationLabel


class ProfileOwner(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId


class SeatPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hasAccessibility: bool | None = Field(
        False,
        description='Whether or not requires assistance for disability.',
        examples=[False],
    )
    seatTypes: Sequence[SeatPrefType] | None = None
    seatLocations: Sequence[SeatPrefLocation] | None = None
    deckLevels: Sequence[DeckLevel] | None = None
    seatDirections: Sequence[SeatPrefDirection] | None = None


class SecondaryServiceProviderTmc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tmcId: CompanyId = Field(..., description='Id of the service provider TMC.')
    supplier: SupplierType = Field(
        ..., description='Supplier for which this service provider should be used.'
    )
    travelType: TravelType = Field(
        ..., description='Travel type for which this service provider should be used.'
    )


class TmcBasicInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    contractingTmc: CompanyRef = Field(
        ..., description='Contracting TMC is the TMC the user/organization contracted.'
    )
    bookingTmc: CompanyRef = Field(
        ...,
        description='Booking TMC is the TMC used for the bookings for the user/organization.',
    )


class TmcInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CompanyId = Field(..., description='TMC id.')
    primaryServiceProviderTmc: PrimaryServiceProviderTmc = Field(
        ..., description='Primary service provider TMC for the TMC.'
    )
    secondaryServiceProviderTmcs: Sequence[SecondaryServiceProviderTmc] | None = Field(
        None, description='Secondary service provider TMCs for the TMC.'
    )
    partnerTmcId: CompanyId | None = Field(
        None, description='Useful to identify the clients onboarded by a PARTNER_TMC'
    )


class TravelArrangerBasicInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: Name | None = None
    email: EmailStr | None = Field(None, examples=['<EMAIL>'])
    phoneNumbers: Sequence[PhoneNumber] | None = None
    userId: UserId
    status: TravelerArrangerStatus | None = None


class TravelArrangerMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    arrangerFor: Sequence[TravelerConfigForArranger]


class TravelArrangerMetadataV2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    roleType: Literal['TRAVEL_ARRANGER'] = Field(
        'TRAVEL_ARRANGER', description='Discriminator property for role metadata.'
    )
    arrangerFor: Sequence[TravelerConfigForArranger]


class UserDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId
    organization: Reference = Field(
        ..., description='The organization associated with the user.'
    )
    persona: Persona = Field(
        ..., description='Persona of the user like EMPLOYEE, GUEST etc.'
    )
    isActive: bool = Field(..., description='Whether user is active or not.')
    externalId: str | None = Field(None, description='External ID of the user.')
    personalDetail: PersonalDetail = Field(
        ..., description='Personal details of the user.'
    )
    businessDetail: BusinessDetail = Field(
        ..., description='Business details of the user.'
    )
    createdAt: DateTimeLocal = Field(
        ..., description='Date and time of the user profile creation.'
    )
    lastUpdatedAt: DateTimeLocal = Field(
        ..., description='Date and time of the latest update made to the user profile.'
    )


class UserOrgId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    organizationAgencyId: OrganizationAgencyId | None = None
    organizationId: OrganizationId
    userId: UserId
    tmcInfo: TmcInfo | None = None
    tmcBasicInfo: TmcBasicInfo | None = None


class UserTravelArrangers(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelArrangers: Sequence[TravelArrangerBasicInfo]


class AdhocUserInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    profileOwner: ProfileOwner
    isSaved: bool | None = Field(
        False,
        description='A boolean flag to show if ad-hoc traveler is visible in search. While updating the user \nif client tries to update this field, it will throw exception.\n',
    )


class AirPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlinePrefs: Sequence[AirlinePref] | None = None
    alliancePref: AlliancePref | None = None
    farePref: FarePref | None = None
    homeAirport: str | None = Field(None, examples=['NEW YORK'])
    mealPref: MealPref | None = None
    numStopPref: NumStopsPref | None = None
    seatAmenityPref: SeatAmenityPref | None = None
    seatLocationPrefs: Sequence[SeatLocationPref] | None = None
    preferredAirports: Sequence[PreferredAirport] | None = Field(
        None, description='A list of user preferred airports.'
    )


class EmergencyContactInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress | None = None
    designation: str | None = Field(None, examples=['MANAGER'])
    email: EmailStr = Field(..., examples=['<EMAIL>'])
    name: Name | None = None
    phoneNumber: PhoneNumber | None = None
    userOrgId: UserOrgId | None = None


class ListUsersV3Response(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    users: Sequence[UserDetail] | None = Field(
        None, description='Users matching the filters specified in the request.'
    )
    pagination: OffsetBasedPaginationResponseParams | None = None


class RailPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preferredRailStations: Sequence[PreferredRailStation] | None = Field(
        None, description='A list of user preferred rail stations.'
    )
    seatPreference: SeatPref | None = None
    travelClasses: Sequence[RailTravelClass] | None = Field(
        None, description='A list of class of service for rail.'
    )
    coachPreferences: Sequence[CoachPref] | None = Field(
        None, description='A list of coach preference for rail.'
    )
    conditionalRates: Sequence[ConditionalRate] | None = Field(
        None, description='A list of conditional rates for rail.'
    )


class RoleMeta(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelArrangerMetadata: TravelArrangerMetadata | None = None


class RoleMetadataV2(
    RootModel[TravelArrangerMetadataV2 | CompanyRoleInfo | TmcRoleInfo]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: TravelArrangerMetadataV2 | CompanyRoleInfo | TmcRoleInfo = Field(
        ..., discriminator='roleType'
    )


class TravelPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airPref: AirPref | None = None
    preferredCurrency: str | None = Field(None, examples=['USD'])
    railCards: Sequence[RailCard] | None = None
    railPref: RailPref | None = None
    carPref: CarPref | None = None
    hotelPref: HotelPref | None = None


class TravelPreferences(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preferences: TravelPref | None = None


class UserPersonalInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    addresses: Sequence[PostalAddress] | None = None
    dob: DateModel | None = None
    email: EmailStr | None = Field(None, examples=['<EMAIL>'])
    emergencyContactInfo: EmergencyContactInfo | None = None
    gender: Gender | None = None
    identityDocs: Sequence[IdentityDocument] | None = Field(
        None,
        description='List of user identity documents.',
        examples=[
            [
                {
                    'passport': {
                        'docId': 'PASSPORTID',
                        'expiryDate': {'iso8601': '2017-07-21'},
                        'issueCountry': 'IN',
                        'issuedDate': {'iso8601': '2017-07-21'},
                        'nationalityCountry': 'IN',
                        'type': 'REGULAR',
                    }
                },
                {'ktn': {'number': '123456', 'issueCountry': 'US'}},
            ]
        ],
    )
    name: Name | None = None
    phoneNumbers: Sequence[PhoneNumber] | None = None
    profilePicture: Image | None = None
    nationality: str | None = Field(
        None, description='Nationality of user', examples=['Indian']
    )
    title: UserTitle | None = None
    preferredLanguage: str | None = Field(
        None, description='Language preferred by user.', examples=['en-US']
    )
    preferredPronoun: PreferredPronoun | None = Field(
        None, description='Pronoun preferred by user.'
    )
    travelerName: Name | None = Field(
        None, description='A name of user that does not contain special characters.'
    )
    emergencyContact: EmergencyContact | None = None


class UserProfile(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID | None = None
    personalInfo: UserPersonalInfo | None = None
    businessInfo: BusinessInfo | None = None
    persona: Persona | None = None
    isActive: bool | None = Field(
        None, description='Indicates if traveler is active.', examples=[True]
    )
    tier: Tier | None = 'BASIC'
    relativeOf: RelativeOf | None = None
    travelPreferences: TravelPreferences | None = None
    membershipInfo: MembershipInfo | None = None
    notificationPreferences: NotificationPreferences | None = None
    travelArrangers: UserTravelArrangers | None = None
    adhocUserInfo: AdhocUserInfo | None = None
    externalId: str | None = Field(
        None, description='The partner-assigned user identifier.'
    )


class UserUpdateRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    personalInfo: UserPersonalInfo | None = None
    businessInfo: BusinessInfo
    persona: Persona
    tier: Tier | None = 'BASIC'
    relativeOf: RelativeOf | None = None
    travelPreferences: TravelPreferences | None = None
    membershipInfo: MembershipInfo | None = None
    notificationPreferences: NotificationPreferences | None = None
    adhocUserInfo: AdhocUserInfo | None = None
    externalId: str | None = Field(
        None, description='The partner-assigned user identifier.'
    )
    travelArrangers: UserTravelArrangers | None = None


class RoleConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    roleType: RoleTypeEnum | None = None
    roleMetadata: RoleMeta | None = None
    metadata: RoleMetadataV2 | None = None


class RolesConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    roles: Sequence[RoleConfig] | None = None


class UserCreateRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    personalInfo: UserPersonalInfo | None = None
    businessInfo: BusinessInfo | None = None
    persona: Persona
    relativeOf: RelativeOf | None = None
    billingCurrency: str | None = Field(
        None, description='Preferred billing currency of the user', examples=['INR']
    )
    tier: Tier | None = 'BASIC'
    externalId: str | None = Field(
        None,
        description='A partner-assigned user identifier. This value must be unique for all travelers within a PNR.\n',
    )
    travelPreferences: TravelPreferences | None = None
    membershipInfo: MembershipInfo | None = None
    notificationPreferences: NotificationPreferences | None = None
    travelArrangers: UserTravelArrangers | None = None
    adhocUserInfo: AdhocUserInfo | None = None
    roles: Sequence[RoleConfig] | None = None
