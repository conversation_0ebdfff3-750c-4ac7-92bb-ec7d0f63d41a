"""
External authentication models for Spotnana API.

These models represent the raw request/response formats for Spotnana authentication endpoints.
Part of the DTO External Layer - raw API models without business logic transformation.
"""

from pydantic import BaseModel, Field


class AuthenticationRequest(BaseModel):
    """Request model for Spotnana authentication endpoint."""

    client_id: str = Field(alias="clientId")
    client_secret: str = Field(alias="clientSecret")
    grant_type: str = Field(default="client_credentials")


class AuthenticationResponse(BaseModel):
    """Response model from Spotnana authentication endpoint."""

    token: str
    expiry_time_in_seconds: int = Field(alias="expiryTimeInSeconds")
    token_type: str | None = Field(default="Bearer")
    scope: str | None = None