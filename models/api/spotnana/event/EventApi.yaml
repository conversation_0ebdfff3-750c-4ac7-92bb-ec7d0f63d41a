openapi: 3.1.0
info:
  title: Event API
  version: v2
servers:
- url: https://apis.spotnana.com
  description: Staging URL
security:
- Bearer: []
components:
  securitySchemes:
    Bearer:
      type: http
      scheme: bearer
  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFound:
      description: The specified resource was not found.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
  schemas:
    AccessType:
      type: string
      description: Access Type of the Payment Source being created.
      enum:
      - CENTRALISED
      - PERSONAL
      - APPLICATION
      - TMC
      - ITINERARY
      - EVENT
      - EVENT_TEMPLATE
      example: CENTRALISED
      x-ignoreBreakingChanges:
      - AccessType->EVENT_TEMPLATE
    AccessTypeAttributes:
      type: object
      title: AccessTypeAttributes
      description: Attributes applicable to the payment source for specific Access
        Type.
      properties:
        travelTypeFilter:
          $ref: '#/components/schemas/TravelTypeFilter'
    AddTravelersToEventRequest:
      title: AddTravelersToEventRequest
      description: Add travelers to an event request
      type: object
      properties:
        type:
          $ref: '#/components/schemas/EventType'
        userIds:
          type: array
          items:
            $ref: '#/components/schemas/UserId'
    AddTravelersToEventResponse:
      title: AddTravelersToEventResponse
      description: Add travelers to an event response
      type: object
      properties:
        addedUserIds:
          type: array
          items:
            $ref: '#/components/schemas/UserId'
    AdditionalInfo:
      type: object
      title: AdditionalInfo
      description: Additional data need to be sent along with the custom field response.
      discriminator:
        propertyName: type
        mapping:
          VARIABLE: '#/components/schemas/Variable'
          EXPRESSION: '#/components/schemas/Expression'
      oneOf:
      - $ref: '#/components/schemas/Variable'
      - $ref: '#/components/schemas/Expression'
    AirBookingGuideline:
      title: AirBookingGuideline
      description: Air related guidelines
      type: object
      properties:
        allowedAirports:
          description: List of allowed airports for arrival of flight-in-event and
            departure of flight-out-event.
          type: array
          items:
            $ref: '#/components/schemas/AirportInfo'
        arrivalBookingWindow:
          description: Booking window for flight-in-event.
          $ref: '#/components/schemas/EventBookingWindow'
        departureBookingWindow:
          description: Booking window for flight-out-event.
          $ref: '#/components/schemas/EventBookingWindow'
        allowedFlightGuidelines:
          description: List of allowed flight guidelines for the event.
          type: array
          items:
            $ref: '#/components/schemas/AllowedFlightGuideline'
    AirBookingGuidelineWrapper:
      type: object
      title: AirBookingGuidelineWrapper
      description: Wrapper for AirBookingGuideline
      properties:
        airBookingGuideLine:
          $ref: '#/components/schemas/AirBookingGuideline'
    AirBookingStatusFilter:
      type: object
      title: AirBookingStatusFilter
      description: Air Booking Status Filter
      required:
      - type
      - bookingStatus
      properties:
        type:
          type: string
          description: Type of filter
        bookingStatus:
          type: array
          description: Air Booking status
          items:
            $ref: '#/components/schemas/BookingStatusType'
    AirItinerary:
      type: object
      title: AirItinerary
      properties:
        airItineraryId:
          $ref: '#/components/schemas/AirItineraryId'
    AirItineraryId:
      type: object
      title: AirItineraryId
      description: Air itinerary id
      properties:
        ancillaryResponseId:
          type: string
          description: Ancillary response id
        seatMapResponseId:
          type: string
          description: Seat map response id
        initiateBookingResponseId:
          type: string
          description: Initiate Booking Response id.
    AirRsvpResponse:
      title: AirRsvpResponse
      type: object
      description: Air rsvp response
      properties:
        notNeeded:
          type: boolean
          description: Whether air booking is needed by the traveler or not
          example: true
    Airline:
      title: Airline
      description: Airline Details
      type: object
      required:
      - carrierCode
      - airlineName
      properties:
        carrierCode:
          type: string
          description: Unique code for the Airline
          example: AA
        airlineName:
          type: string
          description: Full Name of the Airline
          example: American Airlines
        isPresentInPreferredVendors:
          type: boolean
          description: Whether the airline is present in preferred vendor list. This
            is an optional field which gets populated only in the preferred vendor
            autocomplete API.
          example: true
    AirlineInfo:
      title: AirlineInfo
      type: object
      required:
      - airlineCode
      - airlineName
      properties:
        airlineCode:
          type: string
          description: IATA code for airline.
          example: AA
        airlineName:
          type: string
          description: Airline name
          example: American Airlines
    AirlineProgram:
      type: object
      title: AirlineProgram
      description: Descriptor for Airline program details.
      required:
      - airlineInfo
      - airlineProgramMetadata
      properties:
        airlineInfo:
          type: array
          description: Eligible airlines' information list.
          items:
            $ref: '#/components/schemas/Airline'
        airlineProgramMetadata:
          description: Airline program payment specific metadata.
          $ref: '#/components/schemas/AirlineProgramMetadata'
    AirlineProgramMetadata:
      type: object
      title: AirlineProgramMetadata
      oneOf:
      - $ref: '#/components/schemas/UATPMetadataWrapper'
      - $ref: '#/components/schemas/UAPassPlusMetadataWrapper'
    AirlineProgramWrapper:
      type: object
      title: AirlineProgramWrapper
      description: Wrapper for AirlineProgram
      properties:
        airlineProgram:
          $ref: '#/components/schemas/AirlineProgram'
    AirportInfo:
      title: AirportInfo
      description: Detailed information about the airport.
      type: object
      properties:
        airportCode:
          type: string
          description: 3-letter code of the airport.
          example: WRA
        airportName:
          type: string
          description: Full name of the airport.
          example: Warder Airport
        cityName:
          type: string
          description: Name of the city in which the airport is located (or is nearest
            to).
          example: Werder
        countryName:
          type: string
          description: Name of the country in which the airport is located.
          example: Ethiopia
        countryCode:
          type: string
          description: 2-letter IATA country code associated with the airport.
          example: ET
        zoneName:
          type: string
          description: Name of the time zone associated with the airport.
          example: Africa/Addis_Ababa
        stateCode:
          type: string
          description: 2-letter IATA code for the state in which the airport is located.
          example: CA
    AllowTravelersPolicy:
      title: AllowTravelersPolicy
      description: Apply travelers policy when it is less restrictive.
      type: string
      enum:
      - ALLOWED
      - NOT_ALLOWED
    AllowTravelersToRsvp:
      title: AllowTravelersToRsvp
      description: Allow travelers to rsvp for the event
      type: string
      enum:
      - ALLOWED
      - NOT_ALLOWED
    AllowedFlightGuideline:
      title: AllowedFlightGuideline
      description: Flight related guideline
      type: object
      required:
      - allowedFlightType
      properties:
        allowedFlightType:
          description: The type of flight booking allowed for the event.
          $ref: '#/components/schemas/AllowedFlightType'
        numberOfLegs:
          description: The number of legs allowed for the flight booking.
          type: integer
          example: 2
    AllowedFlightType:
      title: AllowedFlightType
      description: The types of flight booking guidelines
      type: string
      enum:
      - ONE_WAY
      - ROUND_TRIP
      - MULTICITY
      example: ROUND_TRIP
    AllowedPaymentConfig:
      type: array
      description: Allowed payment configuration for the event template
      items:
        $ref: '#/components/schemas/PaymentConfigForTravelType'
    AmadeusCheckout:
      type: object
      title: AmadeusCheckout
      required:
      - splitPaymentAllowed
      description: Metadata for Amadeus Checkout payment source.
      properties:
        paymentPageId:
          type: string
          example: aslkdjalwjd
          description: Payment page Id.
        splitPaymentAllowed:
          type: boolean
          example: true
          default: false
          description: True if split payment is allowed in Amadeus Checkout, false
            otherwise
        splitOptionsByCardCompany:
          type: array
          items:
            $ref: '#/components/schemas/SplitOptionByCardCompany'
    AmadeusCheckoutWrapper:
      type: object
      title: AmadeusCheckoutWrapper
      description: Wrapper for Amadeus Checkout payment source.
      required:
      - amadeusCheckout
      properties:
        amadeusCheckout:
          $ref: '#/components/schemas/AmadeusCheckout'
    AsyncJobDetailRequest:
      title: AsyncJobDetailRequest
      type: object
      required:
      - companyId
      properties:
        companyId:
          description: Company Id for which job was created.
          type: string
          example: c5977a9b-095a-40be-a1bb-b9d3c894e4a5
        filters:
          description: Filter to fetch the job details.
          $ref: '#/components/schemas/AsyncJobFilter'
    AsyncJobDetailResponse:
      title: AsyncJobDetailResponse
      type: object
      description: Response object which contain the async job detail.
      properties:
        jobId:
          type: string
          format: uuid
          description: Job Id for the async event job.
          example: f7b3b3b3-7b3b-4b3b-8b3b-3b3b3b3b3b3b
        referenceId:
          type: string
          description: Reference Id associated with the job.
          example: '8859676988'
        type:
          description: Job type.
          $ref: '#/components/schemas/EventJobType'
        status:
          description: Job status.
          $ref: '#/components/schemas/EventJobStatus'
        totalItems:
          type: integer
          description: Total number of items which are being processed by the job.
          example: 42
        successCount:
          type: integer
          description: Success item count.
          example: 41
        failureCount:
          type: integer
          description: Failure item count.
          example: 1
        jobItems:
          type: array
          description: Failed item array.
          items:
            $ref: '#/components/schemas/JobItem'
        createdAt:
          description: Created date time for an job.
          $ref: '#/components/schemas/DateTimeOffset'
        completedAt:
          description: Updated date time for an job.
          $ref: '#/components/schemas/DateTimeOffset'
    AsyncJobFilter:
      type: object
      title: AsyncJobFilter
      oneOf:
      - $ref: '#/components/schemas/JobIdFilter'
      - $ref: '#/components/schemas/ReferenceIdFilter'
      discriminator:
        propertyName: filterType
        mapping:
          JOB_ID_FILTER: '#/components/schemas/JobIdFilter'
          REFERENCE_ID_FILTER: '#/components/schemas/ReferenceIdFilter'
    BookingStatusType:
      title: BookingStatusType
      description: Booking status of the travel.
      type: string
      enum:
      - BOOKED
      - NOT_BOOKED
      - OPTED_OUT
      example: BOOKED
    BrexBudget:
      type: object
      title: BrexBudget
      description: Metadata for Brex Budget payment source.
      properties:
        budgetName:
          type: string
          example: Travel Budget
          description: Budget name.
        cardNumber:
          type: string
          example: '1234567812345678123'
          description: Card number of the budget.
        budgetRemainingBalanceFormatted:
          type: string
          example: $90,000.00
          description: Remaining balance of the budget formatted with currency.
    BrexBudgetWrapper:
      type: object
      title: BrexBudgetWrapper
      description: Wrapper for BrexBudget
      required:
      - brexBudget
      properties:
        brexBudget:
          $ref: '#/components/schemas/BrexBudget'
    BucketEmailPreferences:
      type: object
      title: BucketEmailPreferences
      description: Email communication preferences based on bucket types.
      required:
      - bucketTypes
      properties:
        bucketTypes:
          type: array
          description: List of email bucket types.
          items:
            $ref: '#/components/schemas/EmailBucketType'
        isEnabled:
          type: boolean
          description: Indicates whether emails are disabled.
          default: false
        toEmails:
          type: array
          description: List of email address to be included in emails.
          items:
            type: string
            format: email
            example: <EMAIL>
        ccEmails:
          type: array
          description: List of email address to be cc-ed in emails.
          items:
            type: string
            format: email
            example: <EMAIL>
        bccEmails:
          type: array
          description: List of email address to be bcc-ed in emails.
          items:
            type: string
            format: email
            example: <EMAIL>
    BulkCancelEventRequest:
      title: BulkCancelEventRequest
      type: object
      description: Request payload for bulk event cancellation.
      required:
      - cancelEventRequests
      properties:
        cancelAsync:
          type: boolean
          description: Flag to indicate if the cancellation should be processed asynchronously.
          default: false
          example: false
        cancelEventRequests:
          type: array
          description: List of events to be cancelled.
          items:
            $ref: '#/components/schemas/CancelEventRequest'
          minItems: 1
          maxItems: 100
    BulkCancelEventResponse:
      title: BulkCancelEventResponse
      type: object
      description: Response payload for the bulk event cancellation.
      required:
      - cancelEventResponses
      properties:
        cancelEventResponses:
          type: array
          description: List of events cancellation response.
          items:
            $ref: '#/components/schemas/CancelEventResponse'
    BulkDeleteEventRequest:
      title: BulkDeleteEventRequest
      type: object
      description: Request payload for bulk event deletion.
      required:
      - deleteEventList
      properties:
        deleteEventList:
          type: array
          description: List of events to be deleted.
          items:
            $ref: '#/components/schemas/DeleteEventRequest'
          minItems: 1
          maxItems: 100
    BulkDeleteEventResponse:
      title: BulkDeleteEventResponse
      type: object
      description: Response payload for the bulk event deletion.
      required:
      - deleteEventResponses
      properties:
        deleteEventResponses:
          type: array
          description: List of events delete response.
          items:
            $ref: '#/components/schemas/DeleteEventResponse'
    BulkPublishEventRequest:
      title: BulkPublishEventRequest
      type: object
      description: Request payload for bulk event publishing.
      required:
      - publishEventList
      properties:
        publishEventList:
          type: array
          description: List of events to be published.
          items:
            $ref: '#/components/schemas/PublishEventRequest'
          minItems: 1
          maxItems: 100
    BulkPublishResponse:
      title: BulkPublishResponse
      type: object
      description: Response payload for the bulk event publishing.
      required:
      - publishEventResponses
      properties:
        publishEventResponses:
          type: array
          description: List of events publish response.
          items:
            $ref: '#/components/schemas/PublishEventResponse'
    BulkUpdateEventsRequest:
      title: BulkUpdateEventsRequest
      description: Bulk update events info request object
      type: object
      properties:
        eventsToUpdate:
          type: array
          description: A list of events with the required updates
          items:
            $ref: '#/components/schemas/UpdateEventRequestItem'
          minItems: 1
          maxItems: 100
        updateAsync:
          type: boolean
          description: Whether to update the events asynchronously or not.
          default: false
          example: true
    BulkUpdateEventsResponse:
      title: BulkUpdateEventsResponse
      description: Bulk update events info response
      type: object
      required:
      - updateEventsResponse
      properties:
        updateEventsResponse:
          type: array
          description: List of events update response.
          items:
            $ref: '#/components/schemas/UpdateEventResponse'
    CancelEventRequest:
      title: CancelEventRequest
      type: object
      description: Event details for cancellation.
      required:
      - eventId
      - type
      properties:
        eventId:
          type: string
          description: Unique identifier for the event.
          example: '56789012'
        type:
          $ref: '#/components/schemas/EventType'
    CancelEventResponse:
      title: CancelEventResponse
      type: object
      description: Event cancellation response details.
      required:
      - eventId
      - status
      properties:
        eventId:
          type: string
          description: Unique identifier for the event.
          example: '56789012'
        status:
          $ref: '#/components/schemas/OperationStatus'
        message:
          type: string
          description: Details about success or failure reason.
          example: Cancelled successfully
    CarBookingGuideline:
      title: CarBookingGuideline
      description: Car related guidelines
      type: object
      properties:
        pickupBookingWindow:
          $ref: '#/components/schemas/EventBookingWindow'
        dropoffBookingWindow:
          $ref: '#/components/schemas/EventBookingWindow'
        paymentGuidelines:
          $ref: '#/components/schemas/CarBookingPaymentGuidelines'
          readOnly: true
    CarBookingGuidelineWrapper:
      type: object
      title: CarBookingGuidelineWrapper
      description: Wrapper for CarBookingGuideline
      properties:
        carBookingGuideLine:
          $ref: '#/components/schemas/CarBookingGuideline'
    CarBookingPaymentGuidelines:
      title: CarBookingPaymentGuidelines
      description: Car booking payment guidelines
      type: object
      properties:
        allowedPaymentVendors:
          description: List of allowed car vendors for the event
          type: array
          items:
            $ref: '#/components/schemas/VendorInfo'
        onlyVendorBooking:
          type: boolean
          description: Whether only Vendor bookings are allowed for the event.
          example: true
    CarBookingStatusFilter:
      type: object
      title: CarBookingStatusFilter
      description: Car Booking Status Filter
      required:
      - type
      - bookingStatus
      properties:
        type:
          type: string
          description: Type of filter
        bookingStatus:
          type: array
          description: Car Booking status
          items:
            $ref: '#/components/schemas/BookingStatusType'
    CarItinerary:
      type: object
      title: CarItinerary
      properties:
        carItineraryId:
          $ref: '#/components/schemas/CarItineraryId'
    CarItineraryId:
      type: object
      title: CarItineraryId
      description: Car itinerary id
      required:
      - searchId
      - carId
      properties:
        searchId:
          type: string
          description: Search id
        carId:
          type: string
          description: Selected car option
    CarPaymentSourceMetadata:
      type: object
      title: CarPaymentSourceMetadata
      required:
      - travelType
      properties:
        travelType:
          type: string
          description: Travel Type for the metadata. For cars it's CAR.
          enum:
          - CAR
        vendors:
          description: Vendors for which the payment source is applicable.
          type: array
          items:
            $ref: '#/components/schemas/CarVendorBasic'
    CarRsvpResponse:
      title: CarRsvpResponse
      type: object
      description: Car rsvp response
      properties:
        notNeeded:
          type: boolean
          description: Whether car booking is needed by the traveler or not
          example: true
    CarVendorBasic:
      type: object
      title: CarVendorBasic
      description: Car Vendor Basic Information
      required:
      - code
      properties:
        code:
          type: string
          description: Vendor code
          example: ZE
        name:
          type: string
          description: Vendor name
          example: HERTZ
    Card:
      type: object
      title: Card
      description: User card info
      required:
      - number
      properties:
        id:
          type: string
          description: Unique identifier for this card
          format: uuid
          example: 34d536b6-f8ff-11eb-9a61-0242ac180002
        type:
          type: string
          enum:
          - UNKNOWN
          - CREDIT
          - DEBIT
          description: Type of card
          example: CREDIT
        company:
          $ref: '#/components/schemas/CardCompany'
        name:
          description: Name on card
          type: string
          example: Harrison Schwartz
        address:
          description: Billing address
          $ref: '#/components/schemas/PostalAddress'
        number:
          type: string
          description: Card number
          example: '****************'
        expiryMonth:
          type: integer
          format: int32
          minimum: 1
          maximum: 12
          description: Expiry month
          example: 1
          deprecated: true
        expiryYear:
          type: integer
          format: int32
          description: Expiry year
          minimum: 2000
          example: 2010
          deprecated: true
        cvv:
          type: string
          description: Card cvv number
          example: '012'
        label:
          type: string
          description: Card Label
          example: Label amex
        currency:
          type: string
          description: Native currency of the card.
          example: USD
        externalId:
          type: string
          description: Spotnana partner card id.
          example: bxt_RNGsNfzgJDaTstKIKqK4xEuhGYAnMdYK8T40
        vaultId:
          type: string
          description: ID of the vault used for creating the card.
          format: uuid
          example: 34d536b6-f8ff-11eb-9a61-0242ac180002
        expiry:
          description: Card Expiry.
          $ref: '#/components/schemas/CardExpiry'
        ownershipLabel:
          $ref: '#/components/schemas/OwnershipLabel'
          example: PERSONAL
    CardCompany:
      type: string
      enum:
      - NONE
      - VISA
      - MASTERCARD
      - AMEX
      - DISCOVER
      - AIR_TRAVEL_UATP
      - CARTE_BLANCHE
      - DINERS_CLUB
      - JCB
      - BREX
      - UNION_PAY
      - EURO_CARD
      - ACCESS_CARD
      - ELO_CARD
      description: The issuing company of card
      example: VISA
    CardDescriptor:
      type: object
      title: CardMetadata
      description: Descriptor for card payment source.
      required:
      - card
      properties:
        card:
          $ref: '#/components/schemas/Card'
        isCentrallyBilled:
          type: boolean
          description: Whether card is centrally billed or not.
          default: false
        isLodgeCard:
          type: boolean
          description: Whether card is a lodge card or not.
          default: false
    CardDescriptorWrapper:
      type: object
      title: CardDescriptorWrapper
      description: Wrapper for CardDescriptor
      properties:
        cardDescriptor:
          $ref: '#/components/schemas/CardDescriptor'
    CardExpiry:
      title: CardExpiry
      type: object
      description: Contains the expiry of a Card.
      oneOf:
      - $ref: '#/components/schemas/TokenizedExpiryWrapper'
      - $ref: '#/components/schemas/ExpiryWrapper'
    CashDescriptor:
      type: object
      title: CashDescriptor
      description: Descriptor for Cash.
      required:
      - label
      properties:
        label:
          type: string
          description: Label for Cash payment.
          example: Cash Payment for Amex TLS
    CashDescriptorWrapper:
      type: object
      title: CashDescriptorWrapper
      description: Wrapper for Cash Payment Source.
      properties:
        cashDescriptor:
          $ref: '#/components/schemas/CashDescriptor'
    CompanyFilter:
      type: object
      title: CompanyFilter
      description: CompanyFilter details
      properties:
        companyIds:
          type: array
          description: Company id List
          deprecated: true
          items:
            $ref: '#/components/schemas/CompanyId'
        companyId:
          description: Company id
          $ref: '#/components/schemas/CompanyId'
    CompanyFilterWrapper:
      type: object
      title: CompanyFilterWrapper
      description: Wrapper for CompanyFilter
      properties:
        companyFilter:
          $ref: '#/components/schemas/CompanyFilter'
    CompanyId:
      type: object
      title: CompanyId
      description: Company ID
      required:
      - id
      properties:
        id:
          type: string
          format: uuid
          example: f49d00fe-1eda-4304-ba79-a980f565281d
    CompanySpecifiedAttributeOverride:
      title: CompanySpecifiedAttributeOverride
      type: object
      description: Company specified attribute override for the event
      required:
      - behavior
      - attribute
      properties:
        behavior:
          $ref: '#/components/schemas/OverrideBehavior'
        attribute:
          $ref: '#/components/schemas/CompanySpecifiedUserAttribute'
    CompanySpecifiedUserAttribute:
      type: object
      description: Company specified user arbitrary attribute.
      required:
      - fixedColumnName
      properties:
        fixedColumnName:
          type: string
          example: contingentType
        value:
          type: string
          example: FSTV
    ConfermaMetadata:
      type: object
      title: Conferma
      description: conferma card descriptor.
      required:
      - cardPoolClientId
      properties:
        cardPoolClientId:
          type: string
          description: card pool client Id.
          example: test-id
        cardLabel:
          type: string
          description: Name/Label for visual identification of the card.
          example: cardCompany
        company:
          $ref: '#/components/schemas/CardCompany'
        paymentInstructionIds:
          type: array
          description: List of payment instruction ids
          items:
            $ref: '#/components/schemas/PaymentInstructionId'
    ConfermaMetadataWrapper:
      type: object
      title: ConfermaMetadataWrapper
      description: Wrapper for ConfermaMetadata
      required:
      - confermaMetadata
      properties:
        confermaMetadata:
          $ref: '#/components/schemas/ConfermaMetadata'
    CostCenterFilter:
      type: object
      title: CostCenterFilter
      description: CostCenterFilter details.
      required:
      - costCenterIds
      - companyId
      properties:
        companyId:
          description: company id
          $ref: '#/components/schemas/CompanyId'
        costCenterIds:
          type: array
          description: List of cost center ids
          items:
            $ref: '#/components/schemas/CostCenterId'
    CostCenterFilterWrapper:
      type: object
      title: CostCenterFilterWrapper
      description: Wrapper for CostCenterFilter
      properties:
        costCenterFilter:
          $ref: '#/components/schemas/CostCenterFilter'
    CostCenterId:
      type: object
      title: Cost center id.
      required:
      - id
      properties:
        id:
          type: string
          example: 731ccbca-0415-6fe1-d235-c324dfbe7423
    CostCenterOverride:
      title: CostCenterOverride
      type: object
      description: Cost center override for the event
      required:
      - behavior
      properties:
        behavior:
          $ref: '#/components/schemas/OverrideBehavior'
        costCenterRef:
          $ref: '#/components/schemas/Reference'
    CountryFilter:
      type: object
      title: CountryFilter
      description: CountryFilter details.
      required:
      - companyId
      - countryCodes
      properties:
        countryCodes:
          type: array
          description: country Codes List
          items:
            description: ISO 2-letter country code
            type: string
            example: IN
        companyId:
          description: The company Id for the filter.
          $ref: '#/components/schemas/CompanyId'
    CountryFilterWrapper:
      type: object
      title: CountryFilterWrapper
      description: Wrapper for CountryFilter
      properties:
        countryFilter:
          $ref: '#/components/schemas/CountryFilter'
    CreateEventRequest:
      title: CreateEventRequest
      description: Create Event request object
      type: object
      required:
      - name
      - location
      x-ignoreBreakingChanges:
      - EventApi.yaml->CreateEventRequest->eventcustomFieldV3Responses
      - CreateEventRequest->eventCustomFieldV3Responses
      - EventApi.yaml->CustomFieldV3Response
      properties:
        name:
          type: string
          description: Name of the event
        description:
          type: string
          description: Description of the event
        documents:
          type: array
          description: Documents attached for an event
          items:
            $ref: '#/components/schemas/Document'
        startDateTime:
          $ref: '#/components/schemas/DateTimeLocal'
        endDateTime:
          $ref: '#/components/schemas/DateTimeLocal'
        location:
          $ref: '#/components/schemas/EventLocation'
        contacts:
          type: array
          description: Contacts for the event
          items:
            $ref: '#/components/schemas/UserId'
        bookingGuidelines:
          type: array
          description: A list of booking guideline for the event.
          items:
            $ref: '#/components/schemas/EventBookingGuideline'
        allowedBookingTypes:
          type: array
          description: Allowed booking types for the event group
          items:
            $ref: '#/components/schemas/EventAllowedBookingType'
        externalId:
          type: string
          description: External Id for the event
          example: qwert123
        eventCustomFieldResponses:
          deprecated: true
          type: array
          description: List of custom field responses for the event.
          items:
            $ref: '#/components/schemas/CustomFieldResponse'
        eventCustomFieldV3Responses:
          type: array
          description: List of custom field responses for an event.
          items:
            $ref: '#/components/schemas/CustomFieldPrefilledResponse'
        parentEventId:
          type: string
          description: Optional parent event's id.
          example: '56789012'
        companyId:
          description: Company id
          $ref: '#/components/schemas/CompanyId'
        bookingStyle:
          description: Whether the event bookings will be self served by the traveler
            or would be arranged by an agent for the traveler.
          $ref: '#/components/schemas/EventBookingStyle'
        referenceUsers:
          type: array
          description: Event group reference user ids
          items:
            $ref: '#/components/schemas/UserId'
        travelerTypes:
          type: array
          description: Allowed traveler types for the event
          items:
            $ref: '#/components/schemas/Persona'
        policyId:
          type: string
          format: uuid
          description: Policy associated with the event.
          example: f7b3b3b3-7b3b-4b3b-8b3b-3b3b3b3b3b3b
        travellers:
          type: array
          description: Travellers associated with the event
          items:
            $ref: '#/components/schemas/UserId'
          minItems: 0
          maxItems: 1000
        type:
          $ref: '#/components/schemas/EventType'
        allowTravelersPolicy:
          $ref: '#/components/schemas/AllowTravelersPolicy'
        emailPreferences:
          $ref: '#/components/schemas/BucketEmailPreferences'
        internalNotes:
          type: string
          description: Internal notes of the event
          example: Use travelers default as payment method.
        allowTravelersToRsvp:
          $ref: '#/components/schemas/AllowTravelersToRsvp'
        tripOverrides:
          $ref: '#/components/schemas/TripOverrides'
    CreditStatus:
      type: string
      description: Status of the credit.
      enum:
      - STATUS_UNKNOWN
      - OPEN
      - USED
      - RESERVED
      x-ignoreBreakingChanges:
      - CreditStatus->RESERVED
    CreditUsageType:
      type: string
      description: Usage type of the credit.
      enum:
      - CREDIT_USAGE_TYPE_UNKNOWN
      - COMPANY
      - PERSONAL
    CustomFieldId:
      type: object
      title: CustomFieldId
      description: The custom field type and the id associated with it.
      required:
      - type
      - externalId
      properties:
        type:
          $ref: '#/components/schemas/CustomFieldType'
        externalId:
          type: string
          description: Meeting id or budget id based on custom field type.
    CustomFieldPrefilledResponse:
      type: object
      title: CustomFieldPrefilledResponse
      description: Custom field prefilled responses.
      required:
      - fieldId
      - armId
      - selectedOptions
      properties:
        fieldId:
          type: string
          format: uuid
          description: Custom field id
        fieldName:
          type: string
          description: Name of the custom field
        armId:
          type: string
          format: uuid
          description: Arm id which is applicable
        readOnly:
          type: boolean
          description: Indicates whether the user can change the pre-selected options.
          default: false
          example: true
        hidden:
          type: boolean
          description: Whether this code will be hidden to the user.
          example: true
          default: false
        selectedOptions:
          type: array
          description: The list of options that are selected by user or auto populated.
          items:
            $ref: '#/components/schemas/CustomFieldSelectedOption'
    CustomFieldResponse:
      title: CustomFieldResponse
      type: object
      deprecated: true
      description: Custom field responses as selected by the event coordinator.
      required:
      - customFieldId
      - travelerAccess
      properties:
        customFieldId:
          type: string
          format: uuid
          description: The unique identifier for the custom field.
          example: f49d00fe-1eda-4304-ba79-a980f565281d
        responseItems:
          type: array
          description: Responses selected by the event coordinator.
          items:
            $ref: '#/components/schemas/CustomFieldResponseItem'
        travelerAccess:
          description: Traveler access for the given custom field.
          $ref: '#/components/schemas/CustomFieldTravelerAccess'
    CustomFieldResponseItem:
      type: object
      title: CustomFieldResponseItem
      description: The response selected for the given custom field.
      required:
      - response
      properties:
        response:
          type: string
          description: The response of the custom field. It can be a text input from
            the user or a custom field code if the input is from a radio button or
            checkbox.
          example: Leisure travel
        additionalInput:
          type: string
          description: This contains additional input to the above response. It will
            only be used for checkbox with percentage and will contain the percentage
            value.
          example: '20'
    CustomFieldSelectedOption:
      type: object
      title: CustomFieldSelectedOption
      description: Selected response for the custom field.
      required:
      - name
      properties:
        name:
          type: string
          description: Value of the selection
        description:
          type: string
          description: Description of the selection
        additionalUserInput:
          type: string
          description: Additional user input
        additionalInfos:
          type: array
          description: Actual values of the additional infos
          items:
            type: string
        additionalInfoConfigs:
          type: array
          description: Additional info configs for the selected option
          items:
            $ref: '#/components/schemas/AdditionalInfo'
    CustomFieldTravelerAccess:
      title: CustomFieldTravelerAccess
      description: Traveler access for a custom field
      type: string
      enum:
      - HIDDEN
      - READ_ACCESS
      - WRITE_ACCESS
      example: HIDDEN
    CustomFieldType:
      type: string
      description: The type of custom field.
      enum:
      - QUESTION
      - MEETING
      - BUDGET
      - BREX_TOKEN
      default: QUESTION
    CustomPaymentMethodDescriptor:
      type: object
      title: CustomPaymentMethodDescriptor
      description: Metadata for Custom Payment Method payment source.
      oneOf:
      - $ref: '#/components/schemas/BrexBudgetWrapper'
      - $ref: '#/components/schemas/AmadeusCheckoutWrapper'
      - $ref: '#/components/schemas/QantasTravelFundWrapper'
      - $ref: '#/components/schemas/DpanWrapper'
      - $ref: '#/components/schemas/VirtualPaymentMetadataWrapper'
    CustomPaymentMethodDescriptorWrapper:
      type: object
      title: CustomPaymentMethodDescriptorWrapper
      description: Wrapper for CustomPaymentMethodDescriptor
      properties:
        customPaymentMethodDescriptor:
          $ref: '#/components/schemas/CustomPaymentMethodDescriptor'
    DateModel:
      title: Date
      description: Date in ISO 8601 standard.
      type: object
      required:
      - iso8601
      properties:
        iso8601:
          type: string
          pattern: ^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$
          example: '2017-07-21'
    DateTimeLocal:
      title: DateTimeLocal
      description: Local date and time in ISO 8601 format.
      type: object
      required:
      - iso8601
      properties:
        iso8601:
          type: string
          pattern: ^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$
          example: 2017-07-21T17:32
    DateTimeOffset:
      title: DateTimeOffset
      description: ISO8601 UTC Date Time
      type: object
      required:
      - iso8601
      properties:
        iso8601:
          type: string
          pattern: ^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?(Z|([+-](0[0-9]|1[0-4]):([0-5][0-9])))$
          example: 2017-07-21T17:32Z
    DateTimeRange:
      type: object
      properties:
        min:
          $ref: '#/components/schemas/DateTimeLocal'
          description: Minimum value - inclusive.
        max:
          $ref: '#/components/schemas/DateTimeLocal'
          description: Maximum value - inclusive.
    DelayedInvoicingDescriptor:
      type: object
      title: DelayedInvoicingDescriptor
      description: Descriptor for delayed invoicing.
      required:
      - label
      properties:
        label:
          type: string
          description: Label for delayed invoicing payment method
          example: Delayed Invoicing for India LE
    DelayedInvoicingDescriptorWrapper:
      type: object
      title: DelayedInvoicingDescriptorWrapper
      description: Wrapper for DelayedInvoicing.
      properties:
        delayedInvoicingDescriptor:
          $ref: '#/components/schemas/DelayedInvoicingDescriptor'
    DeleteEventRequest:
      title: DeleteEventRequest
      type: object
      description: Event details for publishing.
      required:
      - id
      properties:
        id:
          type: string
          description: Unique identifier for the event.
          example: '56789012'
        type:
          $ref: '#/components/schemas/EventType'
    DeleteEventResponse:
      title: DeleteEventResponse
      type: object
      description: Event deletion response details.
      required:
      - id
      - status
      properties:
        id:
          type: string
          description: Unique identifier for the event.
          example: '56789012'
        status:
          $ref: '#/components/schemas/OperationStatus'
        message:
          type: string
          description: Details about success or failure reason.
          example: Deleted successfully
    DepartmentFilterWrapper:
      type: object
      title: DepartmentFilterWrapper
      description: Wrapper for DepartmentFilter
      properties:
        departmentFilter:
          $ref: '#/components/schemas/PaymentSourceDepartmentFilter'
    DepartmentId:
      type: object
      title: DepartmentId
      description: Department id.
      required:
      - id
      properties:
        id:
          type: string
          example: 631ccbcf-9414-5fe0-c234-b324dfbe7422
    DepartmentOverride:
      title: DepartmentOverride
      type: object
      description: Department override for the event
      required:
      - behavior
      properties:
        behavior:
          $ref: '#/components/schemas/OverrideBehavior'
        departmentRef:
          $ref: '#/components/schemas/Reference'
    Dimensions:
      type: object
      title: Dimensions
      description: Image dimensions ie width and height.
      properties:
        height:
          type: integer
          format: int32
          example: 120
        width:
          type: integer
          format: int32
          example: 240
    DirectBilling:
      type: object
      title: DirectBilling
      description: Descriptor for direct billing details.
      properties:
        directBillingCode:
          type: string
          description: Direct billing code provided by the vendor.
        corporateDiscountCode:
          type: string
          deprecated: true
          description: Corporate discount code provided by the vendor.
        label:
          type: string
          description: Label for the Direct Billing Payment method.
    DirectBillingWrapper:
      type: object
      title: DirectBillingWrapper
      description: Wrapper for DirectBilling
      properties:
        directBilling:
          $ref: '#/components/schemas/DirectBilling'
    Document:
      type: object
      title: Document
      description: Document details.
      required:
      - url
      - documentId
      - documentMetadata
      properties:
        url:
          type: string
          description: S3 location of the document.
          example: https://s3.amazonaws.com/bucket-name/folder-name/file-name
        documentId:
          type: string
          format: uuid
          description: Unique identifier of the document.
          example: f49d00fe-1eda-4304-ba79-a980f565281d
        documentMetadata:
          $ref: '#/components/schemas/DocumentMetadata'
    DocumentMetadata:
      title: DocumentMetadata
      description: Metadata related to document.
      type: object
      required:
      - documentType
      - entityType
      - entityId
      - entityMetadata
      - name
      properties:
        documentType:
          $ref: '#/components/schemas/DocumentType'
        entityType:
          $ref: '#/components/schemas/EntityType'
        entityId:
          type: string
          description: Entity Id for the given entity type.
          example: '123124'
        entityMetadata:
          $ref: '#/components/schemas/EntityMetadata'
        name:
          type: string
          description: Document name.
          example: BoardingPass.pdf
    DocumentType:
      title: DocumentType
      description: Document type.
      type: string
      enum:
      - BOARDING_PASS
      - CONFIRMATION
      - INVOICE
      - VISA
      - MISCELLANEOUS
      - OTHERS
      - TASK_PROCESSOR
      - EVENT_COVER_IMAGE
      - LOGO_IMAGE
      example: VISA
      x-ignoreBreakingChanges:
      - DocumentType->LOGO_IMAGE
    DpanMetadata:
      type: object
      title: DpanMetadata
      required:
      - label
      description: Metadata for DPAN payment source.
      properties:
        label:
          type: string
          example: AMEX-DPAN
          description: Label for DPAN payment source.
    DpanWrapper:
      type: object
      title: DpanWrapper
      description: Wrapper for Dpan Payment Source.
      required:
      - dpanMetadata
      properties:
        dpanMetadata:
          $ref: '#/components/schemas/DpanMetadata'
    EmailBucketType:
      type: string
      title: EmailBucketType
      description: Email bucket type.
      enum:
      - BOOKING_CONFIRMATIONS
      - BOOKING_CHANGES
      - FLIGHT_UPDATES
      - APPROVAL_REQUESTS
      - APPROVAL_UPDATES
      - REMINDERS
      example: BOOKING_CONFIRMATIONS
    EntityId:
      type: object
      description: Identifier of an object
      required:
      - id
      properties:
        id:
          type: string
          format: uuid
    EntityMetadata:
      title: EntityMetadata
      description: Metadata for associated entity of document.
      type: object
      oneOf:
      - $ref: '#/components/schemas/PnrMetadataWrapper'
      - $ref: '#/components/schemas/EventMetadataWrapper'
    EntityNonUUIDId:
      type: object
      description: Identifier of an non uuid object
      properties:
        id:
          type: string
    EntityType:
      title: EntityType
      description: Entity type against which the document is to uploaded.
      type: string
      enum:
      - TRIP
      - PNR
      - COMPANY
      - AIR_ITINERARY
      - EVENT
      - LOCATION_IMAGE
      - TICKETING_ERROR
      example: PNR
    ErrorParameter:
      type: object
      title: ErrorParameter
      description: Error parameter
      properties:
        name:
          type: string
          description: Parameter name
        value:
          type: string
          description: Parameter value
    ErrorResponse:
      type: object
      properties:
        debugIdentifier:
          type: string
          description: Link to debug the error internally.
        errorMessages:
          type: array
          items:
            type: object
            properties:
              errorCode:
                type: string
                description: Error code to identify the specific errors.
              message:
                type: string
                description: Message containing details of error.
              errorParameters:
                type: array
                description: Error message parameters.
                items:
                  $ref: '#/components/schemas/ErrorParameter'
              errorDetail:
                type: string
                description: More details about the error.
    Event:
      title: Event
      description: Event details
      type: object
      x-ignoreBreakingChanges:
      - EventApi.yaml->CustomFieldV3Response
      - Event->customFieldV3Responses
      properties:
        id:
          type: string
          description: Event ID
        name:
          type: string
          description: Name of the event
        type:
          description: Event Type
          $ref: '#/components/schemas/EventType'
        description:
          type: string
          description: Description of the event
        startDateTime:
          $ref: '#/components/schemas/DateTimeLocal'
        endDateTime:
          $ref: '#/components/schemas/DateTimeLocal'
        location:
          $ref: '#/components/schemas/EventLocation'
        group:
          $ref: '#/components/schemas/EventGroup'
        status:
          $ref: '#/components/schemas/EventStatus'
        companyId:
          $ref: '#/components/schemas/EntityId'
        runningStatus:
          $ref: '#/components/schemas/EventRunningStatus'
        paymentSources:
          type: array
          description: List of payment sources
          items:
            $ref: '#/components/schemas/PaymentSourceInfo'
        numTravelers:
          type: integer
          description: Number of active travelers in the event
          example: 1
        customFieldResponses:
          deprecated: true
          type: array
          description: Preselected custom field responses for the event.
          items:
            $ref: '#/components/schemas/CustomFieldResponse'
        customFieldV3Responses:
          type: array
          description: List of custom field responses for an event.
          items:
            $ref: '#/components/schemas/CustomFieldPrefilledResponse'
        externalId:
          type: string
          description: External Id for the event
          example: qwert123
        policyId:
          type: string
          format: uuid
          description: Policy associated with the event.
          example: f7b3b3b3-7b3b-4b3b-8b3b-3b3b3b3b3b3b
        parentEventId:
          type: string
          description: Optional parent event's id.
          example: '56789012'
        isDetached:
          type: boolean
          description: Whether the event is detached from the parent event or not.
          example: true
    EventAllowedBookingType:
      title: EventAllowedBookingType
      description: Allowed booking type for the event
      type: string
      enum:
      - AIR
      - HOTEL
      - CAR
      - RAIL
    EventBookingGuideline:
      title: EventBookingGuideline
      description: Booking details allowed for the event
      type: object
      oneOf:
      - $ref: '#/components/schemas/AirBookingGuidelineWrapper'
      - $ref: '#/components/schemas/HotelBookingGuidelineWrapper'
      - $ref: '#/components/schemas/CarBookingGuidelineWrapper'
      - $ref: '#/components/schemas/RailBookingGuidelineWrapper'
      properties:
        numGuestsAllowed:
          type: integer
          description: Number of guests allowed to be booked for this booking
          example: 1
    EventBookingStyle:
      title: EventBookingStyle
      description: The booking style for the event's participants.
      type: string
      enum:
      - SELF_SERVE
      - AGENT_MANAGED
      example: SELF_SERVE
    EventBookingWindow:
      title: EventBookingWindow
      description: Event booking window
      type: object
      required:
      - startDateTime
      - endDateTime
      properties:
        startDateTime:
          $ref: '#/components/schemas/DateTimeLocal'
        endDateTime:
          $ref: '#/components/schemas/DateTimeLocal'
    EventCompanyFilter:
      type: object
      title: EventCompanyFilter
      description: Event company filter details
      properties:
        companyId:
          description: Company id
          $ref: '#/components/schemas/CompanyId'
    EventCompanyFilterWrapper:
      type: object
      title: EventCompanyFilterWrapper
      description: Wrapper for EventCompanyFilter
      properties:
        companyFilter:
          $ref: '#/components/schemas/EventCompanyFilter'
    EventCustomFieldResponsesRequest:
      title: EventCustomFieldResponsesRequest
      type: object
      description: Request for updating custom field responses for an event
      x-ignoreBreakingChanges:
      - EventApi.yaml->CustomFieldV3Response
      - EventCustomFieldResponsesRequest->customFieldV3Responses
      properties:
        eventCustomFieldResponses:
          deprecated: true
          type: array
          description: List of custom field responses for an event.
          items:
            $ref: '#/components/schemas/CustomFieldResponse'
          x-optionalFrom: '2025-05-06'
        customFieldV3Responses:
          type: array
          description: List of custom field responses for an event.
          items:
            $ref: '#/components/schemas/CustomFieldPrefilledResponse'
    EventDateRangeFilter:
      type: object
      title: EventDateRangeFilter
      description: Event date range filter
      properties:
        eventDateRange:
          description: Event date range
          $ref: '#/components/schemas/DateTimeRange'
    EventDateRangeFilterWrapper:
      type: object
      title: EventDateRangeFilterWrapper
      description: Wrapper for EventDateRangeFilter
      properties:
        eventDateRangeFilter:
          $ref: '#/components/schemas/EventDateRangeFilter'
    EventEntityMetadata:
      title: EventEntityMetadata
      description: Metadata when document is associated to an event.
      type: object
      properties:
        address:
          $ref: '#/components/schemas/PostalAddress'
    EventFilter:
      title: EventFilter
      type: object
      description: Event filter
      oneOf:
      - $ref: '#/components/schemas/EventUserFilterWrapper'
      - $ref: '#/components/schemas/EventStatusFilterWrapper'
      - $ref: '#/components/schemas/EventNameFilterWrapper'
      - $ref: '#/components/schemas/EventDateRangeFilterWrapper'
      - $ref: '#/components/schemas/EventCompanyFilterWrapper'
      - $ref: '#/components/schemas/EventParentFilterWrapper'
      - $ref: '#/components/schemas/EventTypeFilterWrapper'
      - $ref: '#/components/schemas/EventIdFilterWrapper'
    EventGroup:
      title: EventGroup
      description: Group of users for the event
      type: object
      properties:
        id:
          type: string
          description: Event Group ID
          format: uuid
          example: f49d00fe-1eda-4304-ba79-a980f565281d
        name:
          type: string
          description: Name of the event group
        description:
          type: string
          description: Description of the event group
        bookingGuidelines:
          description: A list of BookingGuideline for event group
          type: array
          items:
            $ref: '#/components/schemas/EventBookingGuideline'
        metadata:
          $ref: '#/components/schemas/EventGroupMetadata'
        group:
          $ref: '#/components/schemas/UserTripGroup'
        createdBy:
          $ref: '#/components/schemas/UserId'
        allowedBookingTypes:
          type: array
          description: Allowed booking types for the event group
          items:
            $ref: '#/components/schemas/EventAllowedBookingType'
        eventUserRsvpList:
          type: array
          description: List of user rsvp responses for the event group
          items:
            $ref: '#/components/schemas/EventUserRsvp'
        allowedPaymentConfig:
          $ref: '#/components/schemas/AllowedPaymentConfig'
    EventGroupMetadata:
      title: EventGroupMetadata
      description: Metadata for event group
      type: object
      properties:
        contacts:
          type: array
          description: Event group contacts
          items:
            $ref: '#/components/schemas/UserId'
        documents:
          type: array
          description: Documents attached for an event group
          items:
            $ref: '#/components/schemas/Document'
        bookingStyle:
          description: Whether the event bookings will be self served by the traveler
            or would be arranged by an agent for the traveler.
          $ref: '#/components/schemas/EventBookingStyle'
        referenceUsers:
          type: array
          description: Event group reference user ids
          items:
            $ref: '#/components/schemas/UserId'
        internalNotes:
          type: string
          description: Internal notes of the event
        travelerTypes:
          type: array
          description: Allowed traveler types for the event
          items:
            $ref: '#/components/schemas/Persona'
        allowTravelersPolicy:
          $ref: '#/components/schemas/AllowTravelersPolicy'
        emailPreferences:
          $ref: '#/components/schemas/BucketEmailPreferences'
        allowTravelersToRsvp:
          $ref: '#/components/schemas/AllowTravelersToRsvp'
        tripOverrides:
          $ref: '#/components/schemas/TripOverrides'
    EventIdFilter:
      type: object
      title: EventIdFilter
      description: Event ID filter details
      required:
      - eventIdList
      properties:
        eventIdList:
          type: array
          description: A list of event IDs.
          items:
            type: string
    EventIdFilterWrapper:
      type: object
      title: EventIdFilterWrapper
      description: Wrapper for EventIdFilter
      properties:
        eventIdFilter:
          $ref: '#/components/schemas/EventIdFilter'
    EventJobItemStatus:
      title: EventItemJobStatus
      description: Event job item status enum.
      type: string
      enum:
      - SUCCESS
      - FAILED
      example: SUCCESS
    EventJobStatus:
      title: EventJobStatus
      description: Event job status enum.
      type: string
      enum:
      - IN PROGRESS
      - COMPLETED
      example: COMPLETED
    EventJobType:
      title: EventJobType
      description: Event job type enum.
      type: string
      enum:
      - PUBLISH
      - MODIFY
      - CANCEL
      example: PUBLISH
    EventLocation:
      title: EventLocation
      description: Location for an event
      type: object
      properties:
        address:
          $ref: '#/components/schemas/PostalAddress'
        coordinates:
          $ref: '#/components/schemas/Latlng'
    EventMetadataWrapper:
      type: object
      title: EventMetadataWrapper
      properties:
        eventMetadata:
          $ref: '#/components/schemas/EventEntityMetadata'
    EventNameFilter:
      type: string
      title: EventNameFilter
      description: Event name filter
      example: Jaipur Offsite
    EventNameFilterWrapper:
      type: object
      title: EventNameFilterWrapper
      description: Wrapper for EventNameFilter
      properties:
        eventNameFilter:
          $ref: '#/components/schemas/EventNameFilter'
    EventParentFilter:
      type: object
      title: EventParentFilter
      description: Event parent filter details
      required:
      - parentEventId
      properties:
        parentEventId:
          type: string
          description: Parent event's id.
          example: '56789012'
    EventParentFilterWrapper:
      type: object
      title: EventParentFilterWrapper
      description: Wrapper for EventParentFilter
      properties:
        eventParentFilter:
          $ref: '#/components/schemas/EventParentFilter'
    EventRsvpResponse:
      title: EventRsvpResponse
      type: object
      description: Event rsvp response
      properties:
        air:
          $ref: '#/components/schemas/AirRsvpResponse'
        hotel:
          $ref: '#/components/schemas/HotelRsvpResponse'
        car:
          $ref: '#/components/schemas/CarRsvpResponse'
        rail:
          $ref: '#/components/schemas/RailRsvpResponse'
    EventRsvpState:
      title: EventRsvpState
      description: Allowed event rsvp states for a user
      type: string
      enum:
      - ADDED
      - INVITED
      - INVITE_ACCEPTED
      - INVITE_DECLINED
      - REMOVED
    EventRunningStatus:
      title: EventRunningStatus
      description: Running status of an event
      type: string
      enum:
      - IN_PROGRESS
      - UPCOMING
      - COMPLETED
      example: UPCOMING
    EventStatus:
      title: EventStatus
      description: Status of an event
      type: string
      enum:
      - DRAFT
      - PUBLISH
      - CANCELLED
      example: DRAFT
    EventStatusFilter:
      type: array
      title: EventStatusFilter
      description: Event status filter
      items:
        $ref: '#/components/schemas/EventStatus'
    EventStatusFilterWrapper:
      type: object
      title: EventStatusFilterWrapper
      description: Wrapper for EventStatusFilter
      properties:
        eventStatusFilter:
          $ref: '#/components/schemas/EventStatusFilter'
    EventTemplateFilterWrapper:
      type: object
      title: EventTemplateFilterWrapper
      description: Wrapper for EventTemplateFilter
      properties:
        eventTemplateFilter:
          $ref: '#/components/schemas/PaymentEventFilter'
    EventTravelerFilter:
      title: EventTravelerFilter
      type: object
      description: Event traveler filter
      discriminator:
        propertyName: type
        mapping:
          TRAVELER_SEARCH_FILTER: '#/components/schemas/TravelerSearchFilter'
          INVITATION_STATUS_FILTER: '#/components/schemas/InvitationStatusFilter'
          AIR_BOOKING_STATUS_FILTER: '#/components/schemas/AirBookingStatusFilter'
          RAIL_BOOKING_STATUS_FILTER: '#/components/schemas/RailBookingStatusFilter'
          CAR_BOOKING_STATUS_FILTER: '#/components/schemas/CarBookingStatusFilter'
          HOTEL_BOOKING_STATUS_FILTER: '#/components/schemas/HotelBookingStatusFilter'
      oneOf:
      - $ref: '#/components/schemas/TravelerSearchFilter'
      - $ref: '#/components/schemas/InvitationStatusFilter'
      - $ref: '#/components/schemas/AirBookingStatusFilter'
      - $ref: '#/components/schemas/RailBookingStatusFilter'
      - $ref: '#/components/schemas/CarBookingStatusFilter'
      - $ref: '#/components/schemas/HotelBookingStatusFilter'
    EventTravelersRequest:
      type: object
      description: List of event travelers request
      properties:
        offset:
          type: integer
          description: Indicates from where in the list of travelers the server should
            start.
          default: 0
          minimum: 0
          example: 2
        limit:
          type: integer
          description: Maximum number of results to be fetched for the query.
          default: 10
          minimum: 0
          maximum: 100
          example: 2
        type:
          $ref: '#/components/schemas/EventType'
        filters:
          type: array
          description: Filters for event travelers
          items:
            $ref: '#/components/schemas/EventTravelerFilter'
    EventTravelersResponse:
      title: EventTravelersResponse
      description: List of travelers response
      type: object
      properties:
        travelerEventInfos:
          type: array
          description: List of user rsvp responses & trip details
          items:
            $ref: '#/components/schemas/TravelerEventInfo'
        userGroupInfo:
          description: Details about group's users
          $ref: '#/components/schemas/UserGroupInfo'
        paginationParams:
          description: Total number of active travelers in the event
          $ref: '#/components/schemas/OffsetBasedPaginationResponseParams'
    EventType:
      title: EventType
      description: Type of an event
      type: string
      enum:
      - GENERIC
      - PROGRAM
      - PROGRAM_SESSION
      - PROGRAM_TRIP
      example: GENERIC
    EventTypeFilter:
      type: object
      title: EventTypeFilter
      description: Event type filter details
      properties:
        types:
          type: array
          title: EventType
          items:
            $ref: '#/components/schemas/EventType'
    EventTypeFilterWrapper:
      type: object
      title: EventTypeFilterWrapper
      description: Wrapper for EventTypeFilter
      properties:
        eventTypeFilter:
          $ref: '#/components/schemas/EventTypeFilter'
    EventUserFilter:
      type: object
      title: EventUserFilter
      description: Event user filter details
      properties:
        userId:
          description: User id
          $ref: '#/components/schemas/UserId'
    EventUserFilterWrapper:
      type: object
      title: EventUserFilterWrapper
      description: Wrapper for EventUserFilter
      properties:
        userFilter:
          $ref: '#/components/schemas/EventUserFilter'
    EventUserInfo:
      title: EventUserInfo
      description: User information for the event
      type: object
      required:
      - userId
      - name
      properties:
        userId:
          $ref: '#/components/schemas/UserId'
        email:
          type: string
          description: Business email of the user
          example: <EMAIL>
        name:
          $ref: '#/components/schemas/Name'
    EventUserRsvp:
      title: EventUserRsvp
      type: object
      description: User rsvp for the event
      properties:
        userId:
          $ref: '#/components/schemas/UserId'
        eventRsvpState:
          $ref: '#/components/schemas/EventRsvpState'
        eventRsvpResponse:
          $ref: '#/components/schemas/EventRsvpResponse'
        invitedAt:
          $ref: '#/components/schemas/DateTimeOffset'
        airBookingStatus:
          $ref: '#/components/schemas/BookingStatusType'
        railBookingStatus:
          $ref: '#/components/schemas/BookingStatusType'
        carBookingStatus:
          $ref: '#/components/schemas/BookingStatusType'
        hotelBookingStatus:
          $ref: '#/components/schemas/BookingStatusType'
    Expiry:
      title: Expiry
      type: object
      description: Contains the expiry of a Card.
      required:
      - expiryMonth
      - expiryYear
      properties:
        expiryMonth:
          type: integer
          format: int32
          minimum: 1
          maximum: 12
          description: Expiry month
          example: 1
        expiryYear:
          type: integer
          format: int32
          description: Expiry year
          minimum: 2000
          example: 2010
    ExpiryWrapper:
      type: object
      title: ExpiryWrapper
      properties:
        expiry:
          $ref: '#/components/schemas/Expiry'
    Expression:
      type: object
      title: Expression
      description: 'Expression consisting of variables and constants which needs to
        be sent along with custom

        field response.

        '
      required:
      - type
      - formatExpression
      properties:
        type:
          type: string
          example: EXPRESSION
          default: EXPRESSION
        formatExpression:
          type: string
          description: "The expression must be of format : `${expression}`.The expression\
            \ can consist of a \ncombination of variables and mathematical operations.\n\
            \ Variable names must begin with `var` followed by a number, which is\
            \ used to identify \nthe variable in the variables list. The numbering\
            \ should follow a 1-based index.\n  To define mathematical operations,\
            \ the operation name should follow the format\n`math.<math_op>(arg1, arg2)`.\
            \ Both `arg1` and `arg2` can be variables or constants. \nThe supported\
            \ math operations (math_op) include: `add, mul, div, sub, min,\nand max`.\
            \ All keywords, such as `<math_op>, math, and var` must be written in\
            \ lowercase.\n"
          example: 'Result:  ${math.mul(var1,5)}  ${var2}'
        variables:
          type: array
          description: Reference names of the variables present in the expression.
          items:
            $ref: '#/components/schemas/VariableName'
    FlightMetadata:
      title: FlightMetadata
      description: Flight metadata against which document is associated.
      type: object
      properties:
        flightId:
          type: string
          description: Unique identifier of the flight.
          example: CgNERU4SA1NGTxoKNTQ1NzI5ODcxMQ
    FlightMetadataWrapper:
      type: object
      title: FlightMetadataWrapper
      properties:
        flightMetadata:
          $ref: '#/components/schemas/FlightMetadata'
    GetEventResponse:
      title: GetEventResponse
      description: Get Event response object
      type: object
      properties:
        event:
          $ref: '#/components/schemas/Event'
    GetEventSummariesRequest:
      title: GetEventSummariesRequest
      description: Request to get Event summaries
      type: object
      properties:
        userId:
          description: User id
          $ref: '#/components/schemas/UserId'
        companyId:
          description: Company id
          $ref: '#/components/schemas/CompanyId'
        tripIds:
          type: array
          description: List of Spotnana trip IDs
          items:
            type: string
            example: '6926658168'
    GetEventSummariesResponse:
      title: GetEventSummariesResponse
      type: object
      description: Response to return Event summaries
      properties:
        tripEventSummaries:
          type: array
          description: List of Event summaries for trips
          items:
            $ref: '#/components/schemas/TripEventSummary'
    GetEventsPaymentSourcesResponse:
      type: object
      title: GetEventsPaymentSourcesResponse
      description: Payment sources for events
      required:
      - paymentSources
      properties:
        paymentSources:
          type: array
          description: List of payment sources
          items:
            $ref: '#/components/schemas/PaymentSourceInfo'
    HotelAmenities:
      title: HotelAmenities
      type: object
      description: Type of hotel amenity
      properties:
        type:
          $ref: '#/components/schemas/HotelAmenityType'
        additionalInfo:
          type: string
          description: Amenity description
          example: Complimentary in-room coffee or tea
        complimentary:
          type: boolean
          description: Is Amenity complimentary
          example: true
    HotelAmenityType:
      title: HotelAmenityType
      type: string
      description: Type of hotel amenity.
      enum:
      - TWENTY_FOUR_HOUR_FRONT_DESK
      - TWENTY_FOUR_HOUR_ROOM_SERVICE
      - TWENTY_FOUR_HOUR_SECURITY
      - ADJOINING_ROOMS
      - AIR_CONDITIONING
      - AIRLINE_DESK
      - ATM_CASH_MACHINE
      - BABY_SITTING
      - BBQ_PICNIC_AREA
      - BILINGUAL_STAFF
      - BOOKSTORE
      - BOUTIQUES_STORES
      - BRAILED_ELEVATORS
      - BUSINESS_LIBRARY
      - CAR_RENTAL_DESK
      - CASINO
      - CHECK_CASHING_POLICY
      - CHECK_IN_KIOSK
      - COCKTAIL_LOUNGE
      - COFFEE_SHOP
      - COIN_OPERATED_LAUNDRY
      - CONCIERGE_DESK
      - CONCIERGE_FLOOR
      - CONFERENCE_FACILITIES
      - COURTYARD
      - CURRENCY_EXCHANGE
      - DESK_WITH_ELECTRICAL_OUTLET
      - DOCTOR_ON_CALL
      - DOOR_MAN
      - DRIVING_RANGE
      - DRUGSTORE_PHARMACY
      - DUTY_FREE_SHOP
      - ELEVATORS
      - EXECUTIVE_FLOOR
      - EXERCISE_GYM
      - EXPRESS_CHECK_IN
      - EXPRESS_CHECK_OUT
      - FAMILY_PLAN
      - FLORIST
      - FOLIOS
      - FREE_AIRPORT_SHUTTLE
      - FREE_PARKING
      - FREE_TRANSPORTATION
      - GAME_ROOM
      - GIFT_NEWS_STAND
      - HAIRDRESSER_BARBER
      - ACCESSIBLE_FACILITIES
      - HEALTH_CLUB
      - HEATED_POOL
      - HOUSEKEEPING_DAILY
      - HOUSEKEEPING_WEEKLY
      - ICE_MACHINE
      - INDOOR_PARKING
      - INDOOR_POOL
      - JACUZZI
      - JOGGING_TRACK
      - KENNELS
      - LAUNDRY_VALET_SERVICE
      - LIQUOR_STORE
      - LIVE_ENTERTAINMENT
      - MASSAGE_SERVICES
      - NIGHTCLUB
      - OFF_SITE_PARKING
      - ON_SITE_PARKING
      - OUTDOOR_PARKING
      - OUTDOOR_POOL
      - PACKAGE_PARCEL_SERVICES
      - PARKING
      - PHOTOCOPY_CENTER
      - PLAYGROUND
      - POOL
      - POOLSIDE_SNACK_BAR
      - PUBLIC_ADDRESS_SYSTEM
      - RAMP_ACCESS
      - RECREATIONAL_VEHICLE_PARKING
      - RESTAURANT
      - ROOM_SERVICE
      - SAFE_DEPOSIT_BOX
      - SAUNA
      - SECURITY
      - SHOE_SHINE_STAND
      - SHOPPING_MALL
      - SOLARIUM
      - SPA
      - SPORTS_BAR
      - STEAM_BATH
      - STORAGE_SPACE
      - SUNDRY_CONVENIENCE_STORE
      - TECHNICAL_CONCIERGE
      - THEATRE_DESK
      - TOUR_SIGHTSEEING_DESK
      - TRANSLATION_SERVICES
      - TRAVEL_AGENCY
      - TRUCK_PARKING
      - VALET_CLEANING
      - DRY_CLEANING
      - VALET_PARKING
      - VENDING_MACHINES
      - VIDEO_TAPES
      - WAKEUP_SERVICE
      - WHEELCHAIR_ACCESS
      - WHIRLPOOL
      - MULTILINGUAL_STAFF
      - WEDDING_SERVICES
      - BANQUET_FACILITIES
      - BELL_STAFF_PORTER
      - BEAUTY_SHOP_SALON
      - COMPLIMENTARY_SELF_SERVICE_LAUNDRY
      - DIRECT_DIAL_TELEPHONE
      - FEMALE_TRAVELER_ROOM_FLOOR
      - PHARMACY
      - STABLES
      - ONE_TWENTY_AC
      - ONE_TWENTY_DC
      - TWO_TWENTY_AC
      - ACCESSIBLE_PARKING
      - TWO_TWENTY_DC
      - BARBEQUE_GRILLS
      - WOMENS_CLOTHING
      - MENS_CLOTHING
      - CHILDRENS_CLOTHING
      - SHOPS_AND_COMMERCIAL_SERVICES
      - VIDEO_GAMES
      - SPORTS_BAR_OPEN_FOR_LUNCH
      - SPORTS_BAR_OPEN_FOR_DINNER
      - ROOM_SERVICE_FULL_MENU
      - ROOM_SERVICE_LIMITED_MENU
      - ROOM_SERVICE_LIMITED_HOURS
      - VALET_SAME_DAY_DRY_CLEANING
      - BODY_SCRUB
      - BODY_WRAP
      - PUBLIC_AREA_AIR_CONDITIONED
      - EFOLIO_AVAILABLE_TO_COMPANY
      - INDIVIDUAL_EFOLIO_AVAILABLE
      - VIDEO_REVIEW_BILLING
      - BUTLER_SERVICE
      - COMPLIMENTARY_IN_ROOM_COFFEE_OR_TEA
      - COMPLIMENTARY_BUFFET_BREAKFAST
      - COMPLIMENTARY_COCKTAILS
      - COMPLIMENTARY_COFFEE_IN_LOBBY
      - COMPLIMENTARY_CONTINENTAL_BREAKFAST
      - COMPLIMENTARY_FULL_AMERICAN_BREAKFAST
      - DINNER_DELIVERY_SERVICE_FROM_LOCAL_RESTAURANT
      - COMPLIMENTARY_NEWSPAPER_DELIVERED_TO_ROOM
      - COMPLIMENTARY_NEWSPAPER_IN_LOBBY
      - COMPLIMENTARY_SHOESHINE
      - EVENING_RECEPTION
      - FRONT_DESK
      - GROCERY_SHOPPING_SERVICE_AVAILABLE
      - HALAL_FOOD_AVAILABLE
      - KOSHER_FOOD_AVAILABLE
      - LIMOUSINE_SERVICE
      - MANAGERS_RECEPTION
      - MEDICAL_FACILITIES_SERVICE
      - TELEPHONE_JACK_ADAPTOR_AVAILABLE
      - ALL_INCLUSIVE_MEAL_PLAN
      - BUFFET_BREAKFAST
      - COMMUNAL_BAR_AREA
      - CONTINENTAL_BREAKFAST
      - FULL_MEAL_PLAN
      - FULL_AMERICAN_BREAKFAST
      - MEAL_PLAN_AVAILABLE
      - MODIFIED_AMERICAN_MEAL_PLAN
      - FOOD_AND_BEVERAGE_OUTLETS
      - LOUNGES_BARS
      - BARBER_SHOP
      - VIDEO_CHECKOUT
      - ONSITE_LAUNDRY
      - TWENTY_FOUR_HOUR_FOOD_AND_BEVERAGE_KIOSK
      - CONCIERGE_LOUNGE
      - PARKING_FEE_MANAGED_BY_HOTEL
      - TRANSPORTATION
      - BREAKFAST_SERVED_IN_RESTAURANT
      - LUNCH_SERVED_IN_RESTAURANT
      - DINNER_SERVED_IN_RESTAURANT
      - FULL_SERVICE_HOUSEKEEPING
      - LIMITED_SERVICE_HOUSEKEEPING
      - HIGH_SPEED_INTERNET_ACCESS_FOR_LAPTOP_IN_PUBLIC_AREAS
      - WIRELESS_INTERNET_CONNECTION_IN_PUBLIC_AREAS
      - ADDITIONAL_SERVICES_AMENITIES_FACILITIES_ON_PROPERTY
      - TRANSPORTATION_SERVICES_LOCAL_AREA
      - TRANSPORTATION_SERVICES_LOCAL_OFFICE
      - DVD_VIDEO_RENTAL
      - PARKING_LOT
      - PARKING_DECK
      - STREET_SIDE_PARKING
      - COCKTAIL_LOUNGE_WITH_ENTERTAINMENT
      - COCKTAIL_LOUNGE_WITH_LIGHT_FARE
      - MOTORCYCLE_PARKING
      - PHONE_SERVICES
      - BALLROOM
      - BUS_PARKING
      - CHILDRENS_PLAY_AREA
      - CHILDRENS_NURSERY
      - DISCO
      - EARLY_CHECK_IN
      - LOCKER_ROOM
      - NON_SMOKING_ROOMS_GENERIC
      - TRAIN_ACCESS
      - AEROBICS_INSTRUCTION
      - BAGGAGE_HOLD
      - BICYCLE_RENTALS
      - DIETICIAN
      - LATE_CHECK_OUT_AVAILABLE
      - PET_SITTING_SERVICES
      - PRAYER_MATS
      - SPORTS_TRAINER
      - TURNDOWN_SERVICE
      - DVDS_VIDEOS_CHILDREN
      - BANK
      - LOBBY_COFFEE_SERVICE
      - BANKING_SERVICES
      - STAIRWELLS
      - PET_AMENITIES_AVAILABLE
      - EXHIBITION_CONVENTION_FLOOR
      - LONG_TERM_PARKING
      - CHILDREN_NOT_ALLOWED
      - CHILDREN_WELCOME
      - COURTESY_CAR
      - HOTEL_DOES_NOT_PROVIDE_PORNOGRAPHIC_FILMS_TV
      - HOTSPOTS
      - FREE_HIGH_SPEED_INTERNET_CONNECTION
      - INTERNET_SERVICES
      - PETS_ALLOWED
      - GOURMET_HIGHLIGHTS
      - CATERING_SERVICES
      - COMPLIMENTARY_BREAKFAST
      - BUSINESS_CENTER
      - BUSINESS_SERVICES
      - SECURED_PARKING
      - RACQUETBALL
      - SNOW_SPORTS
      - TENNIS_COURT
      - WATER_SPORTS
      - CHILD_PROGRAMS
      - GOLF
      - HORSEBACK_RIDING
      - OCEANFRONT
      - BEACHFRONT
      - HAIR_DRYER
      - IRONING_BOARD
      - HEATED_GUEST_ROOMS
      - TOILET
      - PARLOR
      - VIDEO_GAME_PLAYER
      - THALASSOTHERAPY
      - PRIVATE_DINING_FOR_GROUPS
      - HEARING_IMPAIRED_SERVICES
      - CARRYOUT_BREAKFAST
      - DELUXE_CONTINENTAL_BREAKFAST
      - HOT_CONTINENTAL_BREAKFAST
      - HOT_BREAKFAST
      - PRIVATE_POOL
      - CONNECTING_ROOMS
      - DATA_PORT
      - EXTERIOR_CORRIDORS
      - GULF_VIEW
      - ACCESSIBLE_ROOMS
      - HIGH_SPEED_INTERNET_ACCESS
      - INTERIOR_CORRIDORS
      - HIGH_SPEED_WIRELESS
      - KITCHENETTE
      - PRIVATE_BATH_OR_SHOWER
      - FIRE_SAFETY_COMPLIANT
      - WELCOME_DRINK
      - BOARDING_PASS_PRINT_OUT_AVAILABLE
      - PRINTING_SERVICES_AVAILABLE
      - ALL_PUBLIC_AREAS_NON_SMOKING
      - MEETING_ROOMS
      - MOVIES_IN_ROOM
      - SECRETARIAL_SERVICE
      - SNOW_SKIING
      - WATER_SKIING
      - FAX_SERVICE
      - GREAT_ROOM
      - LOBBY
      - MULTIPLE_PHONE_LINES_BILLED_SEPARATELY
      - UMBRELLAS
      - GAS_STATION
      - GROCERY_STORE
      - TWENTY_FOUR_HOUR_COFFEE_SHOP
      - AIRPORT_SHUTTLE_SERVICE
      - LUGGAGE_SERVICE
      - PIANO_BAR
      - VIP_SECURITY
      - COMPLIMENTARY_WIRELESS_INTERNET
      - CONCIERGE_BREAKFAST
      - SAME_GENDER_FLOOR
      - CHILDREN_PROGRAMS
      - BUILDING_MEETS_LOCAL_STATE_AND_COUNTRY_BUILDING_CODES
      - INTERNET_BROWSER_ON_TV
      - NEWSPAPER
      - PARKING_CONTROLLED_ACCESS_GATES_TO_ENTER_PARKING_AREA
      - HOTEL_SAFE_DEPOSIT_BOX_NOT_ROOM_SAFE_BOX
      - STORAGE_SPACE_AVAILABLE_FEE
      - TYPE_OF_ENTRANCES_TO_GUEST_ROOMS
      - BEVERAGE_COCKTAIL
      - CELL_PHONE_RENTAL
      - COFFEE_TEA
      - EARLY_CHECK_IN_GUARANTEE
      - FOOD_AND_BEVERAGE_DISCOUNT
      - LATE_CHECK_OUT_GUARANTEE
      - ROOM_UPGRADE_CONFIRMED
      - ROOM_UPGRADE_ON_AVAILABILITY
      - SHUTTLE_TO_LOCAL_BUSINESSES
      - SHUTTLE_TO_LOCAL_ATTRACTIONS
      - SOCIAL_HOUR
      - VIDEO_BILLING
      - WELCOME_GIFT
      - HYPOALLERGENIC_ROOMS
      - ROOM_AIR_FILTRATION
      - SMOKE_FREE_PROPERTY
      - WATER_PURIFICATION_SYSTEM_IN_USE
      - POOLSIDE_SERVICE
      - CLOTHING_STORE
      - ELECTRIC_CAR_CHARGING_STATIONS
      - OFFICE_RENTAL
      - PIANO
      - INCOMING_FAX
      - OUTGOING_FAX
      - SEMI_PRIVATE_SPACE
      - LOADING_DOCK
      - BABY_KIT
      - CHILDRENS_BREAKFAST
      - CLOAKROOM_SERVICE
      - COFFEE_LOUNGE
      - EVENTS_TICKET_SERVICE
      - LATE_CHECK_IN
      - LIMITED_PARKING
      - OUTDOOR_SUMMER_BAR_CAFE
      - NO_PARKING_AVAILABLE
      - BEER_GARDEN
      - GARDEN_LOUNGE_BAR
      - SUMMER_TERRACE
      - WINTER_TERRACE
      - ROOF_TERRACE
      - BEACH_BAR
      - HELICOPTER_SERVICE
      - FERRY
      - TAPAS_BAR
      - CAFE_BAR
      - SNACK_BAR
      - GUESTROOM_WIRED_INTERNET
      - GUESTROOM_WIRELESS_INTERNET
      - FITNESS_CENTER
      - ALCOHOLIC_BEVERAGES
      - NON_ALCOHOLIC_BEVERAGES
      - HEALTH_AND_BEAUTY_SERVICES
      - LOCAL_CALLS
      - MINIBAR
      - REFRIGERATOR
      - IN_ROOM_SAFE
      - SMOKING_ROOMS_AVAILBLE
      - MOUNTAIN_VIEW
      - POOL_VIEW
      - BEACH_VIEW
      - OCEAN_VIEW
      - ROOMS_WITH_BALCONY
      - FAMILY_ROOM
      - CRIB_CHARGE
      - ROLLAWAY_ADULT
      - FREE_WIFI_IN_MEETING_ROOMS
      - ECO_FRIENDLY
      - EXTRA_PERSON
      - STAY_SAFE
      - ENHANCED_HYGIENE_CLEANLINESS_PROTOCOLS
      example: ECO_FRIENDLY
    HotelBookingGuideline:
      title: HotelBookingGuideline
      description: Hotel related guidelines
      type: object
      properties:
        allowedHotels:
          description: List of allowed Hotel details for the event
          type: array
          items:
            $ref: '#/components/schemas/HotelInfo'
        checkinBookingWindow:
          $ref: '#/components/schemas/EventBookingWindow'
        checkoutBookingWindow:
          $ref: '#/components/schemas/EventBookingWindow'
        paymentGuidelines:
          $ref: '#/components/schemas/HotelBookingPaymentGuidelines'
          readOnly: true
    HotelBookingGuidelineWrapper:
      type: object
      title: HotelBookingGuidelineWrapper
      description: Wrapper for HotelBookingGuideline
      properties:
        hotelBookingGuideLine:
          $ref: '#/components/schemas/HotelBookingGuideline'
    HotelBookingPaymentGuidelines:
      title: HotelBookingPaymentGuidelines
      description: Hotel booking payment guidelines
      type: object
      properties:
        onlyVpayEnabledBooking:
          type: boolean
          readOnly: true
          description: Whether only Vpay enabled bookings are allowed for the event.
          example: true
    HotelBookingStatusFilter:
      type: object
      title: HotelBookingStatusFilter
      description: Hotel Booking Status Filter
      required:
      - type
      - bookingStatus
      properties:
        type:
          type: string
          description: Type of filter
        bookingStatus:
          type: array
          description: Hotel Booking status
          items:
            $ref: '#/components/schemas/BookingStatusType'
    HotelDescription:
      type: object
      title: HotelDescription
      description: Hotel description
      properties:
        type:
          type: string
          enum:
          - GENERAL
          - ALERTS
          - DINING
          - FACILITIES
          - RECREATION
          - SERVICES
          - ATTRACTIONS
          - CANCELLATION_POLICY
          - DEPOSIT_POLICY
          - DIRECTIONS
          - POLICIES
          - SAFETY
          - TRANSPORTATION
          description: Hotel description type
          example: ''
        value:
          type: string
          description: Hotel description value
          example: ''
    HotelImageCategory:
      type: string
      description: Category of image such as exterior, spa, etc.
      enum:
      - UNKNOWN_CATEGORY
      - EXTERIOR_VIEW
      - LOBBY_VIEW
      - POOL_VIEW
      - RESTAURANT
      - HEALTH_CLUB
      - GUEST_ROOM
      - SUITE
      - MEETING_ROOM
      - BALLROOM
      - GOLF_COURSE
      - BEACH
      - SPA
      - BAR_OR_LOUNGE
      - RECREATIONAL_FACILITY
      - LOGO
      - BASICS
      - MAP
      - PROMOTIONAL
      - HOT_NEWS
      - MISCELLANEOUS
      - GUEST_ROOM_AMENITY
      - PROPERTY_AMENITY
      - BUSINESS_CENTER
    HotelImageSet:
      type: object
      description: Object containing details of images of different for a given category.
      required:
      - category
      - imageGroup
      properties:
        category:
          $ref: '#/components/schemas/HotelImageCategory'
        imageGroup:
          $ref: '#/components/schemas/ImageGroup'
    HotelInfo:
      type: object
      title: HotelInfo
      description: Hotel info
      required:
      - address
      - name
      properties:
        address:
          $ref: '#/components/schemas/PostalAddress'
          description: Address of the hotel.
        chainCode:
          type: string
          description: Chain code of the hotel.
          example: RF
        chainName:
          type: string
          description: Chain name of the hotel.
          example: Red Roof Inns
        coordinates:
          $ref: '#/components/schemas/Latlng'
          description: Coordinates of the hotel.
        email:
          type: string
          description: Email address of the hotel.
          example: <EMAIL>
        hotelId:
          type: string
          description: Hotel id.
          example: '100094780'
        name:
          type: string
          description: Name of the hotel.
          example: San Francisco Airport Red Roof
        phone:
          $ref: '#/components/schemas/PhoneNumber'
          description: Phone number of the hotel.
        starRating:
          type: number
          description: Star rating of the hotel.
          format: double
          example: 3.5
        fax:
          type: array
          items:
            $ref: '#/components/schemas/PhoneNumber'
        masterChainCode:
          type: string
          description: Master chain code of the hotel.
          example: EM
        brandName:
          type: string
          description: Brand name of the hotel.
          example: Marriott Hotel Brands
        amenities:
          type: array
          items:
            $ref: '#/components/schemas/HotelAmenities'
        additionalAmenities:
          type: array
          items:
            type: string
          description: List of amenities provided by the supplier.
          example:
          - Room service
          - Wifi
        imageSets:
          type: array
          items:
            $ref: '#/components/schemas/HotelImageSet'
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/HotelDescription'
        thirdPartyHotelCodes:
          type: array
          items:
            $ref: '#/components/schemas/ThirdPartyHotelCode'
    HotelItinerary:
      type: object
      title: HotelItinerary
      properties:
        hotelItineraryId:
          $ref: '#/components/schemas/HotelItineraryId'
    HotelItineraryId:
      type: object
      title: HotelItineraryId
      description: Hotel itinerary id
      required:
      - priceValidateKey
      properties:
        priceValidateKey:
          type: string
          description: Price Validate key
    HotelRsvpResponse:
      title: HotelRsvpResponse
      type: object
      description: Hotel rsvp response
      properties:
        notNeeded:
          type: boolean
          description: Whether hotel booking is needed by the traveler or not
          example: true
    Image:
      type: object
      title: Image
      description: An image with meta data. Either the `data` or `url` property must
        be supplied to load the image.
      properties:
        data:
          type: string
          format: byte
          example: 6935813e12584abda0e43d71cd2ea260
        dimensions:
          $ref: '#/components/schemas/Dimensions'
        url:
          type: string
          example: https://static.wixstatic.com/media/73f2e2_6935813e12584abda0e43d71cd2ea260~mv2.png/v1/fill/w_630,h_94,al_c,q_85,usm_0.66_1.00_0.01/Spotnana%403x.webp
    ImageGroup:
      type: object
      title: ImageGroup
      description: Contains different sizes of the same image.
      required:
      - images
      properties:
        caption:
          type: string
          description: Caption for the image.
          example: Exterior
        images:
          type: array
          description: List of images.
          items:
            $ref: '#/components/schemas/Image'
    IndividualFilter:
      type: object
      title: IndividualFilter
      description: IndividualFilter details.
      required:
      - ids
      - companyId
      properties:
        ids:
          type: array
          description: List of user id
          items:
            $ref: '#/components/schemas/UserId'
        companyId:
          description: company id
          $ref: '#/components/schemas/CompanyId'
    IndividualFilterWrapper:
      type: object
      title: IndividualFilterWrapper
      description: Wrapper for IndividualFilter
      properties:
        individualFilter:
          $ref: '#/components/schemas/IndividualFilter'
    InvitationStatusFilter:
      type: object
      title: InvitationStatusFilter
      description: Invitation status filter
      required:
      - rsvpStates
      - type
      properties:
        type:
          type: string
        rsvpStates:
          type: array
          description: List of rsvp states for which travelers need to be fetched
          items:
            $ref: '#/components/schemas/EventRsvpState'
    Itinerary:
      type: object
      description: Details of the itinerary
      oneOf:
      - $ref: '#/components/schemas/AirItinerary'
      - $ref: '#/components/schemas/HotelItinerary'
      - $ref: '#/components/schemas/CarItinerary'
      - $ref: '#/components/schemas/RailItinerary'
      - $ref: '#/components/schemas/LimoItinerary'
    ItineraryFilter:
      type: object
      title: ItineraryFilter
      description: Itinerary Filter details
      required:
      - itineraryInfo
      properties:
        itineraryInfo:
          $ref: '#/components/schemas/Itinerary'
    ItineraryFilterWrapper:
      type: object
      title: ItineraryFilterWrapper
      description: Wrapper for ItineraryFilter
      properties:
        itineraryFilter:
          $ref: '#/components/schemas/ItineraryFilter'
    JobIdFilter:
      title: JobIdFilter
      type: object
      required:
      - filterType
      - jobId
      properties:
        filterType:
          description: Used to distinguish which type of filter is applied.
          type: string
        jobId:
          description: Job Id for the async event job.
          type: string
          format: uuid
          example: f7b3b3b3-7b3b-4b3b-8b3b-3b3b3b3b3b3b
    JobItem:
      title: JobItem
      type: object
      properties:
        eventId:
          type: string
          description: Unique identifier for the event.
          example: '56789012'
        status:
          $ref: '#/components/schemas/EventJobItemStatus'
        message:
          type: string
          description: Error message
    Latlng:
      title: Latlng
      description: Latitude and Longitude for a Location
      type: object
      required:
      - latitude
      - longitude
      properties:
        latitude:
          type: number
          description: Latitude of the Location
          format: double
          example: 77.1025
        longitude:
          type: number
          description: Longitude of the Location
          format: double
          example: 28.7041
    LegMetadata:
      title: LegMetadata
      description: Leg metadata against which document is associated.
      type: object
      properties:
        legId:
          type: string
          description: Unique identifier of the leg.
          example: CgNTRk8SA0RFThoKNTQ1NzI5ODcxMQ==
    LegMetadataWrapper:
      type: object
      title: LegMetadataWrapper
      properties:
        legMetadata:
          $ref: '#/components/schemas/LegMetadata'
    LegalEntityFilter:
      type: object
      title: LegalEntityFilter
      description: LegalEntityFilter details.
      required:
      - legalEntityIds
      properties:
        legalEntityIds:
          type: array
          description: Legal Entity id List
          items:
            $ref: '#/components/schemas/LegalEntityId'
        companyId:
          description: The company Id for the filter.
          $ref: '#/components/schemas/CompanyId'
    LegalEntityFilterWrapper:
      type: object
      title: LegalEntityFilterWrapper
      description: Wrapper for LegalEntityFilter
      properties:
        legalEntityFilter:
          $ref: '#/components/schemas/LegalEntityFilter'
    LegalEntityId:
      type: object
      title: LegalEntityId
      description: The ID of the legal entity.
      required:
      - id
      properties:
        id:
          type: string
          example: fc1ccbce-8413-4fe9-b233-a324dfbe7421
    LegalEntityOverride:
      title: LegalEntityOverride
      type: object
      description: Legal entity override for the event
      required:
      - behavior
      properties:
        behavior:
          $ref: '#/components/schemas/OverrideBehavior'
        legalEntityRef:
          $ref: '#/components/schemas/Reference'
    LimoItinerary:
      type: object
      title: LimoItinerary
      properties:
        limoItineraryId:
          $ref: '#/components/schemas/LimoItineraryId'
    LimoItineraryId:
      type: object
      title: LimoItineraryId
      description: Limo itinerary id
      required:
      - limoId
      properties:
        limoId:
          type: string
          description: Selected Limo option
    ListEventsRequest:
      title: ListEventsRequest
      description: List of events request
      type: object
      required:
      - listEventType
      properties:
        listEventType:
          type: string
          description: Type of events
          enum:
          - UPCOMING
          - PAST_OR_COMPLETED
          - CANCELLED_EVENT
          example: UPCOMING
        filters:
          type: array
          description: List events filters
          items:
            $ref: '#/components/schemas/EventFilter'
        travelerFilters:
          type: array
          description: Filters for event travelers
          items:
            $ref: '#/components/schemas/EventTravelerFilter'
        offset:
          type: integer
          description: Indicates from where in the list of events the server should
            start.
          default: 0
          minimum: 0
          example: 2
        limit:
          type: integer
          description: Maximum number of results to be fetched for the query.
          default: 10
          maximum: 100
          example: 2
    ListEventsResponse:
      title: ListEventsResponse
      description: List of events response
      type: object
      properties:
        events:
          type: array
          description: List of events
          items:
            $ref: '#/components/schemas/Event'
        paginationParams:
          $ref: '#/components/schemas/OffsetBasedPaginationResponseParams'
    ManagedTravelerPaymentAccess:
      type: object
      description: Settings for sharing this payment method with managed traveler
        profiles. Applicable for Arranger Roles.
      required:
      - enabled
      properties:
        enabled:
          type: boolean
          description: Indicates if this payment method can be used on the Traveler
            Profiles being managed by this User.
          default: false
        visibility:
          type: object
          description: Controls how this payment method is displayed on checkout for
            managed traveler profiles, applicable only if sharing is enabled.
          properties:
            showToTravelers:
              type: boolean
              description: If true, managed travelers can see this payment method
                when booking for themselves. If false, only the Travel Arranger can
                see it.
              default: false
    Money:
      type: object
      title: Money
      description: 'Money object containing details such as the amount, the currency
        code, and the converted amount.

        '
      required:
      - amount
      - currencyCode
      properties:
        amount:
          type: number
          format: double
          description: The numeric value for the amount of money.
          example: 510
        currencyCode:
          type: string
          description: The 3-letter currency code for the money amount (defined using
            ISO 4217 standard).
          example: GBP
        convertedAmount:
          type: number
          format: double
          description: 'The converted currency and amount that has been converted
            (if a currency conversion has been requested).

            For example, if the call requests that money be sent in a specified currency
            (because the frontend requested

            the backend to send money in the user''s preferred currency).

            '
          example: 715.42
        convertedCurrency:
          type: string
          description: The 3-letter currency code for the converted currency (defined
            using ISO 4217 standard).
          example: USD
        otherCoinage:
          type: array
          title: OtherCoinage
          description: List of the dollar amount in other coinage systems like reward
            points, cryptocurrency etc.
          items:
            type: object
            properties:
              coinageCode:
                $ref: '#/components/schemas/PaymentMethod'
                description: Payment method
              amount:
                type: number
                format: double
                example: 1000
              conversionRate:
                type: number
                format: double
                description: 1 coin in this system equals to how many currency value
                example: 0.01
              preferredCurrencyConversionRate:
                type: number
                format: double
                description: 1 coin in this system equals to how many currency value
                example: 0.01
    Name:
      type: object
      title: Name
      description: Full name containing first, middle, last (family) names, and suffix.
      required:
      - given
      - family1
      properties:
        family1:
          type: string
          description: Last (family) name.
          example: Gandas
        family2:
          type: string
          example: FamilyTwo
        given:
          type: string
          description: First (given) name.
          example: Vichitr
        middle:
          type: string
          description: Middle name.
          example: Kumar
        suffix:
          $ref: '#/components/schemas/NameSuffix'
          description: Suffix used with the name. For example SR or JR.
          example: SR
        preferred:
          type: string
          description: Informal preferred name added by traveler. This is not used
            on any PNR or tickets
          example: Don
    NameSuffix:
      title: NameSuffix
      description: Suffix for name
      type: string
      enum:
      - NAME_SUFFIX_UNKNOWN
      - SR
      - JR
      - MD
      - PHD
      - II
      - III
      - IV
      - DO
      - ATTY
      - V
      - VI
      - ESQ
      - DC
      - DDS
      - VM
      - JD
      - SECOND
      - THIRD
      example: SR
    OffsetBasedPaginationResponseParams:
      type: object
      title: PaginationResponseParams
      description: Pagination parameters for response.
      required:
      - totalNumResults
      properties:
        totalNumResults:
          type: integer
          format: int32
          description: Total number of results.
    OperationStatus:
      title: OperationStatus
      description: Generic operation/execution status.
      type: string
      enum:
      - SUCCESS
      - FAILED
      - PARTIAL_SUCCESS
      example: SUCCESS
    OverrideBehavior:
      title: OverrideBehavior
      type: string
      description: Override behavior for the trip overrides
      enum:
      - OVERRIDE_ALLOWED
      - OVERRIDE_NOT_ALLOWED
      - OVERRIDE_MANDATORY
      example: OVERRIDE_ALLOWED
      x-enum-descriptions:
      - Override is allowed.
      - Override is not allowed.
      - Override is mandatory.
    OwnershipLabel:
      title: OwnershipLabel
      type: string
      description: Ownership label of the card whether it is personal, corporate or
        central.
      enum:
      - CORPORATE
      - PERSONAL
      - CENTRAL
    PaymentAccessMapping:
      type: object
      required:
      - id
      description: This object contains an array of payment source details, each including
        unique identifiers and specific attributes related to access types, such as
        travel type information.
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier identifying this payment source
          example: f49d00fe-1eda-4304-ba79-a980f565281d
        accessTypeAttributes:
          description: Attributes applicable to the access type for this mapping.
          $ref: '#/components/schemas/AccessTypeAttributes'
    PaymentConfigForTravelType:
      type: object
      description: Allowed payment configuration for a travel type
      required:
      - travelType
      - paymentConfig
      properties:
        travelType:
          $ref: '#/components/schemas/TravelType'
        paymentConfig:
          $ref: '#/components/schemas/TemplatePaymentConfig'
    PaymentEventFilter:
      type: object
      title: PaymentEventFilter
      description: Payment EventFilter details.
      required:
      - eventIds
      - companyId
      properties:
        eventIds:
          type: array
          description: Event ids List
          items:
            type: string
            example: '12345'
        companyId:
          description: The company Id for the filter.
          $ref: '#/components/schemas/CompanyId'
    PaymentEventFilterWrapper:
      type: object
      title: PaymentEventFilterWrapper
      description: Wrapper for EventFilter
      properties:
        eventFilter:
          $ref: '#/components/schemas/PaymentEventFilter'
    PaymentInstructionId:
      type: object
      title: PaymentInstructionId
      description: Payment instruction id
      required:
      - id
      - travelType
      properties:
        id:
          type: string
          format: uuid
          description: Id corresponding to the payment instruction
          example: f49d00fe-1eda-4304-ba79-a980f565281d
        travelType:
          description: associated travelType for the instruction
          $ref: '#/components/schemas/TravelType'
    PaymentMethod:
      type: string
      description: Payment method
      enum:
      - PAYMENT_METHOD_UNKNOWN
      - CREDIT_CARD
      - BREX_POINTS
      - CASH
      - QANTAS_POINTS
      - VENDOR_PROGRAM_PAYMENT
      - DELAYED_INVOICING
      - FLIGHT_CREDITS
      - QANTAS_TRAVEL_FUND
      - CUSTOM_VIRTUAL_PAYMENT
      example: BREX_POINTS
    PaymentSourceAttributes:
      type: object
      title: PaymentSourceAttributes
      description: Attributes applicable for the payment source.
      properties:
        guestTravelAllowed:
          deprecated: true
          type: boolean
          description: Whether guest travel is allowed.
        roleTypes:
          type: array
          description: Role types which have access to the payment source
          items:
            $ref: '#/components/schemas/RoleType'
        includedPersonas:
          type: array
          description: If specified, list of traveler personas which have access to
            the payment source.
          items:
            $ref: '#/components/schemas/Persona'
        excludedPersonas:
          type: array
          description: List of traveler personas which don't have access to the payment
            source.
          items:
            $ref: '#/components/schemas/Persona'
        allowedThirdPartySources:
          type: array
          description: List of 3rd party sources supported for the payment source.
          items:
            $ref: '#/components/schemas/ThirdPartySource'
        allowedTripTypes:
          type: array
          description: List of trip types supported for the payment source.
          items:
            $ref: '#/components/schemas/TripUsageType'
    PaymentSourceDepartmentFilter:
      type: object
      title: DepartmentFilter
      description: DepartmentFilter details.
      required:
      - companyId
      - departmentIds
      properties:
        companyId:
          description: company id
          $ref: '#/components/schemas/CompanyId'
        departmentIds:
          type: array
          description: List of department ids
          items:
            $ref: '#/components/schemas/DepartmentId'
    PaymentSourceDescriptor:
      type: object
      title: PaymentSourceDescriptor
      description: Descriptor corresponding to the payment source.
      oneOf:
      - $ref: '#/components/schemas/CardDescriptorWrapper'
      - $ref: '#/components/schemas/RewardsProgramDescriptorWrapper'
      - $ref: '#/components/schemas/VirtualCardDescriptorWrapper'
      - $ref: '#/components/schemas/CustomPaymentMethodDescriptorWrapper'
      - $ref: '#/components/schemas/VendorProgramPaymentDescriptorWrapper'
      - $ref: '#/components/schemas/DelayedInvoicingDescriptorWrapper'
      - $ref: '#/components/schemas/UnusedCreditDescriptorWrapper'
      - $ref: '#/components/schemas/CashDescriptorWrapper'
    PaymentSourceFilterMetadata:
      type: object
      title: PaymentSourceFilterMetadata
      description: Metadata corresponding to the payment source.
      oneOf:
      - $ref: '#/components/schemas/LegalEntityFilterWrapper'
      - $ref: '#/components/schemas/CompanyFilterWrapper'
      - $ref: '#/components/schemas/CountryFilterWrapper'
      - $ref: '#/components/schemas/TmcFilterWrapper'
      - $ref: '#/components/schemas/PersonalFilterWrapper'
      - $ref: '#/components/schemas/IndividualFilterWrapper'
      - $ref: '#/components/schemas/CostCenterFilterWrapper'
      - $ref: '#/components/schemas/DepartmentFilterWrapper'
      - $ref: '#/components/schemas/ItineraryFilterWrapper'
      - $ref: '#/components/schemas/PaymentEventFilterWrapper'
      - $ref: '#/components/schemas/EventTemplateFilterWrapper'
      x-ignoreBreakingChanges:
      - PaymentSourceFilterMetadata->EventTemplateFilterWrapper
    PaymentSourceInfo:
      type: object
      title: PaymentSourceInfo
      required:
      - id
      - paymentSource
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier identifying this payment source
          example: f49d00fe-1eda-4304-ba79-a980f565281d
        type:
          $ref: '#/components/schemas/PaymentSourceType'
        paymentSource:
          description: Payment source descriptor.
          $ref: '#/components/schemas/PaymentSourceDescriptor'
        mapping:
          deprecated: true
          description: Mapping i.e. access level, travel type information etc.
          $ref: '#/components/schemas/PaymentSourceMapping'
        status:
          $ref: '#/components/schemas/PaymentSourceStatus'
    PaymentSourceMapping:
      type: object
      title: PaymentSourceMapping
      properties:
        accessType:
          description: Access Type of the Payment Source
          $ref: '#/components/schemas/AccessType'
        accessTypeEntityId:
          type: string
          description: Entity ID corresponding to the Access Type being created
        filter:
          description: Applicable access information for this payment_source
          $ref: '#/components/schemas/PaymentSourceFilterMetadata'
        travelType:
          type: array
          description: Applicable payment source travel type.
          deprecated: true
          items:
            $ref: '#/components/schemas/PaymentSourceTravelType'
        travelTypes:
          type: array
          description: Applicable payment source travel types
          items:
            $ref: '#/components/schemas/PaymentSourceTravelType'
        spendTypes:
          type: array
          description: Applicable spend types for payment source other than travel
          items:
            $ref: '#/components/schemas/PaymentSourceSpendType'
        paymentSourceAttributes:
          description: Attributes applicable to the associated payment source.
          $ref: '#/components/schemas/PaymentSourceAttributes'
        accessTypeAttributes:
          description: Attributes applicable to the access type.
          $ref: '#/components/schemas/AccessTypeAttributes'
    PaymentSourceSpendType:
      type: object
      title: PaymentSourceSpendType
      required:
      - spendType
      properties:
        spendType:
          description: Applicable spend type e.g SERVICE_FEE
          $ref: '#/components/schemas/SpendType'
    PaymentSourceStatus:
      type: string
      description: Status of the Payment Source.
      readOnly: true
      enum:
      - ACTIVE
      - INCOMPLETE
    PaymentSourceTravelType:
      type: object
      title: PaymentSourceTravelType
      required:
      - travelType
      properties:
        travelType:
          description: Applicable travel segment type, e.g. AIR, HOTEL etc.
          $ref: '#/components/schemas/TravelType'
        isRequired:
          type: boolean
          description: Whether this is a required payment source for this travel type.
        allowPostPaidBookings:
          type: boolean
          description: Whether post paid bookings are allowed for this travel type.
          default: false
        metadata:
          description: Travel Type specific metadata for payment source.
          $ref: '#/components/schemas/PaymentSourceTravelTypeMetadata'
    PaymentSourceTravelTypeMetadata:
      type: object
      title: PaymentSourceTravelTypeMetadata
      discriminator:
        propertyName: travelType
        mapping:
          CAR: '#/components/schemas/CarPaymentSourceMetadata'
      oneOf:
      - $ref: '#/components/schemas/CarPaymentSourceMetadata'
    PaymentSourceType:
      type: string
      description: Type of Payment Source
      enum:
      - CARD
      - VIRTUAL_CARD
      - REWARDS_PROGRAM
      - DELAYED_INVOICING
      - CUSTOM_PAYMENT_METHOD
      - VENDOR_PROGRAM_PAYMENT
      - UNUSED_CREDIT
      - CASH
    Persona:
      type: string
      title: Persona
      description: Persona of the user
      enum:
      - UNKNOWN_PERSONA
      - EMPLOYEE
      - GUEST
      - PERSONAL
      - RELATIVE
      - ADHOC
      example: EMPLOYEE
    PersonalFilter:
      type: object
      title: PersonalFilter
      description: TmcFilter details.
      required:
      - id
      properties:
        id:
          description: user id
          $ref: '#/components/schemas/UserId'
        managedTravelerAccess:
          description: Config for providing the access to Managed Travelers if any.
            Applicable for Arranger roles.
          $ref: '#/components/schemas/ManagedTravelerPaymentAccess'
    PersonalFilterWrapper:
      type: object
      title: PersonalFilterWrapper
      description: Wrapper for PersonalFilter
      properties:
        personalFilter:
          $ref: '#/components/schemas/PersonalFilter'
    PhoneNumber:
      type: object
      title: PhoneNumber
      description: Properly formatted phone number.
      properties:
        countryCode:
          type: integer
          format: int32
          description: two digit country code
          example: 91
        countryCodeSource:
          type: string
          enum:
          - UNSPECIFIED
          - FROM_NUMBER_WITH_PLUS_SIGN
          - FROM_NUMBER_WITH_IDD
          - FROM_NUMBER_WITHOUT_PLUS_SIGN
          - FROM_DEFAULT_COUNTRY
          example: FROM_NUMBER_WITH_PLUS_SIGN
        extension:
          description: phone number extension
          type: string
          example: '222'
        isoCountryCode:
          description: ISO alpha-2 code
          type: string
          example: IN
        italianLeadingZero:
          type: boolean
          default: false
          example: true
        nationalNumber:
          type: integer
          format: int64
          example: 8150
        numberOfLeadingZeros:
          type: integer
          format: int32
          default: 0
          example: 1
        preferredDomesticCarrierCode:
          type: string
          example: '7'
        rawInput:
          type: string
          example: '77777'
        type:
          type: string
          enum:
          - UNKNOWN_TYPE
          - MOBILE
          - LANDLINE
          example: MOBILE
    PnrMetadata:
      title: PnrMetadata
      description: Metadata when document is associated to pnr entity.
      type: object
      oneOf:
      - $ref: '#/components/schemas/FlightMetadataWrapper'
      - $ref: '#/components/schemas/LegMetadataWrapper'
    PnrMetadataWrapper:
      type: object
      title: PnrMetadataWrapper
      required:
      - travelType
      properties:
        pnrMetadata:
          $ref: '#/components/schemas/PnrMetadata'
        invoiceMetadata:
          type: object
          title: InvoiceMetadata
          description: Metadata associated with an invoice document.
          required:
          - invoiceNumber
          properties:
            invoiceNumber:
              type: string
              example: SPOT-0001
            invoiceType:
              type: string
              example: FARE_INVOICE
              enum:
              - SERVICE_FEE_INVOICE
              - FARE_INVOICE
              - GENERIC_INVOICE
        travelType:
          $ref: '#/components/schemas/TravelType'
    PostalAddress:
      title: PostalAddress
      description: Postal Address Details
      type: object
      required:
      - addressLines
      - regionCode
      properties:
        addressLines:
          description: Address lines
          type: array
          items:
            type: string
            example: Golden Gate Bridge
        administrativeArea:
          type: string
          description: 'Code of administrative area. For example: DL for Delhi, India.

            Highest administrative subdivision which is used for postal

            addresses of a country or region.

            For example, this can be a state, a province, an oblast, or a prefecture.

            Specifically, for Spain this is the province and not the autonomous

            community (e.g. "Barcelona" and not "Catalonia").

            Many countries don''t use an administrative area in postal addresses.
            E.g.

            in Switzerland this should be left unpopulated.

            '
          example: CA
        administrativeAreaName:
          type: string
          description: "Name of administrative area. This is full name corresponding\
            \ to administrativeArea. \nLike Delhi for DL area code. For some places,\
            \ code and name maybe same as well like Tokyo.\n"
          example: California
        description:
          description: Address description
          type: string
          example: San Francisco Home
        isDefault:
          description: Whether this address is default address in case multiple addresses
            are specified.
          type: boolean
          example: true
        languageCode:
          description: "BCP-47 language code of the contents of this address (if known).\
            \ This is often the UI \nlanguage of the input form or is expected to\
            \ match one of the languages used in the \naddress' country/region, or\
            \ their transliterated equivalents.\nThis can affect formatting in certain\
            \ countries, but is not critical to the correctness \nof the data and\
            \ will never affect any validation or other non-formatting related operations.\n\
            Examples: \"zh-Hant\", \"ja\", \"ja-Latn\", \"en\".\n"
          type: string
          example: en
        locality:
          description: Generally refers to the city/town portion of the address.
          type: string
          example: San Francisco
        locationCode:
          description: IATA 3-letter location code. See https://www.iata.org/en/services/codes.
          type: string
          example: LAX
        organization:
          description: The name of the organization at the address.
          type: string
          example: Spotnana
        postalCode:
          description: Postal code of the address. This is a required field when setting
            for a user/legal entity/company etc.
          type: string
          example: '94130'
        continentCode:
          description: 2 letter continent code of the continent this address falls
            in.
          type: string
          example: AF
        recipients:
          description: The recipient at the address.
          type: array
          items:
            type: string
        regionCode:
          description: Region code of the country/region of the address.
          type: string
          example: US
        regionName:
          description: Region name of the country/region of the address.
          type: string
          example: America
        revision:
          type: integer
          format: int32
          example: 1
        sortingCode:
          description: "Additional, country-specific, sorting code. This is not used\n\
            in most regions. Where it is used, the value is either a string like\n\
            \"CEDEX\", optionally followed by a number (e.g. \"CEDEX 7\"), or just\
            \ a number\nalone, representing the \"sector code\" (Jamaica), \"delivery\
            \ area indicator\"\n(Malawi) or \"post office indicator\" (e.g. C\xF4\
            te d'Ivoire).\n"
          type: string
          example: Jamaica
        sublocality:
          description: Sublocality of the address. This can be neighborhoods, boroughs,
            districts.
          type: string
        timezone:
          description: Time zone of the address.
          type: string
          example: America/Los_Angeles
        coordinates:
          description: Map coordinates of the address.
          $ref: '#/components/schemas/Latlng'
    ProgramDetails:
      type: object
      title: ProgramDetails
      description: Descriptor for programs details.
      required:
      - uscId
      - tourCode
      - snapCode
      properties:
        uscId:
          type: string
          description: Contract Id between vendor and the client.
        tourCode:
          type: string
          description: Tracking code to know which company has booked the ticket,
            it is added to the price quote and ticket.
        snapCode:
          type: string
          description: Discount code.
    PublishEventRequest:
      type: object
      description: Event details for publishing.
      required:
      - id
      - sendInvite
      properties:
        id:
          type: string
          description: Unique identifier for the event.
          example: '56789012'
        sendInvite:
          type: boolean
          description: Flag to indicate if an invite should be sent.
          example: true
        userIds:
          type: array
          description: List of users receiving the invite. - If provided, invites
            will be sent only to the specified users. - If empty or not provided,
            invites will be sent to all users.
          items:
            $ref: '#/components/schemas/UserId'
          minItems: 0
          maxItems: 1000
        type:
          $ref: '#/components/schemas/EventType'
    PublishEventResponse:
      type: object
      description: Event publish response details.
      required:
      - id
      - status
      properties:
        id:
          type: string
          description: Unique identifier for the event.
          example: '56789012'
        status:
          $ref: '#/components/schemas/OperationStatus'
        message:
          type: string
          description: Details about success or failure reason.
          example: Published successfully
    QantasTravelFund:
      type: object
      title: QantasTravelFund
      required:
      - label
      description: Metadata for Qantas Travel Fund payment source.
      properties:
        label:
          type: string
          example: QANTAS_TRAVEL_FUND
          description: Label for Qantas Travel Fund payment source.
    QantasTravelFundWrapper:
      type: object
      title: QantasTravelFundWrapper
      description: Wrapper for Qantas Travel Fund payment source.
      required:
      - qantasTravelFund
      properties:
        qantasTravelFund:
          $ref: '#/components/schemas/QantasTravelFund'
    RailBookingGuideline:
      title: RailBookingGuideline
      description: Rail related guidelines
      type: object
      properties:
        arrivalBookingWindow:
          description: Booking window for rail-in-event.
          $ref: '#/components/schemas/EventBookingWindow'
        departureBookingWindow:
          description: Booking window for rail-out-event.
          $ref: '#/components/schemas/EventBookingWindow'
    RailBookingGuidelineWrapper:
      type: object
      title: RailBookingGuidelineWrapper
      description: Wrapper for RailBookingGuideline
      properties:
        railBookingGuideLine:
          $ref: '#/components/schemas/RailBookingGuideline'
    RailBookingStatusFilter:
      type: object
      title: RailBookingStatusFilter
      description: Rail Booking Status Filter
      required:
      - type
      - bookingStatus
      properties:
        type:
          type: string
          description: Type of filter
        bookingStatus:
          type: array
          description: Rail Booking status
          items:
            $ref: '#/components/schemas/BookingStatusType'
    RailItinerary:
      type: object
      title: RailItinerary
      properties:
        railItineraryId:
          $ref: '#/components/schemas/RailItineraryId'
    RailItineraryId:
      type: object
      title: RailItineraryId
      description: Rail itinerary id
      required:
      - searchKey
      properties:
        searchKey:
          type: string
          description: Search key
    RailRsvpResponse:
      title: RailRsvpResponse
      type: object
      description: Rail rsvp response
      properties:
        notNeeded:
          type: boolean
          description: Whether rail booking is needed by the traveler or not
          example: true
    Reference:
      type: object
      title: Reference object containing uuid and name of the entity.
      description: Reference of an entity
      required:
      - id
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
    ReferenceIdFilter:
      title: ReferenceIdFilter
      type: object
      required:
      - filterType
      - referenceId
      properties:
        filterType:
          description: Used to distinguish which type of filter is applied.
          type: string
        referenceId:
          description: Reference Id associated with the job.
          type: string
          example: '8859676988'
        type:
          description: Job type filter.
          $ref: '#/components/schemas/EventJobType'
    RemoveTravelersFromEventRequest:
      title: RemoveTravelersFromEventRequest
      description: Remove travelers from an event request
      type: object
      properties:
        type:
          $ref: '#/components/schemas/EventType'
        userIds:
          type: array
          items:
            $ref: '#/components/schemas/UserId'
          minItems: 1
          maxItems: 10
    RemoveTravelersFromEventResponse:
      title: RemoveTravelersFromEventResponse
      description: Remove travelers from an event response
      type: object
      properties:
        removedUserIds:
          type: array
          items:
            $ref: '#/components/schemas/UserId'
    ReportingAttribute:
      type: object
      title: ReportingAttribute
      description: ReportingAttribute information for virtual cards.
      properties:
        type:
          type: string
          description: Type of ReportingAttribute
          enum:
          - EMPLOYEE_ID
          - COST_CENTER_ID
          - LEGAL_ENTITY_ID
          example: LEGAL_ENTITY_ID
    RewardsProgramDescriptor:
      type: object
      title: RewardsProgramDescriptor
      description: Descriptor for Rewards Program.
      properties:
        type:
          $ref: '#/components/schemas/RewardsProgramType'
    RewardsProgramDescriptorWrapper:
      type: object
      title: RewardsProgramDescriptorWrapper
      description: Wrapper for RewardsProgramDescriptor
      properties:
        rewardsProgramDescriptor:
          $ref: '#/components/schemas/RewardsProgramDescriptor'
    RewardsProgramType:
      type: string
      title: RewardsProgramType
      description: Type of Rewards Program
      enum:
      - BREX_POINTS
      - QANTAS_POINTS
    RoleType:
      type: string
      enum:
      - UNKNOWN_TYPE
      - COMPANY_ADMIN
      - COMPANY_TRAVEL_ARRANGER
      - TRAVEL_ARRANGER
      - COMPANY_REPORT_ADMIN
      - GLOBAL_ADMIN
      - GLOBAL_AGENT
      - TMC_AGENT
      - TMC_ADMIN
      example: TRAVEL_ARRANGER
    SendInviteRequest:
      title: SendInviteRequest
      description: Send invite for an event to the travelers
      type: object
      properties:
        type:
          $ref: '#/components/schemas/EventType'
        userIds:
          type: array
          items:
            $ref: '#/components/schemas/UserId'
    SetUserEventRsvpRequest:
      title: SetUserEventRsvpRequest
      description: Set user's event rsvp request object
      type: object
      properties:
        type:
          $ref: '#/components/schemas/EventType'
        eventRsvpState:
          $ref: '#/components/schemas/EventRsvpState'
        eventRsvpResponse:
          $ref: '#/components/schemas/EventRsvpResponse'
    SpendType:
      type: string
      title: SpendType
      description: Spend type
      enum:
      - SERVICE_FEE
    SplitOptionByCardCompany:
      type: object
      title: SplitOptionByCardCompany
      required:
      - cardCompanyCode
      description: Split option details when using a card from a specific card company
      properties:
        cardCompanyCode:
          type: string
          description: Card company code which allows splitting the payment
          example: VI, TP
        splitWithCardCompanyCodes:
          type: array
          items:
            type: string
            description: Card company codes payment can be split with. Empty value
              will indicate that payment cannot be split with cards of any company.
    TemplatePaymentConfig:
      type: string
      description: Allowed payment configuration for a travel type in the event template
      enum:
      - TRAVELER_DEFAULT
      - PERSONAL
      - SELECTED_METHODS
    ThirdPartyHotelCode:
      title: ThirdPartyHotelCode
      type: object
      required:
      - hotelCode
      - hotelCodeType
      properties:
        hotelCode:
          type: string
          description: Third party hotel code.
        hotelCodeType:
          type: string
          description: Type of the third party.
          enum:
          - SABRE_CSL
          - SABRE_TN
          - EXPEDIA_RAPID
          - MARRIOTT
          - GIATA
    ThirdPartySource:
      type: string
      enum:
      - UNKNOWN_SOURCE
      - SABRE
      - TRAVEL_FUSION
      - AVIA
      - NDC
      - TRAINLINE
      - ATPCO_NDC
      - FARELOGIX_NDC
      - OFFLINE
      - CONNEXUS
      - ROUTEHAPPY
      - AMADEUS
      - GIATA
      - QBR
      - BCD
      - QANTAS_HOTELS
      - SOUTHWEST
      - EXPEDIA
      - HOTEL_HUB
      - MARRIOTT
      - CLEARTRIP
      - KYTE
      - GROUNDSPAN
      - SABRE_NDC
      - BOOKING_COM
      x-ignoreBreakingChanges:
      - ThirdPartySource->BOOKING_COM
      example: SABRE
    TmcFilter:
      type: object
      title: TmcFilter
      description: TmcFilter details.
      required:
      - companyId
      properties:
        companyId:
          description: Tmc id
          $ref: '#/components/schemas/CompanyId'
    TmcFilterWrapper:
      type: object
      title: TmcFilterWrapper
      description: Wrapper for TmcFilter
      properties:
        tmcFilter:
          $ref: '#/components/schemas/TmcFilter'
    TokenizedExpiry:
      title: TokenizedExpiry
      type: object
      description: Contains the tokenized expiry of a Card.
      required:
      - expiryMonth
      - expiryYear
      properties:
        expiryMonth:
          type: string
          description: Tokenized Expiry month
          example: KvAuPANQWCpjwRQxcC8EXg==
        expiryYear:
          type: string
          description: Tokenized Expiry year
          example: fPBm0OWrKwPyIrCVcbg4cA==
    TokenizedExpiryWrapper:
      type: object
      title: TokenizedExpiryWrapper
      properties:
        tokenizedExpiry:
          $ref: '#/components/schemas/TokenizedExpiry'
    TravelType:
      type: string
      title: TravelType
      description: Travel Type
      enum:
      - AIR
      - HOTEL
      - CAR
      - RAIL
      - LIMO
      - MISC
      - ALL
      example: AIR
    TravelTypeFilter:
      type: object
      title: TravelTypeFilter
      description: Filter for selecting payment sources according to different types
        of travel.
      properties:
        travelTypes:
          type: array
          description: Applicable travel types for Payment Source.
          items:
            $ref: '#/components/schemas/PaymentSourceTravelType'
    TravelerEventInfo:
      title: TravelerEventInfo
      type: object
      description: User rsvp and tripId for the event
      properties:
        userId:
          description: User Id
          $ref: '#/components/schemas/UserId'
        tripInfos:
          type: array
          description: List of trip info for the user
          items:
            $ref: '#/components/schemas/TripInfo'
        eventRsvpState:
          description: Users' rsvp state
          $ref: '#/components/schemas/EventRsvpState'
          deprecated: true
        eventRsvpResponse:
          description: Users' rsvp response. This includes details related to bookings
          $ref: '#/components/schemas/EventRsvpResponse'
          deprecated: true
        eventUserRsvp:
          description: User's rsvp information.
          $ref: '#/components/schemas/EventUserRsvp'
    TravelerEventSummary:
      title: TravelerEventSummary
      type: object
      description: Event summary for a traveler
      required:
      - id
      properties:
        id:
          type: string
          description: Event ID
          example: '12345'
        type:
          description: Event Type
          $ref: '#/components/schemas/EventType'
        name:
          type: string
          description: Name of the event
          example: My event
        description:
          type: string
          description: Description of the event
          example: This is an event description
        startDateTime:
          $ref: '#/components/schemas/DateTimeLocal'
        endDateTime:
          $ref: '#/components/schemas/DateTimeLocal'
        location:
          $ref: '#/components/schemas/EventLocation'
        contacts:
          type: array
          description: Event contacts for the traveler
          items:
            $ref: '#/components/schemas/UserId'
          deprecated: true
        documents:
          type: array
          description: List of documents associated with this event for the traveler
          items:
            $ref: '#/components/schemas/Document'
        bookingGuidelines:
          description: Booking details allowed for the event for the traveler
          type: array
          items:
            $ref: '#/components/schemas/EventBookingGuideline'
        allowedBookingTypes:
          type: array
          description: Allowed booking types for the event for the traveler
          items:
            $ref: '#/components/schemas/EventAllowedBookingType'
        eventUserRsvp:
          $ref: '#/components/schemas/EventUserRsvp'
        contactInfoList:
          type: array
          description: Event contacts for the traveler
          items:
            $ref: '#/components/schemas/EventUserInfo'
        companyId:
          $ref: '#/components/schemas/EntityId'
        runningStatus:
          $ref: '#/components/schemas/EventRunningStatus'
        status:
          $ref: '#/components/schemas/EventStatus'
        isRemovedParticipant:
          type: boolean
          description: Whether the traveler is part of the event.
    TravelerSearchFilter:
      type: object
      title: TravelerSearchFilter
      description: Traveler search filter
      required:
      - type
      properties:
        type:
          type: string
        searchTerm:
          type: string
          example: name
    TripEventSummary:
      title: TripEventSummary
      type: object
      description: Event summary for trip
      properties:
        tripId:
          type: string
          description: Spotnana trip ID
          example: '6926658168'
        travelerEventSummary:
          $ref: '#/components/schemas/TravelerEventSummary'
    TripInfo:
      type: object
      title: TripInfo
      description: Trip information.
      required:
      - tripId
      - status
      properties:
        tripId:
          type: string
          description: ID of the trip of the user.
          example: '1234567890'
        status:
          $ref: '#/components/schemas/UserFacingStatus'
    TripOverrides:
      title: TripOverrides
      type: object
      description: Trip overrides for the event
      required:
      - legalEntity
      - costCenter
      - department
      - userAttributes
      properties:
        legalEntity:
          description: Legal entity override
          $ref: '#/components/schemas/LegalEntityOverride'
        costCenter:
          description: Cost center override
          $ref: '#/components/schemas/CostCenterOverride'
        department:
          description: Department override
          $ref: '#/components/schemas/DepartmentOverride'
        userAttributes:
          type: array
          description: List of company specified attribute overrides
          items:
            $ref: '#/components/schemas/CompanySpecifiedAttributeOverride'
    TripUsageType:
      title: TripUsageType
      description: Trip usage type.
      type: string
      enum:
      - STANDARD
      - EVENT
    UAPassPlusMetadata:
      type: object
      title: UAPassPlusMetadata
      description: Details for UAPassPlus Metadata.
      required:
      - programDetails
      - uatpInfo
      properties:
        programDetails:
          $ref: '#/components/schemas/ProgramDetails'
        uatpInfo:
          $ref: '#/components/schemas/UATPInformation'
    UAPassPlusMetadataWrapper:
      type: object
      title: UAPassPlusMetadataWrapper
      description: Wrapper for UAPassPlus Metadata.
      properties:
        uaPassPlusMetadata:
          $ref: '#/components/schemas/UAPassPlusMetadata'
    UATPInformation:
      type: object
      title: UATPInformation
      description: Descriptor for UATPInformation.
      required:
      - card
      - ticketingValidity
      - travelValidity
      properties:
        card:
          $ref: '#/components/schemas/Card'
        ticketingValidity:
          description: Valid ticketing datetime.
          $ref: '#/components/schemas/DateTimeRange'
        travelValidity:
          description: Valid travel datetime.
          $ref: '#/components/schemas/DateTimeRange'
    UATPMetadata:
      type: object
      title: UATPMetadata
      description: Details for UATP Metadata.
      required:
      - card
      properties:
        card:
          $ref: '#/components/schemas/Card'
    UATPMetadataWrapper:
      type: object
      title: UATPMetadataWrapper
      description: Wrapper for UATP Metadata.
      properties:
        uatpMetadata:
          $ref: '#/components/schemas/UATPMetadata'
    UnusedCreditDescriptor:
      type: object
      title: UnusedCreditDescriptor
      description: Descriptor for unused credits.
      required:
      - unusedCreditInfo
      - userId
      - companyId
      properties:
        unusedCreditInfo:
          $ref: '#/components/schemas/UnusedCreditInfo'
        userId:
          description: The user Id of the user who owns the credit.
          $ref: '#/components/schemas/UserId'
        companyId:
          description: The company Id of the user who owns the credit.
          $ref: '#/components/schemas/CompanyId'
    UnusedCreditDescriptorWrapper:
      type: object
      title: UnusedCreditDescriptorWrapper
      description: Wrapper for UnusedCreditDescriptor.
      properties:
        unusedCreditDescriptor:
          $ref: '#/components/schemas/UnusedCreditDescriptor'
    UnusedCreditInfo:
      type: object
      title: UnusedCreditInfo
      description: Information regarding an unused credit.
      properties:
        sourcePnr:
          type: string
          description: PNR number corresponding to third party through which booking
            was made.
          example: MC5ONS
        spotnanaPnr:
          type: string
          description: Spotnana pnr ID.
          example: '2345678'
        ticketNumber:
          type: string
          description: Ticket number for the ticket that was converted into an unused
            credit.
          example: '5267779139217'
        airlineCode:
          type: string
          description: 2 letter airline code of the airline associated with this unused
            credit.
          example: AA
        airlineInfo:
          description: Airline info with airline name and code
          $ref: '#/components/schemas/AirlineInfo'
        totalFare:
          description: Total airfare associated with the original ticket.
          $ref: '#/components/schemas/Money'
        issueDate:
          description: Issue date for the unused credit.
          $ref: '#/components/schemas/DateTimeOffset'
        expiryDate:
          description: Expiry date for the unused credit.
          $ref: '#/components/schemas/DateTimeOffset'
        usedDate:
          description: Date on which the unused credit was used.
          $ref: '#/components/schemas/DateTimeOffset'
        departureDate:
          description: Date for the departure of the first flight associated with
            the unused credit.
          $ref: '#/components/schemas/DateTimeOffset'
        segmentsAvailable:
          type: string
          description: Whether all segments are unused or some have already been used.
          enum:
          - UNKNOWN
          - ALL_OPEN
          - PARTIAL
          - OTHER
        passengerName:
          description: Name of the passenger associated with the credit.
          $ref: '#/components/schemas/Name'
        departureCountry:
          type: string
          description: 3 letter country code of the departure country associated with
            the original ticket.
          example: USA
        arrivalCountry:
          type: string
          description: 3 letter country code of the arrival country associated with
            the original ticket.
          example: USA
        ticketType:
          type: string
          description: Type of credit.
          enum:
          - TICKET_TYPE_UNKNOWN
          - ETICKET
          - MCO
          - NON_GDS
        pcc:
          type: string
          description: PCC the credit was issued on.
        status:
          $ref: '#/components/schemas/CreditStatus'
        source:
          $ref: '#/components/schemas/ThirdPartySource'
          default: SABRE
          description: Source of unused credit e.g. Sabre, NDC etc.
        tripId:
          type: string
          description: Trip ID that contains the unused credit
          example: '1234567'
        redeemVia:
          type: string
          description: "Credit redemption method. \nIf the value contains `CONTACT_AGENT`,\
            \ then the agent must book the ticket and redeem the credits on behalf\
            \ of the traveler.\n"
          enum:
          - REDEEM_VIA_OBT
          - CONTACT_AGENT
          example: REDEEM_VIA_OBT
        sourceOfTruth:
          type: string
          description: The system that owns the credit.
          enum:
          - SPOTNANA
          - MANUAL_FORM
        owningPcc:
          type: string
          description: PCC the PNR was created on.
        paymentSourceId:
          type: string
          format: uuid
          description: Payment source ID associated with the credit.
          example: edd5b835-8001-430c-98f8-fedeccebe4cf
        creditUsageType:
          $ref: '#/components/schemas/CreditUsageType'
          description: The type of credit usage. This can be either COMPANY or PERSONAL.
        email:
          type: string
          description: Email of the passenger owning the unused credit.
    UpdateEventBasicInfoRequest:
      title: UpdateEventBasicInfoRequest
      description: Update Event basic info request object
      type: object
      required:
      - name
      - location
      properties:
        name:
          type: string
          description: Name of the event
        type:
          $ref: '#/components/schemas/EventType'
        description:
          type: string
          description: Description of the event
        documents:
          type: array
          description: Documents attached for an event
          items:
            $ref: '#/components/schemas/Document'
        startDateTime:
          $ref: '#/components/schemas/DateTimeLocal'
        endDateTime:
          $ref: '#/components/schemas/DateTimeLocal'
        location:
          $ref: '#/components/schemas/EventLocation'
        contacts:
          type: array
          description: Contacts for the event
          items:
            $ref: '#/components/schemas/UserId'
        referenceUsers:
          type: array
          description: Event group reference user ids
          items:
            $ref: '#/components/schemas/UserId'
        travelerTypes:
          type: array
          description: Allowed traveler types for the event
          items:
            $ref: '#/components/schemas/Persona'
        bookingStyle:
          description: Whether the event bookings will be self served by the traveler
            or would be arranged by an agent for the traveler.
          $ref: '#/components/schemas/EventBookingStyle'
        policyId:
          type: string
          format: uuid
          description: Policy associated with the event.
          example: f7b3b3b3-7b3b-4b3b-8b3b-3b3b3b3b3b3b
        allowTravelersPolicy:
          $ref: '#/components/schemas/AllowTravelersPolicy'
        emailPreferences:
          $ref: '#/components/schemas/BucketEmailPreferences'
        allowTravelersToRsvp:
          $ref: '#/components/schemas/AllowTravelersToRsvp'
        internalNotes:
          type: string
          description: Internal notes of the event
          example: Use travelers default as payment method.
        tripOverrides:
          $ref: '#/components/schemas/TripOverrides'
    UpdateEventBookingGuidelinesRequest:
      title: UpdateEventBookingGuidelinesRequest
      description: Update Event booking guidelines request object
      type: object
      required:
      - bookingGuidelines
      properties:
        bookingGuidelines:
          type: array
          description: A list of booking guideline for the event.
          items:
            $ref: '#/components/schemas/EventBookingGuideline'
        allowedBookingTypes:
          type: array
          description: Allowed booking types for the event group
          items:
            $ref: '#/components/schemas/EventAllowedBookingType'
        allowedPaymentConfig:
          $ref: '#/components/schemas/AllowedPaymentConfig'
        paymentMappings:
          type: array
          description: Mappings i.e. access level, travel type information etc.
          items:
            $ref: '#/components/schemas/PaymentAccessMapping'
        eventType:
          $ref: '#/components/schemas/EventType'
    UpdateEventRequestItem:
      title: UpdateEventRequestItem
      description: Update event details and guidelines
      type: object
      required:
      - eventId
      - eventBasicInfo
      - eventBookingGuidelines
      properties:
        eventId:
          type: string
          description: Id of the event to be updated.
          example: '56789012'
        eventBasicInfo:
          $ref: '#/components/schemas/UpdateEventBasicInfoRequest'
        eventBookingGuidelines:
          $ref: '#/components/schemas/UpdateEventBookingGuidelinesRequest'
    UpdateEventResponse:
      type: object
      description: Event update response details.
      required:
      - id
      - status
      properties:
        id:
          type: string
          description: Unique identifier for the event.
          example: '56789012'
        status:
          $ref: '#/components/schemas/OperationStatus'
        message:
          type: string
          description: Details about success or failure reason.
          example: Updated successfully
    UserFacingStatus:
      type: string
      title: UserFacingStatus
      description: User facing booking status
      enum:
      - UNKNOWN_STATUS
      - PENDING_STATUS
      - CONFIRMED_STATUS
      - ACTIVE_STATUS
      - COMPLETED_STATUS
      - CANCELLED_STATUS
      - REFUNDED_STATUS
      - VOIDED_STATUS
      - PROCESSING_STATUS
      - UNCONFIRMED_STATUS
      - AIRLINE_CONTROL_STATUS
      - PAYMENT_DECLINED_STATUS
      - SCHEDULE_CHANGE_STATUS
      - HOLD_STATUS
      - APPROVAL_REQUESTED_STATUS
      - APPROVAL_DENIED_STATUS
      - CANCELLATION_IN_PROGRESS_STATUS
      - INOPERATIVE_STATUS
      - FLIGHT_UNCONFIRMED_STATUS
      example: PENDING_STATUS
    UserGroup:
      type: object
      title: UserGroup
      description: Grouping of users on the basis of different criteria such as org,
        department, etc.
      properties:
        userIds:
          type: array
          items:
            $ref: '#/components/schemas/UserId'
        legalEntityIds:
          type: array
          items:
            $ref: '#/components/schemas/EntityId'
        officeIds:
          type: array
          items:
            $ref: '#/components/schemas/EntityId'
        departments:
          type: array
          description: List of department ids.
          items:
            type: string
        costCenters:
          type: array
          description: List of cost center ids.
          items:
            type: string
        grades:
          type: array
          description: List of grade ids.
          items:
            type: string
        positionTitles:
          type: array
          items:
            type: string
        personas:
          type: array
          items:
            $ref: '#/components/schemas/Persona'
        customFieldIds:
          deprecated: true
          type: array
          items:
            $ref: '#/components/schemas/CustomFieldId'
        countryCodes:
          type: array
          items:
            type: string
            description: Alpha-2 or Alpha-3 ISO country code.
            example: GB
        workerTypes:
          type: array
          items:
            $ref: '#/components/schemas/WorkerType'
        accountingCodes:
          type: array
          items:
            type: string
            description: Code used for accounting.
    UserGroupInfo:
      type: object
      title: UserGroupInfo
      description: Details about group's users.
      properties:
        userGroupCriteria:
          type: array
          description: User group criteria
          items:
            $ref: '#/components/schemas/UserGroup'
        userIds:
          type: array
          description: List of users derived from the user group criteria
          items:
            $ref: '#/components/schemas/UserId'
    UserId:
      type: object
      title: UserId
      description: User identifier
      required:
      - id
      properties:
        id:
          type: string
          format: uuid
    UserTripGroup:
      type: object
      title: UserTripGroup
      description: Definition of a group to combine users and their trips.
      required:
      - ownerId
      properties:
        id:
          type: string
          description: ID of the group.
          format: uuid
          example: f49d00fe-1eda-4304-ba79-a980f565281d
        name:
          type: string
          description: Name of the group.
          example: Sample group name
        userGroupInfo:
          $ref: '#/components/schemas/UserGroupInfo'
        userTripInfos:
          type: array
          description: Details of the users and their trips in the group.
          items:
            $ref: '#/components/schemas/UserTripInfo'
        ownerId:
          $ref: '#/components/schemas/UserId'
        status:
          $ref: '#/components/schemas/UserFacingStatus'
        startDate:
          $ref: '#/components/schemas/DateModel'
        endDate:
          $ref: '#/components/schemas/DateModel'
        createdAt:
          $ref: '#/components/schemas/DateTimeOffset'
    UserTripInfo:
      type: object
      title: UserTripInfo
      description: User and their trip information.
      required:
      - userId
      properties:
        userId:
          $ref: '#/components/schemas/UserId'
        tripInfos:
          type: array
          description: List of trips for the user.
          items:
            $ref: '#/components/schemas/TripInfo'
    Variable:
      type: object
      title: Variable
      description: Variable defining the additional value to be sent along with custom
        field response.
      required:
      - type
      - name
      properties:
        type:
          type: string
          example: VARIABLE
          default: VARIABLE
        name:
          $ref: '#/components/schemas/VariableName'
    VariableName:
      type: string
      title: VariableName
      description: Name of the variable.
      enum:
      - PUBLISHED_FARE
      - LLF
      example: PUBLISHED_FARE
    VendorInfo:
      type: object
      title: VendorInfo
      description: Name/ID etc. of the vendor.
      required:
      - vendorId
      - vendorName
      properties:
        vendorId:
          type: string
          description: ID of the vendor.
        vendorName:
          type: string
          description: Name of the vendor.
    VendorProgramPaymentDescriptor:
      type: object
      title: VendorProgramPaymentDescriptor
      description: Descriptor for vendor program payment.
      required:
      - vendorProgramPaymentMetadata
      properties:
        vendorInfo:
          $ref: '#/components/schemas/VendorInfo'
        vendorProgramPaymentMetadata:
          $ref: '#/components/schemas/VendorProgramPaymentMetadata'
    VendorProgramPaymentDescriptorWrapper:
      type: object
      title: VendorProgramPaymentDescriptorWrapper
      description: Wrapper for VendorProgramPaymentDescriptor
      properties:
        vendorProgramPaymentDescriptor:
          $ref: '#/components/schemas/VendorProgramPaymentDescriptor'
    VendorProgramPaymentMetadata:
      type: object
      title: VendorProgramPaymentMetadata
      description: Vendor program payment specific metadata.
      oneOf:
      - $ref: '#/components/schemas/DirectBillingWrapper'
      - $ref: '#/components/schemas/AirlineProgramWrapper'
    VirtualCardDescriptor:
      type: object
      title: VirtualCardDescriptor
      description: Descriptor for virtual card.
      properties:
        vendor:
          $ref: '#/components/schemas/VirtualCardVendor'
        reportingAttributes:
          type: array
          description: List of ReportingAttributes
          items:
            $ref: '#/components/schemas/ReportingAttribute'
        metadata:
          description: card descriptor corresponding to the virtual card.
          $ref: '#/components/schemas/VirtualCardDescriptorMetadata'
    VirtualCardDescriptorMetadata:
      type: object
      title: VirtualCardDescriptorMetadata
      description: Metadata corresponding to the VirtualCardDescriptor.
      oneOf:
      - $ref: '#/components/schemas/ConfermaMetadataWrapper'
    VirtualCardDescriptorWrapper:
      type: object
      title: VirtualCardDescriptorWrapper
      description: Wrapper for VirtualCardDescriptor
      properties:
        virtualCardDescriptor:
          $ref: '#/components/schemas/VirtualCardDescriptor'
    VirtualCardVendor:
      type: string
      description: Type of Virtual card vendor
      enum:
      - CONFERMA
      example: CONFERMA
    VirtualPaymentMetadata:
      type: object
      title: VirtualPaymentMetadata
      required:
      - label
      description: Metadata for custom virtual payment source.
      properties:
        label:
          type: string
          example: Custom payment card
          description: Label for custom virtual payment source.
    VirtualPaymentMetadataWrapper:
      type: object
      title: VirtualPaymentMetadataWrapper
      description: Wrapper for custom virtual payment metadata
      required:
      - virtualPaymentMetadata
      properties:
        virtualPaymentMetadata:
          $ref: '#/components/schemas/VirtualPaymentMetadata'
    WorkerType:
      type: string
      description: The type of worker.
      enum:
      - EMPLOYEE
      - CONTINGENT
      - SEASONAL
      - INTERN
      - GUEST
      example: EMPLOYEE
      x-enumValidFrom:
        GUEST: '2025-06-03'
tags:
- name: Events
  description: APIs to create and manage events.
paths:
  /v2/events:
    post:
      tags:
      - Events
      summary: Create event
      description: Create a draft event.
      operationId: createEvent
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateEventRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EntityNonUUIDId'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/{eventId}:
    parameters:
    - name: eventId
      in: path
      description: Event ID
      required: true
      schema:
        type: string
        example: '6926658168'
    get:
      tags:
      - Events
      summary: Get event
      description: Get event details for the specified event ID.
      operationId: getEvent
      parameters:
      - name: type
        in: query
        description: EventType of the event.
        schema:
          $ref: '#/components/schemas/EventType'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetEventResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      tags:
      - Events
      summary: Delete a draft event
      description: Delete a draft (i.e., unpublished) event. A deleted event cannot
        be accessed or viewed again.
      operationId: deleteEvent
      responses:
        '200':
          description: OK
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/events/read-by-external-id:
    parameters:
    - name: externalId
      in: query
      description: External ID
      required: true
      schema:
        type: string
        example: '6926658168'
    - name: companyId
      in: query
      description: Company ID
      required: true
      schema:
        type: string
        format: uuid
        example: f49d00fe-1eda-4304-ba79-a980f565281d
    get:
      tags:
      - Events
      summary: Get event by external ID
      description: 'Get event details using the event''s `externalId`. External IDs
        are unique identifiers which can be set based on the company''s preference.

        '
      operationId: getEventByExternalID
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetEventResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/events/{eventId}/travelers/add:
    parameters:
    - name: eventId
      in: path
      description: Event ID
      required: true
      schema:
        type: string
        example: '6926658168'
    post:
      tags:
      - Events
      summary: Add travelers to an event
      description: Add the list of travelers to be invited to the event.
      operationId: addTravelersToEvent
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddTravelersToEventRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddTravelersToEventResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/{eventId}/travelers/remove:
    parameters:
    - name: eventId
      in: path
      description: Event ID
      required: true
      schema:
        type: string
        example: '6926658168'
    post:
      tags:
      - Events
      summary: Remove travelers from an event
      description: Remove travelers from an event. This endpoint allows you to remove
        up to 10 travelers per API call.
      operationId: removeTravelersFromEvent
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RemoveTravelersFromEventRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RemoveTravelersFromEventResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/list:
    post:
      tags:
      - Events
      summary: List events
      description: List all the events that match the specified criteria in the request.
      operationId: listEvents
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ListEventsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListEventsResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/{eventId}/publish:
    parameters:
    - name: eventId
      in: path
      description: Event ID
      required: true
      schema:
        type: string
        example: '6926658168'
    post:
      tags:
      - Events
      summary: Publish event
      description: Publish a draft event.
      operationId: publishEvent
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EntityNonUUIDId'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/{eventId}/edit-basic-info:
    parameters:
    - name: eventId
      in: path
      description: Event ID
      required: true
      schema:
        type: string
        example: '6926658168'
    post:
      tags:
      - Events
      summary: Edit basic details of an event
      description: Edit the basic information of an event such as the event name,
        description, date and time, location, and contacts.
      operationId: updateEventBasicInfo
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateEventBasicInfoRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EntityNonUUIDId'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/{eventId}/edit-booking-guidelines:
    parameters:
    - name: eventId
      in: path
      description: Event ID
      required: true
      schema:
        type: string
        example: '6926658168'
    post:
      tags:
      - Events
      summary: Update event booking guidelines
      description: "Update the booking guidelines for an event.\nYou can update booking\
        \ guidelines such as maximum number of guests allowed to accompany a traveler,\
        \ \narrival and departure window for the event, payment methods, and allowed\
        \ travel types.\n"
      operationId: updateEventBookingGuidelines
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateEventBookingGuidelinesRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EntityNonUUIDId'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/{eventId}/travelers/{travelerId}/rsvp:
    parameters:
    - name: eventId
      in: path
      description: Event ID
      required: true
      schema:
        type: string
        example: '6926658168'
    - name: travelerId
      in: path
      description: Traveler User ID
      required: true
      schema:
        type: string
        format: uuid
        example: f49d00fe-1eda-4304-ba79-a980f565281d
    post:
      tags:
      - Events
      summary: Set traveler RSVP
      description: Update the RSVP status and the RSVP response for a traveler.
      operationId: setUserEventRsvp
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetUserEventRsvpRequest'
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
    get:
      tags:
      - Events
      summary: Get traveler RSVP
      description: Get the RSVP status and the RSVP response for a traveler.
      operationId: getUserEventRsvp
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EventUserRsvp'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/{eventId}/invite/send:
    parameters:
    - name: eventId
      in: path
      description: Event ID
      required: true
      schema:
        type: string
        example: '6926658168'
    post:
      tags:
      - Events
      summary: Send invite
      description: "Send event invites to the travelers via email. \nThe invites can\
        \ be sent to all travelers using a single API call, or you can send invites\
        \ to specific travelers.\n"
      operationId: sendInvite
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendInviteRequest'
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/{eventId}/invite/test:
    parameters:
    - name: eventId
      in: path
      description: Event ID
      required: true
      schema:
        type: string
        example: '6926658168'
    post:
      tags:
      - Events
      summary: Send a test invite
      description: Send a test event invite to the API caller. This endpoint can be
        used to preview the invite before sending it to the travelers.
      operationId: testInvite
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/summaries:
    post:
      tags:
      - Events
      summary: Get event summaries
      description: View summaries of specific events. This endpoint retrieves a list
        of traveler event summaries based on specified `userId`, `companyId`, and
        `tripIds`.
      operationId: getEventSummaries
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetEventSummariesRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetEventSummariesResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/payment-sources:
    parameters:
    - name: userId
      in: query
      description: ID of the event coordinator
      required: true
      schema:
        type: string
        format: uuid
        example: f49d00fe-1eda-4304-ba79-a980f565281d
    get:
      tags:
      - Events
      summary: Get allowed payment sources
      description: Get the payment sources that are associated with the `userId` of
        the event coordinator.
      operationId: getAllowedPaymentSources
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetEventsPaymentSourcesResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/events/{eventId}/cancel:
    parameters:
    - name: eventId
      in: path
      description: Event ID
      required: true
      schema:
        type: string
        example: '6926658168'
    - name: type
      in: query
      description: 'EventType of the event.

        '
      schema:
        $ref: '#/components/schemas/EventType'
    post:
      tags:
      - Events
      summary: Cancel event
      description: "Cancel a published event. \nAfter you've canceled the event, it's\
        \ recommended that you cancel the bookings using the Cancel PNR API.\n"
      operationId: cancelEvent
      responses:
        '200':
          description: OK
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/events/{eventId}/travelers-list:
    parameters:
    - name: eventId
      in: path
      description: Event ID
      required: true
      schema:
        type: string
        example: '6926658168'
    post:
      tags:
      - Events
      summary: Get travelers list
      description: 'Get a paginated list of travelers for an event, including each
        traveler''s RSVP status, RSVP response, and `tripId`.

        '
      operationId: listEventTravelers
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EventTravelersRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EventTravelersResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/{eventId}/custom-field-responses:
    parameters:
    - name: eventId
      in: path
      description: Event ID
      required: true
      schema:
        type: string
        example: '6926658168'
    put:
      tags:
      - Events
      summary: Update event custom field responses
      description: Update the response to a custom field question.
      operationId: updateCustomFieldResponses
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EventCustomFieldResponsesRequest'
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/bulk-publish:
    post:
      tags:
      - Events
      summary: Bulk publish events
      description: API to bulk publish events and has a support for sending invite
        to selective users
      operationId: bulkPublishEvents
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkPublishEventRequest'
      responses:
        '200':
          description: Bulk publish response with event statuses
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkPublishResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/bulk-edit:
    post:
      tags:
      - Events
      summary: Bulk edit/modify events.
      description: Edit the basic info and booking guidelines in bulk for events.
      operationId: bulkEditEvents
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkUpdateEventsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkUpdateEventsResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/bulk-delete:
    post:
      tags:
      - Events
      summary: Bulk delete draft events
      description: API to bulk delete draft events.
      operationId: bulkDeleteEvents
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkDeleteEventRequest'
      responses:
        '200':
          description: Bulk delete response with event statuses
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkDeleteEventResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/bulk-cancel:
    post:
      tags:
      - Events
      summary: Bulk cancel events
      description: API to bulk cancel events.
      operationId: bulkCancelEvents
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkCancelEventRequest'
      responses:
        '200':
          description: Bulk cancel response with event statuses
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkCancelEventResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/events/jobs/status:
    post:
      tags:
      - Events
      summary: Get current details of an async event job.
      description: Get current details of an async event job
      operationId: getAsyncEventJobDetails
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AsyncJobDetailRequest'
      responses:
        '200':
          description: Async job details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AsyncJobDetailResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
