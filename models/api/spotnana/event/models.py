# Code generated from OpenAPI spec. DO NOT EDIT.
# Source: EventApi.yaml
# Generated by: datamodel-code-generator
# 
# This file is automatically generated from the Spotnana Event API OpenAPI specification.
# Any manual changes will be overwritten when the models are regenerated.
# 
# To regenerate: uv run python manage_spotnana_models.py generate

# generated by datamodel-codegen:
#   filename:  EventApi.yaml
#   timestamp: 2025-07-15T00:32:32+00:00

from __future__ import annotations

from collections.abc import Sequence
from enum import Enum
from typing import Literal
from uuid import UUID

from pydantic import BaseModel, ConfigDict, EmailStr, Field, RootModel, conint, constr


class AccessType(Enum):
    CENTRALISED = 'CENTRALISED'
    PERSONAL = 'PERSONAL'
    APPLICATION = 'APPLICATION'
    TMC = 'TMC'
    ITINERARY = 'ITINERARY'
    EVENT = 'EVENT'
    EVENT_TEMPLATE = 'EVENT_TEMPLATE'


class AirItineraryId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ancillaryResponseId: str | None = Field(None, description='Ancillary response id')
    seatMapResponseId: str | None = Field(None, description='Seat map response id')
    initiateBookingResponseId: str | None = Field(
        None, description='Initiate Booking Response id.'
    )


class AirRsvpResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    notNeeded: bool | None = Field(
        None,
        description='Whether air booking is needed by the traveler or not',
        examples=[True],
    )


class Airline(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    carrierCode: str = Field(
        ..., description='Unique code for the Airline', examples=['AA']
    )
    airlineName: str = Field(
        ..., description='Full Name of the Airline', examples=['American Airlines']
    )
    isPresentInPreferredVendors: bool | None = Field(
        None,
        description='Whether the airline is present in preferred vendor list. This is an optional field which gets populated only in the preferred vendor autocomplete API.',
        examples=[True],
    )


class AirlineInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlineCode: str = Field(..., description='IATA code for airline.', examples=['AA'])
    airlineName: str = Field(
        ..., description='Airline name', examples=['American Airlines']
    )


class AirportInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airportCode: str | None = Field(
        None, description='3-letter code of the airport.', examples=['WRA']
    )
    airportName: str | None = Field(
        None, description='Full name of the airport.', examples=['Warder Airport']
    )
    cityName: str | None = Field(
        None,
        description='Name of the city in which the airport is located (or is nearest to).',
        examples=['Werder'],
    )
    countryName: str | None = Field(
        None,
        description='Name of the country in which the airport is located.',
        examples=['Ethiopia'],
    )
    countryCode: str | None = Field(
        None,
        description='2-letter IATA country code associated with the airport.',
        examples=['ET'],
    )
    zoneName: str | None = Field(
        None,
        description='Name of the time zone associated with the airport.',
        examples=['Africa/Addis_Ababa'],
    )
    stateCode: str | None = Field(
        None,
        description='2-letter IATA code for the state in which the airport is located.',
        examples=['CA'],
    )


class AllowTravelersPolicy(Enum):
    ALLOWED = 'ALLOWED'
    NOT_ALLOWED = 'NOT_ALLOWED'


class AllowTravelersToRsvp(Enum):
    ALLOWED = 'ALLOWED'
    NOT_ALLOWED = 'NOT_ALLOWED'


class AllowedFlightType(Enum):
    ONE_WAY = 'ONE_WAY'
    ROUND_TRIP = 'ROUND_TRIP'
    MULTICITY = 'MULTICITY'


class BookingStatusType(Enum):
    BOOKED = 'BOOKED'
    NOT_BOOKED = 'NOT_BOOKED'
    OPTED_OUT = 'OPTED_OUT'


class BrexBudget(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    budgetName: str | None = Field(
        None, description='Budget name.', examples=['Travel Budget']
    )
    cardNumber: str | None = Field(
        None, description='Card number of the budget.', examples=['1234567812345678123']
    )
    budgetRemainingBalanceFormatted: str | None = Field(
        None,
        description='Remaining balance of the budget formatted with currency.',
        examples=['$90,000.00'],
    )


class BrexBudgetWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    brexBudget: BrexBudget


class CarBookingStatusFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['CAR_BOOKING_STATUS_FILTER'] = Field(
        'CAR_BOOKING_STATUS_FILTER', description='Type of filter'
    )
    bookingStatus: Sequence[BookingStatusType] = Field(
        ..., description='Car Booking status'
    )


class CarItineraryId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchId: str = Field(..., description='Search id')
    carId: str = Field(..., description='Selected car option')


class CarRsvpResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    notNeeded: bool | None = Field(
        None,
        description='Whether car booking is needed by the traveler or not',
        examples=[True],
    )


class CarVendorBasic(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    code: str = Field(..., description='Vendor code', examples=['ZE'])
    name: str | None = Field(None, description='Vendor name', examples=['HERTZ'])


class Type(Enum):
    UNKNOWN = 'UNKNOWN'
    CREDIT = 'CREDIT'
    DEBIT = 'DEBIT'


class CardCompany(Enum):
    NONE = 'NONE'
    VISA = 'VISA'
    MASTERCARD = 'MASTERCARD'
    AMEX = 'AMEX'
    DISCOVER = 'DISCOVER'
    AIR_TRAVEL_UATP = 'AIR_TRAVEL_UATP'
    CARTE_BLANCHE = 'CARTE_BLANCHE'
    DINERS_CLUB = 'DINERS_CLUB'
    JCB = 'JCB'
    BREX = 'BREX'
    UNION_PAY = 'UNION_PAY'
    EURO_CARD = 'EURO_CARD'
    ACCESS_CARD = 'ACCESS_CARD'
    ELO_CARD = 'ELO_CARD'


class CashDescriptor(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    label: str = Field(
        ...,
        description='Label for Cash payment.',
        examples=['Cash Payment for Amex TLS'],
    )


class CashDescriptorWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cashDescriptor: CashDescriptor | None = None


class CompanyId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID = Field(..., examples=['f49d00fe-1eda-4304-ba79-a980f565281d'])


class CompanySpecifiedUserAttribute(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fixedColumnName: str = Field(..., examples=['contingentType'])
    value: str | None = Field(None, examples=['FSTV'])


class CostCenterId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['731ccbca-0415-6fe1-d235-c324dfbe7423'])


class CountryFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    countryCodes: Sequence[str] = Field(..., description='country Codes List')
    companyId: CompanyId = Field(..., description='The company Id for the filter.')


class CountryFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    countryFilter: CountryFilter | None = None


class CreditStatus(Enum):
    STATUS_UNKNOWN = 'STATUS_UNKNOWN'
    OPEN = 'OPEN'
    USED = 'USED'
    RESERVED = 'RESERVED'


class CreditUsageType(Enum):
    CREDIT_USAGE_TYPE_UNKNOWN = 'CREDIT_USAGE_TYPE_UNKNOWN'
    COMPANY = 'COMPANY'
    PERSONAL = 'PERSONAL'


class CustomFieldResponseItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    response: str = Field(
        ...,
        description='The response of the custom field. It can be a text input from the user or a custom field code if the input is from a radio button or checkbox.',
        examples=['Leisure travel'],
    )
    additionalInput: str | None = Field(
        None,
        description='This contains additional input to the above response. It will only be used for checkbox with percentage and will contain the percentage value.',
        examples=['20'],
    )


class CustomFieldTravelerAccess(Enum):
    HIDDEN = 'HIDDEN'
    READ_ACCESS = 'READ_ACCESS'
    WRITE_ACCESS = 'WRITE_ACCESS'


class CustomFieldType(Enum):
    QUESTION = 'QUESTION'
    MEETING = 'MEETING'
    BUDGET = 'BUDGET'
    BREX_TOKEN = 'BREX_TOKEN'


class DateModel(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$') = (
        Field(..., examples=['2017-07-21'])
    )


class DateTimeLocal(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(
        pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$'
    ) = Field(..., examples=['2017-07-21T17:32'])


class DateTimeOffset(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(
        pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?(Z|([+-](0[0-9]|1[0-4]):([0-5][0-9])))$'
    ) = Field(..., examples=['2017-07-21T17:32Z'])


class DateTimeRange(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    min: DateTimeLocal | None = Field(None, description='Minimum value - inclusive.')
    max: DateTimeLocal | None = Field(None, description='Maximum value - inclusive.')


class DelayedInvoicingDescriptor(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    label: str = Field(
        ...,
        description='Label for delayed invoicing payment method',
        examples=['Delayed Invoicing for India LE'],
    )


class DelayedInvoicingDescriptorWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    delayedInvoicingDescriptor: DelayedInvoicingDescriptor | None = None


class DepartmentId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['631ccbcf-9414-5fe0-c234-b324dfbe7422'])


class Dimensions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    height: int | None = Field(None, examples=[120])
    width: int | None = Field(None, examples=[240])


class DirectBilling(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    directBillingCode: str | None = Field(
        None, description='Direct billing code provided by the vendor.'
    )
    corporateDiscountCode: str | None = Field(
        None, description='Corporate discount code provided by the vendor.'
    )
    label: str | None = Field(
        None, description='Label for the Direct Billing Payment method.'
    )


class DirectBillingWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    directBilling: DirectBilling | None = None


class DocumentType(Enum):
    BOARDING_PASS = 'BOARDING_PASS'
    CONFIRMATION = 'CONFIRMATION'
    INVOICE = 'INVOICE'
    VISA = 'VISA'
    MISCELLANEOUS = 'MISCELLANEOUS'
    OTHERS = 'OTHERS'
    TASK_PROCESSOR = 'TASK_PROCESSOR'
    EVENT_COVER_IMAGE = 'EVENT_COVER_IMAGE'
    LOGO_IMAGE = 'LOGO_IMAGE'


class DpanMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    label: str = Field(
        ..., description='Label for DPAN payment source.', examples=['AMEX-DPAN']
    )


class DpanWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    dpanMetadata: DpanMetadata


class EmailBucketType(Enum):
    BOOKING_CONFIRMATIONS = 'BOOKING_CONFIRMATIONS'
    BOOKING_CHANGES = 'BOOKING_CHANGES'
    FLIGHT_UPDATES = 'FLIGHT_UPDATES'
    APPROVAL_REQUESTS = 'APPROVAL_REQUESTS'
    APPROVAL_UPDATES = 'APPROVAL_UPDATES'
    REMINDERS = 'REMINDERS'


class EntityId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID


class EntityNonUUIDId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str | None = None


class EntityType(Enum):
    TRIP = 'TRIP'
    PNR = 'PNR'
    COMPANY = 'COMPANY'
    AIR_ITINERARY = 'AIR_ITINERARY'
    EVENT = 'EVENT'
    LOCATION_IMAGE = 'LOCATION_IMAGE'
    TICKETING_ERROR = 'TICKETING_ERROR'


class ErrorParameter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str | None = Field(None, description='Parameter name')
    value: str | None = Field(None, description='Parameter value')


class ErrorMessage(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    errorCode: str | None = Field(
        None, description='Error code to identify the specific errors.'
    )
    message: str | None = Field(
        None, description='Message containing details of error.'
    )
    errorParameters: Sequence[ErrorParameter] | None = Field(
        None, description='Error message parameters.'
    )
    errorDetail: str | None = Field(None, description='More details about the error.')


class ErrorResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    debugIdentifier: str | None = Field(
        None, description='Link to debug the error internally.'
    )
    errorMessages: Sequence[ErrorMessage] | None = None


class EventAllowedBookingType(Enum):
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    CAR = 'CAR'
    RAIL = 'RAIL'


class EventBookingStyle(Enum):
    SELF_SERVE = 'SELF_SERVE'
    AGENT_MANAGED = 'AGENT_MANAGED'


class EventBookingWindow(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    startDateTime: DateTimeLocal
    endDateTime: DateTimeLocal


class EventCompanyFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyId: CompanyId | None = Field(None, description='Company id')


class EventCompanyFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyFilter: EventCompanyFilter | None = None


class EventDateRangeFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventDateRange: DateTimeRange | None = Field(None, description='Event date range')


class EventDateRangeFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventDateRangeFilter: EventDateRangeFilter | None = None


class EventIdFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventIdList: Sequence[str] = Field(..., description='A list of event IDs.')


class EventIdFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventIdFilter: EventIdFilter | None = None


class EventJobItemStatus(Enum):
    SUCCESS = 'SUCCESS'
    FAILED = 'FAILED'


class EventJobStatus(Enum):
    IN_PROGRESS = 'IN PROGRESS'
    COMPLETED = 'COMPLETED'


class EventJobType(Enum):
    PUBLISH = 'PUBLISH'
    MODIFY = 'MODIFY'
    CANCEL = 'CANCEL'


class EventNameFilter(RootModel[str]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: str = Field(
        ...,
        description='Event name filter',
        examples=['Jaipur Offsite'],
        title='EventNameFilter',
    )


class EventNameFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventNameFilter: EventNameFilter | None = None


class EventParentFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    parentEventId: str = Field(
        ..., description="Parent event's id.", examples=['56789012']
    )


class EventParentFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventParentFilter: EventParentFilter | None = None


class EventRsvpState(Enum):
    ADDED = 'ADDED'
    INVITED = 'INVITED'
    INVITE_ACCEPTED = 'INVITE_ACCEPTED'
    INVITE_DECLINED = 'INVITE_DECLINED'
    REMOVED = 'REMOVED'


class EventRunningStatus(Enum):
    IN_PROGRESS = 'IN_PROGRESS'
    UPCOMING = 'UPCOMING'
    COMPLETED = 'COMPLETED'


class EventStatus(Enum):
    DRAFT = 'DRAFT'
    PUBLISH = 'PUBLISH'
    CANCELLED = 'CANCELLED'


class EventStatusFilter(RootModel[Sequence[EventStatus]]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Sequence[EventStatus] = Field(
        ..., description='Event status filter', title='EventStatusFilter'
    )


class EventStatusFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventStatusFilter: EventStatusFilter | None = None


class EventType(Enum):
    GENERIC = 'GENERIC'
    PROGRAM = 'PROGRAM'
    PROGRAM_SESSION = 'PROGRAM_SESSION'
    PROGRAM_TRIP = 'PROGRAM_TRIP'


class EventTypeFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    types: Sequence[EventType] | None = Field(None, title='EventType')


class EventTypeFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventTypeFilter: EventTypeFilter | None = None


class Expiry(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiryMonth: conint(ge=1, le=12) = Field(
        ..., description='Expiry month', examples=[1]
    )
    expiryYear: conint(ge=2000) = Field(..., description='Expiry year', examples=[2010])


class ExpiryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiry: Expiry | None = None


class FlightMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightId: str | None = Field(
        None,
        description='Unique identifier of the flight.',
        examples=['CgNERU4SA1NGTxoKNTQ1NzI5ODcxMQ'],
    )


class FlightMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightMetadata: FlightMetadata | None = None


class HotelAmenityType(Enum):
    TWENTY_FOUR_HOUR_FRONT_DESK = 'TWENTY_FOUR_HOUR_FRONT_DESK'
    TWENTY_FOUR_HOUR_ROOM_SERVICE = 'TWENTY_FOUR_HOUR_ROOM_SERVICE'
    TWENTY_FOUR_HOUR_SECURITY = 'TWENTY_FOUR_HOUR_SECURITY'
    ADJOINING_ROOMS = 'ADJOINING_ROOMS'
    AIR_CONDITIONING = 'AIR_CONDITIONING'
    AIRLINE_DESK = 'AIRLINE_DESK'
    ATM_CASH_MACHINE = 'ATM_CASH_MACHINE'
    BABY_SITTING = 'BABY_SITTING'
    BBQ_PICNIC_AREA = 'BBQ_PICNIC_AREA'
    BILINGUAL_STAFF = 'BILINGUAL_STAFF'
    BOOKSTORE = 'BOOKSTORE'
    BOUTIQUES_STORES = 'BOUTIQUES_STORES'
    BRAILED_ELEVATORS = 'BRAILED_ELEVATORS'
    BUSINESS_LIBRARY = 'BUSINESS_LIBRARY'
    CAR_RENTAL_DESK = 'CAR_RENTAL_DESK'
    CASINO = 'CASINO'
    CHECK_CASHING_POLICY = 'CHECK_CASHING_POLICY'
    CHECK_IN_KIOSK = 'CHECK_IN_KIOSK'
    COCKTAIL_LOUNGE = 'COCKTAIL_LOUNGE'
    COFFEE_SHOP = 'COFFEE_SHOP'
    COIN_OPERATED_LAUNDRY = 'COIN_OPERATED_LAUNDRY'
    CONCIERGE_DESK = 'CONCIERGE_DESK'
    CONCIERGE_FLOOR = 'CONCIERGE_FLOOR'
    CONFERENCE_FACILITIES = 'CONFERENCE_FACILITIES'
    COURTYARD = 'COURTYARD'
    CURRENCY_EXCHANGE = 'CURRENCY_EXCHANGE'
    DESK_WITH_ELECTRICAL_OUTLET = 'DESK_WITH_ELECTRICAL_OUTLET'
    DOCTOR_ON_CALL = 'DOCTOR_ON_CALL'
    DOOR_MAN = 'DOOR_MAN'
    DRIVING_RANGE = 'DRIVING_RANGE'
    DRUGSTORE_PHARMACY = 'DRUGSTORE_PHARMACY'
    DUTY_FREE_SHOP = 'DUTY_FREE_SHOP'
    ELEVATORS = 'ELEVATORS'
    EXECUTIVE_FLOOR = 'EXECUTIVE_FLOOR'
    EXERCISE_GYM = 'EXERCISE_GYM'
    EXPRESS_CHECK_IN = 'EXPRESS_CHECK_IN'
    EXPRESS_CHECK_OUT = 'EXPRESS_CHECK_OUT'
    FAMILY_PLAN = 'FAMILY_PLAN'
    FLORIST = 'FLORIST'
    FOLIOS = 'FOLIOS'
    FREE_AIRPORT_SHUTTLE = 'FREE_AIRPORT_SHUTTLE'
    FREE_PARKING = 'FREE_PARKING'
    FREE_TRANSPORTATION = 'FREE_TRANSPORTATION'
    GAME_ROOM = 'GAME_ROOM'
    GIFT_NEWS_STAND = 'GIFT_NEWS_STAND'
    HAIRDRESSER_BARBER = 'HAIRDRESSER_BARBER'
    ACCESSIBLE_FACILITIES = 'ACCESSIBLE_FACILITIES'
    HEALTH_CLUB = 'HEALTH_CLUB'
    HEATED_POOL = 'HEATED_POOL'
    HOUSEKEEPING_DAILY = 'HOUSEKEEPING_DAILY'
    HOUSEKEEPING_WEEKLY = 'HOUSEKEEPING_WEEKLY'
    ICE_MACHINE = 'ICE_MACHINE'
    INDOOR_PARKING = 'INDOOR_PARKING'
    INDOOR_POOL = 'INDOOR_POOL'
    JACUZZI = 'JACUZZI'
    JOGGING_TRACK = 'JOGGING_TRACK'
    KENNELS = 'KENNELS'
    LAUNDRY_VALET_SERVICE = 'LAUNDRY_VALET_SERVICE'
    LIQUOR_STORE = 'LIQUOR_STORE'
    LIVE_ENTERTAINMENT = 'LIVE_ENTERTAINMENT'
    MASSAGE_SERVICES = 'MASSAGE_SERVICES'
    NIGHTCLUB = 'NIGHTCLUB'
    OFF_SITE_PARKING = 'OFF_SITE_PARKING'
    ON_SITE_PARKING = 'ON_SITE_PARKING'
    OUTDOOR_PARKING = 'OUTDOOR_PARKING'
    OUTDOOR_POOL = 'OUTDOOR_POOL'
    PACKAGE_PARCEL_SERVICES = 'PACKAGE_PARCEL_SERVICES'
    PARKING = 'PARKING'
    PHOTOCOPY_CENTER = 'PHOTOCOPY_CENTER'
    PLAYGROUND = 'PLAYGROUND'
    POOL = 'POOL'
    POOLSIDE_SNACK_BAR = 'POOLSIDE_SNACK_BAR'
    PUBLIC_ADDRESS_SYSTEM = 'PUBLIC_ADDRESS_SYSTEM'
    RAMP_ACCESS = 'RAMP_ACCESS'
    RECREATIONAL_VEHICLE_PARKING = 'RECREATIONAL_VEHICLE_PARKING'
    RESTAURANT = 'RESTAURANT'
    ROOM_SERVICE = 'ROOM_SERVICE'
    SAFE_DEPOSIT_BOX = 'SAFE_DEPOSIT_BOX'
    SAUNA = 'SAUNA'
    SECURITY = 'SECURITY'
    SHOE_SHINE_STAND = 'SHOE_SHINE_STAND'
    SHOPPING_MALL = 'SHOPPING_MALL'
    SOLARIUM = 'SOLARIUM'
    SPA = 'SPA'
    SPORTS_BAR = 'SPORTS_BAR'
    STEAM_BATH = 'STEAM_BATH'
    STORAGE_SPACE = 'STORAGE_SPACE'
    SUNDRY_CONVENIENCE_STORE = 'SUNDRY_CONVENIENCE_STORE'
    TECHNICAL_CONCIERGE = 'TECHNICAL_CONCIERGE'
    THEATRE_DESK = 'THEATRE_DESK'
    TOUR_SIGHTSEEING_DESK = 'TOUR_SIGHTSEEING_DESK'
    TRANSLATION_SERVICES = 'TRANSLATION_SERVICES'
    TRAVEL_AGENCY = 'TRAVEL_AGENCY'
    TRUCK_PARKING = 'TRUCK_PARKING'
    VALET_CLEANING = 'VALET_CLEANING'
    DRY_CLEANING = 'DRY_CLEANING'
    VALET_PARKING = 'VALET_PARKING'
    VENDING_MACHINES = 'VENDING_MACHINES'
    VIDEO_TAPES = 'VIDEO_TAPES'
    WAKEUP_SERVICE = 'WAKEUP_SERVICE'
    WHEELCHAIR_ACCESS = 'WHEELCHAIR_ACCESS'
    WHIRLPOOL = 'WHIRLPOOL'
    MULTILINGUAL_STAFF = 'MULTILINGUAL_STAFF'
    WEDDING_SERVICES = 'WEDDING_SERVICES'
    BANQUET_FACILITIES = 'BANQUET_FACILITIES'
    BELL_STAFF_PORTER = 'BELL_STAFF_PORTER'
    BEAUTY_SHOP_SALON = 'BEAUTY_SHOP_SALON'
    COMPLIMENTARY_SELF_SERVICE_LAUNDRY = 'COMPLIMENTARY_SELF_SERVICE_LAUNDRY'
    DIRECT_DIAL_TELEPHONE = 'DIRECT_DIAL_TELEPHONE'
    FEMALE_TRAVELER_ROOM_FLOOR = 'FEMALE_TRAVELER_ROOM_FLOOR'
    PHARMACY = 'PHARMACY'
    STABLES = 'STABLES'
    ONE_TWENTY_AC = 'ONE_TWENTY_AC'
    ONE_TWENTY_DC = 'ONE_TWENTY_DC'
    TWO_TWENTY_AC = 'TWO_TWENTY_AC'
    ACCESSIBLE_PARKING = 'ACCESSIBLE_PARKING'
    TWO_TWENTY_DC = 'TWO_TWENTY_DC'
    BARBEQUE_GRILLS = 'BARBEQUE_GRILLS'
    WOMENS_CLOTHING = 'WOMENS_CLOTHING'
    MENS_CLOTHING = 'MENS_CLOTHING'
    CHILDRENS_CLOTHING = 'CHILDRENS_CLOTHING'
    SHOPS_AND_COMMERCIAL_SERVICES = 'SHOPS_AND_COMMERCIAL_SERVICES'
    VIDEO_GAMES = 'VIDEO_GAMES'
    SPORTS_BAR_OPEN_FOR_LUNCH = 'SPORTS_BAR_OPEN_FOR_LUNCH'
    SPORTS_BAR_OPEN_FOR_DINNER = 'SPORTS_BAR_OPEN_FOR_DINNER'
    ROOM_SERVICE_FULL_MENU = 'ROOM_SERVICE_FULL_MENU'
    ROOM_SERVICE_LIMITED_MENU = 'ROOM_SERVICE_LIMITED_MENU'
    ROOM_SERVICE_LIMITED_HOURS = 'ROOM_SERVICE_LIMITED_HOURS'
    VALET_SAME_DAY_DRY_CLEANING = 'VALET_SAME_DAY_DRY_CLEANING'
    BODY_SCRUB = 'BODY_SCRUB'
    BODY_WRAP = 'BODY_WRAP'
    PUBLIC_AREA_AIR_CONDITIONED = 'PUBLIC_AREA_AIR_CONDITIONED'
    EFOLIO_AVAILABLE_TO_COMPANY = 'EFOLIO_AVAILABLE_TO_COMPANY'
    INDIVIDUAL_EFOLIO_AVAILABLE = 'INDIVIDUAL_EFOLIO_AVAILABLE'
    VIDEO_REVIEW_BILLING = 'VIDEO_REVIEW_BILLING'
    BUTLER_SERVICE = 'BUTLER_SERVICE'
    COMPLIMENTARY_IN_ROOM_COFFEE_OR_TEA = 'COMPLIMENTARY_IN_ROOM_COFFEE_OR_TEA'
    COMPLIMENTARY_BUFFET_BREAKFAST = 'COMPLIMENTARY_BUFFET_BREAKFAST'
    COMPLIMENTARY_COCKTAILS = 'COMPLIMENTARY_COCKTAILS'
    COMPLIMENTARY_COFFEE_IN_LOBBY = 'COMPLIMENTARY_COFFEE_IN_LOBBY'
    COMPLIMENTARY_CONTINENTAL_BREAKFAST = 'COMPLIMENTARY_CONTINENTAL_BREAKFAST'
    COMPLIMENTARY_FULL_AMERICAN_BREAKFAST = 'COMPLIMENTARY_FULL_AMERICAN_BREAKFAST'
    DINNER_DELIVERY_SERVICE_FROM_LOCAL_RESTAURANT = (
        'DINNER_DELIVERY_SERVICE_FROM_LOCAL_RESTAURANT'
    )
    COMPLIMENTARY_NEWSPAPER_DELIVERED_TO_ROOM = (
        'COMPLIMENTARY_NEWSPAPER_DELIVERED_TO_ROOM'
    )
    COMPLIMENTARY_NEWSPAPER_IN_LOBBY = 'COMPLIMENTARY_NEWSPAPER_IN_LOBBY'
    COMPLIMENTARY_SHOESHINE = 'COMPLIMENTARY_SHOESHINE'
    EVENING_RECEPTION = 'EVENING_RECEPTION'
    FRONT_DESK = 'FRONT_DESK'
    GROCERY_SHOPPING_SERVICE_AVAILABLE = 'GROCERY_SHOPPING_SERVICE_AVAILABLE'
    HALAL_FOOD_AVAILABLE = 'HALAL_FOOD_AVAILABLE'
    KOSHER_FOOD_AVAILABLE = 'KOSHER_FOOD_AVAILABLE'
    LIMOUSINE_SERVICE = 'LIMOUSINE_SERVICE'
    MANAGERS_RECEPTION = 'MANAGERS_RECEPTION'
    MEDICAL_FACILITIES_SERVICE = 'MEDICAL_FACILITIES_SERVICE'
    TELEPHONE_JACK_ADAPTOR_AVAILABLE = 'TELEPHONE_JACK_ADAPTOR_AVAILABLE'
    ALL_INCLUSIVE_MEAL_PLAN = 'ALL_INCLUSIVE_MEAL_PLAN'
    BUFFET_BREAKFAST = 'BUFFET_BREAKFAST'
    COMMUNAL_BAR_AREA = 'COMMUNAL_BAR_AREA'
    CONTINENTAL_BREAKFAST = 'CONTINENTAL_BREAKFAST'
    FULL_MEAL_PLAN = 'FULL_MEAL_PLAN'
    FULL_AMERICAN_BREAKFAST = 'FULL_AMERICAN_BREAKFAST'
    MEAL_PLAN_AVAILABLE = 'MEAL_PLAN_AVAILABLE'
    MODIFIED_AMERICAN_MEAL_PLAN = 'MODIFIED_AMERICAN_MEAL_PLAN'
    FOOD_AND_BEVERAGE_OUTLETS = 'FOOD_AND_BEVERAGE_OUTLETS'
    LOUNGES_BARS = 'LOUNGES_BARS'
    BARBER_SHOP = 'BARBER_SHOP'
    VIDEO_CHECKOUT = 'VIDEO_CHECKOUT'
    ONSITE_LAUNDRY = 'ONSITE_LAUNDRY'
    TWENTY_FOUR_HOUR_FOOD_AND_BEVERAGE_KIOSK = (
        'TWENTY_FOUR_HOUR_FOOD_AND_BEVERAGE_KIOSK'
    )
    CONCIERGE_LOUNGE = 'CONCIERGE_LOUNGE'
    PARKING_FEE_MANAGED_BY_HOTEL = 'PARKING_FEE_MANAGED_BY_HOTEL'
    TRANSPORTATION = 'TRANSPORTATION'
    BREAKFAST_SERVED_IN_RESTAURANT = 'BREAKFAST_SERVED_IN_RESTAURANT'
    LUNCH_SERVED_IN_RESTAURANT = 'LUNCH_SERVED_IN_RESTAURANT'
    DINNER_SERVED_IN_RESTAURANT = 'DINNER_SERVED_IN_RESTAURANT'
    FULL_SERVICE_HOUSEKEEPING = 'FULL_SERVICE_HOUSEKEEPING'
    LIMITED_SERVICE_HOUSEKEEPING = 'LIMITED_SERVICE_HOUSEKEEPING'
    HIGH_SPEED_INTERNET_ACCESS_FOR_LAPTOP_IN_PUBLIC_AREAS = (
        'HIGH_SPEED_INTERNET_ACCESS_FOR_LAPTOP_IN_PUBLIC_AREAS'
    )
    WIRELESS_INTERNET_CONNECTION_IN_PUBLIC_AREAS = (
        'WIRELESS_INTERNET_CONNECTION_IN_PUBLIC_AREAS'
    )
    ADDITIONAL_SERVICES_AMENITIES_FACILITIES_ON_PROPERTY = (
        'ADDITIONAL_SERVICES_AMENITIES_FACILITIES_ON_PROPERTY'
    )
    TRANSPORTATION_SERVICES_LOCAL_AREA = 'TRANSPORTATION_SERVICES_LOCAL_AREA'
    TRANSPORTATION_SERVICES_LOCAL_OFFICE = 'TRANSPORTATION_SERVICES_LOCAL_OFFICE'
    DVD_VIDEO_RENTAL = 'DVD_VIDEO_RENTAL'
    PARKING_LOT = 'PARKING_LOT'
    PARKING_DECK = 'PARKING_DECK'
    STREET_SIDE_PARKING = 'STREET_SIDE_PARKING'
    COCKTAIL_LOUNGE_WITH_ENTERTAINMENT = 'COCKTAIL_LOUNGE_WITH_ENTERTAINMENT'
    COCKTAIL_LOUNGE_WITH_LIGHT_FARE = 'COCKTAIL_LOUNGE_WITH_LIGHT_FARE'
    MOTORCYCLE_PARKING = 'MOTORCYCLE_PARKING'
    PHONE_SERVICES = 'PHONE_SERVICES'
    BALLROOM = 'BALLROOM'
    BUS_PARKING = 'BUS_PARKING'
    CHILDRENS_PLAY_AREA = 'CHILDRENS_PLAY_AREA'
    CHILDRENS_NURSERY = 'CHILDRENS_NURSERY'
    DISCO = 'DISCO'
    EARLY_CHECK_IN = 'EARLY_CHECK_IN'
    LOCKER_ROOM = 'LOCKER_ROOM'
    NON_SMOKING_ROOMS_GENERIC = 'NON_SMOKING_ROOMS_GENERIC'
    TRAIN_ACCESS = 'TRAIN_ACCESS'
    AEROBICS_INSTRUCTION = 'AEROBICS_INSTRUCTION'
    BAGGAGE_HOLD = 'BAGGAGE_HOLD'
    BICYCLE_RENTALS = 'BICYCLE_RENTALS'
    DIETICIAN = 'DIETICIAN'
    LATE_CHECK_OUT_AVAILABLE = 'LATE_CHECK_OUT_AVAILABLE'
    PET_SITTING_SERVICES = 'PET_SITTING_SERVICES'
    PRAYER_MATS = 'PRAYER_MATS'
    SPORTS_TRAINER = 'SPORTS_TRAINER'
    TURNDOWN_SERVICE = 'TURNDOWN_SERVICE'
    DVDS_VIDEOS_CHILDREN = 'DVDS_VIDEOS_CHILDREN'
    BANK = 'BANK'
    LOBBY_COFFEE_SERVICE = 'LOBBY_COFFEE_SERVICE'
    BANKING_SERVICES = 'BANKING_SERVICES'
    STAIRWELLS = 'STAIRWELLS'
    PET_AMENITIES_AVAILABLE = 'PET_AMENITIES_AVAILABLE'
    EXHIBITION_CONVENTION_FLOOR = 'EXHIBITION_CONVENTION_FLOOR'
    LONG_TERM_PARKING = 'LONG_TERM_PARKING'
    CHILDREN_NOT_ALLOWED = 'CHILDREN_NOT_ALLOWED'
    CHILDREN_WELCOME = 'CHILDREN_WELCOME'
    COURTESY_CAR = 'COURTESY_CAR'
    HOTEL_DOES_NOT_PROVIDE_PORNOGRAPHIC_FILMS_TV = (
        'HOTEL_DOES_NOT_PROVIDE_PORNOGRAPHIC_FILMS_TV'
    )
    HOTSPOTS = 'HOTSPOTS'
    FREE_HIGH_SPEED_INTERNET_CONNECTION = 'FREE_HIGH_SPEED_INTERNET_CONNECTION'
    INTERNET_SERVICES = 'INTERNET_SERVICES'
    PETS_ALLOWED = 'PETS_ALLOWED'
    GOURMET_HIGHLIGHTS = 'GOURMET_HIGHLIGHTS'
    CATERING_SERVICES = 'CATERING_SERVICES'
    COMPLIMENTARY_BREAKFAST = 'COMPLIMENTARY_BREAKFAST'
    BUSINESS_CENTER = 'BUSINESS_CENTER'
    BUSINESS_SERVICES = 'BUSINESS_SERVICES'
    SECURED_PARKING = 'SECURED_PARKING'
    RACQUETBALL = 'RACQUETBALL'
    SNOW_SPORTS = 'SNOW_SPORTS'
    TENNIS_COURT = 'TENNIS_COURT'
    WATER_SPORTS = 'WATER_SPORTS'
    CHILD_PROGRAMS = 'CHILD_PROGRAMS'
    GOLF = 'GOLF'
    HORSEBACK_RIDING = 'HORSEBACK_RIDING'
    OCEANFRONT = 'OCEANFRONT'
    BEACHFRONT = 'BEACHFRONT'
    HAIR_DRYER = 'HAIR_DRYER'
    IRONING_BOARD = 'IRONING_BOARD'
    HEATED_GUEST_ROOMS = 'HEATED_GUEST_ROOMS'
    TOILET = 'TOILET'
    PARLOR = 'PARLOR'
    VIDEO_GAME_PLAYER = 'VIDEO_GAME_PLAYER'
    THALASSOTHERAPY = 'THALASSOTHERAPY'
    PRIVATE_DINING_FOR_GROUPS = 'PRIVATE_DINING_FOR_GROUPS'
    HEARING_IMPAIRED_SERVICES = 'HEARING_IMPAIRED_SERVICES'
    CARRYOUT_BREAKFAST = 'CARRYOUT_BREAKFAST'
    DELUXE_CONTINENTAL_BREAKFAST = 'DELUXE_CONTINENTAL_BREAKFAST'
    HOT_CONTINENTAL_BREAKFAST = 'HOT_CONTINENTAL_BREAKFAST'
    HOT_BREAKFAST = 'HOT_BREAKFAST'
    PRIVATE_POOL = 'PRIVATE_POOL'
    CONNECTING_ROOMS = 'CONNECTING_ROOMS'
    DATA_PORT = 'DATA_PORT'
    EXTERIOR_CORRIDORS = 'EXTERIOR_CORRIDORS'
    GULF_VIEW = 'GULF_VIEW'
    ACCESSIBLE_ROOMS = 'ACCESSIBLE_ROOMS'
    HIGH_SPEED_INTERNET_ACCESS = 'HIGH_SPEED_INTERNET_ACCESS'
    INTERIOR_CORRIDORS = 'INTERIOR_CORRIDORS'
    HIGH_SPEED_WIRELESS = 'HIGH_SPEED_WIRELESS'
    KITCHENETTE = 'KITCHENETTE'
    PRIVATE_BATH_OR_SHOWER = 'PRIVATE_BATH_OR_SHOWER'
    FIRE_SAFETY_COMPLIANT = 'FIRE_SAFETY_COMPLIANT'
    WELCOME_DRINK = 'WELCOME_DRINK'
    BOARDING_PASS_PRINT_OUT_AVAILABLE = 'BOARDING_PASS_PRINT_OUT_AVAILABLE'
    PRINTING_SERVICES_AVAILABLE = 'PRINTING_SERVICES_AVAILABLE'
    ALL_PUBLIC_AREAS_NON_SMOKING = 'ALL_PUBLIC_AREAS_NON_SMOKING'
    MEETING_ROOMS = 'MEETING_ROOMS'
    MOVIES_IN_ROOM = 'MOVIES_IN_ROOM'
    SECRETARIAL_SERVICE = 'SECRETARIAL_SERVICE'
    SNOW_SKIING = 'SNOW_SKIING'
    WATER_SKIING = 'WATER_SKIING'
    FAX_SERVICE = 'FAX_SERVICE'
    GREAT_ROOM = 'GREAT_ROOM'
    LOBBY = 'LOBBY'
    MULTIPLE_PHONE_LINES_BILLED_SEPARATELY = 'MULTIPLE_PHONE_LINES_BILLED_SEPARATELY'
    UMBRELLAS = 'UMBRELLAS'
    GAS_STATION = 'GAS_STATION'
    GROCERY_STORE = 'GROCERY_STORE'
    TWENTY_FOUR_HOUR_COFFEE_SHOP = 'TWENTY_FOUR_HOUR_COFFEE_SHOP'
    AIRPORT_SHUTTLE_SERVICE = 'AIRPORT_SHUTTLE_SERVICE'
    LUGGAGE_SERVICE = 'LUGGAGE_SERVICE'
    PIANO_BAR = 'PIANO_BAR'
    VIP_SECURITY = 'VIP_SECURITY'
    COMPLIMENTARY_WIRELESS_INTERNET = 'COMPLIMENTARY_WIRELESS_INTERNET'
    CONCIERGE_BREAKFAST = 'CONCIERGE_BREAKFAST'
    SAME_GENDER_FLOOR = 'SAME_GENDER_FLOOR'
    CHILDREN_PROGRAMS = 'CHILDREN_PROGRAMS'
    BUILDING_MEETS_LOCAL_STATE_AND_COUNTRY_BUILDING_CODES = (
        'BUILDING_MEETS_LOCAL_STATE_AND_COUNTRY_BUILDING_CODES'
    )
    INTERNET_BROWSER_ON_TV = 'INTERNET_BROWSER_ON_TV'
    NEWSPAPER = 'NEWSPAPER'
    PARKING_CONTROLLED_ACCESS_GATES_TO_ENTER_PARKING_AREA = (
        'PARKING_CONTROLLED_ACCESS_GATES_TO_ENTER_PARKING_AREA'
    )
    HOTEL_SAFE_DEPOSIT_BOX_NOT_ROOM_SAFE_BOX = (
        'HOTEL_SAFE_DEPOSIT_BOX_NOT_ROOM_SAFE_BOX'
    )
    STORAGE_SPACE_AVAILABLE_FEE = 'STORAGE_SPACE_AVAILABLE_FEE'
    TYPE_OF_ENTRANCES_TO_GUEST_ROOMS = 'TYPE_OF_ENTRANCES_TO_GUEST_ROOMS'
    BEVERAGE_COCKTAIL = 'BEVERAGE_COCKTAIL'
    CELL_PHONE_RENTAL = 'CELL_PHONE_RENTAL'
    COFFEE_TEA = 'COFFEE_TEA'
    EARLY_CHECK_IN_GUARANTEE = 'EARLY_CHECK_IN_GUARANTEE'
    FOOD_AND_BEVERAGE_DISCOUNT = 'FOOD_AND_BEVERAGE_DISCOUNT'
    LATE_CHECK_OUT_GUARANTEE = 'LATE_CHECK_OUT_GUARANTEE'
    ROOM_UPGRADE_CONFIRMED = 'ROOM_UPGRADE_CONFIRMED'
    ROOM_UPGRADE_ON_AVAILABILITY = 'ROOM_UPGRADE_ON_AVAILABILITY'
    SHUTTLE_TO_LOCAL_BUSINESSES = 'SHUTTLE_TO_LOCAL_BUSINESSES'
    SHUTTLE_TO_LOCAL_ATTRACTIONS = 'SHUTTLE_TO_LOCAL_ATTRACTIONS'
    SOCIAL_HOUR = 'SOCIAL_HOUR'
    VIDEO_BILLING = 'VIDEO_BILLING'
    WELCOME_GIFT = 'WELCOME_GIFT'
    HYPOALLERGENIC_ROOMS = 'HYPOALLERGENIC_ROOMS'
    ROOM_AIR_FILTRATION = 'ROOM_AIR_FILTRATION'
    SMOKE_FREE_PROPERTY = 'SMOKE_FREE_PROPERTY'
    WATER_PURIFICATION_SYSTEM_IN_USE = 'WATER_PURIFICATION_SYSTEM_IN_USE'
    POOLSIDE_SERVICE = 'POOLSIDE_SERVICE'
    CLOTHING_STORE = 'CLOTHING_STORE'
    ELECTRIC_CAR_CHARGING_STATIONS = 'ELECTRIC_CAR_CHARGING_STATIONS'
    OFFICE_RENTAL = 'OFFICE_RENTAL'
    PIANO = 'PIANO'
    INCOMING_FAX = 'INCOMING_FAX'
    OUTGOING_FAX = 'OUTGOING_FAX'
    SEMI_PRIVATE_SPACE = 'SEMI_PRIVATE_SPACE'
    LOADING_DOCK = 'LOADING_DOCK'
    BABY_KIT = 'BABY_KIT'
    CHILDRENS_BREAKFAST = 'CHILDRENS_BREAKFAST'
    CLOAKROOM_SERVICE = 'CLOAKROOM_SERVICE'
    COFFEE_LOUNGE = 'COFFEE_LOUNGE'
    EVENTS_TICKET_SERVICE = 'EVENTS_TICKET_SERVICE'
    LATE_CHECK_IN = 'LATE_CHECK_IN'
    LIMITED_PARKING = 'LIMITED_PARKING'
    OUTDOOR_SUMMER_BAR_CAFE = 'OUTDOOR_SUMMER_BAR_CAFE'
    NO_PARKING_AVAILABLE = 'NO_PARKING_AVAILABLE'
    BEER_GARDEN = 'BEER_GARDEN'
    GARDEN_LOUNGE_BAR = 'GARDEN_LOUNGE_BAR'
    SUMMER_TERRACE = 'SUMMER_TERRACE'
    WINTER_TERRACE = 'WINTER_TERRACE'
    ROOF_TERRACE = 'ROOF_TERRACE'
    BEACH_BAR = 'BEACH_BAR'
    HELICOPTER_SERVICE = 'HELICOPTER_SERVICE'
    FERRY = 'FERRY'
    TAPAS_BAR = 'TAPAS_BAR'
    CAFE_BAR = 'CAFE_BAR'
    SNACK_BAR = 'SNACK_BAR'
    GUESTROOM_WIRED_INTERNET = 'GUESTROOM_WIRED_INTERNET'
    GUESTROOM_WIRELESS_INTERNET = 'GUESTROOM_WIRELESS_INTERNET'
    FITNESS_CENTER = 'FITNESS_CENTER'
    ALCOHOLIC_BEVERAGES = 'ALCOHOLIC_BEVERAGES'
    NON_ALCOHOLIC_BEVERAGES = 'NON_ALCOHOLIC_BEVERAGES'
    HEALTH_AND_BEAUTY_SERVICES = 'HEALTH_AND_BEAUTY_SERVICES'
    LOCAL_CALLS = 'LOCAL_CALLS'
    MINIBAR = 'MINIBAR'
    REFRIGERATOR = 'REFRIGERATOR'
    IN_ROOM_SAFE = 'IN_ROOM_SAFE'
    SMOKING_ROOMS_AVAILBLE = 'SMOKING_ROOMS_AVAILBLE'
    MOUNTAIN_VIEW = 'MOUNTAIN_VIEW'
    POOL_VIEW = 'POOL_VIEW'
    BEACH_VIEW = 'BEACH_VIEW'
    OCEAN_VIEW = 'OCEAN_VIEW'
    ROOMS_WITH_BALCONY = 'ROOMS_WITH_BALCONY'
    FAMILY_ROOM = 'FAMILY_ROOM'
    CRIB_CHARGE = 'CRIB_CHARGE'
    ROLLAWAY_ADULT = 'ROLLAWAY_ADULT'
    FREE_WIFI_IN_MEETING_ROOMS = 'FREE_WIFI_IN_MEETING_ROOMS'
    ECO_FRIENDLY = 'ECO_FRIENDLY'
    EXTRA_PERSON = 'EXTRA_PERSON'
    STAY_SAFE = 'STAY_SAFE'
    ENHANCED_HYGIENE_CLEANLINESS_PROTOCOLS = 'ENHANCED_HYGIENE_CLEANLINESS_PROTOCOLS'


class HotelBookingPaymentGuidelines(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    onlyVpayEnabledBooking: bool | None = Field(
        None,
        description='Whether only Vpay enabled bookings are allowed for the event.',
        examples=[True],
    )


class HotelBookingStatusFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['HOTEL_BOOKING_STATUS_FILTER'] = Field(
        'HOTEL_BOOKING_STATUS_FILTER', description='Type of filter'
    )
    bookingStatus: Sequence[BookingStatusType] = Field(
        ..., description='Hotel Booking status'
    )


class Type1(Enum):
    GENERAL = 'GENERAL'
    ALERTS = 'ALERTS'
    DINING = 'DINING'
    FACILITIES = 'FACILITIES'
    RECREATION = 'RECREATION'
    SERVICES = 'SERVICES'
    ATTRACTIONS = 'ATTRACTIONS'
    CANCELLATION_POLICY = 'CANCELLATION_POLICY'
    DEPOSIT_POLICY = 'DEPOSIT_POLICY'
    DIRECTIONS = 'DIRECTIONS'
    POLICIES = 'POLICIES'
    SAFETY = 'SAFETY'
    TRANSPORTATION = 'TRANSPORTATION'


class HotelDescription(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type1 | None = Field(
        None, description='Hotel description type', examples=['']
    )
    value: str | None = Field(
        None, description='Hotel description value', examples=['']
    )


class HotelImageCategory(Enum):
    UNKNOWN_CATEGORY = 'UNKNOWN_CATEGORY'
    EXTERIOR_VIEW = 'EXTERIOR_VIEW'
    LOBBY_VIEW = 'LOBBY_VIEW'
    POOL_VIEW = 'POOL_VIEW'
    RESTAURANT = 'RESTAURANT'
    HEALTH_CLUB = 'HEALTH_CLUB'
    GUEST_ROOM = 'GUEST_ROOM'
    SUITE = 'SUITE'
    MEETING_ROOM = 'MEETING_ROOM'
    BALLROOM = 'BALLROOM'
    GOLF_COURSE = 'GOLF_COURSE'
    BEACH = 'BEACH'
    SPA = 'SPA'
    BAR_OR_LOUNGE = 'BAR_OR_LOUNGE'
    RECREATIONAL_FACILITY = 'RECREATIONAL_FACILITY'
    LOGO = 'LOGO'
    BASICS = 'BASICS'
    MAP = 'MAP'
    PROMOTIONAL = 'PROMOTIONAL'
    HOT_NEWS = 'HOT_NEWS'
    MISCELLANEOUS = 'MISCELLANEOUS'
    GUEST_ROOM_AMENITY = 'GUEST_ROOM_AMENITY'
    PROPERTY_AMENITY = 'PROPERTY_AMENITY'
    BUSINESS_CENTER = 'BUSINESS_CENTER'


class HotelItineraryId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    priceValidateKey: str = Field(..., description='Price Validate key')


class HotelRsvpResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    notNeeded: bool | None = Field(
        None,
        description='Whether hotel booking is needed by the traveler or not',
        examples=[True],
    )


class Image(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    data: str | None = Field(None, examples=['6935813e12584abda0e43d71cd2ea260'])
    dimensions: Dimensions | None = None
    url: str | None = Field(
        None,
        examples=[
            'https://static.wixstatic.com/media/73f2e2_6935813e12584abda0e43d71cd2ea260~mv2.png/v1/fill/w_630,h_94,al_c,q_85,usm_0.66_1.00_0.01/Spotnana%403x.webp'
        ],
    )


class ImageGroup(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    caption: str | None = Field(
        None, description='Caption for the image.', examples=['Exterior']
    )
    images: Sequence[Image] = Field(..., description='List of images.')


class InvitationStatusFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['INVITATION_STATUS_FILTER'] = 'INVITATION_STATUS_FILTER'
    rsvpStates: Sequence[EventRsvpState] = Field(
        ..., description='List of rsvp states for which travelers need to be fetched'
    )


class JobIdFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['JOB_ID_FILTER'] = Field(
        'JOB_ID_FILTER',
        description='Used to distinguish which type of filter is applied.',
    )
    jobId: UUID = Field(
        ...,
        description='Job Id for the async event job.',
        examples=['f7b3b3b3-7b3b-4b3b-8b3b-3b3b3b3b3b3b'],
    )


class JobItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventId: str | None = Field(
        None, description='Unique identifier for the event.', examples=['56789012']
    )
    status: EventJobItemStatus | None = None
    message: str | None = Field(None, description='Error message')


class Latlng(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    latitude: float = Field(
        ..., description='Latitude of the Location', examples=[77.1025]
    )
    longitude: float = Field(
        ..., description='Longitude of the Location', examples=[28.7041]
    )


class LegMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legId: str | None = Field(
        None,
        description='Unique identifier of the leg.',
        examples=['CgNTRk8SA0RFThoKNTQ1NzI5ODcxMQ=='],
    )


class LegMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legMetadata: LegMetadata | None = None


class LegalEntityId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['fc1ccbce-8413-4fe9-b233-a324dfbe7421'])


class LimoItineraryId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    limoId: str = Field(..., description='Selected Limo option')


class ListEventType(Enum):
    UPCOMING = 'UPCOMING'
    PAST_OR_COMPLETED = 'PAST_OR_COMPLETED'
    CANCELLED_EVENT = 'CANCELLED_EVENT'


class Visibility(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    showToTravelers: bool | None = Field(
        False,
        description='If true, managed travelers can see this payment method when booking for themselves. If false, only the Travel Arranger can see it.',
    )


class ManagedTravelerPaymentAccess(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    enabled: bool = Field(
        ...,
        description='Indicates if this payment method can be used on the Traveler Profiles being managed by this User.',
    )
    visibility: Visibility | None = Field(
        None,
        description='Controls how this payment method is displayed on checkout for managed traveler profiles, applicable only if sharing is enabled.',
    )


class NameSuffix(Enum):
    NAME_SUFFIX_UNKNOWN = 'NAME_SUFFIX_UNKNOWN'
    SR = 'SR'
    JR = 'JR'
    MD = 'MD'
    PHD = 'PHD'
    II = 'II'
    III = 'III'
    IV = 'IV'
    DO = 'DO'
    ATTY = 'ATTY'
    V = 'V'
    VI = 'VI'
    ESQ = 'ESQ'
    DC = 'DC'
    DDS = 'DDS'
    VM = 'VM'
    JD = 'JD'
    SECOND = 'SECOND'
    THIRD = 'THIRD'


class OffsetBasedPaginationResponseParams(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalNumResults: int = Field(..., description='Total number of results.')


class OperationStatus(Enum):
    SUCCESS = 'SUCCESS'
    FAILED = 'FAILED'
    PARTIAL_SUCCESS = 'PARTIAL_SUCCESS'


class OverrideBehavior(Enum):
    OVERRIDE_ALLOWED = 'OVERRIDE_ALLOWED'
    OVERRIDE_NOT_ALLOWED = 'OVERRIDE_NOT_ALLOWED'
    OVERRIDE_MANDATORY = 'OVERRIDE_MANDATORY'


class OwnershipLabel(Enum):
    CORPORATE = 'CORPORATE'
    PERSONAL = 'PERSONAL'
    CENTRAL = 'CENTRAL'


class PaymentEventFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventIds: Sequence[str] = Field(..., description='Event ids List')
    companyId: CompanyId = Field(..., description='The company Id for the filter.')


class PaymentEventFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventFilter: PaymentEventFilter | None = None


class PaymentMethod(Enum):
    PAYMENT_METHOD_UNKNOWN = 'PAYMENT_METHOD_UNKNOWN'
    CREDIT_CARD = 'CREDIT_CARD'
    BREX_POINTS = 'BREX_POINTS'
    CASH = 'CASH'
    QANTAS_POINTS = 'QANTAS_POINTS'
    VENDOR_PROGRAM_PAYMENT = 'VENDOR_PROGRAM_PAYMENT'
    DELAYED_INVOICING = 'DELAYED_INVOICING'
    FLIGHT_CREDITS = 'FLIGHT_CREDITS'
    QANTAS_TRAVEL_FUND = 'QANTAS_TRAVEL_FUND'
    CUSTOM_VIRTUAL_PAYMENT = 'CUSTOM_VIRTUAL_PAYMENT'


class PaymentSourceDepartmentFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyId: CompanyId = Field(..., description='company id')
    departmentIds: Sequence[DepartmentId] = Field(
        ..., description='List of department ids'
    )


class PaymentSourceStatus(Enum):
    ACTIVE = 'ACTIVE'
    INCOMPLETE = 'INCOMPLETE'


class PaymentSourceType(Enum):
    CARD = 'CARD'
    VIRTUAL_CARD = 'VIRTUAL_CARD'
    REWARDS_PROGRAM = 'REWARDS_PROGRAM'
    DELAYED_INVOICING = 'DELAYED_INVOICING'
    CUSTOM_PAYMENT_METHOD = 'CUSTOM_PAYMENT_METHOD'
    VENDOR_PROGRAM_PAYMENT = 'VENDOR_PROGRAM_PAYMENT'
    UNUSED_CREDIT = 'UNUSED_CREDIT'
    CASH = 'CASH'


class Persona(Enum):
    UNKNOWN_PERSONA = 'UNKNOWN_PERSONA'
    EMPLOYEE = 'EMPLOYEE'
    GUEST = 'GUEST'
    PERSONAL = 'PERSONAL'
    RELATIVE = 'RELATIVE'
    ADHOC = 'ADHOC'


class CountryCodeSource(Enum):
    UNSPECIFIED = 'UNSPECIFIED'
    FROM_NUMBER_WITH_PLUS_SIGN = 'FROM_NUMBER_WITH_PLUS_SIGN'
    FROM_NUMBER_WITH_IDD = 'FROM_NUMBER_WITH_IDD'
    FROM_NUMBER_WITHOUT_PLUS_SIGN = 'FROM_NUMBER_WITHOUT_PLUS_SIGN'
    FROM_DEFAULT_COUNTRY = 'FROM_DEFAULT_COUNTRY'


class Type2(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    MOBILE = 'MOBILE'
    LANDLINE = 'LANDLINE'


class PhoneNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    countryCode: int | None = Field(
        None, description='two digit country code', examples=[91]
    )
    countryCodeSource: CountryCodeSource | None = Field(
        None, examples=['FROM_NUMBER_WITH_PLUS_SIGN']
    )
    extension: str | None = Field(
        None, description='phone number extension', examples=['222']
    )
    isoCountryCode: str | None = Field(
        None, description='ISO alpha-2 code', examples=['IN']
    )
    italianLeadingZero: bool | None = Field(False, examples=[True])
    nationalNumber: int | None = Field(None, examples=[8150])
    numberOfLeadingZeros: int | None = Field(0, examples=[1])
    preferredDomesticCarrierCode: str | None = Field(None, examples=['7'])
    rawInput: str | None = Field(None, examples=['77777'])
    type: Type2 | None = Field(None, examples=['MOBILE'])


class PnrMetadata(RootModel[FlightMetadataWrapper | LegMetadataWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: FlightMetadataWrapper | LegMetadataWrapper = Field(
        ...,
        description='Metadata when document is associated to pnr entity.',
        title='PnrMetadata',
    )


class InvoiceType(Enum):
    SERVICE_FEE_INVOICE = 'SERVICE_FEE_INVOICE'
    FARE_INVOICE = 'FARE_INVOICE'
    GENERIC_INVOICE = 'GENERIC_INVOICE'


class InvoiceMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    invoiceNumber: str = Field(..., examples=['SPOT-0001'])
    invoiceType: InvoiceType | None = Field(None, examples=['FARE_INVOICE'])


class PostalAddress(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    addressLines: Sequence[str] = Field(..., description='Address lines')
    administrativeArea: str | None = Field(
        None,
        description='Code of administrative area. For example: DL for Delhi, India.\nHighest administrative subdivision which is used for postal\naddresses of a country or region.\nFor example, this can be a state, a province, an oblast, or a prefecture.\nSpecifically, for Spain this is the province and not the autonomous\ncommunity (e.g. "Barcelona" and not "Catalonia").\nMany countries don\'t use an administrative area in postal addresses. E.g.\nin Switzerland this should be left unpopulated.\n',
        examples=['CA'],
    )
    administrativeAreaName: str | None = Field(
        None,
        description='Name of administrative area. This is full name corresponding to administrativeArea. \nLike Delhi for DL area code. For some places, code and name maybe same as well like Tokyo.\n',
        examples=['California'],
    )
    description: str | None = Field(
        None, description='Address description', examples=['San Francisco Home']
    )
    isDefault: bool | None = Field(
        None,
        description='Whether this address is default address in case multiple addresses are specified.',
        examples=[True],
    )
    languageCode: str | None = Field(
        None,
        description='BCP-47 language code of the contents of this address (if known). This is often the UI \nlanguage of the input form or is expected to match one of the languages used in the \naddress\' country/region, or their transliterated equivalents.\nThis can affect formatting in certain countries, but is not critical to the correctness \nof the data and will never affect any validation or other non-formatting related operations.\nExamples: "zh-Hant", "ja", "ja-Latn", "en".\n',
        examples=['en'],
    )
    locality: str | None = Field(
        None,
        description='Generally refers to the city/town portion of the address.',
        examples=['San Francisco'],
    )
    locationCode: str | None = Field(
        None,
        description='IATA 3-letter location code. See https://www.iata.org/en/services/codes.',
        examples=['LAX'],
    )
    organization: str | None = Field(
        None,
        description='The name of the organization at the address.',
        examples=['Spotnana'],
    )
    postalCode: str | None = Field(
        None,
        description='Postal code of the address. This is a required field when setting for a user/legal entity/company etc.',
        examples=['94130'],
    )
    continentCode: str | None = Field(
        None,
        description='2 letter continent code of the continent this address falls in.',
        examples=['AF'],
    )
    recipients: Sequence[str] | None = Field(
        None, description='The recipient at the address.'
    )
    regionCode: str = Field(
        ...,
        description='Region code of the country/region of the address.',
        examples=['US'],
    )
    regionName: str | None = Field(
        None,
        description='Region name of the country/region of the address.',
        examples=['America'],
    )
    revision: int | None = Field(None, examples=[1])
    sortingCode: str | None = Field(
        None,
        description='Additional, country-specific, sorting code. This is not used\nin most regions. Where it is used, the value is either a string like\n"CEDEX", optionally followed by a number (e.g. "CEDEX 7"), or just a number\nalone, representing the "sector code" (Jamaica), "delivery area indicator"\n(Malawi) or "post office indicator" (e.g. Côte d\'Ivoire).\n',
        examples=['Jamaica'],
    )
    sublocality: str | None = Field(
        None,
        description='Sublocality of the address. This can be neighborhoods, boroughs, districts.',
    )
    timezone: str | None = Field(
        None, description='Time zone of the address.', examples=['America/Los_Angeles']
    )
    coordinates: Latlng | None = Field(
        None, description='Map coordinates of the address.'
    )


class ProgramDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    uscId: str = Field(..., description='Contract Id between vendor and the client.')
    tourCode: str = Field(
        ...,
        description='Tracking code to know which company has booked the ticket, it is added to the price quote and ticket.',
    )
    snapCode: str = Field(..., description='Discount code.')


class PublishEventResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(
        ..., description='Unique identifier for the event.', examples=['56789012']
    )
    status: OperationStatus
    message: str | None = Field(
        None,
        description='Details about success or failure reason.',
        examples=['Published successfully'],
    )


class QantasTravelFund(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    label: str = Field(
        ...,
        description='Label for Qantas Travel Fund payment source.',
        examples=['QANTAS_TRAVEL_FUND'],
    )


class QantasTravelFundWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    qantasTravelFund: QantasTravelFund


class RailBookingGuideline(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    arrivalBookingWindow: EventBookingWindow | None = Field(
        None, description='Booking window for rail-in-event.'
    )
    departureBookingWindow: EventBookingWindow | None = Field(
        None, description='Booking window for rail-out-event.'
    )


class RailBookingGuidelineWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    railBookingGuideLine: RailBookingGuideline | None = None


class RailBookingStatusFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['RAIL_BOOKING_STATUS_FILTER'] = Field(
        'RAIL_BOOKING_STATUS_FILTER', description='Type of filter'
    )
    bookingStatus: Sequence[BookingStatusType] = Field(
        ..., description='Rail Booking status'
    )


class RailItineraryId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchKey: str = Field(..., description='Search key')


class RailRsvpResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    notNeeded: bool | None = Field(
        None,
        description='Whether rail booking is needed by the traveler or not',
        examples=[True],
    )


class Reference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID
    name: str | None = None


class ReferenceIdFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['REFERENCE_ID_FILTER'] = Field(
        'REFERENCE_ID_FILTER',
        description='Used to distinguish which type of filter is applied.',
    )
    referenceId: str = Field(
        ...,
        description='Reference Id associated with the job.',
        examples=['8859676988'],
    )
    type: EventJobType | None = Field(None, description='Job type filter.')


class Type3(Enum):
    EMPLOYEE_ID = 'EMPLOYEE_ID'
    COST_CENTER_ID = 'COST_CENTER_ID'
    LEGAL_ENTITY_ID = 'LEGAL_ENTITY_ID'


class ReportingAttribute(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type3 | None = Field(
        None, description='Type of ReportingAttribute', examples=['LEGAL_ENTITY_ID']
    )


class RewardsProgramType(Enum):
    BREX_POINTS = 'BREX_POINTS'
    QANTAS_POINTS = 'QANTAS_POINTS'


class RoleType(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    COMPANY_ADMIN = 'COMPANY_ADMIN'
    COMPANY_TRAVEL_ARRANGER = 'COMPANY_TRAVEL_ARRANGER'
    TRAVEL_ARRANGER = 'TRAVEL_ARRANGER'
    COMPANY_REPORT_ADMIN = 'COMPANY_REPORT_ADMIN'
    GLOBAL_ADMIN = 'GLOBAL_ADMIN'
    GLOBAL_AGENT = 'GLOBAL_AGENT'
    TMC_AGENT = 'TMC_AGENT'
    TMC_ADMIN = 'TMC_ADMIN'


class SpendType(RootModel[Literal['SERVICE_FEE']]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Literal['SERVICE_FEE'] = Field(
        'SERVICE_FEE', description='Spend type', title='SpendType'
    )


class SplitOptionByCardCompany(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cardCompanyCode: str = Field(
        ...,
        description='Card company code which allows splitting the payment',
        examples=['VI, TP'],
    )
    splitWithCardCompanyCodes: Sequence[str] | None = None


class TemplatePaymentConfig(Enum):
    TRAVELER_DEFAULT = 'TRAVELER_DEFAULT'
    PERSONAL = 'PERSONAL'
    SELECTED_METHODS = 'SELECTED_METHODS'


class HotelCodeType(Enum):
    SABRE_CSL = 'SABRE_CSL'
    SABRE_TN = 'SABRE_TN'
    EXPEDIA_RAPID = 'EXPEDIA_RAPID'
    MARRIOTT = 'MARRIOTT'
    GIATA = 'GIATA'


class ThirdPartyHotelCode(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelCode: str = Field(..., description='Third party hotel code.')
    hotelCodeType: HotelCodeType = Field(..., description='Type of the third party.')


class ThirdPartySource(Enum):
    UNKNOWN_SOURCE = 'UNKNOWN_SOURCE'
    SABRE = 'SABRE'
    TRAVEL_FUSION = 'TRAVEL_FUSION'
    AVIA = 'AVIA'
    NDC = 'NDC'
    TRAINLINE = 'TRAINLINE'
    ATPCO_NDC = 'ATPCO_NDC'
    FARELOGIX_NDC = 'FARELOGIX_NDC'
    OFFLINE = 'OFFLINE'
    CONNEXUS = 'CONNEXUS'
    ROUTEHAPPY = 'ROUTEHAPPY'
    AMADEUS = 'AMADEUS'
    GIATA = 'GIATA'
    QBR = 'QBR'
    BCD = 'BCD'
    QANTAS_HOTELS = 'QANTAS_HOTELS'
    SOUTHWEST = 'SOUTHWEST'
    EXPEDIA = 'EXPEDIA'
    HOTEL_HUB = 'HOTEL_HUB'
    MARRIOTT = 'MARRIOTT'
    CLEARTRIP = 'CLEARTRIP'
    KYTE = 'KYTE'
    GROUNDSPAN = 'GROUNDSPAN'
    SABRE_NDC = 'SABRE_NDC'
    BOOKING_COM = 'BOOKING_COM'


class TmcFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyId: CompanyId = Field(..., description='Tmc id')


class TmcFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tmcFilter: TmcFilter | None = None


class TokenizedExpiry(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiryMonth: str = Field(
        ..., description='Tokenized Expiry month', examples=['KvAuPANQWCpjwRQxcC8EXg==']
    )
    expiryYear: str = Field(
        ..., description='Tokenized Expiry year', examples=['fPBm0OWrKwPyIrCVcbg4cA==']
    )


class TokenizedExpiryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tokenizedExpiry: TokenizedExpiry | None = None


class TravelType(Enum):
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    CAR = 'CAR'
    RAIL = 'RAIL'
    LIMO = 'LIMO'
    MISC = 'MISC'
    ALL = 'ALL'


class TravelerSearchFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['TRAVELER_SEARCH_FILTER'] = 'TRAVELER_SEARCH_FILTER'
    searchTerm: str | None = Field(None, examples=['name'])


class TripUsageType(Enum):
    STANDARD = 'STANDARD'
    EVENT = 'EVENT'


class SegmentsAvailable(Enum):
    UNKNOWN = 'UNKNOWN'
    ALL_OPEN = 'ALL_OPEN'
    PARTIAL = 'PARTIAL'
    OTHER = 'OTHER'


class TicketType(Enum):
    TICKET_TYPE_UNKNOWN = 'TICKET_TYPE_UNKNOWN'
    ETICKET = 'ETICKET'
    MCO = 'MCO'
    NON_GDS = 'NON_GDS'


class RedeemVia(Enum):
    REDEEM_VIA_OBT = 'REDEEM_VIA_OBT'
    CONTACT_AGENT = 'CONTACT_AGENT'


class SourceOfTruth(Enum):
    SPOTNANA = 'SPOTNANA'
    MANUAL_FORM = 'MANUAL_FORM'


class UpdateEventResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(
        ..., description='Unique identifier for the event.', examples=['56789012']
    )
    status: OperationStatus
    message: str | None = Field(
        None,
        description='Details about success or failure reason.',
        examples=['Updated successfully'],
    )


class UserFacingStatus(Enum):
    UNKNOWN_STATUS = 'UNKNOWN_STATUS'
    PENDING_STATUS = 'PENDING_STATUS'
    CONFIRMED_STATUS = 'CONFIRMED_STATUS'
    ACTIVE_STATUS = 'ACTIVE_STATUS'
    COMPLETED_STATUS = 'COMPLETED_STATUS'
    CANCELLED_STATUS = 'CANCELLED_STATUS'
    REFUNDED_STATUS = 'REFUNDED_STATUS'
    VOIDED_STATUS = 'VOIDED_STATUS'
    PROCESSING_STATUS = 'PROCESSING_STATUS'
    UNCONFIRMED_STATUS = 'UNCONFIRMED_STATUS'
    AIRLINE_CONTROL_STATUS = 'AIRLINE_CONTROL_STATUS'
    PAYMENT_DECLINED_STATUS = 'PAYMENT_DECLINED_STATUS'
    SCHEDULE_CHANGE_STATUS = 'SCHEDULE_CHANGE_STATUS'
    HOLD_STATUS = 'HOLD_STATUS'
    APPROVAL_REQUESTED_STATUS = 'APPROVAL_REQUESTED_STATUS'
    APPROVAL_DENIED_STATUS = 'APPROVAL_DENIED_STATUS'
    CANCELLATION_IN_PROGRESS_STATUS = 'CANCELLATION_IN_PROGRESS_STATUS'
    INOPERATIVE_STATUS = 'INOPERATIVE_STATUS'
    FLIGHT_UNCONFIRMED_STATUS = 'FLIGHT_UNCONFIRMED_STATUS'


class UserId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID


class VariableName(Enum):
    PUBLISHED_FARE = 'PUBLISHED_FARE'
    LLF = 'LLF'


class VendorInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorId: str = Field(..., description='ID of the vendor.')
    vendorName: str = Field(..., description='Name of the vendor.')


class VirtualCardVendor(RootModel[Literal['CONFERMA']]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Literal['CONFERMA'] = Field(
        'CONFERMA', description='Type of Virtual card vendor', examples=['CONFERMA']
    )


class VirtualPaymentMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    label: str = Field(
        ...,
        description='Label for custom virtual payment source.',
        examples=['Custom payment card'],
    )


class VirtualPaymentMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    virtualPaymentMetadata: VirtualPaymentMetadata


class WorkerType(Enum):
    EMPLOYEE = 'EMPLOYEE'
    CONTINGENT = 'CONTINGENT'
    SEASONAL = 'SEASONAL'
    INTERN = 'INTERN'
    GUEST = 'GUEST'


class AddTravelersToEventRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: EventType | None = None
    userIds: Sequence[UserId] | None = None


class AddTravelersToEventResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    addedUserIds: Sequence[UserId] | None = None


class AirBookingStatusFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['AIR_BOOKING_STATUS_FILTER'] = Field(
        'AIR_BOOKING_STATUS_FILTER', description='Type of filter'
    )
    bookingStatus: Sequence[BookingStatusType] = Field(
        ..., description='Air Booking status'
    )


class AirItinerary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airItineraryId: AirItineraryId | None = None


class AllowedFlightGuideline(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    allowedFlightType: AllowedFlightType = Field(
        ..., description='The type of flight booking allowed for the event.'
    )
    numberOfLegs: int | None = Field(
        None,
        description='The number of legs allowed for the flight booking.',
        examples=[2],
    )


class AmadeusCheckout(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentPageId: str | None = Field(
        None, description='Payment page Id.', examples=['aslkdjalwjd']
    )
    splitPaymentAllowed: bool = Field(
        ...,
        description='True if split payment is allowed in Amadeus Checkout, false otherwise',
        examples=[True],
    )
    splitOptionsByCardCompany: Sequence[SplitOptionByCardCompany] | None = None


class AmadeusCheckoutWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amadeusCheckout: AmadeusCheckout


class AsyncJobDetailResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    jobId: UUID | None = Field(
        None,
        description='Job Id for the async event job.',
        examples=['f7b3b3b3-7b3b-4b3b-8b3b-3b3b3b3b3b3b'],
    )
    referenceId: str | None = Field(
        None,
        description='Reference Id associated with the job.',
        examples=['8859676988'],
    )
    type: EventJobType | None = Field(None, description='Job type.')
    status: EventJobStatus | None = Field(None, description='Job status.')
    totalItems: int | None = Field(
        None,
        description='Total number of items which are being processed by the job.',
        examples=[42],
    )
    successCount: int | None = Field(
        None, description='Success item count.', examples=[41]
    )
    failureCount: int | None = Field(
        None, description='Failure item count.', examples=[1]
    )
    jobItems: Sequence[JobItem] | None = Field(None, description='Failed item array.')
    createdAt: DateTimeOffset | None = Field(
        None, description='Created date time for an job.'
    )
    completedAt: DateTimeOffset | None = Field(
        None, description='Updated date time for an job.'
    )


class AsyncJobFilter(RootModel[JobIdFilter | ReferenceIdFilter]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: JobIdFilter | ReferenceIdFilter = Field(
        ..., discriminator='filterType', title='AsyncJobFilter'
    )


class BucketEmailPreferences(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bucketTypes: Sequence[EmailBucketType] = Field(
        ..., description='List of email bucket types.'
    )
    isEnabled: bool | None = Field(
        False, description='Indicates whether emails are disabled.'
    )
    toEmails: Sequence[EmailStr] | None = Field(
        None, description='List of email address to be included in emails.'
    )
    ccEmails: Sequence[EmailStr] | None = Field(
        None, description='List of email address to be cc-ed in emails.'
    )
    bccEmails: Sequence[EmailStr] | None = Field(
        None, description='List of email address to be bcc-ed in emails.'
    )


class BulkPublishResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    publishEventResponses: Sequence[PublishEventResponse] = Field(
        ..., description='List of events publish response.'
    )


class BulkUpdateEventsResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    updateEventsResponse: Sequence[UpdateEventResponse] = Field(
        ..., description='List of events update response.'
    )


class CancelEventRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventId: str = Field(
        ..., description='Unique identifier for the event.', examples=['56789012']
    )
    type: EventType


class CancelEventResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventId: str = Field(
        ..., description='Unique identifier for the event.', examples=['56789012']
    )
    status: OperationStatus
    message: str | None = Field(
        None,
        description='Details about success or failure reason.',
        examples=['Cancelled successfully'],
    )


class CarBookingPaymentGuidelines(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    allowedPaymentVendors: Sequence[VendorInfo] | None = Field(
        None, description='List of allowed car vendors for the event'
    )
    onlyVendorBooking: bool | None = Field(
        None,
        description='Whether only Vendor bookings are allowed for the event.',
        examples=[True],
    )


class CarItinerary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    carItineraryId: CarItineraryId | None = None


class CarPaymentSourceMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelType: Literal['CAR'] = Field(
        'CAR', description="Travel Type for the metadata. For cars it's CAR."
    )
    vendors: Sequence[CarVendorBasic] | None = Field(
        None, description='Vendors for which the payment source is applicable.'
    )


class CardExpiry(RootModel[TokenizedExpiryWrapper | ExpiryWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: TokenizedExpiryWrapper | ExpiryWrapper = Field(
        ..., description='Contains the expiry of a Card.', title='CardExpiry'
    )


class CompanyFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyIds: Sequence[CompanyId] | None = Field(None, description='Company id List')
    companyId: CompanyId | None = Field(None, description='Company id')


class CompanyFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyFilter: CompanyFilter | None = None


class CompanySpecifiedAttributeOverride(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    behavior: OverrideBehavior
    attribute: CompanySpecifiedUserAttribute


class CostCenterFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyId: CompanyId = Field(..., description='company id')
    costCenterIds: Sequence[CostCenterId] = Field(
        ..., description='List of cost center ids'
    )


class CostCenterFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    costCenterFilter: CostCenterFilter | None = None


class CostCenterOverride(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    behavior: OverrideBehavior
    costCenterRef: Reference | None = None


class CustomFieldId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: CustomFieldType
    externalId: str = Field(
        ..., description='Meeting id or budget id based on custom field type.'
    )


class CustomFieldResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    customFieldId: UUID = Field(
        ...,
        description='The unique identifier for the custom field.',
        examples=['f49d00fe-1eda-4304-ba79-a980f565281d'],
    )
    responseItems: Sequence[CustomFieldResponseItem] | None = Field(
        None, description='Responses selected by the event coordinator.'
    )
    travelerAccess: CustomFieldTravelerAccess = Field(
        ..., description='Traveler access for the given custom field.'
    )


class CustomPaymentMethodDescriptor(
    RootModel[
        BrexBudgetWrapper
        | AmadeusCheckoutWrapper
        | QantasTravelFundWrapper
        | DpanWrapper
        | VirtualPaymentMetadataWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        BrexBudgetWrapper
        | AmadeusCheckoutWrapper
        | QantasTravelFundWrapper
        | DpanWrapper
        | VirtualPaymentMetadataWrapper
    ) = Field(
        ...,
        description='Metadata for Custom Payment Method payment source.',
        title='CustomPaymentMethodDescriptor',
    )


class CustomPaymentMethodDescriptorWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    customPaymentMethodDescriptor: CustomPaymentMethodDescriptor | None = None


class DeleteEventRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(
        ..., description='Unique identifier for the event.', examples=['56789012']
    )
    type: EventType | None = None


class DeleteEventResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(
        ..., description='Unique identifier for the event.', examples=['56789012']
    )
    status: OperationStatus
    message: str | None = Field(
        None,
        description='Details about success or failure reason.',
        examples=['Deleted successfully'],
    )


class DepartmentFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    departmentFilter: PaymentSourceDepartmentFilter | None = None


class DepartmentOverride(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    behavior: OverrideBehavior
    departmentRef: Reference | None = None


class EventBookingGuideline4(RailBookingGuidelineWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    numGuestsAllowed: int | None = Field(
        None,
        description='Number of guests allowed to be booked for this booking',
        examples=[1],
    )


class EventEntityMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress | None = None


class EventLocation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress | None = None
    coordinates: Latlng | None = None


class EventMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventMetadata: EventEntityMetadata | None = None


class EventRsvpResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    air: AirRsvpResponse | None = None
    hotel: HotelRsvpResponse | None = None
    car: CarRsvpResponse | None = None
    rail: RailRsvpResponse | None = None


class EventTemplateFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventTemplateFilter: PaymentEventFilter | None = None


class EventTravelerFilter(
    RootModel[
        TravelerSearchFilter
        | InvitationStatusFilter
        | AirBookingStatusFilter
        | RailBookingStatusFilter
        | CarBookingStatusFilter
        | HotelBookingStatusFilter
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        TravelerSearchFilter
        | InvitationStatusFilter
        | AirBookingStatusFilter
        | RailBookingStatusFilter
        | CarBookingStatusFilter
        | HotelBookingStatusFilter
    ) = Field(
        ...,
        description='Event traveler filter',
        discriminator='type',
        title='EventTravelerFilter',
    )


class EventTravelersRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    offset: conint(ge=0) | None = Field(
        0,
        description='Indicates from where in the list of travelers the server should start.',
        examples=[2],
    )
    limit: conint(ge=0, le=100) | None = Field(
        10,
        description='Maximum number of results to be fetched for the query.',
        examples=[2],
    )
    type: EventType | None = None
    filters: Sequence[EventTravelerFilter] | None = Field(
        None, description='Filters for event travelers'
    )


class EventUserFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId | None = Field(None, description='User id')


class EventUserFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userFilter: EventUserFilter | None = None


class EventUserRsvp(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId | None = None
    eventRsvpState: EventRsvpState | None = None
    eventRsvpResponse: EventRsvpResponse | None = None
    invitedAt: DateTimeOffset | None = None
    airBookingStatus: BookingStatusType | None = None
    railBookingStatus: BookingStatusType | None = None
    carBookingStatus: BookingStatusType | None = None
    hotelBookingStatus: BookingStatusType | None = None


class Expression(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['EXPRESSION'] = Field('EXPRESSION', examples=['EXPRESSION'])
    formatExpression: str = Field(
        ...,
        description='The expression must be of format : `${expression}`.The expression can consist of a \ncombination of variables and mathematical operations.\n Variable names must begin with `var` followed by a number, which is used to identify \nthe variable in the variables list. The numbering should follow a 1-based index.\n  To define mathematical operations, the operation name should follow the format\n`math.<math_op>(arg1, arg2)`. Both `arg1` and `arg2` can be variables or constants. \nThe supported math operations (math_op) include: `add, mul, div, sub, min,\nand max`. All keywords, such as `<math_op>, math, and var` must be written in lowercase.\n',
        examples=['Result:  ${math.mul(var1,5)}  ${var2}'],
    )
    variables: Sequence[VariableName] | None = Field(
        None, description='Reference names of the variables present in the expression.'
    )


class GetEventSummariesRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId | None = Field(None, description='User id')
    companyId: CompanyId | None = Field(None, description='Company id')
    tripIds: Sequence[str] | None = Field(None, description='List of Spotnana trip IDs')


class HotelAmenities(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: HotelAmenityType | None = None
    additionalInfo: str | None = Field(
        None,
        description='Amenity description',
        examples=['Complimentary in-room coffee or tea'],
    )
    complimentary: bool | None = Field(
        None, description='Is Amenity complimentary', examples=[True]
    )


class HotelImageSet(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    category: HotelImageCategory
    imageGroup: ImageGroup


class HotelInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress = Field(..., description='Address of the hotel.')
    chainCode: str | None = Field(
        None, description='Chain code of the hotel.', examples=['RF']
    )
    chainName: str | None = Field(
        None, description='Chain name of the hotel.', examples=['Red Roof Inns']
    )
    coordinates: Latlng | None = Field(None, description='Coordinates of the hotel.')
    email: str | None = Field(
        None, description='Email address of the hotel.', examples=['<EMAIL>']
    )
    hotelId: str | None = Field(None, description='Hotel id.', examples=['100094780'])
    name: str = Field(
        ...,
        description='Name of the hotel.',
        examples=['San Francisco Airport Red Roof'],
    )
    phone: PhoneNumber | None = Field(None, description='Phone number of the hotel.')
    starRating: float | None = Field(
        None, description='Star rating of the hotel.', examples=[3.5]
    )
    fax: Sequence[PhoneNumber] | None = None
    masterChainCode: str | None = Field(
        None, description='Master chain code of the hotel.', examples=['EM']
    )
    brandName: str | None = Field(
        None, description='Brand name of the hotel.', examples=['Marriott Hotel Brands']
    )
    amenities: Sequence[HotelAmenities] | None = None
    additionalAmenities: Sequence[str] | None = Field(
        None,
        description='List of amenities provided by the supplier.',
        examples=[['Room service', 'Wifi']],
    )
    imageSets: Sequence[HotelImageSet] | None = None
    descriptions: Sequence[HotelDescription] | None = None
    thirdPartyHotelCodes: Sequence[ThirdPartyHotelCode] | None = None


class HotelItinerary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelItineraryId: HotelItineraryId | None = None


class IndividualFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ids: Sequence[UserId] = Field(..., description='List of user id')
    companyId: CompanyId = Field(..., description='company id')


class IndividualFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    individualFilter: IndividualFilter | None = None


class LegalEntityFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legalEntityIds: Sequence[LegalEntityId] = Field(
        ..., description='Legal Entity id List'
    )
    companyId: CompanyId | None = Field(
        None, description='The company Id for the filter.'
    )


class LegalEntityFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legalEntityFilter: LegalEntityFilter | None = None


class LegalEntityOverride(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    behavior: OverrideBehavior
    legalEntityRef: Reference | None = None


class LimoItinerary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    limoItineraryId: LimoItineraryId | None = None


class OtherCoinageItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    coinageCode: PaymentMethod | None = Field(None, description='Payment method')
    amount: float | None = Field(None, examples=[1000])
    conversionRate: float | None = Field(
        None,
        description='1 coin in this system equals to how many currency value',
        examples=[0.01],
    )
    preferredCurrencyConversionRate: float | None = Field(
        None,
        description='1 coin in this system equals to how many currency value',
        examples=[0.01],
    )


class Money(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: float = Field(
        ..., description='The numeric value for the amount of money.', examples=[510]
    )
    currencyCode: str = Field(
        ...,
        description='The 3-letter currency code for the money amount (defined using ISO 4217 standard).',
        examples=['GBP'],
    )
    convertedAmount: float | None = Field(
        None,
        description="The converted currency and amount that has been converted (if a currency conversion has been requested).\nFor example, if the call requests that money be sent in a specified currency (because the frontend requested\nthe backend to send money in the user's preferred currency).\n",
        examples=[715.42],
    )
    convertedCurrency: str | None = Field(
        None,
        description='The 3-letter currency code for the converted currency (defined using ISO 4217 standard).',
        examples=['USD'],
    )
    otherCoinage: Sequence[OtherCoinageItem] | None = Field(
        None,
        description='List of the dollar amount in other coinage systems like reward points, cryptocurrency etc.',
        title='OtherCoinage',
    )


class Name(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    family1: str = Field(..., description='Last (family) name.', examples=['Gandas'])
    family2: str | None = Field(None, examples=['FamilyTwo'])
    given: str = Field(..., description='First (given) name.', examples=['Vichitr'])
    middle: str | None = Field(None, description='Middle name.', examples=['Kumar'])
    suffix: NameSuffix | None = Field(
        None,
        description='Suffix used with the name. For example SR or JR.',
        examples=['SR'],
    )
    preferred: str | None = Field(
        None,
        description='Informal preferred name added by traveler. This is not used on any PNR or tickets',
        examples=['Don'],
    )


class PaymentConfigForTravelType(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelType: TravelType
    paymentConfig: TemplatePaymentConfig


class PaymentInstructionId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID = Field(
        ...,
        description='Id corresponding to the payment instruction',
        examples=['f49d00fe-1eda-4304-ba79-a980f565281d'],
    )
    travelType: TravelType = Field(
        ..., description='associated travelType for the instruction'
    )


class PaymentSourceAttributes(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    guestTravelAllowed: bool | None = Field(
        None, description='Whether guest travel is allowed.'
    )
    roleTypes: Sequence[RoleType] | None = Field(
        None, description='Role types which have access to the payment source'
    )
    includedPersonas: Sequence[Persona] | None = Field(
        None,
        description='If specified, list of traveler personas which have access to the payment source.',
    )
    excludedPersonas: Sequence[Persona] | None = Field(
        None,
        description="List of traveler personas which don't have access to the payment source.",
    )
    allowedThirdPartySources: Sequence[ThirdPartySource] | None = Field(
        None, description='List of 3rd party sources supported for the payment source.'
    )
    allowedTripTypes: Sequence[TripUsageType] | None = Field(
        None, description='List of trip types supported for the payment source.'
    )


class PaymentSourceSpendType(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    spendType: SpendType = Field(
        ..., description='Applicable spend type e.g SERVICE_FEE'
    )


class PaymentSourceTravelTypeMetadata(RootModel[CarPaymentSourceMetadata]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: CarPaymentSourceMetadata = Field(
        ..., discriminator='travelType', title='PaymentSourceTravelTypeMetadata'
    )


class PersonalFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UserId = Field(..., description='user id')
    managedTravelerAccess: ManagedTravelerPaymentAccess | None = Field(
        None,
        description='Config for providing the access to Managed Travelers if any. Applicable for Arranger roles.',
    )


class PersonalFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    personalFilter: PersonalFilter | None = None


class PnrMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrMetadata: PnrMetadata | None = None
    invoiceMetadata: InvoiceMetadata | None = Field(
        None,
        description='Metadata associated with an invoice document.',
        title='InvoiceMetadata',
    )
    travelType: TravelType


class PublishEventRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(
        ..., description='Unique identifier for the event.', examples=['56789012']
    )
    sendInvite: bool = Field(
        ...,
        description='Flag to indicate if an invite should be sent.',
        examples=[True],
    )
    userIds: Sequence[UserId] | None = Field(
        None,
        description='List of users receiving the invite. - If provided, invites will be sent only to the specified users. - If empty or not provided, invites will be sent to all users.',
        max_length=1000,
        min_length=0,
    )
    type: EventType | None = None


class RailItinerary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    railItineraryId: RailItineraryId | None = None


class RemoveTravelersFromEventRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: EventType | None = None
    userIds: Sequence[UserId] | None = Field(None, max_length=10, min_length=1)


class RemoveTravelersFromEventResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    removedUserIds: Sequence[UserId] | None = None


class RewardsProgramDescriptor(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: RewardsProgramType | None = None


class RewardsProgramDescriptorWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    rewardsProgramDescriptor: RewardsProgramDescriptor | None = None


class SendInviteRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: EventType | None = None
    userIds: Sequence[UserId] | None = None


class SetUserEventRsvpRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: EventType | None = None
    eventRsvpState: EventRsvpState | None = None
    eventRsvpResponse: EventRsvpResponse | None = None


class TripInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tripId: str = Field(
        ..., description='ID of the trip of the user.', examples=['1234567890']
    )
    status: UserFacingStatus


class TripOverrides(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legalEntity: LegalEntityOverride = Field(..., description='Legal entity override')
    costCenter: CostCenterOverride = Field(..., description='Cost center override')
    department: DepartmentOverride = Field(..., description='Department override')
    userAttributes: Sequence[CompanySpecifiedAttributeOverride] = Field(
        ..., description='List of company specified attribute overrides'
    )


class UnusedCreditInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    sourcePnr: str | None = Field(
        None,
        description='PNR number corresponding to third party through which booking was made.',
        examples=['MC5ONS'],
    )
    spotnanaPnr: str | None = Field(
        None, description='Spotnana pnr ID.', examples=['2345678']
    )
    ticketNumber: str | None = Field(
        None,
        description='Ticket number for the ticket that was converted into an unused credit.',
        examples=['5267779139217'],
    )
    airlineCode: str | None = Field(
        None,
        description='2 letter airline code of the airline associated with this unused credit.',
        examples=['AA'],
    )
    airlineInfo: AirlineInfo | None = Field(
        None, description='Airline info with airline name and code'
    )
    totalFare: Money | None = Field(
        None, description='Total airfare associated with the original ticket.'
    )
    issueDate: DateTimeOffset | None = Field(
        None, description='Issue date for the unused credit.'
    )
    expiryDate: DateTimeOffset | None = Field(
        None, description='Expiry date for the unused credit.'
    )
    usedDate: DateTimeOffset | None = Field(
        None, description='Date on which the unused credit was used.'
    )
    departureDate: DateTimeOffset | None = Field(
        None,
        description='Date for the departure of the first flight associated with the unused credit.',
    )
    segmentsAvailable: SegmentsAvailable | None = Field(
        None,
        description='Whether all segments are unused or some have already been used.',
    )
    passengerName: Name | None = Field(
        None, description='Name of the passenger associated with the credit.'
    )
    departureCountry: str | None = Field(
        None,
        description='3 letter country code of the departure country associated with the original ticket.',
        examples=['USA'],
    )
    arrivalCountry: str | None = Field(
        None,
        description='3 letter country code of the arrival country associated with the original ticket.',
        examples=['USA'],
    )
    ticketType: TicketType | None = Field(None, description='Type of credit.')
    pcc: str | None = Field(None, description='PCC the credit was issued on.')
    status: CreditStatus | None = None
    source: ThirdPartySource | None = Field(
        'SABRE', description='Source of unused credit e.g. Sabre, NDC etc.'
    )
    tripId: str | None = Field(
        None,
        description='Trip ID that contains the unused credit',
        examples=['1234567'],
    )
    redeemVia: RedeemVia | None = Field(
        None,
        description='Credit redemption method. \nIf the value contains `CONTACT_AGENT`, then the agent must book the ticket and redeem the credits on behalf of the traveler.\n',
        examples=['REDEEM_VIA_OBT'],
    )
    sourceOfTruth: SourceOfTruth | None = Field(
        None, description='The system that owns the credit.'
    )
    owningPcc: str | None = Field(None, description='PCC the PNR was created on.')
    paymentSourceId: UUID | None = Field(
        None,
        description='Payment source ID associated with the credit.',
        examples=['edd5b835-8001-430c-98f8-fedeccebe4cf'],
    )
    creditUsageType: CreditUsageType | None = Field(
        None,
        description='The type of credit usage. This can be either COMPANY or PERSONAL.',
    )
    email: str | None = Field(
        None, description='Email of the passenger owning the unused credit.'
    )


class UserGroup(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userIds: Sequence[UserId] | None = None
    legalEntityIds: Sequence[EntityId] | None = None
    officeIds: Sequence[EntityId] | None = None
    departments: Sequence[str] | None = Field(
        None, description='List of department ids.'
    )
    costCenters: Sequence[str] | None = Field(
        None, description='List of cost center ids.'
    )
    grades: Sequence[str] | None = Field(None, description='List of grade ids.')
    positionTitles: Sequence[str] | None = None
    personas: Sequence[Persona] | None = None
    customFieldIds: Sequence[CustomFieldId] | None = None
    countryCodes: Sequence[str] | None = None
    workerTypes: Sequence[WorkerType] | None = None
    accountingCodes: Sequence[str] | None = None


class UserGroupInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userGroupCriteria: Sequence[UserGroup] | None = Field(
        None, description='User group criteria'
    )
    userIds: Sequence[UserId] | None = Field(
        None, description='List of users derived from the user group criteria'
    )


class UserTripInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId
    tripInfos: Sequence[TripInfo] | None = Field(
        None, description='List of trips for the user.'
    )


class Variable(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['VARIABLE'] = Field('VARIABLE', examples=['VARIABLE'])
    name: VariableName


class AdditionalInfo(RootModel[Variable | Expression]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Variable | Expression = Field(
        ...,
        description='Additional data need to be sent along with the custom field response.',
        discriminator='type',
        title='AdditionalInfo',
    )


class AirBookingGuideline(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    allowedAirports: Sequence[AirportInfo] | None = Field(
        None,
        description='List of allowed airports for arrival of flight-in-event and departure of flight-out-event.',
    )
    arrivalBookingWindow: EventBookingWindow | None = Field(
        None, description='Booking window for flight-in-event.'
    )
    departureBookingWindow: EventBookingWindow | None = Field(
        None, description='Booking window for flight-out-event.'
    )
    allowedFlightGuidelines: Sequence[AllowedFlightGuideline] | None = Field(
        None, description='List of allowed flight guidelines for the event.'
    )


class AirBookingGuidelineWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airBookingGuideLine: AirBookingGuideline | None = None


class AllowedPaymentConfig(RootModel[Sequence[PaymentConfigForTravelType]]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Sequence[PaymentConfigForTravelType] = Field(
        ..., description='Allowed payment configuration for the event template'
    )


class AsyncJobDetailRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyId: str = Field(
        ...,
        description='Company Id for which job was created.',
        examples=['c5977a9b-095a-40be-a1bb-b9d3c894e4a5'],
    )
    filters: AsyncJobFilter | None = Field(
        None, description='Filter to fetch the job details.'
    )


class BulkCancelEventRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cancelAsync: bool | None = Field(
        False,
        description='Flag to indicate if the cancellation should be processed asynchronously.',
        examples=[False],
    )
    cancelEventRequests: Sequence[CancelEventRequest] = Field(
        ..., description='List of events to be cancelled.', max_length=100, min_length=1
    )


class BulkCancelEventResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cancelEventResponses: Sequence[CancelEventResponse] = Field(
        ..., description='List of events cancellation response.'
    )


class BulkDeleteEventRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    deleteEventList: Sequence[DeleteEventRequest] = Field(
        ..., description='List of events to be deleted.', max_length=100, min_length=1
    )


class BulkDeleteEventResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    deleteEventResponses: Sequence[DeleteEventResponse] = Field(
        ..., description='List of events delete response.'
    )


class BulkPublishEventRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    publishEventList: Sequence[PublishEventRequest] = Field(
        ..., description='List of events to be published.', max_length=100, min_length=1
    )


class CarBookingGuideline(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pickupBookingWindow: EventBookingWindow | None = None
    dropoffBookingWindow: EventBookingWindow | None = None
    paymentGuidelines: CarBookingPaymentGuidelines | None = None


class CarBookingGuidelineWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    carBookingGuideLine: CarBookingGuideline | None = None


class Card(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID | None = Field(
        None,
        description='Unique identifier for this card',
        examples=['34d536b6-f8ff-11eb-9a61-0242ac180002'],
    )
    type: Type | None = Field(None, description='Type of card', examples=['CREDIT'])
    company: CardCompany | None = None
    name: str | None = Field(
        None, description='Name on card', examples=['Harrison Schwartz']
    )
    address: PostalAddress | None = Field(None, description='Billing address')
    number: str = Field(..., description='Card number', examples=['****************'])
    expiryMonth: conint(ge=1, le=12) | None = Field(
        None, description='Expiry month', examples=[1]
    )
    expiryYear: conint(ge=2000) | None = Field(
        None, description='Expiry year', examples=[2010]
    )
    cvv: str | None = Field(None, description='Card cvv number', examples=['012'])
    label: str | None = Field(None, description='Card Label', examples=['Label amex'])
    currency: str | None = Field(
        None, description='Native currency of the card.', examples=['USD']
    )
    externalId: str | None = Field(
        None,
        description='Spotnana partner card id.',
        examples=['bxt_RNGsNfzgJDaTstKIKqK4xEuhGYAnMdYK8T40'],
    )
    vaultId: UUID | None = Field(
        None,
        description='ID of the vault used for creating the card.',
        examples=['34d536b6-f8ff-11eb-9a61-0242ac180002'],
    )
    expiry: CardExpiry | None = Field(None, description='Card Expiry.')
    ownershipLabel: OwnershipLabel | None = Field(None, examples=['PERSONAL'])


class CardDescriptor(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    card: Card
    isCentrallyBilled: bool | None = Field(
        False, description='Whether card is centrally billed or not.'
    )
    isLodgeCard: bool | None = Field(
        False, description='Whether card is a lodge card or not.'
    )


class CardDescriptorWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cardDescriptor: CardDescriptor | None = None


class ConfermaMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cardPoolClientId: str = Field(
        ..., description='card pool client Id.', examples=['test-id']
    )
    cardLabel: str | None = Field(
        None,
        description='Name/Label for visual identification of the card.',
        examples=['cardCompany'],
    )
    company: CardCompany | None = None
    paymentInstructionIds: Sequence[PaymentInstructionId] | None = Field(
        None, description='List of payment instruction ids'
    )


class ConfermaMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    confermaMetadata: ConfermaMetadata


class CustomFieldSelectedOption(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Value of the selection')
    description: str | None = Field(None, description='Description of the selection')
    additionalUserInput: str | None = Field(None, description='Additional user input')
    additionalInfos: Sequence[str] | None = Field(
        None, description='Actual values of the additional infos'
    )
    additionalInfoConfigs: Sequence[AdditionalInfo] | None = Field(
        None, description='Additional info configs for the selected option'
    )


class EntityMetadata(RootModel[PnrMetadataWrapper | EventMetadataWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: PnrMetadataWrapper | EventMetadataWrapper = Field(
        ...,
        description='Metadata for associated entity of document.',
        title='EntityMetadata',
    )


class EventBookingGuideline1(AirBookingGuidelineWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    numGuestsAllowed: int | None = Field(
        None,
        description='Number of guests allowed to be booked for this booking',
        examples=[1],
    )


class EventBookingGuideline3(CarBookingGuidelineWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    numGuestsAllowed: int | None = Field(
        None,
        description='Number of guests allowed to be booked for this booking',
        examples=[1],
    )


class EventFilter(
    RootModel[
        EventUserFilterWrapper
        | EventStatusFilterWrapper
        | EventNameFilterWrapper
        | EventDateRangeFilterWrapper
        | EventCompanyFilterWrapper
        | EventParentFilterWrapper
        | EventTypeFilterWrapper
        | EventIdFilterWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        EventUserFilterWrapper
        | EventStatusFilterWrapper
        | EventNameFilterWrapper
        | EventDateRangeFilterWrapper
        | EventCompanyFilterWrapper
        | EventParentFilterWrapper
        | EventTypeFilterWrapper
        | EventIdFilterWrapper
    ) = Field(..., description='Event filter', title='EventFilter')


class EventUserInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId
    email: str | None = Field(
        None, description='Business email of the user', examples=['<EMAIL>']
    )
    name: Name


class HotelBookingGuideline(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    allowedHotels: Sequence[HotelInfo] | None = Field(
        None, description='List of allowed Hotel details for the event'
    )
    checkinBookingWindow: EventBookingWindow | None = None
    checkoutBookingWindow: EventBookingWindow | None = None
    paymentGuidelines: HotelBookingPaymentGuidelines | None = None


class HotelBookingGuidelineWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelBookingGuideLine: HotelBookingGuideline | None = None


class Itinerary(
    RootModel[
        AirItinerary | HotelItinerary | CarItinerary | RailItinerary | LimoItinerary
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        AirItinerary | HotelItinerary | CarItinerary | RailItinerary | LimoItinerary
    ) = Field(..., description='Details of the itinerary')


class ItineraryFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    itineraryInfo: Itinerary


class ItineraryFilterWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    itineraryFilter: ItineraryFilter | None = None


class ListEventsRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    listEventType: ListEventType = Field(
        ..., description='Type of events', examples=['UPCOMING']
    )
    filters: Sequence[EventFilter] | None = Field(
        None, description='List events filters'
    )
    travelerFilters: Sequence[EventTravelerFilter] | None = Field(
        None, description='Filters for event travelers'
    )
    offset: conint(ge=0) | None = Field(
        0,
        description='Indicates from where in the list of events the server should start.',
        examples=[2],
    )
    limit: conint(le=100) | None = Field(
        10,
        description='Maximum number of results to be fetched for the query.',
        examples=[2],
    )


class PaymentSourceFilterMetadata(
    RootModel[
        LegalEntityFilterWrapper
        | CompanyFilterWrapper
        | CountryFilterWrapper
        | TmcFilterWrapper
        | PersonalFilterWrapper
        | IndividualFilterWrapper
        | CostCenterFilterWrapper
        | DepartmentFilterWrapper
        | ItineraryFilterWrapper
        | PaymentEventFilterWrapper
        | EventTemplateFilterWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        LegalEntityFilterWrapper
        | CompanyFilterWrapper
        | CountryFilterWrapper
        | TmcFilterWrapper
        | PersonalFilterWrapper
        | IndividualFilterWrapper
        | CostCenterFilterWrapper
        | DepartmentFilterWrapper
        | ItineraryFilterWrapper
        | PaymentEventFilterWrapper
        | EventTemplateFilterWrapper
    ) = Field(
        ...,
        description='Metadata corresponding to the payment source.',
        title='PaymentSourceFilterMetadata',
    )


class PaymentSourceTravelType(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelType: TravelType = Field(
        ..., description='Applicable travel segment type, e.g. AIR, HOTEL etc.'
    )
    isRequired: bool | None = Field(
        None,
        description='Whether this is a required payment source for this travel type.',
    )
    allowPostPaidBookings: bool | None = Field(
        False,
        description='Whether post paid bookings are allowed for this travel type.',
    )
    metadata: PaymentSourceTravelTypeMetadata | None = Field(
        None, description='Travel Type specific metadata for payment source.'
    )


class TravelTypeFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelTypes: Sequence[PaymentSourceTravelType] | None = Field(
        None, description='Applicable travel types for Payment Source.'
    )


class TravelerEventInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId | None = Field(None, description='User Id')
    tripInfos: Sequence[TripInfo] | None = Field(
        None, description='List of trip info for the user'
    )
    eventRsvpState: EventRsvpState | None = Field(None, description="Users' rsvp state")
    eventRsvpResponse: EventRsvpResponse | None = Field(
        None,
        description="Users' rsvp response. This includes details related to bookings",
    )
    eventUserRsvp: EventUserRsvp | None = Field(
        None, description="User's rsvp information."
    )


class UATPInformation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    card: Card
    ticketingValidity: DateTimeRange = Field(
        ..., description='Valid ticketing datetime.'
    )
    travelValidity: DateTimeRange = Field(..., description='Valid travel datetime.')


class UATPMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    card: Card


class UATPMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    uatpMetadata: UATPMetadata | None = None


class UnusedCreditDescriptor(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    unusedCreditInfo: UnusedCreditInfo
    userId: UserId = Field(
        ..., description='The user Id of the user who owns the credit.'
    )
    companyId: CompanyId = Field(
        ..., description='The company Id of the user who owns the credit.'
    )


class UnusedCreditDescriptorWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    unusedCreditDescriptor: UnusedCreditDescriptor | None = None


class UserTripGroup(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID | None = Field(
        None,
        description='ID of the group.',
        examples=['f49d00fe-1eda-4304-ba79-a980f565281d'],
    )
    name: str | None = Field(
        None, description='Name of the group.', examples=['Sample group name']
    )
    userGroupInfo: UserGroupInfo | None = None
    userTripInfos: Sequence[UserTripInfo] | None = Field(
        None, description='Details of the users and their trips in the group.'
    )
    ownerId: UserId
    status: UserFacingStatus | None = None
    startDate: DateModel | None = None
    endDate: DateModel | None = None
    createdAt: DateTimeOffset | None = None


class VirtualCardDescriptorMetadata(RootModel[ConfermaMetadataWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: ConfermaMetadataWrapper = Field(
        ...,
        description='Metadata corresponding to the VirtualCardDescriptor.',
        title='VirtualCardDescriptorMetadata',
    )


class AccessTypeAttributes(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelTypeFilter: TravelTypeFilter | None = None


class CustomFieldPrefilledResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fieldId: UUID = Field(..., description='Custom field id')
    fieldName: str | None = Field(None, description='Name of the custom field')
    armId: UUID = Field(..., description='Arm id which is applicable')
    readOnly: bool | None = Field(
        False,
        description='Indicates whether the user can change the pre-selected options.',
        examples=[True],
    )
    hidden: bool | None = Field(
        False,
        description='Whether this code will be hidden to the user.',
        examples=[True],
    )
    selectedOptions: Sequence[CustomFieldSelectedOption] = Field(
        ...,
        description='The list of options that are selected by user or auto populated.',
    )


class DocumentMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    documentType: DocumentType
    entityType: EntityType
    entityId: str = Field(
        ..., description='Entity Id for the given entity type.', examples=['123124']
    )
    entityMetadata: EntityMetadata
    name: str = Field(..., description='Document name.', examples=['BoardingPass.pdf'])


class EventBookingGuideline2(HotelBookingGuidelineWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    numGuestsAllowed: int | None = Field(
        None,
        description='Number of guests allowed to be booked for this booking',
        examples=[1],
    )


class EventBookingGuideline(
    RootModel[
        EventBookingGuideline1
        | EventBookingGuideline2
        | EventBookingGuideline3
        | EventBookingGuideline4
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        EventBookingGuideline1
        | EventBookingGuideline2
        | EventBookingGuideline3
        | EventBookingGuideline4
    ) = Field(
        ...,
        description='Booking details allowed for the event',
        title='EventBookingGuideline',
    )


class EventCustomFieldResponsesRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventCustomFieldResponses: Sequence[CustomFieldResponse] | None = Field(
        None, description='List of custom field responses for an event.'
    )
    customFieldV3Responses: Sequence[CustomFieldPrefilledResponse] | None = Field(
        None, description='List of custom field responses for an event.'
    )


class EventTravelersResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerEventInfos: Sequence[TravelerEventInfo] | None = Field(
        None, description='List of user rsvp responses & trip details'
    )
    userGroupInfo: UserGroupInfo | None = Field(
        None, description="Details about group's users"
    )
    paginationParams: OffsetBasedPaginationResponseParams | None = Field(
        None, description='Total number of active travelers in the event'
    )


class PaymentAccessMapping(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID = Field(
        ...,
        description='Unique identifier identifying this payment source',
        examples=['f49d00fe-1eda-4304-ba79-a980f565281d'],
    )
    accessTypeAttributes: AccessTypeAttributes | None = Field(
        None, description='Attributes applicable to the access type for this mapping.'
    )


class PaymentSourceMapping(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    accessType: AccessType | None = Field(
        None, description='Access Type of the Payment Source'
    )
    accessTypeEntityId: str | None = Field(
        None, description='Entity ID corresponding to the Access Type being created'
    )
    filter: PaymentSourceFilterMetadata | None = Field(
        None, description='Applicable access information for this payment_source'
    )
    travelType: Sequence[PaymentSourceTravelType] | None = Field(
        None, description='Applicable payment source travel type.'
    )
    travelTypes: Sequence[PaymentSourceTravelType] | None = Field(
        None, description='Applicable payment source travel types'
    )
    spendTypes: Sequence[PaymentSourceSpendType] | None = Field(
        None, description='Applicable spend types for payment source other than travel'
    )
    paymentSourceAttributes: PaymentSourceAttributes | None = Field(
        None, description='Attributes applicable to the associated payment source.'
    )
    accessTypeAttributes: AccessTypeAttributes | None = Field(
        None, description='Attributes applicable to the access type.'
    )


class UAPassPlusMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    programDetails: ProgramDetails
    uatpInfo: UATPInformation


class UAPassPlusMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    uaPassPlusMetadata: UAPassPlusMetadata | None = None


class UpdateEventBookingGuidelinesRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingGuidelines: Sequence[EventBookingGuideline] = Field(
        ..., description='A list of booking guideline for the event.'
    )
    allowedBookingTypes: Sequence[EventAllowedBookingType] | None = Field(
        None, description='Allowed booking types for the event group'
    )
    allowedPaymentConfig: AllowedPaymentConfig | None = None
    paymentMappings: Sequence[PaymentAccessMapping] | None = Field(
        None, description='Mappings i.e. access level, travel type information etc.'
    )
    eventType: EventType | None = None


class VirtualCardDescriptor(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendor: VirtualCardVendor | None = None
    reportingAttributes: Sequence[ReportingAttribute] | None = Field(
        None, description='List of ReportingAttributes'
    )
    metadata: VirtualCardDescriptorMetadata | None = Field(
        None, description='card descriptor corresponding to the virtual card.'
    )


class VirtualCardDescriptorWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    virtualCardDescriptor: VirtualCardDescriptor | None = None


class AirlineProgramMetadata(
    RootModel[UATPMetadataWrapper | UAPassPlusMetadataWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: UATPMetadataWrapper | UAPassPlusMetadataWrapper = Field(
        ..., title='AirlineProgramMetadata'
    )


class Document(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    url: str = Field(
        ...,
        description='S3 location of the document.',
        examples=['https://s3.amazonaws.com/bucket-name/folder-name/file-name'],
    )
    documentId: UUID = Field(
        ...,
        description='Unique identifier of the document.',
        examples=['f49d00fe-1eda-4304-ba79-a980f565281d'],
    )
    documentMetadata: DocumentMetadata


class EventGroupMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    contacts: Sequence[UserId] | None = Field(None, description='Event group contacts')
    documents: Sequence[Document] | None = Field(
        None, description='Documents attached for an event group'
    )
    bookingStyle: EventBookingStyle | None = Field(
        None,
        description='Whether the event bookings will be self served by the traveler or would be arranged by an agent for the traveler.',
    )
    referenceUsers: Sequence[UserId] | None = Field(
        None, description='Event group reference user ids'
    )
    internalNotes: str | None = Field(None, description='Internal notes of the event')
    travelerTypes: Sequence[Persona] | None = Field(
        None, description='Allowed traveler types for the event'
    )
    allowTravelersPolicy: AllowTravelersPolicy | None = None
    emailPreferences: BucketEmailPreferences | None = None
    allowTravelersToRsvp: AllowTravelersToRsvp | None = None
    tripOverrides: TripOverrides | None = None


class TravelerEventSummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., description='Event ID', examples=['12345'])
    type: EventType | None = Field(None, description='Event Type')
    name: str | None = Field(
        None, description='Name of the event', examples=['My event']
    )
    description: str | None = Field(
        None,
        description='Description of the event',
        examples=['This is an event description'],
    )
    startDateTime: DateTimeLocal | None = None
    endDateTime: DateTimeLocal | None = None
    location: EventLocation | None = None
    contacts: Sequence[UserId] | None = Field(
        None, description='Event contacts for the traveler'
    )
    documents: Sequence[Document] | None = Field(
        None,
        description='List of documents associated with this event for the traveler',
    )
    bookingGuidelines: Sequence[EventBookingGuideline] | None = Field(
        None, description='Booking details allowed for the event for the traveler'
    )
    allowedBookingTypes: Sequence[EventAllowedBookingType] | None = Field(
        None, description='Allowed booking types for the event for the traveler'
    )
    eventUserRsvp: EventUserRsvp | None = None
    contactInfoList: Sequence[EventUserInfo] | None = Field(
        None, description='Event contacts for the traveler'
    )
    companyId: EntityId | None = None
    runningStatus: EventRunningStatus | None = None
    status: EventStatus | None = None
    isRemovedParticipant: bool | None = Field(
        None, description='Whether the traveler is part of the event.'
    )


class TripEventSummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tripId: str | None = Field(
        None, description='Spotnana trip ID', examples=['6926658168']
    )
    travelerEventSummary: TravelerEventSummary | None = None


class UpdateEventBasicInfoRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Name of the event')
    type: EventType | None = None
    description: str | None = Field(None, description='Description of the event')
    documents: Sequence[Document] | None = Field(
        None, description='Documents attached for an event'
    )
    startDateTime: DateTimeLocal | None = None
    endDateTime: DateTimeLocal | None = None
    location: EventLocation
    contacts: Sequence[UserId] | None = Field(
        None, description='Contacts for the event'
    )
    referenceUsers: Sequence[UserId] | None = Field(
        None, description='Event group reference user ids'
    )
    travelerTypes: Sequence[Persona] | None = Field(
        None, description='Allowed traveler types for the event'
    )
    bookingStyle: EventBookingStyle | None = Field(
        None,
        description='Whether the event bookings will be self served by the traveler or would be arranged by an agent for the traveler.',
    )
    policyId: UUID | None = Field(
        None,
        description='Policy associated with the event.',
        examples=['f7b3b3b3-7b3b-4b3b-8b3b-3b3b3b3b3b3b'],
    )
    allowTravelersPolicy: AllowTravelersPolicy | None = None
    emailPreferences: BucketEmailPreferences | None = None
    allowTravelersToRsvp: AllowTravelersToRsvp | None = None
    internalNotes: str | None = Field(
        None,
        description='Internal notes of the event',
        examples=['Use travelers default as payment method.'],
    )
    tripOverrides: TripOverrides | None = None


class UpdateEventRequestItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventId: str = Field(
        ..., description='Id of the event to be updated.', examples=['56789012']
    )
    eventBasicInfo: UpdateEventBasicInfoRequest
    eventBookingGuidelines: UpdateEventBookingGuidelinesRequest


class AirlineProgram(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlineInfo: Sequence[Airline] = Field(
        ..., description="Eligible airlines' information list."
    )
    airlineProgramMetadata: AirlineProgramMetadata = Field(
        ..., description='Airline program payment specific metadata.'
    )


class AirlineProgramWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlineProgram: AirlineProgram | None = None


class BulkUpdateEventsRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventsToUpdate: Sequence[UpdateEventRequestItem] | None = Field(
        None,
        description='A list of events with the required updates',
        max_length=100,
        min_length=1,
    )
    updateAsync: bool | None = Field(
        False,
        description='Whether to update the events asynchronously or not.',
        examples=[True],
    )


class CreateEventRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Name of the event')
    description: str | None = Field(None, description='Description of the event')
    documents: Sequence[Document] | None = Field(
        None, description='Documents attached for an event'
    )
    startDateTime: DateTimeLocal | None = None
    endDateTime: DateTimeLocal | None = None
    location: EventLocation
    contacts: Sequence[UserId] | None = Field(
        None, description='Contacts for the event'
    )
    bookingGuidelines: Sequence[EventBookingGuideline] | None = Field(
        None, description='A list of booking guideline for the event.'
    )
    allowedBookingTypes: Sequence[EventAllowedBookingType] | None = Field(
        None, description='Allowed booking types for the event group'
    )
    externalId: str | None = Field(
        None, description='External Id for the event', examples=['qwert123']
    )
    eventCustomFieldResponses: Sequence[CustomFieldResponse] | None = Field(
        None, description='List of custom field responses for the event.'
    )
    eventCustomFieldV3Responses: Sequence[CustomFieldPrefilledResponse] | None = Field(
        None, description='List of custom field responses for an event.'
    )
    parentEventId: str | None = Field(
        None, description="Optional parent event's id.", examples=['56789012']
    )
    companyId: CompanyId | None = Field(None, description='Company id')
    bookingStyle: EventBookingStyle | None = Field(
        None,
        description='Whether the event bookings will be self served by the traveler or would be arranged by an agent for the traveler.',
    )
    referenceUsers: Sequence[UserId] | None = Field(
        None, description='Event group reference user ids'
    )
    travelerTypes: Sequence[Persona] | None = Field(
        None, description='Allowed traveler types for the event'
    )
    policyId: UUID | None = Field(
        None,
        description='Policy associated with the event.',
        examples=['f7b3b3b3-7b3b-4b3b-8b3b-3b3b3b3b3b3b'],
    )
    travellers: Sequence[UserId] | None = Field(
        None,
        description='Travellers associated with the event',
        max_length=1000,
        min_length=0,
    )
    type: EventType | None = None
    allowTravelersPolicy: AllowTravelersPolicy | None = None
    emailPreferences: BucketEmailPreferences | None = None
    internalNotes: str | None = Field(
        None,
        description='Internal notes of the event',
        examples=['Use travelers default as payment method.'],
    )
    allowTravelersToRsvp: AllowTravelersToRsvp | None = None
    tripOverrides: TripOverrides | None = None


class EventGroup(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID | None = Field(
        None,
        description='Event Group ID',
        examples=['f49d00fe-1eda-4304-ba79-a980f565281d'],
    )
    name: str | None = Field(None, description='Name of the event group')
    description: str | None = Field(None, description='Description of the event group')
    bookingGuidelines: Sequence[EventBookingGuideline] | None = Field(
        None, description='A list of BookingGuideline for event group'
    )
    metadata: EventGroupMetadata | None = None
    group: UserTripGroup | None = None
    createdBy: UserId | None = None
    allowedBookingTypes: Sequence[EventAllowedBookingType] | None = Field(
        None, description='Allowed booking types for the event group'
    )
    eventUserRsvpList: Sequence[EventUserRsvp] | None = Field(
        None, description='List of user rsvp responses for the event group'
    )
    allowedPaymentConfig: AllowedPaymentConfig | None = None


class GetEventSummariesResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tripEventSummaries: Sequence[TripEventSummary] | None = Field(
        None, description='List of Event summaries for trips'
    )


class VendorProgramPaymentMetadata(
    RootModel[DirectBillingWrapper | AirlineProgramWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: DirectBillingWrapper | AirlineProgramWrapper = Field(
        ...,
        description='Vendor program payment specific metadata.',
        title='VendorProgramPaymentMetadata',
    )


class VendorProgramPaymentDescriptor(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorInfo: VendorInfo | None = None
    vendorProgramPaymentMetadata: VendorProgramPaymentMetadata


class VendorProgramPaymentDescriptorWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorProgramPaymentDescriptor: VendorProgramPaymentDescriptor | None = None


class PaymentSourceDescriptor(
    RootModel[
        CardDescriptorWrapper
        | RewardsProgramDescriptorWrapper
        | VirtualCardDescriptorWrapper
        | CustomPaymentMethodDescriptorWrapper
        | VendorProgramPaymentDescriptorWrapper
        | DelayedInvoicingDescriptorWrapper
        | UnusedCreditDescriptorWrapper
        | CashDescriptorWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        CardDescriptorWrapper
        | RewardsProgramDescriptorWrapper
        | VirtualCardDescriptorWrapper
        | CustomPaymentMethodDescriptorWrapper
        | VendorProgramPaymentDescriptorWrapper
        | DelayedInvoicingDescriptorWrapper
        | UnusedCreditDescriptorWrapper
        | CashDescriptorWrapper
    ) = Field(
        ...,
        description='Descriptor corresponding to the payment source.',
        title='PaymentSourceDescriptor',
    )


class PaymentSourceInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID = Field(
        ...,
        description='Unique identifier identifying this payment source',
        examples=['f49d00fe-1eda-4304-ba79-a980f565281d'],
    )
    type: PaymentSourceType | None = None
    paymentSource: PaymentSourceDescriptor = Field(
        ..., description='Payment source descriptor.'
    )
    mapping: PaymentSourceMapping | None = Field(
        None, description='Mapping i.e. access level, travel type information etc.'
    )
    status: PaymentSourceStatus | None = None


class Event(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str | None = Field(None, description='Event ID')
    name: str | None = Field(None, description='Name of the event')
    type: EventType | None = Field(None, description='Event Type')
    description: str | None = Field(None, description='Description of the event')
    startDateTime: DateTimeLocal | None = None
    endDateTime: DateTimeLocal | None = None
    location: EventLocation | None = None
    group: EventGroup | None = None
    status: EventStatus | None = None
    companyId: EntityId | None = None
    runningStatus: EventRunningStatus | None = None
    paymentSources: Sequence[PaymentSourceInfo] | None = Field(
        None, description='List of payment sources'
    )
    numTravelers: int | None = Field(
        None, description='Number of active travelers in the event', examples=[1]
    )
    customFieldResponses: Sequence[CustomFieldResponse] | None = Field(
        None, description='Preselected custom field responses for the event.'
    )
    customFieldV3Responses: Sequence[CustomFieldPrefilledResponse] | None = Field(
        None, description='List of custom field responses for an event.'
    )
    externalId: str | None = Field(
        None, description='External Id for the event', examples=['qwert123']
    )
    policyId: UUID | None = Field(
        None,
        description='Policy associated with the event.',
        examples=['f7b3b3b3-7b3b-4b3b-8b3b-3b3b3b3b3b3b'],
    )
    parentEventId: str | None = Field(
        None, description="Optional parent event's id.", examples=['56789012']
    )
    isDetached: bool | None = Field(
        None,
        description='Whether the event is detached from the parent event or not.',
        examples=[True],
    )


class GetEventResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    event: Event | None = None


class GetEventsPaymentSourcesResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentSources: Sequence[PaymentSourceInfo] = Field(
        ..., description='List of payment sources'
    )


class ListEventsResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    events: Sequence[Event] | None = Field(None, description='List of events')
    paginationParams: OffsetBasedPaginationResponseParams | None = None
