#!/usr/bin/env python3
"""
Multi-functional script for Spotnana OpenAPI specifications.

This script can:
1. Download OpenAPI YAML files from Spotnana's developer portal
2. Generate Python models from the OpenAPI specifications using datamodel-code-generator
3. Both download and generate in one command

Usage:
    uv run python manage_spotnana_models.py download    # Download specs only
    uv run python manage_spotnana_models.py generate    # Generate models only
    uv run python manage_spotnana_models.py all         # Download and generate
"""

import argparse
import asyncio
import importlib.util
import subprocess
import sys
from pathlib import Path
from typing import Any, TypedDict

import aiohttp
import yaml

SPOTNANA_API_SPECS_URL = "https://developer.spotnana.com/_spec/openapi/"


# Mapping of API domains to their OpenAPI spec URLs
SPOTNANA_API_SPECS = {
    "air": f"{SPOTNANA_API_SPECS_URL}AirApi.yaml?download",
    "hotel": f"{SPOTNANA_API_SPECS_URL}HotelApi.yaml?download",
    "company": f"{SPOTNANA_API_SPECS_URL}CompanyApi.yaml?download",
    "policy": f"{SPOTNANA_API_SPECS_URL}PolicyApi.yaml?download",
    "trip": f"{SPOTNANA_API_SPECS_URL}TripApi.yaml?download",
    "event": f"{SPOTNANA_API_SPECS_URL}EventApi.yaml?download",
    "users": f"{SPOTNANA_API_SPECS_URL}UsersApi.yaml?download",
}


class DiscriminatorFix(TypedDict):
    """Configuration for a single discriminated union fix."""

    schema_name: str
    property: str
    enum_value: str


# Discriminated union fixes to resolve Pydantic validation issues
# These add missing enum values to discriminator fields based on OpenAPI discriminator mappings
DISCRIMINATOR_FIXES: dict[str, list[DiscriminatorFix]] = {
    "air": [
        {
            "schema_name": "CardCondition",
            "property": "type",
            "enum_value": "CARD",  # From discriminator mapping: CARD -> CardCondition
        },
        {
            "schema_name": "SeatNoteRemark",
            "property": "remarkType",
            "enum_value": "SEAT_SELECTION_REMARK",  # From discriminator mapping: SEAT_SELECTION_REMARK -> SeatNoteRemark
        },
    ],
    "company": [
        {
            "schema_name": "BookingFeeInfo",
            "property": "feeType",
            "enum_value": "BOOKING_FEE",  # From discriminator mapping: BOOKING_FEE -> BookingFeeInfo
        },
    ],
    "trip": [
        {
            "schema_name": "BookingFeeInfo",
            "property": "feeType",
            "enum_value": "BOOKING_FEE",  # From discriminator mapping: BOOKING_FEE -> BookingFeeInfo
        },
    ],
    "event": [
        {
            "schema_name": "CarPaymentSourceMetadata",
            "property": "travelType",
            "enum_value": "CAR",  # From discriminator mapping: CAR -> CarPaymentSourceMetadata
        },
    ],
}


async def download_spec(session: aiohttp.ClientSession, domain: str, url: str, base_path: Path) -> bool:
    """Download a single OpenAPI specification file."""
    try:
        print(f"Downloading {domain} API spec from {url}...")

        async with session.get(url) as response:
            if response.status == 200:
                content = await response.text()

                # Extract original filename from URL (e.g., AirApi.yaml)
                filename = url.split("/")[-1].split("?")[0]

                # Save to the appropriate subdirectory with original filename
                output_path = base_path / domain / filename
                output_path.parent.mkdir(exist_ok=True)

                with open(output_path, "w", encoding="utf-8") as f:
                    f.write(content)

                print(f"✅ Successfully downloaded {domain} API spec to {output_path}")

                # Apply discriminated union fixes after download
                _apply_post_download_fixes(output_path, domain)

                return True
            else:
                print(f"❌ Failed to download {domain} API spec: HTTP {response.status}")
                return False

    except Exception as e:
        print(f"❌ Error downloading {domain} API spec: {e}")
        return False


async def download_all_specs(base_path: Path) -> None:
    """Download all Spotnana OpenAPI specifications."""
    print("Starting download of Spotnana OpenAPI specifications...")

    async with aiohttp.ClientSession() as session:
        tasks = [download_spec(session, domain, url, base_path) for domain, url in SPOTNANA_API_SPECS.items()]

        results = await asyncio.gather(*tasks)

    # Summary
    successful = sum(results)
    total = len(results)

    print("\n📊 Download Summary:")
    print(f"   Successful: {successful}/{total}")

    if successful == total:
        print("🎉 All OpenAPI specifications downloaded successfully!")
    else:
        print("⚠️  Some downloads failed. Check the output above for details.")
        sys.exit(1)


def validate_generated_models(models_file: Path, domain: str) -> bool:
    """Validate that generated models can be imported without Pydantic errors."""
    try:
        print(f"   Validating {domain} models...")

        # Create a unique module name to avoid conflicts
        module_name = f"spotnana_{domain}_models_validation"

        # Load the module from the file
        spec = importlib.util.spec_from_file_location(module_name, models_file)
        if spec is None or spec.loader is None:
            print(f"   ❌ Could not load module spec for {domain}")
            return False

        # Import the module - this will trigger Pydantic validation
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)

        # Try to access some common model attributes to ensure they're properly loaded
        model_count = len(
            [attr for attr in dir(module) if not attr.startswith("_") and hasattr(getattr(module, attr), "__mro__")]
        )

        print(f"   ✅ Validation successful: {model_count} models loaded")
        return True

    except Exception as e:
        print(f"   ❌ Validation failed for {domain} models:")
        print(f"      Error: {str(e)}")

        # Provide specific guidance for common Pydantic errors
        if "discriminator" in str(e).lower() and "literal" in str(e).lower():
            print("      💡 This appears to be a discriminated union issue.")
            print("         Check for discriminator fields that need to be Literal types.")
        elif "pydantic" in str(e).lower():
            print("      💡 This appears to be a Pydantic validation issue.")
            print("         The generated models may have validation constraints that fail.")

        return False


def _load_yaml_spec(spec_file: Path) -> dict:
    """Load YAML specification file."""
    with open(spec_file, "r", encoding="utf-8") as f:
        return yaml.safe_load(f)


def _save_yaml_spec(spec_file: Path, spec_data: dict) -> None:
    """Save YAML specification file with consistent formatting."""
    with open(spec_file, "w", encoding="utf-8") as f:
        yaml.safe_dump(spec_data, f, default_flow_style=False, sort_keys=False)


def _get_schema_property(spec_data: dict, schema_name: str, property_name: str) -> dict[str, Any] | None:
    """Get schema property definition, return None if not found."""
    schemas = spec_data.get("components", {}).get("schemas", {})
    schema = schemas.get(schema_name, {})
    properties = schema.get("properties", {})
    return properties.get(property_name)


def _apply_post_download_fixes(spec_file: Path, domain: str) -> None:
    """Apply post-download fixes with proper error handling."""
    try:
        if fix_discriminated_unions(spec_file, domain):
            print(f"🔧 Applied discriminated union fixes for {domain}")
        else:
            print(f"⚠️  Failed to apply discriminated union fixes for {domain}")
    except Exception as e:
        print(f"❌ Error applying post-download fixes for {domain}: {e}")


def _apply_single_fix(spec_data: dict, fix: DiscriminatorFix) -> bool:
    """Apply a single discriminated union fix. Returns True if fix was applied."""
    schema_name = fix["schema_name"]
    property_name = fix["property"]
    enum_value = fix["enum_value"]

    property_def = _get_schema_property(spec_data, schema_name, property_name)

    if property_def is None:
        print(f"      ⚠️  Property {property_name} not found in {schema_name}")
        return False

    if "enum" in property_def:
        print(f"      ℹ️  Enum already exists for {schema_name}.{property_name}")
        return False

    property_def["enum"] = [enum_value]
    print(f"      ✅ Added enum: [{enum_value}] to {schema_name}.{property_name}")
    return True


def fix_discriminated_unions(spec_file: Path, domain: str) -> bool:
    """Fix discriminated union issues in OpenAPI specs by adding missing enum values."""
    if domain not in DISCRIMINATOR_FIXES:
        return True  # No fixes needed for this domain

    try:
        print(f"   🔧 Applying discriminated union fixes for {domain}...")

        spec_data = _load_yaml_spec(spec_file)
        fixes_applied = 0

        # Apply each fix for this domain
        for fix in DISCRIMINATOR_FIXES[domain]:
            if _apply_single_fix(spec_data, fix):
                fixes_applied += 1

        # Save the modified YAML if any fixes were applied
        if fixes_applied > 0:
            _save_yaml_spec(spec_file, spec_data)
            print(f"   ✅ Applied {fixes_applied} discriminated union fix(es) for {domain}")
        else:
            print(f"   ℹ️  No fixes needed for {domain} (already applied or not found)")

        return True

    except Exception as e:
        print(f"   ❌ Failed to apply discriminated union fixes for {domain}: {e}")
        return False


def validate_all_models(base_path: Path) -> bool:
    """Validate all generated Python models."""
    print("Starting validation of all generated models...")

    success_count = 0
    total_count = 0
    failed_domains = []

    for domain in SPOTNANA_API_SPECS.keys():
        models_file = base_path / domain / "models.py"
        total_count += 1

        if not models_file.exists():
            print(f"❌ Models file not found for {domain}: {models_file}")
            print("   Run 'generate' command first to generate models")
            failed_domains.append(domain)
            continue

        if validate_generated_models(models_file, domain):
            success_count += 1
        else:
            failed_domains.append(domain)

    # Summary
    print("\n📊 Validation Summary:")
    print(f"   Successful: {success_count}/{total_count}")

    if failed_domains:
        print(f"   Failed domains: {', '.join(failed_domains)}")
        print("⚠️  Some models failed validation. See errors above for details.")
        return False
    else:
        print("🎉 All models validated successfully!")
        return True


def generate_models(base_path: Path) -> None:
    """Generate Python models from OpenAPI specifications."""
    print("Starting model generation from OpenAPI specifications...")

    success_count = 0
    total_count = 0

    for domain in SPOTNANA_API_SPECS.keys():
        domain_path = base_path / domain
        spec_file = domain_path / f"{domain.title()}Api.yaml"
        models_file = domain_path / "models.py"

        total_count += 1

        if not spec_file.exists():
            print(f"❌ OpenAPI spec not found for {domain}: {spec_file}")
            print("   Run 'download' command first to download specifications")
            continue

        cmd: list[str] = []
        try:
            print(f"Generating models for {domain} API...")

            # Run datamodel-code-generator
            cmd = [
                "datamodel-codegen",
                "--input",
                str(spec_file),
                "--output",
                str(models_file),
                "--input-file-type",
                "openapi",
                "--output-model-type",
                "pydantic_v2.BaseModel",
                "--use-standard-collections",
                "--use-generic-container-types",
                "--enable-faux-immutability",
                "--use-union-operator",
                "--enum-field-as-literal",
                "one",
                "--use-one-literal-as-default",
            ]

            subprocess.run(cmd, capture_output=True, text=True, check=True)

            # Add header comment to mark as auto-generated
            if models_file.exists():
                with open(models_file, "r", encoding="utf-8") as f:
                    content = f.read()

                header = f"""# Code generated from OpenAPI spec. DO NOT EDIT.
# Source: {spec_file.name}
# Generated by: datamodel-code-generator
# 
# This file is automatically generated from the Spotnana {domain.title()} API OpenAPI specification.
# Any manual changes will be overwritten when the models are regenerated.
# 
# To regenerate: uv run python manage_spotnana_models.py generate

"""

                with open(models_file, "w", encoding="utf-8") as f:
                    _ = f.write(header + content)

            print(f"✅ Successfully generated models for {domain} API: {models_file}")

            # Validate the generated models
            if validate_generated_models(models_file, domain):
                success_count += 1
            else:
                print(f"⚠️  Models generated but validation failed for {domain} API")
                print(f"   File: {models_file}")

        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to generate models for {domain} API:")
            print(f"   Command: {' '.join(cmd)}")
            print(f"   Error: {e}")
        except Exception as e:
            print(f"❌ Error generating models for {domain} API: {e}")

    # Summary
    print("\n📊 Model Generation Summary:")
    print(f"   Successful: {success_count}/{total_count}")

    if success_count == total_count:
        print("🎉 All models generated successfully!")
    else:
        print("⚠️  Some model generation failed. Check the output above for details.")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Multi-functional script for Spotnana OpenAPI specifications",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    uv run python manage_spotnana_models.py download    # Download specs only
    uv run python manage_spotnana_models.py generate    # Generate models only  
    uv run python manage_spotnana_models.py validate    # Validate existing models
    uv run python manage_spotnana_models.py all         # Download and generate
        """,
    )

    _ = parser.add_argument("command", choices=["download", "generate", "validate", "all"], help="Command to execute")

    args = parser.parse_args()
    command = args.command  # pyright: ignore[reportAny]

    # Get the directory where this script is located
    script_dir = Path(__file__).parent

    print(f"Base directory: {script_dir}")

    if command in ["download", "all"]:
        print(f"Will download specs to subdirectories under: {script_dir}")
        asyncio.run(download_all_specs(script_dir))

    if command in ["generate", "all"]:
        if command == "all":
            print()  # Add spacing between download and generate
        print(f"Will generate models in subdirectories under: {script_dir}")
        generate_models(script_dir)

    if command == "validate":
        print(f"Will validate models in subdirectories under: {script_dir}")
        success = validate_all_models(script_dir)
        if not success:
            sys.exit(1)


if __name__ == "__main__":
    main()
