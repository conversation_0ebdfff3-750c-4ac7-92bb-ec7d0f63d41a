# Code generated from OpenAPI spec. DO NOT EDIT.
# Source: TripApi.yaml
# Generated by: datamodel-code-generator
# 
# This file is automatically generated from the Spotnana Trip API OpenAPI specification.
# Any manual changes will be overwritten when the models are regenerated.
# 
# To regenerate: uv run python manage_spotnana_models.py generate

# generated by datamodel-codegen:
#   filename:  TripApi.yaml
#   timestamp: 2025-07-15T00:32:29+00:00

from __future__ import annotations

from collections.abc import Mapping, Sequence
from enum import Enum
from typing import Any, Literal
from uuid import UUID

from pydantic import BaseModel, ConfigDict, EmailStr, Field, RootModel, conint, constr


class AirCancellationNotSupportedReason(Enum):
    PARTIAL_LEG_FLOWN = 'PARTIAL_LEG_FLOWN'
    EXCHANGED_TICKET = 'EXCHANGED_TICKET'
    AUTOMATED_REFUND_NOT_SUPPORTED = 'AUTOMATED_REFUND_NOT_SUPPORTED'
    AUTOMATED_EXCHANGE_NOT_SUPPORTED = 'AUTOMATED_EXCHANGE_NOT_SUPPORTED'
    NON_REFUNDABLE_BUT_EXCHANGEABLE = 'NON_REFUNDABLE_BUT_EXCHANGEABLE'
    NON_REFUNDABLE_NON_EXCHANGEABLE = 'NON_REFUNDABLE_NON_EXCHANGEABLE'
    FARE_RULES_UNKNOWN = 'FARE_RULES_UNKNOWN'
    FLIGHT_CHECKED_IN = 'FLIGHT_CHECKED_IN'
    FARE_BREAKUP_UNKNOWN = 'FARE_BREAKUP_UNKNOWN'
    CURRENT_TIME_CLOSE_TO_FLIGHT_DEPARTURE = 'CURRENT_TIME_CLOSE_TO_FLIGHT_DEPARTURE'
    ALL_FLIGHTS_USED = 'ALL_FLIGHTS_USED'
    NON_VOIDABLE_ANCILLARY_TICKET = 'NON_VOIDABLE_ANCILLARY_TICKET'
    SUPPLIER_CANCELLATION_NOT_IMPLEMENTED = 'SUPPLIER_CANCELLATION_NOT_IMPLEMENTED'
    OUTSIDE_BOOKING = 'OUTSIDE_BOOKING'
    UPGRADE_REQUESTED = 'UPGRADE_REQUESTED'


class AirCancellationState(Enum):
    CANCELLABLE_BY_OBT = 'CANCELLABLE_BY_OBT'
    CANCELLABLE_BY_SUPPORT = 'CANCELLABLE_BY_SUPPORT'
    NON_CANCELLABLE = 'NON_CANCELLABLE'
    CANCELLATION_IN_PROGRESS = 'CANCELLATION_IN_PROGRESS'
    CANCELLATION_BY_AGENT_REQUESTED = 'CANCELLATION_BY_AGENT_REQUESTED'
    CANCELLED = 'CANCELLED'
    CANCELLATION_INFO_NOT_AVAILABLE = 'CANCELLATION_INFO_NOT_AVAILABLE'


class AirCancellationType(Enum):
    REFUND = 'REFUND'
    VOID = 'VOID'
    UNUSED_CREDITS = 'UNUSED_CREDITS'


class AirConditioning(Enum):
    UNKNOWN_AC = 'UNKNOWN_AC'
    AC_AVAILABLE = 'AC_AVAILABLE'
    AC_NOT_AVAILABLE = 'AC_NOT_AVAILABLE'


class AirItemType(Enum):
    FLIGHT = 'FLIGHT'
    ANCILLARY = 'ANCILLARY'


class AirPnrRemark(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    remarkString: str = Field(
        ..., description='Remark String', examples=['COMPLEATREMARK']
    )


class AirRsvpResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    notNeeded: bool | None = Field(
        None,
        description='Whether air booking is needed by the traveler or not',
        examples=[True],
    )


class AirSeatAssignmentType(RootModel[Literal['AUTO_ASSIGNED']]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Literal['AUTO_ASSIGNED'] = Field(
        'AUTO_ASSIGNED',
        description='The type of seat assignment',
        examples=['AUTO_ASSIGNED'],
    )


class AircraftAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['789 (widebody)'])
    aircraftType: str | None = Field(None, examples=['widebody'])
    aircraftModel: str | None = Field(None, examples=['787'])


class AircraftAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    aircraftAmenity: AircraftAmenity | None = None


class Airline(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    carrierCode: str = Field(
        ..., description='Unique code for the Airline', examples=['AA']
    )
    airlineName: str = Field(
        ..., description='Full Name of the Airline', examples=['American Airlines']
    )
    isPresentInPreferredVendors: bool | None = Field(
        None,
        description='Whether the airline is present in preferred vendor list. This is an optional field which gets populated only in the preferred vendor autocomplete API.',
        examples=[True],
    )


class AirlineInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlineCode: str = Field(..., description='IATA code for airline.', examples=['AA'])
    airlineName: str = Field(
        ..., description='Airline name', examples=['American Airlines']
    )


class FlightType(Enum):
    UNKNOWN_FLIGHT_TYPE = 'UNKNOWN_FLIGHT_TYPE'
    DOMESTIC = 'DOMESTIC'
    INTERNATIONAL = 'INTERNATIONAL'
    ALL = 'ALL'


class AirlinePref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlines: Sequence[str] | None = None
    flightType: FlightType | None = Field(None, examples=['DOMESTIC'])


class AirportDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airportCode: str = Field(
        ..., description='3-letter code of the airport.', examples=['WRA']
    )
    airportName: str = Field(
        ..., description='Full name of the airport.', examples=['Warder Airport']
    )
    cityName: str | None = Field(
        None,
        description='Name of the city in which the airport is located (or is nearest to).',
        examples=['Werder'],
    )
    countryName: str | None = Field(
        None,
        description='Name of the country in which the airport is located.',
        examples=['Ethiopia'],
    )
    countryCode: str | None = Field(
        None,
        description='2-letter IATA country code associated with the airport.',
        examples=['ET'],
    )
    zoneName: str | None = Field(
        None,
        description='Name of the time zone associated with the airport.',
        examples=['Africa/Addis_Ababa'],
    )
    stateCode: str | None = Field(
        None,
        description='2-letter IATA code for the state in which the airport is located.',
        examples=['CA'],
    )


class AirportInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airportCode: str | None = Field(
        None, description='3-letter code of the airport.', examples=['WRA']
    )
    airportName: str | None = Field(
        None, description='Full name of the airport.', examples=['Warder Airport']
    )
    cityName: str | None = Field(
        None,
        description='Name of the city in which the airport is located (or is nearest to).',
        examples=['Werder'],
    )
    countryName: str | None = Field(
        None,
        description='Name of the country in which the airport is located.',
        examples=['Ethiopia'],
    )
    countryCode: str | None = Field(
        None,
        description='2-letter IATA country code associated with the airport.',
        examples=['ET'],
    )
    zoneName: str | None = Field(
        None,
        description='Name of the time zone associated with the airport.',
        examples=['Africa/Addis_Ababa'],
    )
    stateCode: str | None = Field(
        None,
        description='2-letter IATA code for the state in which the airport is located.',
        examples=['CA'],
    )


class Alliance(Enum):
    UNKNOWN_ALLIANCE = 'UNKNOWN_ALLIANCE'
    STAR_ALLIANCE = 'STAR_ALLIANCE'
    ONEWORLD = 'ONEWORLD'
    SKYTEAM = 'SKYTEAM'
    VANILLA_ALLIANCE = 'VANILLA_ALLIANCE'
    U_FLY_ALLIANCE = 'U_FLY_ALLIANCE'
    VALUE_ALLIANCE = 'VALUE_ALLIANCE'


class AlliancePref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    alliances: Sequence[Alliance]


class AllowedFlightType(Enum):
    ONE_WAY = 'ONE_WAY'
    ROUND_TRIP = 'ROUND_TRIP'
    MULTICITY = 'MULTICITY'


class AncillaryFlightIndex(RootModel[conint(ge=-1)]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: conint(ge=-1) = Field(
        ...,
        description="Index of flight in it's leg to which this ancillary belongs. If an ancillary \nbelongs to all flights, this index should be set to -1\n",
        examples=[0],
    )


class AncillaryLegIndex(RootModel[conint(ge=-1)]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: conint(ge=-1) = Field(
        ...,
        description='Index of leg to which this ancillary belongs. If an ancillary belongs to all legs,\nthis index should be set to -1\n',
        examples=[0],
    )


class AncillaryType(Enum):
    EARLY_BIRD = 'EARLY_BIRD'
    WIFI = 'WIFI'
    CARBON_OFFSET = 'CARBON_OFFSET'


class AnswerPair(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    item: str | None = Field(
        None, description='The option selected from the list of available choices.'
    )
    value: str | None = Field(
        None,
        description='The additional input provided (by the user) while selecting one of the options.',
    )
    description: str | None = Field(
        None, description='Description of the selected option.'
    )


class ApprovalStatus1(Enum):
    APPROVAL_PENDING = 'APPROVAL_PENDING'
    APPROVED = 'APPROVED'
    DENIED = 'DENIED'
    TIMED_OUT = 'TIMED_OUT'
    TIMED_OUT_REMINDER = 'TIMED_OUT_REMINDER'
    APPROVAL_NOT_REQUIRED = 'APPROVAL_NOT_REQUIRED'
    SOFT_APPROVAL_TIMEOUT = 'SOFT_APPROVAL_TIMEOUT'


class ApprovalType1(Enum):
    APPROVAL_TYPE_UNKNOWN = 'APPROVAL_TYPE_UNKNOWN'
    HARD_APPROVAL = 'HARD_APPROVAL'
    SOFT_APPROVAL = 'SOFT_APPROVAL'
    PASSIVE_APPROVAL = 'PASSIVE_APPROVAL'


class ApprovalInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    approvalStatus: ApprovalStatus1 = Field(
        ..., description='The approval status for the PNR'
    )
    approvalType: ApprovalType1 | None = Field(
        None, description='Type of APPROVAL', examples=['SOFT_APPROVAL']
    )


class ApprovalStatus(Enum):
    APPROVAL_PENDING = 'APPROVAL_PENDING'
    APPROVED = 'APPROVED'
    DENIED = 'DENIED'
    TIMED_OUT = 'TIMED_OUT'
    SOFT_APPROVAL_TIMEOUT = 'SOFT_APPROVAL_TIMEOUT'


class ApprovalStatusV2(Enum):
    APPROVAL_PENDING = 'APPROVAL_PENDING'
    APPROVED = 'APPROVED'
    DENIED = 'DENIED'
    AUTO_APPROVED = 'AUTO_APPROVED'
    AUTO_DENIED = 'AUTO_DENIED'


class ApprovalTripIdFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['TRIP_ID_FILTER'] = Field(
        'TRIP_ID_FILTER', examples=['TRIP_ID_FILTER']
    )
    tripIds: Sequence[str] = Field(..., description='Trip ID list')


class ApprovalType(Enum):
    HARD_APPROVAL = 'HARD_APPROVAL'
    SOFT_APPROVAL = 'SOFT_APPROVAL'
    PASSIVE_APPROVAL = 'PASSIVE_APPROVAL'


class ApprovalTypeFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['APPROVAL_TYPE_FILTER'] = Field(
        'APPROVAL_TYPE_FILTER', examples=['APPROVAL_TYPE_FILTER']
    )
    approvalTypes: Sequence[ApprovalType] = Field(..., description='Approval Types')


class AssessmentType(Enum):
    NEUTRAL = 'NEUTRAL'
    BENEFIT = 'BENEFIT'
    RESTRICTION = 'RESTRICTION'
    FEE = 'FEE'


class CancelType(Enum):
    VOID = 'VOID'
    REFUND = 'REFUND'


class LegInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legIdx: int = Field(..., examples=[1])


class SupportedExchange(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legInfos: Sequence[LegInfo] | None = Field(
        None, description='List of legs supported for this exchange', min_length=1
    )


class AutomatedExchangeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    supportedExchanges: Sequence[SupportedExchange] | None = Field(
        None,
        description='Supported automated exchanges. If this list is empty, automated exchange is not \nsupported for this booking.\n',
    )


class BagPolicyApplicability(Enum):
    EACH = 'EACH'
    TOTAL = 'TOTAL'


class BedType(Enum):
    UNKNOWN_BED_TYPE = 'UNKNOWN_BED_TYPE'
    DOUBLE = 'DOUBLE'
    FUTON = 'FUTON'
    KING = 'KING'
    MURPHY = 'MURPHY'
    QUEEN = 'QUEEN'
    SOFA = 'SOFA'
    TATAMI_MATS = 'TATAMI_MATS'
    TWIN = 'TWIN'
    SINGLE = 'SINGLE'
    FULL = 'FULL'
    RUN_OF_THE_HOUSE = 'RUN_OF_THE_HOUSE'
    DORM = 'DORM'
    WATER = 'WATER'
    PULL_OUT = 'PULL_OUT'
    TWIN_XL = 'TWIN_XL'


class BeverageAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(
        None, examples=['Premium alcohol beverages provided']
    )
    beverageType: str | None = Field(None, examples=['premium alcoholic'])
    alcoholCost: str | None = Field(None, examples=['free'])


class BeverageAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    beverageAmenity: BeverageAmenity | None = None


class BlockedAdjacentSeatsAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['No blocked adjacent seats'])
    blockedAdjacentSeatsDescription: str | None = Field(
        None, examples=['Adjacent seats are not blocked on this flight']
    )
    blockedAdjacentSeatsAttrDescription: str | None = Field(None, examples=['no'])


class BlockedAdjacentSeatsAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    blockedAdjacentSeatsAmenity: BlockedAdjacentSeatsAmenity | None = None


class BoardingPass(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legIndex: conint(ge=0) | None = Field(
        None,
        description='Index of leg to which this boarding pass belongs.',
        examples=[0],
    )
    flightIndex: conint(ge=0) | None = Field(
        None,
        description="Index of flight in it's leg to which this boarding pass belongs.",
        examples=[0],
    )
    boardingPriority: str | None = Field(
        None,
        description='Boarding Priority for airlines like Southwest which have open seating.',
        examples=['A'],
    )
    boardingZone: str | None = Field(
        None,
        description='Boarding zone for airlines like Southwest which have open seating.',
        examples=['16'],
    )


class Role(Enum):
    UNKNOWN_ROLE = 'UNKNOWN_ROLE'
    AGENT = 'AGENT'
    TRAVEL_MANAGER = 'TRAVEL_MANAGER'
    TRAVELER = 'TRAVELER'
    REGISTRAR = 'REGISTRAR'


class BookerInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str | None = Field(
        None, description='Name of the Booker', examples=['Harry Potter']
    )
    email: str | None = Field(
        None, description='Email of the Booker', examples=['<EMAIL>']
    )
    role: Role | None = Field(
        None, description='User facing role of the Booker', examples=['AGENT']
    )
    tmcName: str | None = Field(
        None,
        description='Name of the Tmc booker belongs to',
        examples=['Spotnana Technology'],
    )


class Status(Enum):
    ANCILLARY_STATUS_UNKNOWN = 'ANCILLARY_STATUS_UNKNOWN'
    CONFIRMED = 'CONFIRMED'
    PENDING = 'PENDING'
    CANCELLED = 'CANCELLED'
    ELIGIBLE = 'ELIGIBLE'
    NOT_APPLICABLE = 'NOT_APPLICABLE'


class Status1(Enum):
    UNKNOWN_STATUS = 'UNKNOWN_STATUS'
    BOOKED = 'BOOKED'
    EXCHANGED = 'EXCHANGED'
    UPDATED = 'UPDATED'
    CANCELLED = 'CANCELLED'


class BookingSourceClient(Enum):
    ANDROID_APP = 'ANDROID_APP'
    ANDROID_WEB = 'ANDROID_WEB'
    ANDROID_EMBED = 'ANDROID_EMBED'
    IOS_APP = 'IOS_APP'
    IOS_WEB = 'IOS_WEB'
    IOS_EMBED = 'IOS_EMBED'
    WEB = 'WEB'


class BookingStatusType(Enum):
    BOOKED = 'BOOKED'
    NOT_BOOKED = 'NOT_BOOKED'
    OPTED_OUT = 'OPTED_OUT'


class BoolWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    b: bool | None = None


class CO2EmissionDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    emissionValue: float = Field(
        ...,
        description='Estimated C02 emissions value for selected flight and seating class, per passenger (in tons)',
        examples=[10],
    )
    averageEmissionValue: float | None = Field(
        None,
        description='Average estimated C02 emissions per passenger for same route (in tons)',
        examples=[10],
    )
    flightDistanceKm: float | None = Field(
        None,
        description='Total distance flown by the flight in kilometres.',
        examples=[10],
    )
    isApproximate: bool | None = Field(
        None,
        description='Indicates whether the emissions value is approximate or not.',
        examples=[True],
    )


class Cabin(Enum):
    UNKNOWN_CABIN = 'UNKNOWN_CABIN'
    ECONOMY = 'ECONOMY'
    PREMIUM_ECONOMY = 'PREMIUM_ECONOMY'
    BUSINESS = 'BUSINESS'
    FIRST = 'FIRST'


class CancelObjectType(Enum):
    PNR = 'PNR'
    TICKET = 'TICKET'
    JOURNEY = 'JOURNEY'


class CancelObjectDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cancelObjectType: CancelObjectType | None = Field(
        None, description='Type of entity to be cancel.', examples=['PNR']
    )
    cancelObjectId: str | None = Field(None, description='Unique ID of Cancel Object')
    vendorCancellationId: str | None = Field(
        None, description='Vendor cancellation Id of the supplier.'
    )


class Policy(Enum):
    UNKNOWN = 'UNKNOWN'
    NON_REFUNDABLE = 'NON_REFUNDABLE'
    FREE_CANCELLATION_UNTIL = 'FREE_CANCELLATION_UNTIL'
    PARTIALLY_REFUNDABLE = 'PARTIALLY_REFUNDABLE'


class CancellationRequestStatus(Enum):
    CANCELLATION_IN_PROGRESS = 'CANCELLATION_IN_PROGRESS'
    CANCELLATION_BY_AGENT_REQUESTED = 'CANCELLATION_BY_AGENT_REQUESTED'


class PaymentType(Enum):
    UNKNOWN_PAYMENT_TYPE = 'UNKNOWN_PAYMENT_TYPE'
    PAY_AT_VENDOR = 'PAY_AT_VENDOR'
    PREPAID = 'PREPAID'


class RateType(Enum):
    PUBLISHED = 'PUBLISHED'
    CORPORATE = 'CORPORATE'
    SPOTNANA = 'SPOTNANA'


class CarAmenities(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numDoors: int | None = Field(None, description='Number of doors.', examples=[4])
    numLargeBags: int | None = Field(
        None, description='Number of large bags.', examples=[2]
    )
    numSeatBelts: int | None = Field(
        None, description='Number of seats belts.', examples=[5]
    )
    numSeats: int | None = Field(None, description='Number of seats.', examples=[5])
    numSmallBags: int | None = Field(
        None, description='Number of small bags.', examples=[5]
    )


class CarCo2EmissionDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    co2EmissionValue: float = Field(
        ..., description='The CO2 emission value in kgs for car.', examples=[0.01]
    )


class CarRebookReference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cancelledPnrIds: Sequence[str] | None = Field(
        None,
        description='Reference to Spotnana Pnr Ids that were cancelled in favor of this booking.',
    )
    rebookedPnrId: str | None = Field(
        None,
        description="Reference to Spotnana Pnr Id that was booked in favor of this cancellation, Populated for cancelled PNR's if they were cancelled for a rebooking.",
        examples=['*********0'],
    )


class CarRsvpResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    notNeeded: bool | None = Field(
        None,
        description='Whether car booking is needed by the traveler or not',
        examples=[True],
    )


class CarType(Enum):
    OTHER = 'OTHER'
    MINI = 'MINI'
    ECONOMY = 'ECONOMY'
    COMPACT = 'COMPACT'
    MID_SIZE = 'MID_SIZE'
    STANDARD = 'STANDARD'
    FULL_SIZE = 'FULL_SIZE'
    PREMIUM = 'PREMIUM'
    LUXURY = 'LUXURY'
    CONVERTIBLE = 'CONVERTIBLE'
    MINIVAN = 'MINIVAN'
    SUV = 'SUV'
    VAN = 'VAN'
    PICKUP = 'PICKUP'
    SPORTS = 'SPORTS'
    SPECIAL = 'SPECIAL'
    RECREATIONAL_VEHICLE = 'RECREATIONAL_VEHICLE'
    WAGON = 'WAGON'


class CarVendor(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    code: str = Field(..., description='Vendor code', examples=['ZE'])
    name: str = Field(..., description='Vendor name', examples=['HERTZ'])
    isPresentInPreferredVendors: bool | None = Field(
        None,
        description='Whether the car vendor is present in preferred vendor list. This is an optional field which gets populated only in the preferred vendor autocomplete API.',
        examples=[True],
    )


class Type(Enum):
    UNKNOWN = 'UNKNOWN'
    CREDIT = 'CREDIT'
    DEBIT = 'DEBIT'


class CardCompany(Enum):
    NONE = 'NONE'
    VISA = 'VISA'
    MASTERCARD = 'MASTERCARD'
    AMEX = 'AMEX'
    DISCOVER = 'DISCOVER'
    AIR_TRAVEL_UATP = 'AIR_TRAVEL_UATP'
    CARTE_BLANCHE = 'CARTE_BLANCHE'
    DINERS_CLUB = 'DINERS_CLUB'
    JCB = 'JCB'
    BREX = 'BREX'
    UNION_PAY = 'UNION_PAY'
    EURO_CARD = 'EURO_CARD'
    ACCESS_CARD = 'ACCESS_CARD'
    ELO_CARD = 'ELO_CARD'


class CentralCardAccessLevel(Enum):
    UNKNOWN = 'UNKNOWN'
    ORGANIZATION = 'ORGANIZATION'
    LEGAL_ENTITY = 'LEGAL_ENTITY'
    TMC = 'TMC'


class CleaningAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(
        None, examples=['Enhanced aircraft cleaning every flight']
    )
    cleaningDescription: str | None = Field(
        None,
        examples=[
            'This flight features an aircraft that will be thoroughly cleaned using disinfectants for every flight'
        ],
    )
    cleaningAttrDescription: str | None = Field(
        None, examples=['enhanced every flight']
    )


class CleaningAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cleaningAmenity: CleaningAmenity | None = None


class CoachPref(Enum):
    SMOKING = 'SMOKING'
    NON_SMOKING = 'NON_SMOKING'
    PETS_ALLOWED = 'PETS_ALLOWED'
    RESTAURANT = 'RESTAURANT'
    QUIET = 'QUIET'


class CompanyId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID = Field(..., examples=['f49d00fe-1eda-4304-ba79-a980f565281d'])


class CompanyIdFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['COMPANY_ID_FILTER'] = 'COMPANY_ID_FILTER'
    companyIds: Sequence[CompanyId] = Field(..., description='List of company ids')


class CompanySpecifiedAttribute(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fixedColumnName: str = Field(..., examples=['contingentType'])
    value: str = Field(..., examples=['FSTV'])


class CompanySpecifiedAttributeLegalEntity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fieldName: str = Field(
        ..., description='Field name of the attribute', examples=['businessId']
    )
    value: str = Field(
        ..., description='Field value of the attribute', examples=['ABCD']
    )


class Condition(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    url: str = Field(
        ...,
        description='Url for terms and conditions.',
        examples=['https://www.amtrak.com/terms-and-conditions.html'],
    )
    text: str = Field(..., description='Display text for the url.', examples=['Amtrak'])


class ConditionalRate(Enum):
    MILITARY = 'MILITARY'
    AAA = 'AAA'
    GOVERNMENT = 'GOVERNMENT'


class ConfermaInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    deploymentId: int = Field(
        ...,
        description='Unique identifier assigned to the virtual card deployment at the point of creation.',
        examples=[68793680],
    )


class CostCenterId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['731ccbca-0415-6fe1-d235-c324dfbe7423'])


class CounterLocation(Enum):
    UNKNOWN_COUNTER = 'UNKNOWN_COUNTER'
    NON_AIRPORT_LOCATION = 'NON_AIRPORT_LOCATION'
    IN_TERMINAL = 'IN_TERMINAL'
    OFF_AIRPORT_RENTAL_SHUTTLE = 'OFF_AIRPORT_RENTAL_SHUTTLE'
    IN_TERMINAL_RENTAL_SHUTTLE = 'IN_TERMINAL_RENTAL_SHUTTLE'
    ON_AIRPORT_RENTAL_SHUTTLE = 'ON_AIRPORT_RENTAL_SHUTTLE'
    ON_AIRPORT_AIRPORT_SHUTTLE = 'ON_AIRPORT_AIRPORT_SHUTTLE'
    CALL_FOR_SHUTTLE = 'CALL_FOR_SHUTTLE'
    TWO_SHUTTLES_AIRPORT_AND_RENTAL = 'TWO_SHUTTLES_AIRPORT_AND_RENTAL'


class CovidTestingAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['No COVID-19 test required'])
    covidTestingDescription: str | None = Field(
        None,
        examples=[
            'A negative COVID-19 test is not required for this flight; check with the airline for possible destination requirements or other restrictions.'
        ],
    )
    covidTestingAttrDescription: str | None = None


class CovidTestingAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    covidTestingAmenity: CovidTestingAmenity | None = None


class CreatedVia(Enum):
    OFFLINE = 'OFFLINE'
    OBT = 'OBT'
    SHELL = 'SHELL'
    PASSIVE = 'PASSIVE'
    API = 'API'


class TicketType(Enum):
    TICKET_TYPE_UNKNOWN = 'TICKET_TYPE_UNKNOWN'
    ETICKET = 'ETICKET'
    MCO = 'MCO'


class SegmentsAvailable(Enum):
    UNKNOWN = 'UNKNOWN'
    ALL_OPEN = 'ALL_OPEN'
    PARTIAL = 'PARTIAL'
    OTHER = 'OTHER'


class CreditCardAccessType(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    CENTRALISED = 'CENTRALISED'
    INDIVIDUAL = 'INDIVIDUAL'
    PERSONAL = 'PERSONAL'
    TMC = 'TMC'
    APPLICATION = 'APPLICATION'
    ITINERARY = 'ITINERARY'
    EVENTS = 'EVENTS'
    TRAVEL_ARRANGER_MANAGED = 'TRAVEL_ARRANGER_MANAGED'
    COMPANY_TRAVEL_ARRANGER_MANAGED = 'COMPANY_TRAVEL_ARRANGER_MANAGED'
    EVENT_TEMPLATE = 'EVENT_TEMPLATE'


class CreditStatus(Enum):
    STATUS_UNKNOWN = 'STATUS_UNKNOWN'
    OPEN = 'OPEN'
    USED = 'USED'
    RESERVED = 'RESERVED'


class CreditUsageType(Enum):
    CREDIT_USAGE_TYPE_UNKNOWN = 'CREDIT_USAGE_TYPE_UNKNOWN'
    COMPANY = 'COMPANY'
    PERSONAL = 'PERSONAL'


class CustomFieldLocation(Enum):
    POLICY_APPROVAL_EMAIL = 'POLICY_APPROVAL_EMAIL'
    PNR_EMAIL = 'PNR_EMAIL'
    TRIP_EMAIL = 'TRIP_EMAIL'


class CustomFieldOptionsParam(Enum):
    COST_CENTER = 'COST_CENTER'
    LEGAL_ENTITY = 'LEGAL_ENTITY'
    OFFICE = 'OFFICE'
    DEPARTMENT = 'DEPARTMENT'


class CustomFieldType(Enum):
    QUESTION = 'QUESTION'
    MEETING = 'MEETING'
    BUDGET = 'BUDGET'
    BREX_TOKEN = 'BREX_TOKEN'


class DateModel(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$') = (
        Field(..., examples=['2017-07-21'])
    )


class DateTimeLocal(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(
        pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$'
    ) = Field(..., examples=['2017-07-21T17:32'])


class DateTimeOffset(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(
        pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?(Z|([+-](0[0-9]|1[0-4]):([0-5][0-9])))$'
    ) = Field(..., examples=['2017-07-21T17:32Z'])


class DateTimeRange(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    min: DateTimeLocal | None = Field(None, description='Minimum value - inclusive.')
    max: DateTimeLocal | None = Field(None, description='Maximum value - inclusive.')


class DeckLevel(Enum):
    UPPER_DECK = 'UPPER_DECK'
    LOWER_DECK = 'LOWER_DECK'


class DepartmentId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['631ccbcf-9414-5fe0-c234-b324dfbe7422'])


class DepartmentV2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID
    name: str = Field(
        ..., description='Name of the department', examples=['IT Department']
    )
    externalId: str | None = Field(
        None,
        description='External id of the department',
        examples=['department-ext-id'],
    )


class Dimensions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    height: int | None = Field(None, examples=[120])
    width: int | None = Field(None, examples=[240])


class DirectBilling(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    directBillingCode: str | None = Field(
        None, description='Direct billing code provided by the vendor.'
    )
    corporateDiscountCode: str | None = Field(
        None, description='Corporate discount code provided by the vendor.'
    )
    label: str | None = Field(
        None, description='Label for the Direct Billing Payment method.'
    )


class DirectBilling2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorId: str = Field(..., description='Unique vendor id/code', examples=['ZI'])
    directBillingCode: str | None = Field(
        None,
        description='Direct billing code provided by the vendor',
        examples=['123456'],
    )


class DirectBillingWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    directBilling: DirectBilling | None = None


class DirectBillingWrapper2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    directBilling: DirectBilling2


class DocumentType(Enum):
    BOARDING_PASS = 'BOARDING_PASS'
    CONFIRMATION = 'CONFIRMATION'
    INVOICE = 'INVOICE'
    VISA = 'VISA'
    MISCELLANEOUS = 'MISCELLANEOUS'
    OTHERS = 'OTHERS'
    TASK_PROCESSOR = 'TASK_PROCESSOR'
    EVENT_COVER_IMAGE = 'EVENT_COVER_IMAGE'
    LOGO_IMAGE = 'LOGO_IMAGE'


class DoubleListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    dList: Sequence[float] | None = None


class DoubleRange(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    min: float | None = Field(None, description='Minimum value - inclusive.')
    max: float | None = Field(None, description='Maximum value - inclusive.')


class DoubleRangeWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    dRange: DoubleRange | None = None


class DoubleWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    d: float | None = None


class DownloadInvoiceResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    data: str | None = Field(None, examples=['6935813e12584abda0e43d71cd2ea260'])


class Duration(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: str | None = Field(
        None,
        description='Durations define the amount of intervening time in a time interval and are represented by the\nformat P[n]Y[n]M[n]DT[n]H[n]M[n]S.\nThe [n] is replaced by the value for each of the date and time elements that follow the [n].\nLeading zeros are not required. The capital letters P, Y, M, W, D, T, H, M, and S are\ndesignators for each of the date and time elements and are not replaced. P is the duration\ndesignator (for period) placed at the start of the duration representation.\nY is the year designator.\nM is the month designator.\nW is the week designator.\nD is the day designator.\nT is the time designator.\nH is the hour designator.\nM is the minute designator.\nS is the second designator and can include decimal digits with arbitrary precision.\n',
        examples=['PT19H55M'],
    )


class ElectricVehicle(Enum):
    UNKNOWN_EV = 'UNKNOWN_EV'
    YES = 'YES'
    NO = 'NO'


class Relation(Enum):
    RELATION_UNKNOWN = 'RELATION_UNKNOWN'
    SPOUSE = 'SPOUSE'
    PARENT = 'PARENT'
    SIBLING = 'SIBLING'
    CHILD = 'CHILD'
    FRIEND = 'FRIEND'
    RELATIVE = 'RELATIVE'
    COLLEAGUE = 'COLLEAGUE'
    OTHER = 'OTHER'


class EngineType(Enum):
    UNKNOWN_ENGINE = 'UNKNOWN_ENGINE'
    PETROL = 'PETROL'
    DIESEL = 'DIESEL'
    ELECTRIC = 'ELECTRIC'
    CNG = 'CNG'
    HYBRID = 'HYBRID'
    HYDROGEN = 'HYDROGEN'
    MULTI_FUEL = 'MULTI_FUEL'
    ETHANOL = 'ETHANOL'


class EntertainmentAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['Seatback on-demand video'])
    entertainmentType: str | None = Field(None, examples=['on-demand'])
    cost: str | None = Field(None, examples=['free'])


class EntertainmentAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    entertainmentAmenity: EntertainmentAmenity | None = None


class EntityId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID


class EntityNonUUIDId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str | None = None


class EntityType(Enum):
    TRIP = 'TRIP'
    PNR = 'PNR'
    COMPANY = 'COMPANY'
    AIR_ITINERARY = 'AIR_ITINERARY'
    EVENT = 'EVENT'
    LOCATION_IMAGE = 'LOCATION_IMAGE'
    TICKETING_ERROR = 'TICKETING_ERROR'


class EntityTypeV3(Enum):
    PNR = 'PNR'
    TICKET = 'TICKET'


class EntityV3(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: EntityTypeV3
    id: str = Field(..., description='Identifier of the entity')


class Equipment(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    code: str | None = Field(
        None, description='Aircraft equipment code', examples=['777']
    )
    type: str | None = Field(
        None, description='Code representing the type of the equipment', examples=['N']
    )
    name: str | None = Field(
        None,
        description='The name of the flight aircraft type',
        examples=['Boeing 737-800'],
    )


class ErrorParameter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str | None = Field(None, description='Parameter name')
    value: str | None = Field(None, description='Parameter value')


class ErrorMessage(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    errorCode: str | None = Field(
        None, description='Error code to identify the specific errors.'
    )
    message: str | None = Field(
        None, description='Message containing details of error.'
    )
    errorParameters: Sequence[ErrorParameter] | None = Field(
        None, description='Error message parameters.'
    )
    errorDetail: str | None = Field(None, description='More details about the error.')


class ErrorResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    debugIdentifier: str | None = Field(
        None, description='Link to debug the error internally.'
    )
    errorMessages: Sequence[ErrorMessage] | None = None


class EventAllowedBookingType(Enum):
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    CAR = 'CAR'
    RAIL = 'RAIL'


class EventBookingWindow(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    startDateTime: DateTimeLocal
    endDateTime: DateTimeLocal


class EventRsvpState(Enum):
    ADDED = 'ADDED'
    INVITED = 'INVITED'
    INVITE_ACCEPTED = 'INVITE_ACCEPTED'
    INVITE_DECLINED = 'INVITE_DECLINED'
    REMOVED = 'REMOVED'


class EventRunningStatus(Enum):
    IN_PROGRESS = 'IN_PROGRESS'
    UPCOMING = 'UPCOMING'
    COMPLETED = 'COMPLETED'


class EventStatus(Enum):
    DRAFT = 'DRAFT'
    PUBLISH = 'PUBLISH'
    CANCELLED = 'CANCELLED'


class EventType(Enum):
    GENERIC = 'GENERIC'
    PROGRAM = 'PROGRAM'
    PROGRAM_SESSION = 'PROGRAM_SESSION'
    PROGRAM_TRIP = 'PROGRAM_TRIP'


class ExchangePayment(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    originalTicketNumber: str = Field(
        ...,
        description='Ticket number which was exchanged to pay for current ticket.',
        examples=['00129332929'],
    )


class Expiry(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiryMonth: conint(ge=1, le=12) = Field(
        ..., description='Expiry month', examples=[1]
    )
    expiryYear: conint(ge=2000) = Field(..., description='Expiry year', examples=[2010])


class ExpiryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiry: Expiry | None = None


class ReasonCode(Enum):
    PAYMENT_METHOD_MISSING = 'PAYMENT_METHOD_MISSING'
    PAYMENT_GATEWAY_FAILURE = 'PAYMENT_GATEWAY_FAILURE'


class FailureReason(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    reasonCode: ReasonCode | None = Field(
        None,
        description='Reason Code associated with the Failure',
        examples=['PAYMENT_METHOD_MISSING'],
    )


class FareType(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    CHANGEABLE = 'CHANGEABLE'
    REFUNDABLE = 'REFUNDABLE'


class FarePref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fareTypes: Sequence[FareType]


class HiddenStop(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airport: constr(pattern=r'^[A-Z]{3}$') = Field(
        ..., description='3 letter IATA airport code', examples=['LHR']
    )
    arrivalDateTime: DateTimeLocal = Field(..., description='Stop start date time')
    departureDateTime: DateTimeLocal = Field(..., description='Stop end date time')
    duration: Duration | None = Field(None, description='Stop duration')


class FlightAndLegIndex(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightIndex: int | None = Field(
        None, description='Flight index within a leg, starts from 0.', examples=[0]
    )
    legIndex: int | None = Field(
        None, description='Leg index within a pnr, starts from 0.', examples=[1]
    )


class FlightId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legIdx: conint(ge=0) = Field(
        ..., description='Index of leg to which this flight belongs', examples=[0]
    )
    flightIdx: conint(ge=0) = Field(
        ..., description="Index of flight in it's leg", examples=[0]
    )


class FlightMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightId: str | None = Field(
        None,
        description='Unique identifier of the flight.',
        examples=['CgNERU4SA1NGTxoKNTQ1NzI5ODcxMQ'],
    )


class FlightMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightMetadata: FlightMetadata | None = None


class FlightNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    num: str = Field(..., description='Flight number.', examples=['321'])
    airlineCode: str = Field(
        ..., description='Two-letter IATA airline code.', examples=['AA']
    )


class FlightRestrictions(Enum):
    PRE_BOOKING_SEAT_NOT_ALLOWED = 'PRE_BOOKING_SEAT_NOT_ALLOWED'
    POST_BOOKING_SEAT_NOT_ALLOWED = 'POST_BOOKING_SEAT_NOT_ALLOWED'
    SEAT_BOOKING_NOT_ALLOWED_DUE_TO_BRAND = 'SEAT_BOOKING_NOT_ALLOWED_DUE_TO_BRAND'
    SEAT_BOOKING_NOT_ALLOWED = 'SEAT_BOOKING_NOT_ALLOWED'
    SEAT_BOOKING_NOT_ALLOWED_OPEN_SEATING = 'SEAT_BOOKING_NOT_ALLOWED_OPEN_SEATING'
    SEAT_NOT_GUARANTEED_WARNING = 'SEAT_NOT_GUARANTEED_WARNING'
    SEAT_BOOKING_NOT_ALLOWED_DUE_TO_CODESHARE = (
        'SEAT_BOOKING_NOT_ALLOWED_DUE_TO_CODESHARE'
    )
    SUPPRESS_SEAT_LOYALTY_PRICING_WARNING = 'SUPPRESS_SEAT_LOYALTY_PRICING_WARNING'


class FlightSeatStatus(Enum):
    CONFIRMED = 'CONFIRMED'
    PENDING = 'PENDING'
    UNKNOWN = 'UNKNOWN'
    CANCELLED = 'CANCELLED'


class FlightWaiverPolicyLink(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str | None = Field(None, description='Name of the link')
    url: str | None = Field(None, description='Url of the link')


class Type1(Enum):
    UNKNOWN = 'UNKNOWN'
    CARD = 'CARD'
    CASH = 'CASH'
    TFPAY = 'TFPAY'
    CHEQUE = 'CHEQUE'
    BREX_POINTS = 'BREX_POINTS'
    QANTAS_POINTS = 'QANTAS_POINTS'


class FreshFoodAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = None
    freshFoodType: str | None = None
    cost: str | None = None


class FreshFoodAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    freshFoodAmenity: FreshFoodAmenity | None = None


class FreshnessInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    latestVersionDateTime: DateTimeOffset | None = Field(
        None,
        description="Time when the PNR information was updated in Spotnana's systems for the latest PNR\nversion available.\n",
    )
    returnedVersionDateTime: DateTimeOffset | None = Field(
        None,
        description="Time when the PNR information was updated in Spotnana's systems for the PNR whose\ninformation is in this object.\n",
    )


class Gate(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    gate: str | None = Field(None, description='Gate number', examples=['1A'])
    terminal: str | None = Field(None, description='Airport terminal', examples=['1'])


class Gender(Enum):
    MALE = 'MALE'
    FEMALE = 'FEMALE'
    UNSPECIFIED = 'UNSPECIFIED'
    UNDISCLOSED = 'UNDISCLOSED'


class GradeId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['831ccbcb-1416-7fe2-e236-d324dfbe7424'])


class HoldInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    holdDeadline: DateTimeOffset


class PaymentType1(Enum):
    UNKNOWN = 'UNKNOWN'
    PAY_AT_HOTEL = 'PAY_AT_HOTEL'
    PREPAID = 'PREPAID'
    OTHER = 'OTHER'


class Payment1(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentType: PaymentType1 = Field(
        ..., description='Payment type.', examples=['PREPAID']
    )
    description: str | None = Field(
        None, description='Payment method, if payment type is OTHER.'
    )


class HotelAccessibleFeatureType(Enum):
    MOBILITY_ACCESSIBLE_ROOM_WITH_TUB = 'MOBILITY_ACCESSIBLE_ROOM_WITH_TUB'
    MOBILITY_ACCESSIBLE_ROOM_WITH_ROLL_IN_SHOWER = (
        'MOBILITY_ACCESSIBLE_ROOM_WITH_ROLL_IN_SHOWER'
    )
    HEARING_ACCESSIBLE_ROOM = 'HEARING_ACCESSIBLE_ROOM'
    MOBILITY_ACCESSIBLE_ROOM_WITH_TUB_AND_ROLL_IN_SHOWER = (
        'MOBILITY_ACCESSIBLE_ROOM_WITH_TUB_AND_ROLL_IN_SHOWER'
    )
    MOBILITY_ACCESSIBLE_ROOM_WITH_TUB_AND_HEARING_ACCESSIBLE_ROOM = (
        'MOBILITY_ACCESSIBLE_ROOM_WITH_TUB_AND_HEARING_ACCESSIBLE_ROOM'
    )
    MOBILITY_ACCESSIBLE_ROOM_WITH_ROLL_IN_SHOWER_AND_HEARING_ACCESSIBLE_ROOM = (
        'MOBILITY_ACCESSIBLE_ROOM_WITH_ROLL_IN_SHOWER_AND_HEARING_ACCESSIBLE_ROOM'
    )
    MOBILITY_ACCESSIBLE_ROOM_WITH_TRANSFER_SHOWER = (
        'MOBILITY_ACCESSIBLE_ROOM_WITH_TRANSFER_SHOWER'
    )
    MOBILITY_ACCESSIBLE_ROOM_WITH_TUB_AND_TRANSFER_SHOWER = (
        'MOBILITY_ACCESSIBLE_ROOM_WITH_TUB_AND_TRANSFER_SHOWER'
    )
    MOBILITY_ACCESSIBLE_ROOM_WITH_TRANSFER_SHOWER_AND_HEARING_ACCESSIBLE_ROOM = (
        'MOBILITY_ACCESSIBLE_ROOM_WITH_TRANSFER_SHOWER_AND_HEARING_ACCESSIBLE_ROOM'
    )


class HotelAdditionalDetailType(Enum):
    ADDITIONAL_DETAIL_TYPE_UNKNOWN = 'ADDITIONAL_DETAIL_TYPE_UNKNOWN'
    RATE_DESCRIPTION = 'RATE_DESCRIPTION'
    PROPERTY_DESCRIPTION = 'PROPERTY_DESCRIPTION'
    PROPERTY_LOCATION = 'PROPERTY_LOCATION'
    ROOM_INFORMATION = 'ROOM_INFORMATION'
    GUARANTEE_INFORMATION = 'GUARANTEE_INFORMATION'
    DEPOSIT_INFORMATION = 'DEPOSIT_INFORMATION'
    CANCELLATION_INFORMATION = 'CANCELLATION_INFORMATION'
    CHECK_IN_CHECK_OUT_INFORMATION = 'CHECK_IN_CHECK_OUT_INFORMATION'
    EXTRA_CHARGE_INFORMATION = 'EXTRA_CHARGE_INFORMATION'
    TAX_INFORMATION = 'TAX_INFORMATION'
    SERVICE_CHARGE_INFORMATION = 'SERVICE_CHARGE_INFORMATION'
    PACKAGE_INFORMATION = 'PACKAGE_INFORMATION'
    COMMISSION_INFORMATION = 'COMMISSION_INFORMATION'
    MISCELLANEOUS_INFORMATION = 'MISCELLANEOUS_INFORMATION'
    PROMOTIONAL_INFORMATION = 'PROMOTIONAL_INFORMATION'
    INCLUSION_INFORMATION = 'INCLUSION_INFORMATION'
    AMENITY_INFORMATION = 'AMENITY_INFORMATION'
    LATE_ARRIVAL_INFORMATION = 'LATE_ARRIVAL_INFORMATION'
    LATE_DEPARTURE_INFORMATION = 'LATE_DEPARTURE_INFORMATION'
    ADVANCED_BOOKING_INFORMATION = 'ADVANCED_BOOKING_INFORMATION'
    EXTRA_PERSON_INFORMATION = 'EXTRA_PERSON_INFORMATION'
    AREAS_SERVED = 'AREAS_SERVED'
    ONSITE_FACILITIES_INFORMATION = 'ONSITE_FACILITIES_INFORMATION'
    OFFSITE_FACILITIES_INFORMATION = 'OFFSITE_FACILITIES_INFORMATION'
    ONSITE_SERVICES_INFORMATION = 'ONSITE_SERVICES_INFORMATION'
    OFFSITE_SERVICES_INFORMATION = 'OFFSITE_SERVICES_INFORMATION'
    EXTENDED_STAY_INFORMATION = 'EXTENDED_STAY_INFORMATION'
    CORPORATE_BOOKING_INFORMATION = 'CORPORATE_BOOKING_INFORMATION'
    BOOKING_GUIDELINES = 'BOOKING_GUIDELINES'
    GOVERNMENT_BOOKING_POLICY = 'GOVERNMENT_BOOKING_POLICY'
    GROUP_BOOKING_INFORMATION = 'GROUP_BOOKING_INFORMATION'
    RATE_DISCLAIMER_INFORMATION = 'RATE_DISCLAIMER_INFORMATION'
    VISA_TRAVEL_REQUIREMENT_INFORMATION = 'VISA_TRAVEL_REQUIREMENT_INFORMATION'
    SECURITY_INFORMATION = 'SECURITY_INFORMATION'
    ONSITE_RECREATIONAL_ACTIVITIES_INFORMATION = (
        'ONSITE_RECREATIONAL_ACTIVITIES_INFORMATION'
    )
    OFFSITE_RECREATIONAL_ACTIVITIES_INFORMATION = (
        'OFFSITE_RECREATIONAL_ACTIVITIES_INFORMATION'
    )
    GENERAL_MEETING_PLANNING_INFORMATION = 'GENERAL_MEETING_PLANNING_INFORMATION'
    GROUP_MEETING_PLANNING_INFORMATION = 'GROUP_MEETING_PLANNING_INFORMATION'
    CONTRACT_NEGOTIATED_BOOKING_INFORMATION = 'CONTRACT_NEGOTIATED_BOOKING_INFORMATION'
    TRAVEL_INDUSTRY_BOOKING_INFORMATION = 'TRAVEL_INDUSTRY_BOOKING_INFORMATION'
    MEETING_ROOM_DESCRIPTION = 'MEETING_ROOM_DESCRIPTION'
    PET_POLICY_DESCRIPTION = 'PET_POLICY_DESCRIPTION'
    MEAL_PLAN_DESCRIPTION = 'MEAL_PLAN_DESCRIPTION'
    FAMILY_PLAN_DESCRIPTION = 'FAMILY_PLAN_DESCRIPTION'
    CHILDREN_INFORMATION = 'CHILDREN_INFORMATION'
    EARLY_CHECKOUT_DESCRIPTION = 'EARLY_CHECKOUT_DESCRIPTION'
    SPECIAL_OFFERS_DESCRIPTION = 'SPECIAL_OFFERS_DESCRIPTION'
    CATERING_DESCRIPTION = 'CATERING_DESCRIPTION'
    ROOM_DECOR_DESCRIPTION = 'ROOM_DECOR_DESCRIPTION'
    OVERSOLD_POLICY_DESCRIPTION = 'OVERSOLD_POLICY_DESCRIPTION'
    LAST_ROOM_AVAILABILITY_DESCRIPTION = 'LAST_ROOM_AVAILABILITY_DESCRIPTION'
    ROOM_TYPE_UPGRADE_DESCRIPTION = 'ROOM_TYPE_UPGRADE_DESCRIPTION'
    DRIVING_DIRECTIONS = 'DRIVING_DIRECTIONS'
    DRIVING_DIRECTIONS_FROM_THE_NORTH = 'DRIVING_DIRECTIONS_FROM_THE_NORTH'
    DRIVING_DIRECTIONS_FROM_THE_SOUTH = 'DRIVING_DIRECTIONS_FROM_THE_SOUTH'
    DRIVING_DIRECTIONS_FROM_THE_EAST = 'DRIVING_DIRECTIONS_FROM_THE_EAST'
    DRIVING_DIRECTIONS_FROM_THE_WEST = 'DRIVING_DIRECTIONS_FROM_THE_WEST'
    SURCHARGE_INFORMATION = 'SURCHARGE_INFORMATION'
    MINIMUM_STAY_INFORMATION = 'MINIMUM_STAY_INFORMATION'
    MAXIMUM_STAY_INFORMATION = 'MAXIMUM_STAY_INFORMATION'
    CHECK_IN_POLICY = 'CHECK_IN_POLICY'
    CHECK_OUT_POLICY = 'CHECK_OUT_POLICY'
    EXPRESS_CHECK_IN_POLICY = 'EXPRESS_CHECK_IN_POLICY'
    EXPRESS_CHECK_OUT_POLICY = 'EXPRESS_CHECK_OUT_POLICY'
    FACILITY_RESTRICTIONS = 'FACILITY_RESTRICTIONS'
    CUSTOMS_INFORMATION_FOR_MATERIAL = 'CUSTOMS_INFORMATION_FOR_MATERIAL'
    SEASONS = 'SEASONS'
    FOOD_AND_BEVERAGE_MINIMUMS_FOR_GROUPS = 'FOOD_AND_BEVERAGE_MINIMUMS_FOR_GROUPS'
    DEPOSIT_POLICY_FOR_MASTER_ACCOUNT = 'DEPOSIT_POLICY_FOR_MASTER_ACCOUNT'
    DEPOSIT_POLICY_FOR_RESERVATIONS = 'DEPOSIT_POLICY_FOR_RESERVATIONS'
    RESTAURANT_SERVICES = 'RESTAURANT_SERVICES'
    SPECIAL_EVENTS = 'SPECIAL_EVENTS'
    CUISINE_DESCRIPTION = 'CUISINE_DESCRIPTION'


class HotelAmenityType(Enum):
    TWENTY_FOUR_HOUR_FRONT_DESK = 'TWENTY_FOUR_HOUR_FRONT_DESK'
    TWENTY_FOUR_HOUR_ROOM_SERVICE = 'TWENTY_FOUR_HOUR_ROOM_SERVICE'
    TWENTY_FOUR_HOUR_SECURITY = 'TWENTY_FOUR_HOUR_SECURITY'
    ADJOINING_ROOMS = 'ADJOINING_ROOMS'
    AIR_CONDITIONING = 'AIR_CONDITIONING'
    AIRLINE_DESK = 'AIRLINE_DESK'
    ATM_CASH_MACHINE = 'ATM_CASH_MACHINE'
    BABY_SITTING = 'BABY_SITTING'
    BBQ_PICNIC_AREA = 'BBQ_PICNIC_AREA'
    BILINGUAL_STAFF = 'BILINGUAL_STAFF'
    BOOKSTORE = 'BOOKSTORE'
    BOUTIQUES_STORES = 'BOUTIQUES_STORES'
    BRAILED_ELEVATORS = 'BRAILED_ELEVATORS'
    BUSINESS_LIBRARY = 'BUSINESS_LIBRARY'
    CAR_RENTAL_DESK = 'CAR_RENTAL_DESK'
    CASINO = 'CASINO'
    CHECK_CASHING_POLICY = 'CHECK_CASHING_POLICY'
    CHECK_IN_KIOSK = 'CHECK_IN_KIOSK'
    COCKTAIL_LOUNGE = 'COCKTAIL_LOUNGE'
    COFFEE_SHOP = 'COFFEE_SHOP'
    COIN_OPERATED_LAUNDRY = 'COIN_OPERATED_LAUNDRY'
    CONCIERGE_DESK = 'CONCIERGE_DESK'
    CONCIERGE_FLOOR = 'CONCIERGE_FLOOR'
    CONFERENCE_FACILITIES = 'CONFERENCE_FACILITIES'
    COURTYARD = 'COURTYARD'
    CURRENCY_EXCHANGE = 'CURRENCY_EXCHANGE'
    DESK_WITH_ELECTRICAL_OUTLET = 'DESK_WITH_ELECTRICAL_OUTLET'
    DOCTOR_ON_CALL = 'DOCTOR_ON_CALL'
    DOOR_MAN = 'DOOR_MAN'
    DRIVING_RANGE = 'DRIVING_RANGE'
    DRUGSTORE_PHARMACY = 'DRUGSTORE_PHARMACY'
    DUTY_FREE_SHOP = 'DUTY_FREE_SHOP'
    ELEVATORS = 'ELEVATORS'
    EXECUTIVE_FLOOR = 'EXECUTIVE_FLOOR'
    EXERCISE_GYM = 'EXERCISE_GYM'
    EXPRESS_CHECK_IN = 'EXPRESS_CHECK_IN'
    EXPRESS_CHECK_OUT = 'EXPRESS_CHECK_OUT'
    FAMILY_PLAN = 'FAMILY_PLAN'
    FLORIST = 'FLORIST'
    FOLIOS = 'FOLIOS'
    FREE_AIRPORT_SHUTTLE = 'FREE_AIRPORT_SHUTTLE'
    FREE_PARKING = 'FREE_PARKING'
    FREE_TRANSPORTATION = 'FREE_TRANSPORTATION'
    GAME_ROOM = 'GAME_ROOM'
    GIFT_NEWS_STAND = 'GIFT_NEWS_STAND'
    HAIRDRESSER_BARBER = 'HAIRDRESSER_BARBER'
    ACCESSIBLE_FACILITIES = 'ACCESSIBLE_FACILITIES'
    HEALTH_CLUB = 'HEALTH_CLUB'
    HEATED_POOL = 'HEATED_POOL'
    HOUSEKEEPING_DAILY = 'HOUSEKEEPING_DAILY'
    HOUSEKEEPING_WEEKLY = 'HOUSEKEEPING_WEEKLY'
    ICE_MACHINE = 'ICE_MACHINE'
    INDOOR_PARKING = 'INDOOR_PARKING'
    INDOOR_POOL = 'INDOOR_POOL'
    JACUZZI = 'JACUZZI'
    JOGGING_TRACK = 'JOGGING_TRACK'
    KENNELS = 'KENNELS'
    LAUNDRY_VALET_SERVICE = 'LAUNDRY_VALET_SERVICE'
    LIQUOR_STORE = 'LIQUOR_STORE'
    LIVE_ENTERTAINMENT = 'LIVE_ENTERTAINMENT'
    MASSAGE_SERVICES = 'MASSAGE_SERVICES'
    NIGHTCLUB = 'NIGHTCLUB'
    OFF_SITE_PARKING = 'OFF_SITE_PARKING'
    ON_SITE_PARKING = 'ON_SITE_PARKING'
    OUTDOOR_PARKING = 'OUTDOOR_PARKING'
    OUTDOOR_POOL = 'OUTDOOR_POOL'
    PACKAGE_PARCEL_SERVICES = 'PACKAGE_PARCEL_SERVICES'
    PARKING = 'PARKING'
    PHOTOCOPY_CENTER = 'PHOTOCOPY_CENTER'
    PLAYGROUND = 'PLAYGROUND'
    POOL = 'POOL'
    POOLSIDE_SNACK_BAR = 'POOLSIDE_SNACK_BAR'
    PUBLIC_ADDRESS_SYSTEM = 'PUBLIC_ADDRESS_SYSTEM'
    RAMP_ACCESS = 'RAMP_ACCESS'
    RECREATIONAL_VEHICLE_PARKING = 'RECREATIONAL_VEHICLE_PARKING'
    RESTAURANT = 'RESTAURANT'
    ROOM_SERVICE = 'ROOM_SERVICE'
    SAFE_DEPOSIT_BOX = 'SAFE_DEPOSIT_BOX'
    SAUNA = 'SAUNA'
    SECURITY = 'SECURITY'
    SHOE_SHINE_STAND = 'SHOE_SHINE_STAND'
    SHOPPING_MALL = 'SHOPPING_MALL'
    SOLARIUM = 'SOLARIUM'
    SPA = 'SPA'
    SPORTS_BAR = 'SPORTS_BAR'
    STEAM_BATH = 'STEAM_BATH'
    STORAGE_SPACE = 'STORAGE_SPACE'
    SUNDRY_CONVENIENCE_STORE = 'SUNDRY_CONVENIENCE_STORE'
    TECHNICAL_CONCIERGE = 'TECHNICAL_CONCIERGE'
    THEATRE_DESK = 'THEATRE_DESK'
    TOUR_SIGHTSEEING_DESK = 'TOUR_SIGHTSEEING_DESK'
    TRANSLATION_SERVICES = 'TRANSLATION_SERVICES'
    TRAVEL_AGENCY = 'TRAVEL_AGENCY'
    TRUCK_PARKING = 'TRUCK_PARKING'
    VALET_CLEANING = 'VALET_CLEANING'
    DRY_CLEANING = 'DRY_CLEANING'
    VALET_PARKING = 'VALET_PARKING'
    VENDING_MACHINES = 'VENDING_MACHINES'
    VIDEO_TAPES = 'VIDEO_TAPES'
    WAKEUP_SERVICE = 'WAKEUP_SERVICE'
    WHEELCHAIR_ACCESS = 'WHEELCHAIR_ACCESS'
    WHIRLPOOL = 'WHIRLPOOL'
    MULTILINGUAL_STAFF = 'MULTILINGUAL_STAFF'
    WEDDING_SERVICES = 'WEDDING_SERVICES'
    BANQUET_FACILITIES = 'BANQUET_FACILITIES'
    BELL_STAFF_PORTER = 'BELL_STAFF_PORTER'
    BEAUTY_SHOP_SALON = 'BEAUTY_SHOP_SALON'
    COMPLIMENTARY_SELF_SERVICE_LAUNDRY = 'COMPLIMENTARY_SELF_SERVICE_LAUNDRY'
    DIRECT_DIAL_TELEPHONE = 'DIRECT_DIAL_TELEPHONE'
    FEMALE_TRAVELER_ROOM_FLOOR = 'FEMALE_TRAVELER_ROOM_FLOOR'
    PHARMACY = 'PHARMACY'
    STABLES = 'STABLES'
    ONE_TWENTY_AC = 'ONE_TWENTY_AC'
    ONE_TWENTY_DC = 'ONE_TWENTY_DC'
    TWO_TWENTY_AC = 'TWO_TWENTY_AC'
    ACCESSIBLE_PARKING = 'ACCESSIBLE_PARKING'
    TWO_TWENTY_DC = 'TWO_TWENTY_DC'
    BARBEQUE_GRILLS = 'BARBEQUE_GRILLS'
    WOMENS_CLOTHING = 'WOMENS_CLOTHING'
    MENS_CLOTHING = 'MENS_CLOTHING'
    CHILDRENS_CLOTHING = 'CHILDRENS_CLOTHING'
    SHOPS_AND_COMMERCIAL_SERVICES = 'SHOPS_AND_COMMERCIAL_SERVICES'
    VIDEO_GAMES = 'VIDEO_GAMES'
    SPORTS_BAR_OPEN_FOR_LUNCH = 'SPORTS_BAR_OPEN_FOR_LUNCH'
    SPORTS_BAR_OPEN_FOR_DINNER = 'SPORTS_BAR_OPEN_FOR_DINNER'
    ROOM_SERVICE_FULL_MENU = 'ROOM_SERVICE_FULL_MENU'
    ROOM_SERVICE_LIMITED_MENU = 'ROOM_SERVICE_LIMITED_MENU'
    ROOM_SERVICE_LIMITED_HOURS = 'ROOM_SERVICE_LIMITED_HOURS'
    VALET_SAME_DAY_DRY_CLEANING = 'VALET_SAME_DAY_DRY_CLEANING'
    BODY_SCRUB = 'BODY_SCRUB'
    BODY_WRAP = 'BODY_WRAP'
    PUBLIC_AREA_AIR_CONDITIONED = 'PUBLIC_AREA_AIR_CONDITIONED'
    EFOLIO_AVAILABLE_TO_COMPANY = 'EFOLIO_AVAILABLE_TO_COMPANY'
    INDIVIDUAL_EFOLIO_AVAILABLE = 'INDIVIDUAL_EFOLIO_AVAILABLE'
    VIDEO_REVIEW_BILLING = 'VIDEO_REVIEW_BILLING'
    BUTLER_SERVICE = 'BUTLER_SERVICE'
    COMPLIMENTARY_IN_ROOM_COFFEE_OR_TEA = 'COMPLIMENTARY_IN_ROOM_COFFEE_OR_TEA'
    COMPLIMENTARY_BUFFET_BREAKFAST = 'COMPLIMENTARY_BUFFET_BREAKFAST'
    COMPLIMENTARY_COCKTAILS = 'COMPLIMENTARY_COCKTAILS'
    COMPLIMENTARY_COFFEE_IN_LOBBY = 'COMPLIMENTARY_COFFEE_IN_LOBBY'
    COMPLIMENTARY_CONTINENTAL_BREAKFAST = 'COMPLIMENTARY_CONTINENTAL_BREAKFAST'
    COMPLIMENTARY_FULL_AMERICAN_BREAKFAST = 'COMPLIMENTARY_FULL_AMERICAN_BREAKFAST'
    DINNER_DELIVERY_SERVICE_FROM_LOCAL_RESTAURANT = (
        'DINNER_DELIVERY_SERVICE_FROM_LOCAL_RESTAURANT'
    )
    COMPLIMENTARY_NEWSPAPER_DELIVERED_TO_ROOM = (
        'COMPLIMENTARY_NEWSPAPER_DELIVERED_TO_ROOM'
    )
    COMPLIMENTARY_NEWSPAPER_IN_LOBBY = 'COMPLIMENTARY_NEWSPAPER_IN_LOBBY'
    COMPLIMENTARY_SHOESHINE = 'COMPLIMENTARY_SHOESHINE'
    EVENING_RECEPTION = 'EVENING_RECEPTION'
    FRONT_DESK = 'FRONT_DESK'
    GROCERY_SHOPPING_SERVICE_AVAILABLE = 'GROCERY_SHOPPING_SERVICE_AVAILABLE'
    HALAL_FOOD_AVAILABLE = 'HALAL_FOOD_AVAILABLE'
    KOSHER_FOOD_AVAILABLE = 'KOSHER_FOOD_AVAILABLE'
    LIMOUSINE_SERVICE = 'LIMOUSINE_SERVICE'
    MANAGERS_RECEPTION = 'MANAGERS_RECEPTION'
    MEDICAL_FACILITIES_SERVICE = 'MEDICAL_FACILITIES_SERVICE'
    TELEPHONE_JACK_ADAPTOR_AVAILABLE = 'TELEPHONE_JACK_ADAPTOR_AVAILABLE'
    ALL_INCLUSIVE_MEAL_PLAN = 'ALL_INCLUSIVE_MEAL_PLAN'
    BUFFET_BREAKFAST = 'BUFFET_BREAKFAST'
    COMMUNAL_BAR_AREA = 'COMMUNAL_BAR_AREA'
    CONTINENTAL_BREAKFAST = 'CONTINENTAL_BREAKFAST'
    FULL_MEAL_PLAN = 'FULL_MEAL_PLAN'
    FULL_AMERICAN_BREAKFAST = 'FULL_AMERICAN_BREAKFAST'
    MEAL_PLAN_AVAILABLE = 'MEAL_PLAN_AVAILABLE'
    MODIFIED_AMERICAN_MEAL_PLAN = 'MODIFIED_AMERICAN_MEAL_PLAN'
    FOOD_AND_BEVERAGE_OUTLETS = 'FOOD_AND_BEVERAGE_OUTLETS'
    LOUNGES_BARS = 'LOUNGES_BARS'
    BARBER_SHOP = 'BARBER_SHOP'
    VIDEO_CHECKOUT = 'VIDEO_CHECKOUT'
    ONSITE_LAUNDRY = 'ONSITE_LAUNDRY'
    TWENTY_FOUR_HOUR_FOOD_AND_BEVERAGE_KIOSK = (
        'TWENTY_FOUR_HOUR_FOOD_AND_BEVERAGE_KIOSK'
    )
    CONCIERGE_LOUNGE = 'CONCIERGE_LOUNGE'
    PARKING_FEE_MANAGED_BY_HOTEL = 'PARKING_FEE_MANAGED_BY_HOTEL'
    TRANSPORTATION = 'TRANSPORTATION'
    BREAKFAST_SERVED_IN_RESTAURANT = 'BREAKFAST_SERVED_IN_RESTAURANT'
    LUNCH_SERVED_IN_RESTAURANT = 'LUNCH_SERVED_IN_RESTAURANT'
    DINNER_SERVED_IN_RESTAURANT = 'DINNER_SERVED_IN_RESTAURANT'
    FULL_SERVICE_HOUSEKEEPING = 'FULL_SERVICE_HOUSEKEEPING'
    LIMITED_SERVICE_HOUSEKEEPING = 'LIMITED_SERVICE_HOUSEKEEPING'
    HIGH_SPEED_INTERNET_ACCESS_FOR_LAPTOP_IN_PUBLIC_AREAS = (
        'HIGH_SPEED_INTERNET_ACCESS_FOR_LAPTOP_IN_PUBLIC_AREAS'
    )
    WIRELESS_INTERNET_CONNECTION_IN_PUBLIC_AREAS = (
        'WIRELESS_INTERNET_CONNECTION_IN_PUBLIC_AREAS'
    )
    ADDITIONAL_SERVICES_AMENITIES_FACILITIES_ON_PROPERTY = (
        'ADDITIONAL_SERVICES_AMENITIES_FACILITIES_ON_PROPERTY'
    )
    TRANSPORTATION_SERVICES_LOCAL_AREA = 'TRANSPORTATION_SERVICES_LOCAL_AREA'
    TRANSPORTATION_SERVICES_LOCAL_OFFICE = 'TRANSPORTATION_SERVICES_LOCAL_OFFICE'
    DVD_VIDEO_RENTAL = 'DVD_VIDEO_RENTAL'
    PARKING_LOT = 'PARKING_LOT'
    PARKING_DECK = 'PARKING_DECK'
    STREET_SIDE_PARKING = 'STREET_SIDE_PARKING'
    COCKTAIL_LOUNGE_WITH_ENTERTAINMENT = 'COCKTAIL_LOUNGE_WITH_ENTERTAINMENT'
    COCKTAIL_LOUNGE_WITH_LIGHT_FARE = 'COCKTAIL_LOUNGE_WITH_LIGHT_FARE'
    MOTORCYCLE_PARKING = 'MOTORCYCLE_PARKING'
    PHONE_SERVICES = 'PHONE_SERVICES'
    BALLROOM = 'BALLROOM'
    BUS_PARKING = 'BUS_PARKING'
    CHILDRENS_PLAY_AREA = 'CHILDRENS_PLAY_AREA'
    CHILDRENS_NURSERY = 'CHILDRENS_NURSERY'
    DISCO = 'DISCO'
    EARLY_CHECK_IN = 'EARLY_CHECK_IN'
    LOCKER_ROOM = 'LOCKER_ROOM'
    NON_SMOKING_ROOMS_GENERIC = 'NON_SMOKING_ROOMS_GENERIC'
    TRAIN_ACCESS = 'TRAIN_ACCESS'
    AEROBICS_INSTRUCTION = 'AEROBICS_INSTRUCTION'
    BAGGAGE_HOLD = 'BAGGAGE_HOLD'
    BICYCLE_RENTALS = 'BICYCLE_RENTALS'
    DIETICIAN = 'DIETICIAN'
    LATE_CHECK_OUT_AVAILABLE = 'LATE_CHECK_OUT_AVAILABLE'
    PET_SITTING_SERVICES = 'PET_SITTING_SERVICES'
    PRAYER_MATS = 'PRAYER_MATS'
    SPORTS_TRAINER = 'SPORTS_TRAINER'
    TURNDOWN_SERVICE = 'TURNDOWN_SERVICE'
    DVDS_VIDEOS_CHILDREN = 'DVDS_VIDEOS_CHILDREN'
    BANK = 'BANK'
    LOBBY_COFFEE_SERVICE = 'LOBBY_COFFEE_SERVICE'
    BANKING_SERVICES = 'BANKING_SERVICES'
    STAIRWELLS = 'STAIRWELLS'
    PET_AMENITIES_AVAILABLE = 'PET_AMENITIES_AVAILABLE'
    EXHIBITION_CONVENTION_FLOOR = 'EXHIBITION_CONVENTION_FLOOR'
    LONG_TERM_PARKING = 'LONG_TERM_PARKING'
    CHILDREN_NOT_ALLOWED = 'CHILDREN_NOT_ALLOWED'
    CHILDREN_WELCOME = 'CHILDREN_WELCOME'
    COURTESY_CAR = 'COURTESY_CAR'
    HOTEL_DOES_NOT_PROVIDE_PORNOGRAPHIC_FILMS_TV = (
        'HOTEL_DOES_NOT_PROVIDE_PORNOGRAPHIC_FILMS_TV'
    )
    HOTSPOTS = 'HOTSPOTS'
    FREE_HIGH_SPEED_INTERNET_CONNECTION = 'FREE_HIGH_SPEED_INTERNET_CONNECTION'
    INTERNET_SERVICES = 'INTERNET_SERVICES'
    PETS_ALLOWED = 'PETS_ALLOWED'
    GOURMET_HIGHLIGHTS = 'GOURMET_HIGHLIGHTS'
    CATERING_SERVICES = 'CATERING_SERVICES'
    COMPLIMENTARY_BREAKFAST = 'COMPLIMENTARY_BREAKFAST'
    BUSINESS_CENTER = 'BUSINESS_CENTER'
    BUSINESS_SERVICES = 'BUSINESS_SERVICES'
    SECURED_PARKING = 'SECURED_PARKING'
    RACQUETBALL = 'RACQUETBALL'
    SNOW_SPORTS = 'SNOW_SPORTS'
    TENNIS_COURT = 'TENNIS_COURT'
    WATER_SPORTS = 'WATER_SPORTS'
    CHILD_PROGRAMS = 'CHILD_PROGRAMS'
    GOLF = 'GOLF'
    HORSEBACK_RIDING = 'HORSEBACK_RIDING'
    OCEANFRONT = 'OCEANFRONT'
    BEACHFRONT = 'BEACHFRONT'
    HAIR_DRYER = 'HAIR_DRYER'
    IRONING_BOARD = 'IRONING_BOARD'
    HEATED_GUEST_ROOMS = 'HEATED_GUEST_ROOMS'
    TOILET = 'TOILET'
    PARLOR = 'PARLOR'
    VIDEO_GAME_PLAYER = 'VIDEO_GAME_PLAYER'
    THALASSOTHERAPY = 'THALASSOTHERAPY'
    PRIVATE_DINING_FOR_GROUPS = 'PRIVATE_DINING_FOR_GROUPS'
    HEARING_IMPAIRED_SERVICES = 'HEARING_IMPAIRED_SERVICES'
    CARRYOUT_BREAKFAST = 'CARRYOUT_BREAKFAST'
    DELUXE_CONTINENTAL_BREAKFAST = 'DELUXE_CONTINENTAL_BREAKFAST'
    HOT_CONTINENTAL_BREAKFAST = 'HOT_CONTINENTAL_BREAKFAST'
    HOT_BREAKFAST = 'HOT_BREAKFAST'
    PRIVATE_POOL = 'PRIVATE_POOL'
    CONNECTING_ROOMS = 'CONNECTING_ROOMS'
    DATA_PORT = 'DATA_PORT'
    EXTERIOR_CORRIDORS = 'EXTERIOR_CORRIDORS'
    GULF_VIEW = 'GULF_VIEW'
    ACCESSIBLE_ROOMS = 'ACCESSIBLE_ROOMS'
    HIGH_SPEED_INTERNET_ACCESS = 'HIGH_SPEED_INTERNET_ACCESS'
    INTERIOR_CORRIDORS = 'INTERIOR_CORRIDORS'
    HIGH_SPEED_WIRELESS = 'HIGH_SPEED_WIRELESS'
    KITCHENETTE = 'KITCHENETTE'
    PRIVATE_BATH_OR_SHOWER = 'PRIVATE_BATH_OR_SHOWER'
    FIRE_SAFETY_COMPLIANT = 'FIRE_SAFETY_COMPLIANT'
    WELCOME_DRINK = 'WELCOME_DRINK'
    BOARDING_PASS_PRINT_OUT_AVAILABLE = 'BOARDING_PASS_PRINT_OUT_AVAILABLE'
    PRINTING_SERVICES_AVAILABLE = 'PRINTING_SERVICES_AVAILABLE'
    ALL_PUBLIC_AREAS_NON_SMOKING = 'ALL_PUBLIC_AREAS_NON_SMOKING'
    MEETING_ROOMS = 'MEETING_ROOMS'
    MOVIES_IN_ROOM = 'MOVIES_IN_ROOM'
    SECRETARIAL_SERVICE = 'SECRETARIAL_SERVICE'
    SNOW_SKIING = 'SNOW_SKIING'
    WATER_SKIING = 'WATER_SKIING'
    FAX_SERVICE = 'FAX_SERVICE'
    GREAT_ROOM = 'GREAT_ROOM'
    LOBBY = 'LOBBY'
    MULTIPLE_PHONE_LINES_BILLED_SEPARATELY = 'MULTIPLE_PHONE_LINES_BILLED_SEPARATELY'
    UMBRELLAS = 'UMBRELLAS'
    GAS_STATION = 'GAS_STATION'
    GROCERY_STORE = 'GROCERY_STORE'
    TWENTY_FOUR_HOUR_COFFEE_SHOP = 'TWENTY_FOUR_HOUR_COFFEE_SHOP'
    AIRPORT_SHUTTLE_SERVICE = 'AIRPORT_SHUTTLE_SERVICE'
    LUGGAGE_SERVICE = 'LUGGAGE_SERVICE'
    PIANO_BAR = 'PIANO_BAR'
    VIP_SECURITY = 'VIP_SECURITY'
    COMPLIMENTARY_WIRELESS_INTERNET = 'COMPLIMENTARY_WIRELESS_INTERNET'
    CONCIERGE_BREAKFAST = 'CONCIERGE_BREAKFAST'
    SAME_GENDER_FLOOR = 'SAME_GENDER_FLOOR'
    CHILDREN_PROGRAMS = 'CHILDREN_PROGRAMS'
    BUILDING_MEETS_LOCAL_STATE_AND_COUNTRY_BUILDING_CODES = (
        'BUILDING_MEETS_LOCAL_STATE_AND_COUNTRY_BUILDING_CODES'
    )
    INTERNET_BROWSER_ON_TV = 'INTERNET_BROWSER_ON_TV'
    NEWSPAPER = 'NEWSPAPER'
    PARKING_CONTROLLED_ACCESS_GATES_TO_ENTER_PARKING_AREA = (
        'PARKING_CONTROLLED_ACCESS_GATES_TO_ENTER_PARKING_AREA'
    )
    HOTEL_SAFE_DEPOSIT_BOX_NOT_ROOM_SAFE_BOX = (
        'HOTEL_SAFE_DEPOSIT_BOX_NOT_ROOM_SAFE_BOX'
    )
    STORAGE_SPACE_AVAILABLE_FEE = 'STORAGE_SPACE_AVAILABLE_FEE'
    TYPE_OF_ENTRANCES_TO_GUEST_ROOMS = 'TYPE_OF_ENTRANCES_TO_GUEST_ROOMS'
    BEVERAGE_COCKTAIL = 'BEVERAGE_COCKTAIL'
    CELL_PHONE_RENTAL = 'CELL_PHONE_RENTAL'
    COFFEE_TEA = 'COFFEE_TEA'
    EARLY_CHECK_IN_GUARANTEE = 'EARLY_CHECK_IN_GUARANTEE'
    FOOD_AND_BEVERAGE_DISCOUNT = 'FOOD_AND_BEVERAGE_DISCOUNT'
    LATE_CHECK_OUT_GUARANTEE = 'LATE_CHECK_OUT_GUARANTEE'
    ROOM_UPGRADE_CONFIRMED = 'ROOM_UPGRADE_CONFIRMED'
    ROOM_UPGRADE_ON_AVAILABILITY = 'ROOM_UPGRADE_ON_AVAILABILITY'
    SHUTTLE_TO_LOCAL_BUSINESSES = 'SHUTTLE_TO_LOCAL_BUSINESSES'
    SHUTTLE_TO_LOCAL_ATTRACTIONS = 'SHUTTLE_TO_LOCAL_ATTRACTIONS'
    SOCIAL_HOUR = 'SOCIAL_HOUR'
    VIDEO_BILLING = 'VIDEO_BILLING'
    WELCOME_GIFT = 'WELCOME_GIFT'
    HYPOALLERGENIC_ROOMS = 'HYPOALLERGENIC_ROOMS'
    ROOM_AIR_FILTRATION = 'ROOM_AIR_FILTRATION'
    SMOKE_FREE_PROPERTY = 'SMOKE_FREE_PROPERTY'
    WATER_PURIFICATION_SYSTEM_IN_USE = 'WATER_PURIFICATION_SYSTEM_IN_USE'
    POOLSIDE_SERVICE = 'POOLSIDE_SERVICE'
    CLOTHING_STORE = 'CLOTHING_STORE'
    ELECTRIC_CAR_CHARGING_STATIONS = 'ELECTRIC_CAR_CHARGING_STATIONS'
    OFFICE_RENTAL = 'OFFICE_RENTAL'
    PIANO = 'PIANO'
    INCOMING_FAX = 'INCOMING_FAX'
    OUTGOING_FAX = 'OUTGOING_FAX'
    SEMI_PRIVATE_SPACE = 'SEMI_PRIVATE_SPACE'
    LOADING_DOCK = 'LOADING_DOCK'
    BABY_KIT = 'BABY_KIT'
    CHILDRENS_BREAKFAST = 'CHILDRENS_BREAKFAST'
    CLOAKROOM_SERVICE = 'CLOAKROOM_SERVICE'
    COFFEE_LOUNGE = 'COFFEE_LOUNGE'
    EVENTS_TICKET_SERVICE = 'EVENTS_TICKET_SERVICE'
    LATE_CHECK_IN = 'LATE_CHECK_IN'
    LIMITED_PARKING = 'LIMITED_PARKING'
    OUTDOOR_SUMMER_BAR_CAFE = 'OUTDOOR_SUMMER_BAR_CAFE'
    NO_PARKING_AVAILABLE = 'NO_PARKING_AVAILABLE'
    BEER_GARDEN = 'BEER_GARDEN'
    GARDEN_LOUNGE_BAR = 'GARDEN_LOUNGE_BAR'
    SUMMER_TERRACE = 'SUMMER_TERRACE'
    WINTER_TERRACE = 'WINTER_TERRACE'
    ROOF_TERRACE = 'ROOF_TERRACE'
    BEACH_BAR = 'BEACH_BAR'
    HELICOPTER_SERVICE = 'HELICOPTER_SERVICE'
    FERRY = 'FERRY'
    TAPAS_BAR = 'TAPAS_BAR'
    CAFE_BAR = 'CAFE_BAR'
    SNACK_BAR = 'SNACK_BAR'
    GUESTROOM_WIRED_INTERNET = 'GUESTROOM_WIRED_INTERNET'
    GUESTROOM_WIRELESS_INTERNET = 'GUESTROOM_WIRELESS_INTERNET'
    FITNESS_CENTER = 'FITNESS_CENTER'
    ALCOHOLIC_BEVERAGES = 'ALCOHOLIC_BEVERAGES'
    NON_ALCOHOLIC_BEVERAGES = 'NON_ALCOHOLIC_BEVERAGES'
    HEALTH_AND_BEAUTY_SERVICES = 'HEALTH_AND_BEAUTY_SERVICES'
    LOCAL_CALLS = 'LOCAL_CALLS'
    MINIBAR = 'MINIBAR'
    REFRIGERATOR = 'REFRIGERATOR'
    IN_ROOM_SAFE = 'IN_ROOM_SAFE'
    SMOKING_ROOMS_AVAILBLE = 'SMOKING_ROOMS_AVAILBLE'
    MOUNTAIN_VIEW = 'MOUNTAIN_VIEW'
    POOL_VIEW = 'POOL_VIEW'
    BEACH_VIEW = 'BEACH_VIEW'
    OCEAN_VIEW = 'OCEAN_VIEW'
    ROOMS_WITH_BALCONY = 'ROOMS_WITH_BALCONY'
    FAMILY_ROOM = 'FAMILY_ROOM'
    CRIB_CHARGE = 'CRIB_CHARGE'
    ROLLAWAY_ADULT = 'ROLLAWAY_ADULT'
    FREE_WIFI_IN_MEETING_ROOMS = 'FREE_WIFI_IN_MEETING_ROOMS'
    ECO_FRIENDLY = 'ECO_FRIENDLY'
    EXTRA_PERSON = 'EXTRA_PERSON'
    STAY_SAFE = 'STAY_SAFE'
    ENHANCED_HYGIENE_CLEANLINESS_PROTOCOLS = 'ENHANCED_HYGIENE_CLEANLINESS_PROTOCOLS'


class HotelBookingPaymentGuidelines(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    onlyVpayEnabledBooking: bool | None = Field(
        None,
        description='Whether only Vpay enabled bookings are allowed for the event.',
        examples=[True],
    )


class HotelBrand(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    brandCode: str | None = Field(
        None, description='The code of hotel brand.', examples=['HY']
    )
    brandName: str | None = Field(
        None, description='The name of hotel brand.', examples=['Global Hytt Corp.']
    )


class HotelChain(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    chainCode: str | None = Field(
        None, description='The code of hotel chain.', examples=['EM']
    )
    chainName: str | None = Field(
        None, description='The name of hotel chain.', examples=['Mariott']
    )


class HotelCo2EmissionDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    co2EmissionValue: float = Field(
        ...,
        description='CO2 emission value in kg per room for the entire stay.',
        examples=[10.5],
    )


class Type2(Enum):
    GENERAL = 'GENERAL'
    ALERTS = 'ALERTS'
    DINING = 'DINING'
    FACILITIES = 'FACILITIES'
    RECREATION = 'RECREATION'
    SERVICES = 'SERVICES'
    ATTRACTIONS = 'ATTRACTIONS'
    CANCELLATION_POLICY = 'CANCELLATION_POLICY'
    DEPOSIT_POLICY = 'DEPOSIT_POLICY'
    DIRECTIONS = 'DIRECTIONS'
    POLICIES = 'POLICIES'
    SAFETY = 'SAFETY'
    TRANSPORTATION = 'TRANSPORTATION'


class HotelDescription(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type2 | None = Field(
        None, description='Hotel description type', examples=['']
    )
    value: str | None = Field(
        None, description='Hotel description value', examples=['']
    )


class HotelImageCategory(Enum):
    UNKNOWN_CATEGORY = 'UNKNOWN_CATEGORY'
    EXTERIOR_VIEW = 'EXTERIOR_VIEW'
    LOBBY_VIEW = 'LOBBY_VIEW'
    POOL_VIEW = 'POOL_VIEW'
    RESTAURANT = 'RESTAURANT'
    HEALTH_CLUB = 'HEALTH_CLUB'
    GUEST_ROOM = 'GUEST_ROOM'
    SUITE = 'SUITE'
    MEETING_ROOM = 'MEETING_ROOM'
    BALLROOM = 'BALLROOM'
    GOLF_COURSE = 'GOLF_COURSE'
    BEACH = 'BEACH'
    SPA = 'SPA'
    BAR_OR_LOUNGE = 'BAR_OR_LOUNGE'
    RECREATIONAL_FACILITY = 'RECREATIONAL_FACILITY'
    LOGO = 'LOGO'
    BASICS = 'BASICS'
    MAP = 'MAP'
    PROMOTIONAL = 'PROMOTIONAL'
    HOT_NEWS = 'HOT_NEWS'
    MISCELLANEOUS = 'MISCELLANEOUS'
    GUEST_ROOM_AMENITY = 'GUEST_ROOM_AMENITY'
    PROPERTY_AMENITY = 'PROPERTY_AMENITY'
    BUSINESS_CENTER = 'BUSINESS_CENTER'


class HotelItemNameMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrType: Literal['HOTEL'] = Field('HOTEL', examples=['HOTEL'])
    hotelName: str | None = Field(
        None, description='Name of the hotel.', examples=['Hilton']
    )
    hotelCity: str | None = Field(
        None, description='City of the hotel.', examples=['New York']
    )
    checkInDate: DateModel | None = Field(None, description='Check in date.')
    nights: str | None = Field(
        None, description='Number of nights.', examples=['2 Nights']
    )


class HotelOccupancy(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numAdults: int | None = Field(
        None, description='Number of adults in the hotel pnr', examples=[1]
    )
    numChildren: int | None = Field(
        None, description='Number of children in the hotel pnr', examples=[1]
    )
    childAges: Sequence[int] | None = None
    numInfants: int | None = Field(
        None, description='Number of infants in the hotel pnr', examples=[1]
    )


class HotelPrefAmenity(Enum):
    PARKING = 'PARKING'
    FREE_PARKING = 'FREE_PARKING'
    FREE_BREAKFAST = 'FREE_BREAKFAST'
    POOL = 'POOL'
    WIFI = 'WIFI'
    FITNESS_CENTER = 'FITNESS_CENTER'
    FAMILY_FRIENDLY = 'FAMILY_FRIENDLY'
    RECEPTION = 'RECEPTION'
    SPA = 'SPA'
    RESTAURANT = 'RESTAURANT'
    BAR = 'BAR'
    TRANSPORTATION = 'TRANSPORTATION'
    PET_FRIENDLY = 'PET_FRIENDLY'
    BUSINESS_CENTER = 'BUSINESS_CENTER'
    AIR_CONDITIONING = 'AIR_CONDITIONING'
    BEACH_ACCESS = 'BEACH_ACCESS'
    LAUNDRY_SERVICES = 'LAUNDRY_SERVICES'
    ROOM_SERVICE = 'ROOM_SERVICE'
    ACCESSIBLE = 'ACCESSIBLE'


class HotelRateType(Enum):
    PUBLISHED = 'PUBLISHED'
    CORPORATE = 'CORPORATE'
    SPOTNANA = 'SPOTNANA'
    REGULAR = 'REGULAR'
    AAA = 'AAA'
    AARP = 'AARP'
    SENIOR_CITIZEN = 'SENIOR_CITIZEN'
    GOVERNMENT = 'GOVERNMENT'
    MILITARY = 'MILITARY'
    MEMBERSHIP = 'MEMBERSHIP'
    BEST_AVAILABLE_RATE = 'BEST_AVAILABLE_RATE'
    TMC = 'TMC'


class Type3(Enum):
    UNKNOWN = 'UNKNOWN'
    ADJOINING_ROOMS = 'ADJOINING_ROOMS'
    AIR_CONDITIONING = 'AIR_CONDITIONING'
    ALARM_CLOCK = 'ALARM_CLOCK'
    ALL_NEWS_CHANNEL = 'ALL_NEWS_CHANNEL'
    AM_FM_RADIO = 'AM_FM_RADIO'
    BABY_LISTENING_DEVICE = 'BABY_LISTENING_DEVICE'
    BALCONY_LANAI_TERRACE = 'BALCONY_LANAI_TERRACE'
    BARBEQUE_GRILLS = 'BARBEQUE_GRILLS'
    BATH_TUB_WITH_SPRAY_JETS = 'BATH_TUB_WITH_SPRAY_JETS'
    BATHROBE = 'BATHROBE'
    BATHROOM_AMENITIES = 'BATHROOM_AMENITIES'
    BATHROOM_TELEPHONE = 'BATHROOM_TELEPHONE'
    BATHTUB = 'BATHTUB'
    BATHTUB_ONLY = 'BATHTUB_ONLY'
    BATHTUB_SHOWER_COMBINATION = 'BATHTUB_SHOWER_COMBINATION'
    BIDET = 'BIDET'
    BOTTLED_WATER = 'BOTTLED_WATER'
    CABLE_TELEVISION = 'CABLE_TELEVISION'
    COFFEE_TEA_MAKER = 'COFFEE_TEA_MAKER'
    COLOR_TELEVISION = 'COLOR_TELEVISION'
    COMPUTER = 'COMPUTER'
    CONNECTING_ROOMS = 'CONNECTING_ROOMS'
    CONVERTERS_VOLTAGE_ADAPTORS = 'CONVERTERS_VOLTAGE_ADAPTORS'
    COPIER = 'COPIER'
    CORDLESS_PHONE = 'CORDLESS_PHONE'
    CRIBS = 'CRIBS'
    DATA_PORT = 'DATA_PORT'
    DESK = 'DESK'
    DESK_WITH_LAMP = 'DESK_WITH_LAMP'
    DINING_GUIDE = 'DINING_GUIDE'
    DIRECT_DIAL_PHONE_NUMBER = 'DIRECT_DIAL_PHONE_NUMBER'
    DISHWASHER = 'DISHWASHER'
    DOUBLE_BEDS = 'DOUBLE_BEDS'
    DUAL_VOLTAGE_OUTLET = 'DUAL_VOLTAGE_OUTLET'
    ELECTRICAL_CURRENT_VOLTAGE = 'ELECTRICAL_CURRENT_VOLTAGE'
    ERGONOMIC_CHAIR = 'ERGONOMIC_CHAIR'
    EXTENDED_PHONE_CORD = 'EXTENDED_PHONE_CORD'
    FAX_MACHINE = 'FAX_MACHINE'
    FIRE_ALARM = 'FIRE_ALARM'
    FIRE_ALARM_WITH_LIGHT = 'FIRE_ALARM_WITH_LIGHT'
    FIREPLACE = 'FIREPLACE'
    FREE_TOLL_FREE_CALLS = 'FREE_TOLL_FREE_CALLS'
    FREE_CALLS = 'FREE_CALLS'
    FREE_CREDIT_CARD_ACCESS_CALLS = 'FREE_CREDIT_CARD_ACCESS_CALLS'
    FREE_LOCAL_CALLS = 'FREE_LOCAL_CALLS'
    FREE_MOVIES_VIDEO = 'FREE_MOVIES_VIDEO'
    FULL_KITCHEN = 'FULL_KITCHEN'
    GRAB_BARS_IN_BATHROOM = 'GRAB_BARS_IN_BATHROOM'
    GRECIAN_TUB = 'GRECIAN_TUB'
    HAIRDRYER = 'HAIRDRYER'
    HIGH_SPEED_INTERNET_CONNECTION = 'HIGH_SPEED_INTERNET_CONNECTION'
    INTERACTIVE_WEB_TV = 'INTERACTIVE_WEB_TV'
    INTERNATIONAL_DIRECT_DIALING = 'INTERNATIONAL_DIRECT_DIALING'
    INTERNET_ACCESS = 'INTERNET_ACCESS'
    IRON = 'IRON'
    IRONING_BOARD = 'IRONING_BOARD'
    WHIRPOOL = 'WHIRPOOL'
    KING_BED = 'KING_BED'
    KITCHEN = 'KITCHEN'
    KITCHEN_SUPPLIES = 'KITCHEN_SUPPLIES'
    KITCHENETTE = 'KITCHENETTE'
    KNOCK_LIGHT = 'KNOCK_LIGHT'
    LAPTOP = 'LAPTOP'
    LARGE_DESK = 'LARGE_DESK'
    LARGE_WORK_AREA = 'LARGE_WORK_AREA'
    LAUNDRY_BASKET_CLOTHES_HAMPER = 'LAUNDRY_BASKET_CLOTHES_HAMPER'
    LOFT = 'LOFT'
    MICROWAVE = 'MICROWAVE'
    MINIBAR = 'MINIBAR'
    MODEM = 'MODEM'
    MODEM_JACK = 'MODEM_JACK'
    MULTILINE_PHONE = 'MULTILINE_PHONE'
    NEWSPAPER = 'NEWSPAPER'
    NONSMOKING = 'NONSMOKING'
    NOTEPADS = 'NOTEPADS'
    OFFICE_SUPPLIES = 'OFFICE_SUPPLIES'
    OVEN = 'OVEN'
    PAY_PER_VIEW_MOVIES_ON_TV = 'PAY_PER_VIEW_MOVIES_ON_TV'
    PENS = 'PENS'
    PHONE_IN_BATHROOM = 'PHONE_IN_BATHROOM'
    PLATES_AND_BOWLS = 'PLATES_AND_BOWLS'
    POTS_AND_PANS = 'POTS_AND_PANS'
    PRAYER_MATS = 'PRAYER_MATS'
    PRINTER = 'PRINTER'
    PRIVATE_BATHROOM = 'PRIVATE_BATHROOM'
    QUEEN_BED = 'QUEEN_BED'
    RECLINER = 'RECLINER'
    REFRIGERATOR = 'REFRIGERATOR'
    REFRIGERATOR_WITH_ICE_MAKER = 'REFRIGERATOR_WITH_ICE_MAKER'
    REMOTE_CONTROL_TELEVISION = 'REMOTE_CONTROL_TELEVISION'
    ROLLAWAY_BED = 'ROLLAWAY_BED'
    SAFE = 'SAFE'
    SCANNER = 'SCANNER'
    SEPARATE_CLOSET = 'SEPARATE_CLOSET'
    SEPARATE_MODEM_LINE_AVAILABLE = 'SEPARATE_MODEM_LINE_AVAILABLE'
    SHOE_POLISHER = 'SHOE_POLISHER'
    SHOWER_ONLY = 'SHOWER_ONLY'
    SILVERWARE_UTENSILS = 'SILVERWARE_UTENSILS'
    SITTING_AREA = 'SITTING_AREA'
    SMOKE_DETECTORS = 'SMOKE_DETECTORS'
    SMOKING = 'SMOKING'
    SOFA_BED = 'SOFA_BED'
    SPEAKER_PHONE = 'SPEAKER_PHONE'
    STEREO = 'STEREO'
    STOVE = 'STOVE'
    TAPE_RECORDER = 'TAPE_RECORDER'
    TELEPHONE = 'TELEPHONE'
    TELEPHONE_FOR_HEARING_IMPAIRED = 'TELEPHONE_FOR_HEARING_IMPAIRED'
    TELEPHONES_WITH_MESSAGE_LIGHT = 'TELEPHONES_WITH_MESSAGE_LIGHT'
    TOASTER_OVEN = 'TOASTER_OVEN'
    TROUSER_PANT_PRESS = 'TROUSER_PANT_PRESS'
    TURN_DOWN_SERVICE = 'TURN_DOWN_SERVICE'
    TWIN_BED = 'TWIN_BED'
    VAULTED_CEILINGS = 'VAULTED_CEILINGS'
    VCR_MOVIES = 'VCR_MOVIES'
    VCR_PLAYER = 'VCR_PLAYER'
    VIDEO_GAMES_AMENITY = 'VIDEO_GAMES_AMENITY'
    VOICE_MAIL = 'VOICE_MAIL'
    WAKEUP_CALLS = 'WAKEUP_CALLS'
    WATER_CLOSET = 'WATER_CLOSET'
    WATER_PURIFICATION_SYSTEM = 'WATER_PURIFICATION_SYSTEM'
    WET_BAR = 'WET_BAR'
    WIRELESS_INTERNET_CONNECTION = 'WIRELESS_INTERNET_CONNECTION'
    WIRELESS_KEYBOARD = 'WIRELESS_KEYBOARD'
    ADAPTOR_AVAILABLE_FOR_TELEPHONE_PC_USE = 'ADAPTOR_AVAILABLE_FOR_TELEPHONE_PC_USE'
    AIR_CONDITIONING_INDIVIDUALLY_CONTROLLED_IN_ROOM = (
        'AIR_CONDITIONING_INDIVIDUALLY_CONTROLLED_IN_ROOM'
    )
    BATHTUB_ANDWHIRLPOOL_SEPARATE = 'BATHTUB_ANDWHIRLPOOL_SEPARATE'
    TELEPHONE_WITH_DATA_PORTS = 'TELEPHONE_WITH_DATA_PORTS'
    CD_PLAYER = 'CD_PLAYER'
    COMPLIMENTARY_LOCAL_CALLS_TIME_LIMIT = 'COMPLIMENTARY_LOCAL_CALLS_TIME_LIMIT'
    EXTRA_PERSON_CHARGE_FOR_ROLLAWAY_USE = 'EXTRA_PERSON_CHARGE_FOR_ROLLAWAY_USE'
    DOWN_FEATHER_PILLOWS = 'DOWN_FEATHER_PILLOWS'
    DESK_WITH_ELECTRICAL_OUTLET = 'DESK_WITH_ELECTRICAL_OUTLET'
    ESPN_AVAILABLE = 'ESPN_AVAILABLE'
    FOAM_PILLOWS = 'FOAM_PILLOWS'
    HBO_AVAILABLE = 'HBO_AVAILABLE'
    HIGH_CEILINGS = 'HIGH_CEILINGS'
    MARBLE_BATHROOM = 'MARBLE_BATHROOM'
    LIST_OF_MOVIE_CHANNELS_AVAILABLE = 'LIST_OF_MOVIE_CHANNELS_AVAILABLE'
    PETS_ALLOWED = 'PETS_ALLOWED'
    OVERSIZED_BATHTUB = 'OVERSIZED_BATHTUB'
    SHOWER = 'SHOWER'
    SINK_INROOM = 'SINK_INROOM'
    SOUNDPROOFED_ROOM = 'SOUNDPROOFED_ROOM'
    STORAGE_SPACE = 'STORAGE_SPACE'
    TABLES_AND_CHAIRS = 'TABLES_AND_CHAIRS'
    TWOLINE_PHONE = 'TWOLINE_PHONE'
    WALKIN_CLOSET = 'WALKIN_CLOSET'
    WASHER_DRYER = 'WASHER_DRYER'
    WEIGHT_SCALE = 'WEIGHT_SCALE'
    WELCOME_GIFT = 'WELCOME_GIFT'
    SPARE_ELECTRICAL_OUTLET_AVAILABLE_AT_DESK = (
        'SPARE_ELECTRICAL_OUTLET_AVAILABLE_AT_DESK'
    )
    NONREFUNDABLE_CHARGE_FOR_PETS = 'NONREFUNDABLE_CHARGE_FOR_PETS'
    REFUNDABLE_DEPOSIT_FOR_PETS = 'REFUNDABLE_DEPOSIT_FOR_PETS'
    SEPARATE_TUB_AND_SHOWER = 'SEPARATE_TUB_AND_SHOWER'
    ENTRANCE_TYPE_TO_GUEST_ROOM = 'ENTRANCE_TYPE_TO_GUEST_ROOM'
    CEILING_FAN = 'CEILING_FAN'
    CNN_AVAILABLE = 'CNN_AVAILABLE'
    ELECTRICAL_ADAPTORS_AVAILABLE = 'ELECTRICAL_ADAPTORS_AVAILABLE'
    BUFFET_BREAKFAST = 'BUFFET_BREAKFAST'
    ACCESSIBLE_ROOM = 'ACCESSIBLE_ROOM'
    CLOSETS_IN_ROOM = 'CLOSETS_IN_ROOM'
    DVD_PLAYER = 'DVD_PLAYER'
    MINIREFRIGERATOR = 'MINIREFRIGERATOR'
    SEPARATE_LINE_BILLING_FOR_MULTILINE_PHONE = (
        'SEPARATE_LINE_BILLING_FOR_MULTILINE_PHONE'
    )
    SELFCONTROLLED_HEATING_COOLING_SYSTEM = 'SELFCONTROLLED_HEATING_COOLING_SYSTEM'
    TOASTER = 'TOASTER'
    ANALOG_DATA_PORT = 'ANALOG_DATA_PORT'
    COLLECT_CALLS = 'COLLECT_CALLS'
    INTERNATIONAL_CALLS = 'INTERNATIONAL_CALLS'
    CARRIER_ACCESS = 'CARRIER_ACCESS'
    INTERSTATE_CALLS = 'INTERSTATE_CALLS'
    INTRASTATE_CALLS = 'INTRASTATE_CALLS'
    LOCAL_CALLS = 'LOCAL_CALLS'
    LONG_DISTANCE_CALLS = 'LONG_DISTANCE_CALLS'
    OPERATORASSISTED_CALLS = 'OPERATORASSISTED_CALLS'
    CREDIT_CARD_ACCESS_CALLS = 'CREDIT_CARD_ACCESS_CALLS'
    CALLING_CARD_CALLS = 'CALLING_CARD_CALLS'
    TOLL_FREE_CALLS = 'TOLL_FREE_CALLS'
    UNIVERSAL_AC_DC_ADAPTORS = 'UNIVERSAL_AC_DC_ADAPTORS'
    BATHTUB_SEAT = 'BATHTUB_SEAT'
    CANOPY_POSTER_BED = 'CANOPY_POSTER_BED'
    CUPS_GLASSWARE = 'CUPS_GLASSWARE'
    ENTERTAINMENT_CENTER = 'ENTERTAINMENT_CENTER'
    FAMILY_OVERSIZED_ROOM = 'FAMILY_OVERSIZED_ROOM'
    HYPOALLERGENIC_BED = 'HYPOALLERGENIC_BED'
    HYPOALLERGENIC_PILLOWS = 'HYPOALLERGENIC_PILLOWS'
    LAMP = 'LAMP'
    MEAL_INCLUDED_BREAKFAST = 'MEAL_INCLUDED_BREAKFAST'
    MEAL_INCLUDED_CONTINENTAL_BREAKFAST = 'MEAL_INCLUDED_CONTINENTAL_BREAKFAST'
    MEAL_INCLUDED_DINNER = 'MEAL_INCLUDED_DINNER'
    MEAL_INCLUDED_LUNCH = 'MEAL_INCLUDED_LUNCH'
    SHARED_BATHROOM = 'SHARED_BATHROOM'
    TELEPHONE_TDD_TEXTPHONE = 'TELEPHONE_TDD_TEXTPHONE'
    WATER_BED = 'WATER_BED'
    EXTRA_ADULT_CHARGE = 'EXTRA_ADULT_CHARGE'
    EXTRA_CHILD_CHARGE = 'EXTRA_CHILD_CHARGE'
    EXTRA_CHILD_CHARGE_FOR_ROLLAWAY_USE = 'EXTRA_CHILD_CHARGE_FOR_ROLLAWAY_USE'
    MEAL_INCLUDED_FULL_AMERICAN_BREAKFAST = 'MEAL_INCLUDED_FULL_AMERICAN_BREAKFAST'
    FUTON = 'FUTON'
    MURPHY_BED = 'MURPHY_BED'
    TATAMI_MATS = 'TATAMI_MATS'
    SINGLE_BED = 'SINGLE_BED'
    ANNEX_ROOM = 'ANNEX_ROOM'
    FREE_NEWSPAPER = 'FREE_NEWSPAPER'
    HONEYMOON_SUITES = 'HONEYMOON_SUITES'
    COMPLIMENTARY_HIGH_SPEED_INTERNET_IN_ROOM = (
        'COMPLIMENTARY_HIGH_SPEED_INTERNET_IN_ROOM'
    )
    MAID_SERVICE = 'MAID_SERVICE'
    PC_HOOKUP_IN_ROOM = 'PC_HOOKUP_IN_ROOM'
    SATELLITE_TELEVISION = 'SATELLITE_TELEVISION'
    VIP_ROOMS = 'VIP_ROOMS'
    CELL_PHONE_RECHARGER = 'CELL_PHONE_RECHARGER'
    DVR_PLAYER = 'DVR_PLAYER'
    IPOD_DOCKING_STATION = 'IPOD_DOCKING_STATION'
    MEDIA_CENTER = 'MEDIA_CENTER'
    PLUG_AND_PLAY_PANEL = 'PLUG_AND_PLAY_PANEL'
    SATELLITE_RADIO = 'SATELLITE_RADIO'
    VIDEO_ON_DEMAND = 'VIDEO_ON_DEMAND'
    EXTERIOR_CORRIDORS = 'EXTERIOR_CORRIDORS'
    GULF_VIEW = 'GULF_VIEW'
    ACCESSIBLE_ROOM_AMENITY = 'ACCESSIBLE_ROOM_AMENITY'
    INTERIOR_CORRIDORS = 'INTERIOR_CORRIDORS'
    MOUNTAIN_VIEW = 'MOUNTAIN_VIEW'
    OCEAN_VIEW = 'OCEAN_VIEW'
    HIGH_SPEED_INTERNET_ACCESS_FEE = 'HIGH_SPEED_INTERNET_ACCESS_FEE'
    HIGH_SPEED_WIRELESS = 'HIGH_SPEED_WIRELESS'
    PREMIUM_MOVIE_CHANNELS = 'PREMIUM_MOVIE_CHANNELS'
    SLIPPERS = 'SLIPPERS'
    FIRST_NIGHTERS_KIT = 'FIRST_NIGHTERS_KIT'
    CHAIR_PROVIDED_WITH_DESK = 'CHAIR_PROVIDED_WITH_DESK'
    PILLOW_TOP_MATTRESS = 'PILLOW_TOP_MATTRESS'
    FEATHER_BED = 'FEATHER_BED'
    DUVET = 'DUVET'
    LUXURY_LINEN_TYPE = 'LUXURY_LINEN_TYPE'
    INTERNATIONAL_CHANNELS = 'INTERNATIONAL_CHANNELS'
    PANTRY = 'PANTRY'
    DISHCLEANING_SUPPLIES = 'DISHCLEANING_SUPPLIES'
    DOUBLE_VANITY = 'DOUBLE_VANITY'
    LIGHTED_MAKEUP_MIRROR = 'LIGHTED_MAKEUP_MIRROR'
    UPGRADED_BATHROOM_AMENITIES = 'UPGRADED_BATHROOM_AMENITIES'
    VCR_PLAYER_AVAILABLE_AT_FRONT_DESK = 'VCR_PLAYER_AVAILABLE_AT_FRONT_DESK'
    INSTANT_HOT_WATER = 'INSTANT_HOT_WATER'
    OUTDOOR_SPACE = 'OUTDOOR_SPACE'
    HINOKI_TUB = 'HINOKI_TUB'
    PRIVATE_POOL = 'PRIVATE_POOL'
    HIGH_DEFINITION_HD_FLAT_PANEL_TELEVISION_32_INCHES_OR_GREATER = (
        'HIGH_DEFINITION_HD_FLAT_PANEL_TELEVISION_32_INCHES_OR_GREATER'
    )
    ROOM_WINDOWS_OPEN = 'ROOM_WINDOWS_OPEN'
    BEDDING_TYPE_UNKNOWN_OR_UNSPECIFIED = 'BEDDING_TYPE_UNKNOWN_OR_UNSPECIFIED'
    FULL_BED = 'FULL_BED'
    ROUND_BED = 'ROUND_BED'
    TV = 'TV'
    CHILD_ROLLAWAY = 'CHILD_ROLLAWAY'
    DVD_PLAYER_AVAILABLE_AT_FRONT_DESK = 'DVD_PLAYER_AVAILABLE_AT_FRONT_DESK'
    VIDEO_GAME_PLAYER = 'VIDEO_GAME_PLAYER'
    VIDEO_GAME_PLAYER_AVAILABLE_AT_FRONT_DESK = (
        'VIDEO_GAME_PLAYER_AVAILABLE_AT_FRONT_DESK'
    )
    DINING_ROOM_SEATS = 'DINING_ROOM_SEATS'
    FULL_SIZE_MIRROR = 'FULL_SIZE_MIRROR'
    MOBILE_CELLULAR_PHONES = 'MOBILE_CELLULAR_PHONES'
    MOVIES = 'MOVIES'
    MULTIPLE_CLOSETS = 'MULTIPLE_CLOSETS'
    PLATES_GLASSWARE = 'PLATES_GLASSWARE'
    SAFE_LARGE_ENOUGH_TO_ACCOMMODATE_A_LAPTOP = (
        'SAFE_LARGE_ENOUGH_TO_ACCOMMODATE_A_LAPTOP'
    )
    BED_LINEN_THREAD_COUNT = 'BED_LINEN_THREAD_COUNT'
    BLACKOUT_CURTAIN = 'BLACKOUT_CURTAIN'
    BLURAY_PLAYER = 'BLURAY_PLAYER'
    DEVICE_WITH_MP3 = 'DEVICE_WITH_MP3'
    NO_ADULT_CHANNELS_OR_ADULT_CHANNEL_LOCK = 'NO_ADULT_CHANNELS_OR_ADULT_CHANNEL_LOCK'
    NONALLERGENIC_ROOM = 'NONALLERGENIC_ROOM'
    PILLOW_TYPE = 'PILLOW_TYPE'
    SEATING_AREA_WITH_SOFA_CHAIR = 'SEATING_AREA_WITH_SOFA_CHAIR'
    SEPARATE_TOILET_AREA = 'SEPARATE_TOILET_AREA'
    WEB_ENABLED = 'WEB_ENABLED'
    WIDESCREEN_TV = 'WIDESCREEN_TV'
    OTHER_DATA_CONNECTION = 'OTHER_DATA_CONNECTION'
    PHONELINE_BILLED_SEPARATELY = 'PHONELINE_BILLED_SEPARATELY'
    SEPARATE_TUB_OR_SHOWER = 'SEPARATE_TUB_OR_SHOWER'
    VIDEO_GAMES = 'VIDEO_GAMES'
    ROOF_VENTILATOR = 'ROOF_VENTILATOR'
    CHILDRENS_PLAYPEN = 'CHILDRENS_PLAYPEN'
    PLUNGE_POOL = 'PLUNGE_POOL'
    DVD_MOVIES = 'DVD_MOVIES'
    AIR_FILTRATION = 'AIR_FILTRATION'


class HotelRoomAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    additionalInfo: str | None = Field(
        None, description='Extra information about the room amenity'
    )
    complimentary: bool | None = Field(
        None, description='Is amenity complimentary', examples=[True]
    )
    type: Type3 | None = Field(
        None, description='Room amenity type', examples=['WEB_ENABLED']
    )


class HotelRoomMealType(Enum):
    UNKNOWN = 'UNKNOWN'
    ALL_INCLUSIVE = 'ALL_INCLUSIVE'
    AMERICAN = 'AMERICAN'
    BED_AND_BREAKFAST = 'BED_AND_BREAKFAST'
    BUFFET_BREAKFAST = 'BUFFET_BREAKFAST'
    CARIBBEAN_BREAKFAST = 'CARIBBEAN_BREAKFAST'
    CONTINENTAL_BREAKFAST = 'CONTINENTAL_BREAKFAST'
    ENGLISH_BREAKFAST = 'ENGLISH_BREAKFAST'
    EUROPEAN_PLAN = 'EUROPEAN_PLAN'
    FAMILY_PLAN = 'FAMILY_PLAN'
    FULL_BOARD = 'FULL_BOARD'
    FULL_BREAKFAST = 'FULL_BREAKFAST'
    HALF_BOARD_MODIFIED_AMERICAN_PLAN = 'HALF_BOARD_MODIFIED_AMERICAN_PLAN'
    AS_BROCHURED = 'AS_BROCHURED'
    ROOM_ONLY = 'ROOM_ONLY'
    SELF_CATERING = 'SELF_CATERING'
    BERMUDA = 'BERMUDA'
    DINNER_BED_AND_BREAKFAST_PLAN = 'DINNER_BED_AND_BREAKFAST_PLAN'
    FAMILY_AMERICAN = 'FAMILY_AMERICAN'
    BREAKFAST_MEAL_PLAN = 'BREAKFAST_MEAL_PLAN'
    MODIFIED = 'MODIFIED'
    LUNCH_MEAL_PLAN = 'LUNCH_MEAL_PLAN'
    DINNER_MEAL_PLAN = 'DINNER_MEAL_PLAN'
    BREAKFAST_AND_LUNCH = 'BREAKFAST_AND_LUNCH'


class HotelRoomMealsIncluded(Enum):
    BREAKFAST = 'BREAKFAST'
    LUNCH = 'LUNCH'
    DINNER = 'DINNER'


class HotelRsvpResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    notNeeded: bool | None = Field(
        None,
        description='Whether hotel booking is needed by the traveler or not',
        examples=[True],
    )


class RoomLocation(Enum):
    HIGH_FLOOR = 'HIGH_FLOOR'
    LOW_FLOOR = 'LOW_FLOOR'


class RoomFeature(Enum):
    CRIB = 'CRIB'
    ROLLAWAY_BED = 'ROLLAWAY_BED'
    FEATHER_FREE_ROOM = 'FEATHER_FREE_ROOM'
    ACCESSIBLE_ROOM = 'ACCESSIBLE_ROOM'
    NEAR_ELEVATOR = 'NEAR_ELEVATOR'


class CheckIn(Enum):
    EARLY_CHECK_IN = 'EARLY_CHECK_IN'
    LATE_CHECK_IN = 'LATE_CHECK_IN'


class IdType(Enum):
    TAX_ID = 'TAX_ID'
    VAT_ID = 'VAT_ID'
    VAT_REG_NO = 'VAT_REG_NO'
    GST_NO = 'GST_NO'
    ABN = 'ABN'
    GSTIN = 'GSTIN'
    SIRET = 'SIRET'
    BUSINESS_REG_NO = 'BUSINESS_REG_NO'
    UID = 'UID'
    CVR_NO = 'CVR_NO'
    EIN = 'EIN'


class IdInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    idType: IdType = Field(..., description='The type of Id.', examples=['TAX_ID'])
    value: str = Field(..., description='Value of the Id.', examples=['TXG239023092'])


class Image(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    data: str | None = Field(None, examples=['6935813e12584abda0e43d71cd2ea260'])
    dimensions: Dimensions | None = None
    url: str | None = Field(
        None,
        examples=[
            'https://static.wixstatic.com/media/73f2e2_6935813e12584abda0e43d71cd2ea260~mv2.png/v1/fill/w_630,h_94,al_c,q_85,usm_0.66_1.00_0.01/Spotnana%403x.webp'
        ],
    )


class ImageGroup(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    caption: str | None = Field(
        None, description='Caption for the image.', examples=['Exterior']
    )
    images: Sequence[Image] = Field(..., description='List of images.')


class Type4(Enum):
    UNKNOWN = 'UNKNOWN'
    VISA = 'VISA'


class ImmigrationDocument(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    authorizedStayDuration: Duration | None = Field(
        None, description='Duration of the stay authorized by the immigration document.'
    )
    docId: str = Field(
        ...,
        description='The ID of the immigration document.',
        examples=['ImmigrationDocumentID'],
    )
    expiryDate: DateModel = Field(
        ..., description='The date on which the immigration document expires.'
    )
    issueCountry: str = Field(
        ...,
        description='The country that issued the immigration document.',
        examples=['IN'],
    )
    issuedDate: DateModel | None = Field(
        None, description='The date on which the immigration document was issued.'
    )
    nationalityCountry: str | None = Field(None, examples=['IN'])
    reentryRequirementDuration: Duration | None = None
    type: Type4 | None = Field(None, examples=['VISA'])


class ImmigrationDocumentWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    immigrationDoc: ImmigrationDocument | None = None


class IncludeLocation(Enum):
    BOOKING_CONFIRMATION_EMAILS = 'BOOKING_CONFIRMATION_EMAILS'
    APPROVAL_EMAILS = 'APPROVAL_EMAILS'
    COMPANY_REPORTS = 'COMPANY_REPORTS'
    CONSOLIDATED_ITINERARY_EMAILS = 'CONSOLIDATED_ITINERARY_EMAILS'


class Int32Range(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    min: int | None = Field(None, description='Minimum value - inclusive.')
    max: int | None = Field(None, description='Maximum value - inclusive.')


class Int32RangeWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iRange: Int32Range | None = None


class Int32Wrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    i: int | None = None


class Int64Wrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    l: int | None = None


class IntListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iList: Sequence[int] | None = None


class DocumentType1(Enum):
    INVOICE = 'INVOICE'
    RECEIPT = 'RECEIPT'
    NONE = 'NONE'


class InvoiceDataRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tmcId: str | None = Field(
        None,
        description='TMC for which invoice is being requested. Deprecated in favor of invoiceIds.',
        examples=['15c94ac1-f540-4916-9243-306a75fbd8a9'],
    )
    invoiceNumbers: Sequence[str] | None = Field(
        None,
        description='One or more invoice numbers being requested. Deprecated in favor of invoiceIds.',
    )
    invoiceIds: Sequence[UUID] | None = Field(
        None, description='One or more Invoice IDs being requested'
    )


class PurchaseType(Enum):
    TRIP_FEE = 'TRIP_FEE'
    FLIGHT = 'FLIGHT'
    ANCILLARY = 'ANCILLARY'
    HOTEL = 'HOTEL'
    CAR = 'CAR'
    RAIL = 'RAIL'


class Type5(Enum):
    PURCHASE = 'PURCHASE'
    REFUND = 'REFUND'


class KeyValue(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    key: str | None = Field(None, description='Header key.')
    value: str | None = Field(None, description='Header value.')


class KnownTravelerNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    number: str = Field(..., examples=['12345'])
    issueCountry: str = Field(..., examples=['US'])


class KnownTravelerNumberWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ktn: KnownTravelerNumber | None = None


class Latlng(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    latitude: float = Field(
        ..., description='Latitude of the Location', examples=[77.1025]
    )
    longitude: float = Field(
        ..., description='Longitude of the Location', examples=[28.7041]
    )


class LayoutAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['1-2-1 seat layout'])
    directAisleAccess: str | None = Field(None, examples=['yes'])


class LayoutAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    layoutAmenity: LayoutAmenity | None = None


class Restriction(Enum):
    NONE = 'NONE'
    SEAT_EDIT_NOT_ALLOWED = 'SEAT_EDIT_NOT_ALLOWED'
    LOYALTY_EDIT_NOT_ALLOWED = 'LOYALTY_EDIT_NOT_ALLOWED'
    KTN_EDIT_NOT_ALLOWED = 'KTN_EDIT_NOT_ALLOWED'
    REDRESS_EDIT_NOT_ALLOWED = 'REDRESS_EDIT_NOT_ALLOWED'
    SSR_EDIT_NOT_ALLOWED = 'SSR_EDIT_NOT_ALLOWED'
    OSI_EDIT_NOT_ALLOWED = 'OSI_EDIT_NOT_ALLOWED'
    SEAT_CHANGE_NOT_ALLOWED = 'SEAT_CHANGE_NOT_ALLOWED'


class LegMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legId: str | None = Field(
        None,
        description='Unique identifier of the leg.',
        examples=['CgNTRk8SA0RFThoKNTQ1NzI5ODcxMQ=='],
    )


class LegMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legMetadata: LegMetadata | None = None


class LegalEntityId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['fc1ccbce-8413-4fe9-b233-a324dfbe7421'])


class LegalEntityIdListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legalEntityIdList: Sequence[LegalEntityId] | None = None


class LegalEntityIdWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legalEntityId: LegalEntityId | None = None


class Unit(Enum):
    UNKNOWN_UNIT = 'UNKNOWN_UNIT'
    KM = 'KM'
    MILE = 'MILE'


class Length(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    length: float = Field(
        ..., description='Distance from search point.', examples=[150]
    )
    unit: Unit = Field(
        ..., description='Unit of measure being applied.', examples=['MILE']
    )


class LengthWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    length: Length | None = None


class LimoAmenities(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numPassengers: int | None = Field(None, description='Number of passengers allowed.')
    numSmallBags: int | None = Field(None, description='Number of small bags allowed.')
    numLargeBags: int | None = Field(None, description='Number of large bags allowed.')


class CarType1(Enum):
    UNKNOWN_CAR_TYPE = 'UNKNOWN_CAR_TYPE'
    STANDARD_CAR = 'STANDARD_CAR'
    EXECUTIVE_CAR = 'EXECUTIVE_CAR'
    BUSINESS_VAN = 'BUSINESS_VAN'
    FIRST_CLASS = 'FIRST_CLASS'
    SUV = 'SUV'
    SEDAN = 'SEDAN'
    EXECUTIVE_SEDAN = 'EXECUTIVE_SEDAN'
    LUXURY_SEDAN = 'LUXURY_SEDAN'
    EXECUTIVE_VAN = 'EXECUTIVE_VAN'
    BUSINESS_SEDAN = 'BUSINESS_SEDAN'


class PaymentType2(Enum):
    UNKNOWN_PAYMENT_TYPE = 'UNKNOWN_PAYMENT_TYPE'
    PAY_AT_VENDOR = 'PAY_AT_VENDOR'
    PREPAID = 'PREPAID'


class LimoVendorInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    code: str | None = Field(None, description='Vendor code', examples=['ZE'])
    name: str = Field(..., description='Vendor name', examples=['HERTZ'])
    email: str | None = Field(
        None, description='Vendor Email', examples=['<EMAIL>']
    )


class PnrType1(Enum):
    AIR = 'AIR'
    CAR = 'CAR'
    RAIL = 'RAIL'
    HOTEL = 'HOTEL'
    LIMO = 'LIMO'
    ALL = 'ALL'


class Location(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    countryCode: str | None = Field(
        None, description='Unique country code for a location', examples=['TR']
    )
    countryName: str | None = Field(
        None, description='Full name of the country', examples=['Turkey']
    )
    googlePlaceId: str | None = Field(
        None,
        description='Unique place ID for the location assigned by Google',
        examples=['ChIJL_P_CXMEDTkRw0ZdG-0GVvw'],
    )
    latlong: Latlng | None = None
    name: str = Field(..., description='Full name of the Location', examples=['Denver'])
    stateName: str | None = Field(
        None, description='Full name of the state', examples=['Colorado']
    )


class LocationContactInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    phone: Sequence[str] | None = None
    fax: Sequence[str] | None = None
    email: Sequence[str] | None = None


class LocationOperatingSchedule(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    interval: Sequence[DateTimeRange] = Field(
        ..., description='Operating schedule interval'
    )


class LoyaltyDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    loyaltyProgram: str | None = None
    loyaltyNumber: str | None = None


class Type6(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    CAR = 'CAR'
    RAIL = 'RAIL'


class LoyaltyInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    appliedTo: Sequence[str] | None = None
    id: str = Field(..., examples=['firstId'])
    issuedBy: str = Field(..., examples=['firstIssuedBy'])
    type: Type6 = Field(..., examples=['AIR'])


class Status2(Enum):
    UNKNOWN = 'UNKNOWN'
    CONFIRMED = 'CONFIRMED'
    PENDING = 'PENDING'
    CANCELLED = 'CANCELLED'


class ManualVerificationReason(Enum):
    FARE_THRESHOLD_VERIFIED = 'FARE_THRESHOLD_VERIFIED'
    FRAUD_CITY_VERIFIED = 'FRAUD_CITY_VERIFIED'


class MaskAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(
        None,
        description='The basic text displayed for mask amenity.',
        examples=['Face covering required'],
    )
    maskDescription: str | None = Field(
        None,
        description='A full description of the mask amenity.',
        examples=[
            'All passengers are required to wear a face covering throughout their journey'
        ],
    )
    maskAttrDescription: str | None = Field(None, examples=['yes'])


class MaskAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maskAmenity: MaskAmenity | None = None


class MealType(Enum):
    UNKNOWN_MEAL = 'UNKNOWN_MEAL'
    AVML = 'AVML'
    BBML = 'BBML'
    BLML = 'BLML'
    CHML = 'CHML'
    DBML = 'DBML'
    FPML = 'FPML'
    GFML = 'GFML'
    HFML = 'HFML'
    HNML = 'HNML'
    KSML = 'KSML'
    LCML = 'LCML'
    LFML = 'LFML'
    LPML = 'LPML'
    LSML = 'LSML'
    MOML = 'MOML'
    NLML = 'NLML'
    NSML = 'NSML'
    ORML = 'ORML'
    PFML = 'PFML'
    RVML = 'RVML'
    SFML = 'SFML'
    SPML = 'SPML'
    VGML = 'VGML'
    VJML = 'VJML'
    VLML = 'VLML'
    VOML = 'VOML'


class NameSuffix(Enum):
    NAME_SUFFIX_UNKNOWN = 'NAME_SUFFIX_UNKNOWN'
    SR = 'SR'
    JR = 'JR'
    MD = 'MD'
    PHD = 'PHD'
    II = 'II'
    III = 'III'
    IV = 'IV'
    DO = 'DO'
    ATTY = 'ATTY'
    V = 'V'
    VI = 'VI'
    ESQ = 'ESQ'
    DC = 'DC'
    DDS = 'DDS'
    VM = 'VM'
    JD = 'JD'
    SECOND = 'SECOND'
    THIRD = 'THIRD'


class Type7(Enum):
    DNI = 'DNI'
    NIE = 'NIE'


class NationalDoc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    docId: str = Field(
        ...,
        description='Unique id identifying the national document.',
        examples=['NationalDocId'],
    )
    issueCountry: str = Field(
        ...,
        description='IS0 2 letter country code of the country issuing this id.',
        examples=['IN'],
    )
    issuedDate: DateModel | None = None
    expiryDate: DateModel | None = None
    type: Type7 | None = Field(None, examples=['DNI'])


class NationalDocWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    nationalDoc: NationalDoc | None = None


class NumStopsPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numOfStops: int = Field(..., examples=[34])


class OfferExpiryInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    dateTime: DateTimeOffset


class OfficeId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(
        ...,
        description='The value of the unique ID for the office.',
        examples=['531ccbce-8413-4fe9-b233-a324dfbe7421'],
    )


class OfficeIdListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    officeIdList: Sequence[OfficeId] | None = Field(
        None, description='A list of office IDs.'
    )


class OfficeIdWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    officeId: OfficeId | None = None


class OffsetBasedPaginationRequestParams(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    offset: conint(ge=0) | None = Field(
        0,
        description='The starting index in the list from which results are returned. The value must be greater than or equal to 0.',
    )
    limit: conint(ge=1) | None = Field(
        100, description='Maximum number of results to be fetched.'
    )


class OffsetBasedPaginationResponseParams(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalNumResults: int = Field(..., description='Total number of results.')


class Option(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayCode: str = Field(
        ..., description='The code which is sent in answer response.'
    )
    displayValue: str | None = Field(
        None, description='The text to be displayed to the user beside this option.'
    )


class OptionSource(Enum):
    MANUAL = 'MANUAL'
    COMPANY_CONFIG = 'COMPANY_CONFIG'


class OrganizationAgencyId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str


class OrganizationId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str


class OtherFeeType(RootModel[Literal['MERCHANT_FEE']]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Literal['MERCHANT_FEE'] = Field(
        'MERCHANT_FEE',
        description='Other fee type',
        examples=['MERCHANT_FEE'],
        title='OtherFeeType',
    )


class OtherServiceInformationDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    customText: str | None = Field(
        None,
        description='The special instruction is given in the form of text.',
        examples=['Wheelchair access'],
    )
    flightIndexes: Sequence[FlightAndLegIndex] | None = Field(
        None,
        description='Index of flight mapping with the OSI, starts with 0. One OSI belong to one airline, so all the segments of an airline will have the same OSI.',
    )


class OutOfPolicyFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['OUT_OF_POLICY_FILTER'] = Field(
        'OUT_OF_POLICY_FILTER', examples=['OUT_OF_POLICY_FILTER']
    )
    outOfPolicy: bool = Field(
        ..., description='Out of policy indicator', examples=[True]
    )


class OverallStatus(Enum):
    PENDING_STATUS = 'PENDING_STATUS'
    CONFIRMED_STATUS = 'CONFIRMED_STATUS'
    CANCELLED_STATUS = 'CANCELLED_STATUS'
    AIRLINE_CONTROL_STATUS = 'AIRLINE_CONTROL_STATUS'
    PAYMENT_DECLINED_STATUS = 'PAYMENT_DECLINED_STATUS'
    DISRUPTED_STATUS = 'DISRUPTED_STATUS'
    DRAFT_STATUS = 'DRAFT_STATUS'


class OverallStatusFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['OVERALL_STATUS_FILTER'] = 'OVERALL_STATUS_FILTER'
    overallStatuses: Sequence[OverallStatus] = Field(
        ..., description='List of overall statuses'
    )


class OwnershipLabel(Enum):
    CORPORATE = 'CORPORATE'
    PERSONAL = 'PERSONAL'
    CENTRAL = 'CENTRAL'


class OwningPCCInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    zoneId: str | None = Field(
        None, description='Zone id of the PCC', examples=['America/Chicago']
    )


class PassengerCapacityAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['Standard passenger capacity'])
    passengerCapacityDescription: str | None = Field(
        None, examples=['Ticket sales are not limited for this flight']
    )
    passengerCapacityAttrDescription: str | None = Field(None, examples=['no'])


class PassengerCapacityAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    passengerCapacityAmenity: PassengerCapacityAmenity | None = None


class PassengerType(Enum):
    UNKNOWN_PASSENGER_TYPE = 'UNKNOWN_PASSENGER_TYPE'
    ADULT = 'ADULT'
    CHILD = 'CHILD'
    INFANT = 'INFANT'
    INFANT_ON_LAP = 'INFANT_ON_LAP'
    YOUTH = 'YOUTH'
    SENIOR = 'SENIOR'
    TEEN = 'TEEN'


class Type8(Enum):
    UNKNOWN = 'UNKNOWN'
    REGULAR = 'REGULAR'


class Passport(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    docId: str = Field(..., examples=['PassportID'])
    expiryDate: DateModel
    issueCountry: str = Field(..., examples=['IN'])
    issuedDate: DateModel | None = None
    nationalityCountry: str = Field(..., examples=['IN'])
    type: Type8 | None = Field(None, examples=['REGULAR'])


class PassportWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    passport: Passport | None = None


class PaymentType3(Enum):
    UNKNOWN = 'UNKNOWN'
    FLIGHTS = 'FLIGHTS'
    SERVICE_FEE = 'SERVICE_FEE'
    MISCELLANEOUS = 'MISCELLANEOUS'
    HOTELS = 'HOTELS'
    CARS = 'CARS'
    RAILS = 'RAILS'
    LIMO = 'LIMO'
    MISC = 'MISC'


class PaymentThirdParty(Enum):
    UNKNOWN_PARTY = 'UNKNOWN_PARTY'
    STRIPE = 'STRIPE'
    TFPAY = 'TFPAY'
    TRAINLINE = 'TRAINLINE'
    BREX = 'BREX'
    OUTSIDE_OBT = 'OUTSIDE_OBT'
    RAZORPAY = 'RAZORPAY'


class PaymentGateway(Enum):
    PAYMENT_GATEWAY_UNKNOWN = 'PAYMENT_GATEWAY_UNKNOWN'
    STRIPE = 'STRIPE'
    BREX = 'BREX'


class ApplicableToEnum(Enum):
    UNKNOWN_APPLICABLE_TO = 'UNKNOWN_APPLICABLE_TO'
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    RAIL = 'RAIL'
    CAR = 'CAR'
    SERVICE_FEE = 'SERVICE_FEE'


class PaymentMode(Enum):
    CREDIT_CARD = 'CREDIT_CARD'
    ON_ACCOUNT = 'ON_ACCOUNT'
    DIRECT = 'DIRECT'
    EXCHANGE = 'EXCHANGE'
    BREX_POINTS = 'BREX_POINTS'
    CASH = 'CASH'
    QANTAS_POINTS = 'QANTAS_POINTS'
    VENDOR_PROGRAM_PAYMENT = 'VENDOR_PROGRAM_PAYMENT'
    AMADEUS_CHECKOUT = 'AMADEUS_CHECKOUT'
    PREVIOUS_BOOKING_VALUE = 'PREVIOUS_BOOKING_VALUE'
    FLIGHT_CREDITS = 'FLIGHT_CREDITS'
    QANTAS_TRAVEL_FUND = 'QANTAS_TRAVEL_FUND'


class PaymentInstruction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID = Field(
        ...,
        description='Id corresponding to the payment instruction',
        examples=['f49d00fe-1eda-4304-ba79-a980f565281d'],
    )
    displayText: str = Field(
        ...,
        description='Display text corresponding to the payment instruction',
        examples=['Room and tax only'],
    )
    checkoutText: str = Field(
        ...,
        description='Payment instruction text to display during checkout workflow',
        examples=['Room and tax only'],
    )


class BrexBudgetMetadata1(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    budgetName: str | None = Field(
        None, description='Name of the budget', examples=['Travel budget']
    )
    paidByPersonalCard: bool | None = Field(
        None,
        description='Whether it was paid by budget card or personal card',
        examples=[False],
    )


class CustomPaymentMethodMetadata1(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    brexBudgetMetadata: BrexBudgetMetadata1 | None = Field(
        None, description='Metadata for Brex Budget'
    )


class PaymentMethod(Enum):
    PAYMENT_METHOD_UNKNOWN = 'PAYMENT_METHOD_UNKNOWN'
    CREDIT_CARD = 'CREDIT_CARD'
    BREX_POINTS = 'BREX_POINTS'
    CASH = 'CASH'
    QANTAS_POINTS = 'QANTAS_POINTS'
    VENDOR_PROGRAM_PAYMENT = 'VENDOR_PROGRAM_PAYMENT'
    DELAYED_INVOICING = 'DELAYED_INVOICING'
    FLIGHT_CREDITS = 'FLIGHT_CREDITS'
    QANTAS_TRAVEL_FUND = 'QANTAS_TRAVEL_FUND'
    CUSTOM_VIRTUAL_PAYMENT = 'CUSTOM_VIRTUAL_PAYMENT'


class PaymentSourceType(Enum):
    CARD = 'CARD'
    VIRTUAL_CARD = 'VIRTUAL_CARD'
    REWARDS_PROGRAM = 'REWARDS_PROGRAM'
    DELAYED_INVOICING = 'DELAYED_INVOICING'
    CUSTOM_PAYMENT_METHOD = 'CUSTOM_PAYMENT_METHOD'
    VENDOR_PROGRAM_PAYMENT = 'VENDOR_PROGRAM_PAYMENT'
    UNUSED_CREDIT = 'UNUSED_CREDIT'
    CASH = 'CASH'


class PercentageWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    percentage: float | None = None


class Persona(Enum):
    UNKNOWN_PERSONA = 'UNKNOWN_PERSONA'
    EMPLOYEE = 'EMPLOYEE'
    GUEST = 'GUEST'
    PERSONAL = 'PERSONAL'
    RELATIVE = 'RELATIVE'
    ADHOC = 'ADHOC'


class PersonaListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    personaList: Sequence[Persona] | None = None


class PersonaWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    persona: Persona | None = None


class CountryCodeSource(Enum):
    UNSPECIFIED = 'UNSPECIFIED'
    FROM_NUMBER_WITH_PLUS_SIGN = 'FROM_NUMBER_WITH_PLUS_SIGN'
    FROM_NUMBER_WITH_IDD = 'FROM_NUMBER_WITH_IDD'
    FROM_NUMBER_WITHOUT_PLUS_SIGN = 'FROM_NUMBER_WITHOUT_PLUS_SIGN'
    FROM_DEFAULT_COUNTRY = 'FROM_DEFAULT_COUNTRY'


class Type9(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    MOBILE = 'MOBILE'
    LANDLINE = 'LANDLINE'


class PhoneNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    countryCode: int | None = Field(
        None, description='two digit country code', examples=[91]
    )
    countryCodeSource: CountryCodeSource | None = Field(
        None, examples=['FROM_NUMBER_WITH_PLUS_SIGN']
    )
    extension: str | None = Field(
        None, description='phone number extension', examples=['222']
    )
    isoCountryCode: str | None = Field(
        None, description='ISO alpha-2 code', examples=['IN']
    )
    italianLeadingZero: bool | None = Field(False, examples=[True])
    nationalNumber: int | None = Field(None, examples=[8150])
    numberOfLeadingZeros: int | None = Field(0, examples=[1])
    preferredDomesticCarrierCode: str | None = Field(None, examples=['7'])
    rawInput: str | None = Field(None, examples=['77777'])
    type: Type9 | None = Field(None, examples=['MOBILE'])


class PnrBookingStatus(Enum):
    PENDING = 'PENDING'
    CONFIRMED = 'CONFIRMED'
    ACTIVE = 'ACTIVE'
    COMPLETED = 'COMPLETED'
    CANCELLED = 'CANCELLED'
    REFUNDED = 'REFUNDED'
    VOIDED = 'VOIDED'
    PROCESSING = 'PROCESSING'
    UNCONFIRMED = 'UNCONFIRMED'
    AIRLINE_CONTROL = 'AIRLINE_CONTROL'
    PAYMENT_DECLINED = 'PAYMENT_DECLINED'
    SCHEDULE_CHANGE = 'SCHEDULE_CHANGE'
    UNKNOWN = 'UNKNOWN'
    HOLD = 'HOLD'
    APPROVAL_REQUESTED = 'APPROVAL_REQUESTED'
    APPROVAL_DENIED = 'APPROVAL_DENIED'
    CANCELLATION_IN_PROGRESS = 'CANCELLATION_IN_PROGRESS'
    INOPERATIVE_STATUS = 'INOPERATIVE_STATUS'
    FLIGHT_UNCONFIRMED_STATUS = 'FLIGHT_UNCONFIRMED_STATUS'


class CancellationType(Enum):
    NO_REFUND = 'NO_REFUND'
    FULL_REFUND = 'FULL_REFUND'
    PARTIAL_REFUND = 'PARTIAL_REFUND'
    FULL_CREDIT = 'FULL_CREDIT'


class BookingEmailInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    disableEmail: bool | None = Field(
        None,
        description='Whether an email should be sent to travelers.',
        examples=[True],
    )


class AdditionalMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airportInfo: Sequence[AirportInfo] | None = None
    airlineInfo: Sequence[AirlineInfo] | None = None
    bta: str | None = Field(
        None,
        description='BTA (Business Travel Account) indicator for this PNR. This provides additional information and is only populated when the PNR is delay invoiced.',
        examples=['YMT'],
    )


class TravelerPnrVisibilityStatus(Enum):
    VISIBLE = 'VISIBLE'
    HIDDEN = 'HIDDEN'


class ExternalInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    externalPnrId: str | None = Field(
        None, description='External Pnr ID', examples=['A2345']
    )
    externalPnrVersion: int | None = Field(
        None, description='External Pnr Version', examples=[1]
    )
    externalUrlMetadata: str | None = Field(
        None,
        description='External url information to be attached for pnr',
        examples=['exchange=a,cancel=b'],
    )


class PnrMetadata(RootModel[FlightMetadataWrapper | LegMetadataWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: FlightMetadataWrapper | LegMetadataWrapper = Field(
        ...,
        description='Metadata when document is associated to pnr entity.',
        title='PnrMetadata',
    )


class InvoiceType(Enum):
    SERVICE_FEE_INVOICE = 'SERVICE_FEE_INVOICE'
    FARE_INVOICE = 'FARE_INVOICE'
    GENERIC_INVOICE = 'GENERIC_INVOICE'


class InvoiceMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    invoiceNumber: str = Field(..., examples=['SPOT-0001'])
    invoiceType: InvoiceType | None = Field(None, examples=['FARE_INVOICE'])


class PolicyType(Enum):
    GLOBAL = 'GLOBAL'
    DEFAULT = 'DEFAULT'
    GROUP = 'GROUP'


class PnrPolicyId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., description='Policy id')
    version: str = Field(..., description='version of the policy')
    policyName: str | None = Field(None, description='Name of the policy applied.')
    approvalType: ApprovalType | None = Field(None, examples=['SOFT_APPROVAL'])
    policyType: PolicyType | None = Field(None, examples=['GLOBAL'])


class PnrResponseOnExternalId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str = Field(
        ...,
        description='Spotnana PNR Id mapped with external Id',
        examples=['*********0'],
    )
    tripId: str = Field(
        ...,
        description='Spotnana trip Id mapped with external Id',
        examples=['*********0'],
    )


class PnrStatus(Enum):
    UNKNOWN = 'UNKNOWN'
    INITIATED = 'INITIATED'
    CANCELLED = 'CANCELLED'
    CONFIRMED = 'CONFIRMED'
    GROUP_BOOKING_ON_REQUEST = 'GROUP_BOOKING_ON_REQUEST'
    WAITLISTED = 'WAITLISTED'
    PENDING = 'PENDING'
    AIRLINE_UPGRADE = 'AIRLINE_UPGRADE'
    WAITLIST_CONFIRMED = 'WAITLIST_CONFIRMED'
    BOOKING_DENIED_CONTACT_SUPPORT = 'BOOKING_DENIED_CONTACT_SUPPORT'
    NO_SHOW = 'NO_SHOW'
    CONTACT_SUPPORT = 'CONTACT_SUPPORT'
    STATUS_CHANGED_CONTACT_SUPPORT = 'STATUS_CHANGED_CONTACT_SUPPORT'
    SCHEDULE_CHANGE = 'SCHEDULE_CHANGE'
    SEGMENT_REQUEST = 'SEGMENT_REQUEST'
    SCHEDULE_CHANGE_WAITLISTED_BOOKING = 'SCHEDULE_CHANGE_WAITLISTED_BOOKING'
    REQUEST_PENDING = 'REQUEST_PENDING'
    WAITLISTED_NOT_CONFIRMED = 'WAITLISTED_NOT_CONFIRMED'
    SCHEDULE_CHANGE_NOT_CONFIRMED = 'SCHEDULE_CHANGE_NOT_CONFIRMED'
    SCHEDULE_CHANGE_PENDING_STATUS = 'SCHEDULE_CHANGE_PENDING_STATUS'
    MIS_CONNECTION = 'MIS_CONNECTION'
    REQUESTED = 'REQUESTED'
    TICKETED = 'TICKETED'
    VOIDED = 'VOIDED'
    CANCELLED_BY_VENDOR = 'CANCELLED_BY_VENDOR'
    CANCELLATION_IN_PROGRESS = 'CANCELLATION_IN_PROGRESS'
    REINSTATED = 'REINSTATED'
    BOOKING_ON_HOLD = 'BOOKING_ON_HOLD'
    AIRLINE_CONTROL = 'AIRLINE_CONTROL'
    MODIFIED = 'MODIFIED'
    PAYMENT_DECLINED = 'PAYMENT_DECLINED'
    INOPERATIVE = 'INOPERATIVE'
    UNCONFIRMED = 'UNCONFIRMED'


class AdditionalMetadata1(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airportInfo: Sequence[AirportDetail] | None = None


class PnrType(Enum):
    AIR = 'AIR'
    CAR = 'CAR'
    RAIL = 'RAIL'
    HOTEL = 'HOTEL'
    LIMO = 'LIMO'
    MISC = 'MISC'


class PnrTypeFilterV2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['PNR_TYPE_FILTER'] = Field(
        'PNR_TYPE_FILTER', examples=['PNR_TYPE_FILTER']
    )
    pnrTypes: Sequence[PnrType] = Field(..., description='PNR type list')


class PointsBalance(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: float = Field(..., description='Points balance', examples=[10])
    pointType: str = Field(
        ...,
        description='Point type. Valid type is BREX_POINT_TYPE',
        examples=['BREX_POINT_TYPE'],
    )


class PolicyAlertOnSelectionAction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    message: str | None = None


class PolicyAlertOnSelectionActionWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    alertOnSelection: PolicyAlertOnSelectionAction | None = None


class PolicyFlagAction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    message: str | None = None


class PolicyFlagActionWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flag: PolicyFlagAction | None = None


class PolicyHideActionWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hide: bool | None = None


class PolicyPredicate(Enum):
    UNKNOWN_PREDICATE_STRING = 'UNKNOWN_PREDICATE_STRING'
    MAX_FARE_PER_TRAVELLER_VIOLATION = 'MAX_FARE_PER_TRAVELLER_VIOLATION'
    FARE_MORE_THAN_MINIMUM = 'FARE_MORE_THAN_MINIMUM'
    FARE_MORE_THAN_MEDIAN = 'FARE_MORE_THAN_MEDIAN'
    FARE_LESS_THAN_MEDIAN = 'FARE_LESS_THAN_MEDIAN'
    FARE_MORE_THAN_LLF = 'FARE_MORE_THAN_LLF'
    MAX_FARE_PER_TRAVELLER_VIOLATION_INCLUDING_TAX = (
        'MAX_FARE_PER_TRAVELLER_VIOLATION_INCLUDING_TAX'
    )
    MAX_FARE_PER_TRAVELLER_VIOLATION_EXCLUDING_TAX = (
        'MAX_FARE_PER_TRAVELLER_VIOLATION_EXCLUDING_TAX'
    )
    HOTEL_PAYMENT_OPTIONS_VIOLATION = 'HOTEL_PAYMENT_OPTIONS_VIOLATION'
    RAIL_BOOKING_WINDOW_GAP_VIOLATION = 'RAIL_BOOKING_WINDOW_GAP_VIOLATION'
    RAIL_TRAVEL_CLASS_VIOLATION = 'RAIL_TRAVEL_CLASS_VIOLATION'
    RAIL_TICKET_REFUNDABLE_VIOLATION = 'RAIL_TICKET_REFUNDABLE_VIOLATION'
    RAIL_MAX_BOOKING_PRICE_VIOLATION_INCLUDING_TAX = (
        'RAIL_MAX_BOOKING_PRICE_VIOLATION_INCLUDING_TAX'
    )
    RAIL_MAX_BOOKING_PRICE_VIOLATION_EXCLUDING_TAX = (
        'RAIL_MAX_BOOKING_PRICE_VIOLATION_EXCLUDING_TAX'
    )
    AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_INCLUDING_TAX = (
        'AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_INCLUDING_TAX'
    )
    AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_EXCLUDING_TAX = (
        'AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_EXCLUDING_TAX'
    )
    HOTEL_RESTRICTED_KEYWORDS_VIOLATION = 'HOTEL_RESTRICTED_KEYWORDS_VIOLATION'
    RESTRICTED_LOCATION_VIOLATION = 'RESTRICTED_LOCATION_VIOLATION'
    FLIGHT_ADVANCE_BOOKING_WINDOW_DOMESTIC_VIOLATION = (
        'FLIGHT_ADVANCE_BOOKING_WINDOW_DOMESTIC_VIOLATION'
    )
    FLIGHT_ADVANCE_BOOKING_WINDOW_INTERNATIONAL_VIOLATION = (
        'FLIGHT_ADVANCE_BOOKING_WINDOW_INTERNATIONAL_VIOLATION'
    )
    FLIGHT_ADVANCE_BOOKING_WINDOW_VIOLATION = 'FLIGHT_ADVANCE_BOOKING_WINDOW_VIOLATION'
    ITINERARY_WITHIN_EVENT_TRAVEL_WINDOW = 'ITINERARY_WITHIN_EVENT_TRAVEL_WINDOW'
    HOTEL_IN_ALLOWED_HOTEL_LIST = 'HOTEL_IN_ALLOWED_HOTEL_LIST'
    PAYMENT_ACCESS_VIOLATION = 'PAYMENT_ACCESS_VIOLATION'
    AIRPORT_IN_ALLOWED_AIRPORT_LIST = 'AIRPORT_IN_ALLOWED_AIRPORT_LIST'
    ITINERARY_TYPE_IS_NOT_IN_ALLOWED_BOOKING_TYPES = (
        'ITINERARY_TYPE_IS_NOT_IN_ALLOWED_BOOKING_TYPES'
    )
    PAYMENT_AIR_ADDON_VIOLATION = 'PAYMENT_AIR_ADDON_VIOLATION'
    MAX_HOTEL_BOOKING_PRICE_INCLUDING_TAX = 'MAX_HOTEL_BOOKING_PRICE_INCLUDING_TAX'
    MAX_HOTEL_BOOKING_PRICE_EXCLUDING_TAX = 'MAX_HOTEL_BOOKING_PRICE_EXCLUDING_TAX'
    AIR_NUM_TRAVELERS_ALLOWED = 'AIR_NUM_TRAVELERS_ALLOWED'
    PREFERRED_VENDOR_VIOLATION = 'PREFERRED_VENDOR_VIOLATION'
    SEAT_ADDON_VIOLATION = 'SEAT_ADDON_VIOLATION'
    BAGGAGE_ADDON_VIOLATION = 'BAGGAGE_ADDON_VIOLATION'
    EARLY_BIRD_ADDON_VIOLATION = 'EARLY_BIRD_ADDON_VIOLATION'
    WIFI_ADDON_VIOLATION = 'WIFI_ADDON_VIOLATION'
    RESTRICTED_BOOKING_VIOLATION = 'RESTRICTED_BOOKING_VIOLATION'
    HIGHEST_ALLOWED_CABIN_VIOLATION = 'HIGHEST_ALLOWED_CABIN_VIOLATION'
    LOWEST_FARE_PER_HOTEL_PROPERTY_VIOLATION = (
        'LOWEST_FARE_PER_HOTEL_PROPERTY_VIOLATION'
    )


class PolicyPreventBookingAction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    prevent: bool | None = Field(
        None,
        description='True if booking is to be blocked if rule is violated, else false',
    )
    reason: str | None = Field(
        None,
        description='Reason describing why was that specific itinerary not allowed to book.',
    )


class PolicyPreventBookingActionWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preventBooking: PolicyPreventBookingAction | None = None


class PolicyStatus(Enum):
    IN_POLICY = 'IN_POLICY'
    OUT_OF_POLICY = 'OUT_OF_POLICY'


class PolicyStatusFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['POLICY_STATUS_FILTER'] = 'POLICY_STATUS_FILTER'
    policyStatuses: Sequence[PolicyStatus]


class PostalAddress(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    addressLines: Sequence[str] = Field(..., description='Address lines')
    administrativeArea: str | None = Field(
        None,
        description='Code of administrative area. For example: DL for Delhi, India.\nHighest administrative subdivision which is used for postal\naddresses of a country or region.\nFor example, this can be a state, a province, an oblast, or a prefecture.\nSpecifically, for Spain this is the province and not the autonomous\ncommunity (e.g. "Barcelona" and not "Catalonia").\nMany countries don\'t use an administrative area in postal addresses. E.g.\nin Switzerland this should be left unpopulated.\n',
        examples=['CA'],
    )
    administrativeAreaName: str | None = Field(
        None,
        description='Name of administrative area. This is full name corresponding to administrativeArea. \nLike Delhi for DL area code. For some places, code and name maybe same as well like Tokyo.\n',
        examples=['California'],
    )
    description: str | None = Field(
        None, description='Address description', examples=['San Francisco Home']
    )
    isDefault: bool | None = Field(
        None,
        description='Whether this address is default address in case multiple addresses are specified.',
        examples=[True],
    )
    languageCode: str | None = Field(
        None,
        description='BCP-47 language code of the contents of this address (if known). This is often the UI \nlanguage of the input form or is expected to match one of the languages used in the \naddress\' country/region, or their transliterated equivalents.\nThis can affect formatting in certain countries, but is not critical to the correctness \nof the data and will never affect any validation or other non-formatting related operations.\nExamples: "zh-Hant", "ja", "ja-Latn", "en".\n',
        examples=['en'],
    )
    locality: str | None = Field(
        None,
        description='Generally refers to the city/town portion of the address.',
        examples=['San Francisco'],
    )
    locationCode: str | None = Field(
        None,
        description='IATA 3-letter location code. See https://www.iata.org/en/services/codes.',
        examples=['LAX'],
    )
    organization: str | None = Field(
        None,
        description='The name of the organization at the address.',
        examples=['Spotnana'],
    )
    postalCode: str | None = Field(
        None,
        description='Postal code of the address. This is a required field when setting for a user/legal entity/company etc.',
        examples=['94130'],
    )
    continentCode: str | None = Field(
        None,
        description='2 letter continent code of the continent this address falls in.',
        examples=['AF'],
    )
    recipients: Sequence[str] | None = Field(
        None, description='The recipient at the address.'
    )
    regionCode: str = Field(
        ...,
        description='Region code of the country/region of the address.',
        examples=['US'],
    )
    regionName: str | None = Field(
        None,
        description='Region name of the country/region of the address.',
        examples=['America'],
    )
    revision: int | None = Field(None, examples=[1])
    sortingCode: str | None = Field(
        None,
        description='Additional, country-specific, sorting code. This is not used\nin most regions. Where it is used, the value is either a string like\n"CEDEX", optionally followed by a number (e.g. "CEDEX 7"), or just a number\nalone, representing the "sector code" (Jamaica), "delivery area indicator"\n(Malawi) or "post office indicator" (e.g. Côte d\'Ivoire).\n',
        examples=['Jamaica'],
    )
    sublocality: str | None = Field(
        None,
        description='Sublocality of the address. This can be neighborhoods, boroughs, districts.',
    )
    timezone: str | None = Field(
        None, description='Time zone of the address.', examples=['America/Los_Angeles']
    )
    coordinates: Latlng | None = Field(
        None, description='Map coordinates of the address.'
    )


class PostalAddressWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    postalAddress: PostalAddress | None = None


class PowerAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['Power USB outlets'])
    powerType: str | None = Field(None, examples=['power/usb'])
    cost: str | None = Field(None, examples=['free'])
    usbPort: str | None = Field(None, examples=['yes'])
    powerOutlet: str | None = Field(None, examples=['yes'])


class PowerAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    powerAmenity: PowerAmenity | None = None


class PreCheckoutQuestionType(Enum):
    UNKNOWN_CHECKOUT_QUESTION_TYPE = 'UNKNOWN_CHECKOUT_QUESTION_TYPE'
    USER_DEFINED_QUESTION = 'USER_DEFINED_QUESTION'
    OOP_REASON_CODE = 'OOP_REASON_CODE'


class PreDefinedAnswers(Enum):
    UNKNOWN_CHECKOUT_ANSWER_TYPE = 'UNKNOWN_CHECKOUT_ANSWER_TYPE'
    OTHER = 'OTHER'
    TIMING_OR_SCHEDULING = 'TIMING_OR_SCHEDULING'
    AIRLINE_PREFERENCE = 'AIRLINE_PREFERENCE'
    AIRPORT_PREFERENCE = 'AIRPORT_PREFERENCE'
    MILEAGE_OR_PROGRAM_UPGRADE = 'MILEAGE_OR_PROGRAM_UPGRADE'
    NO_OTHER_OPTION_AVAILABLE = 'NO_OTHER_OPTION_AVAILABLE'
    FASTER_OPTION = 'FASTER_OPTION'
    NO_ANSWER = 'NO_ANSWER'
    NOT_AVAILABLE = 'NOT_AVAILABLE'


class PreSearchQuestionType(Enum):
    UNKNOWN_SEARCH_QUESTION_TYPE = 'UNKNOWN_SEARCH_QUESTION_TYPE'
    PURPOSE_OF_TRIP = 'PURPOSE_OF_TRIP'


class PreferredLocationLabel(Enum):
    HOME = 'HOME'
    WORK = 'WORK'
    OTHER = 'OTHER'


class PreferredPronoun(Enum):
    SHE_HER_HERS = 'SHE_HER_HERS'
    HE_HIM_HIS = 'HE_HIM_HIS'
    THEY_THEM_THEIRS = 'THEY_THEM_THEIRS'


class PreferredRailStation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    stationName: str | None = Field(
        None, description='Rail station name.', examples=['Chicago Union Station']
    )
    stationCode: str = Field(..., description='Rail station code.', examples=['CHI'])
    cityName: str | None = Field(
        None,
        description='Name of city where the rail station is located.',
        examples=['Chicago'],
    )
    countryCode: str | None = Field(
        None, description='Alpha-2 country code where the rail station is located.'
    )
    label: PreferredLocationLabel


class PreferredType(Enum):
    NOT_PREFERRED = 'NOT_PREFERRED'
    COMPANY_PREFERRED = 'COMPANY_PREFERRED'
    SPOTTERS_CHOICE = 'SPOTTERS_CHOICE'
    COMPANY_BLOCKED = 'COMPANY_BLOCKED'
    TMC_PREFERRED = 'TMC_PREFERRED'


class PrimaryServiceProviderTmc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tmcId: CompanyId = Field(..., description='Id of the service provider TMC.')


class ApprovalType3(Enum):
    APPROVED = 'APPROVED'
    DECLINED = 'DECLINED'


class ProcessPnrApprovalRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    approvalId: str | None = Field(
        None,
        description='Serialised Id containing approval related info',
        examples=['test-approval-id'],
    )
    approvalType: ApprovalType3 = Field(
        ..., description='Approval response APPROVED or DECLINED', examples=['APPROVED']
    )


class ProductType(Enum):
    PNR = 'PNR'
    SERVICE_FEE = 'SERVICE_FEE'


class ProgramDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    uscId: str = Field(..., description='Contract Id between vendor and the client.')
    tourCode: str = Field(
        ...,
        description='Tracking code to know which company has booked the ticket, it is added to the price quote and ticket.',
    )
    snapCode: str = Field(..., description='Discount code.')


class QcFinalizeData(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str = Field(..., examples=['011*********0'])


class QuestionFormat(Enum):
    INPUT_BOX = 'INPUT_BOX'
    RADIO_BUTTON = 'RADIO_BUTTON'
    CHECKBOX = 'CHECKBOX'
    CHECKBOX_WITH_PERCENTAGE = 'CHECKBOX_WITH_PERCENTAGE'


class QuestionType(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preSearchQuestionType: PreSearchQuestionType | None = None
    preCheckoutQuestionType: PreCheckoutQuestionType | None = None


class PaymentMode1(Enum):
    PRE_PAID = 'PRE_PAID'
    PAID_AT_STATION = 'PAID_AT_STATION'


class RailAmenityType(Enum):
    WIFI = 'WIFI'
    LUGGAGE_INCLUDED = 'LUGGAGE_INCLUDED'
    ELECTRICAL_SOCKET = 'ELECTRICAL_SOCKET'
    CAFE = 'CAFE'
    SEAT_DISPLAY = 'SEAT_DISPLAY'
    NEWSPAPER = 'NEWSPAPER'
    MEAL = 'MEAL'
    QUIET_CAR = 'QUIET_CAR'
    SEAT_SELECTION = 'SEAT_SELECTION'
    LOCAL_TRANSPORT = 'LOCAL_TRANSPORT'
    LOUNGE = 'LOUNGE'
    LEATHER_SEATS = 'LEATHER_SEATS'
    BICYCLE = 'BICYCLE'
    PET = 'PET'
    FIRST_CLASS_COMFORT = 'FIRST_CLASS_COMFORT'
    BUSINESS_PREMIER = 'BUSINESS_PREMIER'
    PERSONALIZED_SERVICE = 'PERSONALIZED_SERVICE'
    BOARDING = 'BOARDING'
    WHEELCHAIR_ACCESS = 'WHEELCHAIR_ACCESS'
    NURSERY_SPACE = 'NURSERY_SPACE'
    TABLE_SEAT = 'TABLE_SEAT'
    LARGE_SEAT = 'LARGE_SEAT'
    EXTRA_LEGROOM = 'EXTRA_LEGROOM'
    AIR_CONDITIONING = 'AIR_CONDITIONING'
    READING_LIGHT = 'READING_LIGHT'


class RailAppliedPromotion(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['GROUP_SAVE'] | None = Field(
        None, description='Type of the promotion', examples=['GROUP_SAVE']
    )
    name: str = Field(..., description='Name of the promotion', examples=['GroupSave'])
    description: str | None = Field(
        None,
        description='Description of the promotion',
        examples=['GroupSave discount allows any group of 3 - 9 passengers'],
    )
    conditions: Sequence[str] | None = Field(
        None, description='Conditions of the promotion'
    )


class RailBookingGuideline(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    arrivalBookingWindow: EventBookingWindow | None = Field(
        None, description='Booking window for rail-in-event.'
    )
    departureBookingWindow: EventBookingWindow | None = Field(
        None, description='Booking window for rail-out-event.'
    )


class RailBookingGuidelineWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    railBookingGuideLine: RailBookingGuideline | None = None


class RailCard(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cardNumber: str | None = Field(None, description='Number of card')
    expiryDate: DateModel | None = Field(
        None, description='Expiry date of the Rail Card.'
    )
    name: str = Field(
        ..., description='Name of the Rail Card.', examples=['Veterans Railcard']
    )
    spotnanaCode: str = Field(
        ...,
        description='Unique Spotnana code/identifier for Rail Card.',
        examples=['VET'],
    )
    vendor: str = Field(..., description='Vendor Name.', examples=['ATOC'])


class RailCo2EmissionDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    co2EmissionKilograms: float = Field(
        ...,
        description='CO2 emission for the outward/inward leg in kilograms for a single passenger.',
        examples=[10],
    )
    isApproximate: bool | None = Field(
        None,
        description='Indicates whether the emissions value is approximate or not.',
        examples=[True],
    )


class RailDeliveryOption(Enum):
    ELECTRONIC_TICKET = 'ELECTRONIC_TICKET'
    KIOSK = 'KIOSK'


class RailExchangeType(Enum):
    AMEND_RESERVATION = 'AMEND_RESERVATION'
    PAY_THE_DIFFERENCE = 'PAY_THE_DIFFERENCE'
    REBOOK_AND_REFUND = 'REBOOK_AND_REFUND'


class RailFareComposition(Enum):
    THROUGH = 'THROUGH'
    DIRECT_SPLIT = 'DIRECT_SPLIT'
    INTERCHANGE_SPLIT = 'INTERCHANGE_SPLIT'


class RailFareDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str = Field(
        ...,
        description='Description about this fare.',
        examples=[
            'Your journey must be on the date, or the day after the date, specified on the ticket.'
        ],
    )
    name: str = Field(..., description='Name of the Attribute.', examples=['VALIDITY'])


class RailFareType(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str = Field(
        ...,
        description='Description of this fare type.',
        examples=['Anytime Return (1st Class)'],
    )
    fareDetails: Sequence[RailFareDetail] | None = None
    fareSummary: str | None = Field(
        None,
        description='Summary of the fare',
        examples=['Any time of day, return within 1 month.'],
    )


class RailJourneySummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    origin: str = Field(..., examples=['London'])
    destination: str = Field(..., examples=['London'])
    departureDateTime: DateTimeOffset | None = None
    arrivalDateTime: DateTimeOffset | None = None
    pnrStatus: PnrStatus | None = None
    journeyStatus: PnrStatus | None = None


class RailPassengerType(Enum):
    ADULT = 'ADULT'
    CHILD = 'CHILD'
    INFANT = 'INFANT'
    YOUTH = 'YOUTH'
    SENIOR = 'SENIOR'


class RailPnrRelatedSectionInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    newSectionIndexes: Sequence[int] | None = Field(
        None, description='Section indexes of the exchanged itinerary.'
    )
    oldSectionIndexes: Sequence[int] | None = Field(
        None, description='Section indexes of the previous itinerary.'
    )


class SectionStatus(Enum):
    INITIATED = 'INITIATED'
    CONFIRMED = 'CONFIRMED'
    CANCELLED = 'CANCELLED'
    REQUESTED = 'REQUESTED'


class RailPnrTicket(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    issuedDateTime: DateTimeOffset | None = Field(
        None, description='Ticket issued date time.'
    )
    ticketNumber: str = Field(
        ..., description='Ticket number.', examples=['0840010000072']
    )
    legs: Sequence[conint(ge=0)] = Field(
        ..., description='List of legs covered by this ticket.'
    )
    passengerRefs: Sequence[conint(ge=0)] = Field(
        ..., description='List of passenger indexes for this ticket.'
    )


class RailPnrVendorConfirmation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    passengerRefs: Sequence[conint(ge=0)] | None = Field(
        None, description='List of passenger indexes.'
    )
    vendorConfirmationId: str = Field(
        ..., description='Vendor confirmation id.', examples=['XY179358']
    )


class RailRsvpResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    notNeeded: bool | None = Field(
        None,
        description='Whether rail booking is needed by the traveler or not',
        examples=[True],
    )


class RailSearchType(Enum):
    ONE_WAY = 'ONE_WAY'
    RETURN = 'RETURN'
    OPEN_RETURN = 'OPEN_RETURN'


class RailSeatPreferenceSelection(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    carriageType: str | None = Field(
        None, description='Seat Carriage Type', examples=['Quiet']
    )
    deckType: str | None = Field(
        None, description='Seat Carriage Type', examples=['Upstairs']
    )
    direction: str | None = Field(
        None, description='Seat Direction.', examples=['Backward facing']
    )
    facilities: Sequence[str] | None = Field(None, description='Seat Facilities.')
    positionType: str | None = Field(
        None, description='Seat Position Type.', examples=['Window']
    )
    seatLocationType: str | None = Field(
        None, description='Seat Location Type.', examples=['Sit close to']
    )
    seatType: str | None = Field(None, description='Seat Type.', examples=['Family'])


class StationType(Enum):
    INDIVIDUAL = 'INDIVIDUAL'
    STATION_GROUP = 'STATION_GROUP'
    METRO = 'METRO'
    BUS_STOP = 'BUS_STOP'
    FERRY_TERMINAL = 'FERRY_TERMINAL'


class RailStationSourceRefInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    stationReferenceId: str = Field(
        ...,
        description='Reference Id of the Station',
        examples=[
            'https://et2-trainlineapi.ttlnonprod.com/stations/op~U~6e6165d9-1be0-4a52-b461-57f18d756c4c'
        ],
    )
    inventoryName: str = Field(..., description='Inventory Name', examples=['Atoc'])


class RailTicketDeliveryStatus(Enum):
    FULFILLING = 'FULFILLING'
    FULFILLED = 'FULFILLED'


class RailTicketType(Enum):
    PDF = 'PDF'
    PNG = 'PNG'
    APPLE_PK_PASS = 'APPLE_PK_PASS'
    HTML = 'HTML'
    COLLECTION_NUMBER = 'COLLECTION_NUMBER'


class RailTravelClass(Enum):
    FIRST = 'FIRST'
    STANDARD = 'STANDARD'
    BUSINESS = 'BUSINESS'
    SLEEPER = 'SLEEPER'
    STANDARD_PREMIUM = 'STANDARD_PREMIUM'
    BUSINESS_PREMIUM = 'BUSINESS_PREMIUM'
    COACH = 'COACH'
    ROOM = 'ROOM'
    EXECUTIVE = 'EXECUTIVE'


class RailValidity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    validFrom: DateTimeLocal = Field(..., description='Starting time of validity.')
    validUntil: DateTimeLocal = Field(..., description='End time of validity.')


class Type10(Enum):
    TRAIN = 'TRAIN'
    BUS = 'BUS'
    FERRY = 'FERRY'
    WALK = 'WALK'
    TAXI = 'TAXI'
    TRAM = 'TRAM'
    METRO = 'METRO'
    TUBE = 'TUBE'


class RailVehicle(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    carrierName: str = Field(
        ..., description='Name of the carrier.', examples=['Deutsche Bahn']
    )
    timetableId: str = Field(..., description='Timetable ID', examples=['ICE 373'])
    transportName: str | None = Field(
        None, description='Name of the transport.', examples=['TGV']
    )
    type: Type10 = Field(..., description='Type of vehicle.', examples=['TRAIN'])


class NegotiatedRateType(Enum):
    NONE = 'NONE'
    CORPORATE_RATE = 'CORPORATE_RATE'
    AGENCY_RATE = 'AGENCY_RATE'


class RateTypeModel(Enum):
    RATE_TYPE_UNKNOWN = 'RATE_TYPE_UNKNOWN'
    PUBLISHED = 'PUBLISHED'
    TMC_NEGOTIATED = 'TMC_NEGOTIATED'
    COMPANY_NEGOTIATED = 'COMPANY_NEGOTIATED'


class RatingWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    rating: float | None = None


class RebookType(Enum):
    RATE_ASSURANCE = 'RATE_ASSURANCE'
    MODIFY = 'MODIFY'


class RedressNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    number: str = Field(..., examples=['12345'])
    issueCountry: str = Field(..., examples=['US'])


class RedressNumberWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    redress: RedressNumber | None = None


class Reference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID
    name: str | None = None


class Label(Enum):
    TRIP_ID = 'TRIP_ID'
    PNR_ID = 'PNR_ID'
    AGENCY_REFERENCE = 'AGENCY_REFERENCE'


class ReferenceId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    label: Label = Field(
        ...,
        description='Label for the reference ID on invoice.',
        examples=['AGENCY_REFERENCE'],
    )
    value: str = Field(..., description='The reference ID.', examples=['122332432'])


class RefreshType(Enum):
    REFRESH_TYPE_BLOCKING = 'REFRESH_TYPE_BLOCKING'
    REFRESH_TYPE_ASYNC = 'REFRESH_TYPE_ASYNC'
    REFRESH_TYPE_DISABLED = 'REFRESH_TYPE_DISABLED'


class RefundType(Enum):
    REFUND = 'REFUND'
    CREDIT = 'CREDIT'


class RegionCode(Enum):
    US = 'US'
    UK = 'UK'
    NL = 'NL'
    SG = 'SG'
    CA = 'CA'
    MX = 'MX'
    DO = 'DO'
    AR = 'AR'
    BR = 'BR'
    CO = 'CO'
    GT = 'GT'
    PE = 'PE'
    CR = 'CR'
    IL = 'IL'
    NOR = 'NOR'
    AE = 'AE'
    SE = 'SE'
    TR = 'TR'
    PL = 'PL'
    SA = 'SA'
    ZA = 'ZA'
    AU = 'AU'
    CN = 'CN'
    HK = 'HK'
    IN = 'IN'
    ID = 'ID'
    JP = 'JP'
    KR = 'KR'
    MY = 'MY'
    NZ = 'NZ'
    PH = 'PH'
    TH = 'TH'
    VN = 'VN'
    FR = 'FR'
    DK = 'DK'
    CZ = 'CZ'
    HU = 'HU'
    AL = 'AL'
    BY = 'BY'
    BA = 'BA'
    BG = 'BG'
    IS = 'IS'
    AD = 'AD'
    AT = 'AT'
    BE = 'BE'
    CY = 'CY'
    EE = 'EE'
    FI = 'FI'
    DE = 'DE'
    GR = 'GR'
    IE = 'IE'
    IT = 'IT'
    XK = 'XK'
    LV = 'LV'
    LT = 'LT'
    LU = 'LU'
    MC = 'MC'
    ME = 'ME'
    MT = 'MT'
    PT = 'PT'
    SM = 'SM'
    SK = 'SK'
    SI = 'SI'
    ES = 'ES'
    VA = 'VA'
    HR = 'HR'
    LI = 'LI'
    MD = 'MD'
    MK = 'MK'
    RO = 'RO'
    RU = 'RU'
    RS = 'RS'
    CH = 'CH'
    UA = 'UA'
    GB = 'GB'
    TW = 'TW'


class ResidueType(Enum):
    NO_RESIDUE = 'NO_RESIDUE'
    MCO = 'MCO'
    FORFEITED = 'FORFEITED'
    REFUNDED = 'REFUNDED'


class RestrictedKeywordsWithReason(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    keyword: str | None = Field(
        None, description='Restricted keyword', examples=['Test Keyword']
    )
    reason: str | None = Field(
        None, description='Reason for restriction', examples=['Test Reason']
    )


class RewardsProgramType(Enum):
    BREX_POINTS = 'BREX_POINTS'
    QANTAS_POINTS = 'QANTAS_POINTS'


class GuaranteeType(Enum):
    GUARANTEE = 'GUARANTEE'
    DEPOSIT = 'DEPOSIT'


class RoomAmenityType(Enum):
    UNKNOWN = 'UNKNOWN'
    ADJOINING_ROOMS = 'ADJOINING_ROOMS'
    AIR_CONDITIONING = 'AIR_CONDITIONING'
    ALARM_CLOCK = 'ALARM_CLOCK'
    ALL_NEWS_CHANNEL = 'ALL_NEWS_CHANNEL'
    AM_FM_RADIO = 'AM_FM_RADIO'
    BABY_LISTENING_DEVICE = 'BABY_LISTENING_DEVICE'
    BALCONY_LANAI_TERRACE = 'BALCONY_LANAI_TERRACE'
    BARBEQUE_GRILLS = 'BARBEQUE_GRILLS'
    BATH_TUB_WITH_SPRAY_JETS = 'BATH_TUB_WITH_SPRAY_JETS'
    BATHROBE = 'BATHROBE'
    BATHROOM_AMENITIES = 'BATHROOM_AMENITIES'
    BATHROOM_TELEPHONE = 'BATHROOM_TELEPHONE'
    BATHTUB = 'BATHTUB'
    BATHTUB_ONLY = 'BATHTUB_ONLY'
    BATHTUB_SHOWER_COMBINATION = 'BATHTUB_SHOWER_COMBINATION'
    BIDET = 'BIDET'
    BOTTLED_WATER = 'BOTTLED_WATER'
    CABLE_TELEVISION = 'CABLE_TELEVISION'
    COFFEE_TEA_MAKER = 'COFFEE_TEA_MAKER'
    COLOR_TELEVISION = 'COLOR_TELEVISION'
    COMPUTER = 'COMPUTER'
    CONNECTING_ROOMS = 'CONNECTING_ROOMS'
    CONVERTERS_VOLTAGE_ADAPTORS = 'CONVERTERS_VOLTAGE_ADAPTORS'
    COPIER = 'COPIER'
    CORDLESS_PHONE = 'CORDLESS_PHONE'
    CRIBS = 'CRIBS'
    DATA_PORT = 'DATA_PORT'
    DESK = 'DESK'
    DESK_WITH_LAMP = 'DESK_WITH_LAMP'
    DINING_GUIDE = 'DINING_GUIDE'
    DIRECT_DIAL_PHONE_NUMBER = 'DIRECT_DIAL_PHONE_NUMBER'
    DISHWASHER = 'DISHWASHER'
    DOUBLE_BEDS = 'DOUBLE_BEDS'
    DUAL_VOLTAGE_OUTLET = 'DUAL_VOLTAGE_OUTLET'
    ELECTRICAL_CURRENT_VOLTAGE = 'ELECTRICAL_CURRENT_VOLTAGE'
    ERGONOMIC_CHAIR = 'ERGONOMIC_CHAIR'
    EXTENDED_PHONE_CORD = 'EXTENDED_PHONE_CORD'
    FAX_MACHINE = 'FAX_MACHINE'
    FIRE_ALARM = 'FIRE_ALARM'
    FIRE_ALARM_WITH_LIGHT = 'FIRE_ALARM_WITH_LIGHT'
    FIREPLACE = 'FIREPLACE'
    FREE_TOLL_FREE_CALLS = 'FREE_TOLL_FREE_CALLS'
    FREE_CALLS = 'FREE_CALLS'
    FREE_CREDIT_CARD_ACCESS_CALLS = 'FREE_CREDIT_CARD_ACCESS_CALLS'
    FREE_LOCAL_CALLS = 'FREE_LOCAL_CALLS'
    FREE_MOVIES_VIDEO = 'FREE_MOVIES_VIDEO'
    FULL_KITCHEN = 'FULL_KITCHEN'
    GRAB_BARS_IN_BATHROOM = 'GRAB_BARS_IN_BATHROOM'
    GRECIAN_TUB = 'GRECIAN_TUB'
    HAIRDRYER = 'HAIRDRYER'
    HIGH_SPEED_INTERNET_CONNECTION = 'HIGH_SPEED_INTERNET_CONNECTION'
    INTERACTIVE_WEB_TV = 'INTERACTIVE_WEB_TV'
    INTERNATIONAL_DIRECT_DIALING = 'INTERNATIONAL_DIRECT_DIALING'
    INTERNET_ACCESS = 'INTERNET_ACCESS'
    IRON = 'IRON'
    IRONING_BOARD = 'IRONING_BOARD'
    WHIRPOOL = 'WHIRPOOL'
    KING_BED = 'KING_BED'
    KITCHEN = 'KITCHEN'
    KITCHEN_SUPPLIES = 'KITCHEN_SUPPLIES'
    KITCHENETTE = 'KITCHENETTE'
    KNOCK_LIGHT = 'KNOCK_LIGHT'
    LAPTOP = 'LAPTOP'
    LARGE_DESK = 'LARGE_DESK'
    LARGE_WORK_AREA = 'LARGE_WORK_AREA'
    LAUNDRY_BASKET_CLOTHES_HAMPER = 'LAUNDRY_BASKET_CLOTHES_HAMPER'
    LOFT = 'LOFT'
    MICROWAVE = 'MICROWAVE'
    MINIBAR = 'MINIBAR'
    MODEM = 'MODEM'
    MODEM_JACK = 'MODEM_JACK'
    MULTILINE_PHONE = 'MULTILINE_PHONE'
    NEWSPAPER = 'NEWSPAPER'
    NONSMOKING = 'NONSMOKING'
    NOTEPADS = 'NOTEPADS'
    OFFICE_SUPPLIES = 'OFFICE_SUPPLIES'
    OVEN = 'OVEN'
    PAY_PER_VIEW_MOVIES_ON_TV = 'PAY_PER_VIEW_MOVIES_ON_TV'
    PENS = 'PENS'
    PHONE_IN_BATHROOM = 'PHONE_IN_BATHROOM'
    PLATES_AND_BOWLS = 'PLATES_AND_BOWLS'
    POTS_AND_PANS = 'POTS_AND_PANS'
    PRAYER_MATS = 'PRAYER_MATS'
    PRINTER = 'PRINTER'
    PRIVATE_BATHROOM = 'PRIVATE_BATHROOM'
    QUEEN_BED = 'QUEEN_BED'
    RECLINER = 'RECLINER'
    REFRIGERATOR = 'REFRIGERATOR'
    REFRIGERATOR_WITH_ICE_MAKER = 'REFRIGERATOR_WITH_ICE_MAKER'
    REMOTE_CONTROL_TELEVISION = 'REMOTE_CONTROL_TELEVISION'
    ROLLAWAY_BED = 'ROLLAWAY_BED'
    SAFE = 'SAFE'
    SCANNER = 'SCANNER'
    SEPARATE_CLOSET = 'SEPARATE_CLOSET'
    SEPARATE_MODEM_LINE_AVAILABLE = 'SEPARATE_MODEM_LINE_AVAILABLE'
    SHOE_POLISHER = 'SHOE_POLISHER'
    SHOWER_ONLY = 'SHOWER_ONLY'
    SILVERWARE_UTENSILS = 'SILVERWARE_UTENSILS'
    SITTING_AREA = 'SITTING_AREA'
    SMOKE_DETECTORS = 'SMOKE_DETECTORS'
    SMOKING = 'SMOKING'
    SOFA_BED = 'SOFA_BED'
    SPEAKER_PHONE = 'SPEAKER_PHONE'
    STEREO = 'STEREO'
    STOVE = 'STOVE'
    TAPE_RECORDER = 'TAPE_RECORDER'
    TELEPHONE = 'TELEPHONE'
    TELEPHONE_FOR_HEARING_IMPAIRED = 'TELEPHONE_FOR_HEARING_IMPAIRED'
    TELEPHONES_WITH_MESSAGE_LIGHT = 'TELEPHONES_WITH_MESSAGE_LIGHT'
    TOASTER_OVEN = 'TOASTER_OVEN'
    TROUSER_PANT_PRESS = 'TROUSER_PANT_PRESS'
    TURN_DOWN_SERVICE = 'TURN_DOWN_SERVICE'
    TWIN_BED = 'TWIN_BED'
    VAULTED_CEILINGS = 'VAULTED_CEILINGS'
    VCR_MOVIES = 'VCR_MOVIES'
    VCR_PLAYER = 'VCR_PLAYER'
    VIDEO_GAMES_AMENITY = 'VIDEO_GAMES_AMENITY'
    VOICE_MAIL = 'VOICE_MAIL'
    WAKEUP_CALLS = 'WAKEUP_CALLS'
    WATER_CLOSET = 'WATER_CLOSET'
    WATER_PURIFICATION_SYSTEM = 'WATER_PURIFICATION_SYSTEM'
    WET_BAR = 'WET_BAR'
    WIRELESS_INTERNET_CONNECTION = 'WIRELESS_INTERNET_CONNECTION'
    WIRELESS_KEYBOARD = 'WIRELESS_KEYBOARD'
    ADAPTOR_AVAILABLE_FOR_TELEPHONE_PC_USE = 'ADAPTOR_AVAILABLE_FOR_TELEPHONE_PC_USE'
    AIR_CONDITIONING_INDIVIDUALLY_CONTROLLED_IN_ROOM = (
        'AIR_CONDITIONING_INDIVIDUALLY_CONTROLLED_IN_ROOM'
    )
    BATHTUB_ANDWHIRLPOOL_SEPARATE = 'BATHTUB_ANDWHIRLPOOL_SEPARATE'
    TELEPHONE_WITH_DATA_PORTS = 'TELEPHONE_WITH_DATA_PORTS'
    CD_PLAYER = 'CD_PLAYER'
    COMPLIMENTARY_LOCAL_CALLS_TIME_LIMIT = 'COMPLIMENTARY_LOCAL_CALLS_TIME_LIMIT'
    EXTRA_PERSON_CHARGE_FOR_ROLLAWAY_USE = 'EXTRA_PERSON_CHARGE_FOR_ROLLAWAY_USE'
    DOWN_FEATHER_PILLOWS = 'DOWN_FEATHER_PILLOWS'
    DESK_WITH_ELECTRICAL_OUTLET = 'DESK_WITH_ELECTRICAL_OUTLET'
    ESPN_AVAILABLE = 'ESPN_AVAILABLE'
    FOAM_PILLOWS = 'FOAM_PILLOWS'
    HBO_AVAILABLE = 'HBO_AVAILABLE'
    HIGH_CEILINGS = 'HIGH_CEILINGS'
    MARBLE_BATHROOM = 'MARBLE_BATHROOM'
    LIST_OF_MOVIE_CHANNELS_AVAILABLE = 'LIST_OF_MOVIE_CHANNELS_AVAILABLE'
    PETS_ALLOWED = 'PETS_ALLOWED'
    OVERSIZED_BATHTUB = 'OVERSIZED_BATHTUB'
    SHOWER = 'SHOWER'
    SINK_INROOM = 'SINK_INROOM'
    SOUNDPROOFED_ROOM = 'SOUNDPROOFED_ROOM'
    STORAGE_SPACE = 'STORAGE_SPACE'
    TABLES_AND_CHAIRS = 'TABLES_AND_CHAIRS'
    TWOLINE_PHONE = 'TWOLINE_PHONE'
    WALKIN_CLOSET = 'WALKIN_CLOSET'
    WASHER_DRYER = 'WASHER_DRYER'
    WEIGHT_SCALE = 'WEIGHT_SCALE'
    WELCOME_GIFT = 'WELCOME_GIFT'
    SPARE_ELECTRICAL_OUTLET_AVAILABLE_AT_DESK = (
        'SPARE_ELECTRICAL_OUTLET_AVAILABLE_AT_DESK'
    )
    NONREFUNDABLE_CHARGE_FOR_PETS = 'NONREFUNDABLE_CHARGE_FOR_PETS'
    REFUNDABLE_DEPOSIT_FOR_PETS = 'REFUNDABLE_DEPOSIT_FOR_PETS'
    SEPARATE_TUB_AND_SHOWER = 'SEPARATE_TUB_AND_SHOWER'
    ENTRANCE_TYPE_TO_GUEST_ROOM = 'ENTRANCE_TYPE_TO_GUEST_ROOM'
    CEILING_FAN = 'CEILING_FAN'
    CNN_AVAILABLE = 'CNN_AVAILABLE'
    ELECTRICAL_ADAPTORS_AVAILABLE = 'ELECTRICAL_ADAPTORS_AVAILABLE'
    BUFFET_BREAKFAST = 'BUFFET_BREAKFAST'
    ACCESSIBLE_ROOM = 'ACCESSIBLE_ROOM'
    CLOSETS_IN_ROOM = 'CLOSETS_IN_ROOM'
    DVD_PLAYER = 'DVD_PLAYER'
    MINIREFRIGERATOR = 'MINIREFRIGERATOR'
    SEPARATE_LINE_BILLING_FOR_MULTILINE_PHONE = (
        'SEPARATE_LINE_BILLING_FOR_MULTILINE_PHONE'
    )
    SELFCONTROLLED_HEATING_COOLING_SYSTEM = 'SELFCONTROLLED_HEATING_COOLING_SYSTEM'
    TOASTER = 'TOASTER'
    ANALOG_DATA_PORT = 'ANALOG_DATA_PORT'
    COLLECT_CALLS = 'COLLECT_CALLS'
    INTERNATIONAL_CALLS = 'INTERNATIONAL_CALLS'
    CARRIER_ACCESS = 'CARRIER_ACCESS'
    INTERSTATE_CALLS = 'INTERSTATE_CALLS'
    INTRASTATE_CALLS = 'INTRASTATE_CALLS'
    LOCAL_CALLS = 'LOCAL_CALLS'
    LONG_DISTANCE_CALLS = 'LONG_DISTANCE_CALLS'
    OPERATORASSISTED_CALLS = 'OPERATORASSISTED_CALLS'
    CREDIT_CARD_ACCESS_CALLS = 'CREDIT_CARD_ACCESS_CALLS'
    CALLING_CARD_CALLS = 'CALLING_CARD_CALLS'
    TOLL_FREE_CALLS = 'TOLL_FREE_CALLS'
    UNIVERSAL_AC_DC_ADAPTORS = 'UNIVERSAL_AC_DC_ADAPTORS'
    BATHTUB_SEAT = 'BATHTUB_SEAT'
    CANOPY_POSTER_BED = 'CANOPY_POSTER_BED'
    CUPS_GLASSWARE = 'CUPS_GLASSWARE'
    ENTERTAINMENT_CENTER = 'ENTERTAINMENT_CENTER'
    FAMILY_OVERSIZED_ROOM = 'FAMILY_OVERSIZED_ROOM'
    HYPOALLERGENIC_BED = 'HYPOALLERGENIC_BED'
    HYPOALLERGENIC_PILLOWS = 'HYPOALLERGENIC_PILLOWS'
    LAMP = 'LAMP'
    MEAL_INCLUDED_BREAKFAST = 'MEAL_INCLUDED_BREAKFAST'
    MEAL_INCLUDED_CONTINENTAL_BREAKFAST = 'MEAL_INCLUDED_CONTINENTAL_BREAKFAST'
    MEAL_INCLUDED_DINNER = 'MEAL_INCLUDED_DINNER'
    MEAL_INCLUDED_LUNCH = 'MEAL_INCLUDED_LUNCH'
    SHARED_BATHROOM = 'SHARED_BATHROOM'
    TELEPHONE_TDD_TEXTPHONE = 'TELEPHONE_TDD_TEXTPHONE'
    WATER_BED = 'WATER_BED'
    EXTRA_ADULT_CHARGE = 'EXTRA_ADULT_CHARGE'
    EXTRA_CHILD_CHARGE = 'EXTRA_CHILD_CHARGE'
    EXTRA_CHILD_CHARGE_FOR_ROLLAWAY_USE = 'EXTRA_CHILD_CHARGE_FOR_ROLLAWAY_USE'
    MEAL_INCLUDED_FULL_AMERICAN_BREAKFAST = 'MEAL_INCLUDED_FULL_AMERICAN_BREAKFAST'
    FUTON = 'FUTON'
    MURPHY_BED = 'MURPHY_BED'
    TATAMI_MATS = 'TATAMI_MATS'
    SINGLE_BED = 'SINGLE_BED'
    ANNEX_ROOM = 'ANNEX_ROOM'
    FREE_NEWSPAPER = 'FREE_NEWSPAPER'
    HONEYMOON_SUITES = 'HONEYMOON_SUITES'
    COMPLIMENTARY_HIGH_SPEED_INTERNET_IN_ROOM = (
        'COMPLIMENTARY_HIGH_SPEED_INTERNET_IN_ROOM'
    )
    MAID_SERVICE = 'MAID_SERVICE'
    PC_HOOKUP_IN_ROOM = 'PC_HOOKUP_IN_ROOM'
    SATELLITE_TELEVISION = 'SATELLITE_TELEVISION'
    VIP_ROOMS = 'VIP_ROOMS'
    CELL_PHONE_RECHARGER = 'CELL_PHONE_RECHARGER'
    DVR_PLAYER = 'DVR_PLAYER'
    IPOD_DOCKING_STATION = 'IPOD_DOCKING_STATION'
    MEDIA_CENTER = 'MEDIA_CENTER'
    PLUG_AND_PLAY_PANEL = 'PLUG_AND_PLAY_PANEL'
    SATELLITE_RADIO = 'SATELLITE_RADIO'
    VIDEO_ON_DEMAND = 'VIDEO_ON_DEMAND'
    EXTERIOR_CORRIDORS = 'EXTERIOR_CORRIDORS'
    GULF_VIEW = 'GULF_VIEW'
    ACCESSIBLE_ROOM_AMENITY = 'ACCESSIBLE_ROOM_AMENITY'
    INTERIOR_CORRIDORS = 'INTERIOR_CORRIDORS'
    MOUNTAIN_VIEW = 'MOUNTAIN_VIEW'
    OCEAN_VIEW = 'OCEAN_VIEW'
    HIGH_SPEED_INTERNET_ACCESS_FEE = 'HIGH_SPEED_INTERNET_ACCESS_FEE'
    HIGH_SPEED_WIRELESS = 'HIGH_SPEED_WIRELESS'
    PREMIUM_MOVIE_CHANNELS = 'PREMIUM_MOVIE_CHANNELS'
    SLIPPERS = 'SLIPPERS'
    FIRST_NIGHTERS_KIT = 'FIRST_NIGHTERS_KIT'
    CHAIR_PROVIDED_WITH_DESK = 'CHAIR_PROVIDED_WITH_DESK'
    PILLOW_TOP_MATTRESS = 'PILLOW_TOP_MATTRESS'
    FEATHER_BED = 'FEATHER_BED'
    DUVET = 'DUVET'
    LUXURY_LINEN_TYPE = 'LUXURY_LINEN_TYPE'
    INTERNATIONAL_CHANNELS = 'INTERNATIONAL_CHANNELS'
    PANTRY = 'PANTRY'
    DISHCLEANING_SUPPLIES = 'DISHCLEANING_SUPPLIES'
    DOUBLE_VANITY = 'DOUBLE_VANITY'
    LIGHTED_MAKEUP_MIRROR = 'LIGHTED_MAKEUP_MIRROR'
    UPGRADED_BATHROOM_AMENITIES = 'UPGRADED_BATHROOM_AMENITIES'
    VCR_PLAYER_AVAILABLE_AT_FRONT_DESK = 'VCR_PLAYER_AVAILABLE_AT_FRONT_DESK'
    INSTANT_HOT_WATER = 'INSTANT_HOT_WATER'
    OUTDOOR_SPACE = 'OUTDOOR_SPACE'
    HINOKI_TUB = 'HINOKI_TUB'
    PRIVATE_POOL = 'PRIVATE_POOL'
    HIGH_DEFINITION_HD_FLAT_PANEL_TELEVISION_32_INCHES_OR_GREATER = (
        'HIGH_DEFINITION_HD_FLAT_PANEL_TELEVISION_32_INCHES_OR_GREATER'
    )
    ROOM_WINDOWS_OPEN = 'ROOM_WINDOWS_OPEN'
    BEDDING_TYPE_UNKNOWN_OR_UNSPECIFIED = 'BEDDING_TYPE_UNKNOWN_OR_UNSPECIFIED'
    FULL_BED = 'FULL_BED'
    ROUND_BED = 'ROUND_BED'
    TV = 'TV'
    CHILD_ROLLAWAY = 'CHILD_ROLLAWAY'
    DVD_PLAYER_AVAILABLE_AT_FRONT_DESK = 'DVD_PLAYER_AVAILABLE_AT_FRONT_DESK'
    VIDEO_GAME_PLAYER = 'VIDEO_GAME_PLAYER'
    VIDEO_GAME_PLAYER_AVAILABLE_AT_FRONT_DESK = (
        'VIDEO_GAME_PLAYER_AVAILABLE_AT_FRONT_DESK'
    )
    DINING_ROOM_SEATS = 'DINING_ROOM_SEATS'
    FULL_SIZE_MIRROR = 'FULL_SIZE_MIRROR'
    MOBILE_CELLULAR_PHONES = 'MOBILE_CELLULAR_PHONES'
    MOVIES = 'MOVIES'
    MULTIPLE_CLOSETS = 'MULTIPLE_CLOSETS'
    PLATES_GLASSWARE = 'PLATES_GLASSWARE'
    SAFE_LARGE_ENOUGH_TO_ACCOMMODATE_A_LAPTOP = (
        'SAFE_LARGE_ENOUGH_TO_ACCOMMODATE_A_LAPTOP'
    )
    BED_LINEN_THREAD_COUNT = 'BED_LINEN_THREAD_COUNT'
    BLACKOUT_CURTAIN = 'BLACKOUT_CURTAIN'
    BLURAY_PLAYER = 'BLURAY_PLAYER'
    DEVICE_WITH_MP3 = 'DEVICE_WITH_MP3'
    NO_ADULT_CHANNELS_OR_ADULT_CHANNEL_LOCK = 'NO_ADULT_CHANNELS_OR_ADULT_CHANNEL_LOCK'
    NONALLERGENIC_ROOM = 'NONALLERGENIC_ROOM'
    PILLOW_TYPE = 'PILLOW_TYPE'
    SEATING_AREA_WITH_SOFA_CHAIR = 'SEATING_AREA_WITH_SOFA_CHAIR'
    SEPARATE_TOILET_AREA = 'SEPARATE_TOILET_AREA'
    WEB_ENABLED = 'WEB_ENABLED'
    WIDESCREEN_TV = 'WIDESCREEN_TV'
    OTHER_DATA_CONNECTION = 'OTHER_DATA_CONNECTION'
    PHONELINE_BILLED_SEPARATELY = 'PHONELINE_BILLED_SEPARATELY'
    SEPARATE_TUB_OR_SHOWER = 'SEPARATE_TUB_OR_SHOWER'
    VIDEO_GAMES = 'VIDEO_GAMES'
    ROOF_VENTILATOR = 'ROOF_VENTILATOR'
    CHILDRENS_PLAYPEN = 'CHILDRENS_PLAYPEN'
    PLUNGE_POOL = 'PLUNGE_POOL'
    DVD_MOVIES = 'DVD_MOVIES'
    AIR_FILTRATION = 'AIR_FILTRATION'


class RoomAmenitiy(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    additionalInfo: str | None = Field(
        None, description='Extra information about the room amenity'
    )
    isComplimentary: bool | None = Field(
        None, description='Is amenity complimentary', examples=[True]
    )
    roomAmenityType: RoomAmenityType = Field(
        ..., description='Room amenity type', examples=['WEB_ENABLED']
    )


class RoomClass(Enum):
    STANDARD = 'STANDARD'
    PREMIUM = 'PREMIUM'
    DELUXE = 'DELUXE'
    BUSINESS = 'BUSINESS'
    PRESIDENTIAL = 'PRESIDENTIAL'
    SUPERIOR = 'SUPERIOR'
    JUNIOR = 'JUNIOR'
    CLUB = 'CLUB'
    UNKNOWN = 'UNKNOWN'


class BedCount(Enum):
    ONE_BED = 'ONE_BED'
    TWO_BEDS = 'TWO_BEDS'


class RoomType1(Enum):
    SMOKING = 'SMOKING'
    NON_SMOKING = 'NON_SMOKING'


class MostImportantFact(Enum):
    ROOM_TYPE = 'ROOM_TYPE'
    BED_COUNT = 'BED_COUNT'
    ROOM_LOCATION = 'ROOM_LOCATION'


class RoomLocation1(Enum):
    HIGH_FLOOR = 'HIGH_FLOOR'
    LOW_FLOOR = 'LOW_FLOOR'
    NEAR_ELEVATOR = 'NEAR_ELEVATOR'


class PillowType(Enum):
    FOAM = 'FOAM'
    EXTRA_FOAM = 'EXTRA_FOAM'
    EXTRA_FEATHER = 'EXTRA_FEATHER'


class RoomAmenityPref(Enum):
    FEATHER_FREE_ROOM = 'FEATHER_FREE_ROOM'
    EXTRA_TOWELS = 'EXTRA_TOWELS'
    REFRIGERATOR = 'REFRIGERATOR'


class RoomPreference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    isMobilityAccessible: bool | None = Field(
        False,
        description='Whether or not mobility accessible room, tub.',
        examples=[False],
    )
    bedCount: BedCount | None = Field(
        None, description='The number of bed in the room.', examples=['ONE_BED']
    )
    roomType: RoomType1 | None = Field(
        None, description='Single selection of type of room.', examples=['SMOKING']
    )
    mostImportantFact: MostImportantFact | None = Field(
        None,
        description='Single selection of the most import fact.',
        examples=['BED_COUNT'],
    )
    roomLocation: RoomLocation1 | None = Field(
        None, description='Location of the hotel room', examples=['HIGH_FLOOR']
    )
    pillowType: PillowType | None = Field(
        None, description='The type of pillow in hotel room.', examples=['FOAM']
    )
    roomAmenityPrefs: Sequence[RoomAmenityPref] | None = None


class RoomType(Enum):
    ROOM = 'ROOM'
    SUITE = 'SUITE'
    VILLA = 'VILLA'
    APARTMENT = 'APARTMENT'
    COTTAGE = 'COTTAGE'
    STUDIO = 'STUDIO'
    UNKNOWN_ROOM = 'UNKNOWN_ROOM'


class RoomView(Enum):
    UNKNOWN = 'UNKNOWN'
    AIRPORT = 'AIRPORT'
    BAY = 'BAY'
    CITY = 'CITY'
    COURTYARD = 'COURTYARD'
    GOLF = 'GOLF'
    HARBOR = 'HARBOR'
    INTERCOASTAL = 'INTERCOASTAL'
    LAKE = 'LAKE'
    MARINA = 'MARINA'
    MOUNTAIN = 'MOUNTAIN'
    OCEAN = 'OCEAN'
    POOL = 'POOL'
    RIVER = 'RIVER'
    WATER = 'WATER'
    BEACH = 'BEACH'
    GARDEN = 'GARDEN'
    PARK = 'PARK'
    FOREST = 'FOREST'
    RAIN_FOREST = 'RAIN_FOREST'
    VARIOUS = 'VARIOUS'
    LIMITED = 'LIMITED'
    SLOPE = 'SLOPE'
    STRIP = 'STRIP'
    COUNTRYSIDE = 'COUNTRYSIDE'
    SEA = 'SEA'
    GULF = 'GULF'


class RuleBasedInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ruleName: str = Field(..., description='Rule name', examples=['Rule1'])
    chargeProcessorType: Literal['RULE_BASED_INFO'] = Field(
        'RULE_BASED_INFO',
        description='Charge processor type',
        examples=['RULE_BASED_INFO'],
    )


class SabrePnrRemarkType(Enum):
    ALPHA_CODED = 'ALPHA_CODED'
    CLIENT_ADDRESS = 'CLIENT_ADDRESS'
    CORPORATE = 'CORPORATE'
    DELIVERY_ADDRESS = 'DELIVERY_ADDRESS'
    GENERAL = 'GENERAL'
    GROUP_NAME = 'GROUP_NAME'
    HIDDEN = 'HIDDEN'
    HISTORICAL = 'HISTORICAL'
    INVOICE = 'INVOICE'
    ITINERARY = 'ITINERARY'
    OSI = 'OSI'


class ScheduleChangePossibleAction(Enum):
    ACCEPT_BOOKING = 'ACCEPT_BOOKING'
    CANCEL_BOOKING = 'CANCEL_BOOKING'
    MODIFY_BOOKING = 'MODIFY_BOOKING'
    CONTACT_AGENT = 'CONTACT_AGENT'


class ScheduleChangeRefundInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    isRefundAllowed: bool | None = Field(
        None,
        description='Whether refund is allowed if user denies schedule change/cancels the flight',
    )
    refundWaiverCode: str | None = Field(
        None, description='Waiver code for refund if applicable'
    )
    exchangeWaiverCode: str | None = Field(
        None, description='Waiver code for exchange if available'
    )


class SeatAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['Full flat all aisle access'])
    seatType: str | None = Field(None, examples=['full flat pod'])
    width: str | None = None
    legroom: str | None = None
    pitch: float | None = Field(None, examples=[78])


class SeatAmenityType(Enum):
    UNKNOWN_AIR_SEAT_AMENITY_TYPE = 'UNKNOWN_AIR_SEAT_AMENITY_TYPE'
    FLAT_BED = 'FLAT_BED'
    WIFI = 'WIFI'
    IN_SEAT_POWER = 'IN_SEAT_POWER'


class SeatAmenityPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    seatAmenityTypes: Sequence[SeatAmenityType]


class SeatAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    seatAmenity: SeatAmenity | None = None


class Position(Enum):
    UNKNOWN_POSITION = 'UNKNOWN_POSITION'
    AISLE = 'AISLE'
    WINDOW = 'WINDOW'
    AISLE_OR_WINDOW = 'AISLE_OR_WINDOW'


class SeatLocationPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cabins: Sequence[Cabin] | None = None
    isBulkHeadPref: bool | None = Field(None, examples=[False])
    maxFlightDurationInHours: int | None = Field(None, examples=[3])
    position: Position | None = Field(None, examples=['WINDOW'])


class SeatPrefDirection(Enum):
    FORWARD = 'FORWARD'
    BACKWARD = 'BACKWARD'


class SeatPrefLocation(Enum):
    AISLE = 'AISLE'
    WINDOW = 'WINDOW'
    SOLO = 'SOLO'


class SeatPrefType(Enum):
    SLEEPER_BED = 'SLEEPER_BED'
    NORMAL = 'NORMAL'
    TABLE_SEAT = 'TABLE_SEAT'


class SellerInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(
        ...,
        description='Name of seller, usually a TMC Legal Entity.',
        examples=['Spotnana India Pvt. Ltd.'],
    )
    address: str = Field(
        ...,
        description='Address of seller.',
        examples=['115 Broadway Suite 04-101,\nNew York, NY 10006'],
    )
    taxId: str | None = Field(
        None,
        description='Tax number for the seller, for ex GSTN in India',
        examples=['TXG239023092'],
    )
    idInfo: Sequence[IdInfo] | None = Field(
        None, description='The seller ID information.'
    )


class Status3(Enum):
    ISSUED = 'ISSUED'
    VOIDED = 'VOIDED'
    PENDING = 'PENDING'
    DELAYED_INVOICE = 'DELAYED_INVOICE'
    FAILED = 'FAILED'
    NO_CHARGE = 'NO_CHARGE'
    UNKNOWN = 'UNKNOWN'


class ServiceFeeTransactionType(Enum):
    TRANSACTION_TYPE_BOOKING = 'TRANSACTION_TYPE_BOOKING'
    TRANSACTION_TYPE_MODIFICATION = 'TRANSACTION_TYPE_MODIFICATION'
    TRANSACTION_TYPE_CANCELLATION = 'TRANSACTION_TYPE_CANCELLATION'


class ShellPnrInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    shellPnrCreateReasonType: str | None = Field(
        None,
        description='Reason for creating the shell pnr',
        examples=['CONTENT_NOT_AVAILABLE'],
    )
    createShellPnrReason: str | None = Field(
        None,
        description='If the reason for creating the shell pnr is chosen as OTHER or CONTENT_NOT_AVAILABLE, \nwe expect the agent to explain the reason using this free text\n',
        examples=['Flight Missing'],
    )
    pnrType: str | None = Field(
        None,
        description='It will contain the PNR type for different shell PNRs',
        examples=['AIR'],
    )


class SimpleAirportInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airportCode: str = Field(..., description='The airport code.', examples=['WRA'])
    airportName: str = Field(
        ..., description='The airport name.', examples=['Warder Airport']
    )
    cityName: str = Field(..., description='The city name.', examples=['Werder'])


class SimpleCarPnrInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorConfirmationId: str = Field(
        ..., description='The confirmation ID in the vendor system.'
    )
    vendorName: str = Field(..., description='The vendor name.')
    address: PostalAddress = Field(..., description='The address of the car rental.')
    pickUpDateTime: DateTimeLocal = Field(..., description='The pick-up date and time.')
    dropOffDateTime: DateTimeLocal = Field(
        ..., description='The drop-off date and time.'
    )
    sortingPriority: int | None = Field(
        None,
        description='This field sets the sorting priority of the car to determine its order of display on \nthe trips page.\n',
        examples=[0],
    )


class SimpleFlightInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorConfirmationId: str = Field(
        ..., description='The confirmation ID in the vendor system.'
    )
    flightNumber: str = Field(..., description='The flight number.')
    airlineCode: str = Field(..., description='The operating airline code.')
    departureAirportInfo: SimpleAirportInfo = Field(
        ..., description='The departure airport code.'
    )
    arrivalAirportInfo: SimpleAirportInfo = Field(
        ..., description='The arrival airport code.'
    )
    departureDatetime: DateTimeLocal | None = Field(
        None, description='The departure date and time for the flight.'
    )
    arrivalDateTime: DateTimeLocal | None = Field(
        None, description='The arrival date and time for the flight.'
    )


class SimpleHotelPnrInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorConfirmationId: str = Field(
        ..., description='The confirmation ID in the vendor system.'
    )
    hotelName: str = Field(..., description='The hotel name.')
    address: PostalAddress = Field(..., description='The address for the hotel.')
    checkInDateTime: DateTimeLocal = Field(
        ..., description='The check-in date and time for the hotel.'
    )
    checkOutDatetime: DateTimeLocal | None = Field(
        None, description='The check-out date and time for the hotel.'
    )
    sortingPriority: int | None = Field(
        None,
        description='This field sets the sorting priority of the hotel to determine its order of display\non the trips page.\n',
        examples=[0],
    )


class SimpleLegInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flights: Sequence[SimpleFlightInfo]
    sortingPriority: int | None = Field(
        None,
        description='This field sets the sorting priority of the simple leg to determine its order of display\non the trips page.\n',
        examples=[0],
    )


class SimpleLimoPnrInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorConfirmationId: str = Field(
        ..., description='The confirmation ID in the vendor system.'
    )
    vendorName: str = Field(..., description='The vendor name.')
    pickupAddress: PostalAddress = Field(
        ..., description='The pickup address for the limo.'
    )
    dropOffAddress: PostalAddress | None = Field(
        None, description='The drop-off address for the limo.'
    )
    pickUpDateTime: DateTimeLocal = Field(..., description='The pick-up date and time.')
    dropOffDateTime: DateTimeLocal = Field(
        ..., description='The drop-off date and time.'
    )
    sortingPriority: int | None = Field(
        None,
        description='This field sets the sorting priority of the limo to determine its order of display on \nthe trips page.\n',
        examples=[0],
    )


class SimpleMoney(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: float = Field(..., description='Amount', examples=[510])
    currencyCode: str = Field(
        ...,
        description='The 3-letter currency code defined in ISO 4217.',
        examples=['GBP'],
    )


class SimpleRailStationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Name of the station.')
    code: str = Field(..., description='Unique code of the station.')


class Unit1(Enum):
    CENTIMETER = 'CENTIMETER'
    METER = 'METER'


class SizeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    length: float | None = Field(
        None, description='The weight limit of a baggage option.', examples=[32]
    )
    unit: Unit1 | None = Field(
        None, description='The unit of measurement for the bag size.'
    )


class SortOrder(Enum):
    DESC = 'DESC'
    ASC = 'ASC'


class SourceInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    sourcePnrId: str | None = Field(
        None, description='The source PNR ID', examples=['AEDAVF']
    )
    bookingSource: str = Field(
        ..., description='The source of booking', examples=['United.com']
    )
    thirdParty: str | None = Field(
        None,
        description='The known third party source of the booking.',
        examples=['United.com'],
    )
    bookingDateTime: DateTimeOffset | None = Field(
        None, description='Booking date time of the PNR'
    )
    posDescriptor: str | None = Field(
        None,
        description='Point of sale descriptor for this pnr from source',
        examples=['LA5K'],
    )
    iataNumber: str | None = Field(
        None, description='IATA number associated with this pnr', examples=['426666']
    )


class Status4(Enum):
    SPECIAL_SERVICE_REQUEST_STATUS_UNKNOWN = 'SPECIAL_SERVICE_REQUEST_STATUS_UNKNOWN'
    CONFIRMED = 'CONFIRMED'
    REQUESTED = 'REQUESTED'
    UNABLE_TO_CONFIRM = 'UNABLE_TO_CONFIRM'
    DENIED = 'DENIED'


class SpecialServiceRequestInfoDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    code: str | None = Field(None, description='4 letter Special Service Request code.')
    customText: str | None = Field(
        None, description='Free text string for special request.'
    )
    flightIndex: int | None = Field(
        None,
        description='Index of flight mapping with the special service request, starts with 0.',
    )
    legIndex: int | None = Field(
        None,
        description='Index of leg mapping with the special service request, starts with 0.',
    )
    status: Status4 | None = Field(
        None, description='Status of the Special Service Request.'
    )


class SplitOptionByCardCompany(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cardCompanyCode: str = Field(
        ...,
        description='Card company code which allows splitting the payment',
        examples=['VI, TP'],
    )
    splitWithCardCompanyCodes: Sequence[str] | None = None


class StatisticType(Enum):
    MINIMUM = 'MINIMUM'
    MEDIAN = 'MEDIAN'
    MAXIMUM = 'MAXIMUM'


class StringListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    sList: Sequence[str] | None = None


class StringWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    s: str | None = None


class StripeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    accountId: str | None = Field(
        None, description='Stripe account id', examples=['acct_1OAbc3AB5abcdDdA']
    )
    accountDisplayName: str | None = Field(
        None,
        description='Stripe account display name as seen on the stripe dashboard',
        examples=['Abc Travel'],
    )
    platformAccountGatewayId: str | None = Field(
        None,
        description='Gateway-id of platform account when this gateway is a connected account',
        examples=['connect-platform:#'],
    )


class StripeInfoWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    stripeInfo: StripeInfo


class SupplierType(Enum):
    SABRE = 'SABRE'
    AMADEUS = 'AMADEUS'
    TRAVEL_FUSION = 'TRAVEL_FUSION'
    FARELOGIX_NDC = 'FARELOGIX_NDC'
    ATPCO_NDC = 'ATPCO_NDC'
    TRAINLINE = 'TRAINLINE'
    AVIA = 'AVIA'
    QBR = 'QBR'
    BCD = 'BCD'
    QANTAS_HOTELS = 'QANTAS_HOTELS'
    SOUTHWEST = 'SOUTHWEST'
    EXPEDIA = 'EXPEDIA'
    HOTEL_HUB = 'HOTEL_HUB'
    NDC = 'NDC'
    MARRIOTT = 'MARRIOTT'
    CLEARTRIP = 'CLEARTRIP'
    KYTE = 'KYTE'
    GROUNDSPAN = 'GROUNDSPAN'
    SABRE_NDC = 'SABRE_NDC'
    BOOKING_COM = 'BOOKING_COM'


class SuspendReason(Enum):
    AIR_PRICE_OPTIMIZATION = 'AIR_PRICE_OPTIMIZATION'
    EXCHANGE = 'EXCHANGE'
    GDS_EXCHANGE = 'GDS_EXCHANGE'


class SuspendRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    reason: SuspendReason


class TemperatureAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['No pre-flight temperature check'])
    temperatureDescription: str | None = Field(
        None, examples=['A temperature check is not required before boarding']
    )
    temperatureAttrDescription: str | None = Field(None, examples=['no'])


class TemperatureAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    temperatureAmenity: TemperatureAmenity | None = None


class TermsAndConditions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    conditions: Sequence[Condition] | None = Field(
        None, description='List of conditions'
    )


class HotelCodeType(Enum):
    SABRE_CSL = 'SABRE_CSL'
    SABRE_TN = 'SABRE_TN'
    EXPEDIA_RAPID = 'EXPEDIA_RAPID'
    MARRIOTT = 'MARRIOTT'
    GIATA = 'GIATA'


class ThirdPartyHotelCode(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelCode: str = Field(..., description='Third party hotel code.')
    hotelCodeType: HotelCodeType = Field(..., description='Type of the third party.')


class ThirdPartySource(Enum):
    UNKNOWN_SOURCE = 'UNKNOWN_SOURCE'
    SABRE = 'SABRE'
    TRAVEL_FUSION = 'TRAVEL_FUSION'
    AVIA = 'AVIA'
    NDC = 'NDC'
    TRAINLINE = 'TRAINLINE'
    ATPCO_NDC = 'ATPCO_NDC'
    FARELOGIX_NDC = 'FARELOGIX_NDC'
    OFFLINE = 'OFFLINE'
    CONNEXUS = 'CONNEXUS'
    ROUTEHAPPY = 'ROUTEHAPPY'
    AMADEUS = 'AMADEUS'
    GIATA = 'GIATA'
    QBR = 'QBR'
    BCD = 'BCD'
    QANTAS_HOTELS = 'QANTAS_HOTELS'
    SOUTHWEST = 'SOUTHWEST'
    EXPEDIA = 'EXPEDIA'
    HOTEL_HUB = 'HOTEL_HUB'
    MARRIOTT = 'MARRIOTT'
    CLEARTRIP = 'CLEARTRIP'
    KYTE = 'KYTE'
    GROUNDSPAN = 'GROUNDSPAN'
    SABRE_NDC = 'SABRE_NDC'
    BOOKING_COM = 'BOOKING_COM'


class TicketType1(Enum):
    UNKNOWN_TICKET_TYPE = 'UNKNOWN_TICKET_TYPE'
    FLIGHT = 'FLIGHT'
    ANCILLARY = 'ANCILLARY'
    MIXED = 'MIXED'


class Status5(Enum):
    UNKNOWN = 'UNKNOWN'
    ISSUED = 'ISSUED'
    VOIDED = 'VOIDED'
    REFUNDED = 'REFUNDED'
    EXCHANGED = 'EXCHANGED'


class Status6(Enum):
    UNKNOWN = 'UNKNOWN'
    AIRPORT_CONTROL = 'AIRPORT_CONTROL'
    LIFTED = 'LIFTED'
    CHECKED_IN = 'CHECKED_IN'
    EXCHANGED = 'EXCHANGED'
    FLOWN = 'FLOWN'
    NOT_FLOWN = 'NOT_FLOWN'
    REFUNDED = 'REFUNDED'
    VOIDED = 'VOIDED'
    PRINTED = 'PRINTED'
    IRREGULAR_OPERATIONS = 'IRREGULAR_OPERATIONS'
    PRINT_EXCHANGE = 'PRINT_EXCHANGE'
    PAPER_TICKET = 'PAPER_TICKET'
    SUSPENDED = 'SUSPENDED'


class FlightCoupon(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legIdx: conint(ge=0) | None = Field(
        None, description='Index of leg to which this coupon belongs', examples=[0]
    )
    flightIdx: conint(ge=0) | None = Field(
        None,
        description="Index of flight in it's leg to which this coupon belongs",
        examples=[0],
    )
    status: Status6 | None = Field(
        None, description='Flight status', examples=['FLOWN']
    )


class AncillaryType1(Enum):
    UNKNOWN = 'UNKNOWN'
    SEAT = 'SEAT'
    LUGGAGE = 'LUGGAGE'
    EARLY_BIRD = 'EARLY_BIRD'
    WIFI = 'WIFI'
    CARBON_OFFSET = 'CARBON_OFFSET'


class TicketSettlement(Enum):
    UNKNOWN_TICKET_SETTLEMENT = 'UNKNOWN_TICKET_SETTLEMENT'
    ARC_TICKET = 'ARC_TICKET'
    BSP_TICKET = 'BSP_TICKET'
    NON_ARC_BSP_TICKET = 'NON_ARC_BSP_TICKET'


class EmdInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    emdType: str | None = Field(
        None, description='Type of EMD', examples=['STAND-ALONE']
    )


class TicketType2(Enum):
    FLIGHT = 'FLIGHT'
    ANCILLARY = 'ANCILLARY'


class TicketIncompleteReason(Enum):
    UNKNOWN_REASON = 'UNKNOWN_REASON'
    SYS_TICKET = 'SYS_TICKET'
    MISSING_MARKUP_INFO = 'MISSING_MARKUP_INFO'
    DISABLED_PCC = 'DISABLED_PCC'


class TicketScheduleChangeInformation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    possibleActions: Sequence[ScheduleChangePossibleAction] | None = None
    refundInfo: ScheduleChangeRefundInfo | None = None


class TicketStatus(Enum):
    UNKNOWN = 'UNKNOWN'
    ISSUED = 'ISSUED'
    VOIDED = 'VOIDED'
    REFUNDED_EXCHANGED = 'REFUNDED_EXCHANGED'
    EXCHANGED = 'EXCHANGED'


class Tier(Enum):
    BASIC = 'BASIC'
    SEAT1A = 'SEAT1A'


class TimeLocal(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(pattern=r'^([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$') = (
        Field(..., examples=['17:32'])
    )


class TmcCalculatorInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    calculatorType: str = Field(
        ..., description='TMC service charge calculator type', examples=['SPOTNANA']
    )
    chargeProcessorType: Literal['TMC_CALCULATOR_INFO'] = Field(
        'TMC_CALCULATOR_INFO',
        description='Charge processor type',
        examples=['TMC_CALCULATOR_INFO'],
    )


class GatewayType(Enum):
    STRIPE = 'STRIPE'
    BREX = 'BREX'
    RAZORPAY = 'RAZORPAY'


class TmcGatewayInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tmcId: CompanyId | None = None
    gatewayType: GatewayType | None = Field(
        None,
        description='Third party payment gateway used to process the payment',
        examples=['STRIPE'],
    )
    gatewayId: str | None = Field(
        None, description='GatewayId for related gatewayInfo.', examples=['demo-us:1']
    )


class TokenizedExpiry(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiryMonth: str = Field(
        ..., description='Tokenized Expiry month', examples=['KvAuPANQWCpjwRQxcC8EXg==']
    )
    expiryYear: str = Field(
        ..., description='Tokenized Expiry year', examples=['fPBm0OWrKwPyIrCVcbg4cA==']
    )


class TokenizedExpiryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tokenizedExpiry: TokenizedExpiry | None = None


class TransactionAncillaryType(Enum):
    SEAT = 'SEAT'
    LUGGAGE = 'LUGGAGE'
    EARLY_BIRD = 'EARLY_BIRD'
    WIFI = 'WIFI'
    CARBON_OFFSET = 'CARBON_OFFSET'


class TransactionFareType(Enum):
    BASE_AMOUNT = 'BASE_AMOUNT'
    OTHER_FEE_AND_TAXES = 'OTHER_FEE_AND_TAXES'
    PENALTY = 'PENALTY'
    OB_FEES = 'OB_FEES'
    DESTINATION_FEES = 'DESTINATION_FEES'
    TOTAL = 'TOTAL'


class TransactionStatus(Enum):
    ISSUED = 'ISSUED'
    DELAYED_INVOICE = 'DELAYED_INVOICE'
    FAILED = 'FAILED'


class TransactionType(Enum):
    TRIP_SERVICE_FEE = 'TRIP_SERVICE_FEE'
    AIR_TICKET_ISSUED = 'AIR_TICKET_ISSUED'
    AIR_TICKET_VOIDED = 'AIR_TICKET_VOIDED'
    AIR_TICKET_REFUNDED = 'AIR_TICKET_REFUNDED'
    AIR_TICKET_EXCHANGED = 'AIR_TICKET_EXCHANGED'
    AIR_TICKET_UNDETERMINED = 'AIR_TICKET_UNDETERMINED'
    HOTEL_BOOKED = 'HOTEL_BOOKED'
    HOTEL_MODIFIED = 'HOTEL_MODIFIED'
    HOTEL_CANCELLED = 'HOTEL_CANCELLED'
    CAR_BOOKED = 'CAR_BOOKED'
    CAR_CANCELLED = 'CAR_CANCELLED'
    RAIL_BOOKED = 'RAIL_BOOKED'
    RAIL_REFUNDED = 'RAIL_REFUNDED'
    RAIL_EXCHANGED = 'RAIL_EXCHANGED'
    RAIL_CANCELLED = 'RAIL_CANCELLED'


class Transmission(Enum):
    UNKNOWN_TRANSMISSION = 'UNKNOWN_TRANSMISSION'
    MANUAL_UNSPECIFIED_DRIVE = 'MANUAL_UNSPECIFIED_DRIVE'
    MANUAL_4WD = 'MANUAL_4WD'
    MANUAL_AWD = 'MANUAL_AWD'
    AUTO_UNSPECIFIED_DRIVE = 'AUTO_UNSPECIFIED_DRIVE'
    AUTO_4WD = 'AUTO_4WD'
    AUTO_AWD = 'AUTO_AWD'


class TransmissionSearchFilter(Enum):
    MANUAL = 'MANUAL'
    AUTOMATIC = 'AUTOMATIC'


class TravelClassHierarchy(Enum):
    UNKNOWN = 'UNKNOWN'
    STANDARD = 'STANDARD'
    COACH = 'COACH'
    STANDARD_PREMIUM = 'STANDARD_PREMIUM'
    BUSINESS = 'BUSINESS'
    BUSINESS_PREMIUM = 'BUSINESS_PREMIUM'
    FIRST = 'FIRST'
    SLEEPER = 'SLEEPER'


class TravelClassHierarchyWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    railTravelClass: TravelClassHierarchy | None = None


class TravelRegionType(Enum):
    DOMESTIC = 'DOMESTIC'
    INTERNATIONAL = 'INTERNATIONAL'


class TravelType(Enum):
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    CAR = 'CAR'
    RAIL = 'RAIL'
    LIMO = 'LIMO'
    MISC = 'MISC'
    ALL = 'ALL'


class TravelerCompanyInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CompanyId | None = None
    name: str | None = Field(
        None, description='Name of the company', examples=['Spotnana']
    )
    externalId: str | None = Field(
        None, description='External Id of the company', examples=['abcd']
    )


class TravelerLegalEntity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str | None = Field(None, description='Legal entity ID')
    name: str | None = Field(
        None, description='Name of the legal entity', examples=['Spotnana India']
    )
    ein: str | None = Field(None, description='EIN of the traveler')
    externalId: str | None = Field(
        None, description='External Id of the legal entity', examples=['abcd']
    )
    companySpecifiedAttributes: (
        Sequence[CompanySpecifiedAttributeLegalEntity] | None
    ) = None


class TripAdditionalInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vpayBillBack: str | None = Field(
        None,
        description='Vpay bill back indicator. Can be one of N(none), C(only car), H(only hotel), B(both) depending on whether there is an active hotel or car pnr using virtual card.',
        examples=['B'],
    )


class TripDateTimeRangeFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal[
        'CREATE_DATE_TIME_RANGE_FILTER', 'UPDATE_DATE_TIME_RANGE_FILTER'
    ]
    startDateTime: DateTimeOffset | None = Field(
        None, description='Start date and time for the date time range filter'
    )
    endDateTime: DateTimeOffset | None = Field(
        None, description='End date and time for the date time range filter'
    )


class TripEndDateRangeFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['TRIP_END_DATE_RANGE_FILTER'] = 'TRIP_END_DATE_RANGE_FILTER'
    startDate: DateModel | None = Field(
        None, description='Start date for the date range filter'
    )
    endDate: DateModel | None = Field(
        None, description='End date for the date range filter'
    )


class TripEventMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventId: str = Field(..., description='The event id.', examples=['*********'])
    rootEventId: str | None = Field(
        None, description='The root event id.', examples=['*********']
    )
    eventType: EventType | None = None
    associatedEventIds: Sequence[str] | None = Field(
        None, description='The associated event ids.'
    )


class TripIdFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['TRIP_ID_FILTER'] = 'TRIP_ID_FILTER'
    tripIds: Sequence[str] = Field(..., description='List of trip ids')


class TripPartnerInfoType(RootModel[Literal['BUDGET']]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Literal['BUDGET'] = Field('BUDGET', description='Trip Partner Info type.')


class SortField(Enum):
    START_DATE = 'START_DATE'
    END_DATE = 'END_DATE'


class TripSortOptions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    sortField: SortField = Field(..., description='Field to sort by')
    sortOrder: SortOrder


class TripStartDateRangeFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['TRIP_START_DATE_RANGE_FILTER'] = 'TRIP_START_DATE_RANGE_FILTER'
    startDate: DateModel | None = Field(
        None, description='Start date for the date range filter'
    )
    endDate: DateModel | None = Field(
        None, description='End date for the date range filter'
    )


class TripTravelTypeFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['TRIP_TRAVEL_TYPE_FILTER'] = 'TRIP_TRAVEL_TYPE_FILTER'
    travelTypes: Sequence[TravelType]


class TripType(Enum):
    UPCOMING = 'UPCOMING'
    PAST_OR_COMPLETED = 'PAST_OR_COMPLETED'
    CANCELLED = 'CANCELLED'
    DISRUPTED = 'DISRUPTED'
    DRAFT = 'DRAFT'


class TripTypeFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['TRIP_TYPE_FILTER'] = 'TRIP_TYPE_FILTER'
    tripTypes: Sequence[TripType] = Field(..., description='List of trip types')


class TripUsageType(Enum):
    STANDARD = 'STANDARD'
    EVENT = 'EVENT'


class UAPassPlusMetadataExternal(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    uatpInfo: Mapping[str, Any] | None = Field(
        None, description='UATP card information for UAPassPlus', title='uatpInfo'
    )


class UAPassPlusMetadataWrapperExternal(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    uaPassPlusMetadata: UAPassPlusMetadataExternal


class TicketType3(Enum):
    TICKET_TYPE_UNKNOWN = 'TICKET_TYPE_UNKNOWN'
    ETICKET = 'ETICKET'
    MCO = 'MCO'
    NON_GDS = 'NON_GDS'


class RedeemVia(Enum):
    REDEEM_VIA_OBT = 'REDEEM_VIA_OBT'
    CONTACT_AGENT = 'CONTACT_AGENT'


class SourceOfTruth(Enum):
    SPOTNANA = 'SPOTNANA'
    MANUAL_FORM = 'MANUAL_FORM'


class UpdateTripRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tripId: str = Field(..., description='Spotnana trip Id', examples=['6926658168'])
    tripName: str = Field(
        ..., description='Name of the trip', examples=['JFK SFO Trip']
    )
    tripDescription: str | None = Field(
        None, description='Trip description', examples=['JFK SFO Business Trip']
    )


class UserFacingStatus(Enum):
    UNKNOWN_STATUS = 'UNKNOWN_STATUS'
    PENDING_STATUS = 'PENDING_STATUS'
    CONFIRMED_STATUS = 'CONFIRMED_STATUS'
    ACTIVE_STATUS = 'ACTIVE_STATUS'
    COMPLETED_STATUS = 'COMPLETED_STATUS'
    CANCELLED_STATUS = 'CANCELLED_STATUS'
    REFUNDED_STATUS = 'REFUNDED_STATUS'
    VOIDED_STATUS = 'VOIDED_STATUS'
    PROCESSING_STATUS = 'PROCESSING_STATUS'
    UNCONFIRMED_STATUS = 'UNCONFIRMED_STATUS'
    AIRLINE_CONTROL_STATUS = 'AIRLINE_CONTROL_STATUS'
    PAYMENT_DECLINED_STATUS = 'PAYMENT_DECLINED_STATUS'
    SCHEDULE_CHANGE_STATUS = 'SCHEDULE_CHANGE_STATUS'
    HOLD_STATUS = 'HOLD_STATUS'
    APPROVAL_REQUESTED_STATUS = 'APPROVAL_REQUESTED_STATUS'
    APPROVAL_DENIED_STATUS = 'APPROVAL_DENIED_STATUS'
    CANCELLATION_IN_PROGRESS_STATUS = 'CANCELLATION_IN_PROGRESS_STATUS'
    INOPERATIVE_STATUS = 'INOPERATIVE_STATUS'
    FLIGHT_UNCONFIRMED_STATUS = 'FLIGHT_UNCONFIRMED_STATUS'


class UserId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID


class UserIdWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId | None = None


class UserTitle(Enum):
    TITLE_UNKNOWN = 'TITLE_UNKNOWN'
    MR = 'MR'
    MS = 'MS'
    MRS = 'MRS'
    MX = 'MX'
    MASTER = 'MASTER'
    MISS = 'MISS'
    DR = 'DR'
    PROFESSOR = 'PROFESSOR'
    CAPTAIN = 'CAPTAIN'
    REVEREND = 'REVEREND'
    HONOURABLE = 'HONOURABLE'
    SIR = 'SIR'
    LADY = 'LADY'
    AMBASSADOR = 'AMBASSADOR'
    LORD = 'LORD'
    BRIGADIER = 'BRIGADIER'
    SENATOR = 'SENATOR'
    DAME = 'DAME'
    JUSTICE = 'JUSTICE'
    UK = 'UK'


class VaccineAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['Vaccination facility available'])
    vaccineDescription: str | None = Field(None, description='chek')
    vaccineAttrDescription: str | None = None


class VaccineAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vaccineAmenity: VaccineAmenity | None = None


class ValueAddedServiceFeeType(Enum):
    VIRTUAL_CARD_PAYMENT = 'VIRTUAL_CARD_PAYMENT'
    PRICE_OPTIMIZATION = 'PRICE_OPTIMIZATION'
    UNUSED_CREDIT_APPLICATION = 'UNUSED_CREDIT_APPLICATION'


class VariableName(Enum):
    PUBLISHED_FARE = 'PUBLISHED_FARE'
    LLF = 'LLF'


class Vendor(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    code: str = Field(..., description='Vendor code.', examples=['ZE'])
    name: str = Field(..., description='Vendor name.', examples=['HERTZ'])
    email: str | None = Field(
        None, description='Vendor email.', examples=['<EMAIL>']
    )
    phone: PhoneNumber | None = Field(None, description='Vendor contact number.')


class VendorInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorId: str = Field(..., description='ID of the vendor.')
    vendorName: str = Field(..., description='Name of the vendor.')


class VendorProgramPaymentMetadata2(
    RootModel[DirectBillingWrapper2 | UAPassPlusMetadataWrapperExternal]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: DirectBillingWrapper2 | UAPassPlusMetadataWrapperExternal = Field(
        ...,
        description='Metadata related to vendor program payment method',
        title='VendorProgramPaymentMetadata',
    )


class VirtualCardVendor(RootModel[Literal['CONFERMA']]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Literal['CONFERMA'] = Field(
        'CONFERMA', description='Type of Virtual card vendor', examples=['CONFERMA']
    )


class VirtualCardVendorInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendor: VirtualCardVendor | None = None
    vendorCardId: str = Field(
        ..., description='Virtual card id.', examples=['68793680']
    )
    virtualCardVendorCardPoolId: str = Field(
        ..., description='Card pool id of virtual card vendor.', examples=['51907']
    )
    confermaInfo: ConfermaInfo | None = None


class VirtualPaymentMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    label: str = Field(
        ...,
        description='Label for custom virtual payment source.',
        examples=['Custom payment card'],
    )


class VirtualPaymentMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    virtualPaymentMetadata: VirtualPaymentMetadata


class Voucher(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: SimpleMoney = Field(..., description='Amount in the voucher.')


class Unit2(Enum):
    kg = 'kg'
    lb = 'lb'


class WeightLimit(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    weight: float | None = Field(
        None, description='The weight limit of a baggage option.', examples=[32]
    )
    unit: Unit2 | None = Field(
        None,
        description='The unit of measurement for the weight limit.',
        examples=['kg'],
    )


class WifiAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['Basic web browsing (fee)'])
    cost: str | None = Field(None, examples=['paid'])


class WifiAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    wifiAmenity: WifiAmenity | None = None


class WorkerType(Enum):
    EMPLOYEE = 'EMPLOYEE'
    CONTINGENT = 'CONTINGENT'
    SEASONAL = 'SEASONAL'
    INTERN = 'INTERN'
    GUEST = 'GUEST'


class WorkerTypeListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    workerTypeList: Sequence[WorkerType] | None = None


class WorkerTypeWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    workerType: WorkerType | None = None


class AdhocTravelerInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    registrarUserId: UserId | None = Field(
        None, description='The registrar of the adhoc traveler.'
    )
    externalId: str | None = Field(
        None, description='External Id of user', examples=['qwert123']
    )


class AdhocTravelerInfoWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    adhocTravelerInfo: AdhocTravelerInfo | None = None


class AirAmenity(
    RootModel[
        SeatAmenityWrapper
        | WifiAmenityWrapper
        | PowerAmenityWrapper
        | EntertainmentAmenityWrapper
        | BeverageAmenityWrapper
        | AircraftAmenityWrapper
        | LayoutAmenityWrapper
        | FreshFoodAmenityWrapper
        | CleaningAmenityWrapper
        | MaskAmenityWrapper
        | TemperatureAmenityWrapper
        | PassengerCapacityAmenityWrapper
        | BlockedAdjacentSeatsAmenityWrapper
        | CovidTestingAmenityWrapper
        | VaccineAmenityWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        SeatAmenityWrapper
        | WifiAmenityWrapper
        | PowerAmenityWrapper
        | EntertainmentAmenityWrapper
        | BeverageAmenityWrapper
        | AircraftAmenityWrapper
        | LayoutAmenityWrapper
        | FreshFoodAmenityWrapper
        | CleaningAmenityWrapper
        | MaskAmenityWrapper
        | TemperatureAmenityWrapper
        | PassengerCapacityAmenityWrapper
        | BlockedAdjacentSeatsAmenityWrapper
        | CovidTestingAmenityWrapper
        | VaccineAmenityWrapper
    ) = Field(..., title='AirAmenity')


class AirNameInvoiceMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlineName: str = Field(
        ..., description='Name of the airline.', examples=['United Airlines']
    )
    flightNumber: str = Field(..., description='Flight number.', examples=['UA 123'])
    origin: str = Field(..., description='Origin of the flight.', examples=['SFO'])
    destination: str = Field(
        ..., description='Destination of the flight.', examples=['EWR']
    )
    date: DateModel | None = Field(None, description='Date of the flight.')
    cabin: Cabin = Field(..., description='Cabin class.', examples=['ECONOMY'])
    ancillary: str | None = Field(
        None, description='Ancillary services.', examples=['Seat Purchase']
    )


class AirRequestTravelerInfo(RootModel[UserIdWrapper | AdhocTravelerInfoWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: UserIdWrapper | AdhocTravelerInfoWrapper = Field(
        ...,
        description='The traveler identifiers. These can be either the Spotnana user IDs of the travelers or information regarding\nthe adhoc travelers.\n',
        title='AirRequestTravelerInfo',
    )


class AirVendorCancellationObject(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorCancellationId: str | None = Field(
        None,
        description='Identifier returned by the vendor for a cancellation transaction',
    )
    flightAndLegIndices: Sequence[FlightAndLegIndex] | None = Field(
        None, description='Vendor Cancellation'
    )


class Airport(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airportCode: str = Field(
        ..., description='Unique code for the Airport', examples=['LHR']
    )
    airportName: str = Field(
        ..., description='Full Name of the Airport', examples=['Heathrow Airport']
    )
    cityCode: str = Field(
        ..., description='City Code for the location', examples=['LON']
    )
    location: Location | None = None


class AllowedFlightGuideline(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    allowedFlightType: AllowedFlightType = Field(
        ..., description='The type of flight booking allowed for the event.'
    )
    numberOfLegs: int | None = Field(
        None,
        description='The number of legs allowed for the flight booking.',
        examples=[2],
    )


class AmadeusCheckoutMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentPageId: str | None = Field(
        None, description='Payment page Id.', examples=['aslkdjalwjd']
    )
    splitPaymentAllowed: bool = Field(
        ...,
        description='True if split payment is allowed in Amadeus Checkout, false otherwise',
        examples=[True],
    )
    splitOptionsByCardCompany: Sequence[SplitOptionByCardCompany] | None = None


class AmadeusCheckoutMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amadeusCheckoutMetadata: AmadeusCheckoutMetadata


class AncillaryFlightId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legIndex: AncillaryLegIndex
    flightIndex: AncillaryFlightIndex | None = None


class ApprovalDeadlineDateRangeFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['APPROVAL_DEADLINE_DATE_RANGE_FILTER'] = Field(
        'APPROVAL_DEADLINE_DATE_RANGE_FILTER',
        examples=['APPROVAL_DEADLINE_DATE_RANGE_FILTER'],
    )
    startDate: DateModel | None = Field(
        None, description='Start date for the date range filter'
    )
    endDate: DateModel | None = Field(
        None, description='End date for the date range filter'
    )


class ApprovalDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    originalApprovalType: ApprovalType | None = None
    appliedApprovalType: ApprovalType | None = None


class ApprovalStatusFilterV2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['APPROVAL_STATUS_FILTER'] = Field(
        'APPROVAL_STATUS_FILTER', examples=['APPROVAL_STATUS_FILTER']
    )
    approvalStatuses: Sequence[ApprovalStatusV2] = Field(
        ..., description='Approval Status list'
    )


class ApproverUserIdFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['APPROVER_USER_ID_FILTER'] = Field(
        'APPROVER_USER_ID_FILTER', examples=['APPROVER_USER_ID_FILTER']
    )
    userIds: Sequence[UserId] = Field(..., description='List of user IDs')


class BagWeightLimit(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    weightLimit: Sequence[WeightLimit] | None = Field(
        None, description='Weight limit in different units'
    )
    applicability: BagPolicyApplicability | None = None


class BasicTripInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tripId: str = Field(..., description='Spotnana trip Id', examples=['6926658168'])
    tripName: str = Field(
        ..., description='Name of the trip', examples=['JFK SFO Trip']
    )
    tripDescription: str | None = Field(
        None, description='Trip description', examples=['JFK SFO Business Trip']
    )
    applicationId: UUID | None = Field(
        None, description='Application id used for the trip.'
    )
    startDate: DateModel | None = None
    endDate: DateModel | None = None


class BookingInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    updatedDateTime: DateTimeOffset | None = Field(
        None, description='Date Time when the booking was created/updated'
    )
    status: Status1 | None = Field(
        None,
        description='User facing status of the booking in history',
        examples=['BOOKED'],
    )
    bookingSourceClient: BookingSourceClient | None = Field(
        None, description='Client on which this booking was created/updated'
    )


class BrexBudgetMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    budgetName: str = Field(..., description='Budget name.', examples=['Travel Budget'])
    budgetRemainingBalance: SimpleMoney | None = Field(
        None, description='Remaining balance of the budget.'
    )
    cardId: str | None = Field(None, description='Card id of the budget.')
    budgetRemainingBalanceFormatted: str | None = Field(
        None,
        description='Remaining balance of the budget formatted with currency.',
        examples=['$90,000.00'],
    )
    isPersonalCardAllowed: bool | None = Field(
        None, description='Whether the personal card is allowed for the trip.'
    )
    isBudgetMasked: bool | None = Field(
        None, description='Whether the budget is masked.'
    )


class BrexBudgetMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    brexBudgetMetadata: BrexBudgetMetadata


class BuyerInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(
        ..., description='Name of buyer, usually a traveler.', examples=['John Doe']
    )
    address: str = Field(
        ...,
        description='Address of buyer. Could be office, legal entity or org address.',
        examples=['115 Broadway Suite 04-101,\nNew York, NY 10006'],
    )
    taxId: str | None = Field(
        None,
        description='Tax number for the seller, for ex GSTN in India',
        examples=['TXG239023092'],
    )
    idInfo: Sequence[IdInfo] | None = Field(
        None, description='The seller ID information.'
    )


class CarBookingPaymentGuidelines(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    allowedPaymentVendors: Sequence[VendorInfo] | None = Field(
        None, description='List of allowed car vendors for the event'
    )
    onlyVendorBooking: bool | None = Field(
        None,
        description='Whether only Vendor bookings are allowed for the event.',
        examples=[True],
    )


class CarLocation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress = Field(..., description='Location address / airport code.')
    coordinates: Latlng | None = Field(
        None, description='Latitude and longitude of the location.'
    )
    counterLocation: CounterLocation | None = Field(
        None,
        description='Counter location. If airport counter, set locationCode in address.',
    )
    contactInfo: LocationContactInfo | None = None
    operatingSchedule: LocationOperatingSchedule | None = None


class CarPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendors: Sequence[CarVendor] | None = Field(
        None, description='A list of car vendors.'
    )
    carTypes: Sequence[CarType] | None = Field(
        None, description='A list of types of car.'
    )
    engineTypes: Sequence[EngineType] | None = Field(
        None, description='A list of types of engine.'
    )
    transmissionTypes: Sequence[TransmissionSearchFilter] | None = Field(
        None, description='A list of types of transmission.'
    )
    conditionalRates: Sequence[ConditionalRate] | None = Field(
        None, description='A list of conditional rates for rail.'
    )


class CarSearchLocationParam(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airportCode: str | None = Field(
        None, description='IATA code for AIRPORT', examples=['SFO']
    )
    cityCode: str | None = Field(None, description='City Code.', examples=['JFK'])
    coordinates: Latlng | None = None


class CarSpec(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airConditioning: AirConditioning | None = Field(
        None, description='Whether air conditioning is available.'
    )
    amenities: CarAmenities | None = Field(None, description='Car amenities.')
    type: CarType = Field(..., description='Car type.')
    displayName: str | None = Field(
        None, description='Car display name.', examples=['Economy']
    )
    engineType: EngineType | None = Field(None, description='Engine type.')
    imageGroups: Sequence[ImageGroup] | None = Field(
        None, description='Collection of car images.'
    )
    transmission: Transmission | None = Field(None, description='Transmission type.')


class CardAccessEntity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    entityId: str = Field(
        ...,
        description='Holds the id for for the user who can access the card or organization id or legal entity',
    )
    centralCardAccessLevel: CentralCardAccessLevel | None = None


class CardExpiry(RootModel[TokenizedExpiryWrapper | ExpiryWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: TokenizedExpiryWrapper | ExpiryWrapper = Field(
        ..., description='Contains the expiry of a Card.', title='CardExpiry'
    )


class ChargeProcessorInfo(RootModel[RuleBasedInfo | TmcCalculatorInfo]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: RuleBasedInfo | TmcCalculatorInfo = Field(
        ...,
        description='Information about the processor that calculated the charge',
        discriminator='chargeProcessorType',
        title='ChargeProcessorInfo',
    )


class CompanyConfigSource(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    optionsParam: CustomFieldOptionsParam


class CompanyConfigSourceWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyConfig: CompanyConfigSource | None = None


class CompanyRef(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CompanyId
    name: str | None = None
    logo: Image | None = Field(None, description='Company logo')


class ConnectionRiskAlert(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    departingFlightIndex: int | None = Field(
        None, description='Index of departing flight for this connection'
    )
    minConnectionDuration: Duration | None = Field(
        None, description='Minimum connection time'
    )
    previousConnectionDuration: Duration | None = Field(
        None, description='Connection time before this alert'
    )
    updatedConnectionDuration: Duration | None = Field(
        None, description='Updated connection time'
    )


class CostCenter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CostCenterId
    name: str = Field(..., examples=['CostCenter'])
    externalId: str | None = Field(None, examples=['external-id'])


class CreateTripRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tripName: str = Field(..., description='Name of the trip.')
    tripDescription: str | None = Field(None, description='Description of the trip.')
    userId: UserId | None = None
    registrarId: UserId | None = Field(None, description='Creator of the Trip.')


class CreditCardAccess(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    accessType: CreditCardAccessType
    entityIds: Sequence[str] = Field(
        ...,
        description='Holds the ids for for all users who can access the card or organization id',
    )
    entities: Sequence[CardAccessEntity] | None = Field(
        None,
        description='A list of cardAccessEntity consisting of central card access level if present and entity id.',
    )


class CustomField(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(
        ..., description='Meeting id or budget id based on custom field type.'
    )
    type: CustomFieldType
    description: str = Field(
        ...,
        description='Description of the custom Field',
        examples=['GLOBAL OFFSITE 2022'],
    )


class CustomFieldId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: CustomFieldType
    externalId: str = Field(
        ..., description='Meeting id or budget id based on custom field type.'
    )


class DateRange(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    startDate: DateTimeLocal
    endDate: DateTimeLocal


class Department(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: DepartmentId
    name: str = Field(..., examples=['IT Department'])
    externalId: str | None = Field(
        None,
        description='External id of the department',
        examples=['department-ext-id'],
    )
    employeeCount: int | None = Field(
        None, description='Count of employees in the department', examples=[57]
    )


class EmergencyContact(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Full name of contact.', examples=['John Smith'])
    email: EmailStr | None = Field(
        None,
        description='Email address of contact.',
        examples=['<EMAIL>'],
    )
    designation: str | None = Field(
        None, description='Job title of contact.', examples=['MANAGER']
    )
    relation: Relation | None = Field(
        None, description='Relation of contact to user.', examples=['SPOUSE']
    )
    phoneNumbers: Sequence[PhoneNumber] = Field(
        ..., description='Phone numbers of contact.'
    )
    preferredLanguage: str | None = Field(
        None, description='Language preferred by user.', examples=['en-US']
    )


class EventBookingGuideline4(RailBookingGuidelineWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    numGuestsAllowed: int | None = Field(
        None,
        description='Number of guests allowed to be booked for this booking',
        examples=[1],
    )


class EventEntityMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress | None = None


class EventLocation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress | None = None
    coordinates: Latlng | None = None


class EventMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    eventMetadata: EventEntityMetadata | None = None


class EventRsvpResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    air: AirRsvpResponse | None = None
    hotel: HotelRsvpResponse | None = None
    car: CarRsvpResponse | None = None
    rail: RailRsvpResponse | None = None


class EventUserRsvp(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId | None = None
    eventRsvpState: EventRsvpState | None = None
    eventRsvpResponse: EventRsvpResponse | None = None
    invitedAt: DateTimeOffset | None = None
    airBookingStatus: BookingStatusType | None = None
    railBookingStatus: BookingStatusType | None = None
    carBookingStatus: BookingStatusType | None = None
    hotelBookingStatus: BookingStatusType | None = None


class Expression(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['EXPRESSION'] = Field('EXPRESSION', examples=['EXPRESSION'])
    formatExpression: str = Field(
        ...,
        description='The expression must be of format : `${expression}`.The expression can consist of a \ncombination of variables and mathematical operations.\n Variable names must begin with `var` followed by a number, which is used to identify \nthe variable in the variables list. The numbering should follow a 1-based index.\n  To define mathematical operations, the operation name should follow the format\n`math.<math_op>(arg1, arg2)`. Both `arg1` and `arg2` can be variables or constants. \nThe supported math operations (math_op) include: `add, mul, div, sub, min,\nand max`. All keywords, such as `<math_op>, math, and var` must be written in lowercase.\n',
        examples=['Result:  ${math.mul(var1,5)}  ${var2}'],
    )
    variables: Sequence[VariableName] | None = Field(
        None, description='Reference names of the variables present in the expression.'
    )


class FinalizeMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    isMandatoryTicketing: bool | None = Field(
        None, description='True for forceful ticketing of PNR.'
    )
    mandatorySkipTicketing: bool | None = Field(
        None, description='Override other params and skip ticketing.'
    )
    suspendReason: SuspendReason | None = None
    taskId: str | None = Field(
        None,
        description='Task Id which needs to be mark completed before finalizing the PNR.',
    )


class OtherStatus(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cabin: Cabin = Field(..., description='Upgraded flight cabin')
    status: PnrStatus | None = Field(None, description='Upgraded flight status')


class FlightDetailInformation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    departureDateTime: DateTimeLocal
    arrivalDateTime: DateTimeLocal
    cabin: Cabin | None = None
    originAirportCode: constr(pattern=r'^[A-Z]{3}$') = Field(
        ..., description='3 letter IATA airport code for origin', examples=['SFO']
    )
    destinationAirportCode: constr(pattern=r'^[A-Z]{3}$') = Field(
        ..., description='3 letter IATA airport code for destination', examples=['LHR']
    )
    marketing: FlightNumber | None = None
    operating: FlightNumber | None = None


class FlightSummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    origin: constr(pattern=r'^[A-Z]{3}$') | None = Field(
        None, description='3 letter IATA airport code for origin', examples=['SFO']
    )
    destination: constr(pattern=r'^[A-Z]{3}$') | None = Field(
        None, description='3 letter IATA airport code for destination', examples=['LHR']
    )
    departureDateTime: DateTimeLocal | None = Field(
        None, description='Departure date time of flight'
    )
    arrivalDateTime: DateTimeLocal | None = Field(
        None, description='Arrival date time of flight'
    )
    departureGate: Gate | None = Field(None, description='Departure gate and terminal')
    arrivalGate: Gate | None = Field(None, description='Arrival gate and terminal')
    marketing: FlightNumber | None = Field(None, description='Marketing flight number')
    operating: FlightNumber | None = Field(None, description='Operating flight number')
    operatingAirlineName: str | None = Field(
        None,
        description='Free text operating airline name that has to be displayed to the user if present\n',
        examples=['SKYWEST DBA UNITED EXPRESS'],
    )
    cabin: Cabin | None = Field(None, description='Flight cabin')


class FlightUpdates(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    previousDepartureDateTime: DateTimeLocal | None = Field(
        None, description='Departure date time before this alert'
    )
    previousArrivalDateTime: DateTimeLocal | None = Field(
        None, description='Arrival date time before this alert'
    )
    previousDepartureGate: Gate | None = Field(
        None, description='Departure gate before this alert'
    )
    previousArrivalGate: Gate | None = Field(
        None, description='Arrival gate before this alert'
    )


class FlightWaiverCode(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    waiverCode: str | None = Field(None, description='Flight waiver code')
    firstNewTravelDate: DateTimeLocal | None = Field(
        None, description='First new travel date applicable for the waiver code'
    )
    lastNewTravelDate: DateTimeLocal | None = Field(
        None, description='Last new travel date applicable for the waiver code'
    )
    firstOriginalTravelDate: DateTimeLocal | None = Field(
        None, description='First new travel date applicable for the waiver code'
    )
    lastOriginalTravelDate: DateTimeLocal | None = Field(
        None, description='Last new travel date applicable for the waiver code'
    )
    policyLinks: Sequence[FlightWaiverPolicyLink] | None = Field(
        None, description='List of waiver codes policy link'
    )
    additionalInfo: str | None = Field(
        None, description='Textual information about waiver policies'
    )


class GatewayInfo(RootModel[StripeInfoWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: StripeInfoWrapper = Field(
        ..., description='Payment gateway information', title='GatewayInfo'
    )


class Grade(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    employeeCount: int | None = Field(
        None, description='Count of employees in the grade', examples=[75]
    )
    id: GradeId
    name: str = Field(..., examples=['Grade'])


class HotelAdditionalDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    additionalDetailType: HotelAdditionalDetailType | None = Field(
        None, description='Type of the Additional Detail for the room.'
    )
    text: str | None = None


class HotelAmenities(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: HotelAmenityType | None = None
    additionalInfo: str | None = Field(
        None,
        description='Amenity description',
        examples=['Complimentary in-room coffee or tea'],
    )
    complimentary: bool | None = Field(
        None, description='Is Amenity complimentary', examples=[True]
    )


class HotelData(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelName: str | None = Field(
        None, description='Name of the hotel.', examples=['Marriott Bellandur']
    )
    address: PostalAddress | None = Field(None, description='Address of the hotel.')


class HotelImageSet(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    category: HotelImageCategory
    imageGroup: ImageGroup


class HotelInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress = Field(..., description='Address of the hotel.')
    chainCode: str | None = Field(
        None, description='Chain code of the hotel.', examples=['RF']
    )
    chainName: str | None = Field(
        None, description='Chain name of the hotel.', examples=['Red Roof Inns']
    )
    coordinates: Latlng | None = Field(None, description='Coordinates of the hotel.')
    email: str | None = Field(
        None, description='Email address of the hotel.', examples=['<EMAIL>']
    )
    hotelId: str | None = Field(None, description='Hotel id.', examples=['100094780'])
    name: str = Field(
        ...,
        description='Name of the hotel.',
        examples=['San Francisco Airport Red Roof'],
    )
    phone: PhoneNumber | None = Field(None, description='Phone number of the hotel.')
    starRating: float | None = Field(
        None, description='Star rating of the hotel.', examples=[3.5]
    )
    fax: Sequence[PhoneNumber] | None = None
    masterChainCode: str | None = Field(
        None, description='Master chain code of the hotel.', examples=['EM']
    )
    brandName: str | None = Field(
        None, description='Brand name of the hotel.', examples=['Marriott Hotel Brands']
    )
    amenities: Sequence[HotelAmenities] | None = None
    additionalAmenities: Sequence[str] | None = Field(
        None,
        description='List of amenities provided by the supplier.',
        examples=[['Room service', 'Wifi']],
    )
    imageSets: Sequence[HotelImageSet] | None = None
    descriptions: Sequence[HotelDescription] | None = None
    thirdPartyHotelCodes: Sequence[ThirdPartyHotelCode] | None = None


class HotelPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelParentChains: Sequence[HotelChain] | None = Field(
        None, description='A list of hotel parent chains.'
    )
    hotelBrands: Sequence[HotelBrand] | None = Field(
        None, description='A list of hotel brands.'
    )
    hotelAmenityTypes: Sequence[HotelPrefAmenity] | None = Field(
        None, description='A list of HotelAmenities.'
    )
    roomPreference: RoomPreference | None = None
    conditionalRates: Sequence[ConditionalRate] | None = Field(
        None, description='A list of conditional rates for rail.'
    )


class HotelRateAssuranceMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    selfReportedSavings: SimpleMoney | None = None
    actualSavings: SimpleMoney | None = None


class HotelRoomMeal(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    mealsIncluded: Sequence[HotelRoomMealsIncluded] | None = None
    mealPlan: HotelRoomMealType = Field(
        ..., description='Meal type added with the room booked.'
    )


class HotelSpecialRequests(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    roomLocations: Sequence[RoomLocation] | None = Field(
        None, description='Room Location special request'
    )
    roomFeatures: Sequence[RoomFeature] | None = Field(
        None, description='Room Features List'
    )
    checkIn: CheckIn | None = Field(
        None, description='Early or Late Check-in', examples=['LATE_CHECK_IN']
    )
    checkInTime: TimeLocal | None = Field(
        None, description='Requested time for check-in'
    )
    flightNumber: str | None = Field(
        None, description='Attach flight number', examples=['AC1234']
    )
    additionalNote: str | None = Field(
        None,
        description='Free form text to describe special request',
        examples=['Extra pillows and blankets for added comfort during the stay.'],
    )
    accessibleFeatures: Sequence[HotelAccessibleFeatureType] | None = Field(
        None, description='Accessible Features List'
    )


class HotelTravelerInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerIdx: conint(ge=0) = Field(
        ...,
        description='Index of traveler in travelers list to which this info belongs',
        examples=[0],
    )
    userId: UserId | None = Field(
        None, description='User ID of traveler to which TravelerInfo belongs'
    )
    loyaltyInfos: Sequence[LoyaltyInfo] | None = None


class IdentityDocument(
    RootModel[
        PassportWrapper
        | ImmigrationDocumentWrapper
        | RedressNumberWrapper
        | KnownTravelerNumberWrapper
        | NationalDocWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        PassportWrapper
        | ImmigrationDocumentWrapper
        | RedressNumberWrapper
        | KnownTravelerNumberWrapper
        | NationalDocWrapper
    ) = Field(
        ...,
        description='Identity document details. Currently supported documents are passport, immigration document, \nknown traveler number, redress number and national document.\n',
        title='IdentityDocument',
    )


class InvoiceInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    invoiceNumber: str = Field(
        ..., description='The invoice number for an invoice', examples=['SPOT-0001']
    )
    productType: ProductType = Field(
        ..., description='Type of product for which invoice is generated.'
    )
    invoiceId: str = Field(
        ...,
        description='Unique ID of the invoice.',
        examples=['2a5c0a73-1306-47f2-8210-7dfc812f4d0e'],
    )


class ItemGroupInvoiceData(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    invoiceNumber: str | None = Field(
        None, description='The invoice number for an invoice', examples=[2797091789401]
    )
    buyer: BuyerInfo | None = Field(None, description='Buyer details in the invoice')
    seller: SellerInfo | None = Field(None, description='Seller details in the invoice')


class KeywordsWithReasonList(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    keywords: Sequence[RestrictedKeywordsWithReason] | None = None


class TravelerRestriction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId = Field(
        ..., description='User ID of traveler to which this restriction applies'
    )
    restrictions: Sequence[Restriction] | None = None


class LegSummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legStatus: UserFacingStatus | None = None
    flights: Sequence[FlightSummary] | None = None


class LegUpdates(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    connectionAlerts: Sequence[ConnectionRiskAlert] | None = None


class Driver(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Driver name.', examples=['John Doe'])
    phone: PhoneNumber = Field(..., description='Driver phone number.')
    driverInstructions: str | None = Field(
        None, description='Notes to inform driver about any special instructions.'
    )


class Leg1(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    dropOffDateTime: DateTimeLocal | None = Field(
        None, description='Drop off date and time.'
    )
    dropOffLocation: CarLocation = Field(..., description='Drop off location.')
    dropOffNotes: str | None = Field(None, description='Drop off notes for the driver.')
    pickupDateTime: DateTimeLocal = Field(..., description='Pick up date and time.')
    pickupLocation: CarLocation = Field(..., description='Pick up location.')
    pickupNotes: str | None = Field(None, description='Pick up notes for the driver.')
    sortingPriority: int | None = Field(
        None, description='Sorting priority of this leg'
    )


class LimoDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    carType: CarType1 = Field(
        ..., description='Limo type.', examples=['STANDARD_CAR'], title='CarType'
    )
    electricVehicle: ElectricVehicle = Field(
        ..., description='Whether the limo is electric.'
    )
    vendorName: str | None = Field(
        None, description='Vendor name of the limo.', examples=['NATIONAL']
    )
    limoVendorInfo: LimoVendorInfo | None = Field(
        None, description='Vendor details of the limo.'
    )
    amenities: LimoAmenities | None = Field(
        None,
        description='Contains information about amenities like number of seats, number of bags etc.',
    )


class LimoLegSummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fromDate: DateTimeLocal | None = None
    toDate: DateTimeLocal | None = None
    pickupLocation: CarLocation | None = None
    dropOffLocation: CarLocation | None = None


class ListOrganisationTripsRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    organizationId: OrganizationId
    updatedAt: DateTimeOffset | None = None
    pnrType: PnrType1 = Field(..., description='Type of PNR')
    customFieldIds: Sequence[CustomFieldId] | None = Field(
        None,
        description='List of custom field IDs to filter. This field is not in use and has been deprecated.',
    )


class ManualVerificationIntent(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    remark: str | None = Field(
        None,
        description='Additional remarks or notes about the verification.',
        examples=['Verification provided after manual approval.'],
    )
    reasons: Sequence[ManualVerificationReason] | None = Field(
        None, description='List of reasons for manual verification.'
    )


class ManualVerificationIntentWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    manualVerificationIntent: ManualVerificationIntent | None = None


class MealPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    exclMealPrefs: Sequence[MealType] | None = None
    inclMealPrefs: Sequence[MealType] | None = None
    specialMealDescription: str | None = Field(None, examples=['Veg only meal'])


class OtherCoinageItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    coinageCode: PaymentMethod | None = Field(None, description='Payment method')
    amount: float | None = Field(None, examples=[1000])
    conversionRate: float | None = Field(
        None,
        description='1 coin in this system equals to how many currency value',
        examples=[0.01],
    )
    preferredCurrencyConversionRate: float | None = Field(
        None,
        description='1 coin in this system equals to how many currency value',
        examples=[0.01],
    )


class Money(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: float = Field(
        ..., description='The numeric value for the amount of money.', examples=[510]
    )
    currencyCode: str = Field(
        ...,
        description='The 3-letter currency code for the money amount (defined using ISO 4217 standard).',
        examples=['GBP'],
    )
    convertedAmount: float | None = Field(
        None,
        description="The converted currency and amount that has been converted (if a currency conversion has been requested).\nFor example, if the call requests that money be sent in a specified currency (because the frontend requested\nthe backend to send money in the user's preferred currency).\n",
        examples=[715.42],
    )
    convertedCurrency: str | None = Field(
        None,
        description='The 3-letter currency code for the converted currency (defined using ISO 4217 standard).',
        examples=['USD'],
    )
    otherCoinage: Sequence[OtherCoinageItem] | None = Field(
        None,
        description='List of the dollar amount in other coinage systems like reward points, cryptocurrency etc.',
        title='OtherCoinage',
    )


class MoneyWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    money: Money | None = None


class Name(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    family1: str = Field(..., description='Last (family) name.', examples=['Gandas'])
    family2: str | None = Field(None, examples=['FamilyTwo'])
    given: str = Field(..., description='First (given) name.', examples=['Vichitr'])
    middle: str | None = Field(None, description='Middle name.', examples=['Kumar'])
    suffix: NameSuffix | None = Field(
        None,
        description='Suffix used with the name. For example SR or JR.',
        examples=['SR'],
    )
    preferred: str | None = Field(
        None,
        description='Informal preferred name added by traveler. This is not used on any PNR or tickets',
        examples=['Don'],
    )


class Office(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress | None = None
    id: OfficeId
    name: str = Field(..., examples=['Office'])
    latlng: Latlng | None = None
    taxId: str | None = Field(None, examples=['133232'])


class OptionSourceMetadata(RootModel[CompanyConfigSourceWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: CompanyConfigSourceWrapper = Field(
        ...,
        description='Metadata information for the option source.',
        title='OptionSourceMetadata',
    )


class OtherFeeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingFeeType: str = Field(
        ..., description='Type of the booking fee', examples=['OTHER_FEE']
    )
    calculatedAmount: Money | None = None
    chargeProcessorInfo: ChargeProcessorInfo | None = None
    otherFeeType: OtherFeeType
    feeName: str | None = Field(
        None,
        description='Name of the fee to be charged.',
        examples=['TMC Standard Fee'],
    )


class PaymentTransactionInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentReference: str | None = Field(
        None,
        description='Payment transaction reference',
        examples=['pi_2PAlaAIE$aaaaAaA0bLqk9AI'],
    )
    networkTransactionId: str | None = Field(
        None,
        description='Network transaction id provided by the card network. Applicable only for cards.',
        examples=['721107212171711'],
    )
    gatewayInfo: GatewayInfo | None = None
    failureReason: FailureReason | None = None
    gatewayId: str | None = Field(
        None, description='GatewayId for related gatewayInfo.', examples=['demo-us:1']
    )


class PnrInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str = Field(..., description='Pnr ID', examples=['6789533589'])
    customFields: Sequence[CustomField] | None = None


class PnrMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrMetadata: PnrMetadata | None = None
    invoiceMetadata: InvoiceMetadata | None = Field(
        None,
        description='Metadata associated with an invoice document.',
        title='InvoiceMetadata',
    )
    travelType: TravelType


class TaxItem1(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Money = Field(..., description='Tax amount')
    taxCode: str = Field(..., description='Tax code')


class PnrTaxBreakdown(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tax: Sequence[TaxItem1] = Field(..., description='List of taxes for this ticket')


class Preference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preferredType: PreferredType
    blockedReason: str | None = Field(
        None, description='Reason for blocking the leg, hotel or car.'
    )
    label: str | None = Field(
        None, description='The label assigned to a specific tier of preference.'
    )


class PreferredAirport(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airportName: str | None = Field(
        None,
        description='Airport name.',
        examples=['San Francisco International Airport'],
    )
    airportCode: str = Field(..., description='IATA airport code.', examples=['SFO'])
    label: PreferredLocationLabel


class PrimaryTravelerIdFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['PRIMARY_TRAVELER_ID_FILTER'] = Field(
        'PRIMARY_TRAVELER_ID_FILTER', examples=['PRIMARY_TRAVELER_ID_FILTER']
    )
    primaryTravelerIds: Sequence[UserId] = Field(
        ..., description="Primary traveler's user ID list"
    )


class ProfileOwner(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId


class QantasTravelFundMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    label: str = Field(
        ...,
        description='Label for the Qantas Travel Fund.',
        examples=['QANTAS_TRAVEL_FUND'],
    )
    fundBalance: Money = Field(..., description='Balance of the travel fund wallet.')


class QantasTravelFundMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    qantasTravelFundMetadata: QantasTravelFundMetadata


class QcFinalizationWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    qcFinalizeData: QcFinalizeData | None = None


class RailNameInvoiceMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendor: str | None = Field(
        None, description='Name of the rail.', examples=['Amtrak']
    )
    trainTableId: str | None = Field(
        None, description='Train number.', examples=['A123']
    )
    origin: str | None = Field(
        None, description='Origin of the train.', examples=['Washington Union Station']
    )
    destination: str | None = Field(
        None, description='Destination of the train.', examples=['South Station']
    )
    date: DateModel | None = Field(None, description='Date of the journey.')
    travelClass: RailTravelClass | None = Field(
        None, description='Travel class.', examples=['FIRST']
    )
    fareType: str | None = Field(
        None, description='Fare type.', examples=['Flexible Fare']
    )
    confirmationNumber: str | None = Field(
        None, description='Confirmation number.', examples=['*********0']
    )


class RailPnrAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str | None = Field(
        None, description='Amenity description', examples=['Seat Reservation']
    )
    name: str = Field(..., description='Amenity name', examples=['Seat Reservation'])
    price: Money | None = Field(None, description='Price of the amenity')
    quantity: int | None = Field(
        None, description='Available/Selected quantity', examples=[1]
    )
    type: RailAmenityType | None = Field(None, description='Rail Amenity type')


class RailPnrETicketMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str | None = Field(
        None, description='E-ticket file name.', examples=['Rail e-ticket']
    )
    eticketData: str = Field(..., description='E-ticket data as bytes.')
    type: RailTicketType = Field(..., description='Type of ticket.')


class RailPnrExchangeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    exchangeType: RailExchangeType = Field(..., description='Exchange type.')
    relatedSectionInfo: RailPnrRelatedSectionInfo | None = Field(
        None, description='Related section information for the exchanged itinerary.'
    )


class RailPnrFareLegInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legIndex: int = Field(..., description='Index of leg in journey.', examples=[1])
    travelClass: RailTravelClass | None = Field(None, examples=['FIRST'])


class RailPnrJourneyInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    journeyStatus: PnrStatus | None = Field(None, description='Status of journey.')
    legs: Sequence[conint(ge=0)] = Field(
        ..., description='List of indexes of legs covered in the journey.'
    )
    co2EmissionDetails: RailCo2EmissionDetails | None = Field(
        None, description='This sets the CO2 emission info for rail.'
    )
    sortingPriority: int | None = Field(
        None,
        description='Sort order of the rail journey on the trips page',
        examples=[0],
    )
    fareComposition: RailFareComposition | None = Field(
        None, description='Indicates whether the fare is through or split.'
    )
    userFacingStatus: UserFacingStatus | None = Field(
        None, description='User facing status of the journey.'
    )


class AllocatedSpace(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    coachNumber: str = Field(..., description='Coach Number.', examples=['B'])
    travelerIdx: conint(ge=0) = Field(
        ..., description='Traveler for which the seat belongs.', examples=[0]
    )
    userId: UserId | None = Field(
        None, description='User ID of traveler to which this seat belongs.'
    )
    seatNumber: str = Field(..., description='Seat Number.', examples=['32'])
    additionalProperties: Sequence[str] | None = Field(
        None, description='Additional Properties of allocated seat.'
    )


class RailPnrSummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    journeys: Sequence[RailJourneySummary] | None = None
    totalAmount: Money | None = None


class RailPnrSummaryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    railPnrSummary: RailPnrSummary | None = None


class RailPnrTicketDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    deliveryOption: RailDeliveryOption = Field(
        ..., description='Delivery option selected for the section.'
    )
    eticketMetadata: RailPnrETicketMetadata | None = Field(
        None, description='E-ticket metadata.'
    )
    reference: str | None = Field(
        None,
        description='For KIOSK, it contains the reference number of the ticket.\nFor ELECTRONIC_TICKET, it contains the path where the actual content of the ticket is.\n',
    )
    deliveryStatus: RailTicketDeliveryStatus | None = Field(
        None, description='Delivery status of the ticket.'
    )


class RailRateInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    base: Money = Field(..., description='Base Amount.')
    fees: Money = Field(..., description='Fees and Taxes.')
    railCardDiscount: Money | None = Field(
        None, description='Rail Card Discount Amount'
    )
    total: Money = Field(..., description='Total Amount.')
    refund: Money | None = Field(None, description='Total Refund Amount.')
    refundVoucher: Voucher | None = Field(None, description='Refund voucher details.')
    rateDifference: Money | None = Field(
        None, description='Base rate difference during exchange.'
    )
    totalRateDifference: Money | None = Field(
        None, description='Total rate difference during exchange.'
    )
    includesCommission: bool | None = Field(
        None, description='Whether the rate includes commission', examples=[False]
    )


class RailRefundVoucher(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Money = Field(..., description='Amount refunded as voucher.')


class RailStationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Name of the station')
    code: str | None = Field(None, description='Unique code of the station.')
    cityCode: str | None = Field(
        None, description='City Code for the Railway Station', examples=['LON']
    )
    timeZone: str | None = Field(
        None, description='TimeZone of the Railway Station', examples=['Europe/London']
    )
    countryCode: str | None = Field(
        None,
        description='Unique Code of the Country of Railway Station',
        examples=['GB'],
    )
    latLong: Latlng | None = Field(
        None, description='Coordinates of the Railway Station.'
    )
    localCode: str | None = Field(
        None, description='Local code of the station.', examples=['EUS1444']
    )
    sourceRefInfos: Sequence[RailStationSourceRefInfo] | None = Field(
        None, description='Source Reference Info'
    )
    stationType: StationType | None = Field(
        None, description='Status of the journey.', examples=['METRO']
    )
    cityName: str | None = Field(
        None, description='City Name of the Railway Station', examples=['LONDON']
    )
    stateCode: str | None = Field(
        None, description='State code for the Railway Station', examples=['MA']
    )
    continentCode: str | None = Field(
        None,
        description="Unique Code of the Railway Station's Continent",
        examples=['EU'],
    )


class RailTicketAsset(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str = Field(
        ..., description='Provide some details on what the given asset contains.'
    )
    reference: str = Field(
        ...,
        description='For ticket type collection number, it contains the reference number of the ticket.\nFor others, it contains the path where the actual content of the ticket is.\n',
    )
    type: RailTicketType = Field(
        ..., description='Provide details on what type of the ticket is.'
    )


class RailTravelCard(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fareType: RailFareType | None = Field(None, description='Fare details of the card.')
    rateInfo: RailRateInfo | None = Field(
        None, description='Price details of the card.'
    )
    travelCardKey: str = Field(
        ...,
        description='Base64 encoded string to uniquely identify the card.',
        examples=[
            'CilvcH5VfmZmMjU0MjVhLTQ1ZGYtNDRlZi1iYjFmLWRhZTYxMDFkMDhiORIkYjJiMTdkZmYtNDgwNy00YzgyLWI2YWEtMWM5Yjc0ZTEzOWZi'
        ],
    )
    validityPeriod: RailValidity | None = Field(
        None, description='Validity period of the card.'
    )
    vendorName: str = Field(
        ..., description='Vendor which provides the card.', examples=['ATOC']
    )
    zoneValidity: str | None = Field(
        None,
        description='Zones in which travel card is valid.',
        examples=['London Travelcard Zones 1-4'],
    )


class RebookReference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cancelledPnrIds: Sequence[str] | None = Field(
        None,
        description='Backward reference to the cancelled PNRs (if any) after rebooking.',
    )
    rebookedPnrId: str | None = Field(
        None,
        description='Forward reference to the rebooked PNR (if any) after cancellation.',
        examples=['*********0'],
    )
    hotelRateAssuranceMetadata: HotelRateAssuranceMetadata | None = None
    rebookType: RebookType | None = Field(
        None,
        description='Indicates the reason for rebooking.',
        examples=['RATE_ASSURANCE'],
    )


class RewardsProgramMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: RewardsProgramType
    pointsBalance: PointsBalance | None = None


class RewardsProgramMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    rewardsProgramMetadata: RewardsProgramMetadata | None = None


class RoomViewModel(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: RoomView | None = None
    roomClass: Sequence[RoomClass] | None = None


class RoomInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    roomType: RoomType | None = None
    typeClassDescription: str | None = None
    roomView: RoomViewModel | None = None
    roomTypeCode: str | None = Field(
        None, description='Room type code.', examples=['1QN']
    )


class SabrePnrRemark(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: SabrePnrRemarkType | None = None
    text: str | None = Field(
        None,
        description='ThirdPartyPnrRemark',
        examples=['PREF ALLIANCES 1-ONE WORLD/AA'],
    )
    legIndex: int | None = Field(
        None,
        description='Index referencing the flight leg for which the remark should be added. Currently required only for OSI Remarks. Should be used in conjunction with the flight Index.',
        examples=[1],
    )
    flightIndex: int | None = Field(
        None,
        description='Index referencing the flight for which the remark should be added. Currently required only for OSI Remarks. Should be used in conjunction with the leg Index.',
        examples=[0],
    )


class SabrePnrRemarkWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    sabre: SabrePnrRemark | None = None


class ScheduleChangeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightIndices: Sequence[int] | None = Field(
        None,
        description='List of flight indices in this leg which has schedule changes.',
    )
    disruptedFlightIndices: Sequence[int] | None = Field(
        None,
        description='List of flight indices in the disrupted flights list in the air pnr which has schedule changes.',
    )
    possibleActions: Sequence[ScheduleChangePossibleAction] | None = Field(
        None,
        description='Actions allowed for the user on the schedule change for this leg',
    )


class SearchParams(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    location: CarSearchLocationParam
    datetime: DateTimeLocal
    searchQuery: str | None = Field(
        None, description='Search query provided for the search.', examples=['London']
    )


class SeatPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hasAccessibility: bool | None = Field(
        False,
        description='Whether or not requires assistance for disability.',
        examples=[False],
    )
    seatTypes: Sequence[SeatPrefType] | None = None
    seatLocations: Sequence[SeatPrefLocation] | None = None
    deckLevels: Sequence[DeckLevel] | None = None
    seatDirections: Sequence[SeatPrefDirection] | None = None


class SecondaryServiceProviderTmc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tmcId: CompanyId = Field(..., description='Id of the service provider TMC.')
    supplier: SupplierType = Field(
        ..., description='Supplier for which this service provider should be used.'
    )
    travelType: TravelType = Field(
        ..., description='Travel type for which this service provider should be used.'
    )


class SimpleAirPnrInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legs: Sequence[SimpleLegInfo]


class SimpleRailLegInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    originStationInfo: SimpleRailStationInfo = Field(
        ..., description='The originating station info of the Leg.'
    )
    destinationStationInfo: SimpleRailStationInfo = Field(
        ..., description='The destination station info of the Leg.'
    )
    departureDatetime: DateTimeLocal = Field(
        ..., description='The departure date and time for the flight.'
    )
    arrivalDateTime: DateTimeLocal = Field(
        ..., description='The arrival date and time for the flight.'
    )
    carrierName: str = Field(..., description='The name of the carrier.')
    sortingPriority: int | None = Field(
        None,
        description='This field sets the sorting priority of the rail leg to determine its order of display\non the trips page.\n',
        examples=[0],
    )


class SimpleSectionInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorConfirmationId: str = Field(
        ..., description='The confirmation ID in the vendor system.'
    )
    legs: Sequence[SimpleRailLegInfo] = Field(
        ..., description='The information related to the legs in a section.'
    )


class SplitPayment(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    inPolicyAmount: Money | None = Field(
        None, description='Amount approved by Company to be reimbursed.'
    )
    isAmountReimbursable: bool | None = Field(
        None, description='If the in policy amount is reimbursable or not.'
    )
    personalFopChargeableAmount: Money | None = Field(
        None, description='Amount that is to be paid by traveler itself.'
    )


class Tax(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Money = Field(..., description='Tax amount')
    taxCode: str | None = Field(None, description='Tax code', examples=['VAT'])
    percentage: float | None = Field(
        None, description='Tax amount to total amount', examples=[9]
    )


class TaxAmount(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Money
    taxCode: str = Field(..., description='Tax code', examples=['VAT'])
    description: str = Field(..., description='Tax code')
    percentage: float | None = Field(None, description='Amount', examples=[10])


class TaxBreakdown(RootModel[Sequence[Tax]]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Sequence[Tax]


class ThirdPartyPnrRemark(RootModel[SabrePnrRemarkWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: SabrePnrRemarkWrapper = Field(
        ...,
        description='Third party remarks to the given PNR',
        title='ThirdPartyPnrRemark',
    )


class ExchangePolicy1(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    exchangePenalty: Money | None = Field(
        None, description='Exchange penalty if exchangeable'
    )
    isExchangeable: bool | None = Field(
        None, description='Is ticket exchangeable or not', examples=[True]
    )
    isCat16: bool | None = Field(None, description='Is source cat16', examples=[True])
    isConditional: bool | None = Field(
        None, description='Is conditional', examples=[True]
    )


class RefundPolicy(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    refundPenalty: Money | None = Field(
        None, description='Refund penalty if refundable'
    )
    isRefundable: bool | None = Field(
        None, description='Is ticket refundable or not', examples=[True]
    )
    isRefundableByObt: bool | None = Field(
        None, description='Is ticket refundable or not in OBT', examples=[True]
    )
    isCat16: bool | None = Field(None, description='Is source cat16', examples=[True])
    isConditional: bool | None = Field(
        None, description='Is conditional', examples=[True]
    )


class LlfInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    llf: Money = Field(..., description='Lowest logical fare')
    reasonCode: PreDefinedAnswers | None = Field(
        None, description='Reason code for selecting higher price than llf'
    )
    reason: str | None = Field(
        None,
        description='Free form text if selected reason code is OTHER',
        examples=['Want a stop in London'],
    )


class Commission(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Money | None = Field(
        None, description='The commission amount we are getting from the vendor'
    )
    percent: float | None = Field(
        None,
        description='The percentage of the base amount that we are getting as commission',
        examples=[10],
    )


class TmcBasicInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    contractingTmc: CompanyRef = Field(
        ..., description='Contracting TMC is the TMC the user/organization contracted.'
    )
    bookingTmc: CompanyRef = Field(
        ...,
        description='Booking TMC is the TMC used for the bookings for the user/organization.',
    )


class TmcInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CompanyId = Field(..., description='TMC id.')
    primaryServiceProviderTmc: PrimaryServiceProviderTmc = Field(
        ..., description='Primary service provider TMC for the TMC.'
    )
    secondaryServiceProviderTmcs: Sequence[SecondaryServiceProviderTmc] | None = Field(
        None, description='Secondary service provider TMCs for the TMC.'
    )
    partnerTmcId: CompanyId | None = Field(
        None, description='Useful to identify the clients onboarded by a PARTNER_TMC'
    )


class TransactionFare(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    transactionFareType: TransactionFareType | None = Field(
        None, description='Fare type for this transaction.'
    )
    amount: Money | None = Field(
        None, description='Amount associated with the given fare type.'
    )


class TransactionFeeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingFeeType: str = Field(
        ..., description='Type of the booking fee', examples=['TRANSACTION_FEE']
    )
    calculatedAmount: Money | None = None
    chargeProcessorInfo: ChargeProcessorInfo | None = None
    transactionFeeType: ServiceFeeTransactionType | None = None


class TravelerBusinessInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legalEntity: TravelerLegalEntity | None = None
    companyId: CompanyId | None = None
    companyInfo: TravelerCompanyInfo | None = None
    workerType: WorkerType | None = None
    employeeId: str | None = Field(
        None, description='Employee id of the user', examples=['1234']
    )
    companySpecifiedAttributes: Sequence[CompanySpecifiedAttribute] | None = None
    userCostCenter: CostCenter | None = None
    grade: Grade | None = None
    office: Office | None = None
    department: DepartmentV2 | None = None
    accountingCode: str | None = Field(
        None, description='Code used for accounting.', examples=['123']
    )


class TravelerInfoResponse(RootModel[UserIdWrapper | AdhocTravelerInfoWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: UserIdWrapper | AdhocTravelerInfoWrapper = Field(
        ...,
        description='The traveler identifiers. These can be either the Spotnana user IDs of the travelers or information regarding\nthe adhoc travelers.\n',
        title='TravelerInfoResponse',
    )


class TravelerMatchConditions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    workerTypes: Sequence[WorkerType] | None = Field(
        None, description='Worker types. Users belonging to any of these would match.'
    )
    countries: Sequence[str] | None = Field(None, description='Countries.')
    legalEntities: Sequence[Reference] | None = Field(
        None, description='Legal entities'
    )
    departments: Sequence[Reference] | None = Field(None, description='Departments')
    costCenters: Sequence[Reference] | None = Field(None, description='Cost centers')
    offices: Sequence[Reference] | None = Field(None, description='Offices')


class TravelerRailInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerIndex: int | None = None
    userId: UserId | None = Field(
        None, description='User ID of traveler to which this TravelerInfo belongs'
    )
    railCard: RailCard | None = None
    loyaltyDetails: LoyaltyDetails | None = None


class TripFeeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingFeeType: str = Field(
        ..., description='Type of the booking fee', examples=['TRIP_FEE']
    )
    calculatedAmount: Money | None = None
    chargeProcessorInfo: ChargeProcessorInfo | None = None


class TripUsageMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tripUsageType: TripUsageType = Field(
        ..., description='The trip usage type for the trip entity.'
    )
    eventMetadata: TripEventMetadata | None = Field(
        None,
        description='Metadata associated with the event if trip usage type is event.',
    )


class TripUserIdFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    filterType: Literal['TRIP_USER_ID_FILTER'] = 'TRIP_USER_ID_FILTER'
    userIds: Sequence[UserId] = Field(..., description='List of user ids')


class UnusedCreditInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    sourcePnr: str | None = Field(
        None,
        description='PNR number corresponding to third party through which booking was made.',
        examples=['MC5ONS'],
    )
    spotnanaPnr: str | None = Field(
        None, description='Spotnana pnr ID.', examples=['2345678']
    )
    ticketNumber: str | None = Field(
        None,
        description='Ticket number for the ticket that was converted into an unused credit.',
        examples=['5267779139217'],
    )
    airlineCode: str | None = Field(
        None,
        description='2 letter airline code of the airline associated with this unused credit.',
        examples=['AA'],
    )
    airlineInfo: AirlineInfo | None = Field(
        None, description='Airline info with airline name and code'
    )
    totalFare: Money | None = Field(
        None, description='Total airfare associated with the original ticket.'
    )
    issueDate: DateTimeOffset | None = Field(
        None, description='Issue date for the unused credit.'
    )
    expiryDate: DateTimeOffset | None = Field(
        None, description='Expiry date for the unused credit.'
    )
    usedDate: DateTimeOffset | None = Field(
        None, description='Date on which the unused credit was used.'
    )
    departureDate: DateTimeOffset | None = Field(
        None,
        description='Date for the departure of the first flight associated with the unused credit.',
    )
    segmentsAvailable: SegmentsAvailable | None = Field(
        None,
        description='Whether all segments are unused or some have already been used.',
    )
    passengerName: Name | None = Field(
        None, description='Name of the passenger associated with the credit.'
    )
    departureCountry: str | None = Field(
        None,
        description='3 letter country code of the departure country associated with the original ticket.',
        examples=['USA'],
    )
    arrivalCountry: str | None = Field(
        None,
        description='3 letter country code of the arrival country associated with the original ticket.',
        examples=['USA'],
    )
    ticketType: TicketType3 | None = Field(None, description='Type of credit.')
    pcc: str | None = Field(None, description='PCC the credit was issued on.')
    status: CreditStatus | None = None
    source: ThirdPartySource | None = Field(
        'SABRE', description='Source of unused credit e.g. Sabre, NDC etc.'
    )
    tripId: str | None = Field(
        None,
        description='Trip ID that contains the unused credit',
        examples=['1234567'],
    )
    redeemVia: RedeemVia | None = Field(
        None,
        description='Credit redemption method. \nIf the value contains `CONTACT_AGENT`, then the agent must book the ticket and redeem the credits on behalf of the traveler.\n',
        examples=['REDEEM_VIA_OBT'],
    )
    sourceOfTruth: SourceOfTruth | None = Field(
        None, description='The system that owns the credit.'
    )
    owningPcc: str | None = Field(None, description='PCC the PNR was created on.')
    paymentSourceId: UUID | None = Field(
        None,
        description='Payment source ID associated with the credit.',
        examples=['edd5b835-8001-430c-98f8-fedeccebe4cf'],
    )
    creditUsageType: CreditUsageType | None = Field(
        None,
        description='The type of credit usage. This can be either COMPANY or PERSONAL.',
    )
    email: str | None = Field(
        None, description='Email of the passenger owning the unused credit.'
    )


class UserOrgId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    organizationAgencyId: OrganizationAgencyId | None = None
    organizationId: OrganizationId
    userId: UserId
    tmcInfo: TmcInfo | None = None
    tmcBasicInfo: TmcBasicInfo | None = None


class UserOrgIdListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userOrgIdList: Sequence[UserOrgId] | None = None


class UserOrgIdWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userOrgId: UserOrgId | None = None


class ValueAddedServiceFeeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingFeeType: str = Field(
        ..., description='Type of the booking fee', examples=['VALUE_ADDED_SERVICE_FEE']
    )
    calculatedAmount: Money | None = None
    chargeProcessorInfo: ChargeProcessorInfo | None = None
    valueAddedServiceFeeType: ValueAddedServiceFeeType | None = None


class Variable(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['VARIABLE'] = Field('VARIABLE', examples=['VARIABLE'])
    name: VariableName


class VirtualCardInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Money | None = Field(
        None,
        description='Virtual card deployment amount. This amount will drive the maximum authorisation value permitted on the virtual card.',
    )
    dateRange: DateRange | None = Field(
        None, description='Date range within which the virtual card can be charged.'
    )


class VirtualCardMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendor: VirtualCardVendor
    cardLabel: str = Field(
        ...,
        description='Name/Label for visual identification of the card.',
        examples=['cardCompany'],
    )
    company: CardCompany
    paymentInstruction: PaymentInstruction | None = None


class VirtualCardMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    virtualCardMetadata: VirtualCardMetadata | None = None


class VirtualCardPaymentMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorInfo: VirtualCardVendorInfo
    cardInfo: VirtualCardInfo | None = None
    paymentInstructionId: str | None = Field(
        None,
        description='Payment instruction id set during addition of virtual card payment source',
        examples=['1eb8b778-f0a6-4037-865c-4580982fa36e'],
    )
    shouldReveal: bool | None = Field(
        None,
        description='Identifier for when to reveal virtual cards as they are revealed to travellers only 24hrs before check-in.',
        examples=[False],
    )


class AddRemarksRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    remarks: Sequence[ThirdPartyPnrRemark] = Field(
        ..., description='The list of remarks to be added to the give PNR.'
    )


class AddRemarksResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    processedRemarks: Sequence[ThirdPartyPnrRemark] | None = Field(
        None, description='The list of remarks added to the given PNR.'
    )
    ignoredRemarks: Sequence[ThirdPartyPnrRemark] | None = Field(
        None, description='The list of remarks ignored.'
    )


class AdditionalInfo(RootModel[Variable | Expression]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Variable | Expression = Field(
        ...,
        description='Additional data need to be sent along with the custom field response.',
        discriminator='type',
        title='AdditionalInfo',
    )


class AdhocUserInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    profileOwner: ProfileOwner
    isSaved: bool | None = Field(
        False,
        description='A boolean flag to show if ad-hoc traveler is visible in search. While updating the user \nif client tries to update this field, it will throw exception.\n',
    )


class AirBookingGuideline(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    allowedAirports: Sequence[AirportInfo] | None = Field(
        None,
        description='List of allowed airports for arrival of flight-in-event and departure of flight-out-event.',
    )
    arrivalBookingWindow: EventBookingWindow | None = Field(
        None, description='Booking window for flight-in-event.'
    )
    departureBookingWindow: EventBookingWindow | None = Field(
        None, description='Booking window for flight-out-event.'
    )
    allowedFlightGuidelines: Sequence[AllowedFlightGuideline] | None = Field(
        None, description='List of allowed flight guidelines for the event.'
    )


class AirBookingGuidelineWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airBookingGuideLine: AirBookingGuideline | None = None


class AirFlight(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    arrivalDateTime: DateTimeLocal | None = Field(
        None, description='Arrival date time of flight'
    )
    departureDateTime: DateTimeLocal | None = Field(
        None, description='Departure date time of flight'
    )
    marketing: FlightNumber | None = Field(None, description='Marketing flight number')
    operating: FlightNumber | None = Field(None, description='Operating flight number')
    origin: Airport | None = Field(
        None, description='Airport info about the origin airport.'
    )
    destination: Airport | None = Field(
        None, description='Airport info about the destination airport.'
    )
    cabin: Cabin | None = Field(None, description='Cabin class for this flight')


class AirItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    itemType: Literal['AIR_ITEM'] = 'AIR_ITEM'
    airItemType: AirItemType | None = Field(
        None, description='type of the air item , could be flights or ancillary.'
    )
    flights: Sequence[AirFlight] | None = None
    ancillaryTypes: Sequence[TransactionAncillaryType] | None = None
    oldFlights: Sequence[AirFlight] | None = None


class AirItemNameMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrType: Literal['AIR'] = Field('AIR', examples=['AIR'])
    ticketStatus: TicketStatus | None = Field(
        None, description='Ticket status.', examples=['REFUNDED_EXCHANGED']
    )
    airNameInvoiceMetadata: Sequence[AirNameInvoiceMetadata] | None = Field(
        None, description='Air invoice metadata.'
    )
    defaultItemName: str | None = Field(
        None, description='Default item name.', examples=['Flight void']
    )


class AirPnrSummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legInfos: Sequence[LegSummary] | None = None
    totalAmount: Money | None = None


class AirPnrSummaryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airPnrSummary: AirPnrSummary | None = None


class AirPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlinePrefs: Sequence[AirlinePref] | None = None
    alliancePref: AlliancePref | None = None
    farePref: FarePref | None = None
    homeAirport: str | None = Field(None, examples=['NEW YORK'])
    mealPref: MealPref | None = None
    numStopPref: NumStopsPref | None = None
    seatAmenityPref: SeatAmenityPref | None = None
    seatLocationPrefs: Sequence[SeatLocationPref] | None = None
    preferredAirports: Sequence[PreferredAirport] | None = Field(
        None, description='A list of user preferred airports.'
    )


class AirPriceOptimizationMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    oldTickets: Sequence[str] | None = Field(
        None,
        description='List of old tickets present in PNR before rebook.\nFor non-ticketed PNRs, tickets will not be present.\n',
    )
    newTickets: Sequence[str] | None = Field(
        None,
        description='List of new tickets in PNR after rebook.\nFor non-ticketed PNRs, tickets will not be present.\n',
    )
    oldPnrId: str = Field(
        ...,
        description='Old Spotnana PnrId of the PNR before rebook.\n',
        examples=['6789533589'],
    )
    newPnrId: str = Field(
        ...,
        description='New Spotnana PnrId of the PNR after rebook.\nIf the same PNR has been modified during rebook, oldPnrId and newPnrId will be same.\nIf a new PNR has been booked during rebook, oldPnrId and newPnrId will be different.\n',
        examples=['6789533589'],
    )
    oldPrice: Money = Field(..., description='Total price of the PNR before rebook.')
    newPrice: Money = Field(..., description='Total price of the PNR after rebook.')
    priceDrop: Money = Field(..., description='Total price drop during rebook process.')
    penaltyPrice: Money | None = Field(
        None, description='Penalty amount paid during rebook process.'
    )


class AirPriceOptimizationMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airPriceOptimizationMetadata: AirPriceOptimizationMetadata | None = None


class AirVendorCancellationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airVendorCancellationObjects: Sequence[AirVendorCancellationObject] | None = Field(
        None, description='Vendor Cancellation'
    )


class ApprovalFilterV2(
    RootModel[
        ApprovalStatusFilterV2
        | ApproverUserIdFilter
        | ApprovalTypeFilter
        | ApprovalDeadlineDateRangeFilter
        | PrimaryTravelerIdFilter
        | PnrTypeFilterV2
        | OutOfPolicyFilter
        | ApprovalTripIdFilter
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        ApprovalStatusFilterV2
        | ApproverUserIdFilter
        | ApprovalTypeFilter
        | ApprovalDeadlineDateRangeFilter
        | PrimaryTravelerIdFilter
        | PnrTypeFilterV2
        | OutOfPolicyFilter
        | ApprovalTripIdFilter
    ) = Field(
        ...,
        description='Filter parameters for the approval data list request.',
        discriminator='filterType',
        title='ApprovalFilterV2',
    )


class BagFees(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fee: Money | None = None
    applicability: BagPolicyApplicability | None = None


class BaggageInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str | None = Field(
        None, description='Baggage policy text', examples=['1 checked bag, 33 lbs']
    )
    count: int | None = Field(None, description='Count of bags', examples=[3])
    sizeLimitInfo: Sequence[SizeInfo] | None = Field(
        None, description='Size of bag in cm'
    )
    weightLimitInfo: Sequence[BagWeightLimit] | None = Field(
        None, description='Array of bag weight limits'
    )
    fee: Sequence[BagFees] | None = Field(None, description='Array of bag weight fees')


class BaggagePolicy(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    checkedIn: Sequence[BaggageInfo] = Field(
        ..., description='Policies for checked-in baggage'
    )
    carryOn: Sequence[BaggageInfo] = Field(
        ..., description='Policies for carry-on baggage'
    )


class Seat(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legIdx: conint(ge=0) | None = Field(
        None, description='Index of leg to which this seat belongs', examples=[0]
    )
    flightIdx: conint(ge=0) | None = Field(
        None,
        description="Index of flight in it's leg to which this seat belongs",
        examples=[0],
    )
    amount: Money | None = Field(None, description='Total seat price')
    number: str | None = Field(
        None, description='Assigned seat number', examples=['32A']
    )
    status: FlightSeatStatus | None = Field(None, description='Seat booking status')
    sourceStatus: str | None = Field(
        None, description='Raw status code of the seat. E.g. HK, KK.', examples=['HK']
    )
    assignmentType: AirSeatAssignmentType | None = Field(
        None, description='The type of seat assignment.'
    )


class OtherCharge(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Money = Field(..., description='Total amount for extra item')
    type: str = Field(..., description='Name of extra item', examples=['Meal'])


class BookingFeeInfo1(TripFeeInfo):
    model_config = ConfigDict(
        frozen=True,
    )
    feeType: Literal['BOOKING_FEE'] = Field(
        'BOOKING_FEE', description='Type of the fee', examples=['BOOKING_FEE']
    )
    bookingFeeType: Literal['TRIP_FEE'] = 'TRIP_FEE'


class BookingFeeInfo2(TransactionFeeInfo):
    model_config = ConfigDict(
        frozen=True,
    )
    feeType: Literal['BOOKING_FEE'] = Field(
        'BOOKING_FEE', description='Type of the fee', examples=['BOOKING_FEE']
    )
    bookingFeeType: Literal['TRANSACTION_FEE'] = 'TRANSACTION_FEE'


class BookingFeeInfo3(ValueAddedServiceFeeInfo):
    model_config = ConfigDict(
        frozen=True,
    )
    feeType: Literal['BOOKING_FEE'] = Field(
        'BOOKING_FEE', description='Type of the fee', examples=['BOOKING_FEE']
    )
    bookingFeeType: Literal['VALUE_ADDED_SERVICE_FEE'] = 'VALUE_ADDED_SERVICE_FEE'


class BookingFeeInfo4(OtherFeeInfo):
    model_config = ConfigDict(
        frozen=True,
    )
    feeType: Literal['BOOKING_FEE'] = Field(
        'BOOKING_FEE', description='Type of the fee', examples=['BOOKING_FEE']
    )
    bookingFeeType: Literal['OTHER_FEE'] = 'OTHER_FEE'


class BookingFeeInfo(
    RootModel[BookingFeeInfo1 | BookingFeeInfo2 | BookingFeeInfo3 | BookingFeeInfo4]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: BookingFeeInfo1 | BookingFeeInfo2 | BookingFeeInfo3 | BookingFeeInfo4 = Field(
        ..., discriminator='bookingFeeType', title='BookingFeeInfo'
    )


class BookingHistory(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookerInfo: BookerInfo | None = Field(None, description='Booker Information')
    bookingInfo: BookingInfo | None = Field(None, description='Booking Information')


class CancellationPolicy1(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str | None = Field(
        None,
        description='Text describing the cancellation policy.',
        examples=['Non-refundable'],
    )
    fee: Money | None = None
    assessmentType: AssessmentType | None = Field(None, description='Assessment Type')
    isCat16: bool | None = Field(None, description='Is source cat16', examples=[True])
    isConditional: bool | None = Field(
        None, description='Is conditional', examples=[True]
    )


class CancellationPolicy2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    policy: Policy = Field(
        ...,
        description='Indicates whether the booking is refundable or not.',
        examples=['NON_REFUNDABLE'],
    )
    deadline: DateTimeLocal | None = Field(
        None,
        description='The local date and time of the final deadline for cancellation, if policy type is\nFREE_CANCELLATION_UNTIL or PARTIALLY_REFUNDABLE.\n',
    )
    deadlineUtc: DateTimeOffset | None = Field(
        None,
        description='The UTC date and time of the final deadline for cancellation, if policy type is\nFREE_CANCELLATION_UNTIL or PARTIALLY_REFUNDABLE.\n',
    )
    durationBeforeArrivalDeadline: Duration | None = Field(
        None,
        description='The duration before arrival of the final deadline for cancellation, if policy type is\n`FREE_CANCELLATION_UNTIL` or `PARTIALLY_REFUNDABLE`.\n',
    )
    amount: Money | None = None


class CarBookingGuideline(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pickupBookingWindow: EventBookingWindow | None = None
    dropoffBookingWindow: EventBookingWindow | None = None
    paymentGuidelines: CarBookingPaymentGuidelines | None = None


class CarBookingGuidelineWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    carBookingGuideLine: CarBookingGuideline | None = None


class CarDailyRate(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    total: Money | None = Field(None, description='Daily total rate.')
    base: Money | None = Field(None, description='Daily base rate.')
    tax: Money | None = Field(None, description='Daily tax rate.')


class CarInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    carSpec: CarSpec = Field(..., description='Vehicle information.')
    dropOffLocation: CarLocation = Field(..., description='Drop off location.')
    mileageAllowance: Length | None = Field(None, description='Mileage allowance.')
    pickupLocation: CarLocation = Field(..., description='Pick up location.')
    vendor: Vendor
    carTypeCode: str | None = Field(
        None, description='Car type code', examples=['EBMR']
    )
    extraMileageCharge: Money | None = None
    preferredType: Sequence[PreferredType] | None = Field(
        None, description='Car preference'
    )
    preferences: Sequence[Preference] | None = Field(
        None, description='Car preferences'
    )
    co2EmissionDetail: CarCo2EmissionDetail | None = Field(
        None, description='The CO2 emission detail for car.'
    )


class CarItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    itemType: Literal['CAR_ITEM'] = 'CAR_ITEM'
    pickUpDateTime: DateTimeLocal | None = Field(
        None, description='Check in date time.'
    )
    dropOffDateTime: DateTimeLocal | None = Field(
        None, description='Check out date time.'
    )
    pickUpLocation: CarLocation | None = Field(None, description='Pick up location.')
    dropOffLocation: CarLocation | None = Field(None, description='Drop off location.')
    carSpec: CarSpec | None = Field(None, description='Car specific details.')
    carDailyAverageRate: CarDailyRate | None = Field(
        None, description='Car average daily rental rate.'
    )
    dailyRates: Sequence[CarDailyRate] | None = Field(
        None, description='Daily rates for rental car in the date range.'
    )


class CarPnrSummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vehicleName: str | None = Field(
        None, description='Name of vehicle', examples=['mercedez']
    )
    vendor: Vendor | None = None
    totalAmount: Money | None = None
    fromDate: DateTimeLocal | None = None
    toDate: DateTimeLocal | None = None
    pnrStatus: PnrStatus | None = None
    pickupLocation: CarLocation | None = None
    dropOffLocation: CarLocation | None = None


class CarPnrSummaryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    carPnrSummary: CarPnrSummary | None = None


class CarSearchInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pickup: SearchParams
    dropOff: SearchParams


class Card(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID | None = Field(
        None,
        description='Unique identifier for this card',
        examples=['34d536b6-f8ff-11eb-9a61-0242ac180002'],
    )
    type: Type | None = Field(None, description='Type of card', examples=['CREDIT'])
    company: CardCompany | None = None
    name: str | None = Field(
        None, description='Name on card', examples=['Harrison Schwartz']
    )
    address: PostalAddress | None = Field(None, description='Billing address')
    number: str = Field(..., description='Card number', examples=['****************'])
    expiryMonth: conint(ge=1, le=12) | None = Field(
        None, description='Expiry month', examples=[1]
    )
    expiryYear: conint(ge=2000) | None = Field(
        None, description='Expiry year', examples=[2010]
    )
    cvv: str | None = Field(None, description='Card cvv number', examples=['012'])
    label: str | None = Field(None, description='Card Label', examples=['Label amex'])
    currency: str | None = Field(
        None, description='Native currency of the card.', examples=['USD']
    )
    externalId: str | None = Field(
        None,
        description='Spotnana partner card id.',
        examples=['bxt_RNGsNfzgJDaTstKIKqK4xEuhGYAnMdYK8T40'],
    )
    vaultId: UUID | None = Field(
        None,
        description='ID of the vault used for creating the card.',
        examples=['34d536b6-f8ff-11eb-9a61-0242ac180002'],
    )
    expiry: CardExpiry | None = Field(None, description='Card Expiry.')
    ownershipLabel: OwnershipLabel | None = Field(None, examples=['PERSONAL'])


class CardDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    company: CardCompany
    token: str = Field(
        ..., description='Tokenized Card Number', examples=['****************']
    )
    expiry: CardExpiry


class CardMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    card: Card


class CardMetadata2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    card: Card | None = None
    accessType: CreditCardAccess | None = None
    isLodgeCard: bool | None = Field(
        None,
        description='Whether the payment is made using a lodge card',
        examples=[False],
    )
    bta: str | None = Field(
        None,
        description="Whether this is a BTA card. Possible values are 'Y' or 'N'. Relevant only for lodge cards.",
        examples=['Y'],
    )


class CardMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cardMetadata: CardMetadata | None = None


class Extra(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: str = Field(..., description='Charge type.', examples=['SERVICE_CHARGE'])
    amount: Money = Field(..., description='Extra amount.')


class TaxItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Money | None = Field(None, description='Tax amount')
    taxCode: str | None = Field(None, description='Tax code')


class TaxBreakdownModel(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tax: Sequence[TaxItem] | None = Field(
        None, description='List of taxes for this ticket'
    )


class Credit(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['CREDIT'] = Field(
        'CREDIT',
        description='Payment source type. Should be set to UNUSED_CREDIT for unused credit details.',
    )
    pnrOwningPcc: str | None = Field(None, description='PCC the PNR was created on.')
    unusedCreditPcc: str | None = Field(
        None, description='PCC the credit was issued on.'
    )
    departureCountry: str | None = Field(
        None,
        description='3 letter country code of the departure country associated with the original ticket.',
        examples=['USA'],
    )
    arrivalCountry: str | None = Field(
        None,
        description='3 letter country code of the arrival country associated with the original ticket.',
        examples=['USA'],
    )
    ticketType: TicketType = Field(..., description='Type of credit.')
    departureDate: DateTimeOffset | None = Field(
        None,
        description='Date for the departure of the first flight associated with the unused credit.',
    )
    segmentsAvailable: SegmentsAvailable = Field(
        ...,
        description='Whether all segments are unused or some have already been used.',
    )
    traveler: AirRequestTravelerInfo = Field(
        ...,
        description='Information about the traveler for which the credit should be redeemed.',
    )
    passengerName: Name = Field(
        ..., description='Name of the passenger associated with the credit.'
    )
    airlineInfo: AirlineInfo = Field(
        ..., description='Airline info with airline name and code'
    )
    totalFare: Money = Field(
        ..., description='Total airfare associated with the original ticket.'
    )
    issueDate: DateTimeOffset | None = Field(
        None, description='Issue date for the unused credit.'
    )
    expiryDate: DateTimeOffset | None = Field(
        None, description='Expiry date for the unused credit.'
    )
    source: ThirdPartySource | None = Field(
        'SABRE', description='Source of unused credit e.g. Sabre, NDC etc.'
    )
    sourcePnr: str = Field(
        ...,
        description='PNR number corresponding to third party through which booking was made.',
        examples=['MC5ONS'],
    )
    flightIds: Sequence[str] | None = Field(
        None,
        description='ID of the flights on which this credit applies.',
        min_length=1,
    )
    ticketNumber: str = Field(
        ...,
        description='Ticket number for the ticket that was converted into an unused credit.',
        examples=['5267779139217'],
    )


class CustomFieldMatchConditions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerConditions: TravelerMatchConditions | None = None
    travelTypes: Sequence[TravelType] | None = Field(
        None, description='Travel types to match.'
    )
    travelRegionTypes: Sequence[TravelRegionType] | None = Field(
        None, description='Travel region types to match.'
    )
    tripUsageTypes: Sequence[TripUsageType] | None = Field(
        None,
        description='Trip usage types to match. If empty, all trip usage types will be matched.',
    )


class CustomFieldSelectedOption(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Value of the selection')
    description: str | None = Field(None, description='Description of the selection')
    additionalUserInput: str | None = Field(None, description='Additional user input')
    additionalInfos: Sequence[str] | None = Field(
        None, description='Actual values of the additional infos'
    )
    additionalInfoConfigs: Sequence[AdditionalInfo] | None = Field(
        None, description='Additional info configs for the selected option'
    )


class CustomFieldV3Response(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fieldId: UUID = Field(..., description='Custom field id')
    fieldName: str | None = Field(None, description='Name of the custom field')
    armId: UUID = Field(..., description='Arm id which is applicable')
    includeLocations: Sequence[IncludeLocation] | None = None
    selectedOptions: Sequence[CustomFieldSelectedOption] = Field(
        ...,
        description='The list of options that are selected by user or auto populated.',
    )


class CustomPaymentMethodMetadata(
    RootModel[
        BrexBudgetMetadataWrapper
        | AmadeusCheckoutMetadataWrapper
        | QantasTravelFundMetadataWrapper
        | VirtualPaymentMetadataWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        BrexBudgetMetadataWrapper
        | AmadeusCheckoutMetadataWrapper
        | QantasTravelFundMetadataWrapper
        | VirtualPaymentMetadataWrapper
    ) = Field(
        ...,
        description='Metadata for Custom Payment Method payment source.',
        title='CustomPaymentMethodMetadata',
    )


class CustomPaymentMethodMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    customPaymentMethodMetadata: CustomPaymentMethodMetadata | None = None


class Dpan(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['DPAN'] = Field(
        'DPAN',
        description='Payment source type. Should be set to DPAN for card details.',
    )
    cardDetails: CardDetails


class EmergencyContactInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress | None = None
    designation: str | None = Field(None, examples=['MANAGER'])
    email: EmailStr = Field(..., examples=['<EMAIL>'])
    name: Name | None = None
    phoneNumber: PhoneNumber | None = None
    userOrgId: UserOrgId | None = None


class EntityMetadata(RootModel[PnrMetadataWrapper | EventMetadataWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: PnrMetadataWrapper | EventMetadataWrapper = Field(
        ...,
        description='Metadata for associated entity of document.',
        title='EntityMetadata',
    )


class EventBookingGuideline1(AirBookingGuidelineWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    numGuestsAllowed: int | None = Field(
        None,
        description='Number of guests allowed to be booked for this booking',
        examples=[1],
    )


class EventBookingGuideline3(CarBookingGuidelineWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    numGuestsAllowed: int | None = Field(
        None,
        description='Number of guests allowed to be booked for this booking',
        examples=[1],
    )


class EventUserInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId
    email: str | None = Field(
        None, description='Business email of the user', examples=['<EMAIL>']
    )
    name: Name


class ExchangePolicy(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str | None = Field(
        None,
        description='Text describing the exchange policy.',
        examples=['Change allowed for free'],
    )
    fee: Money | None = None
    assessmentType: AssessmentType | None = None
    isCat16: bool | None = Field(None, description='Is source cat16', examples=[True])
    isConditional: bool | None = Field(
        None, description='Is conditional', examples=[True]
    )


class FareAmount(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    base: Money = Field(..., description='Base fare amount.')
    tax: Money = Field(..., description='Tax amount.')


class FareComponentDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fareBasisCode: str = Field(
        ..., description='Fare basis code for the booking', examples=['QUAJZNB3']
    )
    tourCode: str | None = Field(
        None, description='Tour code applied on this fare component', examples=['ABC12']
    )
    ticketDesignator: str | None = Field(
        None,
        description='Ticket designator applied on this fare component',
        examples=['ABC'],
    )
    baseFare: Money | None = Field(
        None, description='Base fare for this fare component'
    )
    flightIds: Sequence[FlightId] = Field(..., min_length=1)
    farePaxType: PassengerType | None = Field(
        None,
        description='The passengerType of the fare, this can be different from the passengerType of the user for which this fare is booked.',
    )


class FeeInfo(RootModel[BookingFeeInfo]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: BookingFeeInfo = Field(..., discriminator='feeType', title='FeeInfo')


class FinalizeIntent(
    RootModel[
        AirPriceOptimizationMetadataWrapper
        | ManualVerificationIntentWrapper
        | QcFinalizationWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        AirPriceOptimizationMetadataWrapper
        | ManualVerificationIntentWrapper
        | QcFinalizationWrapper
    ) = Field(..., description='Intent of finalizing Pnr.', title='FinalizeIntent')


class Flight(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    departureDateTime: DateTimeLocal = Field(
        ..., description='Departure date time of flight'
    )
    arrivalDateTime: DateTimeLocal = Field(
        ..., description='Arrival date time of flight'
    )
    duration: Duration | None = Field(None, description='Flight duration')
    flightId: str | None = Field(
        None,
        description='Unique identifier for a flight.',
        examples=['CjoKOGNsYXNzIERhdGVUaW1lTG9jYWwgewogICAgaXNvO'],
    )
    origin: constr(pattern=r'^[A-Z]{3}$') = Field(
        ..., description='3 letter IATA airport code for origin', examples=['SFO']
    )
    destination: constr(pattern=r'^[A-Z]{3}$') = Field(
        ..., description='3 letter IATA airport code for destination', examples=['LHR']
    )
    departureGate: Gate | None = Field(None, description='Departure gate and terminal')
    arrivalGate: Gate | None = Field(None, description='Arrival gate and terminal')
    marketing: FlightNumber = Field(..., description='Marketing flight number')
    operating: FlightNumber = Field(..., description='Operating flight number')
    operatingAirlineName: str | None = Field(
        None,
        description='Free text operating airline name that has to be displayed to the user if present\n',
        examples=['SKYWEST DBA UNITED EXPRESS'],
    )
    hiddenStops: Sequence[HiddenStop] | None = Field(
        None, description='Stops for refueling or getting more passengers'
    )
    vendorConfirmationNumber: str | None = Field(
        None, description='Flight confirmation number', examples=['ABQTEJ']
    )
    cabin: Cabin | None = Field(None, description='Flight cabin')
    bookingCode: str | None = Field(
        None, description='Flight Booking Code', examples=['A']
    )
    flightStatus: PnrStatus | None = Field(None, description='Status of flight')
    otherStatuses: Sequence[OtherStatus] | None = Field(
        None,
        description='Details about any duplicate flight segment. Cabin code and status of other duplicate \nsegments.\n',
    )
    co2EmissionDetail: CO2EmissionDetail | None = Field(
        None, description='CO2 emission info for this flight'
    )
    restrictions: Sequence[FlightRestrictions] | None = Field(
        None, description='Restrictions such as seat booking, loyalty etc'
    )
    flightUpdates: FlightUpdates | None = None
    sourceStatus: str | None = Field(
        None, description='Flight status code from supplier'
    )
    equipment: Equipment | None = None
    distance: Length | None = None
    flightWaiverCodes: Sequence[FlightWaiverCode] | None = Field(
        None, description='List of waiver codes for flight'
    )
    amenities: Sequence[AirAmenity] | None = Field(
        None, description='The amenities associated with the flight.'
    )
    flightIndex: int | None = Field(
        None, description='Index of flight in the leg', examples=[0]
    )


class FlightCredits(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalCreditAmount: FareAmount = Field(..., description='Amount of flight credits.')
    changePenalty: FareAmount = Field(
        ..., description='Change penalty applicable for using this credit.'
    )
    netCreditAmount: FareAmount = Field(
        ..., description='Net credit amount (total credit amount - change penalty)'
    )


class GetApprovalDataV3Request(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paginationRequestParams: OffsetBasedPaginationRequestParams | None = None
    includePnrSummaries: bool | None = Field(
        None,
        description='Boolean flag to include PNR summaries in the response',
        examples=[True],
    )
    approvalFilters: Sequence[ApprovalFilterV2] | None = Field(
        None, description='Filter for the approval data list request.'
    )


class HotelBookingGuideline(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    allowedHotels: Sequence[HotelInfo] | None = Field(
        None, description='List of allowed Hotel details for the event'
    )
    checkinBookingWindow: EventBookingWindow | None = None
    checkoutBookingWindow: EventBookingWindow | None = None
    paymentGuidelines: HotelBookingPaymentGuidelines | None = None


class HotelBookingGuidelineWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelBookingGuideLine: HotelBookingGuideline | None = None


class HotelNightlyRate(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    total: Money | None = Field(None, description='Nightly total rate.')
    base: Money | None = Field(None, description='Nightly base rate.')
    tax: Money | None = Field(None, description='Nightly tax rate.')


class HotelPnrSummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str | None = Field(None, description='Name of hotel', examples=['Marriot'])
    city: str | None = Field(None, description='Name of city', examples=['Bangalore'])
    totalAmount: Money | None = None
    fromDate: DateTimeLocal | None = None
    toDate: DateTimeLocal | None = None
    pnrStatus: PnrStatus | None = None
    address: PostalAddress | None = None


class HotelPnrSummaryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelPnrSummary: HotelPnrSummary | None = None


class KeywordWithReasonListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    keywordWithReasonList: KeywordsWithReasonList | None = None


class LLFPnrInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalFare: Money


class FareOffer(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId = Field(
        ..., description='User ID of traveler to which this fare offer applies'
    )
    baggagePolicy: BaggagePolicy | None = None
    cancellationPolicy: CancellationPolicy1 | None = None
    exchangePolicy: ExchangePolicy | None = None


class Leg(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flights: Sequence[Flight] = Field(
        ..., description='List of flights in this leg', min_length=1
    )
    brandName: str | None = Field(None, description='Brand name', examples=['BUSFLEX'])
    validatingAirlineCode: str | None = Field(
        None,
        description='2 letter IATA airline code for validating airline',
        examples=['AA'],
    )
    legStatus: UserFacingStatus | None = Field(
        None,
        description='The leg status, which is supposed to be displayed to the end user.',
    )
    sortingPriority: int | None = Field(
        None,
        description='This field sets the sorting priority of the leg to determine its order of display on the\ntrips page\n',
        examples=[1],
    )
    travelerRestrictions: Sequence[TravelerRestriction] | None = Field(
        None,
        description='Per traveler list of restrictions such as loyalty, ktn edit etc.',
    )
    fareOffers: Sequence[FareOffer] | None = Field(
        None,
        description='Fare offers for this leg including baggage, cancellation policy, etc',
    )
    legId: str | None = Field(None, description='Identifier for leg', examples=['1'])
    rateType: RateTypeModel | None = Field(
        None, description='Type of fare booked for this leg'
    )
    legUpdates: LegUpdates | None = None
    preferredTypes: Sequence[PreferredType] | None = Field(
        None,
        description='Preference types for a leg, like org preferred, spotters choice, etc.',
    )
    preferences: Sequence[Preference] | None = Field(
        None,
        description='Preference info for a leg, like org preferred, spotters choice, etc.',
    )
    scheduleChangeInfo: ScheduleChangeInfo | None = None
    legIndex: int | None = Field(
        None, description='Index of leg in the booking', examples=[0]
    )


class LegInfoRail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    arrivalDateTime: DateTimeLocal | None = Field(
        None, description='Date time of arrival.'
    )
    departureDateTime: DateTimeLocal | None = Field(
        None, description='Date time of departure.'
    )
    originInfo: RailStationInfo | None = Field(None, description='Origin station info.')
    destinationInfo: RailStationInfo | None = Field(
        None, description='Destination station info.'
    )


class LimoPnrSummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vehicleName: str | None = Field(
        None, description='Name of vehicle', examples=['mercedez']
    )
    vendor: Vendor | None = None
    legs: Sequence[LimoLegSummary] | None = None
    totalAmount: Money | None = None
    pnrStatus: PnrStatus | None = None


class LimoPnrSummaryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    limoPnrSummary: LimoPnrSummary | None = None


class LuggageInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legIdx: conint(ge=0) | None = Field(
        None,
        description='Index of leg to which this luggage belongs. If -1, belongs to all legs',
        examples=[0],
    )
    amount: Money = Field(..., description='Total price')
    weightKg: conint(ge=0) = Field(
        ..., description='Total weight in Kgs', examples=[40]
    )
    numBags: conint(ge=0) | None = Field(
        None, description='Number of bags.', examples=[2]
    )
    status: Status2 | None = Field(
        None, description='Status of baggage', examples=['CONFIRMED']
    )


class MiscPnrSummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str = Field(
        ..., description='Description of the segment', examples=['MiscellaneousPnr']
    )
    startDateTime: DateTimeLocal
    endDateTime: DateTimeLocal
    totalAmount: Money | None = None
    status: PnrStatus | None = None
    sortingPriority: int | None = Field(
        None, description='Sorting priority of the segment', examples=[1]
    )


class MiscPnrSummaryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    miscPnrSummary: MiscPnrSummary | None = None


class OptionInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    source: OptionSource
    sourceMetadata: OptionSourceMetadata | None = None
    totalNumOptions: int | None = Field(None, description='Total number of options')
    options: Sequence[Option] | None = Field(
        None,
        description='Available options for the question. This will contain only max 10 options if only \nsummary is requested.\n',
    )


class OrgTripInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tripId: str = Field(..., description='Spotnana trip ID', examples=['6926658168'])
    pnrInfos: Sequence[PnrInfo] = Field(
        ..., description='Pnr Info for the organisation trips'
    )
    tripName: str | None = Field(
        None, description='Trip Name', examples=['SFO - DEL Meeting']
    )
    tripDescription: str | None = Field(
        None, description='Trip Description', examples=['SFO-DEL 22-04-2022']
    )
    applicationId: UUID | None = Field(
        None, description='Application used while creating trip.'
    )


class OriginalTicketDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ticketNumber: str = Field(
        ...,
        description='Ticket number of the original ticket.',
        examples=['*********0'],
    )
    amount: FareAmount | None = Field(
        None, description='Face value of the original ticket.'
    )
    taxBreakdown: PnrTaxBreakdown | None = Field(
        None, description='Tax breakdown of the original ticket.'
    )
    conjunctionTicketSuffix: str | None = Field(
        None, description='Conjunction ticket suffix.', examples=['A']
    )
    cogsAmount: FareAmount | None = Field(
        None, description='The cost amount of original ticket'
    )
    cogsRefund: FareAmount | None = Field(
        None, description='COGS refund in original ticket as part of this exchange'
    )
    ctcRefund: FareAmount | None = Field(
        None, description='CTC refund in original ticket as part of this exchange'
    )


class OtherAncillaryFare(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: AncillaryType
    totalFare: FareAmount = Field(..., description='Total fare for this ancillary')


class PaymentInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    applicableTo: Sequence[ApplicableToEnum] | None = None
    card: Card
    accessType: CreditCardAccessType | None = None
    access: CreditCardAccess | None = None


class PaymentMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    customPaymentMethodMetadata: CustomPaymentMethodMetadata1 | None = Field(
        None, description='Metadata related to custom payment method'
    )
    vendorProgramPaymentMetadata: VendorProgramPaymentMetadata2 | None = None
    virtualCardMetadata: VirtualCardPaymentMetadata | None = None
    cardMetadata: CardMetadata2 | None = None


class PnrSummaryWrapper(
    RootModel[
        AirPnrSummaryWrapper
        | RailPnrSummaryWrapper
        | HotelPnrSummaryWrapper
        | CarPnrSummaryWrapper
        | LimoPnrSummaryWrapper
        | MiscPnrSummaryWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        AirPnrSummaryWrapper
        | RailPnrSummaryWrapper
        | HotelPnrSummaryWrapper
        | CarPnrSummaryWrapper
        | LimoPnrSummaryWrapper
        | MiscPnrSummaryWrapper
    ) = Field(..., description='Approval filter', title='ApprovalFilter')


class PolicyConstValue(
    RootModel[
        Int32Wrapper
        | Int64Wrapper
        | StringWrapper
        | DoubleWrapper
        | BoolWrapper
        | IntListWrapper
        | DoubleListWrapper
        | StringListWrapper
        | MoneyWrapper
        | LengthWrapper
        | PostalAddressWrapper
        | UserOrgIdWrapper
        | LegalEntityIdWrapper
        | OfficeIdWrapper
        | UserOrgIdListWrapper
        | LegalEntityIdListWrapper
        | OfficeIdListWrapper
        | RatingWrapper
        | PercentageWrapper
        | Int32RangeWrapper
        | DoubleRangeWrapper
        | PersonaWrapper
        | PersonaListWrapper
        | TravelClassHierarchyWrapper
        | KeywordWithReasonListWrapper
        | WorkerTypeWrapper
        | WorkerTypeListWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        Int32Wrapper
        | Int64Wrapper
        | StringWrapper
        | DoubleWrapper
        | BoolWrapper
        | IntListWrapper
        | DoubleListWrapper
        | StringListWrapper
        | MoneyWrapper
        | LengthWrapper
        | PostalAddressWrapper
        | UserOrgIdWrapper
        | LegalEntityIdWrapper
        | OfficeIdWrapper
        | UserOrgIdListWrapper
        | LegalEntityIdListWrapper
        | OfficeIdListWrapper
        | RatingWrapper
        | PercentageWrapper
        | Int32RangeWrapper
        | DoubleRangeWrapper
        | PersonaWrapper
        | PersonaListWrapper
        | TravelClassHierarchyWrapper
        | KeywordWithReasonListWrapper
        | WorkerTypeWrapper
        | WorkerTypeListWrapper
    )


class PolicyTakeApprovalAction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numHierarchyLevels: int | None = Field(
        None,
        description="How many levels of hierarchy should approval be taken? If it's just immediate manager, \nthis value would be 1. If it's manager and their manager, this would ge 2 and so on.\n",
    )
    positionTitles: Sequence[str] | None = Field(
        None,
        description='What position in the cost center or department. For eg, any business class upgrade \nmight require VP approval.\n',
    )
    userOrgIds: Sequence[UserOrgId] | None = Field(
        None,
        description='The specific users from whom to take approval. For eg, say for a company, all approvals\ngo through Adam.\n',
    )
    allRequired: bool | None = Field(
        None,
        description='This tells whether all the people above needs to approve or if any of these approve, \nit is sufficient.\n',
    )
    hardApprovalRequired: bool | None = Field(
        None,
        description='Whether to take soft approval (false) or hard approval (true).',
    )


class PolicyTakeApprovalActionWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    takeApproval: PolicyTakeApprovalAction | None = None


class PolicyViolationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    predicateString: str | None = None
    predicate: PolicyPredicate | None = None
    expectedValue: PolicyConstValue | None = None
    actualValue: PolicyConstValue | None = None


class Question(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID
    name: str = Field(
        ...,
        description="Question display name that the user will see. For eg, 'Choose the purpose of your trip'.",
    )
    questionFormat: QuestionFormat | None = None
    optionInfo: OptionInfo | None = None
    isRequired: bool = Field(
        ...,
        description='Whether its compulsory to answer the question or not.',
        examples=[True],
    )
    isDisabled: bool = Field(
        ...,
        description='Whether the question is disabled or not. If true, this should not be asked.',
        examples=[True],
    )
    includeInItinerary: bool | None = Field(
        False,
        description='Whether to include this question in the itinerary related emails.',
        examples=[True],
    )
    customFieldLocations: Sequence[CustomFieldLocation] | None = None
    matchConditions: CustomFieldMatchConditions | None = None
    questionType: QuestionType | None = None


class RailAncillary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelCard: RailTravelCard | None = Field(
        None, description='Details of the travel cards.'
    )
    type: Literal['TRAVEL_CARD'] = Field(
        'TRAVEL_CARD', description='Type of the ancillary', examples=['TRAVEL_CARD']
    )


class RailItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    itemType: Literal['RAIL_ITEM'] = 'RAIL_ITEM'
    legInfos: Sequence[LegInfoRail] | None = None
    ancillaries: Sequence[RailAncillary] | None = None


class RailItemNameMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    railNameInvoiceMetadata: Sequence[RailNameInvoiceMetadata] | None = Field(
        None, description='RailNameInvoiceMetadata information.'
    )
    pnrType: Literal['RAIL'] = Field('RAIL', examples=['RAIL'])


class RailPnrLegInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    allocatedSpaces: Sequence[AllocatedSpace] | None = Field(
        None, description='Space allocated for each passenger.'
    )
    travelerRailInfo: Sequence[TravelerRailInfo] | None = None
    amenities: Sequence[RailPnrAmenity] | None = Field(
        None, description='List of amenities available for the leg.'
    )
    arriveAt: DateTimeOffset | None = Field(
        None, description='Date and time of arrival of the leg.'
    )
    departAt: DateTimeOffset | None = Field(
        None, description='Date and time of departure of the leg.'
    )
    arriveAtLocal: DateTimeLocal | None = Field(
        None,
        description='Date and time of arrival of the leg in local time of the arrival station.',
    )
    departAtLocal: DateTimeLocal | None = Field(
        None,
        description='Date and time of departure of the leg in local time of the departure station.',
    )
    destination: str = Field(
        ...,
        description='Destination station name of the leg.',
        examples=['Liverpool Lime Street'],
    )
    distance: Length | None = Field(None, description='Distance covered by the leg.')
    duration: Duration | None = Field(None, description='Duration of the leg.')
    fareType: str = Field(
        ..., description='Details of fare for this leg.', examples=['Anytime Return']
    )
    origin: str = Field(
        ...,
        description='Originating station name of the leg.',
        examples=['London Euston'],
    )
    travelClass: RailTravelClass | None = Field(
        None, description='Travel class for the leg.'
    )
    vehicle: RailVehicle = Field(..., description='Vehicle used in the leg.')
    originInfo: RailStationInfo | None = Field(
        None, description='Information about origin station.'
    )
    destinationInfo: RailStationInfo | None = Field(
        None, description='Information about destination station.'
    )
    railFareType: RailFareType | None = Field(
        None, description='Captures Cancellation policy and Fair Rules'
    )
    ticketNumber: str | None = Field(
        None, description='ticket number', examples=['*********']
    )
    carrierConfirmationNumber: str | None = Field(
        None,
        description='carrier confirmation number for a segment',
        examples=['QP123AF'],
    )
    seatPreferenceSelection: RailSeatPreferenceSelection | None = Field(
        None, description='Selected Seat Preferences for the leg.'
    )
    legId: str | None = Field(
        None, description='Leg Id.', examples=['9a766905-b5e9-43f7-b985-0738ff692324']
    )
    vendorName: str | None = Field(
        None,
        description='Name of the vendor for which current leg is reserved',
        examples=['ATOC'],
    )
    co2EmissionGramsPerPassenger: int | None = Field(
        None,
        description='CO2 emission for the current leg in gram for a single passenger.',
        examples=[10903],
    )


class RailPnrPassengerInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userOrgId: UserOrgId | None = Field(None, description='User organization ID.')
    passengerType: RailPassengerType | None = Field(
        None, description='Type of passenger.'
    )


class RailPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preferredRailStations: Sequence[PreferredRailStation] | None = Field(
        None, description='A list of user preferred rail stations.'
    )
    seatPreference: SeatPref | None = None
    travelClasses: Sequence[RailTravelClass] | None = Field(
        None, description='A list of class of service for rail.'
    )
    coachPreferences: Sequence[CoachPref] | None = Field(
        None, description='A list of coach preference for rail.'
    )
    conditionalRates: Sequence[ConditionalRate] | None = Field(
        None, description='A list of conditional rates for rail.'
    )


class RailRefundRateInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalFare: FareAmount = Field(..., description='Total fare.')
    nonRefundableFare: FareAmount | None = Field(
        None, description='Non refundable fare.'
    )
    cancellationFee: Money | None = Field(None, description='Cancellation fee.')
    refund: Money | None = Field(None, description='Total refund amount.')
    voucher: RailRefundVoucher | None = Field(
        None, description='Refund voucher details'
    )


class RateMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    negotiatedRateType: NegotiatedRateType | None = Field(
        None, examples=['CORPORATE_RATE']
    )
    publishedRate: FareAmount | None = Field(
        None, description='the non negotiated rate or the market rate.'
    )
    tmcNegotiatedRate: FareAmount | None = Field(
        None, description='the tmc negotiated rate.'
    )
    corporateNegotiatedRate: FareAmount | None = Field(
        None, description='the corporate negotiated rate.'
    )


class RawPaymentSourceDetails(RootModel[Dpan | Credit]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Dpan | Credit = Field(
        ...,
        description='Raw Details of the Payment Source',
        discriminator='type',
        title='RawPaymentSourceDetails',
    )


class RequestedByDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrRequestedBy: str = Field(
        ..., description='Name of the requester', examples=['Peter']
    )
    requestedByUserOrgId: UserOrgId | None = None


class SavingsFare(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fareAmount: FareAmount = Field(
        ..., description='Amount saved with base and tax breakup.'
    )
    isTaxIncluded: bool | None = False


class SelectedPaymentSource(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentSourceId: UUID | None = Field(
        None,
        description='Unique identifier identifying this payment source.',
        examples=['f49d00fe-1eda-4304-ba79-a980f565281d'],
    )
    rawPaymentSource: RawPaymentSourceDetails | None = None
    postPaymentRedirectionUrl: str | None = Field(
        None,
        description='Url for post payment redirection if payment source navigates user to a third party url',
        examples=[
            'https://mycompany.com/checkout?paymentSourceId=f49d00fe-1eda-4304-ba79-a980f565281d'
        ],
    )
    cvv: str | None = Field(
        None, description='CVV associated with associated payment source, if any.'
    )
    amount: Money | None = Field(
        None, description='Total amount to be charged for specified payment items.'
    )


class SimpleRailPnrInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    sections: Sequence[SimpleSectionInfo] = Field(
        ..., description='The List of sections covered in the itinerary.'
    )
    ticketReferences: Sequence[RailTicketAsset] = Field(
        ...,
        description='Containing details about the content and how to get the ticket. e.g. for eticket, it will\ncontain details of the path where pdf/apple pk pass is stored. For collection ticket, \nit will contain the reference number of the ticket.\n',
    )


class StatisticsItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    statisticType: StatisticType
    totalFare: FareAmount


class Ancillary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ancillaryType: AncillaryType1 = Field(
        ..., description='Type of ancillary', examples=['SEAT']
    )
    totalFare: FareAmount = Field(..., description='Total fare of this ancillary')
    legIndex: conint(ge=-1) = Field(
        ...,
        description='Index of leg to which this ancillary belongs. If an ancillary belongs to all legs, this index should be set to -1',
        examples=[0],
    )
    flightIndex: conint(ge=-1) = Field(
        ...,
        description="Index of flight in it's leg to which this ancillary belongs. If an ancillary belongs to all flights, this index should be set to -1",
        examples=[0],
    )


class ExchangeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    originalTicketNumber: str = Field(
        ...,
        description='If the ticket was created as part of an exchange, the original ticket that was exchanged to create this ticket.',
        examples=['0017574214321'],
    )
    penalty: Money | None = Field(None, description='Exchange penalty')
    addCollectAmount: FareAmount | None = Field(
        None,
        description='Extra amount the client was charged if the ticket was created as part of an exchange.',
    )
    originalTicketDetails: OriginalTicketDetails | None = Field(
        None,
        description='If the ticket was created as part of an exchange, complete details of the original ticket that was exchanged to create this ticket.',
    )
    residueType: ResidueType | None = Field(None, description='Type of residue ticket')
    addCollectTaxBreakdown: PnrTaxBreakdown | None = Field(
        None, description='Tax breakdown information for the add collect amount'
    )


class RefundInfo1(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    refundAmount: FareAmount = Field(
        ...,
        description='The amount refunded back to the client if the ticket was refunded',
    )
    refundDate: DateModel | None = Field(
        None, description='The date when the refund was issued'
    )
    penalty: FareAmount | None = Field(None, description='Refund penalty')
    refundTaxBreakdown: PnrTaxBreakdown | None = Field(
        None, description='Tax breakdown for the refund amount'
    )


class CostItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentId: str = Field(
        ..., description='The payment id for this transaction as present in PNR CTC'
    )
    amount: FareAmount = Field(
        ..., description='Total amount of the ticket paid as part of this transaction'
    )
    isRefunded: bool | None = Field(
        None,
        description='Indicates whether this amount was charged or refunded to the customer',
    )


class TicketCtc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cost: Sequence[CostItem]


class TravelPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airPref: AirPref | None = None
    preferredCurrency: str | None = Field(None, examples=['USD'])
    railCards: Sequence[RailCard] | None = None
    railPref: RailPref | None = None
    carPref: CarPref | None = None
    hotelPref: HotelPref | None = None


class TravelerPersonalInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    loyaltyInfos: Sequence[LoyaltyInfo] | None = None
    travelPref: TravelPref | None = None


class TripBudgetInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    budgetName: str = Field(..., description='Name of the budget')
    budgetRemainingBalance: SimpleMoney | None = Field(
        None,
        description='Remaining balance of the budget. It will be present only if the user has role agent and \nabove.\n',
    )
    isPersonalCardAllowed: bool | None = Field(
        False,
        description='Whether the personal card is allowed for the trip.',
        examples=[True],
    )
    isBudgetMasked: bool | None = Field(
        False, description='Whether the budget is masked.', examples=[False]
    )
    budgetRemainingBalanceFormatted: str | None = Field(
        None,
        description='Remaining balance of the budget in formatted string.',
        examples=['$1000.00'],
    )
    externalCardDetails: Card | None = Field(
        None, description='Details of the external card.'
    )
    isRewardPointsAllowed: bool | None = Field(
        False,
        description='Whether the reward points are allowed on the budget.',
        examples=[True],
    )
    rewardPoints: float | None = Field(
        None, description='Available reward point balance.', examples=[5050]
    )
    rewardPointsValueUsd: float | None = Field(
        None, description='Available reward points value in USD.', examples=[50.5]
    )


class TripBudgetInfoWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tripBudgetInfo: TripBudgetInfo | None = None


class TripFilters(
    RootModel[
        TripUserIdFilter
        | TripIdFilter
        | CompanyIdFilter
        | TripTypeFilter
        | TripTravelTypeFilter
        | PolicyStatusFilter
        | TripStartDateRangeFilter
        | TripEndDateRangeFilter
        | OverallStatusFilter
        | TripDateTimeRangeFilter
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        TripUserIdFilter
        | TripIdFilter
        | CompanyIdFilter
        | TripTypeFilter
        | TripTravelTypeFilter
        | PolicyStatusFilter
        | TripStartDateRangeFilter
        | TripEndDateRangeFilter
        | OverallStatusFilter
        | TripDateTimeRangeFilter
    ) = Field(
        ...,
        description='Filter parameters for the list trip summary request.',
        discriminator='filterType',
    )


class TripPartnerInfoDetails(RootModel[TripBudgetInfoWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: TripBudgetInfoWrapper = Field(
        ...,
        description='OneOf trip partner info Details',
        title='TripPartnerInfoDetailsWrapper',
    )


class TripPaymentInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalFare: Money = Field(
        ...,
        description='It is net cost of the trip in form of money. It also includes service fee if visibleToTraveler is true. It does not include refunded amount if some bookings were cancelled and includes only net amount of trip.',
    )
    totalFareAmount: FareAmount | None = Field(
        None,
        description='It is Total cost of trip in form of FareAmount with base and tax. It does not include service fee.',
    )
    serviceFeeFareAmount: FareAmount | None = Field(
        None, description='The service fees fare amount of this trip.'
    )


class UATPInformation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    card: Card
    ticketingValidity: DateTimeRange = Field(
        ..., description='Valid ticketing datetime.'
    )
    travelValidity: DateTimeRange = Field(..., description='Valid travel datetime.')


class UATPMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    card: Card


class UATPMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    uatpMetadata: UATPMetadata | None = None


class User(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    addresses: Sequence[PostalAddress] | None = None
    dob: DateModel | None = None
    email: EmailStr | None = Field(None, examples=['<EMAIL>'])
    emergencyContactInfo: EmergencyContactInfo | None = None
    emergencyContact: EmergencyContact | None = None
    gender: Gender | None = None
    identityDocs: Sequence[IdentityDocument] | None = Field(
        None,
        description='List of user identity documents.',
        examples=[
            [
                {
                    'passport': {
                        'docId': 'PASSPORTID',
                        'expiryDate': {'iso8601': '2017-07-21'},
                        'issueCountry': 'IN',
                        'issuedDate': {'iso8601': '2017-07-21'},
                        'nationalityCountry': 'IN',
                        'type': 'REGULAR',
                    }
                },
                {'ktn': {'number': '123456', 'issueCountry': 'US'}},
            ]
        ],
    )
    name: Name | None = None
    paymentInfos: Sequence[PaymentInfo] | None = None
    phoneNumbers: Sequence[PhoneNumber] | None = None
    profilePicture: Image | None = None
    nationality: str | None = Field(
        None, description='Nationality of user', examples=['Indian']
    )
    title: UserTitle | None = None


class UserBasicInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userOrgId: UserOrgId | None = None
    persona: Persona | None = None
    email: EmailStr | None = Field(None, examples=['<EMAIL>'])
    name: Name | None = None
    profilePicture: Image | None = None
    tier: Tier | None = 'BASIC'
    phoneNumber: PhoneNumber | None = None
    employeeId: str | None = Field(None, description='Employee id of the user')
    isActive: bool | None = Field(
        None, description='Whether user profile is active or not.', examples=[True]
    )


class UserBusinessInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    department: Department | None = None
    designation: str | None = Field(None, examples=['MANAGER'])
    email: EmailStr = Field(..., examples=['<EMAIL>'])
    employeeId: str = Field(
        ...,
        description="Unique employee id. Can use email if a company don't use employee ids.",
        examples=['101'],
    )
    grade: Grade | None = None
    legalEntityId: LegalEntityId
    managerBasicInfo: UserBasicInfo | None = None
    office: Office | None = None
    organizationId: OrganizationId
    phoneNumbers: Sequence[PhoneNumber] | None = None
    userCostCenter: CostCenter | None = None
    designatedApproverInfos: Sequence[UserBasicInfo] | None = Field(
        None, description='A list of user basic info for designated approvers.'
    )
    designatedApproverUserIds: Sequence[UserId] | None = Field(
        None, description='A list of userId for designated approvers.'
    )
    authorizerEmail: str | None = Field(
        None,
        description='Email address to be used as approval authorizer, when a manager is not present.',
        examples=['<EMAIL>'],
    )


class UserPersonalInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    addresses: Sequence[PostalAddress] | None = None
    dob: DateModel | None = None
    email: EmailStr | None = Field(None, examples=['<EMAIL>'])
    emergencyContactInfo: EmergencyContactInfo | None = None
    gender: Gender | None = None
    identityDocs: Sequence[IdentityDocument] | None = Field(
        None,
        description='List of user identity documents.',
        examples=[
            [
                {
                    'passport': {
                        'docId': 'PASSPORTID',
                        'expiryDate': {'iso8601': '2017-07-21'},
                        'issueCountry': 'IN',
                        'issuedDate': {'iso8601': '2017-07-21'},
                        'nationalityCountry': 'IN',
                        'type': 'REGULAR',
                    }
                },
                {'ktn': {'number': '123456', 'issueCountry': 'US'}},
            ]
        ],
    )
    name: Name | None = None
    phoneNumbers: Sequence[PhoneNumber] | None = None
    profilePicture: Image | None = None
    nationality: str | None = Field(
        None, description='Nationality of user', examples=['Indian']
    )
    title: UserTitle | None = None
    preferredLanguage: str | None = Field(
        None, description='Language preferred by user.', examples=['en-US']
    )
    preferredPronoun: PreferredPronoun | None = Field(
        None, description='Pronoun preferred by user.'
    )
    travelerName: Name | None = Field(
        None, description='A name of user that does not contain special characters.'
    )
    emergencyContact: EmergencyContact | None = None


class AirFareInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalFare: FareAmount = Field(..., description='Total fare charged.')
    usedFare: FareAmount = Field(..., description='Used fare component.')
    nonRefundableFare: FareAmount = Field(..., description='Non refundable fare.')
    cancellationFee: FareAmount = Field(..., description='Cancellation fee.')
    merchantFee: Money = Field(..., description='Non refundable merchant fee.')
    refund: FareAmount = Field(..., description='Total refund amount.')
    flightCredits: FlightCredits | None = Field(
        None,
        description='Flight credits info for the cases where the ticket can be converted to unused credits\n',
    )


class AppliedCreditInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    creditId: str = Field(
        ...,
        description='Unique ID of credit applied. For example, numeric ID of unused ticket.',
        examples=['0017574214321'],
    )
    creditAmount: FareAmount | None = Field(
        None, description='Total credit amount available.'
    )
    fareDifference: Money | None = Field(
        None,
        description='Difference between the selected fare and credit available.\nIf the fare is greater than credit available, value of this field will be positive (that amount will be charged to payment method).\nIf the fare is less than credit available, value of this field will be negative (credit balance will remain).\n',
    )
    penalty: Money | None = Field(
        None,
        description='Penalty amount applied to redeem credit. This could be a change fee applied when redeeming.',
    )


class ApproverInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    basicInfo: UserBasicInfo | None = None


class SupportedCancellation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cancelType: CancelType = Field(
        ..., description='Type of cancellation', title='CancelType'
    )
    maxCancellationDateTime: DateTimeOffset | None = Field(
        None, description='Max date time till when this cancellation is supported'
    )
    totalFare: FareAmount = Field(..., description='Total booking fare')
    penalty: Money | None = Field(None, description='Total penalty')
    merchantFee: Money | None = Field(None, description='Non refundable merchant fee')
    refund: Money = Field(
        ..., description='Total refund (totalFare - penalty - merchantFee)'
    )


class AutomatedCancellationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    supportedCancellations: Sequence[SupportedCancellation] | None = Field(
        None,
        description='Supported automated cancellations. If this list is empty, automated cancellation is not \nsupported for this booking\n',
    )


class OtherAncillary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: AncillaryType
    legIndex: AncillaryLegIndex | None = None
    flightIndex: AncillaryFlightIndex | None = None
    fare: FareAmount | None = Field(None, description='Fare for this ancillary')
    status: Status = Field(
        ..., description='Status of this ancillary', examples=['CONFIRMED']
    )
    flightIds: Sequence[AncillaryFlightId]


class FlightFareBreakupItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legIndices: Sequence[int] | None = Field(
        None, description='List of legs for this fare info'
    )
    flightsFare: FareAmount | None = Field(
        None, description='Total flight fare for the above legs'
    )


class Itinerary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalFare: FareAmount | None = Field(
        None,
        description='Total itinerary fare including flights, ancillaries and extras. This does not \ninclude service fee\n',
    )
    totalFlightsFare: FareAmount | None = Field(None, description='Total flights fare')
    totalSeatFare: Money | None = Field(None, description='Total fare for seats')
    totalLuggageFare: Money | None = Field(None, description='Total fare for luggage')
    totalMerchantFees: Money | None = Field(None, description='Total merchant fees')
    totalAirlineFees: Money | None = Field(
        None, description='Total card or sevice fee charged by airline'
    )
    otherCharges: Money | None = Field(
        None, description='Charges other than flight, seat and luggage'
    )
    flightFareBreakup: Sequence[FlightFareBreakupItem] | None = Field(
        None, description='Ticket level flight fare breakup'
    )
    fareComponents: Sequence[FareComponentDetail] | None = Field(
        None, description='List of fare component details for this itinerary'
    )
    otherAncillaryFares: Sequence[OtherAncillaryFare] | None = Field(
        None, description='Fare breakup for ancillaries other than seat and luggage'
    )


class Booking(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    seats: Sequence[Seat] | None = Field(
        None, description='Seat booking info', title='Seat'
    )
    luggageDetails: Sequence[LuggageInfo] | None = Field(
        None, description='Additional luggage info'
    )
    otherAncillaries: Sequence[OtherAncillary] | None = Field(
        None, description='Ancillaries other than seat and luggage'
    )
    itinerary: Itinerary | None = Field(
        None, description='Info about booked itinerary', title='Itinerary'
    )
    otherCharges: Sequence[OtherCharge] | None = Field(
        None,
        description='Extra charges over flight fare, seat and luggage.',
        title='Other',
    )


class RefundInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    refundAmount: FareAmount = Field(..., description='Amount refunded if cancelled')
    refundTaxAmount: FareAmount | None = Field(
        None, description='Amount of tax refunded if cancelled'
    )
    refundDate: DateModel | None = Field(None, description='Refund transaction date')
    penalty: FareAmount | None = Field(None, description='Refund penalty')
    refundVoucher: Voucher | None = Field(None, description='Refund voucher details.')


class Cost(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    base: Money = Field(..., description='Base Amount.')
    extras: Sequence[Extra] | None = Field(None, description='List of extras.')
    refund: Money | None = Field(None, description='Refund Amount.')
    tax: Money = Field(..., description='Tax amount.')
    commission: Commission | None = Field(None, description='Commission')
    includesCommission: bool | None = Field(
        None, description='Whether the rate includes commission', examples=[False]
    )
    taxBreakdown: TaxBreakdownModel | None = Field(None, description='Tax breakdown')
    transactionDate: DateModel | None = Field(None, description='Transaction date')
    refundInfo: RefundInfo | None = Field(None, description='Refund info')


class CreatedMco(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airline: str | None = Field(
        None, description='Code of the airline for which mco is created.'
    )
    fare: FareAmount | None = None
    mcoNumber: str | None = Field(None, description='Number of the mco created.')
    issuedDate: DateTimeLocal | None = None
    isMcoVoided: bool | None = Field(
        None, description='Whether the MCO was voided or not', examples=[False]
    )


class CustomFieldResponsesPerEntity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    entity: EntityV3
    selectedResponses: Sequence[CustomFieldV3Response] = Field(
        ...,
        description='The list of responses that are selected by user or auto populated.',
    )


class DocumentMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    documentType: DocumentType
    entityType: EntityType
    entityId: str = Field(
        ..., description='Entity Id for the given entity type.', examples=['123124']
    )
    entityMetadata: EntityMetadata
    name: str = Field(..., description='Document name.', examples=['BoardingPass.pdf'])


class EntityAnswer(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    entityId: str | None = Field(None, description='The unique ID for the question.')
    userInput: str | None = Field(
        None, description='The text input given by user (if any).'
    )
    itemIds: Sequence[int] | None = Field(
        None,
        description='The id/enum value corresponding to the option chosen by the user as\nanswer.\n',
    )
    answers: Sequence[AnswerPair] | None = None
    customFieldType: CustomFieldType | None = 'QUESTION'
    questionDisplayText: str | None = Field(
        None, description='The question text to be displayed to the user.'
    )
    question: Question | None = None


class EventBookingGuideline2(HotelBookingGuidelineWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    numGuestsAllowed: int | None = Field(
        None,
        description='Number of guests allowed to be booked for this booking',
        examples=[1],
    )


class EventBookingGuideline(
    RootModel[
        EventBookingGuideline1
        | EventBookingGuideline2
        | EventBookingGuideline3
        | EventBookingGuideline4
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        EventBookingGuideline1
        | EventBookingGuideline2
        | EventBookingGuideline3
        | EventBookingGuideline4
    ) = Field(
        ...,
        description='Booking details allowed for the event',
        title='EventBookingGuideline',
    )


class FareStatistics(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    statisticsItems: Sequence[StatisticsItem]


class FormOfPayment(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type1 = Field(
        ..., description='Type of payment used', examples=['CARD'], title='PaymentType'
    )
    card: Card | None = Field(
        None,
        description="The payment card to be used to charge customer. This is only set if the payment type is 'CARD'",
    )
    additionalInfo: str | None = Field(
        None, description="Additional info to be added if payment type is 'UNKNOWN'."
    )
    accessType: CreditCardAccess | None = None
    paymentMethod: PaymentMethod | None = Field(
        None, description='Payment method used to pay for this transaction'
    )
    paymentMetadata: PaymentMetadata | None = None
    paymentSourceType: PaymentSourceType | None = None


class GetTripSummariesV3Request(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paginationRequestParams: OffsetBasedPaginationRequestParams
    includePnrSummaries: bool | None = Field(
        None,
        description='Boolean flag to include PNR summaries in the response',
        examples=[True],
    )
    tripFilters: Sequence[TripFilters] | None = Field(
        None, description='Filter for the list trip summary request.'
    )
    sortOptions: Sequence[TripSortOptions] | None = Field(
        None, description='Sort options for the list trip summary request'
    )


class HotelItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    itemType: Literal['HOTEL_ITEM'] = 'HOTEL_ITEM'
    checkInDateTime: DateTimeLocal | None = Field(
        None, description='date time of check in'
    )
    checkoutDateTime: DateTimeLocal | None = Field(
        None, description='date time of check out'
    )
    isPrepaid: bool | None = Field(
        None, description='If the booking is prepaid', examples=[False]
    )
    prepaidAmount: Money | None = Field(None, description='Amount due now')
    postpaidAmount: Money | None = Field(None, description='Amount due later')
    averageNightlyRate: HotelNightlyRate | None = Field(
        None, description='Average Rate for all the nights.'
    )
    nightlyRates: Sequence[HotelNightlyRate] | None = Field(
        None, description='Rates for all nights in stay date range.'
    )
    hotelData: HotelData | None = None
    rateType: HotelRateType | None = Field(None, description='Rate type for the hotel.')


class ItemNameMetadata(
    RootModel[AirItemNameMetadata | HotelItemNameMetadata | RailItemNameMetadata]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: AirItemNameMetadata | HotelItemNameMetadata | RailItemNameMetadata = Field(
        ...,
        description='Metadata for the ticket.',
        discriminator='pnrType',
        title='ticketMetadata',
    )


class LimoInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cancellationPolicy: CancellationPolicy2 | None = Field(
        None, description='Limo cancellation policy.'
    )
    driver: Driver | None = Field(
        None, description='Driver details of the limo.', title='Driver'
    )
    legs: Sequence[Leg1] = Field(
        ..., description='List of legs covered in the booking.'
    )
    limoDetails: LimoDetails = Field(
        ..., description='Limo details.', title='LimoDetails'
    )
    bookingNotes: str | None = Field(
        None,
        description='Contains information about rate, cancellation policies and terms and conditions.',
    )
    duration: Duration | None = Field(None, description='Booking duration')
    notesToVendor: str | None = Field(
        None, description='Notes added by the booker for the vendor at the checkout.'
    )
    paymentType: PaymentType2 | None = Field(
        None, description='Payment type.', examples=['PREPAID']
    )
    pnrStatus: PnrStatus | None = Field(None, description='Pnr status.')
    rate: Cost = Field(..., description='Limo rate.')
    vendorConfirmationNumber: str = Field(
        ..., description='Limo confirmation number.', examples=['2108528068']
    )
    sourceStatus: str | None = Field(None, description='Source status code of the pnr')


class ListOrganisationTripsResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    trips: Sequence[OrgTripInfo] | None = None


class MiscInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str = Field(
        ...,
        description='Description of the miscellaneous segment.',
        examples=['Any text info'],
    )
    endDateTime: DateTimeLocal = Field(..., description='End date and time.')
    pnrStatus: PnrStatus | None = Field(None, description='Pnr status.')
    rate: Cost = Field(..., description='Miscellaneous segment rate.')
    startDateTime: DateTimeLocal = Field(..., description='Start date and time.')
    vendorConfirmationNumber: str = Field(
        ...,
        description='Miscellaneous segment confirmation number.',
        examples=['2108528068'],
    )
    sortingPriority: int | None = Field(
        None, description='Sorting priority of this pnr', examples=[10]
    )
    sourceStatus: str | None = Field(None, description='Source status code of the pnr')


class Payment(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerIndices: Sequence[int] = Field(
        ...,
        description='The list of traveler indices on which this payment applies.',
        examples=[[0, 1]],
        min_length=1,
    )
    userIds: Sequence[UserId] | None = Field(
        None, description='The list of traveler user IDs on which this payment applies.'
    )
    amount: FareAmount | None = Field(None, description='Total amount')
    fop: FormOfPayment | None = None
    paymentReference: str | None = Field(
        None,
        description='Reference number for original payment made for this service',
        examples=['HAH2618716871'],
    )
    paymentType: PaymentType3 = Field(
        ..., description='Service for which this payment is made', examples=['FLIGHTS']
    )
    paymentThirdParty: PaymentThirdParty | None = Field(
        None,
        description='Third party who is processing the payment',
        examples=['STRIPE'],
    )
    paymentId: str | None = Field(
        None, description='The payment id for this transaction'
    )
    paymentGateway: PaymentGateway | None = Field(
        None,
        description='Third party payment gateway used to process the payment',
        examples=['STRIPE'],
    )
    isRefunded: bool | None = Field(
        None,
        description='Whether the payment is refunded to the customer',
        examples=[False],
    )
    selectedPaymentSource: SelectedPaymentSource | None = None
    networkTransactionId: str | None = Field(
        None,
        description='Network transaction id provided by the card network. Applicable only for cards.',
        examples=['721107212171711'],
    )
    tmcGatewayInfo: TmcGatewayInfo | None = None


class PaymentData(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    isRefunded: bool | None = Field(
        None, description='If the relevant amount was refunded.', examples=[True]
    )
    amount: Money | None = Field(
        None, description='Amount relating to this payment method'
    )
    fop: FormOfPayment | None = Field(
        None, description='fop used for this payment amount'
    )
    isGuarantee: bool | None = Field(
        None,
        description='If the booking can be reserved now and paid later on site.',
        examples=[True],
    )
    transactionStatus: TransactionStatus | None = Field(
        None, description='Status of the service fee transaction.'
    )


class PnrCreationDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    requestedByDetails: RequestedByDetails | None = None


class PaymentInfoItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fop: FormOfPayment = Field(..., description='Form of payment')
    totalCharge: Money = Field(..., description='Total charge for this FOP')
    totalRefund: Money | None = Field(None, description='Total refund for this FOP')
    netCharge: Money | None = Field(None, description='Total charge - total refund')


class PnrTraveler(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId = Field(..., description='User ID for this traveler')
    travelerInfo: TravelerInfoResponse | None = Field(
        None, description='Traveler info for this traveler'
    )
    personalInfo: UserPersonalInfo | None = Field(
        None, description='Personal info like name, email for this traveler'
    )
    loyalties: Sequence[LoyaltyInfo] | None = None
    persona: Persona | None = None
    businessInfo: TravelerBusinessInfo | None = None
    tier: Tier | None = 'BASIC'


class PolicyAction(
    RootModel[
        PolicyFlagActionWrapper
        | PolicyHideActionWrapper
        | PolicyAlertOnSelectionActionWrapper
        | PolicyTakeApprovalActionWrapper
        | PolicyPreventBookingActionWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        PolicyFlagActionWrapper
        | PolicyHideActionWrapper
        | PolicyAlertOnSelectionActionWrapper
        | PolicyTakeApprovalActionWrapper
        | PolicyPreventBookingActionWrapper
    ) = Field(..., description='Action that is required / done for policy.')


class PolicyRuleResultInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    violationInfos: Sequence[PolicyViolationInfo] | None = None
    subViolationInfos: Sequence[PolicyViolationInfo] | None = Field(
        None,
        description='In case of complex rules this will contain extra information as to how the rule was \ncalculated.\n',
    )
    actions: Sequence[PolicyAction] | None = Field(
        None,
        description='Followed actions if rule was satisfied else violated actions.',
    )


class PreBookAnswers(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    answers: Sequence[EntityAnswer] | None = None
    preBookQuestionResponseId: str | None = Field(
        None,
        description='The unique id sent back in the pre book questions API response',
    )


class RailPnrFareInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    appliedPromotions: Sequence[RailAppliedPromotion] | None = Field(
        None, description='List of applied promotions for this fare.'
    )
    destinationInfo: RailStationInfo | None = Field(
        None, description='Fare destination station info.'
    )
    fareLegs: Sequence[RailPnrFareLegInfo]
    fareType: RailFareType = Field(..., description='Fare details for this fare.')
    originInfo: RailStationInfo | None = Field(
        None, description='Fare origin station info.'
    )
    passengerRefs: Sequence[conint(ge=0)] | None = Field(
        None, description='List of passenger indexes for this fare.'
    )
    rate: Cost | None = Field(None, description='Rate Information of the fare.')
    routeRestriction: str | None = Field(
        None,
        description='Route restrictions for the fare.',
        examples=['Travel is allowed via any permitted route.'],
    )


class RailPnrSectionInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fares: Sequence[RailPnrFareInfo] = Field(
        ..., description='List of all fares in this section.'
    )
    rate: Cost | None = Field(None, description='Rate information about the section.')
    sectionStatus: SectionStatus | None = Field(
        None, description='Status of the section.', examples=['CONFIRMED']
    )
    vendorConfirmations: Sequence[RailPnrVendorConfirmation] | None = Field(
        None, description='List of vendor confirmation numbers for this section.'
    )
    vendorName: str | None = Field(
        None, description='Name of the vendor for this section.', examples=['ATOC']
    )
    userFacingStatus: UserFacingStatus | None = Field(
        None, description='User facing status of the section.'
    )


class RailRefundOption(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    refundId: str = Field(..., description='Refund option id.', examples=['*********0'])
    refundRateInfo: RailRefundRateInfo = Field(
        ..., description='Rail refund fare info.'
    )
    legs: Sequence[int] | None = Field(
        None, description='List of leg indices for the refund option.'
    )


class RefundInfoDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    refundType: RefundType | None = Field(
        None, description='Type of refund.', examples=['CREDIT']
    )
    amount: FareAmount | None = None
    fop: FormOfPayment | None = None
    expiryDate: DateModel | None = Field(
        None,
        description='Expiration for a CREDIT refund. This field will be ignored for all cases where the\nrefund_type is not CREDIT. If not set, we assume the credit will expire one year from\nticket issue date.\n',
    )


class RateInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    nightlyRate: Money = Field(..., description='Average nightly rate for the booking.')
    totalRate: Cost = Field(..., description='Total rate for the booking.')
    overallPenaltyCharged: Money | None = Field(
        None, description='Overall Penalty charged for the booking.'
    )
    publishedRate: Cost | None = Field(
        None, description='Published rate for the room, at the time of booking.'
    )
    publishedNightlyRate: Cost | None = Field(
        None, description='Published nightly rate for the room, at the time of booking.'
    )
    prepaidRate: Cost | None = Field(
        None, description='Prepaid component for the rate.'
    )
    postpaidRate: Cost | None = Field(
        None, description='Pay at property component for the rate.'
    )
    rateCode: str | None = Field(
        None, description='Rate Code used for the booking', examples=['SIG']
    )
    isModifiable: bool | None = Field(
        None, description='Flag to indicate if the rate is modifiable', examples=[False]
    )
    isFopModifiable: bool | None = Field(
        None,
        description='Flag to indicate if the form of payment is modifiable',
        examples=[True],
    )
    nightlyRates: Sequence[Cost] | None = Field(
        None, description='Nightly rates for the booking.'
    )
    rateType: HotelRateType | None = Field(None, description='The type of rate.')


class Room(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bedCount: int = Field(..., description='Number of rooms.', examples=[1])
    bedType: BedType = Field(..., description='Bed type.')
    cancellationPolicy: CancellationPolicy2 = Field(
        ..., description='Room cancellation policy.'
    )
    rateInfo: RateInfo = Field(
        ..., description='Rate info for the booking', title='RateInfo'
    )
    roomName: str | None = Field(
        None,
        description='Name of the room.',
        examples=['Standard Twin Room (City View)'],
    )
    imageSets: Sequence[HotelImageSet] | None = None
    meals: HotelRoomMeal | None = Field(
        None, description='Details of Meal plan available with the room.'
    )
    roomAmenities: Sequence[RoomAmenitiy] | None = None
    amenities: Sequence[HotelRoomAmenity] | None = None
    additionalAmenities: Sequence[str] | None = None
    guaranteeType: GuaranteeType | None = Field(
        None, description='Guarantee type for the booking.'
    )
    additionalDetails: Sequence[HotelAdditionalDetail] | None = None
    roomInfo: RoomInfo | None = None
    co2EmissionDetail: HotelCo2EmissionDetail | None = Field(
        None, description='The co2 emission detail for the room.'
    )
    rateMetadata: RateMetadata | None = Field(None, description='Price Metadata.')


class ServiceFee(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fare: FareAmount = Field(..., description='Amount charged for the service fee')
    taxes: Sequence[TaxAmount] | None = Field(
        None, description='List of applicable taxes'
    )
    ticketNumber: str = Field(..., description='Ticket Number')
    status: Status3 = Field(
        ..., description='Status for the associated ticket', examples=['ISSUED']
    )
    fop: FormOfPayment | None = Field(
        None, description='Form of payment for the above amount'
    )
    visibleToTraveler: bool | None = Field(
        None, description='Show/hide service fee to traveler', examples=[True]
    )
    paymentTransactionInfo: PaymentTransactionInfo | None = None
    tags: Sequence[KeyValue] | None = Field(
        None,
        description='List of key-value pairs for storing some tags related to service-fee.',
    )
    feeInfo: FeeInfo | None = None


class SimplePnr(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str = Field(..., description='Spotnana PNR Id', examples=['*********0'])
    sourcePnrId: str = Field(
        ...,
        description='The source PNR reference (Sabre PNR, NDC booking reference etc.)',
        examples=['AEDAVF'],
    )
    source: ThirdPartySource = Field(
        ..., description='Source of the booking e.g. Sabre, NDC etc.'
    )
    air: SimpleAirPnrInfo | None = Field(
        None, description='Air PNR specific information.'
    )
    hotel: SimpleHotelPnrInfo | None = Field(
        None, description='Hotel PNR specific information.'
    )
    car: SimpleCarPnrInfo | None = Field(
        None, description='Car PNR specific information.'
    )
    rail: SimpleRailPnrInfo | None = Field(
        None, description='Rail PNR specific information.'
    )
    limo: SimpleLimoPnrInfo | None = Field(
        None, description='Limo PNR specific information.'
    )
    pnrStatus: PnrStatus | None = Field(
        None, description='Pnr status for this booking.'
    )
    userFacingStatus: UserFacingStatus | None = Field(
        None, description='User facing status for this booking.'
    )


class PaymentDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: FareAmount | None = Field(
        None, description='The amount paid for this ticket'
    )
    fop: FormOfPayment = Field(..., description='Form of payment for the above amount')
    isRefunded: bool | None = Field(
        None,
        description='Whether the above amount is issued or refunded to the customer.',
        examples=[False],
    )


class Ticket(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ticketNumber: str = Field(
        ..., description='Ticket number', examples=['0017574214321']
    )
    ticketType: TicketType1 | None = Field(None, description='Ticket type')
    issuedDateTime: DateTimeLocal | None = Field(
        None, description='Date time when the ticket was issued'
    )
    status: Status5 | None = Field(
        None, description='Ticket status', examples=['ISSUED']
    )
    amount: FareAmount | None = Field(None, description='Total amount of ticket')
    flightCoupons: Sequence[FlightCoupon] | None = Field(
        None, description='List of flight coupons in this ticket'
    )
    ancillaries: Sequence[Ancillary] | None = Field(
        None, description='List of ancillaries in this ticket'
    )
    validatingAirlineCode: str | None = Field(
        None,
        description='2 letter IATA airline code for the validating/ticketing airline',
        examples=['AA'],
    )
    exchangePolicy: ExchangePolicy1 | None = Field(
        None, description='Ticket exchange policy', title='TicketExchangePolicy'
    )
    exchangeInfo: ExchangeInfo | None = Field(
        None, description='Ticket exchange info', title='ExchangeInfo'
    )
    refundPolicy: RefundPolicy | None = Field(
        None, description='Ticket refund policy', title='TicketRefundPolicy'
    )
    refundInfo: RefundInfo1 | None = Field(
        None, description='Ticket refund info', title='RefundInfo'
    )
    llfInfo: LlfInfo | None = Field(
        None,
        description='Information about lowest logical fare for this ticket',
        title='LLFInfo',
    )
    taxBreakdown: PnrTaxBreakdown | None = Field(
        None, description='Tax breakdown information for this ticket'
    )
    commission: Commission | None = Field(
        None, description='Ticket commission info', title='Commission'
    )
    iataNumber: str | None = Field(None, description='Iata number')
    fareCalculation: str | None = Field(None, description='Fare calculation line')
    updateDateTime: DateTimeLocal | None = Field(
        None,
        description='Date time when the ticket was updated (voided, refunded or exchanged)',
    )
    paymentDetails: Sequence[PaymentDetail] | None = Field(
        None, description='List of payment details for this ticket'
    )
    ticketSettlement: TicketSettlement | None = Field(
        None, description='Ticket settlement', examples=['ARC_TICKET']
    )
    ticketCtc: TicketCtc | None = Field(
        None, description='The cost to customer for this ticket', title='ItemCTC'
    )
    publishedFare: FareAmount | None = Field(
        None,
        description='Published fare for this ticket. This field can be used to find savings\nin case of corporate / private fare bookings.\n',
    )
    merchantFee: Money | None = Field(
        None, description='Merchant fee charged on this ticket'
    )
    airlineFee: Money | None = Field(
        None, description='Card or sevice fee charged by airline'
    )
    vendorCancellationId: str | None = Field(
        None,
        description='Cancellation id sent by the supplier.',
        examples=['2108528068'],
    )
    conjunctionTicketSuffix: Sequence[str] | None = Field(
        None,
        description='List of ticket suffix in case of multiple coupons in a single ticket from GDS',
    )
    createdUnusedCredit: UnusedCreditInfo | None = Field(
        None, description='Unused credit that was created by cancelling this ticket.'
    )
    ticketScheduleChangeInformation: TicketScheduleChangeInformation | None = None
    pcc: str | None = Field(
        None, description='The ticketing PCC for this ticket.', examples=['ABCD']
    )
    savingsFare: SavingsFare | None = Field(None, description='Savings Fare')
    preBookAnswers: PreBookAnswers | None = Field(
        None, description='The pre booking answer.'
    )
    ticketIncompleteReasons: Sequence[TicketIncompleteReason] | None = Field(
        None, description='List of reasons for ticket being incomplete'
    )
    associatedTicketNumber: str | None = Field(
        None,
        description='The associated ticket number for this ticket',
        examples=[1251913362894],
    )
    rficCode: str | None = Field(
        None, description='Reason for issuance code', examples=['D']
    )
    emdInfo: EmdInfo | None = Field(
        None, description='EMD Information of this ticket if applicable'
    )


class TransactionItem(RootModel[AirItem | RailItem | CarItem | HotelItem]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: AirItem | RailItem | CarItem | HotelItem = Field(
        ...,
        description='Items for the given item group.',
        discriminator='itemType',
        title='TransactionItem',
    )


class Traveler(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerPersonalInfo: TravelerPersonalInfo | None = None
    user: User | None = None
    userBusinessInfo: UserBusinessInfo | None = None
    userOrgId: UserOrgId | None = None
    persona: Persona | None = None
    isActive: bool | None = Field(
        None,
        description='A boolean flag to show if traveler is active.',
        examples=[True],
    )
    tier: Tier | None = 'BASIC'
    adhocUserInfo: AdhocUserInfo | None = None
    externalId: str | None = Field(None, description='External id of this user.')


class TravelerInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airVendorCancellationInfo: AirVendorCancellationInfo | None = None
    createdMcos: Sequence[CreatedMco] | None = Field(None, description='Created Mcos')
    travelerIdx: conint(ge=0) = Field(
        ...,
        description='Index of traveler in travelers list to which this info belongs',
        examples=[0],
    )
    userId: UserId | None = Field(
        None,
        description='User ID of traveler to which this TravelerInfo object belongs',
    )
    paxType: PassengerType | None = None
    tickets: Sequence[Ticket] | None = Field(
        None, description='Tickets belonging to this traveler'
    )
    boardingPass: Sequence[BoardingPass] | None = Field(
        None, description='Boarding pass details.'
    )
    booking: Booking = Field(
        ...,
        description='Info about items booked for this traveler eg. flights, seats, etc.',
    )
    appliedCredits: Sequence[AppliedCreditInfo] | None = Field(
        None, description='Information about credit redeemed in booking.'
    )
    specialServiceRequestInfos: Sequence[SpecialServiceRequestInfoDetail] | None = (
        Field(
            None,
            description='List of all the Special Service Requests associated with the traveler.',
        )
    )


class TripPartnerInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    partnerInfoType: TripPartnerInfoType
    partnerInfoDetails: TripPartnerInfoDetails


class TripPartnerInfoResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tripId: str = Field(..., description='Spotnana trip Id', examples=['6926658168'])
    partnerInfo: TripPartnerInfo


class UAPassPlusMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    programDetails: ProgramDetails
    uatpInfo: UATPInformation


class UAPassPlusMetadataWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    uaPassPlusMetadata: UAPassPlusMetadata | None = None


class BookingMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fareStatistics: FareStatistics | None = Field(
        None,
        description='Fare statistics of all logical itineraries. In case of multi pax, only primary i.e. \nadult fare is included in stats.\n',
    )


class Air(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legs: Sequence[Leg] = Field(
        ..., description='List of legs that are part of this bookings'
    )
    airPnrRemarks: Sequence[AirPnrRemark] | None = Field(
        None, description='Remarks added for the pnr.'
    )
    travelerInfos: Sequence[TravelerInfo] = Field(
        ..., description='Information about traveler bookings', min_length=1
    )
    automatedCancellationInfo: AutomatedCancellationInfo | None = None
    automatedExchangeInfo: AutomatedExchangeInfo | None = None
    bookingMetadata: BookingMetadata | None = Field(
        None,
        description='Metadata like fare statistics for this booking',
        title='BookingMetadata',
    )
    otherServiceInfos: Sequence[OtherServiceInformationDetail] | None = Field(
        None,
        description='List of all the Other Service Information associated with the PNR.',
    )
    holdDeadline: HoldInfo | None = None
    airPriceOptimizationMetadata: AirPriceOptimizationMetadata | None = None
    disruptedFlightDetails: Sequence[FlightDetailInformation] | None = Field(
        None,
        description='List of all the last confirmed flights that have changed via disruption by the airline.',
    )


class AirCancellationOption(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cancellationType: AirCancellationType | None = Field(
        None, description='Cancellation type like VOID, REFUND, etc.'
    )
    fareInfo: AirFareInfo = Field(
        ..., description='Fare breakup for cancellation option.'
    )
    offerExpiryInfo: OfferExpiryInfo | None = Field(
        None, description='Cancellation offer expiry datetime in UTC.'
    )
    optionId: str | None = Field(
        None, description='Unique id for this cancellation option.', examples=['1']
    )


class AirlineProgramMetadata(
    RootModel[UATPMetadataWrapper | UAPassPlusMetadataWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: UATPMetadataWrapper | UAPassPlusMetadataWrapper = Field(
        ..., title='AirlineProgramMetadata'
    )


class ApprovalData(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    approvalId: str | None = Field(
        None, description='Unique approval Id request', examples=['uniqueId']
    )
    approvers: Sequence[ApproverInfo] | None = None
    approvalStatus: ApprovalStatus | None = None
    approvalVoidDeadline: DateTimeOffset | None = None
    approvalDetail: ApprovalDetail | None = None


class COGS(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    payments: Sequence[Payment] = Field(
        ...,
        description='Potentially multiple payments that spotnana will pay to vendors for this booking',
        min_length=1,
    )


class CTC(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    payments: Sequence[Payment] = Field(
        ...,
        description='Potentially multiple payments that spotnana will charge the clients',
        min_length=1,
    )


class CancelDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cancelObjectDetail: CancelObjectDetail | None = None
    refundInfo: Sequence[RefundInfoDetail] | None = Field(
        None, description='List of refund details associated with the cancel object.'
    )


class Car(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cancellationPolicy: CancellationPolicy2 | None = Field(
        None, description='Car rental cancellation policy.'
    )
    carInfo: CarInfo = Field(..., description='Details of the car booked.')
    dropOffDateTime: DateTimeLocal = Field(..., description='Drop off date and time.')
    paymentType: PaymentType | None = Field(
        None, description='Payment type.', examples=['PREPAID']
    )
    pickupDateTime: DateTimeLocal = Field(..., description='Pickup date and time.')
    pnrStatus: PnrStatus | None = Field(None, description='Pnr status.')
    rate: Cost = Field(..., description='Car rental rate.')
    vendorConfirmationNumber: str = Field(
        ..., description='Car confirmation number.', examples=['2108528068']
    )
    sortingPriority: int | None = Field(
        None, description='Sort order on the trips page', examples=[0]
    )
    corporateId: str | None = Field(
        None, description='Corporate id applied while booking', examples=['8371']
    )
    rateType: RateType | None = Field(None, description='Rate type of the car booked')
    sourceStatus: str | None = Field(None, description='Source status code of the pnr')
    vendorCancellationId: str | None = Field(
        None,
        description='Cancellation id sent by the supplier.',
        examples=['2108528068'],
    )
    originalCarSearchInfo: CarSearchInfo | None = Field(
        None, description='Original Car search info.'
    )
    rateMetadata: RateMetadata | None = Field(None, description='Price Metadata.')
    rebookReference: CarRebookReference | None = Field(
        None,
        description="Reference to PNR's which were cancelled or booked in favour of this PNR.",
    )
    dailyRates: Sequence[CarDailyRate] | None = Field(
        None, description='Contains daily rates for each day of the rental duration.'
    )
    carAverageRate: CarDailyRate | None = Field(
        None, description='Contains average rate for the entire rental duration.'
    )


class Document(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    url: str = Field(
        ...,
        description='S3 location of the document.',
        examples=['https://s3.amazonaws.com/bucket-name/folder-name/file-name'],
    )
    documentId: UUID = Field(
        ...,
        description='Unique identifier of the document.',
        examples=['f49d00fe-1eda-4304-ba79-a980f565281d'],
    )
    documentMetadata: DocumentMetadata


class FinalizeRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preBookAnswers: PreBookAnswers | None = None
    finalizeIntent: FinalizeIntent | None = None
    finalizeMetadata: FinalizeMetadata | None = None
    customFieldV3UserResponses: Sequence[CustomFieldV3Response] | None = Field(
        None, description='Custom field user responses for the booking.'
    )


class Hotel(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    checkInDateTime: DateTimeLocal = Field(..., description='Check-in date and time.')
    checkOutDateTime: DateTimeLocal = Field(..., description='Check-out date and time.')
    hotelInfo: HotelInfo = Field(..., description='Hotel details.')
    numberOfRooms: int = Field(..., description='Number of rooms booked.', examples=[1])
    payment: Payment1 = Field(..., description='Payment method.', title='HotelPayment')
    pnrStatus: PnrStatus | None = Field(None, description='Pnr status.')
    room: Room = Field(..., description='Room info.')
    vendorConfirmationNumber: str = Field(
        ..., description='Hotel confirmation number', examples=['2108528068']
    )
    travelerInfos: Sequence[HotelTravelerInfo] | None = Field(
        None, description='Information about travelers.'
    )
    sortingPriority: int | None = Field(
        None, description='Sorting priority amongst other pnrs', examples=[0]
    )
    vendorReferenceId: str | None = Field(
        None, description='Reference provided by vendors', examples=['AWKHGS']
    )
    vendorCancellationId: str | None = Field(
        None,
        description='Cancellation reference provided by vendors',
        examples=['AWKHGS'],
    )
    preferredType: Sequence[PreferredType] | None = Field(
        None, description='Hotel preference'
    )
    preferences: Sequence[Preference] | None = Field(
        None, description='Hotel preference'
    )
    occupancy: Sequence[HotelOccupancy] | None = Field(
        None, description='Occupancy information'
    )
    sourceStatus: str | None = Field(None, description='Source status code of the pnr')
    rebookReference: RebookReference | None = None
    hotelSpecialRequests: HotelSpecialRequests | None = Field(
        None, description='Hotel special requests.'
    )


class Item(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(
        ..., description='Name of the item.', examples=['Air Ticket SFO-EWR']
    )
    identifier: str = Field(
        ...,
        description='Number identifying the item, for ex, the ticket number for air ticket.',
        examples=['00129383829'],
    )
    purchaseType: PurchaseType = Field(
        ...,
        description='Type of item, indicates what type of item is being sold',
        examples=['TRIP_FEE'],
    )
    type: Type5 = Field(
        ...,
        description='Type of item, indicates if the item was purchased vs refunded.',
        examples=['PURCHASE'],
    )
    amount: FareAmount = Field(..., description='Amount of money.')
    travelerName: str = Field(
        ..., description='Name of the traveler.', examples=['John Doe']
    )
    taxBreakdown: TaxBreakdown | None = Field(
        None,
        description='Tax breakdown for the invoice, this will be present for UK invoices with VAT',
    )
    itemNameMetadata: ItemNameMetadata | None = Field(
        None, description='Metadata for the item name.'
    )


class ItemGroup(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    transactionType: TransactionType | None = Field(
        None, description='Type of this transaction'
    )
    userId: UserId | None = Field(
        None, description='User id of the user to which the itemGroup is related'
    )
    confirmationNumber: str | None = Field(None, examples=['2797091789401'])
    transactionDateTime: DateTimeLocal | None = Field(
        None, description='Date time of the transaction'
    )
    transactionAmountDiff: FareAmount | None = Field(
        None,
        description='Difference between amount relating to this itemGroup and last itemGroup having same confirmation number.',
    )
    transactionFeesDiff: Money | None = Field(
        None,
        description='Difference between airline fees in case of air and last itemGroup having same confirmation number.',
    )
    totalAmountDiff: FareAmount | None = Field(
        None,
        description='Difference between total amount for this itemGroup and last itemGroup having same confirmation number.',
    )
    penaltyDiff: Money | None = Field(
        None,
        description='Difference between penalty charged for this itemGroup and last itemGroup having same confirmation number.',
    )
    transactionAmount: FareAmount | None = Field(
        None, description='Amount relating to this itemGroup.'
    )
    transactionFees: Money | None = Field(
        None, description='Airline fees in case of air'
    )
    totalAmount: FareAmount | None = Field(
        None, description='Total amount for this itemGroup.'
    )
    totalPnrAmount: Money | None = Field(
        None, description='Total amount for this pnr up until this transaction.'
    )
    penalty: Money | None = Field(
        None, description='Penalty charged for this itemGroup.'
    )
    originalTicketNumber: str | None = Field(
        None, description='Original ticket number if this is an exchanged ticket'
    )
    invoiceData: ItemGroupInvoiceData | None = Field(
        None, description='Invoice related data'
    )
    items: Sequence[TransactionItem] | None = None
    transactionFares: Sequence[TransactionFare] | None = None
    previousTransactionFares: Sequence[TransactionFare] | None = None


class PnrCancelRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cancellationType: CancellationType | None = Field(
        None,
        description='Type of cancellation to be performed. For the NO_REFUND, FULL_REFUND and\nFULL_CREDIT cancellation types, if no cancellationDetails are provided, we assume the\nentire PNR is being canceled. If the cancellation type is FULL_CREDIT, we will also\nassume the credits created will have 0 penalty and an expiry date of 1 year from the\nissue date of the ticket.\n',
        examples=['FULL_REFUND'],
    )
    cancellationDetails: Sequence[CancelDetail] | None = Field(
        None,
        description='List of object to be cancelled and their associated refund details.',
    )
    bookingEmailInfo: BookingEmailInfo | None = Field(
        None,
        description='Information passed to the Spotnana email system that processes booking updates.',
    )


class PnrPolicyInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    policies: Sequence[PnrPolicyId] | None = Field(
        None, description='List of policies applied to the itinerary.'
    )
    ruleResultInfos: Sequence[PolicyRuleResultInfo] | None = None


class PolicyDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    reasonCode: PreDefinedAnswers | None = Field(
        None, description='Reason code for booking out of policy'
    )
    reason: str | None = Field(
        None,
        description='Free form text if selected reason code is OTHER',
        examples=['Business class cheaper than economy'],
    )
    appliedPolicyInfo: PnrPolicyInfo = Field(
        ...,
        description='Information related to policies and results of those evaluation on this pnr\n',
    )
    isOutOfPolicy: bool | None = Field(
        None,
        description='Flag to indicate if the booking is out of policy',
        examples=[True],
    )


class PolicyInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    outOfPolicy: bool = Field(
        ...,
        description='True if the booking is out of policy, else false',
        examples=[False],
    )
    approverName: Name | None = Field(
        None, description='The name of the approver. Required if out of policy is true'
    )
    reasonCode: PreDefinedAnswers | None = Field(
        None, description='Reason code for booking out of policy'
    )
    reason: str | None = Field(
        None,
        description='Free form text if selected reason code is OTHER',
        examples=['Business class cheaper than economy'],
    )
    appliedPolicyInfo: PnrPolicyInfo | None = Field(
        None,
        description='Information related to policies and results of those evaluation on this pnr\n',
    )


class RailCancellationDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    refundOptions: Sequence[RailRefundOption] | None = Field(
        None, description='Refund options for this booking.'
    )


class RailCancellationDetailsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    rail: RailCancellationDetails | None = None


class RailPnrPreviousItinerary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: RailSearchType = Field(..., description='Search type of the itinerary.')
    outwardJourney: RailPnrJourneyInfo = Field(
        ..., description='Outward journey details.'
    )
    inwardJourney: RailPnrJourneyInfo | None = Field(
        None, description='Inward journey details.'
    )
    legInfos: Sequence[RailPnrLegInfo] = Field(
        ..., description='List of legs covered in the booking.'
    )
    sections: Sequence[RailPnrSectionInfo] = Field(
        ..., description='List of sections covered in the itinerary.'
    )
    deliveryOption: RailDeliveryOption = Field(
        ..., description='Delivery option of the itinerary.'
    )
    sourceReference: str = Field(..., description='Source reference of the itinerary.')
    rate: Cost = Field(..., description='Rate information of the previous itinerary.')


class TicketCancellationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ticketNumber: str | None = Field(
        None,
        description='Ticket number. For non-ticketed PNRs, this field will not be present.',
        examples=['011*********0'],
    )
    ticketType: TicketType2 = Field(
        ..., description='Type of ticket.', examples=['FLIGHT'], title='TicketType'
    )
    ticketFare: FareAmount = Field(
        ...,
        description='Fare of this ticket. For tickets created via exchange, it will be equal to add collect\n',
    )
    cancellationState: AirCancellationState = Field(
        ..., description='Current cancellation state for this ticket.'
    )
    cancellationOptions: Sequence[AirCancellationOption] | None = Field(
        None,
        description='Cancellation options for this ticket. A ticket can have multiple options, like refunding\nticket, converting ticket to flight credits.\n',
        min_length=1,
    )
    cancellationNotSupportedReasons: (
        Sequence[AirCancellationNotSupportedReason] | None
    ) = Field(
        None,
        description='Reasons why cancellation is not supported via OBT.',
        min_length=1,
    )
    isNonRefundableAndNonExchangeable: bool | None = Field(
        None, description='If true, the ticket is not refundable and not exchangeable.'
    )


class Transaction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentReqId: str | None = Field(
        None,
        description='payment request id',
        examples=['3077eeb8-b706-47e5-a762-8442638c07a4'],
    )
    ctc: Sequence[PaymentData] | None = None
    itemGroups: Sequence[ItemGroup] | None = None
    pnrVersion: int | None = Field(
        None, description='Version number associated with the Pnr'
    )
    splitPayment: SplitPayment | None = Field(
        None, description='Split payment info for reimbursements.'
    )


class TravelerEventSummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., description='Event ID', examples=['12345'])
    type: EventType | None = Field(None, description='Event Type')
    name: str | None = Field(
        None, description='Name of the event', examples=['My event']
    )
    description: str | None = Field(
        None,
        description='Description of the event',
        examples=['This is an event description'],
    )
    startDateTime: DateTimeLocal | None = None
    endDateTime: DateTimeLocal | None = None
    location: EventLocation | None = None
    contacts: Sequence[UserId] | None = Field(
        None, description='Event contacts for the traveler'
    )
    documents: Sequence[Document] | None = Field(
        None,
        description='List of documents associated with this event for the traveler',
    )
    bookingGuidelines: Sequence[EventBookingGuideline] | None = Field(
        None, description='Booking details allowed for the event for the traveler'
    )
    allowedBookingTypes: Sequence[EventAllowedBookingType] | None = Field(
        None, description='Allowed booking types for the event for the traveler'
    )
    eventUserRsvp: EventUserRsvp | None = None
    contactInfoList: Sequence[EventUserInfo] | None = Field(
        None, description='Event contacts for the traveler'
    )
    companyId: EntityId | None = None
    runningStatus: EventRunningStatus | None = None
    status: EventStatus | None = None
    isRemovedParticipant: bool | None = Field(
        None, description='Whether the traveler is part of the event.'
    )


class AirTravelerCancellationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId = Field(..., description='User ID of traveler.')
    travelerInfo: TravelerInfoResponse | None = Field(
        None, description='Unique identifier for the traveler in this response.'
    )
    tickets: Sequence[TicketCancellationInfo] = Field(
        ..., description='Cancellation information for traveler tickets.', min_length=1
    )
    cancellationOptions: Sequence[AirCancellationOption] | None = Field(
        None, description='Cancellation options for this traveler.', min_length=1
    )


class AirlineProgram(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlineInfo: Sequence[Airline] = Field(
        ..., description="Eligible airlines' information list."
    )
    airlineProgramMetadata: AirlineProgramMetadata = Field(
        ..., description='Airline program payment specific metadata.'
    )


class AirlineProgramWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlineProgram: AirlineProgram | None = None


class PnrSummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str = Field(..., description='Spotnana PNR Id', examples=['*********0'])
    sourcePnrId: str | None = Field(
        None, description='Source PNR Id', examples=['QWERGF']
    )
    pnrStatus: PnrStatus | None = None
    pnrSummary: PnrSummaryWrapper | None = None
    userFacingStatus: UserFacingStatus | None = None
    tmcInfo: TmcInfo | None = None
    source: ThirdPartySource | None = Field(
        None, description='Source of the booking e.g. Sabre, NDC etc.'
    )
    travelers: Sequence[Traveler] | None = None
    pnrType: PnrType | None = None
    approvalData: ApprovalData | None = None
    policyInfo: PolicyDetail | None = None
    additionalMetadata: AdditionalMetadata1 | None = Field(
        None, description='Any addtional metadata associated with the booking'
    )
    preBookAnswers: PreBookAnswers | None = Field(
        None,
        description='The per booking answers collected from the traveler during checkout.',
    )


class Rail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    inwardJourney: RailPnrJourneyInfo | None = Field(
        None, description='Inward Journey.'
    )
    legInfos: Sequence[RailPnrLegInfo] = Field(
        ..., description='List of legs covered in the booking.'
    )
    outwardJourney: RailPnrJourneyInfo = Field(..., description='Outward Journey.')
    rate: Cost = Field(..., description='Rate information of the journey.')
    rateMetadata: RateMetadata | None = Field(None, description='Price Metadata.')
    passengerInfos: Sequence[RailPnrPassengerInfo] | None = Field(
        None, description='List of passenger information.'
    )
    paymentMode: PaymentMode1 | None = Field(None, examples=['PRE_PAID'])
    sections: Sequence[RailPnrSectionInfo] | None = Field(
        None, description='List of sections covered in the booking.'
    )
    tickets: Sequence[RailPnrTicket] | None = Field(
        None, description='List of rail tickets.'
    )
    ticketDetails: Sequence[RailPnrTicketDetail]
    type: RailSearchType = Field(..., description='Journey type.')
    vendorConfirmationNumber: str = Field(
        ..., description='Rail confirmation number', examples=['ABQTEJ']
    )
    itineraryId: str | None = Field(
        None, description='Itinerary ID', examples=['abcde']
    )
    ancillaries: Sequence[RailAncillary] | None = Field(
        None, description='Details of the selected ancillaries for the booking.'
    )
    termsAndConditions: TermsAndConditions | None = None
    exchangeInfo: RailPnrExchangeInfo | None = Field(
        None, description='Exchange information for rail booking.'
    )
    previousItinerary: RailPnrPreviousItinerary | None = Field(
        None, description='Previous itinerary information for rail booking.'
    )


class TripSummary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tripId: str = Field(..., description='Spotnana trip Id', examples=['6926658168'])
    tripName: str = Field(
        ..., description='Name of the trip', examples=['JFK SFO Trip']
    )
    description: str | None = Field(
        None, description='Description of the trip', examples=['Business Trip']
    )
    pnrSummaries: Sequence[PnrSummary] | None = None
    tripStatus: PnrBookingStatus | None = None
    startDate: DateModel | None = None
    endDate: DateModel | None = None
    userBasicInfo: UserBasicInfo | None = None
    overallStatus: OverallStatus | None = None
    tripUsageMetadata: TripUsageMetadata | None = None
    createdByUserBasicInfo: UserBasicInfo | None = None
    policyStatus: PolicyStatus | None = None


class VendorProgramPaymentMetadata(
    RootModel[DirectBillingWrapper | AirlineProgramWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: DirectBillingWrapper | AirlineProgramWrapper = Field(
        ...,
        description='Vendor program payment specific metadata.',
        title='VendorProgramPaymentMetadata',
    )


class AirCancellationDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelers: Sequence[AirTravelerCancellationInfo] = Field(
        ...,
        description='Cancellation information for travelers in this booking.',
        min_length=1,
    )
    cancellationOptions: Sequence[AirCancellationOption] | None = Field(
        None, description='Cancellation options for this booking.', min_length=1
    )
    cancellationState: AirCancellationState = Field(
        ..., description='Cancellation state for this booking.'
    )


class AirCancellationDetailsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    air: AirCancellationDetails | None = None


class ApprovalDataV2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    approvalId: UUID | None = Field(
        None,
        description='Unique ID to identify approval data',
        examples=['f49d00fe-1eda-4304-ba79-a980f565281d'],
    )
    pnrId: str | None = Field(None, description='PNR ID', examples=[*********0])
    tripId: str | None = Field(None, description='Trip ID', examples=[*********0])
    approvalVoidDeadline: DateTimeOffset | None = Field(
        None, description='Deadline at which approval gets void and auto action taken'
    )
    approvalStatus: ApprovalStatusV2 | None = Field(None, description='Approval Status')
    companyId: CompanyId | None = Field(None, description='Company ID')
    outOfPolicy: bool | None = Field(
        None, description='Out of policy indicator', examples=[True]
    )
    pnrType: PnrType | None = Field(None, description='PNR type')
    approvalType: ApprovalType | None = Field(None, description='Approval type applied')
    approversUserIds: Sequence[UserId] | None = Field(
        None, description='User IDs of assigned approvers'
    )
    approvalProcessedBy: UserId | None = Field(
        None, description='User Id of approval action (approved/denied) taken by'
    )
    legalEntityId: LegalEntityId | None = Field(
        None, description='The ID of the legal entity.'
    )
    pnrVersion: int | None = Field(
        None, description='Version number associated with the PNR when approval raised'
    )
    pnrSummary: PnrSummary | None = Field(
        None, description='Corresponding PNR summary object for the approval data'
    )


class CancellationDetails(
    RootModel[AirCancellationDetailsWrapper | RailCancellationDetailsWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: AirCancellationDetailsWrapper | RailCancellationDetailsWrapper = Field(
        ...,
        description='Cancellation details for this booking',
        title='CancellationDetails',
    )


class GetApprovalDataV3Response(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paginationResponseParams: OffsetBasedPaginationResponseParams
    approvalDataList: Sequence[ApprovalDataV2] | None = Field(
        None, description='List of Approval Data'
    )


class GetTripSummariesV3Response(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paginationResponseParams: OffsetBasedPaginationResponseParams
    tripSummaries: Sequence[TripSummary] | None = Field(
        None, description='List of Trip summaries'
    )
    failedTripSummaries: Sequence[TripSummary] | None = Field(
        None, description='List of Trip summaries'
    )


class PnrCancellationDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str = Field(
        ..., description='PNR ID for the booking.', examples=['*********0']
    )
    tripId: str = Field(
        ..., description='Trip ID for the booking.', examples=['2345678901']
    )
    cancellationDetails: CancellationDetails | None = Field(
        None, description='Cancellation details for this booking.'
    )


class PnrData(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    version: int | None = Field(
        None,
        description='Current PNR version. This field is used for handling conflicts in case of concurrent \nupdates. This should be sent unmodified in update request.\n',
        examples=[1],
    )
    createdVia: CreatedVia | None = None
    initialVersionCreatedVia: CreatedVia | None = None
    sourceInfo: SourceInfo | None = None
    invoiceDelayedBooking: bool | None = Field(
        None, description='True if the billing is delayed', examples=[False]
    )
    travelers: Sequence[Traveler] | None = Field(None, min_length=1)
    pnrTravelers: Sequence[PnrTraveler] | None = Field(
        None,
        description='List of travelers on this pnr and their information. This is a required field.',
        min_length=1,
    )
    costOfGoodsSold: COGS | None = None
    costToCustomer: CTC | None = None
    transactions: Sequence[Transaction] | None = None
    splitPayment: SplitPayment | None = Field(
        None, description='Split payment details for claiming reimbursements.'
    )
    isFinalized: bool | None = Field(
        None,
        description='Indicates if finalize action has been performed on the pnr.',
        examples=[False],
    )
    policyInfo: PolicyInfo | None = Field(
        None, description='Policy related info for this booking'
    )
    airPnr: Air | None = Field(None, description='Air booking information')
    hotelPnr: Hotel | None = Field(None, description='Hotel booking information')
    carPnr: Car | None = Field(None, description='Car booking information')
    railPnr: Rail | None = Field(None, description='Rail booking information')
    limoPnr: LimoInfo | None = Field(None, description='Limo booking information')
    miscPnr: MiscInfo | None = Field(
        None, description='Miscellaneous booking information'
    )
    additionalMetadata: AdditionalMetadata | None = Field(
        None, description='Any addtional metadata associated with the booking'
    )
    preBookAnswers: PreBookAnswers | None = Field(
        None,
        description='The per booking answers collected from the traveler during checkout.',
    )
    customFields: Sequence[CustomField] | None = Field(
        None, description='List of all the Custom Fields associated with this Pnr'
    )
    customFieldsV3Responses: Sequence[CustomFieldResponsesPerEntity] | None = Field(
        None,
        description='List of all the Custom Fields responses associated with this Pnr.',
    )
    bookingHistory: Sequence[BookingHistory] | None = Field(
        None, description='Change Log associated with a PNR/Booking'
    )
    totalFare: Money | None = Field(
        None,
        description='The total fare of the PNR inclusive of various components, for example: Flights, Seats, \nService fee for an Air Pnr; Hotel rooms, Hotel fee, Service for a Hotel PNR. Service fee is included only when show trip fee to traveler is enabled in service fee settings.\n',
    )
    serviceFees: Sequence[ServiceFee] | None = Field(
        None, description='The service fees associated with this pnr.'
    )
    paymentInfo: Sequence[PaymentInfoItem] | None = Field(
        None, description='FOP fare breakup for all payments'
    )
    bookingStatus: UserFacingStatus | None = Field(
        None, description='User facing status for this booking.'
    )
    contactSupport: bool | None = Field(
        None,
        description='Set to true if there might be an issue in the PNR for which the user should be warned to\ncontact TMC support.\n',
        examples=[False],
    )
    travelerPnrVisibilityStatus: TravelerPnrVisibilityStatus | None = Field(
        None,
        description='Represents the PNR visibility status, if marked as HIDDEN should not be displayed to the traveler',
    )
    shellPnrInfo: ShellPnrInfo | None = None
    pnrCreationDetails: PnrCreationDetails | None = None
    approvalInfo: Sequence[ApprovalInfo] | None = Field(
        None, description='Approval related information for the PNR'
    )
    cancellationRequestStatus: CancellationRequestStatus | None = None
    externalInfo: ExternalInfo | None = Field(
        None, description='PNR related external information'
    )
    tripId: str | None = Field(
        None,
        description='Spotnana trip ID to which the PNR belongs to',
        examples=['*********'],
    )
    documents: Sequence[Document] | None = None
    freshnessInfo: FreshnessInfo | None = None
    bookingEmailInfo: BookingEmailInfo | None = Field(
        None,
        description='Information passed to the Spotnana email system that processes booking updates.',
    )
    llfPnrInfo: LLFPnrInfo | None = None
    pnrId: str | None = Field(
        None, description='Spotnana PNR ID', examples=['*********']
    )
    invoiceInfos: Sequence[InvoiceInfo] | None = Field(
        None, description='Invoice related information for the PNR'
    )
    totalFareAmount: FareAmount | None = Field(
        None,
        description='Total fare amount of the PNR comprising components such as base fare and taxes. This field does not include any Service fees.',
    )
    dkNumber: str | None = Field(
        None, description='DK number attached to the PNR.', examples=['*********']
    )
    savingsFare: SavingsFare | None = None
    tripUsageMetadata: TripUsageMetadata | None = None
    owningPccInfo: OwningPCCInfo | None = None
    suspendReason: SuspendReason | None = None


class PnrDetailsResponseWithId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str | None = Field(
        None, description='Unique PNR ID', examples=['6926658168']
    )
    data: PnrData | None = None


class TripV3DetailsResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    basicTripInfo: BasicTripInfo
    pnrs: Sequence[PnrDetailsResponseWithId]
    pendingShellPnrs: Sequence[PnrDetailsResponseWithId] | None = None
    pendingManualFormPnrs: Sequence[PnrDetailsResponseWithId] | None = None
    tripStatus: PnrBookingStatus | None = None
    tripBookingStatus: UserFacingStatus | None = None
    eventSummary: TravelerEventSummary | None = None
    simplePnrs: Sequence[SimplePnr] | None = None
    additionalInfo: TripAdditionalInfo | None = None
    tripPaymentInfo: TripPaymentInfo | None = None


class VendorProgramPaymentDescriptor(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorInfo: VendorInfo | None = None
    vendorProgramPaymentMetadata: VendorProgramPaymentMetadata


class VendorProgramPaymentDescriptorWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorProgramPaymentDescriptor: VendorProgramPaymentDescriptor | None = None


class PaymentSourceMetadata(
    RootModel[
        CardMetadataWrapper
        | VirtualCardMetadataWrapper
        | RewardsProgramMetadataWrapper
        | CustomPaymentMethodMetadataWrapper
        | VendorProgramPaymentDescriptorWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        CardMetadataWrapper
        | VirtualCardMetadataWrapper
        | RewardsProgramMetadataWrapper
        | CustomPaymentMethodMetadataWrapper
        | VendorProgramPaymentDescriptorWrapper
    ) = Field(
        ...,
        description='Metadata corresponding to the payment source.',
        title='PaymentSourceMetadata',
    )


class ActualPayment(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentSourceType: PaymentSourceType = Field(
        ..., description='Type of payment source.'
    )
    paymentMetadata: PaymentSourceMetadata = Field(
        ..., description='Type of payment source.'
    )


class PaymentInformation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str | None = Field(
        None, description='Payment reference ID.', examples=['sdkljhafisudlls']
    )
    transactionTime: DateTimeOffset | None = Field(
        None, description='Transaction timestamp.'
    )
    amount: Money = Field(..., description='Amount paid.')
    paymentMode: PaymentMode = Field(
        ..., description='Mode of Payment shown on invoice.', examples=['CREDIT_CARD']
    )
    paymentInstrumentIdentifier: str = Field(
        ...,
        description='An identifier for the payment instrument used. For example, last four credit card number digits in case a credit card was used.',
        examples=['**2343'],
    )
    actualPayment: ActualPayment | None = Field(
        None, description='Details of how payment was done'
    )
    exchangePayment: ExchangePayment | None = Field(
        None, description='Details of how payment was done'
    )


class InvoiceData(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    issuedDate: DateModel = Field(..., description='Date of invoice issue')
    seller: SellerInfo = Field(
        ..., description='Entity that sold the items mentioned in the invoice.'
    )
    buyer: BuyerInfo = Field(
        ..., description='Buyer of items mentioned in the invoice.'
    )
    items: Sequence[Item] = Field(..., description='Items being invoiced.')
    payments: Sequence[PaymentInformation] = Field(
        ..., description='Information on how the items have been paid for.'
    )
    productId: str = Field(
        ..., description='ID of the product being invoiced', examples=['*********']
    )
    productType: ProductType = Field(
        ..., description='Type of product for which invoice is generated.'
    )
    invoiceAmount: FareAmount = Field(..., description='Total invoice amount.')
    taxBreakdown: TaxBreakdown | None = Field(
        None,
        description='Tax breakdown for the invoice, this will be present for UK invoices with VAT',
    )
    referenceIds: Sequence[ReferenceId] = Field(
        ...,
        description='Reference Numbers to be shown on the invoice, for ex, Trip ID, PNR ID etc.',
    )
    regionCode: RegionCode | None = Field(
        None,
        description='The region code for which the invoice is generated.',
        examples=['US'],
    )
    documentType: DocumentType1 | None = Field(
        None, description='Type of document', examples=['INVOICE']
    )
    footerText: str | None = Field(
        None,
        description='Text shown at bottom of the PDF document. Nothing is shown if not set.',
        examples=[
            'This document is proof of payment. If you require a tax invoice, please request one from the travel provider.'
        ],
    )


class Invoice(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    invoiceNumber: str = Field(
        ..., description='The invoice number', examples=['SPOT-0001232']
    )
    invoiceId: str = Field(
        ...,
        description='The invoice ID',
        examples=['d694cf94-534e-410e-b66b-11ef970622bc'],
    )
    data: InvoiceData = Field(..., description='The invoice content')


class InvoiceDataResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    invoices: Sequence[Invoice] = Field(
        ..., description='List of invoices which were requested.'
    )
