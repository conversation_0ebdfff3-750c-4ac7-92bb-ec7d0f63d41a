Data model for Spotnana API

OpenAPI spec: https://docs.spotnana.com/reference/openapi

## Directory Structure

This directory contains API models organized by Spotnana API domains:

- `air/` - Flight search, booking, seat selection operations
- `hotel/` - Hotel search and booking operations  
- `company/` - Company management, legal entities, cost centers
- `policy/` - Travel policies, custom fields
- `trip/` - Trip management, PNRs, invoicing
- `event/` - Event management and templates
- `users/` - User management, profiles, travel preferences, roles

## Using the Management Script

The `manage_spotnana_models.py` script is a multi-functional tool that can:

### Download OpenAPI Specifications
```bash
# From the models/api/spotnana/ directory
uv run python manage_spotnana_models.py download
```

### Generate Python Models
```bash
# From the models/api/spotnana/ directory  
uv run python manage_spotnana_models.py generate
```

### Download and Generate in One Command
```bash
# From the models/api/spotnana/ directory
uv run python manage_spotnana_models.py all
```

### What Each Command Does

**Download Command:**
1. Downloads OpenAPI YAML files from Spotnana's developer portal
2. Saves each spec to the appropriate subdirectory with its original filename (e.g., `AirApi.yaml`)
3. Creates any missing subdirectories automatically

**Generate Command:**
1. Generates Python Pydantic v2 models from existing OpenAPI specifications
2. Creates `models.py` files in each subdirectory
3. Uses modern Python features (union operators, standard collections, etc.)

**All Command:**
1. Runs download first, then generate
2. Complete workflow from specs to Python models

The script downloads specs from these endpoints:
- Air API: https://developer.spotnana.com/_spec/openapi/AirApi.yaml?download
- Hotel API: https://developer.spotnana.com/_spec/openapi/HotelApi.yaml?download
- Company API: https://developer.spotnana.com/_spec/openapi/CompanyApi.yaml?download
- Policy API: https://developer.spotnana.com/_spec/openapi/PolicyApi.yaml?download
- Trip API: https://developer.spotnana.com/_spec/openapi/TripApi.yaml?download
- Event API: https://developer.spotnana.com/_spec/openapi/EventApi.yaml?download
- Users API: https://developer.spotnana.com/_spec/openapi/UsersApi.yaml?download

## Requirements

The script requires:
- Python 3.9+
- `aiohttp` package (for downloading specs)
- `datamodel-code-generator` package (for generating models)

These dependencies should already be available in the project's virtual environment. If not, install them:
```bash
uv add aiohttp datamodel-code-generator
```

## Validation Command

```bash
# From the models/api/spotnana/ directory
uv run python manage_spotnana_models.py validate
```

**Validate Command:**
1. Tests all generated Python models by importing them
2. Detects Pydantic validation issues (like discriminated union problems)
3. Provides helpful error messages and guidance for fixing issues
4. Returns proper exit codes for CI/CD integration

## Discriminated Union Issue Resolution

### Root Cause Analysis

**Problem**: Several Spotnana OpenAPI specifications contain discriminated unions that fail Pydantic v2 validation with the error:
```
pydantic.errors.PydanticUserError: Model 'XxxModel' needs field 'fieldName' to be of type `Literal`
```

**Root Cause**: 
Pydantic v2 requires discriminator fields in discriminated unions to have `enum` constraints (which become `Literal` types in Python) to enable proper type discrimination. The Spotnana OpenAPI specs define discriminated unions with `discriminator.propertyName` and `discriminator.mapping`, but the discriminator fields themselves lack `enum` values.

**Example of the issue**:
```yaml
# In OpenAPI spec - BROKEN
CardCondition:
  properties:
    type:
      type: string  # Missing enum constraint!
  discriminator:
    propertyName: type
    mapping:
      CARD: '#/components/schemas/CardCondition'

# What Pydantic v2 needs - FIXED
CardCondition:
  properties:
    type:
      type: string
      enum: [CARD]  # Added enum constraint
  discriminator:
    propertyName: type
    mapping:
      CARD: '#/components/schemas/CardCondition'
```

### Our Design Solution

**Approach**: Automated post-download fixing in `manage_spotnana_models.py`

**Why This Approach**:
1. **Non-invasive**: We don't modify Spotnana's original specs permanently
2. **Maintainable**: Fixes are applied automatically on every download
3. **Traceable**: Clear mapping between discriminator mappings and required enum values
4. **Robust**: Detects existing fixes to avoid duplicate application

**Implementation**:
1. **Download Phase**: After downloading each OpenAPI spec, automatically apply discriminated union fixes
2. **Fix Configuration**: Type-safe configuration mapping domains to specific schema fixes
3. **YAML Processing**: Parse OpenAPI specs, add missing `enum` values based on discriminator mappings
4. **Validation Integration**: Automatically validate generated models to catch new issues

**Fixes Applied**:
- **Air API**: `CardCondition.type = ["CARD"]`, `SeatNoteRemark.remarkType = ["SEAT_SELECTION_REMARK"]`
- **Company API**: `BookingFeeInfo.feeType = ["BOOKING_FEE"]`
- **Trip API**: `BookingFeeInfo.feeType = ["BOOKING_FEE"]`
- **Event API**: `CarPaymentSourceMetadata.travelType = ["CAR"]`

**Results**:
- ✅ All 7 APIs now generate valid Pydantic v2 models
- ✅ 2,715+ total models successfully loaded and validated
- ✅ Automatic detection and fixing of future discriminated union issues

### Alternative Approaches Considered

1. **Manual YAML editing**: Rejected due to maintenance burden and risk of overwriting changes
2. **Pydantic model overrides**: Rejected due to complexity and deviation from OpenAPI specs
3. **Custom model generator**: Rejected due to over-engineering for a targeted issue
4. **Downgrade to Pydantic v1**: Rejected due to missing modern features and security updates
