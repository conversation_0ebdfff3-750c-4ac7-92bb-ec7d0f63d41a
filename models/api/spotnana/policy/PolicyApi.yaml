openapi: 3.1.0
info:
  title: Policy API
  version: v2
servers:
  - url: https://apis.spotnana.com
    description: Staging URL
security:
  - Bearer: []
components:
  securitySchemes:
    Bearer:
      type: http
      scheme: bearer
  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFound:
      description: The specified resource was not found.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
  schemas:
    Action:
      type: object
      title: Action
      oneOf:
        - $ref: '#/components/schemas/PreventBookingWrapper'
    Addon:
      title: Addon
      description: Add-ons which can be selected as part of an air reservation.
      type: string
      enum:
        - SEAT
        - BAGGAGE
        - WIFI
        - CARBON_OFFSET
        - EARLY_BIRD
        - ALL
        - NONE
      example: SEAT
    AirPolicyRules:
      type: object
      title: AirPolicyRules
      description: Details of rules related to air.
      properties:
        restrictedAirlines:
          $ref: '#/components/schemas/RestrictedAirlines'
        restrictedAirCrafts:
          $ref: '#/components/schemas/RestrictedAirCrafts'
        maxFlightBookingPriceByDuration:
          $ref: '#/components/schemas/MaxFlightBookingPriceByDuration'
        flightCabinUpgrade:
          $ref: '#/components/schemas/FlightCabinUpgrade'
        flightAdvanceBookingWindow:
          $ref: '#/components/schemas/FlightAdvanceBookingWindow'
        flightTicketsRefundable:
          $ref: '#/components/schemas/FlightTicketsRefundable'
        flightTicketsChangeable:
          $ref: '#/components/schemas/FlightTicketsChangeable'
        highestFlightCabinByDurationDomestic:
          $ref: '#/components/schemas/HighestFlightCabinByDuration'
        highestFlightCabinByDurationInternational:
          $ref: '#/components/schemas/HighestFlightCabinByDuration'
        highestFlightCabinOvernight:
          $ref: '#/components/schemas/HighestFlightCabinOvernight'
        differenceBetweenFlightFareAndMedianFare:
          $ref: '#/components/schemas/DifferenceBetweenFlightFareAndMedianFare'
        co2EmissionPerPassengerPerKm:
          $ref: '#/components/schemas/Co2EmissionPerPassengerPerKm'
        maxFlightBookingPriceInternational:
          $ref: '#/components/schemas/MaxFlightBookingPrice'
        maxFlightBookingPriceDomestic:
          $ref: '#/components/schemas/MaxFlightBookingPrice'
        lowestLogicalFare:
          $ref: '#/components/schemas/LowestLogicalFare'
    AllowedAirAddonsProps:
      type: object
      title: AllowedAirAddonsProps
      description: >-
        Add-ons which can be booked on a central card as part of an air
        reservation.
      required:
        - addons
      properties:
        addons:
          type: array
          items:
            $ref: '#/components/schemas/Addon'
        paymentSourceAccessTypes:
          type: array
          items:
            $ref: '#/components/schemas/PaymentSourceAccessType'
          default:
            - CENTRALISED
    AllowedAirAddonsPropsWrapper:
      type: object
      title: AllowedAirAddonsPropsWrapper
      properties:
        allowedAirAddonsProps:
          $ref: '#/components/schemas/AllowedAirAddonsProps'
    AllowedCarTypes:
      type: object
      title: AllowedCarTypes
      description: >
        Car rule to describe whether to use default policy rule or use different
        set of 

        allowed car types for booking.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/AllowedCarTypesPropsWrapper'
    AllowedCarTypesProps:
      type: object
      title: AllowedCarTypesProps
      description: List of allowed car types.
      required:
        - carTypes
      properties:
        carTypes:
          type: array
          items:
            $ref: '#/components/schemas/CarType'
    AllowedCarTypesPropsWrapper:
      type: object
      title: AllowedCarTypesPropsWrapper
      properties:
        allowedCarTypesProps:
          $ref: '#/components/schemas/AllowedCarTypesProps'
    ApprovalCondition:
      type: object
      title: ApprovalCondition
      description: >-
        Rules which should be satisfied to use an approval action. There is AND
        relationship between rules.
      properties:
        rules:
          type: array
          items:
            $ref: '#/components/schemas/ApprovalRule'
    ApprovalRule:
      type: object
      title: ApprovalRule
      description: One of rule which should be satisfied to use an approval action.
      oneOf:
        - $ref: '#/components/schemas/TravelRegionRuleWrapper'
    ArrayOfReference:
      type: object
      properties:
        length:
          type: integer
          format: int32
        elements:
          type: array
          description: List of references containing id and name.
          items:
            $ref: '#/components/schemas/Reference'
        totalNumResults:
          type: integer
          format: int32
          description: Total number of results
    Cabin:
      title: Cabin
      description: Flight cabin
      type: string
      enum:
        - UNKNOWN_CABIN
        - ECONOMY
        - PREMIUM_ECONOMY
        - BUSINESS
        - FIRST
      example: ECONOMY
    CabinClassNotAllowedDomesticProps:
      allOf:
        - $ref: '#/components/schemas/CabinClassNotAllowedProps'
    CabinClassNotAllowedDomesticPropsWrapper:
      type: object
      title: CabinClassNotAllowedDomesticPropsWrapper
      properties:
        cabinClassNotAllowedDomesticProps:
          $ref: '#/components/schemas/CabinClassNotAllowedDomesticProps'
    CabinClassNotAllowedInternationalProps:
      allOf:
        - $ref: '#/components/schemas/CabinClassNotAllowedProps'
    CabinClassNotAllowedInternationalPropsWrapper:
      type: object
      title: CabinClassNotAllowedInternationalPropsWrapper
      properties:
        cabinClassNotAllowedInternationalProps:
          $ref: '#/components/schemas/CabinClassNotAllowedInternationalProps'
    CabinClassNotAllowedProps:
      type: object
      title: CabinClassNotAllowedProps
      description: List of cabins which should not be allowed.
      required:
        - cabins
      properties:
        cabins:
          type: array
          items:
            $ref: '#/components/schemas/Cabin'
    CarEngineTypesNotAllowedProps:
      allOf:
        - $ref: '#/components/schemas/CarEngineTypesProps'
    CarEngineTypesNotAllowedPropsWrapper:
      type: object
      title: CarEngineTypesNotAllowedPropsWrapper
      properties:
        carEngineTypesNotAllowedProps:
          $ref: '#/components/schemas/CarEngineTypesNotAllowedProps'
    CarEngineTypesProps:
      type: object
      title: CarEngineTypesProps
      description: List of car engine types.
      required:
        - carEngineTypes
      properties:
        carEngineTypes:
          type: array
          items:
            $ref: '#/components/schemas/EngineType'
    CarPolicyRules:
      type: object
      title: CarPolicyRules
      description: Details of rules related to car.
      properties:
        maxCarPricePerNumberOfDays:
          $ref: '#/components/schemas/MaxCarPricePerNumberOfDays'
        allowedCarTypes:
          $ref: '#/components/schemas/AllowedCarTypes'
    CarType:
      type: string
      title: CarType
      description: Car type.
      enum:
        - OTHER
        - MINI
        - ECONOMY
        - COMPACT
        - MID_SIZE
        - STANDARD
        - FULL_SIZE
        - PREMIUM
        - LUXURY
        - CONVERTIBLE
        - MINIVAN
        - SUV
        - VAN
        - PICKUP
        - SPORTS
        - SPECIAL
        - RECREATIONAL_VEHICLE
        - WAGON
      example: ECONOMY
    CarTypesNotAllowedProps:
      allOf:
        - $ref: '#/components/schemas/AllowedCarTypesProps'
    CarTypesNotAllowedPropsWrapper:
      type: object
      title: CarTypesNotAllowedPropsWrapper
      properties:
        carTypesNotAllowedProps:
          $ref: '#/components/schemas/CarTypesNotAllowedProps'
    Co2EmissionPerPassengerPerKm:
      type: object
      title: Co2EmissionPerPassengerPerKm
      description: >
        Air rule to describe whether to use default policy or different rule
        regarding CO2 emission.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/Co2EmissionPerPassengerPerKmPropsWrapper'
    Co2EmissionPerPassengerPerKmProps:
      type: object
      title: Co2EmissionPerPassengerPerKmProps
      description: >
        Maximum allowed Co2 emission (in grams) per passenger per flight
        distance covered (in Kms).
      required:
        - emission
      properties:
        emission:
          type: number
          format: double
    Co2EmissionPerPassengerPerKmPropsWrapper:
      type: object
      title: Co2EmissionPerPassengerPerKmPropsWrapper
      properties:
        co2EmissionPerPassengerPerKmProps:
          $ref: '#/components/schemas/Co2EmissionPerPassengerPerKmProps'
    CommonPolicyRules:
      type: object
      title: CommonPolicyRules
      description: Details of policy that is common to all travel type.
      properties:
        restrictedContinents:
          $ref: '#/components/schemas/RestrictedContinents'
        restrictedCountries:
          $ref: '#/components/schemas/RestrictedCountries'
    CompanyConfigSource:
      type: object
      title: CompanyConfigSource
      description: >-
        For this option source, options would be auto generated based on
        specified parameter.
      required:
        - optionsParam
      properties:
        optionsParam:
          $ref: '#/components/schemas/CustomFieldOptionsParam'
    CompanyConfigSourceWrapper:
      type: object
      title: CompanyConfigSourceWrapper
      description: Wrapper for option source company config.
      properties:
        companyConfig:
          $ref: '#/components/schemas/CompanyConfigSource'
    ContinentProps:
      type: object
      title: ContinentProps
      description: List of continent with their type of restriction.
      required:
        - restrictions
      properties:
        restrictions:
          type: array
          items:
            $ref: '#/components/schemas/ContinentRestriction'
    ContinentPropsWrapper:
      type: object
      title: ContinentPropsWrapper
      properties:
        continentProps:
          $ref: '#/components/schemas/ContinentProps'
    ContinentRestriction:
      type: object
      title: ContinentRestriction
      description: Continent code with type of restriction.
      required:
        - type
        - continentCode
      properties:
        type:
          type: string
          enum:
            - BLOCK_TRAVEL_TO_CONTINENT
            - ALLOW_WITHIN_CONTINENT
        continentCode:
          type: string
          description: 2-letter continent code.
          example: NA
    CountryProps:
      type: object
      title: CountryProps
      description: List of country related restrictions.
      required:
        - restrictions
      properties:
        restrictions:
          type: array
          items:
            $ref: '#/components/schemas/CountryRestriction'
    CountryPropsWrapper:
      type: object
      title: CountryPropsWrapper
      properties:
        countryProps:
          $ref: '#/components/schemas/CountryProps'
    CountryRestriction:
      type: object
      title: CountryRestriction
      description: Country with their restriction type.
      required:
        - type
        - countryCode
      properties:
        type:
          type: string
          enum:
            - BLOCK_TRAVEL_TO_COUNTRY
            - ALLOW_WITHIN_COUNTRY
        countryCode:
          type: string
          description: ISO 2-letter country code.
    CreatePolicyRequest:
      type: object
      title: Create Policy Request.
      description: Request containing details of policy to be created.
      required:
        - name
        - type
        - currency
        - userGroups
      properties:
        name:
          type: string
          description: Name of the policy.
        type:
          $ref: '#/components/schemas/PolicyType'
        parentPolicyId:
          type: string
          format: uuid
          description: >-
            Policy to inherit the values of linked rules. If empty, consider the
            company default policy.
          example: f7b3b3b3-7b3b-4b3b-8b3b-3b3b3b3b3b3b
        currency:
          $ref: '#/components/schemas/PolicyCurrency'
        inPolicyApprovalInfo:
          deprecated: true
          $ref: '#/components/schemas/PolicyApprovalInfo'
        outOfPolicyApprovalInfo:
          deprecated: true
          $ref: '#/components/schemas/PolicyApprovalInfo'
        inPolicyApprovalInfos:
          type: array
          items:
            $ref: '#/components/schemas/PolicyApprovalInfo'
        outOfPolicyApprovalInfos:
          type: array
          items:
            $ref: '#/components/schemas/PolicyApprovalInfo'
        outOfPolicyAgentApprovalInfos:
          type: array
          items:
            $ref: '#/components/schemas/PolicyApprovalInfo'
        enableAgentActionOverride:
          type: boolean
          description: >-
            True if agent can override the PREVENT_BOOKING action. If enabled,
            outOfPolicyAgentApprovalInfos for all travel types should be
            provided.
        userGroups:
          type: array
          items:
            $ref: '#/components/schemas/UserGroup'
        commonPolicyRules:
          $ref: '#/components/schemas/CommonPolicyRules'
        carPolicyRules:
          $ref: '#/components/schemas/CarPolicyRules'
        hotelPolicyRules:
          $ref: '#/components/schemas/HotelPolicyRules'
        airPolicyRules:
          $ref: '#/components/schemas/AirPolicyRules'
        isRestrictive:
          deprecated: true
          type: boolean
          description: True if policy is a restrictive policy.
          example: true
        rules:
          type: array
          description: >-
            A list of general policy rules for all travel types air, hotel, car
            and rail.
          items:
            $ref: '#/components/schemas/UniversalRule'
        airOopReasonCodes:
          $ref: '#/components/schemas/OopReasonCodes'
          description: >-
            Out of policy reason code question to be asked to user for air
            booking.
        hotelOopReasonCodes:
          $ref: '#/components/schemas/OopReasonCodes'
          description: >-
            Out of policy reason code question to be asked to user for hotel
            booking.
        carOopReasonCodes:
          $ref: '#/components/schemas/OopReasonCodes'
          description: >-
            Out of policy reason code question to be asked to user for car
            booking.
        railOopReasonCodes:
          $ref: '#/components/schemas/OopReasonCodes'
          description: >-
            Out of policy reason code question to be asked to user for rail
            booking.
        category:
          $ref: '#/components/schemas/PolicyCategory'
          description: Category of the policy.
          default: EMPLOYEE
    CreateQuestionRequest:
      type: object
      title: CreateQuestionRequest
      description: Request for question creation.
      required:
        - name
        - questionFormat
        - isRequired
        - isDisabled
      properties:
        name:
          type: string
          description: >-
            Question display name that the user will see. For eg, 'Choose the
            purpose of your trip'.
        questionFormat:
          $ref: '#/components/schemas/QuestionFormat'
        optionInfo:
          $ref: '#/components/schemas/OptionInfo'
        isRequired:
          type: boolean
          description: Whether its compulsory to answer the question or not.
          default: false
          example: true
        isDisabled:
          type: boolean
          description: >-
            Whether the question is disabled or not. If true, this should not be
            asked.
          default: true
          example: false
        includeInItinerary:
          type: boolean
          description: Whether to include this question in the itinerary related emails.
          default: false
          example: true
          deprecated: true
        customFieldLocations:
          type: array
          items:
            $ref: '#/components/schemas/CustomFieldLocation'
        matchConditions:
          $ref: '#/components/schemas/CustomFieldMatchConditions'
    CurrencyCodeWrapper:
      type: object
      title: CurrencyCodeWrapper
      properties:
        currencyCode:
          type: string
          description: iso code of the currency.
          example: USD
    CustomFieldId:
      type: object
      title: CustomFieldId
      description: The custom field type and the id associated with it.
      required:
        - type
        - externalId
      properties:
        type:
          $ref: '#/components/schemas/CustomFieldType'
        externalId:
          type: string
          description: Meeting id or budget id based on custom field type.
    CustomFieldLocation:
      title: CustomFieldLocation
      description: Display the custom fields in all these locations.
      type: string
      enum:
        - POLICY_APPROVAL_EMAIL
        - PNR_EMAIL
        - TRIP_EMAIL
      example: POLICY_APPROVAL_EMAIL
    CustomFieldMatchConditions:
      type: object
      title: CustomFieldMatchConditions
      description: Conditions to select the custom field for given context.
      properties:
        travelerConditions:
          $ref: '#/components/schemas/TravelerMatchConditions'
        travelTypes:
          type: array
          description: Travel types to match.
          items:
            $ref: '#/components/schemas/TravelType'
        travelRegionTypes:
          type: array
          description: Travel region types to match.
          items:
            $ref: '#/components/schemas/TravelRegionType'
        tripUsageTypes:
          type: array
          description: >-
            Trip usage types to match. If empty, all trip usage types will be
            matched.
          items:
            $ref: '#/components/schemas/TripUsageType'
    CustomFieldOptionsParam:
      type: string
      title: CustomFieldOptionsParam
      enum:
        - COST_CENTER
        - LEGAL_ENTITY
        - OFFICE
        - DEPARTMENT
      description: Parameter to form options for the custom field.
      example: COST_CENTER
    CustomFieldType:
      type: string
      description: The type of custom field.
      enum:
        - QUESTION
        - MEETING
        - BUDGET
        - BREX_TOKEN
      default: QUESTION
    DateTimeOffset:
      title: DateTimeOffset
      description: ISO8601 UTC Date Time
      type: object
      required:
        - iso8601
      properties:
        iso8601:
          type: string
          pattern: >-
            ^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?(Z|([+-](0[0-9]|1[0-4]):([0-5][0-9])))$
          example: 2017-07-21T17:32Z
    DifferenceBetweenFlightFareAndMedianFare:
      type: object
      title: DifferenceBetweenFlightFareAndMedianFare
      description: >
        Air rule to describe whether to use default policy or different rule for
        difference between

        a flight fare and median fare of the flights for a destination.

        Note: Median fare was calculated based on number of stops, cabin class
        and flight duration.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: >-
            #/components/schemas/DifferenceBetweenFlightFareAndMedianFarePropsWrapper
    DifferenceBetweenFlightFareAndMedianFareProps:
      type: object
      title: DifferenceBetweenFlightFareAndMedianFareProps
      description: |
        Allowed difference between flight's total fare and median fare for all
        flights for that destination.
      required:
        - percentage
      properties:
        percentage:
          description: >
            Percentage difference between flight's total fare and median fare
            for all

            flights for that destination.
          type: integer
          format: int32
    DifferenceBetweenFlightFareAndMedianFarePropsWrapper:
      type: object
      title: DifferenceBetweenFlightFareAndMedianFarePropsWrapper
      properties:
        differenceBetweenFlightFareAndMedianFareProps:
          $ref: '#/components/schemas/DifferenceBetweenFlightFareAndMedianFareProps'
    DifferenceValue:
      type: object
      title: Different Types Of Values.
      oneOf:
        - $ref: '#/components/schemas/SimpleMoneyWrapper'
        - $ref: '#/components/schemas/Percentage'
    DoubleRange:
      type: object
      properties:
        min:
          type: number
          format: double
          description: Minimum value - inclusive.
        max:
          type: number
          format: double
          description: Maximum value - inclusive.
    EngineType:
      type: string
      title: EngineType
      description: Engine types.
      enum:
        - UNKNOWN_ENGINE
        - PETROL
        - DIESEL
        - ELECTRIC
        - CNG
        - HYBRID
        - HYDROGEN
        - MULTI_FUEL
        - ETHANOL
      example: PETROL
    EntityId:
      type: object
      description: Identifier of an object
      required:
        - id
      properties:
        id:
          type: string
          format: uuid
    ErrorParameter:
      type: object
      title: ErrorParameter
      description: Error parameter
      properties:
        name:
          type: string
          description: Parameter name
        value:
          type: string
          description: Parameter value
    ErrorResponse:
      type: object
      properties:
        debugIdentifier:
          type: string
          description: Link to debug the error internally.
        errorMessages:
          type: array
          items:
            type: object
            properties:
              errorCode:
                type: string
                description: Error code to identify the specific errors.
              message:
                type: string
                description: Message containing details of error.
              errorParameters:
                type: array
                description: Error message parameters.
                items:
                  $ref: '#/components/schemas/ErrorParameter'
              errorDetail:
                type: string
                description: More details about the error.
    FlightAdvanceBookingWindow:
      type: object
      title: FlightAdvanceBookingWindow
      description: >
        Air rule to describe whether to use default policy or a different
        advance booking window for

        a flight.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/FlightAdvanceBookingWindowPropsWrapper'
    FlightAdvanceBookingWindowDomesticProps:
      type: object
      title: FlightAdvanceBookingWindowDomesticProps
      description: Number of days in advance one can book a domestic flight.
      required:
        - numDaysInAdvance
      properties:
        numDaysInAdvance:
          type: integer
          format: int32
    FlightAdvanceBookingWindowDomesticPropsWrapper:
      type: object
      title: FlightAdvanceBookingWindowDomesticPropsWrapper
      properties:
        flightAdvanceBookingWindowDomesticProps:
          $ref: '#/components/schemas/FlightAdvanceBookingWindowDomesticProps'
    FlightAdvanceBookingWindowInternationalProps:
      type: object
      title: FlightAdvanceBookingWindowInternationalProps
      description: Number of days in advance one can book an international flight.
      required:
        - numDaysInAdvance
      properties:
        numDaysInAdvance:
          type: integer
          format: int32
    FlightAdvanceBookingWindowInternationalPropsWrapper:
      type: object
      title: FlightAdvanceBookingWindowInternationalPropsWrapper
      properties:
        flightAdvanceBookingWindowInternationalProps:
          $ref: '#/components/schemas/FlightAdvanceBookingWindowInternationalProps'
    FlightAdvanceBookingWindowProps:
      type: object
      title: FlightAdvanceBookingWindowProps
      description: Number of days in advance one can book a flight.
      required:
        - numDaysInAdvance
      properties:
        numDaysInAdvance:
          type: integer
          format: int32
    FlightAdvanceBookingWindowPropsWrapper:
      type: object
      title: FlightAdvanceBookingWindowPropsWrapper
      properties:
        flightAdvanceBookingWindowProps:
          $ref: '#/components/schemas/FlightAdvanceBookingWindowProps'
    FlightCabinUpgrade:
      type: object
      title: FlightCabinUpgrade
      description: >
        Air rule to describe whether to use default policy or specific rules
        regarding flight cabin 

        upgrade.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/FlightCabinUpgradePropsWrapper'
    FlightCabinUpgradeProps:
      type: object
      title: FlightCabinUpgradeProps
      description: Details whether flight cabin upgrade is allowed or not if cheaper.
      required:
        - isAllowed
      properties:
        isAllowed:
          type: boolean
    FlightCabinUpgradePropsWrapper:
      type: object
      title: FlightCabinUpgradePropsWrapper
      properties:
        flightCabinUpgradeProps:
          $ref: '#/components/schemas/FlightCabinUpgradeProps'
    FlightTicketsChangeable:
      type: object
      title: FlightTicketsChangeable
      description: >
        Air rule to describe whether to use default policy or different rule
        regarding flight 

        tickets exchange.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/FlightTicketsChangeablePropsWrapper'
    FlightTicketsChangeableProps:
      type: object
      title: FlightTicketsChangeableProps
      description: >-
        Different type of ticket exchange policy allowed for flight ticket
        booking.
      required:
        - type
      properties:
        type:
          type: string
          enum:
            - NONE
            - CHANGEABLE
            - CHANGEABLE_WITH_PENALTY
            - DONT_ALLOW
    FlightTicketsChangeablePropsWrapper:
      type: object
      title: FlightTicketsChangeablePropsWrapper
      properties:
        flightTicketsChangeableProps:
          $ref: '#/components/schemas/FlightTicketsChangeableProps'
    FlightTicketsRefundable:
      type: object
      title: FlightTicketsRefundable
      description: >
        Air rule to describe whether to use default policy or different rule
        regarding refundable

        flight tickets.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/FlightTicketsRefundablePropsWrapper'
    FlightTicketsRefundableProps:
      type: object
      title: FlightTicketsRefundableProps
      description: Different type of refund policy allowed for flight ticket booking.
      required:
        - type
      properties:
        type:
          type: string
          enum:
            - NONE
            - REFUNDABLE
            - REFUNDABLE_WITH_PENALTY
            - DONT_ALLOW
    FlightTicketsRefundablePropsWrapper:
      type: object
      title: FlightTicketsRefundablePropsWrapper
      properties:
        flightTicketsRefundableProps:
          $ref: '#/components/schemas/FlightTicketsRefundableProps'
    HighestCabinForDuration:
      type: object
      title: HighestCabinForDuration
      description: For a given duration of a flight, the highest cabin that can be booked.
      required:
        - cabin
        - durationRange
      properties:
        cabin:
          $ref: '#/components/schemas/Cabin'
        durationRange:
          $ref: '#/components/schemas/Int32Range'
    HighestFlightCabinByDuration:
      type: object
      title: HighestFlightCabinByDuration
      description: >
        Air rule to describe whether to use default policy or different set of
        rules for cabin based

        on flight duration.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/HighestFlightCabinByDurationPropsWrapper'
    HighestFlightCabinByDurationProps:
      type: object
      title: HighestFlightCabinByDurationProps
      description: List of highest cabin that can be booked for different flight durations.
      required:
        - highestCabinForDurationList
      properties:
        highestCabinForDurationList:
          type: array
          items:
            $ref: '#/components/schemas/HighestCabinForDuration'
    HighestFlightCabinByDurationPropsWrapper:
      type: object
      title: HighestFlightCabinByDurationPropsWrapper
      properties:
        highestFlightCabinByDurationProps:
          $ref: '#/components/schemas/HighestFlightCabinByDurationProps'
    HighestFlightCabinOvernight:
      type: object
      title: HighestFlightCabinOvernight
      description: >
        Air rule to describe whether to use default policy or different cabin
        rule for overnight 

        flight.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/HighestFlightCabinOvernightPropsWrapper'
    HighestFlightCabinOvernightProps:
      type: object
      title: HighestFlightCabinOvernightProps
      description: Highest cabin that can be booked for overnight flight.
      required:
        - cabin
      properties:
        cabin:
          $ref: '#/components/schemas/Cabin'
        overnightTimeParams:
          $ref: '#/components/schemas/OvernightTimeParams'
    HighestFlightCabinOvernightPropsWrapper:
      type: object
      title: HighestFlightCabinOvernightPropsWrapper
      properties:
        highestFlightCabinOvernightProps:
          $ref: '#/components/schemas/HighestFlightCabinOvernightProps'
    HighestRailTravelClassByDurationProps:
      type: object
      title: HighestRailTravelClassByDurationProps
      description: >-
        List of highest travel class that can be booked for different rail
        durations.
      required:
        - highestTravelClassForDurationList
      properties:
        highestTravelClassForDurationList:
          type: array
          items:
            $ref: '#/components/schemas/HighestTravelClassForDuration'
    HighestRailTravelClassByDurationPropsWrapper:
      type: object
      title: HighestRailTravelClassByDurationPropsWrapper
      properties:
        highestRailTravelClassByDurationProps:
          $ref: '#/components/schemas/HighestRailTravelClassByDurationProps'
    HighestTravelClassForDuration:
      type: object
      title: HighestTravelClassForDuration
      description: >-
        For a given duration of a rail, the highest travel class that can be
        booked.
      required:
        - travelClass
        - durationRange
      properties:
        travelClass:
          $ref: '#/components/schemas/RailTravelClass'
        durationRange:
          $ref: '#/components/schemas/Int32Range'
    HotelAdvanceBookingWindow:
      type: object
      title: HotelAdvanceBookingWindow
      description: >
        Hotel rule to describe whether to use default policy or different rule
        for advance booking.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/HotelAdvanceBookingWindowPropsWrapper'
    HotelAdvanceBookingWindowProps:
      type: object
      title: HotelAdvanceBookingWindowProps
      description: Number of advance days for a hotel booking.
      required:
        - numDaysInAdvance
      properties:
        numDaysInAdvance:
          type: integer
          format: int32
    HotelAdvanceBookingWindowPropsWrapper:
      type: object
      title: HotelAdvanceBookingWindowPropsWrapper
      properties:
        hotelAdvanceBookingWindowProps:
          $ref: '#/components/schemas/HotelAdvanceBookingWindowProps'
    HotelCancellation:
      type: object
      title: HotelCancellation
      description: >
        Hotel rule to describe whether to use default policy or allow booking of
        any specific type 

        of cancellation.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/HotelCancellationPropsWrapper'
    HotelCancellationProps:
      type: object
      title: HotelCancellationProps
      description: Different types of cancellation rules for hotel booking.
      required:
        - type
      properties:
        type:
          type: string
          enum:
            - NONE
            - REFUNDABLE
            - REFUNDABLE_WITH_PENALTY
            - DONT_ALLOW
    HotelCancellationPropsWrapper:
      type: object
      title: HotelCancellationPropsWrapper
      properties:
        hotelCancellationProps:
          $ref: '#/components/schemas/HotelCancellationProps'
    HotelChainCodes:
      type: object
      title: HotelChainCodes
      description: >
        Hotel rule to describe whether to use default policy or use a different
        set of allowed hotel 

        chain codes.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/HotelChainCodesPropsWrapper'
    HotelChainCodesProps:
      type: object
      title: HotelChainCodesProps
      description: List of hotel chain codes.
      required:
        - hotelChainCodes
      properties:
        hotelChainCodes:
          type: array
          items:
            type: string
            description: Unique chain codes of the hotel.
    HotelChainCodesPropsWrapper:
      type: object
      title: HotelChainCodesPropsWrapper
      properties:
        hotelChainCodesProps:
          $ref: '#/components/schemas/HotelChainCodesProps'
    HotelCodesProps:
      type: object
      title: HotelCodesProps
      description: List of hotel codes.
      required:
        - hotelCodes
      properties:
        hotelCodes:
          type: array
          items:
            type: string
            description: Unique code of the hotel.
    HotelCodesPropsWrapper:
      type: object
      title: HotelCodesPropsWrapper
      properties:
        hotelCodesProps:
          $ref: '#/components/schemas/HotelCodesProps'
    HotelMedianRate:
      type: object
      title: HotelMedianRate
      description: >
        List of attributes/filters to find candidate hotels for calculating
        median

        rate in hotel search.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/HotelMedianRatePropsWrapper'
    HotelMedianRateProps:
      type: object
      title: HotelMedianRateProps
      description: Props for hotel median rate calculation.
      required:
        - searchRadius
        - ratingRange
      properties:
        searchRadius:
          description: Search radius within which candidate hotel has to be.
          $ref: '#/components/schemas/Length'
        ratingRange:
          description: Rating range of candidate hotels.
          $ref: '#/components/schemas/DoubleRange'
    HotelMedianRatePropsWrapper:
      type: object
      title: HotelMedianRatePropsWrapper
      properties:
        hotelMedianRateProps:
          $ref: '#/components/schemas/HotelMedianRateProps'
    HotelPolicyRules:
      type: object
      title: HotelPolicyRules
      description: Details of rules related to hotel.
      properties:
        restrictedHotels:
          $ref: '#/components/schemas/RestrictedHotels'
        maxHotelPriceByLocation:
          $ref: '#/components/schemas/MaxHotelPriceByLocation'
        hotelAdvanceBookingWindow:
          $ref: '#/components/schemas/HotelAdvanceBookingWindow'
        hotelCancellation:
          $ref: '#/components/schemas/HotelCancellation'
        hotelChainCodes:
          $ref: '#/components/schemas/HotelChainCodes'
        hotelStarRating:
          $ref: '#/components/schemas/HotelStarRating'
        hotelMedianRateNightly:
          $ref: '#/components/schemas/HotelMedianRate'
    HotelRateConditionsNotAllowedProps:
      type: object
      title: HotelRateConditionsNotAllowedProps
      description: >-
        List of cancellation types and payment options which should not be
        allowed.
      required:
        - types
      properties:
        types:
          type: array
          items:
            type: string
            enum:
              - NON_REFUNDABLE
              - PREPAID
              - REQUIRES_DEPOSIT
              - PAY_AT_PROPERTY
    HotelRateConditionsNotAllowedPropsWrapper:
      type: object
      title: HotelRateConditionsNotAllowedPropsWrapper
      properties:
        hotelRateConditionsNotAllowedProps:
          $ref: '#/components/schemas/HotelRateConditionsNotAllowedProps'
    HotelRestrictedKeyword:
      type: object
      title: HotelRestrictedKeyword
      description: Keyword that should not be allowed in hotel name/description.
      required:
        - keyword
        - reason
      properties:
        keyword:
          type: string
          description: Keyword that should not be allowed in hotel name/description.
        reason:
          type: string
          description: Reason for not allowing the keyword.
    HotelRestrictedKeywordsProps:
      type: object
      title: HotelRestrictedKeywordsProps
      description: List of keywords that should not be allowed in hotel name/description.
      properties:
        hotelRestrictedKeywordsList:
          type: array
          items:
            $ref: '#/components/schemas/HotelRestrictedKeyword'
    HotelRestrictedKeywordsPropsWrapper:
      type: object
      title: HotelRestrictedKeywordsPropsWrapper
      properties:
        hotelRestrictedKeywordsProps:
          $ref: '#/components/schemas/HotelRestrictedKeywordsProps'
    HotelStarRating:
      type: object
      title: HotelStarRatingRule
      description: >
        Hotel rule to describe whether to use default policy rule or use the
        specific range of star 

        rated hotels.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/HotelStarRatingPropsWrapper'
    HotelStarRatingProps:
      type: object
      title: HotelStar RatingProps
      description: Range of hotel star ratings.
      required:
        - ratingRange
      properties:
        ratingRange:
          $ref: '#/components/schemas/DoubleRange'
    HotelStarRatingPropsWrapper:
      type: object
      title: HotelRatingPropsWrapper
      properties:
        hotelStarRatingProps:
          $ref: '#/components/schemas/HotelStarRatingProps'
    HrFeedPolicyDetails:
      type: object
      title: User Group Via Hr Feed Details.
      description: User group details if membership is via HR feed.
      properties:
        totalMemberCount:
          type: integer
          format: int32
          description: Total member count configured via HR feed.
    Int32Range:
      type: object
      properties:
        min:
          type: integer
          format: int32
          description: Minimum value - inclusive.
        max:
          type: integer
          format: int32
          description: Maximum value - inclusive.
    IsLinkedWrapper:
      type: object
      title: IsLinkedWrapper
      properties:
        isLinked:
          type: boolean
          description: If the value has to be taken from default policy.
          example: true
    Latlng:
      title: Latlng
      description: Latitude and Longitude for a Location
      type: object
      required:
        - latitude
        - longitude
      properties:
        latitude:
          type: number
          description: Latitude of the Location
          format: double
          example: 77.1025
        longitude:
          type: number
          description: Longitude of the Location
          format: double
          example: 28.7041
    Length:
      type: object
      title: Length
      description: Specifies the length or a distance.
      required:
        - length
        - unit
      properties:
        length:
          type: number
          description: Distance from search point.
          format: double
          example: 150
        unit:
          type: string
          description: Unit of measure being applied.
          enum:
            - UNKNOWN_UNIT
            - KM
            - MILE
          example: MILE
    LowestFarePerHotelPropertyProps:
      type: object
      title: LowestFarePerHotelPropertyProps
      description: Props for lowest fare per hotel property rule.
      required:
        - onlyAllowLowestFare
      properties:
        onlyAllowLowestFare:
          type: boolean
          description: If true, only the lowest fare per hotel property will be allowed.
    LowestFarePerHotelPropertyPropsWrapper:
      type: object
      title: LowestFarePerHotelPropertyPropsWrapper
      properties:
        lowestFarePerHotelPropertyProps:
          $ref: '#/components/schemas/LowestFarePerHotelPropertyProps'
    LowestLogicalFare:
      type: object
      title: LowestLogicalFare
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/LowestLogicalFarePropsWrapper'
    LowestLogicalFareProps:
      type: object
      title: LowestLogicalFareProps
      description: >-
        Properties which will be used to filter candidates for a given flight in
        order to calculate lowest possible fare for the group.
      properties:
        flightTimeWindowInHoursDomestic:
          description: >-
            Time window (in hours) applied at both departure and arrival to get
            time bounds of candidate flights for domestic travel.
          type: integer
          format: int32
          deprecated: true
        flightTimeWindowInHoursInternational:
          description: >-
            Time window (in hours) applied at both departure and arrival to get
            time bounds of candidate flights for international travel.
          type: integer
          format: int32
          deprecated: true
        maxLayoverDurationInHoursDomestic:
          description: >-
            Maximum layover duration (in hours) possible for candidate flights
            for domestic travel.
          type: integer
          format: int32
          deprecated: true
        maxLayoverDurationInHoursInternational:
          description: >-
            Maximum layover duration (in hours) possible for candidate flights
            for international travel.
          type: integer
          format: int32
          deprecated: true
        maxLayoverDurationMinutesDomestic:
          description: >-
            Maximum layover duration (in minutes) possible for candidate flights
            for domestic travel.
          type: integer
          format: int32
          example: 120
        maxLayoverDurationMinutesInternational:
          description: >-
            Maximum layover duration (in minutes) possible for candidate flights
            for international travel.
          type: integer
          format: int32
          example: 150
        flightTimeWindowMinutesDomestic:
          description: |
            Time window (in minutes) applied at both departure and arrival 
            to get time bounds of candidate flights for domestic travel.
          type: integer
          format: int32
          example: 60
        flightTimeWindowMinutesInternational:
          description: |
            Time window (in minutes) applied at both departure and arrival 
            to get time bounds of candidate flights for international travel.
          type: integer
          format: int32
          example: 90
        maxNumberOfStops:
          description: >-
            Maximum number of stops possible in a leg to be for it to be
            considered as a valid llf candidate.
          type: string
          enum:
            - ANY
            - ONE_OR_LESS
            - TWO_OR_LESS
            - FEWEST
        airportConnectionChanges:
          description: Is more than one distinct airport possible in a leg.
          type: string
          enum:
            - NOT_ALLOWED
        carrier:
          description: Carriers which may be excluded / included for llf calculation.
          type: object
          properties:
            type:
              description: Whether to include / exclude carriers.
              type: string
              enum:
                - PREFERRED
                - EXCLUDE
            airlines:
              description: List of airline codes.
              type: array
              items:
                type: string
    LowestLogicalFarePropsWrapper:
      type: object
      title: LlfPropsWrapper
      properties:
        props:
          $ref: '#/components/schemas/LowestLogicalFareProps'
    MaxCarPricePerNumberOfDays:
      type: object
      title: MaxCarPricePerNumberOfDays
      description: |
        Car rule to describe whether to use default policy rules 
        or list of rules for max price for given number of days.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/MaxCarPricePerNumberOfDaysPropsWrapper'
    MaxCarPricePerNumberOfDaysProps:
      type: object
      title: MaxCarPricePerNumberOfDaysProps
      description: Max price for given number days for a car booking.
      required:
        - maxPrice
      properties:
        maxPrice:
          $ref: '#/components/schemas/SimpleMoney'
        isTaxIncluded:
          type: boolean
          default: false
    MaxCarPricePerNumberOfDaysPropsWrapper:
      type: object
      title: MaxCarPricePerNumberOfDaysPropsWrapper
      properties:
        maxCarPricePerNumberOfDaysProps:
          $ref: '#/components/schemas/MaxCarPricePerNumberOfDaysProps'
    MaxFlightBookingPrice:
      type: object
      title: MaxFlightBookingPrice
      description: Max allowed flight booking price for that selected cabin.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/MaxFlightBookingPricePropsWrapper'
    MaxFlightBookingPriceByDuration:
      type: object
      title: MaxFlightBookingPriceByDuration
      description: >
        Air rule to describe whether to use default policy or different set of
        max booking price

        for different flight durations.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/MaxFlightBookingPriceByDurationPropsWrapper'
    MaxFlightBookingPriceByDurationProps:
      type: object
      title: MaxFlightBookingPriceByDurationProps
      description: List of max price of flight booking for different flight duration.
      required:
        - maxPricePerDurationList
      properties:
        maxPricePerDurationList:
          type: array
          items:
            $ref: '#/components/schemas/MaxFlightPricePerDuration'
        isTaxIncluded:
          type: boolean
          default: false
    MaxFlightBookingPriceByDurationPropsWrapper:
      type: object
      title: MaxFlightBookingPriceByDurationPropsWrapper
      properties:
        maxFlightBookingPriceByDurationProps:
          $ref: '#/components/schemas/MaxFlightBookingPriceByDurationProps'
    MaxFlightBookingPriceProps:
      type: object
      title: MaxFlightBookingPriceProps
      description: >
        Air rule to describing how the maximum allowed flight price will be
        calculated and set for policy.
      required:
        - type
        - sign
        - difference
      properties:
        type:
          type: string
          enum:
            - MINIMUM
            - MEDIAN
            - LOWEST_LOGICAL_FARE
        sign:
          type: string
          enum:
            - MORE
            - LESS
        difference:
          $ref: '#/components/schemas/DifferenceValue'
        isTaxIncluded:
          type: boolean
          default: true
    MaxFlightBookingPricePropsWrapper:
      type: object
      title: MaxFlightBookingPricePropsWrapper
      properties:
        maxFlightBookingPriceProps:
          $ref: '#/components/schemas/MaxFlightBookingPriceProps'
    MaxFlightPricePerDuration:
      type: object
      title: MaxFlightPricePerDuration
      description: Maximum price of a flight booking for given duration.
      required:
        - maxPrice
        - durationRange
      properties:
        maxPrice:
          $ref: '#/components/schemas/SimpleMoney'
        durationRange:
          $ref: '#/components/schemas/Int32Range'
    MaxHotelBookingPriceProps:
      type: object
      title: MaxHotelBookingPriceProps
      description: >-
        Maximum allowed price for a hotel booking based on already calculated
        rate statistics such as median price.
      required:
        - difference
        - type
      properties:
        difference:
          $ref: '#/components/schemas/DifferenceValue'
        isTaxIncluded:
          type: boolean
          default: true
        type:
          type: string
          enum:
            - MORE_THAN_MEDIAN
            - LESS_THAN_MEDIAN
    MaxHotelBookingPricePropsWrapper:
      type: object
      title: MaxHotelBookingPricePropsWrapper
      properties:
        maxHotelBookingPriceProps:
          $ref: '#/components/schemas/MaxHotelBookingPriceProps'
    MaxHotelPriceByLocation:
      type: object
      title: MaxHotelPriceByLocation
      description: >
        Hotel Rule to describe whether to use default policy rule or different
        set of rules for max  

        booking price for different locations.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/MaxHotelPriceByLocationPropsWrapper'
    MaxHotelPriceByLocationProps:
      type: object
      title: MaxHotelPriceByLocationProps
      description: List of max price of a hotel booking in a location.
      required:
        - maxPricePerLocationList
      properties:
        maxDefaultPrice:
          $ref: '#/components/schemas/SimpleMoney'
        isTaxIncluded:
          type: boolean
          default: false
        maxPricePerLocationList:
          type: array
          items:
            $ref: '#/components/schemas/MaxPriceInLocation'
    MaxHotelPriceByLocationPropsWrapper:
      type: object
      title: MaxHotelPriceByLocationPropsWrapper
      properties:
        maxHotelPriceByLocationProps:
          $ref: '#/components/schemas/MaxHotelPriceByLocationProps'
    MaxPriceInLocation:
      type: object
      title: MaxPriceInLocation
      description: Max price of a booking for a given location.
      required:
        - maxPrice
        - address
      properties:
        maxPrice:
          $ref: '#/components/schemas/SimpleMoney'
        address:
          $ref: '#/components/schemas/PostalAddress'
        isTaxIncluded:
          type: boolean
    MaxRailBookingPriceByDurationProps:
      type: object
      title: MaxRailBookingPriceByDurationProps
      description: List of max price of rail booking for different rail duration.
      required:
        - maxPricePerDurationList
      properties:
        maxPricePerDurationList:
          type: array
          items:
            $ref: '#/components/schemas/MaxRailPricePerDuration'
        isTaxIncluded:
          type: boolean
          default: false
    MaxRailBookingPriceByDurationPropsWrapper:
      type: object
      title: MaxRailBookingPriceByDurationPropsWrapper
      properties:
        maxRailBookingPriceByDurationProps:
          $ref: '#/components/schemas/MaxRailBookingPriceByDurationProps'
    MaxRailPricePerDuration:
      type: object
      title: MaxRailPricePerDuration
      description: Maximum price of a rail booking for given duration.
      required:
        - maxPrice
        - durationRange
      properties:
        maxPrice:
          $ref: '#/components/schemas/SimpleMoney'
        durationRange:
          $ref: '#/components/schemas/Int32Range'
    OopReasonCodeUde:
      type: object
      title: OopReasonCodeUde
      description: >-
        The message defines the format of a OOP reason code related question
        which would be asked during checkout on policy violation.
      properties:
        entityId:
          type: string
        displayInfo:
          type: string
          description: >-
            Question display name that the user will see. For eg, 'Choose the
            purpose of your trip'.
        questionFormat:
          $ref: '#/components/schemas/QuestionFormat'
        options:
          description: >-
            List of potential options for any question. Options will be present
            if the question type provides options to select.
          type: array
          items:
            $ref: '#/components/schemas/Option'
        isRequired:
          type: boolean
          description: Whether its compulsory to answer the question or not.
          default: false
          example: true
        isDisabled:
          type: boolean
          description: >-
            Whether the question is disabled or not. If true, this should not be
            asked.
          default: false
          example: true
    OopReasonCodes:
      type: object
      title: OopReasonCodes
      description: >-
        OOP reason codes value. Either linked to default policy or actual
        values.
      oneOf:
        - $ref: '#/components/schemas/OopReasonCodesPropsWrapper'
        - $ref: '#/components/schemas/IsLinkedWrapper'
    OopReasonCodesProps:
      type: object
      description: Out of policy reason codes.
      properties:
        reasonCodes:
          type: array
          items:
            $ref: '#/components/schemas/OopReasonCodeUde'
            description: Questions to capture oop reason codes during checkout.
    OopReasonCodesPropsWrapper:
      type: object
      title: OopReasonCodesPropsWrapper
      properties:
        props:
          $ref: '#/components/schemas/OopReasonCodesProps'
    Option:
      type: object
      title: Option
      description: Answer option for a question
      required:
        - displayCode
      properties:
        displayCode:
          type: string
          description: The code which is sent in answer response.
        displayValue:
          type: string
          description: The text to be displayed to the user beside this option.
    OptionInfo:
      type: object
      title: OptionInfo
      description: Options related information for the question.
      required:
        - source
      properties:
        source:
          $ref: '#/components/schemas/OptionSource'
          default: MANUAL
        sourceMetadata:
          $ref: '#/components/schemas/OptionSourceMetadata'
        totalNumOptions:
          type: integer
          description: Total number of options
        options:
          type: array
          description: >
            Available options for the question. This will contain only max 10
            options if only 

            summary is requested.
          items:
            $ref: '#/components/schemas/Option'
    OptionSource:
      type: string
      enum:
        - MANUAL
        - COMPANY_CONFIG
      description: Option source
      example: MANUAL
    OptionSourceMetadata:
      type: object
      title: OptionSourceMetadata
      description: Metadata information for the option source.
      oneOf:
        - $ref: '#/components/schemas/CompanyConfigSourceWrapper'
    OvernightTimeParams:
      type: object
      title: OvernightTimeParams
      description: This defines what is considered as overnight.
      required:
        - nightTimeStart
        - nightTimeEnd
        - nightTimeOverlapMinutes
        - includeLayover
      properties:
        nightTimeStart:
          description: Time when night starts.
          $ref: '#/components/schemas/TimeLocal'
        nightTimeEnd:
          description: Time when night ends.
          $ref: '#/components/schemas/TimeLocal'
        includeLayover:
          description: Whether layover time should be included in the overnight time.
          type: boolean
          default: false
        nightTimeOverlapMinutes:
          description: >-
            The minimum night flight time needed to qualify as an overnight
            flight.
          type: integer
          format: int32
          minimum: 0
    PaymentSourceAccessType:
      title: PaymentSourceAccessType
      description: Payment source access type which can be selected for payment.
      type: string
      enum:
        - CENTRALISED
        - PERSONAL
      example: CENTRALISED
    Percentage:
      title: Percentage
      type: object
      description: Number expressed as a fraction of 100.
      properties:
        percentage:
          type: number
          format: double
          example: 20
    Persona:
      type: string
      title: Persona
      description: Persona of the user
      enum:
        - UNKNOWN_PERSONA
        - EMPLOYEE
        - GUEST
        - PERSONAL
        - RELATIVE
        - ADHOC
      example: EMPLOYEE
    Policy:
      allOf:
        - $ref: '#/components/schemas/CreatePolicyRequest'
      type: object
      title: Policy details.
      description: Details of policy.
      required:
        - id
        - name
        - type
        - currency
        - userGroups
      properties:
        id:
          type: string
          format: uuid
          example: 4974a66b-7493-4f41-908c-58ba81093947
        version:
          type: integer
          format: int32
        updatedAt:
          $ref: '#/components/schemas/DateTimeOffset'
        isMembershipViaHrFeed:
          type: boolean
          description: True if membership is via HR feed. Read-only.
          example: true
        hrFeedPolicyDetails:
          $ref: '#/components/schemas/HrFeedPolicyDetails'
          description: User group details if membership is via HR feed. Read-only.
    PolicyApprovalInfo:
      type: object
      title: PolicyApprovalInfo
      required:
        - approvalProcessType
      properties:
        approvalProcessType:
          type: string
          enum:
            - NONE
            - SOFT_APPROVAL
            - HARD_APPROVAL
            - PASSIVE_APPROVAL
            - PREVENT_BOOKING
        designatedApprovers:
          type: array
          description: List of designated approvers.
          items:
            $ref: '#/components/schemas/UserId'
        isManagerApprover:
          type: boolean
          example: false
        isEmployeeLevelDesignatedApprover:
          type: boolean
          description: >-
            Indicate whether the policy is approved by employee level designated
            approver or not.
          example: false
          default: false
        shouldNotifyManager:
          type: boolean
          description: Indicate whether approval emails should be cc'ed to manager or not.
          example: false
          default: false
        travelTypes:
          type: array
          description: List of travel types for which this ApprovalInfo is applicable.
          items:
            type: string
            enum:
              - AIR
              - HOTEL
              - RAIL
              - CAR
        approvalConditions:
          type: array
          description: >-
            List of approval conditions. OR operator is applied between the
            approval conditions.
          items:
            $ref: '#/components/schemas/ApprovalCondition'
        defaultApprovers:
          type: array
          description: List of designated default approvers.
          items:
            $ref: '#/components/schemas/UserId'
    PolicyCategory:
      type: string
      description: Category of the policy.
      title: PolicyCategory
      enum:
        - EMPLOYEE
        - GUEST
      example: EMPLOYEE
    PolicyCurrency:
      title: PolicyCurrency
      description: Currency for the policy.
      oneOf:
        - $ref: '#/components/schemas/CurrencyCodeWrapper'
        - $ref: '#/components/schemas/IsLinkedWrapper'
    PolicyRuleType:
      type: string
      description: Policy Rule type.
      title: PolicyRuleType
      enum:
        - BASIC_ECONOMY_FARES_DOMESTIC
        - BASIC_ECONOMY_FARES_INTERNATIONAL
        - CABIN_CLASS_NOT_ALLOWED_DOMESTIC
        - CABIN_CLASS_NOT_ALLOWED_INTERNATIONAL
        - HOTEL_RATE_CONDITIONS_NOT_ALLOWED
        - CAR_TYPES_NOT_ALLOWED
        - RAIL_ADVANCE_BOOKING_WINDOW
        - MAX_RAIL_BOOKING_PRICE_BY_DURATION
        - HIGHEST_RAIL_TRAVEL_CLASS_BY_DURATION
        - RAIL_TICKETS_REFUNDABLE
        - HOTEL_RESTRICTED_KEYWORDS
        - FLIGHT_ADVANCE_BOOKING_WINDOW_DOMESTIC
        - FLIGHT_ADVANCE_BOOKING_WINDOW_INTERNATIONAL
        - ALLOWED_AIR_ADDONS
        - MAX_HOTEL_BOOKING_PRICE
        - CAR_ENGINE_TYPES_NOT_ALLOWED
        - LOWEST_FARE_PER_HOTEL_PROPERTY
      example: CAR_TYPES_NOT_ALLOWED
    PolicyType:
      type: string
      description: Policy type.
      title: PolicyType
      enum:
        - DEFAULT
        - GROUP
        - INTERNAL
      example: GROUP
    PolicyValue:
      type: object
      title: PolicyValue
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/UniversalPropsWrapper'
    PostalAddress:
      title: PostalAddress
      description: Postal Address Details
      type: object
      required:
        - addressLines
        - regionCode
      properties:
        addressLines:
          description: Address lines
          type: array
          items:
            type: string
            example: Golden Gate Bridge
        administrativeArea:
          type: string
          description: >
            Code of administrative area. For example: DL for Delhi, India.

            Highest administrative subdivision which is used for postal

            addresses of a country or region.

            For example, this can be a state, a province, an oblast, or a
            prefecture.

            Specifically, for Spain this is the province and not the autonomous

            community (e.g. "Barcelona" and not "Catalonia").

            Many countries don't use an administrative area in postal addresses.
            E.g.

            in Switzerland this should be left unpopulated.
          example: CA
        administrativeAreaName:
          type: string
          description: >
            Name of administrative area. This is full name corresponding to
            administrativeArea. 

            Like Delhi for DL area code. For some places, code and name maybe
            same as well like Tokyo.
          example: California
        description:
          description: Address description
          type: string
          example: San Francisco Home
        isDefault:
          description: >-
            Whether this address is default address in case multiple addresses
            are specified.
          type: boolean
          example: true
        languageCode:
          description: >
            BCP-47 language code of the contents of this address (if known).
            This is often the UI 

            language of the input form or is expected to match one of the
            languages used in the 

            address' country/region, or their transliterated equivalents.

            This can affect formatting in certain countries, but is not critical
            to the correctness 

            of the data and will never affect any validation or other
            non-formatting related operations.

            Examples: "zh-Hant", "ja", "ja-Latn", "en".
          type: string
          example: en
        locality:
          description: Generally refers to the city/town portion of the address.
          type: string
          example: San Francisco
        locationCode:
          description: >-
            IATA 3-letter location code. See
            https://www.iata.org/en/services/codes.
          type: string
          example: LAX
        organization:
          description: The name of the organization at the address.
          type: string
          example: Spotnana
        postalCode:
          description: >-
            Postal code of the address. This is a required field when setting
            for a user/legal entity/company etc.
          type: string
          example: '94130'
        continentCode:
          description: 2 letter continent code of the continent this address falls in.
          type: string
          example: AF
        recipients:
          description: The recipient at the address.
          type: array
          items:
            type: string
        regionCode:
          description: Region code of the country/region of the address.
          type: string
          example: US
        regionName:
          description: Region name of the country/region of the address.
          type: string
          example: America
        revision:
          type: integer
          format: int32
          example: 1
        sortingCode:
          description: >
            Additional, country-specific, sorting code. This is not used

            in most regions. Where it is used, the value is either a string like

            "CEDEX", optionally followed by a number (e.g. "CEDEX 7"), or just a
            number

            alone, representing the "sector code" (Jamaica), "delivery area
            indicator"

            (Malawi) or "post office indicator" (e.g. Côte d'Ivoire).
          type: string
          example: Jamaica
        sublocality:
          description: >-
            Sublocality of the address. This can be neighborhoods, boroughs,
            districts.
          type: string
        timezone:
          description: Time zone of the address.
          type: string
          example: America/Los_Angeles
        coordinates:
          description: Map coordinates of the address.
          $ref: '#/components/schemas/Latlng'
    PreCheckoutQuestionType:
      type: string
      enum:
        - UNKNOWN_CHECKOUT_QUESTION_TYPE
        - USER_DEFINED_QUESTION
        - OOP_REASON_CODE
      description: >
        Types of pre-checkout questions.

        USER_DEFINED_QUESTION the default question type for all pre checkout
        questions which have been created from UI.

        OOP_REASON_CODE is kept separate so that existing OOP flow doesn't
        break.
      example: OOP_REASON_CODE
    PreSearchQuestionType:
      type: string
      enum:
        - UNKNOWN_SEARCH_QUESTION_TYPE
        - PURPOSE_OF_TRIP
      description: >
        Types of pre-search questions.

        PURPOSE_OF_TRIP required to ask purpose of the trip user is going to.
        For example: meeting, training, interview.
      example: PURPOSE_OF_TRIP
    PreventBooking:
      type: object
      title: PreventBooking
      description: Whether to allow booking if a rule is violated.
      properties:
        prevent:
          type: boolean
          default: false
        reason:
          type: string
    PreventBookingWrapper:
      type: object
      title: PreventBookingWrapper
      properties:
        preventBooking:
          $ref: '#/components/schemas/PreventBooking'
    Question:
      type: object
      title: Question
      description: >-
        The message defines the format of a question which can be asked to a
        user in any kind of workflows.
      required:
        - id
        - name
        - isRequired
        - isDisabled
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          description: >-
            Question display name that the user will see. For eg, 'Choose the
            purpose of your trip'.
        questionFormat:
          $ref: '#/components/schemas/QuestionFormat'
        optionInfo:
          $ref: '#/components/schemas/OptionInfo'
        isRequired:
          type: boolean
          description: Whether its compulsory to answer the question or not.
          default: false
          example: true
        isDisabled:
          type: boolean
          description: >-
            Whether the question is disabled or not. If true, this should not be
            asked.
          default: true
          example: true
        includeInItinerary:
          type: boolean
          description: Whether to include this question in the itinerary related emails.
          default: false
          example: true
          deprecated: true
        customFieldLocations:
          type: array
          items:
            $ref: '#/components/schemas/CustomFieldLocation'
        matchConditions:
          $ref: '#/components/schemas/CustomFieldMatchConditions'
        questionType:
          $ref: '#/components/schemas/QuestionType'
    QuestionFormat:
      type: string
      enum:
        - INPUT_BOX
        - RADIO_BUTTON
        - CHECKBOX
        - CHECKBOX_WITH_PERCENTAGE
      description: >
        Question types. INPUT_BOX will make user enter a free flowing text.

        RADIO_BUTTON will have multiple options, user can select only one.

        CHECKBOX questions contain the possible set of options, from which the
        user can choose multiple options.

        CHECKBOX_WITH_PERCENTAGE is similar to checkbox, with the difference
        being that each option having an additional input field whose values
        must add up to 100.
      example: CHECKBOX
    QuestionType:
      type: object
      title: QuestionType
      description: Question type.
      properties:
        preSearchQuestionType:
          $ref: '#/components/schemas/PreSearchQuestionType'
        preCheckoutQuestionType:
          $ref: '#/components/schemas/PreCheckoutQuestionType'
    RailAdvanceBookingWindowProps:
      type: object
      title: RailAdvanceBookingWindowProps
      description: Number of days in advance one can book a Rail ticket.
      required:
        - numDaysInAdvance
      properties:
        numDaysInAdvance:
          type: integer
          format: int32
    RailAdvanceBookingWindowPropsWrapper:
      type: object
      title: RailAdvanceBookingWindowPropsWrapper
      properties:
        railAdvanceBookingWindowProps:
          $ref: '#/components/schemas/RailAdvanceBookingWindowProps'
    RailTicketsRefundableProps:
      type: object
      title: RailTicketsRefundableProps
      description: Different type of refund policy allowed for rail ticket booking.
      required:
        - type
      properties:
        type:
          type: string
          enum:
            - REFUNDABLE_WITHOUT_FEES
            - REFUNDABLE_WITH_OR_WITHOUT_FEES
            - NON_REFUNDABLE
    RailTicketsRefundablePropsWrapper:
      type: object
      title: RailTicketsRefundablePropsWrapper
      properties:
        railTicketsRefundableProps:
          $ref: '#/components/schemas/RailTicketsRefundableProps'
    RailTravelClass:
      description: Travel class
      type: string
      enum:
        - FIRST
        - STANDARD
        - BUSINESS
        - SLEEPER
        - STANDARD_PREMIUM
        - BUSINESS_PREMIUM
        - COACH
        - ROOM
        - EXECUTIVE
      example: FIRST
    Reference:
      type: object
      title: Reference object containing uuid and name of the entity.
      description: Reference of an entity
      required:
        - id
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
    RestrictedAirCrafts:
      type: object
      title: RestrictedAirCrafts
      description: >
        Air rule to describe whether to use default policy or use the specific
        set of air crafts 

        that are restricted.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/RestrictedAirCraftsPropsWrapper'
    RestrictedAirCraftsProps:
      type: object
      title: RestrictedAirCraftsProps
      description: List of IATA Aircraft code.
      required:
        - airCraftCodes
      properties:
        airCraftCodes:
          type: array
          items:
            type: string
            description: IATA Aircraft code
    RestrictedAirCraftsPropsWrapper:
      type: object
      title: RestrictedAirCraftsPropsWrapper
      properties:
        restrictedAirCraftsProps:
          $ref: '#/components/schemas/RestrictedAirCraftsProps'
    RestrictedAirlines:
      type: object
      title: RestrictedAirlines
      description: >
        Air rule to describe whether to use default policy or use the specific
        set of airlines that 

        are restricted.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/RestrictedAirlinesPropsWrapper'
    RestrictedAirlinesProps:
      type: object
      title: RestrictedAirlinesProps
      description: List of IATA Airline code.
      required:
        - airlineCodes
      properties:
        airlineCodes:
          type: array
          items:
            type: string
    RestrictedAirlinesPropsWrapper:
      type: object
      title: RestrictedAirlinesPropsWrapper
      properties:
        restrictedAirlinesProps:
          $ref: '#/components/schemas/RestrictedAirlinesProps'
    RestrictedContinents:
      type: object
      title: RestrictedContinents
      description: >-
        Details containing about the continent restrictions or use default
        policy restrictions.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/ContinentPropsWrapper'
    RestrictedCountries:
      type: object
      title: RestrictedCountries
      description: >-
        Details containing country related restrictions or use default policy
        restrictions.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/CountryPropsWrapper'
    RestrictedHotels:
      type: object
      title: RestrictedHotels
      description: >
        Hotel rule to describe whether to use default policy rule or a different
        set of restricted 

        hotels.
      oneOf:
        - $ref: '#/components/schemas/IsLinkedWrapper'
        - $ref: '#/components/schemas/HotelCodesPropsWrapper'
    SimpleMoney:
      type: object
      title: SimpleMoney
      description: Money object containing just amount and currency code.
      required:
        - amount
        - currencyCode
      properties:
        amount:
          type: number
          format: double
          description: Amount
          example: 510
        currencyCode:
          type: string
          description: The 3-letter currency code defined in ISO 4217.
          example: GBP
    SimpleMoneyWrapper:
      type: object
      title: SimpleMoneyWrapper
      properties:
        money:
          $ref: '#/components/schemas/SimpleMoney'
    TimeLocal:
      title: TimeLocal
      description: ISO8601 Local Time
      type: object
      required:
        - iso8601
      properties:
        iso8601:
          type: string
          pattern: ^([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$
          example: '17:32'
    TravelRegionRule:
      type: object
      title: TravelRegionRule
      description: Rule to evaluate travel region, INTERNATIONAL or DOMESTIC.
      properties:
        travelRegionType:
          $ref: '#/components/schemas/TravelRegionType'
    TravelRegionRuleWrapper:
      type: object
      title: TravelRegionRuleWrapper
      description: Rule to evaluate travel region, INTERNATIONAL or DOMESTIC.
      properties:
        travelRegionRule:
          $ref: '#/components/schemas/TravelRegionRule'
    TravelRegionType:
      type: string
      title: TravelRegionType
      description: Travel region Type
      enum:
        - DOMESTIC
        - INTERNATIONAL
      example: DOMESTIC
    TravelType:
      type: string
      title: TravelType
      description: Travel Type
      enum:
        - AIR
        - HOTEL
        - CAR
        - RAIL
        - LIMO
        - MISC
        - ALL
      example: AIR
    TravelerMatchConditions:
      type: object
      title: TravelerMatchConditions
      description: Matching conditions for traveler.
      properties:
        workerTypes:
          type: array
          description: Worker types. Users belonging to any of these would match.
          items:
            $ref: '#/components/schemas/WorkerType'
        countries:
          type: array
          description: Countries.
          items:
            type: string
        legalEntities:
          type: array
          description: Legal entities
          items:
            $ref: '#/components/schemas/Reference'
        departments:
          type: array
          description: Departments
          items:
            $ref: '#/components/schemas/Reference'
        costCenters:
          type: array
          description: Cost centers
          items:
            $ref: '#/components/schemas/Reference'
        offices:
          type: array
          description: Offices
          items:
            $ref: '#/components/schemas/Reference'
    TripUsageType:
      title: TripUsageType
      description: Trip usage type.
      type: string
      enum:
        - STANDARD
        - EVENT
    UniversalProps:
      type: object
      title: UniversalProps
      description: Universal Props.
      oneOf:
        - $ref: '#/components/schemas/CabinClassNotAllowedDomesticPropsWrapper'
        - $ref: '#/components/schemas/CabinClassNotAllowedInternationalPropsWrapper'
        - $ref: '#/components/schemas/CarTypesNotAllowedPropsWrapper'
        - $ref: '#/components/schemas/CarEngineTypesNotAllowedPropsWrapper'
        - $ref: '#/components/schemas/HotelRateConditionsNotAllowedPropsWrapper'
        - $ref: '#/components/schemas/RailAdvanceBookingWindowPropsWrapper'
        - $ref: '#/components/schemas/MaxRailBookingPriceByDurationPropsWrapper'
        - $ref: '#/components/schemas/RailTicketsRefundablePropsWrapper'
        - $ref: '#/components/schemas/HighestRailTravelClassByDurationPropsWrapper'
        - $ref: '#/components/schemas/HotelRestrictedKeywordsPropsWrapper'
        - $ref: '#/components/schemas/FlightAdvanceBookingWindowDomesticPropsWrapper'
        - $ref: >-
            #/components/schemas/FlightAdvanceBookingWindowInternationalPropsWrapper
        - $ref: '#/components/schemas/AllowedAirAddonsPropsWrapper'
        - $ref: '#/components/schemas/MaxHotelBookingPricePropsWrapper'
        - $ref: '#/components/schemas/LowestFarePerHotelPropertyPropsWrapper'
    UniversalPropsWrapper:
      type: object
      title: UniversalPropsWrapper
      properties:
        universalProps:
          $ref: '#/components/schemas/UniversalProps'
    UniversalRule:
      type: object
      title: UniversalRule
      description: Policy Rules.
      required:
        - ruleType
      properties:
        ruleType:
          $ref: '#/components/schemas/PolicyRuleType'
        action:
          $ref: '#/components/schemas/Action'
        policyValue:
          $ref: '#/components/schemas/PolicyValue'
    UserGroup:
      type: object
      title: UserGroup
      description: >-
        Grouping of users on the basis of different criteria such as org,
        department, etc.
      properties:
        userIds:
          type: array
          items:
            $ref: '#/components/schemas/UserId'
        legalEntityIds:
          type: array
          items:
            $ref: '#/components/schemas/EntityId'
        officeIds:
          type: array
          items:
            $ref: '#/components/schemas/EntityId'
        departments:
          type: array
          description: List of department ids.
          items:
            type: string
        costCenters:
          type: array
          description: List of cost center ids.
          items:
            type: string
        grades:
          type: array
          description: List of grade ids.
          items:
            type: string
        positionTitles:
          type: array
          items:
            type: string
        personas:
          type: array
          items:
            $ref: '#/components/schemas/Persona'
        customFieldIds:
          deprecated: true
          type: array
          items:
            $ref: '#/components/schemas/CustomFieldId'
        countryCodes:
          type: array
          items:
            type: string
            description: Alpha-2 or Alpha-3 ISO country code.
            example: GB
        workerTypes:
          type: array
          items:
            $ref: '#/components/schemas/WorkerType'
        accountingCodes:
          type: array
          items:
            type: string
            description: Code used for accounting.
    UserId:
      type: object
      title: UserId
      description: User identifier
      required:
        - id
      properties:
        id:
          type: string
          format: uuid
    WorkerType:
      type: string
      description: The type of worker.
      enum:
        - EMPLOYEE
        - CONTINGENT
        - SEASONAL
        - INTERN
        - GUEST
      example: EMPLOYEE
      x-enumValidFrom:
        GUEST: '2025-06-03'
tags:
  - name: Company Policies
    description: APIs to configure company travel policies.
  - name: Custom Fields
    description: APIs to manage company custom fields.
paths:
  /v2/companies/{companyId}/policies:
    parameters:
      - name: companyId
        in: path
        description: Identifier for company.
        required: true
        schema:
          type: string
          format: uuid
        example: 4974a66b-7493-4f41-908c-58ba81093947
    post:
      tags:
        - Company Policies
      summary: Create policy
      description: This endpoint creates a policy.
      operationId: createPolicy
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePolicyRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EntityId'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
    get:
      tags:
        - Company Policies
      summary: List company policies
      description: This endpoint list company policies.
      operationId: listPolicies
      parameters:
        - name: categories
          in: query
          description: Filter policies by categories. Defaults to EMPLOYEE if not provided.
          required: false
          schema:
            type: array
            items:
              $ref: '#/components/schemas/PolicyCategory'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ArrayOfReference'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/companies/{companyId}/policies/{policyId}:
    parameters:
      - name: companyId
        in: path
        description: Identifier for company.
        required: true
        schema:
          type: string
          format: uuid
        example: 4974a66b-7493-4f41-908c-58ba81093947
      - name: policyId
        in: path
        description: Identifier for policy.
        required: true
        schema:
          type: string
          format: uuid
        example: 4974a66b-7493-4f41-908c-58ba81093947
    get:
      tags:
        - Company Policies
      summary: Get policy
      description: This endpoint gets a policy by ID.
      operationId: readPolicy
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Policy'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    put:
      tags:
        - Company Policies
      summary: Update policy
      description: This endpoint updates a policy by ID.
      operationId: updatePolicy
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Policy'
      responses:
        '204':
          description: Updated Successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      tags:
        - Company Policies
      summary: Delete policy
      description: This endpoint deletes a policy by ID.
      operationId: deletePolicy
      responses:
        '204':
          description: No Content
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/companies/{companyId}/questions:
    parameters:
      - name: companyId
        in: path
        description: Identifier for company.
        required: true
        schema:
          type: string
          format: uuid
        example: 4974a66b-7493-4f41-908c-58ba81093947
    get:
      tags:
        - Custom Fields
      summary: Get company questions
      operationId: getCompanyQuestions
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ArrayOfReference'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
    post:
      tags:
        - Custom Fields
      summary: Create company question
      operationId: createCompanyQuestion
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateQuestionRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EntityId'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/companies/{companyId}/questions/{questionId}:
    parameters:
      - name: companyId
        in: path
        description: Identifier for company.
        required: true
        schema:
          type: string
          format: uuid
        example: 4974a66b-7493-4f41-908c-58ba81093947
      - name: questionId
        in: path
        description: Identifier for question.
        required: true
        schema:
          type: string
          format: uuid
        example: 1234a66b-7493-4f41-908c-58ba81093947
    get:
      tags:
        - Custom Fields
      summary: Get company question
      operationId: getCompanyQuestion
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Question'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    put:
      tags:
        - Custom Fields
      summary: Update company question
      operationId: updateCompanyQuestion
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Question'
      responses:
        '200':
          description: OK
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      tags:
        - Custom Fields
      summary: Delete company question
      operationId: deleteCompanyQuestion
      responses:
        '200':
          description: OK
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
