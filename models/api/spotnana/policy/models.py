# Code generated from OpenAPI spec. DO NOT EDIT.
# Source: PolicyApi.yaml
# Generated by: datamodel-code-generator
# 
# This file is automatically generated from the Spotnana Policy API OpenAPI specification.
# Any manual changes will be overwritten when the models are regenerated.
# 
# To regenerate: uv run python manage_spotnana_models.py generate

# generated by datamodel-codegen:
#   filename:  PolicyApi.yaml
#   timestamp: 2025-07-15T00:32:26+00:00

from __future__ import annotations

from collections.abc import Sequence
from enum import Enum
from typing import Literal
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, RootModel, conint, constr


class Addon(Enum):
    SEAT = 'SEAT'
    BAGGAGE = 'BAGGAGE'
    WIFI = 'WIFI'
    CARBON_OFFSET = 'CARBON_OFFSET'
    EARLY_BIRD = 'EARLY_BIRD'
    ALL = 'ALL'
    NONE = 'NONE'


class Cabin(Enum):
    UNKNOWN_CABIN = 'UNKNOWN_CABIN'
    ECONOMY = 'ECONOMY'
    PREMIUM_ECONOMY = 'PREMIUM_ECONOMY'
    BUSINESS = 'BUSINESS'
    FIRST = 'FIRST'


class CabinClassNotAllowedProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cabins: Sequence[Cabin]


class CarType(Enum):
    OTHER = 'OTHER'
    MINI = 'MINI'
    ECONOMY = 'ECONOMY'
    COMPACT = 'COMPACT'
    MID_SIZE = 'MID_SIZE'
    STANDARD = 'STANDARD'
    FULL_SIZE = 'FULL_SIZE'
    PREMIUM = 'PREMIUM'
    LUXURY = 'LUXURY'
    CONVERTIBLE = 'CONVERTIBLE'
    MINIVAN = 'MINIVAN'
    SUV = 'SUV'
    VAN = 'VAN'
    PICKUP = 'PICKUP'
    SPORTS = 'SPORTS'
    SPECIAL = 'SPECIAL'
    RECREATIONAL_VEHICLE = 'RECREATIONAL_VEHICLE'
    WAGON = 'WAGON'


class Co2EmissionPerPassengerPerKmProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    emission: float


class Co2EmissionPerPassengerPerKmPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    co2EmissionPerPassengerPerKmProps: Co2EmissionPerPassengerPerKmProps | None = None


class Type(Enum):
    BLOCK_TRAVEL_TO_CONTINENT = 'BLOCK_TRAVEL_TO_CONTINENT'
    ALLOW_WITHIN_CONTINENT = 'ALLOW_WITHIN_CONTINENT'


class ContinentRestriction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type
    continentCode: str = Field(
        ..., description='2-letter continent code.', examples=['NA']
    )


class Type1(Enum):
    BLOCK_TRAVEL_TO_COUNTRY = 'BLOCK_TRAVEL_TO_COUNTRY'
    ALLOW_WITHIN_COUNTRY = 'ALLOW_WITHIN_COUNTRY'


class CountryRestriction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type1
    countryCode: str = Field(..., description='ISO 2-letter country code.')


class CurrencyCodeWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    currencyCode: str | None = Field(
        None, description='iso code of the currency.', examples=['USD']
    )


class CustomFieldLocation(Enum):
    POLICY_APPROVAL_EMAIL = 'POLICY_APPROVAL_EMAIL'
    PNR_EMAIL = 'PNR_EMAIL'
    TRIP_EMAIL = 'TRIP_EMAIL'


class CustomFieldOptionsParam(Enum):
    COST_CENTER = 'COST_CENTER'
    LEGAL_ENTITY = 'LEGAL_ENTITY'
    OFFICE = 'OFFICE'
    DEPARTMENT = 'DEPARTMENT'


class CustomFieldType(Enum):
    QUESTION = 'QUESTION'
    MEETING = 'MEETING'
    BUDGET = 'BUDGET'
    BREX_TOKEN = 'BREX_TOKEN'


class DateTimeOffset(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(
        pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?(Z|([+-](0[0-9]|1[0-4]):([0-5][0-9])))$'
    ) = Field(..., examples=['2017-07-21T17:32Z'])


class DifferenceBetweenFlightFareAndMedianFareProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    percentage: int = Field(
        ...,
        description="Percentage difference between flight's total fare and median fare for all\nflights for that destination.\n",
    )


class DifferenceBetweenFlightFareAndMedianFarePropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    differenceBetweenFlightFareAndMedianFareProps: (
        DifferenceBetweenFlightFareAndMedianFareProps | None
    ) = None


class DoubleRange(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    min: float | None = Field(None, description='Minimum value - inclusive.')
    max: float | None = Field(None, description='Maximum value - inclusive.')


class EngineType(Enum):
    UNKNOWN_ENGINE = 'UNKNOWN_ENGINE'
    PETROL = 'PETROL'
    DIESEL = 'DIESEL'
    ELECTRIC = 'ELECTRIC'
    CNG = 'CNG'
    HYBRID = 'HYBRID'
    HYDROGEN = 'HYDROGEN'
    MULTI_FUEL = 'MULTI_FUEL'
    ETHANOL = 'ETHANOL'


class EntityId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID


class ErrorParameter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str | None = Field(None, description='Parameter name')
    value: str | None = Field(None, description='Parameter value')


class ErrorMessage(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    errorCode: str | None = Field(
        None, description='Error code to identify the specific errors.'
    )
    message: str | None = Field(
        None, description='Message containing details of error.'
    )
    errorParameters: Sequence[ErrorParameter] | None = Field(
        None, description='Error message parameters.'
    )
    errorDetail: str | None = Field(None, description='More details about the error.')


class ErrorResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    debugIdentifier: str | None = Field(
        None, description='Link to debug the error internally.'
    )
    errorMessages: Sequence[ErrorMessage] | None = None


class FlightAdvanceBookingWindowDomesticProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numDaysInAdvance: int


class FlightAdvanceBookingWindowDomesticPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightAdvanceBookingWindowDomesticProps: (
        FlightAdvanceBookingWindowDomesticProps | None
    ) = None


class FlightAdvanceBookingWindowInternationalProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numDaysInAdvance: int


class FlightAdvanceBookingWindowInternationalPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightAdvanceBookingWindowInternationalProps: (
        FlightAdvanceBookingWindowInternationalProps | None
    ) = None


class FlightAdvanceBookingWindowProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numDaysInAdvance: int


class FlightAdvanceBookingWindowPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightAdvanceBookingWindowProps: FlightAdvanceBookingWindowProps | None = None


class FlightCabinUpgradeProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    isAllowed: bool


class FlightCabinUpgradePropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightCabinUpgradeProps: FlightCabinUpgradeProps | None = None


class Type2(Enum):
    NONE = 'NONE'
    CHANGEABLE = 'CHANGEABLE'
    CHANGEABLE_WITH_PENALTY = 'CHANGEABLE_WITH_PENALTY'
    DONT_ALLOW = 'DONT_ALLOW'


class FlightTicketsChangeableProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type2


class FlightTicketsChangeablePropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightTicketsChangeableProps: FlightTicketsChangeableProps | None = None


class Type3(Enum):
    NONE = 'NONE'
    REFUNDABLE = 'REFUNDABLE'
    REFUNDABLE_WITH_PENALTY = 'REFUNDABLE_WITH_PENALTY'
    DONT_ALLOW = 'DONT_ALLOW'


class FlightTicketsRefundableProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type3


class FlightTicketsRefundablePropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightTicketsRefundableProps: FlightTicketsRefundableProps | None = None


class HotelAdvanceBookingWindowProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numDaysInAdvance: int


class HotelAdvanceBookingWindowPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelAdvanceBookingWindowProps: HotelAdvanceBookingWindowProps | None = None


class HotelCancellationProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type3


class HotelCancellationPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelCancellationProps: HotelCancellationProps | None = None


class HotelChainCodesProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelChainCodes: Sequence[str]


class HotelChainCodesPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelChainCodesProps: HotelChainCodesProps | None = None


class HotelCodesProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelCodes: Sequence[str]


class HotelCodesPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelCodesProps: HotelCodesProps | None = None


class Type5(Enum):
    NON_REFUNDABLE = 'NON_REFUNDABLE'
    PREPAID = 'PREPAID'
    REQUIRES_DEPOSIT = 'REQUIRES_DEPOSIT'
    PAY_AT_PROPERTY = 'PAY_AT_PROPERTY'


class HotelRateConditionsNotAllowedProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    types: Sequence[Type5]


class HotelRateConditionsNotAllowedPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelRateConditionsNotAllowedProps: HotelRateConditionsNotAllowedProps | None = None


class HotelRestrictedKeyword(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    keyword: str = Field(
        ..., description='Keyword that should not be allowed in hotel name/description.'
    )
    reason: str = Field(..., description='Reason for not allowing the keyword.')


class HotelRestrictedKeywordsProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelRestrictedKeywordsList: Sequence[HotelRestrictedKeyword] | None = None


class HotelRestrictedKeywordsPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelRestrictedKeywordsProps: HotelRestrictedKeywordsProps | None = None


class HotelStarRatingProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ratingRange: DoubleRange


class HotelStarRatingPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelStarRatingProps: HotelStarRatingProps | None = None


class HrFeedPolicyDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalMemberCount: int | None = Field(
        None, description='Total member count configured via HR feed.'
    )


class Int32Range(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    min: int | None = Field(None, description='Minimum value - inclusive.')
    max: int | None = Field(None, description='Maximum value - inclusive.')


class IsLinkedWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    isLinked: bool | None = Field(
        None,
        description='If the value has to be taken from default policy.',
        examples=[True],
    )


class Latlng(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    latitude: float = Field(
        ..., description='Latitude of the Location', examples=[77.1025]
    )
    longitude: float = Field(
        ..., description='Longitude of the Location', examples=[28.7041]
    )


class Unit(Enum):
    UNKNOWN_UNIT = 'UNKNOWN_UNIT'
    KM = 'KM'
    MILE = 'MILE'


class Length(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    length: float = Field(
        ..., description='Distance from search point.', examples=[150]
    )
    unit: Unit = Field(
        ..., description='Unit of measure being applied.', examples=['MILE']
    )


class LowestFarePerHotelPropertyProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    onlyAllowLowestFare: bool = Field(
        ...,
        description='If true, only the lowest fare per hotel property will be allowed.',
    )


class LowestFarePerHotelPropertyPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    lowestFarePerHotelPropertyProps: LowestFarePerHotelPropertyProps | None = None


class MaxNumberOfStops(Enum):
    ANY = 'ANY'
    ONE_OR_LESS = 'ONE_OR_LESS'
    TWO_OR_LESS = 'TWO_OR_LESS'
    FEWEST = 'FEWEST'


class Type6(Enum):
    PREFERRED = 'PREFERRED'
    EXCLUDE = 'EXCLUDE'


class Carrier(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type6 | None = Field(
        None, description='Whether to include / exclude carriers.'
    )
    airlines: Sequence[str] | None = Field(None, description='List of airline codes.')


class LowestLogicalFareProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightTimeWindowInHoursDomestic: int | None = Field(
        None,
        description='Time window (in hours) applied at both departure and arrival to get time bounds of candidate flights for domestic travel.',
    )
    flightTimeWindowInHoursInternational: int | None = Field(
        None,
        description='Time window (in hours) applied at both departure and arrival to get time bounds of candidate flights for international travel.',
    )
    maxLayoverDurationInHoursDomestic: int | None = Field(
        None,
        description='Maximum layover duration (in hours) possible for candidate flights for domestic travel.',
    )
    maxLayoverDurationInHoursInternational: int | None = Field(
        None,
        description='Maximum layover duration (in hours) possible for candidate flights for international travel.',
    )
    maxLayoverDurationMinutesDomestic: int | None = Field(
        None,
        description='Maximum layover duration (in minutes) possible for candidate flights for domestic travel.',
        examples=[120],
    )
    maxLayoverDurationMinutesInternational: int | None = Field(
        None,
        description='Maximum layover duration (in minutes) possible for candidate flights for international travel.',
        examples=[150],
    )
    flightTimeWindowMinutesDomestic: int | None = Field(
        None,
        description='Time window (in minutes) applied at both departure and arrival \nto get time bounds of candidate flights for domestic travel.\n',
        examples=[60],
    )
    flightTimeWindowMinutesInternational: int | None = Field(
        None,
        description='Time window (in minutes) applied at both departure and arrival \nto get time bounds of candidate flights for international travel.\n',
        examples=[90],
    )
    maxNumberOfStops: MaxNumberOfStops | None = Field(
        None,
        description='Maximum number of stops possible in a leg to be for it to be considered as a valid llf candidate.',
    )
    airportConnectionChanges: Literal['NOT_ALLOWED'] | None = Field(
        None, description='Is more than one distinct airport possible in a leg.'
    )
    carrier: Carrier | None = Field(
        None,
        description='Carriers which may be excluded / included for llf calculation.',
    )


class LowestLogicalFarePropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    props: LowestLogicalFareProps | None = None


class Type7(Enum):
    MINIMUM = 'MINIMUM'
    MEDIAN = 'MEDIAN'
    LOWEST_LOGICAL_FARE = 'LOWEST_LOGICAL_FARE'


class Sign(Enum):
    MORE = 'MORE'
    LESS = 'LESS'


class Type8(Enum):
    MORE_THAN_MEDIAN = 'MORE_THAN_MEDIAN'
    LESS_THAN_MEDIAN = 'LESS_THAN_MEDIAN'


class Option(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayCode: str = Field(
        ..., description='The code which is sent in answer response.'
    )
    displayValue: str | None = Field(
        None, description='The text to be displayed to the user beside this option.'
    )


class OptionSource(Enum):
    MANUAL = 'MANUAL'
    COMPANY_CONFIG = 'COMPANY_CONFIG'


class PaymentSourceAccessType(Enum):
    CENTRALISED = 'CENTRALISED'
    PERSONAL = 'PERSONAL'


class Percentage(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    percentage: float | None = Field(None, examples=[20])


class Persona(Enum):
    UNKNOWN_PERSONA = 'UNKNOWN_PERSONA'
    EMPLOYEE = 'EMPLOYEE'
    GUEST = 'GUEST'
    PERSONAL = 'PERSONAL'
    RELATIVE = 'RELATIVE'
    ADHOC = 'ADHOC'


class ApprovalProcessType(Enum):
    NONE = 'NONE'
    SOFT_APPROVAL = 'SOFT_APPROVAL'
    HARD_APPROVAL = 'HARD_APPROVAL'
    PASSIVE_APPROVAL = 'PASSIVE_APPROVAL'
    PREVENT_BOOKING = 'PREVENT_BOOKING'


class TravelType1(Enum):
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    RAIL = 'RAIL'
    CAR = 'CAR'


class PolicyCategory(Enum):
    EMPLOYEE = 'EMPLOYEE'
    GUEST = 'GUEST'


class PolicyCurrency(RootModel[CurrencyCodeWrapper | IsLinkedWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: CurrencyCodeWrapper | IsLinkedWrapper = Field(
        ..., description='Currency for the policy.', title='PolicyCurrency'
    )


class PolicyRuleType(Enum):
    BASIC_ECONOMY_FARES_DOMESTIC = 'BASIC_ECONOMY_FARES_DOMESTIC'
    BASIC_ECONOMY_FARES_INTERNATIONAL = 'BASIC_ECONOMY_FARES_INTERNATIONAL'
    CABIN_CLASS_NOT_ALLOWED_DOMESTIC = 'CABIN_CLASS_NOT_ALLOWED_DOMESTIC'
    CABIN_CLASS_NOT_ALLOWED_INTERNATIONAL = 'CABIN_CLASS_NOT_ALLOWED_INTERNATIONAL'
    HOTEL_RATE_CONDITIONS_NOT_ALLOWED = 'HOTEL_RATE_CONDITIONS_NOT_ALLOWED'
    CAR_TYPES_NOT_ALLOWED = 'CAR_TYPES_NOT_ALLOWED'
    RAIL_ADVANCE_BOOKING_WINDOW = 'RAIL_ADVANCE_BOOKING_WINDOW'
    MAX_RAIL_BOOKING_PRICE_BY_DURATION = 'MAX_RAIL_BOOKING_PRICE_BY_DURATION'
    HIGHEST_RAIL_TRAVEL_CLASS_BY_DURATION = 'HIGHEST_RAIL_TRAVEL_CLASS_BY_DURATION'
    RAIL_TICKETS_REFUNDABLE = 'RAIL_TICKETS_REFUNDABLE'
    HOTEL_RESTRICTED_KEYWORDS = 'HOTEL_RESTRICTED_KEYWORDS'
    FLIGHT_ADVANCE_BOOKING_WINDOW_DOMESTIC = 'FLIGHT_ADVANCE_BOOKING_WINDOW_DOMESTIC'
    FLIGHT_ADVANCE_BOOKING_WINDOW_INTERNATIONAL = (
        'FLIGHT_ADVANCE_BOOKING_WINDOW_INTERNATIONAL'
    )
    ALLOWED_AIR_ADDONS = 'ALLOWED_AIR_ADDONS'
    MAX_HOTEL_BOOKING_PRICE = 'MAX_HOTEL_BOOKING_PRICE'
    CAR_ENGINE_TYPES_NOT_ALLOWED = 'CAR_ENGINE_TYPES_NOT_ALLOWED'
    LOWEST_FARE_PER_HOTEL_PROPERTY = 'LOWEST_FARE_PER_HOTEL_PROPERTY'


class PolicyType(Enum):
    DEFAULT = 'DEFAULT'
    GROUP = 'GROUP'
    INTERNAL = 'INTERNAL'


class PostalAddress(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    addressLines: Sequence[str] = Field(..., description='Address lines')
    administrativeArea: str | None = Field(
        None,
        description='Code of administrative area. For example: DL for Delhi, India.\nHighest administrative subdivision which is used for postal\naddresses of a country or region.\nFor example, this can be a state, a province, an oblast, or a prefecture.\nSpecifically, for Spain this is the province and not the autonomous\ncommunity (e.g. "Barcelona" and not "Catalonia").\nMany countries don\'t use an administrative area in postal addresses. E.g.\nin Switzerland this should be left unpopulated.\n',
        examples=['CA'],
    )
    administrativeAreaName: str | None = Field(
        None,
        description='Name of administrative area. This is full name corresponding to administrativeArea. \nLike Delhi for DL area code. For some places, code and name maybe same as well like Tokyo.\n',
        examples=['California'],
    )
    description: str | None = Field(
        None, description='Address description', examples=['San Francisco Home']
    )
    isDefault: bool | None = Field(
        None,
        description='Whether this address is default address in case multiple addresses are specified.',
        examples=[True],
    )
    languageCode: str | None = Field(
        None,
        description='BCP-47 language code of the contents of this address (if known). This is often the UI \nlanguage of the input form or is expected to match one of the languages used in the \naddress\' country/region, or their transliterated equivalents.\nThis can affect formatting in certain countries, but is not critical to the correctness \nof the data and will never affect any validation or other non-formatting related operations.\nExamples: "zh-Hant", "ja", "ja-Latn", "en".\n',
        examples=['en'],
    )
    locality: str | None = Field(
        None,
        description='Generally refers to the city/town portion of the address.',
        examples=['San Francisco'],
    )
    locationCode: str | None = Field(
        None,
        description='IATA 3-letter location code. See https://www.iata.org/en/services/codes.',
        examples=['LAX'],
    )
    organization: str | None = Field(
        None,
        description='The name of the organization at the address.',
        examples=['Spotnana'],
    )
    postalCode: str | None = Field(
        None,
        description='Postal code of the address. This is a required field when setting for a user/legal entity/company etc.',
        examples=['94130'],
    )
    continentCode: str | None = Field(
        None,
        description='2 letter continent code of the continent this address falls in.',
        examples=['AF'],
    )
    recipients: Sequence[str] | None = Field(
        None, description='The recipient at the address.'
    )
    regionCode: str = Field(
        ...,
        description='Region code of the country/region of the address.',
        examples=['US'],
    )
    regionName: str | None = Field(
        None,
        description='Region name of the country/region of the address.',
        examples=['America'],
    )
    revision: int | None = Field(None, examples=[1])
    sortingCode: str | None = Field(
        None,
        description='Additional, country-specific, sorting code. This is not used\nin most regions. Where it is used, the value is either a string like\n"CEDEX", optionally followed by a number (e.g. "CEDEX 7"), or just a number\nalone, representing the "sector code" (Jamaica), "delivery area indicator"\n(Malawi) or "post office indicator" (e.g. Côte d\'Ivoire).\n',
        examples=['Jamaica'],
    )
    sublocality: str | None = Field(
        None,
        description='Sublocality of the address. This can be neighborhoods, boroughs, districts.',
    )
    timezone: str | None = Field(
        None, description='Time zone of the address.', examples=['America/Los_Angeles']
    )
    coordinates: Latlng | None = Field(
        None, description='Map coordinates of the address.'
    )


class PreCheckoutQuestionType(Enum):
    UNKNOWN_CHECKOUT_QUESTION_TYPE = 'UNKNOWN_CHECKOUT_QUESTION_TYPE'
    USER_DEFINED_QUESTION = 'USER_DEFINED_QUESTION'
    OOP_REASON_CODE = 'OOP_REASON_CODE'


class PreSearchQuestionType(Enum):
    UNKNOWN_SEARCH_QUESTION_TYPE = 'UNKNOWN_SEARCH_QUESTION_TYPE'
    PURPOSE_OF_TRIP = 'PURPOSE_OF_TRIP'


class PreventBooking(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    prevent: bool | None = False
    reason: str | None = None


class PreventBookingWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preventBooking: PreventBooking | None = None


class QuestionFormat(Enum):
    INPUT_BOX = 'INPUT_BOX'
    RADIO_BUTTON = 'RADIO_BUTTON'
    CHECKBOX = 'CHECKBOX'
    CHECKBOX_WITH_PERCENTAGE = 'CHECKBOX_WITH_PERCENTAGE'


class QuestionType(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preSearchQuestionType: PreSearchQuestionType | None = None
    preCheckoutQuestionType: PreCheckoutQuestionType | None = None


class RailAdvanceBookingWindowProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numDaysInAdvance: int


class RailAdvanceBookingWindowPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    railAdvanceBookingWindowProps: RailAdvanceBookingWindowProps | None = None


class Type9(Enum):
    REFUNDABLE_WITHOUT_FEES = 'REFUNDABLE_WITHOUT_FEES'
    REFUNDABLE_WITH_OR_WITHOUT_FEES = 'REFUNDABLE_WITH_OR_WITHOUT_FEES'
    NON_REFUNDABLE = 'NON_REFUNDABLE'


class RailTicketsRefundableProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type9


class RailTicketsRefundablePropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    railTicketsRefundableProps: RailTicketsRefundableProps | None = None


class RailTravelClass(Enum):
    FIRST = 'FIRST'
    STANDARD = 'STANDARD'
    BUSINESS = 'BUSINESS'
    SLEEPER = 'SLEEPER'
    STANDARD_PREMIUM = 'STANDARD_PREMIUM'
    BUSINESS_PREMIUM = 'BUSINESS_PREMIUM'
    COACH = 'COACH'
    ROOM = 'ROOM'
    EXECUTIVE = 'EXECUTIVE'


class Reference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID
    name: str | None = None


class RestrictedAirCraftsProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airCraftCodes: Sequence[str]


class RestrictedAirCraftsPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    restrictedAirCraftsProps: RestrictedAirCraftsProps | None = None


class RestrictedAirlinesProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlineCodes: Sequence[str]


class RestrictedAirlinesPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    restrictedAirlinesProps: RestrictedAirlinesProps | None = None


class RestrictedHotels(RootModel[IsLinkedWrapper | HotelCodesPropsWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | HotelCodesPropsWrapper = Field(
        ...,
        description='Hotel rule to describe whether to use default policy rule or a different set of restricted \nhotels.\n',
        title='RestrictedHotels',
    )


class SimpleMoney(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: float = Field(..., description='Amount', examples=[510])
    currencyCode: str = Field(
        ...,
        description='The 3-letter currency code defined in ISO 4217.',
        examples=['GBP'],
    )


class SimpleMoneyWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    money: SimpleMoney | None = None


class TimeLocal(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(pattern=r'^([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$') = (
        Field(..., examples=['17:32'])
    )


class TravelRegionType(Enum):
    DOMESTIC = 'DOMESTIC'
    INTERNATIONAL = 'INTERNATIONAL'


class TravelType(Enum):
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    CAR = 'CAR'
    RAIL = 'RAIL'
    LIMO = 'LIMO'
    MISC = 'MISC'
    ALL = 'ALL'


class TripUsageType(Enum):
    STANDARD = 'STANDARD'
    EVENT = 'EVENT'


class UserId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID


class WorkerType(Enum):
    EMPLOYEE = 'EMPLOYEE'
    CONTINGENT = 'CONTINGENT'
    SEASONAL = 'SEASONAL'
    INTERN = 'INTERN'
    GUEST = 'GUEST'


class Action(RootModel[PreventBookingWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: PreventBookingWrapper = Field(..., title='Action')


class AllowedAirAddonsProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    addons: Sequence[Addon]
    paymentSourceAccessTypes: Sequence[PaymentSourceAccessType] | None = ['CENTRALISED']


class AllowedAirAddonsPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    allowedAirAddonsProps: AllowedAirAddonsProps | None = None


class AllowedCarTypesProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    carTypes: Sequence[CarType]


class AllowedCarTypesPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    allowedCarTypesProps: AllowedCarTypesProps | None = None


class ArrayOfReference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    length: int | None = None
    elements: Sequence[Reference] | None = Field(
        None, description='List of references containing id and name.'
    )
    totalNumResults: int | None = Field(None, description='Total number of results')


class CabinClassNotAllowedDomesticProps(CabinClassNotAllowedProps):
    pass
    model_config = ConfigDict(
        frozen=True,
    )


class CabinClassNotAllowedDomesticPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cabinClassNotAllowedDomesticProps: CabinClassNotAllowedDomesticProps | None = None


class CabinClassNotAllowedInternationalProps(CabinClassNotAllowedProps):
    pass
    model_config = ConfigDict(
        frozen=True,
    )


class CabinClassNotAllowedInternationalPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cabinClassNotAllowedInternationalProps: (
        CabinClassNotAllowedInternationalProps | None
    ) = None


class CarEngineTypesProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    carEngineTypes: Sequence[EngineType]


class CarTypesNotAllowedProps(AllowedCarTypesProps):
    pass
    model_config = ConfigDict(
        frozen=True,
    )


class CarTypesNotAllowedPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    carTypesNotAllowedProps: CarTypesNotAllowedProps | None = None


class Co2EmissionPerPassengerPerKm(
    RootModel[IsLinkedWrapper | Co2EmissionPerPassengerPerKmPropsWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | Co2EmissionPerPassengerPerKmPropsWrapper = Field(
        ...,
        description='Air rule to describe whether to use default policy or different rule regarding CO2 emission.\n',
        title='Co2EmissionPerPassengerPerKm',
    )


class CompanyConfigSource(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    optionsParam: CustomFieldOptionsParam


class CompanyConfigSourceWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyConfig: CompanyConfigSource | None = None


class ContinentProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    restrictions: Sequence[ContinentRestriction]


class ContinentPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    continentProps: ContinentProps | None = None


class CountryProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    restrictions: Sequence[CountryRestriction]


class CountryPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    countryProps: CountryProps | None = None


class CustomFieldId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: CustomFieldType
    externalId: str = Field(
        ..., description='Meeting id or budget id based on custom field type.'
    )


class DifferenceBetweenFlightFareAndMedianFare(
    RootModel[IsLinkedWrapper | DifferenceBetweenFlightFareAndMedianFarePropsWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | DifferenceBetweenFlightFareAndMedianFarePropsWrapper = (
        Field(
            ...,
            description='Air rule to describe whether to use default policy or different rule for difference between\na flight fare and median fare of the flights for a destination.\nNote: Median fare was calculated based on number of stops, cabin class and flight duration.\n',
            title='DifferenceBetweenFlightFareAndMedianFare',
        )
    )


class DifferenceValue(RootModel[SimpleMoneyWrapper | Percentage]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: SimpleMoneyWrapper | Percentage = Field(
        ..., title='Different Types Of Values.'
    )


class FlightAdvanceBookingWindow(
    RootModel[IsLinkedWrapper | FlightAdvanceBookingWindowPropsWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | FlightAdvanceBookingWindowPropsWrapper = Field(
        ...,
        description='Air rule to describe whether to use default policy or a different advance booking window for\na flight.\n',
        title='FlightAdvanceBookingWindow',
    )


class FlightCabinUpgrade(RootModel[IsLinkedWrapper | FlightCabinUpgradePropsWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | FlightCabinUpgradePropsWrapper = Field(
        ...,
        description='Air rule to describe whether to use default policy or specific rules regarding flight cabin \nupgrade.\n',
        title='FlightCabinUpgrade',
    )


class FlightTicketsChangeable(
    RootModel[IsLinkedWrapper | FlightTicketsChangeablePropsWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | FlightTicketsChangeablePropsWrapper = Field(
        ...,
        description='Air rule to describe whether to use default policy or different rule regarding flight \ntickets exchange.\n',
        title='FlightTicketsChangeable',
    )


class FlightTicketsRefundable(
    RootModel[IsLinkedWrapper | FlightTicketsRefundablePropsWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | FlightTicketsRefundablePropsWrapper = Field(
        ...,
        description='Air rule to describe whether to use default policy or different rule regarding refundable\nflight tickets.\n',
        title='FlightTicketsRefundable',
    )


class HighestCabinForDuration(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cabin: Cabin
    durationRange: Int32Range


class HighestFlightCabinByDurationProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    highestCabinForDurationList: Sequence[HighestCabinForDuration]


class HighestFlightCabinByDurationPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    highestFlightCabinByDurationProps: HighestFlightCabinByDurationProps | None = None


class HighestTravelClassForDuration(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelClass: RailTravelClass
    durationRange: Int32Range


class HotelAdvanceBookingWindow(
    RootModel[IsLinkedWrapper | HotelAdvanceBookingWindowPropsWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | HotelAdvanceBookingWindowPropsWrapper = Field(
        ...,
        description='Hotel rule to describe whether to use default policy or different rule for advance booking.\n',
        title='HotelAdvanceBookingWindow',
    )


class HotelCancellation(RootModel[IsLinkedWrapper | HotelCancellationPropsWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | HotelCancellationPropsWrapper = Field(
        ...,
        description='Hotel rule to describe whether to use default policy or allow booking of any specific type \nof cancellation.\n',
        title='HotelCancellation',
    )


class HotelChainCodes(RootModel[IsLinkedWrapper | HotelChainCodesPropsWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | HotelChainCodesPropsWrapper = Field(
        ...,
        description='Hotel rule to describe whether to use default policy or use a different set of allowed hotel \nchain codes.\n',
        title='HotelChainCodes',
    )


class HotelMedianRateProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchRadius: Length = Field(
        ..., description='Search radius within which candidate hotel has to be.'
    )
    ratingRange: DoubleRange = Field(
        ..., description='Rating range of candidate hotels.'
    )


class HotelMedianRatePropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelMedianRateProps: HotelMedianRateProps | None = None


class HotelStarRating(RootModel[IsLinkedWrapper | HotelStarRatingPropsWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | HotelStarRatingPropsWrapper = Field(
        ...,
        description='Hotel rule to describe whether to use default policy rule or use the specific range of star \nrated hotels.\n',
        title='HotelStarRatingRule',
    )


class LowestLogicalFare(RootModel[IsLinkedWrapper | LowestLogicalFarePropsWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | LowestLogicalFarePropsWrapper = Field(
        ..., title='LowestLogicalFare'
    )


class MaxCarPricePerNumberOfDaysProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maxPrice: SimpleMoney
    isTaxIncluded: bool | None = False


class MaxCarPricePerNumberOfDaysPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maxCarPricePerNumberOfDaysProps: MaxCarPricePerNumberOfDaysProps | None = None


class MaxFlightBookingPriceProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type7
    sign: Sign
    difference: DifferenceValue
    isTaxIncluded: bool | None = True


class MaxFlightBookingPricePropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maxFlightBookingPriceProps: MaxFlightBookingPriceProps | None = None


class MaxFlightPricePerDuration(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maxPrice: SimpleMoney
    durationRange: Int32Range


class MaxHotelBookingPriceProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    difference: DifferenceValue
    isTaxIncluded: bool | None = True
    type: Type8


class MaxHotelBookingPricePropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maxHotelBookingPriceProps: MaxHotelBookingPriceProps | None = None


class MaxPriceInLocation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maxPrice: SimpleMoney
    address: PostalAddress
    isTaxIncluded: bool | None = None


class MaxRailPricePerDuration(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maxPrice: SimpleMoney
    durationRange: Int32Range


class OopReasonCodeUde(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    entityId: str | None = None
    displayInfo: str | None = Field(
        None,
        description="Question display name that the user will see. For eg, 'Choose the purpose of your trip'.",
    )
    questionFormat: QuestionFormat | None = None
    options: Sequence[Option] | None = Field(
        None,
        description='List of potential options for any question. Options will be present if the question type provides options to select.',
    )
    isRequired: bool | None = Field(
        False,
        description='Whether its compulsory to answer the question or not.',
        examples=[True],
    )
    isDisabled: bool | None = Field(
        False,
        description='Whether the question is disabled or not. If true, this should not be asked.',
        examples=[True],
    )


class OopReasonCodesProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    reasonCodes: Sequence[OopReasonCodeUde] | None = None


class OopReasonCodesPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    props: OopReasonCodesProps | None = None


class OptionSourceMetadata(RootModel[CompanyConfigSourceWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: CompanyConfigSourceWrapper = Field(
        ...,
        description='Metadata information for the option source.',
        title='OptionSourceMetadata',
    )


class OvernightTimeParams(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    nightTimeStart: TimeLocal = Field(..., description='Time when night starts.')
    nightTimeEnd: TimeLocal = Field(..., description='Time when night ends.')
    includeLayover: bool = Field(
        ...,
        description='Whether layover time should be included in the overnight time.',
    )
    nightTimeOverlapMinutes: conint(ge=0) = Field(
        ...,
        description='The minimum night flight time needed to qualify as an overnight flight.',
    )


class RestrictedAirCrafts(RootModel[IsLinkedWrapper | RestrictedAirCraftsPropsWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | RestrictedAirCraftsPropsWrapper = Field(
        ...,
        description='Air rule to describe whether to use default policy or use the specific set of air crafts \nthat are restricted.\n',
        title='RestrictedAirCrafts',
    )


class RestrictedAirlines(RootModel[IsLinkedWrapper | RestrictedAirlinesPropsWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | RestrictedAirlinesPropsWrapper = Field(
        ...,
        description='Air rule to describe whether to use default policy or use the specific set of airlines that \nare restricted.\n',
        title='RestrictedAirlines',
    )


class RestrictedContinents(RootModel[IsLinkedWrapper | ContinentPropsWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | ContinentPropsWrapper = Field(
        ...,
        description='Details containing about the continent restrictions or use default policy restrictions.',
        title='RestrictedContinents',
    )


class RestrictedCountries(RootModel[IsLinkedWrapper | CountryPropsWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | CountryPropsWrapper = Field(
        ...,
        description='Details containing country related restrictions or use default policy restrictions.',
        title='RestrictedCountries',
    )


class TravelRegionRule(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelRegionType: TravelRegionType | None = None


class TravelRegionRuleWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelRegionRule: TravelRegionRule | None = None


class TravelerMatchConditions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    workerTypes: Sequence[WorkerType] | None = Field(
        None, description='Worker types. Users belonging to any of these would match.'
    )
    countries: Sequence[str] | None = Field(None, description='Countries.')
    legalEntities: Sequence[Reference] | None = Field(
        None, description='Legal entities'
    )
    departments: Sequence[Reference] | None = Field(None, description='Departments')
    costCenters: Sequence[Reference] | None = Field(None, description='Cost centers')
    offices: Sequence[Reference] | None = Field(None, description='Offices')


class UserGroup(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userIds: Sequence[UserId] | None = None
    legalEntityIds: Sequence[EntityId] | None = None
    officeIds: Sequence[EntityId] | None = None
    departments: Sequence[str] | None = Field(
        None, description='List of department ids.'
    )
    costCenters: Sequence[str] | None = Field(
        None, description='List of cost center ids.'
    )
    grades: Sequence[str] | None = Field(None, description='List of grade ids.')
    positionTitles: Sequence[str] | None = None
    personas: Sequence[Persona] | None = None
    customFieldIds: Sequence[CustomFieldId] | None = None
    countryCodes: Sequence[str] | None = None
    workerTypes: Sequence[WorkerType] | None = None
    accountingCodes: Sequence[str] | None = None


class AllowedCarTypes(RootModel[IsLinkedWrapper | AllowedCarTypesPropsWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | AllowedCarTypesPropsWrapper = Field(
        ...,
        description='Car rule to describe whether to use default policy rule or use different set of \nallowed car types for booking.\n',
        title='AllowedCarTypes',
    )


class ApprovalRule(RootModel[TravelRegionRuleWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: TravelRegionRuleWrapper = Field(
        ...,
        description='One of rule which should be satisfied to use an approval action.',
        title='ApprovalRule',
    )


class CarEngineTypesNotAllowedProps(CarEngineTypesProps):
    pass
    model_config = ConfigDict(
        frozen=True,
    )


class CarEngineTypesNotAllowedPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    carEngineTypesNotAllowedProps: CarEngineTypesNotAllowedProps | None = None


class CommonPolicyRules(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    restrictedContinents: RestrictedContinents | None = None
    restrictedCountries: RestrictedCountries | None = None


class CustomFieldMatchConditions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerConditions: TravelerMatchConditions | None = None
    travelTypes: Sequence[TravelType] | None = Field(
        None, description='Travel types to match.'
    )
    travelRegionTypes: Sequence[TravelRegionType] | None = Field(
        None, description='Travel region types to match.'
    )
    tripUsageTypes: Sequence[TripUsageType] | None = Field(
        None,
        description='Trip usage types to match. If empty, all trip usage types will be matched.',
    )


class HighestFlightCabinByDuration(
    RootModel[IsLinkedWrapper | HighestFlightCabinByDurationPropsWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | HighestFlightCabinByDurationPropsWrapper = Field(
        ...,
        description='Air rule to describe whether to use default policy or different set of rules for cabin based\non flight duration.\n',
        title='HighestFlightCabinByDuration',
    )


class HighestFlightCabinOvernightProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cabin: Cabin
    overnightTimeParams: OvernightTimeParams | None = None


class HighestFlightCabinOvernightPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    highestFlightCabinOvernightProps: HighestFlightCabinOvernightProps | None = None


class HighestRailTravelClassByDurationProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    highestTravelClassForDurationList: Sequence[HighestTravelClassForDuration]


class HighestRailTravelClassByDurationPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    highestRailTravelClassByDurationProps: (
        HighestRailTravelClassByDurationProps | None
    ) = None


class HotelMedianRate(RootModel[IsLinkedWrapper | HotelMedianRatePropsWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | HotelMedianRatePropsWrapper = Field(
        ...,
        description='List of attributes/filters to find candidate hotels for calculating median\nrate in hotel search.\n',
        title='HotelMedianRate',
    )


class MaxCarPricePerNumberOfDays(
    RootModel[IsLinkedWrapper | MaxCarPricePerNumberOfDaysPropsWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | MaxCarPricePerNumberOfDaysPropsWrapper = Field(
        ...,
        description='Car rule to describe whether to use default policy rules \nor list of rules for max price for given number of days.\n',
        title='MaxCarPricePerNumberOfDays',
    )


class MaxFlightBookingPrice(
    RootModel[IsLinkedWrapper | MaxFlightBookingPricePropsWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | MaxFlightBookingPricePropsWrapper = Field(
        ...,
        description='Max allowed flight booking price for that selected cabin.',
        title='MaxFlightBookingPrice',
    )


class MaxFlightBookingPriceByDurationProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maxPricePerDurationList: Sequence[MaxFlightPricePerDuration]
    isTaxIncluded: bool | None = False


class MaxFlightBookingPriceByDurationPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maxFlightBookingPriceByDurationProps: (
        MaxFlightBookingPriceByDurationProps | None
    ) = None


class MaxHotelPriceByLocationProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maxDefaultPrice: SimpleMoney | None = None
    isTaxIncluded: bool | None = False
    maxPricePerLocationList: Sequence[MaxPriceInLocation]


class MaxHotelPriceByLocationPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maxHotelPriceByLocationProps: MaxHotelPriceByLocationProps | None = None


class MaxRailBookingPriceByDurationProps(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maxPricePerDurationList: Sequence[MaxRailPricePerDuration]
    isTaxIncluded: bool | None = False


class MaxRailBookingPriceByDurationPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maxRailBookingPriceByDurationProps: MaxRailBookingPriceByDurationProps | None = None


class OopReasonCodes(RootModel[OopReasonCodesPropsWrapper | IsLinkedWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: OopReasonCodesPropsWrapper | IsLinkedWrapper = Field(
        ...,
        description='OOP reason codes value. Either linked to default policy or actual values.',
        title='OopReasonCodes',
    )


class OptionInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    source: OptionSource
    sourceMetadata: OptionSourceMetadata | None = None
    totalNumOptions: int | None = Field(None, description='Total number of options')
    options: Sequence[Option] | None = Field(
        None,
        description='Available options for the question. This will contain only max 10 options if only \nsummary is requested.\n',
    )


class Question(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID
    name: str = Field(
        ...,
        description="Question display name that the user will see. For eg, 'Choose the purpose of your trip'.",
    )
    questionFormat: QuestionFormat | None = None
    optionInfo: OptionInfo | None = None
    isRequired: bool = Field(
        ...,
        description='Whether its compulsory to answer the question or not.',
        examples=[True],
    )
    isDisabled: bool = Field(
        ...,
        description='Whether the question is disabled or not. If true, this should not be asked.',
        examples=[True],
    )
    includeInItinerary: bool | None = Field(
        False,
        description='Whether to include this question in the itinerary related emails.',
        examples=[True],
    )
    customFieldLocations: Sequence[CustomFieldLocation] | None = None
    matchConditions: CustomFieldMatchConditions | None = None
    questionType: QuestionType | None = None


class UniversalProps(
    RootModel[
        CabinClassNotAllowedDomesticPropsWrapper
        | CabinClassNotAllowedInternationalPropsWrapper
        | CarTypesNotAllowedPropsWrapper
        | CarEngineTypesNotAllowedPropsWrapper
        | HotelRateConditionsNotAllowedPropsWrapper
        | RailAdvanceBookingWindowPropsWrapper
        | MaxRailBookingPriceByDurationPropsWrapper
        | RailTicketsRefundablePropsWrapper
        | HighestRailTravelClassByDurationPropsWrapper
        | HotelRestrictedKeywordsPropsWrapper
        | FlightAdvanceBookingWindowDomesticPropsWrapper
        | FlightAdvanceBookingWindowInternationalPropsWrapper
        | AllowedAirAddonsPropsWrapper
        | MaxHotelBookingPricePropsWrapper
        | LowestFarePerHotelPropertyPropsWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        CabinClassNotAllowedDomesticPropsWrapper
        | CabinClassNotAllowedInternationalPropsWrapper
        | CarTypesNotAllowedPropsWrapper
        | CarEngineTypesNotAllowedPropsWrapper
        | HotelRateConditionsNotAllowedPropsWrapper
        | RailAdvanceBookingWindowPropsWrapper
        | MaxRailBookingPriceByDurationPropsWrapper
        | RailTicketsRefundablePropsWrapper
        | HighestRailTravelClassByDurationPropsWrapper
        | HotelRestrictedKeywordsPropsWrapper
        | FlightAdvanceBookingWindowDomesticPropsWrapper
        | FlightAdvanceBookingWindowInternationalPropsWrapper
        | AllowedAirAddonsPropsWrapper
        | MaxHotelBookingPricePropsWrapper
        | LowestFarePerHotelPropertyPropsWrapper
    ) = Field(..., description='Universal Props.', title='UniversalProps')


class UniversalPropsWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    universalProps: UniversalProps | None = None


class ApprovalCondition(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    rules: Sequence[ApprovalRule] | None = None


class CarPolicyRules(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maxCarPricePerNumberOfDays: MaxCarPricePerNumberOfDays | None = None
    allowedCarTypes: AllowedCarTypes | None = None


class CreateQuestionRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(
        ...,
        description="Question display name that the user will see. For eg, 'Choose the purpose of your trip'.",
    )
    questionFormat: QuestionFormat
    optionInfo: OptionInfo | None = None
    isRequired: bool = Field(
        ...,
        description='Whether its compulsory to answer the question or not.',
        examples=[True],
    )
    isDisabled: bool = Field(
        ...,
        description='Whether the question is disabled or not. If true, this should not be asked.',
        examples=[False],
    )
    includeInItinerary: bool | None = Field(
        False,
        description='Whether to include this question in the itinerary related emails.',
        examples=[True],
    )
    customFieldLocations: Sequence[CustomFieldLocation] | None = None
    matchConditions: CustomFieldMatchConditions | None = None


class HighestFlightCabinOvernight(
    RootModel[IsLinkedWrapper | HighestFlightCabinOvernightPropsWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | HighestFlightCabinOvernightPropsWrapper = Field(
        ...,
        description='Air rule to describe whether to use default policy or different cabin rule for overnight \nflight.\n',
        title='HighestFlightCabinOvernight',
    )


class MaxFlightBookingPriceByDuration(
    RootModel[IsLinkedWrapper | MaxFlightBookingPriceByDurationPropsWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | MaxFlightBookingPriceByDurationPropsWrapper = Field(
        ...,
        description='Air rule to describe whether to use default policy or different set of max booking price\nfor different flight durations.\n',
        title='MaxFlightBookingPriceByDuration',
    )


class MaxHotelPriceByLocation(
    RootModel[IsLinkedWrapper | MaxHotelPriceByLocationPropsWrapper]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | MaxHotelPriceByLocationPropsWrapper = Field(
        ...,
        description='Hotel Rule to describe whether to use default policy rule or different set of rules for max  \nbooking price for different locations.\n',
        title='MaxHotelPriceByLocation',
    )


class PolicyApprovalInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    approvalProcessType: ApprovalProcessType
    designatedApprovers: Sequence[UserId] | None = Field(
        None, description='List of designated approvers.'
    )
    isManagerApprover: bool | None = Field(None, examples=[False])
    isEmployeeLevelDesignatedApprover: bool | None = Field(
        False,
        description='Indicate whether the policy is approved by employee level designated approver or not.',
        examples=[False],
    )
    shouldNotifyManager: bool | None = Field(
        False,
        description="Indicate whether approval emails should be cc'ed to manager or not.",
        examples=[False],
    )
    travelTypes: Sequence[TravelType1] | None = Field(
        None,
        description='List of travel types for which this ApprovalInfo is applicable.',
    )
    approvalConditions: Sequence[ApprovalCondition] | None = Field(
        None,
        description='List of approval conditions. OR operator is applied between the approval conditions.',
    )
    defaultApprovers: Sequence[UserId] | None = Field(
        None, description='List of designated default approvers.'
    )


class PolicyValue(RootModel[IsLinkedWrapper | UniversalPropsWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: IsLinkedWrapper | UniversalPropsWrapper = Field(..., title='PolicyValue')


class UniversalRule(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ruleType: PolicyRuleType
    action: Action | None = None
    policyValue: PolicyValue | None = None


class AirPolicyRules(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    restrictedAirlines: RestrictedAirlines | None = None
    restrictedAirCrafts: RestrictedAirCrafts | None = None
    maxFlightBookingPriceByDuration: MaxFlightBookingPriceByDuration | None = None
    flightCabinUpgrade: FlightCabinUpgrade | None = None
    flightAdvanceBookingWindow: FlightAdvanceBookingWindow | None = None
    flightTicketsRefundable: FlightTicketsRefundable | None = None
    flightTicketsChangeable: FlightTicketsChangeable | None = None
    highestFlightCabinByDurationDomestic: HighestFlightCabinByDuration | None = None
    highestFlightCabinByDurationInternational: HighestFlightCabinByDuration | None = (
        None
    )
    highestFlightCabinOvernight: HighestFlightCabinOvernight | None = None
    differenceBetweenFlightFareAndMedianFare: (
        DifferenceBetweenFlightFareAndMedianFare | None
    ) = None
    co2EmissionPerPassengerPerKm: Co2EmissionPerPassengerPerKm | None = None
    maxFlightBookingPriceInternational: MaxFlightBookingPrice | None = None
    maxFlightBookingPriceDomestic: MaxFlightBookingPrice | None = None
    lowestLogicalFare: LowestLogicalFare | None = None


class HotelPolicyRules(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    restrictedHotels: RestrictedHotels | None = None
    maxHotelPriceByLocation: MaxHotelPriceByLocation | None = None
    hotelAdvanceBookingWindow: HotelAdvanceBookingWindow | None = None
    hotelCancellation: HotelCancellation | None = None
    hotelChainCodes: HotelChainCodes | None = None
    hotelStarRating: HotelStarRating | None = None
    hotelMedianRateNightly: HotelMedianRate | None = None


class CreatePolicyRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Name of the policy.')
    type: PolicyType
    parentPolicyId: UUID | None = Field(
        None,
        description='Policy to inherit the values of linked rules. If empty, consider the company default policy.',
        examples=['f7b3b3b3-7b3b-4b3b-8b3b-3b3b3b3b3b3b'],
    )
    currency: PolicyCurrency
    inPolicyApprovalInfo: PolicyApprovalInfo | None = None
    outOfPolicyApprovalInfo: PolicyApprovalInfo | None = None
    inPolicyApprovalInfos: Sequence[PolicyApprovalInfo] | None = None
    outOfPolicyApprovalInfos: Sequence[PolicyApprovalInfo] | None = None
    outOfPolicyAgentApprovalInfos: Sequence[PolicyApprovalInfo] | None = None
    enableAgentActionOverride: bool | None = Field(
        None,
        description='True if agent can override the PREVENT_BOOKING action. If enabled, outOfPolicyAgentApprovalInfos for all travel types should be provided.',
    )
    userGroups: Sequence[UserGroup]
    commonPolicyRules: CommonPolicyRules | None = None
    carPolicyRules: CarPolicyRules | None = None
    hotelPolicyRules: HotelPolicyRules | None = None
    airPolicyRules: AirPolicyRules | None = None
    isRestrictive: bool | None = Field(
        None, description='True if policy is a restrictive policy.', examples=[True]
    )
    rules: Sequence[UniversalRule] | None = Field(
        None,
        description='A list of general policy rules for all travel types air, hotel, car and rail.',
    )
    airOopReasonCodes: OopReasonCodes | None = Field(
        None,
        description='Out of policy reason code question to be asked to user for air booking.',
    )
    hotelOopReasonCodes: OopReasonCodes | None = Field(
        None,
        description='Out of policy reason code question to be asked to user for hotel booking.',
    )
    carOopReasonCodes: OopReasonCodes | None = Field(
        None,
        description='Out of policy reason code question to be asked to user for car booking.',
    )
    railOopReasonCodes: OopReasonCodes | None = Field(
        None,
        description='Out of policy reason code question to be asked to user for rail booking.',
    )
    category: PolicyCategory | None = Field(
        'EMPLOYEE', description='Category of the policy.'
    )


class Policy(CreatePolicyRequest):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID = Field(..., examples=['4974a66b-7493-4f41-908c-58ba81093947'])
    version: int | None = None
    updatedAt: DateTimeOffset | None = None
    isMembershipViaHrFeed: bool | None = Field(
        None,
        description='True if membership is via HR feed. Read-only.',
        examples=[True],
    )
    hrFeedPolicyDetails: HrFeedPolicyDetails | None = Field(
        None, description='User group details if membership is via HR feed. Read-only.'
    )
    name: str = Field(..., description='Name of the policy.')
    type: PolicyType
    currency: PolicyCurrency
    userGroups: Sequence[UserGroup]
