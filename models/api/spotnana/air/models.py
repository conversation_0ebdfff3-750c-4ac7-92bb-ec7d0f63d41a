# Code generated from OpenAPI spec. DO NOT EDIT.
# Source: AirApi.yaml
# Generated by: datamodel-code-generator
# 
# This file is automatically generated from the Spotnana Air API OpenAPI specification.
# Any manual changes will be overwritten when the models are regenerated.
# 
# To regenerate: uv run python manage_spotnana_models.py generate

# generated by datamodel-codegen:
#   filename:  AirApi.yaml
#   timestamp: 2025-07-15T00:32:22+00:00

from __future__ import annotations

from collections.abc import Sequence
from enum import Enum
from typing import Literal
from uuid import UUID

from pydantic import BaseModel, ConfigDict, EmailStr, Field, RootModel, conint, constr


class AccessType(Enum):
    CENTRALISED = 'CENTRALISED'
    PERSONAL = 'PERSONAL'
    APPLICATION = 'APPLICATION'
    TMC = 'TMC'
    ITINERARY = 'ITINERARY'
    EVENT = 'EVENT'
    EVENT_TEMPLATE = 'EVENT_TEMPLATE'


class AirAttributesRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchId: str = Field(
        ...,
        description='The unique ID for the search response for which the attributes are being requested.',
        examples=['ChBjZDg3ZjRjZmRmMTFmMWFiEhBjZDg3Z'],
    )


class AirCheckoutRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchId: str = Field(
        ...,
        description='Search id returned in the corresponding air search API response.',
        examples=['ChBjZDg3ZjRjZmRmMTFm'],
    )
    itineraryId: str = Field(
        ...,
        description='Value of the itineraryId for the itinerary selected by the user in the corresponding air search API response.',
        examples=['IthBjZDg3ZjRjZmRmMTFm'],
    )


class PnrStatus(Enum):
    SUCCESS = 'SUCCESS'
    APPROVAL_PENDING = 'APPROVAL_PENDING'
    CC_VERIFICATION_REQUIRED = 'CC_VERIFICATION_REQUIRED'
    PAYMENT_PENDING = 'PAYMENT_PENDING'
    CONFIRMATION_PENDING = 'CONFIRMATION_PENDING'
    ERROR = 'ERROR'


class AirExchangeAvailableOptions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelDate: bool | None = Field(
        None, description='If true it means Airline allows to change the date.'
    )
    route: bool | None = Field(
        None, description='If true it means Airline allows to change the route.'
    )
    addLeg: bool | None = Field(
        None,
        description='If true it means Airline allows to add new leg to the existing itinerary.',
    )
    removeLeg: bool | None = Field(
        None,
        description='If true it means Airline allows to remove leg from the existing itinerary.',
    )
    modifyPartialItinerary: bool | None = Field(
        None, description='If true it means Airline allows to modify partial itinerary.'
    )
    sameEndpointRestriction: bool | None = Field(
        None,
        description='If true it means Airline allows to change the endpoint across legs for the return itinerary.',
    )


class AirExchangeState(Enum):
    EXCHANGEABLE_BY_OBT = 'EXCHANGEABLE_BY_OBT'
    EXCHANGEABLE_BY_SUPPORT = 'EXCHANGEABLE_BY_SUPPORT'


class AirIntermediateRevalidateItineraryRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchId: str = Field(
        ...,
        description='The unique `searchId` corresponding to the selected itinerary.',
    )
    itineraryId: str = Field(
        ..., description='The selected `itineraryId` which you would like to validate.'
    )


class AirItineraryIdentifier(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchId: str = Field(
        ...,
        description='The search ID associated with the itinerary, returned in the air search response.',
        examples=['Xjdks78X'],
    )
    itineraryId: str = Field(
        ...,
        description='The itinerary ID of the itinerary selected by the user, for which the seat map is requested.',
        examples=['ijdks78X'],
    )


class LoyaltyType(Enum):
    OWN = 'OWN'
    PARTNER = 'PARTNER'
    ALLIANCE = 'ALLIANCE'


class AirLoyaltyProgram(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    programName: str = Field(
        ..., description='Name of the loyalty program.', examples=['AA Advantage']
    )
    airlineCode: str = Field(
        ...,
        description='IATA code of the airline that hosts the loyalty program.',
        examples=['AA'],
    )
    airlineName: str = Field(
        ...,
        description='Name of the airline that hosts the loyalty program.',
        examples=['American Airlines'],
    )
    loyaltyType: LoyaltyType = Field(
        ...,
        description='Type of the loyalty program. This indicates how the loyalty program is associated with the airline mentioned in the API request.',
        examples=['OWN'],
    )


class AirSelectedItineraryRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchId: str = Field(
        ...,
        description='The searchId returned in the corresponding air search API response.',
        examples=['ChBjZDg3ZjRjZmRmMTFm'],
    )
    itineraryId: str = Field(
        ...,
        description='The itineraryId of the itinerary selected by the user in the corresponding air search API response.',
        examples=['IthBjZDg3ZjRjZmRmMTFm'],
    )


class AircraftAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['789 (widebody)'])
    aircraftType: str | None = Field(None, examples=['widebody'])
    aircraftModel: str | None = Field(None, examples=['787'])


class AircraftAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    aircraftAmenity: AircraftAmenity | None = None


class Type(Enum):
    CREDIT = 'CREDIT'
    DEBIT = 'DEBIT'


class AirlineInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlineCode: str = Field(..., description='IATA code for airline.', examples=['AA'])
    airlineName: str = Field(
        ..., description='Airline name', examples=['American Airlines']
    )


class AirlineInfoComplete(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlineCode: str = Field(..., description='Airline code', examples=['AA'])
    airlineName: str = Field(
        ..., description='Airline name', examples=['American Airlines']
    )
    country: str = Field(..., description='Airline country', examples=['United States'])
    twoLetterCountryCode: str = Field(
        ..., description='Two letter country code', examples=['US']
    )
    isActive: bool = Field(..., description='Airline active status', examples=[True])
    loyaltyProgramName: str = Field(
        ..., description='Airline loyalty program name', examples=['AA Advantage']
    )
    loyaltyProgramCode: str = Field(
        ..., description='Airline loyalty program code', examples=['AA Adv']
    )
    airlineTicketPrefix: str = Field(
        ..., description='Airline ticket prefix', examples=['001']
    )


class AirlineInfoResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlineInfo: Sequence[AirlineInfoComplete]


class FlightType(Enum):
    UNKNOWN_FLIGHT_TYPE = 'UNKNOWN_FLIGHT_TYPE'
    DOMESTIC = 'DOMESTIC'
    INTERNATIONAL = 'INTERNATIONAL'
    ALL = 'ALL'


class AirlinePref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlines: Sequence[str] | None = None
    flightType: FlightType | None = Field(None, examples=['DOMESTIC'])


class AirlinePrefs(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legIndex: int | None = None
    airline: Sequence[str] | None = None


class AirlinePrefsFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlinePrefs: Sequence[AirlinePrefs] | None = None


class AirportInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airportCode: str | None = Field(
        None, description='3-letter code of the airport.', examples=['WRA']
    )
    airportName: str | None = Field(
        None, description='Full name of the airport.', examples=['Warder Airport']
    )
    cityName: str | None = Field(
        None,
        description='Name of the city in which the airport is located (or is nearest to).',
        examples=['Werder'],
    )
    countryName: str | None = Field(
        None,
        description='Name of the country in which the airport is located.',
        examples=['Ethiopia'],
    )
    countryCode: str | None = Field(
        None,
        description='2-letter IATA country code associated with the airport.',
        examples=['ET'],
    )
    zoneName: str | None = Field(
        None,
        description='Name of the time zone associated with the airport.',
        examples=['Africa/Addis_Ababa'],
    )
    stateCode: str | None = Field(
        None,
        description='2-letter IATA code for the state in which the airport is located.',
        examples=['CA'],
    )


class Alliance(Enum):
    UNKNOWN_ALLIANCE = 'UNKNOWN_ALLIANCE'
    STAR_ALLIANCE = 'STAR_ALLIANCE'
    ONEWORLD = 'ONEWORLD'
    SKYTEAM = 'SKYTEAM'
    VANILLA_ALLIANCE = 'VANILLA_ALLIANCE'
    U_FLY_ALLIANCE = 'U_FLY_ALLIANCE'
    VALUE_ALLIANCE = 'VALUE_ALLIANCE'


class AllianceFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    alliances: Sequence[Alliance] | None = None
    airlines: Sequence[str] | None = Field(
        None, description='2-letter IATA codes for the preferred airlines.'
    )


class AlliancePref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    alliances: Sequence[Alliance]


class AmadeusPaymentActionToken(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentRequestId: str = Field(
        ...,
        description='Initial payment id generated by checkout SDK to initiate payment',
    )
    actionToken: str = Field(
        ...,
        description='Token returned by checkout SDK to create/validate FOP in PSS order',
    )


class Status(Enum):
    ELIGIBLE = 'ELIGIBLE'
    NOT_APPLICABLE = 'NOT_APPLICABLE'
    PURCHASED = 'PURCHASED'


class AncillaryType(Enum):
    EARLY_BIRD = 'EARLY_BIRD'
    WIFI = 'WIFI'
    CARBON_OFFSET = 'CARBON_OFFSET'


class AnswerPair(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    item: str | None = Field(
        None, description='The option selected from the list of available choices.'
    )
    value: str | None = Field(
        None,
        description='The additional input provided (by the user) while selecting one of the options.',
    )
    description: str | None = Field(
        None, description='Description of the selected option.'
    )


class ApplicationWarnings(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    warningType: Literal['LOYALTY_NAME_MISMATCH'] | None = Field(
        None, description='Warning Type'
    )
    description: str | None = Field(
        None,
        description='Warning description',
        examples=['FREQUENT TRAVELER NUMBER DOES NOT EXIST FOR THIS AIRLINE'],
    )


class AssessmentType(Enum):
    NEUTRAL = 'NEUTRAL'
    BENEFIT = 'BENEFIT'
    RESTRICTION = 'RESTRICTION'
    FEE = 'FEE'


class AvailableSeats(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    columnNumber: Sequence[str] | None = None


class BagPolicyApplicability(Enum):
    EACH = 'EACH'
    TOTAL = 'TOTAL'


class BaggageFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    freeCheckedBag: bool | None = Field(
        None,
        description='If true, only itineraries with free checked bag are returned. Otherwise, no\npreference.\n',
    )
    freeCarryOn: bool | None = Field(
        None,
        description='If true, only itineraries with free carry on bag are returned. Otherwise, no\npreference.\n',
    )


class BeverageAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(
        None, examples=['Premium alcohol beverages provided']
    )
    beverageType: str | None = Field(None, examples=['premium alcoholic'])
    alcoholCost: str | None = Field(None, examples=['free'])


class BeverageAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    beverageAmenity: BeverageAmenity | None = None


class BlockedAdjacentSeatsAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['No blocked adjacent seats'])
    blockedAdjacentSeatsDescription: str | None = Field(
        None, examples=['Adjacent seats are not blocked on this flight']
    )
    blockedAdjacentSeatsAttrDescription: str | None = Field(None, examples=['no'])


class BlockedAdjacentSeatsAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    blockedAdjacentSeatsAmenity: BlockedAdjacentSeatsAmenity | None = None


class BoardingPolicy(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str = Field(
        ...,
        description='Text describing the priority boarding policy.',
        examples=['Priority boarding provided for free'],
    )


class BoolWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    b: bool | None = None


class CO2EmissionDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    emissionValue: float = Field(
        ...,
        description='Estimated C02 emissions value for selected flight and seating class, per passenger (in tons)',
        examples=[10],
    )
    averageEmissionValue: float | None = Field(
        None,
        description='Average estimated C02 emissions per passenger for same route (in tons)',
        examples=[10],
    )
    flightDistanceKm: float | None = Field(
        None,
        description='Total distance flown by the flight in kilometres.',
        examples=[10],
    )
    isApproximate: bool | None = Field(
        None,
        description='Indicates whether the emissions value is approximate or not.',
        examples=[True],
    )


class Cabin(Enum):
    UNKNOWN_CABIN = 'UNKNOWN_CABIN'
    ECONOMY = 'ECONOMY'
    PREMIUM_ECONOMY = 'PREMIUM_ECONOMY'
    BUSINESS = 'BUSINESS'
    FIRST = 'FIRST'


class CabinFilterType(Enum):
    DEFAULT = 'DEFAULT'
    ALL = 'ALL'
    AT_LEAST_ONE = 'AT_LEAST_ONE'


class CabinViewFareCategory(Enum):
    UNKNOWN_CABIN_CATEGORY = 'UNKNOWN_CABIN_CATEGORY'
    BASIC = 'BASIC'
    ECONOMY = 'ECONOMY'
    PREMIUM_ECONOMY = 'PREMIUM_ECONOMY'
    ECONOMY_PLUS = 'ECONOMY_PLUS'
    BUSINESS = 'BUSINESS'
    FIRST = 'FIRST'


class CancellationStatus(Enum):
    CANCELLED = 'CANCELLED'
    AGENT_TASK_CREATED = 'AGENT_TASK_CREATED'


class CarType(Enum):
    OTHER = 'OTHER'
    MINI = 'MINI'
    ECONOMY = 'ECONOMY'
    COMPACT = 'COMPACT'
    MID_SIZE = 'MID_SIZE'
    STANDARD = 'STANDARD'
    FULL_SIZE = 'FULL_SIZE'
    PREMIUM = 'PREMIUM'
    LUXURY = 'LUXURY'
    CONVERTIBLE = 'CONVERTIBLE'
    MINIVAN = 'MINIVAN'
    SUV = 'SUV'
    VAN = 'VAN'
    PICKUP = 'PICKUP'
    SPORTS = 'SPORTS'
    SPECIAL = 'SPECIAL'
    RECREATIONAL_VEHICLE = 'RECREATIONAL_VEHICLE'
    WAGON = 'WAGON'


class CarVendor(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    code: str = Field(..., description='Vendor code', examples=['ZE'])
    name: str = Field(..., description='Vendor name', examples=['HERTZ'])
    isPresentInPreferredVendors: bool | None = Field(
        None,
        description='Whether the car vendor is present in preferred vendor list. This is an optional field which gets populated only in the preferred vendor autocomplete API.',
        examples=[True],
    )


class Type1(Enum):
    UNKNOWN = 'UNKNOWN'
    CREDIT = 'CREDIT'
    DEBIT = 'DEBIT'


class CardCompany(Enum):
    NONE = 'NONE'
    VISA = 'VISA'
    MASTERCARD = 'MASTERCARD'
    AMEX = 'AMEX'
    DISCOVER = 'DISCOVER'
    AIR_TRAVEL_UATP = 'AIR_TRAVEL_UATP'
    CARTE_BLANCHE = 'CARTE_BLANCHE'
    DINERS_CLUB = 'DINERS_CLUB'
    JCB = 'JCB'
    BREX = 'BREX'
    UNION_PAY = 'UNION_PAY'
    EURO_CARD = 'EURO_CARD'
    ACCESS_CARD = 'ACCESS_CARD'
    ELO_CARD = 'ELO_CARD'


class CardCondition(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['CARD'] = 'CARD'
    cardCompanies: Sequence[CardCompany] | None = Field(
        None, description='List of card companies allowed for the payment'
    )


class Type2(Enum):
    BASSINET = 'BASSINET'
    SPECIAL_ASSISTANCE_WHEELCHAIR = 'SPECIAL_ASSISTANCE_WHEELCHAIR'
    BAGGAGE = 'BAGGAGE'
    UNACCOMPANIED_MINOR = 'UNACCOMPANIED_MINOR'
    PETS = 'PETS'
    MEET_AND_ASSIST = 'MEET_AND_ASSIST'
    OTHERS = 'OTHERS'
    MEAL = 'MEAL'
    SPECIAL_ASSISTANCE_DISABILITY = 'SPECIAL_ASSISTANCE_DISABILITY'


class Category(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type2 | None = Field(
        None,
        description='Enum of supported category types.',
        examples=['SPECIAL_ASSISTANCE_WHEELCHAIR'],
    )
    description: str | None = Field(
        None,
        description='Category description.',
        examples=['Special Assistance - Wheelchair'],
    )


class CentralCardAccessLevel(Enum):
    UNKNOWN = 'UNKNOWN'
    ORGANIZATION = 'ORGANIZATION'
    LEGAL_ENTITY = 'LEGAL_ENTITY'
    TMC = 'TMC'


class ChangeableFilter(Enum):
    NO_CHANGEABLE_FILTER = 'NO_CHANGEABLE_FILTER'
    CHANGEABLE_FLEXIBLE_REFUNDABLE = 'CHANGEABLE_FLEXIBLE_REFUNDABLE'
    REFUNDABLE_WITH_PENALTY = 'REFUNDABLE_WITH_PENALTY'
    REFUNDABLE_WITHOUT_PENALTY = 'REFUNDABLE_WITHOUT_PENALTY'


class CheckInPolicy(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str = Field(
        ...,
        description='Description text for the check-in policy.',
        examples=['Standard check-in priority'],
    )


class CleaningAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(
        None, examples=['Enhanced aircraft cleaning every flight']
    )
    cleaningDescription: str | None = Field(
        None,
        examples=[
            'This flight features an aircraft that will be thoroughly cleaned using disinfectants for every flight'
        ],
    )
    cleaningAttrDescription: str | None = Field(
        None, examples=['enhanced every flight']
    )


class CleaningAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cleaningAmenity: CleaningAmenity | None = None


class CoachPref(Enum):
    SMOKING = 'SMOKING'
    NON_SMOKING = 'NON_SMOKING'
    PETS_ALLOWED = 'PETS_ALLOWED'
    RESTAURANT = 'RESTAURANT'
    QUIET = 'QUIET'


class ColumnPosition(Enum):
    AISLE = 'AISLE'
    WINDOW = 'WINDOW'
    WINDOW_AISLE = 'WINDOW_AISLE'
    CENTER = 'CENTER'


class CompanyId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID = Field(..., examples=['f49d00fe-1eda-4304-ba79-a980f565281d'])


class CompanyId1(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID = Field(..., examples=['edd5b835-8001-430c-98f8-fedeccebe4cf'])


class PaginationParams(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    offset: int | None = Field(None, examples=[0])
    totalRecords: int | None = Field(None, examples=[100])


class CompanyId2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID | None = Field(None, examples=['edd5b835-8001-430c-98f8-fedeccebe4cf'])


class ConditionalRate(Enum):
    MILITARY = 'MILITARY'
    AAA = 'AAA'
    GOVERNMENT = 'GOVERNMENT'


class ConnectingAirportsLegFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legIndex: int | None = None
    airportCodes: Sequence[str] | None = None


class CorporateCodeFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    corpAccountCodes: Sequence[str] | None = Field(
        None, description='List of Corporate code to be filtered'
    )


class CostCenterId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['731ccbca-0415-6fe1-d235-c324dfbe7423'])


class CovidFilterPreference(Enum):
    NO_PREFERENCE = 'NO_PREFERENCE'
    REQUIRED = 'REQUIRED'
    NOT_REQUIRED = 'NOT_REQUIRED'


class CovidTestingAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['No COVID-19 test required'])
    covidTestingDescription: str | None = Field(
        None,
        examples=[
            'A negative COVID-19 test is not required for this flight; check with the airline for possible destination requirements or other restrictions.'
        ],
    )
    covidTestingAttrDescription: str | None = None


class CovidTestingAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    covidTestingAmenity: CovidTestingAmenity | None = None


class TicketType1(Enum):
    TICKET_TYPE_UNKNOWN = 'TICKET_TYPE_UNKNOWN'
    ETICKET = 'ETICKET'
    MCO = 'MCO'


class SegmentsAvailable(Enum):
    UNKNOWN = 'UNKNOWN'
    ALL_OPEN = 'ALL_OPEN'
    PARTIAL = 'PARTIAL'
    OTHER = 'OTHER'


class CreditCardAccessType(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    CENTRALISED = 'CENTRALISED'
    INDIVIDUAL = 'INDIVIDUAL'
    PERSONAL = 'PERSONAL'
    TMC = 'TMC'
    APPLICATION = 'APPLICATION'
    ITINERARY = 'ITINERARY'
    EVENTS = 'EVENTS'
    TRAVEL_ARRANGER_MANAGED = 'TRAVEL_ARRANGER_MANAGED'
    COMPANY_TRAVEL_ARRANGER_MANAGED = 'COMPANY_TRAVEL_ARRANGER_MANAGED'
    EVENT_TEMPLATE = 'EVENT_TEMPLATE'


class CreditStatus(Enum):
    STATUS_UNKNOWN = 'STATUS_UNKNOWN'
    OPEN = 'OPEN'
    USED = 'USED'
    RESERVED = 'RESERVED'


class CreditUsageType(Enum):
    CREDIT_USAGE_TYPE_UNKNOWN = 'CREDIT_USAGE_TYPE_UNKNOWN'
    COMPANY = 'COMPANY'
    PERSONAL = 'PERSONAL'


class CustomFieldLocation(Enum):
    POLICY_APPROVAL_EMAIL = 'POLICY_APPROVAL_EMAIL'
    PNR_EMAIL = 'PNR_EMAIL'
    TRIP_EMAIL = 'TRIP_EMAIL'


class CustomFieldOptionsParam(Enum):
    COST_CENTER = 'COST_CENTER'
    LEGAL_ENTITY = 'LEGAL_ENTITY'
    OFFICE = 'OFFICE'
    DEPARTMENT = 'DEPARTMENT'


class CustomFieldType(Enum):
    QUESTION = 'QUESTION'
    MEETING = 'MEETING'
    BUDGET = 'BUDGET'
    BREX_TOKEN = 'BREX_TOKEN'


class DateModel(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$') = (
        Field(..., examples=['2017-07-21'])
    )


class DateTimeLocal(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(
        pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$'
    ) = Field(..., examples=['2017-07-21T17:32'])


class DateTimeOffset(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(
        pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?(Z|([+-](0[0-9]|1[0-4]):([0-5][0-9])))$'
    ) = Field(..., examples=['2017-07-21T17:32Z'])


class DeckLevel(Enum):
    UPPER_DECK = 'UPPER_DECK'
    LOWER_DECK = 'LOWER_DECK'


class DepartmentId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['631ccbcf-9414-5fe0-c234-b324dfbe7422'])


class Dimensions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    height: int | None = Field(None, examples=[120])
    width: int | None = Field(None, examples=[240])


class DoubleListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    dList: Sequence[float] | None = None


class DoubleRange(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    min: float | None = Field(None, description='Minimum value - inclusive.')
    max: float | None = Field(None, description='Maximum value - inclusive.')


class DoubleRangeWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    dRange: DoubleRange | None = None


class DoubleWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    d: float | None = None


class Duration(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: str | None = Field(
        None,
        description='Durations define the amount of intervening time in a time interval and are represented by the\nformat P[n]Y[n]M[n]DT[n]H[n]M[n]S.\nThe [n] is replaced by the value for each of the date and time elements that follow the [n].\nLeading zeros are not required. The capital letters P, Y, M, W, D, T, H, M, and S are\ndesignators for each of the date and time elements and are not replaced. P is the duration\ndesignator (for period) placed at the start of the duration representation.\nY is the year designator.\nM is the month designator.\nW is the week designator.\nD is the day designator.\nT is the time designator.\nH is the hour designator.\nM is the minute designator.\nS is the second designator and can include decimal digits with arbitrary precision.\n',
        examples=['PT19H55M'],
    )


class Relation(Enum):
    RELATION_UNKNOWN = 'RELATION_UNKNOWN'
    SPOUSE = 'SPOUSE'
    PARENT = 'PARENT'
    SIBLING = 'SIBLING'
    CHILD = 'CHILD'
    FRIEND = 'FRIEND'
    RELATIVE = 'RELATIVE'
    COLLEAGUE = 'COLLEAGUE'
    OTHER = 'OTHER'


class EngineType(Enum):
    UNKNOWN_ENGINE = 'UNKNOWN_ENGINE'
    PETROL = 'PETROL'
    DIESEL = 'DIESEL'
    ELECTRIC = 'ELECTRIC'
    CNG = 'CNG'
    HYBRID = 'HYBRID'
    HYDROGEN = 'HYDROGEN'
    MULTI_FUEL = 'MULTI_FUEL'
    ETHANOL = 'ETHANOL'


class EntertainmentAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['Seatback on-demand video'])
    entertainmentType: str | None = Field(None, examples=['on-demand'])
    cost: str | None = Field(None, examples=['free'])


class EntertainmentAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    entertainmentAmenity: EntertainmentAmenity | None = None


class Equipment(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    code: str | None = Field(
        None, description='Aircraft equipment code', examples=['777']
    )
    type: str | None = Field(
        None, description='Code representing the type of the equipment', examples=['N']
    )
    name: str | None = Field(
        None,
        description='The name of the flight aircraft type',
        examples=['Boeing 737-800'],
    )


class ErrorParameter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str | None = Field(None, description='Parameter name')
    value: str | None = Field(None, description='Parameter value')


class ErrorMessage(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    errorCode: str | None = Field(
        None, description='Error code to identify the specific errors.'
    )
    message: str | None = Field(
        None, description='Message containing details of error.'
    )
    errorParameters: Sequence[ErrorParameter] | None = Field(
        None, description='Error message parameters.'
    )
    errorDetail: str | None = Field(None, description='More details about the error.')


class ErrorResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    debugIdentifier: str | None = Field(
        None, description='Link to debug the error internally.'
    )
    errorMessages: Sequence[ErrorMessage] | None = None


class ExchangeNotSupportedReason(Enum):
    EXCHANGE_NOT_SUPPORTED_REASON_UNKNOWN = 'EXCHANGE_NOT_SUPPORTED_REASON_UNKNOWN'
    FLIGHT_CHECKED_IN = 'FLIGHT_CHECKED_IN'
    NON_EXCHANGEABLE = 'NON_EXCHANGEABLE'
    AUTOMATED_EXCHANGE_NOT_SUPPORTED = 'AUTOMATED_EXCHANGE_NOT_SUPPORTED'
    MULTI_PAX_EXCHANGE_NOT_SUPPORTED = 'MULTI_PAX_EXCHANGE_NOT_SUPPORTED'
    EXCHANGE_NOT_SUPPORTED_FOR_MARKUP_FARES = 'EXCHANGE_NOT_SUPPORTED_FOR_MARKUP_FARES'
    REWARDS_EXCHANGE_NOT_ENABLED = 'REWARDS_EXCHANGE_NOT_ENABLED'
    FARE_RULES_UNKNOWN = 'FARE_RULES_UNKNOWN'
    ALL_FLIGHTS_USED = 'ALL_FLIGHTS_USED'
    MISSING_PQ = 'MISSING_PQ'
    AIRLINE_TICKET = 'AIRLINE_TICKET'
    AIRLINE_CONTROL = 'AIRLINE_CONTROL'
    SOURCE_NOT_IMPLEMENTED = 'SOURCE_NOT_IMPLEMENTED'
    SCHEDULE_CHANGE_PENDING = 'SCHEDULE_CHANGE_PENDING'
    APPROVAL_PENDING = 'APPROVAL_PENDING'
    APPROVAL_DENIED = 'APPROVAL_DENIED'
    EXCHANGE_NOT_ALLOWED_WITHIN_24_HRS_TICKETING = (
        'EXCHANGE_NOT_ALLOWED_WITHIN_24_HRS_TICKETING'
    )


class ExistingLeg(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    index: int | None = Field(
        None,
        description='The index of the leg in the existing PNR which needs to be kept or removed(see remove field) during the exchange process with the following origin-destination information. For new legs leave empty (null). Fields origin, destination and date are required only if this leg is a new leg to be added.',
        examples=[1],
    )
    remove: bool | None = Field(
        None,
        description='This should be set to true, if this leg is to be cancelled.',
        examples=[True],
    )


class Expiry(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiryMonth: conint(ge=1, le=12) = Field(
        ..., description='Expiry month', examples=[1]
    )
    expiryYear: conint(ge=2000) = Field(..., description='Expiry year', examples=[2010])


class ExpiryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiry: Expiry | None = None


class FacilityType(Enum):
    BULKHEAD = 'BULKHEAD'
    STAIRS = 'STAIRS'
    TABLE = 'TABLE'
    LAVATORY = 'LAVATORY'
    BAR = 'BAR'
    CLOSET = 'CLOSET'
    AIR_PHONE = 'AIR_PHONE'
    EXIT_DOOR = 'EXIT_DOOR'
    EMERGENCY_EXIT = 'EMERGENCY_EXIT'
    GALLEY = 'GALLEY'
    LUGGAGE_STORAGE = 'LUGGAGE_STORAGE'
    STORAGE_SPACE = 'STORAGE_SPACE'


class FareAttributes(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    isVoidable: bool | None = Field(
        None, description='Designates whether the fare is voidable or not.'
    )
    holdDeadline: DateTimeLocal | None = Field(
        None,
        description='Designates the dateTime until which the fare will be held (when a traveler places a hold on the fare).',
    )
    isNonVerifiedExchangeOffer: bool | None = Field(
        None,
        description='Designates whether the fare is a non-verified exchange offer. For these fares, the \nexchange price guarantee is not provided by the supplier.\n',
    )


class FareComponent(Enum):
    BASE = 'BASE'
    TAX = 'TAX'


class FareMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    corpAccountCode: str | None = Field(
        None, description='The corp account code for each pax.', examples=['6CA4']
    )


class FareType(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    CHANGEABLE = 'CHANGEABLE'
    REFUNDABLE = 'REFUNDABLE'


class FarePref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fareTypes: Sequence[FareType]


class FareTypeFilter(Enum):
    UNKNOWN = 'UNKNOWN'
    PUBLIC = 'PUBLIC'
    PRIVATE = 'PRIVATE'
    CORPORATE = 'CORPORATE'


class FetchAirFareRuleRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchId: str | None = Field(
        None,
        description='The unique ID (identifying the current workflow) returned by the backend in the air\nsearch results response (AirSearchResponse.searchId) of the previous leg. If set, the\nrateOptionId and userId must also be specified.\n',
    )
    rateOptionId: str | None = Field(
        None,
        description="The unique ID identifying the selected flights in the previous leg's response.",
    )
    pnrId: str | None = Field(
        None,
        description='The unique Spotnana PNR ID against which flights are booked.',
        examples=['1213124111'],
    )
    userId: UUID | None = Field(
        None,
        description='Unique ID of the user.',
        examples=['4974a66b-7493-4f41-908c-58ba81093947'],
    )


class FlightAttributeCategory(Enum):
    AIRCRAFT = 'AIRCRAFT'
    SEAT = 'SEAT'
    MEALS = 'MEALS'
    BEVERAGES = 'BEVERAGES'
    ENTERTAINMENT = 'ENTERTAINMENT'
    POWER = 'POWER'
    WIFI = 'WIFI'
    SERVICE_INFLIGHT = 'SERVICE_INFLIGHT'
    UPGRADE = 'UPGRADE'
    TRANSPORT_TO_AIRPORT = 'TRANSPORT_TO_AIRPORT'
    CHECK_IN = 'CHECK_IN'
    DEPARTURE_TERMINAL = 'DEPARTURE_TERMINAL'
    DEPARTMENT_LOUNGE = 'DEPARTMENT_LOUNGE'
    GATE_BOARDING = 'GATE_BOARDING'
    SERVICE_DEPARTURE = 'SERVICE_DEPARTURE'
    STOPOVER = 'STOPOVER'
    CONNECTING_EASE = 'CONNECTING_EASE'
    CONNECTING_TERMINAL = 'CONNECTING_TERMINAL'
    CONNECTING_LOUNGE = 'CONNECTING_LOUNGE'
    SERVICE_CONNECTING = 'SERVICE_CONNECTING'
    BAGGAGE_COLLECTION = 'BAGGAGE_COLLECTION'
    ARRIVAL_TERMINAL = 'ARRIVAL_TERMINAL'
    ARRIVAL_LOUNGE = 'ARRIVAL_LOUNGE'
    ARRIVAL_TRANSPORT = 'ARRIVAL_TRANSPORT'
    SERVICE_ARRIVAL = 'SERVICE_ARRIVAL'
    SCHEDULE_ROUTE = 'SCHEDULE_ROUTE'
    LOYALITY_PROGRAM = 'LOYALITY_PROGRAM'
    BRAND = 'BRAND'
    PROMOTION = 'PROMOTION'


class HiddenStop(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airport: AirportInfo = Field(
        ...,
        description='The airport where the flight will make a stop (not a traditional layover stop).',
    )
    arrivalDateTime: DateTimeLocal = Field(
        ..., description='The start date and time for the stop.'
    )
    departureDateTime: DateTimeLocal = Field(
        ..., description='The end date and time for the stop.'
    )


class FlightInLegRule(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    origin: str | None = None
    destination: str | None = None
    departureDateTime: DateTimeLocal | None = None
    flightId: str | None = None


class FlightNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    num: str = Field(..., description='Flight number.', examples=['321'])
    airlineCode: str = Field(
        ..., description='Two-letter IATA airline code.', examples=['AA']
    )


class FlightNumberFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightNumber: FlightNumber | None = None


class FlightRef(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightRef: str = Field(
        ...,
        description='Points to the detailed flight information in auxiliary flight data fields. flightRef is present in\nFlightCommon. As a result, FlightRef can be used to point to the FlightCommon object.\n',
        examples=['flight_3'],
    )


class FlightRefWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightRef: FlightRef | None = None


class FreshFoodAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = None
    freshFoodType: str | None = None
    cost: str | None = None


class FreshFoodAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    freshFoodAmenity: FreshFoodAmenity | None = None


class GateInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    gate: str | None = Field(None, description='Gate number', examples=['1A'])
    terminal: str | None = Field(None, description='Airport terminal', examples=['1'])


class GatewayType(Enum):
    PAYMENT_GATEWAY_UNKNOWN = 'PAYMENT_GATEWAY_UNKNOWN'
    STRIPE = 'STRIPE'
    BREX = 'BREX'
    RAZORPAY = 'RAZORPAY'


class GatewayIdentifier(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    gatewayId: str | None = Field(
        None, description='Gateway Id for which the payment method should be verified.'
    )
    gatewayType: GatewayType | None = Field(
        None, description='Gateway Type for of the verification gateway.'
    )


class Gender(Enum):
    MALE = 'MALE'
    FEMALE = 'FEMALE'
    UNSPECIFIED = 'UNSPECIFIED'
    UNDISCLOSED = 'UNDISCLOSED'


class GradeId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['831ccbcb-1416-7fe2-e236-d324dfbe7424'])


class HotelBrand(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    brandCode: str | None = Field(
        None, description='The code of hotel brand.', examples=['HY']
    )
    brandName: str | None = Field(
        None, description='The name of hotel brand.', examples=['Global Hytt Corp.']
    )


class HotelChain(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    chainCode: str | None = Field(
        None, description='The code of hotel chain.', examples=['EM']
    )
    chainName: str | None = Field(
        None, description='The name of hotel chain.', examples=['Mariott']
    )


class HotelPrefAmenity(Enum):
    PARKING = 'PARKING'
    FREE_PARKING = 'FREE_PARKING'
    FREE_BREAKFAST = 'FREE_BREAKFAST'
    POOL = 'POOL'
    WIFI = 'WIFI'
    FITNESS_CENTER = 'FITNESS_CENTER'
    FAMILY_FRIENDLY = 'FAMILY_FRIENDLY'
    RECEPTION = 'RECEPTION'
    SPA = 'SPA'
    RESTAURANT = 'RESTAURANT'
    BAR = 'BAR'
    TRANSPORTATION = 'TRANSPORTATION'
    PET_FRIENDLY = 'PET_FRIENDLY'
    BUSINESS_CENTER = 'BUSINESS_CENTER'
    AIR_CONDITIONING = 'AIR_CONDITIONING'
    BEACH_ACCESS = 'BEACH_ACCESS'
    LAUNDRY_SERVICES = 'LAUNDRY_SERVICES'
    ROOM_SERVICE = 'ROOM_SERVICE'
    ACCESSIBLE = 'ACCESSIBLE'


class Image(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    data: str | None = Field(None, examples=['6935813e12584abda0e43d71cd2ea260'])
    dimensions: Dimensions | None = None
    url: str | None = Field(
        None,
        examples=[
            'https://static.wixstatic.com/media/73f2e2_6935813e12584abda0e43d71cd2ea260~mv2.png/v1/fill/w_630,h_94,al_c,q_85,usm_0.66_1.00_0.01/Spotnana%403x.webp'
        ],
    )


class ImageGroup(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    caption: str | None = Field(
        None, description='Caption for the image.', examples=['Exterior']
    )
    images: Sequence[Image] = Field(..., description='List of images.')


class Type3(Enum):
    UNKNOWN = 'UNKNOWN'
    VISA = 'VISA'


class ImmigrationDocument(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    authorizedStayDuration: Duration | None = Field(
        None, description='Duration of the stay authorized by the immigration document.'
    )
    docId: str = Field(
        ...,
        description='The ID of the immigration document.',
        examples=['ImmigrationDocumentID'],
    )
    expiryDate: DateModel = Field(
        ..., description='The date on which the immigration document expires.'
    )
    issueCountry: str = Field(
        ...,
        description='The country that issued the immigration document.',
        examples=['IN'],
    )
    issuedDate: DateModel | None = Field(
        None, description='The date on which the immigration document was issued.'
    )
    nationalityCountry: str | None = Field(None, examples=['IN'])
    reentryRequirementDuration: Duration | None = None
    type: Type3 | None = Field(None, examples=['VISA'])


class ImmigrationDocumentWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    immigrationDoc: ImmigrationDocument | None = None


class IncludeLocation(Enum):
    BOOKING_CONFIRMATION_EMAILS = 'BOOKING_CONFIRMATION_EMAILS'
    APPROVAL_EMAILS = 'APPROVAL_EMAILS'
    COMPANY_REPORTS = 'COMPANY_REPORTS'
    CONSOLIDATED_ITINERARY_EMAILS = 'CONSOLIDATED_ITINERARY_EMAILS'


class InitiateBookingWorkflowIds(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    checkoutResponseId: str | None = Field(
        None, description='ID sent by the backend in the air checkout response.'
    )
    seatMapResponseId: str | None = Field(
        None, description='ID sent by the backend in the air seat map response.'
    )


class Int32Range(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    min: int | None = Field(None, description='Minimum value - inclusive.')
    max: int | None = Field(None, description='Maximum value - inclusive.')


class Int32RangeWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iRange: Int32Range | None = None


class Int32Wrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    i: int | None = None


class Int64Wrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    l: int | None = None


class IntListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iList: Sequence[int] | None = None


class ItemType(Enum):
    SERVICE_FEE = 'SERVICE_FEE'
    TRAVEL_TICKET = 'TRAVEL_TICKET'
    SEAT = 'SEAT'
    BAGGAGE = 'BAGGAGE'
    EARLY_BIRD = 'EARLY_BIRD'


class KnownTravelerNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    number: str = Field(..., examples=['12345'])
    issueCountry: str = Field(..., examples=['US'])


class KnownTravelerNumberWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ktn: KnownTravelerNumber | None = None


class Latlng(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    latitude: float = Field(
        ..., description='Latitude of the Location', examples=[77.1025]
    )
    longitude: float = Field(
        ..., description='Longitude of the Location', examples=[28.7041]
    )


class LayoutAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['1-2-1 seat layout'])
    directAisleAccess: str | None = Field(None, examples=['yes'])


class LayoutAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    layoutAmenity: LayoutAmenity | None = None


class Applicability(Enum):
    PER_LEG = 'PER_LEG'
    ALL_LEGS = 'ALL_LEGS'


class LegApplicability(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    applicability: Applicability = Field(..., examples=['PER_LEG'])
    legId: str | None = Field(
        None,
        description='Identifier for the leg. Present when applicability is PER_LEG.',
        examples=['leg_0'],
    )


class LegSearchParams(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchId: str | None = Field(
        None,
        description='The unique ID returned by the air search response of the previous leg.',
    )
    selectedRateOptionId: str | None = Field(
        None,
        description="The unique ID identifying the selected flights in the previous leg's response.",
    )
    legIndex: int | None = Field(
        None,
        description='The index ID for the leg for which the results are to be returned for this request.',
    )
    asyncRouteHappy: bool | None = Field(
        None,
        description='Designates if RouteHappy content is fetched asynchronously. If true, RouteHappy content will be fetched\nasynchronously and not returned in response.\n',
    )
    pageNumber: int | None = Field(
        None,
        description='The page number for which data is to be fetched. If pageNumber is 0, the complete air search response (without pagination) will be returned.',
    )
    pageSize: int | None = Field(
        None,
        description='Optional parameter to specify the max number of results per page. If not set or set as 0, default page size of 50 is used.',
    )


class LegalEntityId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['fc1ccbce-8413-4fe9-b233-a324dfbe7421'])


class LegalEntityIdListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legalEntityIdList: Sequence[LegalEntityId] | None = None


class LegalEntityIdWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legalEntityId: LegalEntityId | None = None


class Unit(Enum):
    UNKNOWN_UNIT = 'UNKNOWN_UNIT'
    KM = 'KM'
    MILE = 'MILE'


class Length(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    length: float = Field(
        ..., description='Distance from search point.', examples=[150]
    )
    unit: Unit = Field(
        ..., description='Unit of measure being applied.', examples=['MILE']
    )


class LengthWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    length: Length | None = None


class Location(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    countryCode: str | None = Field(
        None, description='Unique country code for a location', examples=['TR']
    )
    countryName: str | None = Field(
        None, description='Full name of the country', examples=['Turkey']
    )
    googlePlaceId: str | None = Field(
        None,
        description='Unique place ID for the location assigned by Google',
        examples=['ChIJL_P_CXMEDTkRw0ZdG-0GVvw'],
    )
    latlong: Latlng | None = None
    name: str = Field(..., description='Full name of the Location', examples=['Denver'])
    stateName: str | None = Field(
        None, description='Full name of the state', examples=['Colorado']
    )


class LoungePolicy(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str = Field(
        ...,
        description='Lounge policy description text (may include whether a fee is charged).',
        examples=['Lounge access for a fee'],
    )


class Type4(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    CAR = 'CAR'
    RAIL = 'RAIL'


class LoyaltyInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    appliedTo: Sequence[str] | None = None
    id: str = Field(..., examples=['firstId'])
    issuedBy: str = Field(..., examples=['firstIssuedBy'])
    type: Type4 = Field(..., examples=['AIR'])


class Parameter(Enum):
    DATE_OF_BIRTH = 'DATE_OF_BIRTH'
    BILLING_ADDRESS = 'BILLING_ADDRESS'
    POSTAL_CODE = 'POSTAL_CODE'
    CVV = 'CVV'
    PASSPORT_ID = 'PASSPORT_ID'
    PASSPORT_EXPIRY_DATE = 'PASSPORT_EXPIRY_DATE'
    PASSPORT_ISSUE_COUNTRY = 'PASSPORT_ISSUE_COUNTRY'
    NATIONALITY = 'NATIONALITY'
    GENDER = 'GENDER'
    CVV_ON_PERSONAL_CARD = 'CVV_ON_PERSONAL_CARD'


class MaskAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(
        None,
        description='The basic text displayed for mask amenity.',
        examples=['Face covering required'],
    )
    maskDescription: str | None = Field(
        None,
        description='A full description of the mask amenity.',
        examples=[
            'All passengers are required to wear a face covering throughout their journey'
        ],
    )
    maskAttrDescription: str | None = Field(None, examples=['yes'])


class MaskAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    maskAmenity: MaskAmenity | None = None


class MealType(Enum):
    UNKNOWN_MEAL = 'UNKNOWN_MEAL'
    AVML = 'AVML'
    BBML = 'BBML'
    BLML = 'BLML'
    CHML = 'CHML'
    DBML = 'DBML'
    FPML = 'FPML'
    GFML = 'GFML'
    HFML = 'HFML'
    HNML = 'HNML'
    KSML = 'KSML'
    LCML = 'LCML'
    LFML = 'LFML'
    LPML = 'LPML'
    LSML = 'LSML'
    MOML = 'MOML'
    NLML = 'NLML'
    NSML = 'NSML'
    ORML = 'ORML'
    PFML = 'PFML'
    RVML = 'RVML'
    SFML = 'SFML'
    SPML = 'SPML'
    VGML = 'VGML'
    VJML = 'VJML'
    VLML = 'VLML'
    VOML = 'VOML'


class MigrateUnusedCreditsToCompanyCreditsRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentSourceIds: Sequence[str]
    updateCreditUsageTypeTo: CreditUsageType = Field(
        ...,
        description='The type of credit usage to be updated to. This can be either COMPANY or PERSONAL. If passed Company, it means you are trying to update from personal to Company',
    )


class MigrationStatus(Enum):
    STATUS_UNKNOWN = 'STATUS_UNKNOWN'
    SUCCESS = 'SUCCESS'
    FAILED = 'FAILED'


class ModuleId(Enum):
    BOOKING = 'BOOKING'
    AGENT = 'AGENT'


class MultiAirports(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airports: Sequence[constr(pattern=r'^[A-Z]{3}$')] = Field(
        ..., description='List of 3-letter airport IATA codes.'
    )


class MultiTicketFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hideMultiTicket: bool | None = Field(
        None,
        description='Designates whether multi ticket options are to be shown. If true, only single ticket itineraries are returned.',
    )


class NGSFareCategory(Enum):
    UNKNOWN_NGS_CATEGORY = 'UNKNOWN_NGS_CATEGORY'
    BASE = 'BASE'
    STANDARD = 'STANDARD'
    ENHANCED = 'ENHANCED'
    PREMIUM = 'PREMIUM'
    LUXURY = 'LUXURY'
    ULTRA_LUXURY = 'ULTRA_LUXURY'


class NameSuffix(Enum):
    NAME_SUFFIX_UNKNOWN = 'NAME_SUFFIX_UNKNOWN'
    SR = 'SR'
    JR = 'JR'
    MD = 'MD'
    PHD = 'PHD'
    II = 'II'
    III = 'III'
    IV = 'IV'
    DO = 'DO'
    ATTY = 'ATTY'
    V = 'V'
    VI = 'VI'
    ESQ = 'ESQ'
    DC = 'DC'
    DDS = 'DDS'
    VM = 'VM'
    JD = 'JD'
    SECOND = 'SECOND'
    THIRD = 'THIRD'


class Type5(Enum):
    DNI = 'DNI'
    NIE = 'NIE'


class NationalDoc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    docId: str = Field(
        ...,
        description='Unique id identifying the national document.',
        examples=['NationalDocId'],
    )
    issueCountry: str = Field(
        ...,
        description='IS0 2 letter country code of the country issuing this id.',
        examples=['IN'],
    )
    issuedDate: DateModel | None = None
    expiryDate: DateModel | None = None
    type: Type5 | None = Field(None, examples=['DNI'])


class NationalDocWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    nationalDoc: NationalDoc | None = None


class NewLeg(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    origin: constr(pattern=r'^[A-Z]{3}$') | None = Field(
        None, description='3 letter IATA airport code for origin', examples=['SFO']
    )
    destination: constr(pattern=r'^[A-Z]{3}$') | None = Field(
        None, description='3 letter IATA airport code for origin', examples=['SFO']
    )
    date: DateTimeLocal | None = None


class NumStopsPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numOfStops: int = Field(..., examples=[34])


class OfficeId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(
        ...,
        description='The value of the unique ID for the office.',
        examples=['531ccbce-8413-4fe9-b233-a324dfbe7421'],
    )


class OfficeIdListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    officeIdList: Sequence[OfficeId] | None = Field(
        None, description='A list of office IDs.'
    )


class OfficeIdWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    officeId: OfficeId | None = None


class Option(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayCode: str = Field(
        ..., description='The code which is sent in answer response.'
    )
    displayValue: str | None = Field(
        None, description='The text to be displayed to the user beside this option.'
    )


class OptionSource(Enum):
    MANUAL = 'MANUAL'
    COMPANY_CONFIG = 'COMPANY_CONFIG'


class OrganizationAgencyId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str


class OrganizationId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str


class OtherAncillaryDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ancillaryType: AncillaryType
    flightIndex: int = Field(
        ...,
        description='Index referencing the flight for which the changes are requested.',
        examples=[1],
    )
    legIndex: int = Field(
        ...,
        description='Index referencing the leg for which the changes are requested.',
        examples=[1],
    )


class OtherServiceInformation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    info: str = Field(
        ...,
        description='Free text field for adding Other Service Information.',
        examples=['VIP Passenger.'],
    )
    flightIndex: int | None = Field(
        None,
        description='Index referencing the flight for which the changes are requested.',
        examples=[2],
    )


class OwnershipLabel(Enum):
    CORPORATE = 'CORPORATE'
    PERSONAL = 'PERSONAL'
    CENTRAL = 'CENTRAL'


class PaginationResponseParams(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numResults: int = Field(
        ..., description='Total number of results in the paginated list.'
    )
    numPages: int = Field(
        ..., description='Total number of pages in the paginated list.'
    )


class PassengerAge(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numYears: int = Field(..., description='Age of the passenger', examples=[22])


class PassengerCapacityAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['Standard passenger capacity'])
    passengerCapacityDescription: str | None = Field(
        None, examples=['Ticket sales are not limited for this flight']
    )
    passengerCapacityAttrDescription: str | None = Field(None, examples=['no'])


class PassengerCapacityAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    passengerCapacityAmenity: PassengerCapacityAmenity | None = None


class PassengerType(Enum):
    UNKNOWN_PASSENGER_TYPE = 'UNKNOWN_PASSENGER_TYPE'
    ADULT = 'ADULT'
    CHILD = 'CHILD'
    INFANT = 'INFANT'
    INFANT_ON_LAP = 'INFANT_ON_LAP'
    YOUTH = 'YOUTH'
    SENIOR = 'SENIOR'
    TEEN = 'TEEN'


class Type6(Enum):
    UNKNOWN = 'UNKNOWN'
    REGULAR = 'REGULAR'


class Passport(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    docId: str = Field(..., examples=['PassportID'])
    expiryDate: DateModel
    issueCountry: str = Field(..., examples=['IN'])
    issuedDate: DateModel | None = None
    nationalityCountry: str = Field(..., examples=['IN'])
    type: Type6 | None = Field(None, examples=['REGULAR'])


class PassportWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    passport: Passport | None = None


class ApplicableToEnum(Enum):
    UNKNOWN_APPLICABLE_TO = 'UNKNOWN_APPLICABLE_TO'
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    RAIL = 'RAIL'
    CAR = 'CAR'
    SERVICE_FEE = 'SERVICE_FEE'


class PaymentItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    itemType: ItemType | None = Field(
        None, description='Type of payment item eligible for this fop rule'
    )
    fareComponent: Sequence[FareComponent] | None = None


class PaymentMethod(Enum):
    PAYMENT_METHOD_UNKNOWN = 'PAYMENT_METHOD_UNKNOWN'
    CREDIT_CARD = 'CREDIT_CARD'
    BREX_POINTS = 'BREX_POINTS'
    CASH = 'CASH'
    QANTAS_POINTS = 'QANTAS_POINTS'
    VENDOR_PROGRAM_PAYMENT = 'VENDOR_PROGRAM_PAYMENT'
    DELAYED_INVOICING = 'DELAYED_INVOICING'
    FLIGHT_CREDITS = 'FLIGHT_CREDITS'
    QANTAS_TRAVEL_FUND = 'QANTAS_TRAVEL_FUND'
    CUSTOM_VIRTUAL_PAYMENT = 'CUSTOM_VIRTUAL_PAYMENT'


class PaymentSourceCondition(RootModel[CardCondition]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: CardCondition = Field(
        ...,
        description='The payment source fop condition',
        discriminator='type',
        title='PaymentSourceCondition',
    )


class PaymentSourcePrePaymentAdditionalInformation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    loyaltyAccountNumber: str | None = Field(
        None, description='Loyalty account number in case rewards program is used'
    )
    loyaltyProgramName: str | None = Field(
        None, description='Loyalty program name in case rewards program is used'
    )
    canStoreCreditCard: bool | None = Field(
        None,
        description="Is payment provider allowed to store user's credit card information",
    )


class PaymentSourcePrePaymentInformation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentSourceId: UUID | None = Field(
        None,
        description='Unique identifier identifying this payment source.',
        examples=['f49d00fe-1eda-4304-ba79-a980f565281d'],
    )
    paymentRequestId: str | None = Field(
        None,
        description='Payment source initialization request id',
        examples=['XHY675ER7Y2'],
    )
    additionalInformation: PaymentSourcePrePaymentAdditionalInformation | None = None


class PaymentSplitCriterion(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    accessType: AccessType | None = Field(
        None,
        description='Access Type of the Payment Sources applicable for a portion of payment.',
    )


class PercentageWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    percentage: float | None = None


class Persona(Enum):
    UNKNOWN_PERSONA = 'UNKNOWN_PERSONA'
    EMPLOYEE = 'EMPLOYEE'
    GUEST = 'GUEST'
    PERSONAL = 'PERSONAL'
    RELATIVE = 'RELATIVE'
    ADHOC = 'ADHOC'


class PersonaListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    personaList: Sequence[Persona] | None = None


class PersonaWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    persona: Persona | None = None


class CountryCodeSource(Enum):
    UNSPECIFIED = 'UNSPECIFIED'
    FROM_NUMBER_WITH_PLUS_SIGN = 'FROM_NUMBER_WITH_PLUS_SIGN'
    FROM_NUMBER_WITH_IDD = 'FROM_NUMBER_WITH_IDD'
    FROM_NUMBER_WITHOUT_PLUS_SIGN = 'FROM_NUMBER_WITHOUT_PLUS_SIGN'
    FROM_DEFAULT_COUNTRY = 'FROM_DEFAULT_COUNTRY'


class Type7(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    MOBILE = 'MOBILE'
    LANDLINE = 'LANDLINE'


class PhoneNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    countryCode: int | None = Field(
        None, description='two digit country code', examples=[91]
    )
    countryCodeSource: CountryCodeSource | None = Field(
        None, examples=['FROM_NUMBER_WITH_PLUS_SIGN']
    )
    extension: str | None = Field(
        None, description='phone number extension', examples=['222']
    )
    isoCountryCode: str | None = Field(
        None, description='ISO alpha-2 code', examples=['IN']
    )
    italianLeadingZero: bool | None = Field(False, examples=[True])
    nationalNumber: int | None = Field(None, examples=[8150])
    numberOfLeadingZeros: int | None = Field(0, examples=[1])
    preferredDomesticCarrierCode: str | None = Field(None, examples=['7'])
    rawInput: str | None = Field(None, examples=['77777'])
    type: Type7 | None = Field(None, examples=['MOBILE'])


class PnrItinerary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str = Field(..., description='The PNR ID.', examples=['1234567812'])


class PnrItineraryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnr: PnrItinerary | None = None


class ApprovalType(Enum):
    HARD_APPROVAL = 'HARD_APPROVAL'
    SOFT_APPROVAL = 'SOFT_APPROVAL'
    PASSIVE_APPROVAL = 'PASSIVE_APPROVAL'


class PolicyType(Enum):
    GLOBAL = 'GLOBAL'
    DEFAULT = 'DEFAULT'
    GROUP = 'GROUP'


class PnrPolicyId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., description='Policy id')
    version: str = Field(..., description='version of the policy')
    policyName: str | None = Field(None, description='Name of the policy applied.')
    approvalType: ApprovalType | None = Field(None, examples=['SOFT_APPROVAL'])
    policyType: PolicyType | None = Field(None, examples=['GLOBAL'])


class PnrStatusModel(Enum):
    UNKNOWN = 'UNKNOWN'
    INITIATED = 'INITIATED'
    CANCELLED = 'CANCELLED'
    CONFIRMED = 'CONFIRMED'
    GROUP_BOOKING_ON_REQUEST = 'GROUP_BOOKING_ON_REQUEST'
    WAITLISTED = 'WAITLISTED'
    PENDING = 'PENDING'
    AIRLINE_UPGRADE = 'AIRLINE_UPGRADE'
    WAITLIST_CONFIRMED = 'WAITLIST_CONFIRMED'
    BOOKING_DENIED_CONTACT_SUPPORT = 'BOOKING_DENIED_CONTACT_SUPPORT'
    NO_SHOW = 'NO_SHOW'
    CONTACT_SUPPORT = 'CONTACT_SUPPORT'
    STATUS_CHANGED_CONTACT_SUPPORT = 'STATUS_CHANGED_CONTACT_SUPPORT'
    SCHEDULE_CHANGE = 'SCHEDULE_CHANGE'
    SEGMENT_REQUEST = 'SEGMENT_REQUEST'
    SCHEDULE_CHANGE_WAITLISTED_BOOKING = 'SCHEDULE_CHANGE_WAITLISTED_BOOKING'
    REQUEST_PENDING = 'REQUEST_PENDING'
    WAITLISTED_NOT_CONFIRMED = 'WAITLISTED_NOT_CONFIRMED'
    SCHEDULE_CHANGE_NOT_CONFIRMED = 'SCHEDULE_CHANGE_NOT_CONFIRMED'
    SCHEDULE_CHANGE_PENDING_STATUS = 'SCHEDULE_CHANGE_PENDING_STATUS'
    MIS_CONNECTION = 'MIS_CONNECTION'
    REQUESTED = 'REQUESTED'
    TICKETED = 'TICKETED'
    VOIDED = 'VOIDED'
    CANCELLED_BY_VENDOR = 'CANCELLED_BY_VENDOR'
    CANCELLATION_IN_PROGRESS = 'CANCELLATION_IN_PROGRESS'
    REINSTATED = 'REINSTATED'
    BOOKING_ON_HOLD = 'BOOKING_ON_HOLD'
    AIRLINE_CONTROL = 'AIRLINE_CONTROL'
    MODIFIED = 'MODIFIED'
    PAYMENT_DECLINED = 'PAYMENT_DECLINED'
    INOPERATIVE = 'INOPERATIVE'
    UNCONFIRMED = 'UNCONFIRMED'


class PolicyAlertOnSelectionAction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    message: str | None = None


class PolicyAlertOnSelectionActionWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    alertOnSelection: PolicyAlertOnSelectionAction | None = None


class PolicyFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    onlyInPolicy: bool | None = Field(
        None, description='If true, only in-policy itineraries are returned.'
    )


class PolicyFlagAction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    message: str | None = None


class PolicyFlagActionWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flag: PolicyFlagAction | None = None


class PolicyHideActionWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hide: bool | None = None


class PolicyPredicate(Enum):
    UNKNOWN_PREDICATE_STRING = 'UNKNOWN_PREDICATE_STRING'
    MAX_FARE_PER_TRAVELLER_VIOLATION = 'MAX_FARE_PER_TRAVELLER_VIOLATION'
    FARE_MORE_THAN_MINIMUM = 'FARE_MORE_THAN_MINIMUM'
    FARE_MORE_THAN_MEDIAN = 'FARE_MORE_THAN_MEDIAN'
    FARE_LESS_THAN_MEDIAN = 'FARE_LESS_THAN_MEDIAN'
    FARE_MORE_THAN_LLF = 'FARE_MORE_THAN_LLF'
    MAX_FARE_PER_TRAVELLER_VIOLATION_INCLUDING_TAX = (
        'MAX_FARE_PER_TRAVELLER_VIOLATION_INCLUDING_TAX'
    )
    MAX_FARE_PER_TRAVELLER_VIOLATION_EXCLUDING_TAX = (
        'MAX_FARE_PER_TRAVELLER_VIOLATION_EXCLUDING_TAX'
    )
    HOTEL_PAYMENT_OPTIONS_VIOLATION = 'HOTEL_PAYMENT_OPTIONS_VIOLATION'
    RAIL_BOOKING_WINDOW_GAP_VIOLATION = 'RAIL_BOOKING_WINDOW_GAP_VIOLATION'
    RAIL_TRAVEL_CLASS_VIOLATION = 'RAIL_TRAVEL_CLASS_VIOLATION'
    RAIL_TICKET_REFUNDABLE_VIOLATION = 'RAIL_TICKET_REFUNDABLE_VIOLATION'
    RAIL_MAX_BOOKING_PRICE_VIOLATION_INCLUDING_TAX = (
        'RAIL_MAX_BOOKING_PRICE_VIOLATION_INCLUDING_TAX'
    )
    RAIL_MAX_BOOKING_PRICE_VIOLATION_EXCLUDING_TAX = (
        'RAIL_MAX_BOOKING_PRICE_VIOLATION_EXCLUDING_TAX'
    )
    AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_INCLUDING_TAX = (
        'AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_INCLUDING_TAX'
    )
    AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_EXCLUDING_TAX = (
        'AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_EXCLUDING_TAX'
    )
    HOTEL_RESTRICTED_KEYWORDS_VIOLATION = 'HOTEL_RESTRICTED_KEYWORDS_VIOLATION'
    RESTRICTED_LOCATION_VIOLATION = 'RESTRICTED_LOCATION_VIOLATION'
    FLIGHT_ADVANCE_BOOKING_WINDOW_DOMESTIC_VIOLATION = (
        'FLIGHT_ADVANCE_BOOKING_WINDOW_DOMESTIC_VIOLATION'
    )
    FLIGHT_ADVANCE_BOOKING_WINDOW_INTERNATIONAL_VIOLATION = (
        'FLIGHT_ADVANCE_BOOKING_WINDOW_INTERNATIONAL_VIOLATION'
    )
    FLIGHT_ADVANCE_BOOKING_WINDOW_VIOLATION = 'FLIGHT_ADVANCE_BOOKING_WINDOW_VIOLATION'
    ITINERARY_WITHIN_EVENT_TRAVEL_WINDOW = 'ITINERARY_WITHIN_EVENT_TRAVEL_WINDOW'
    HOTEL_IN_ALLOWED_HOTEL_LIST = 'HOTEL_IN_ALLOWED_HOTEL_LIST'
    PAYMENT_ACCESS_VIOLATION = 'PAYMENT_ACCESS_VIOLATION'
    AIRPORT_IN_ALLOWED_AIRPORT_LIST = 'AIRPORT_IN_ALLOWED_AIRPORT_LIST'
    ITINERARY_TYPE_IS_NOT_IN_ALLOWED_BOOKING_TYPES = (
        'ITINERARY_TYPE_IS_NOT_IN_ALLOWED_BOOKING_TYPES'
    )
    PAYMENT_AIR_ADDON_VIOLATION = 'PAYMENT_AIR_ADDON_VIOLATION'
    MAX_HOTEL_BOOKING_PRICE_INCLUDING_TAX = 'MAX_HOTEL_BOOKING_PRICE_INCLUDING_TAX'
    MAX_HOTEL_BOOKING_PRICE_EXCLUDING_TAX = 'MAX_HOTEL_BOOKING_PRICE_EXCLUDING_TAX'
    AIR_NUM_TRAVELERS_ALLOWED = 'AIR_NUM_TRAVELERS_ALLOWED'
    PREFERRED_VENDOR_VIOLATION = 'PREFERRED_VENDOR_VIOLATION'
    SEAT_ADDON_VIOLATION = 'SEAT_ADDON_VIOLATION'
    BAGGAGE_ADDON_VIOLATION = 'BAGGAGE_ADDON_VIOLATION'
    EARLY_BIRD_ADDON_VIOLATION = 'EARLY_BIRD_ADDON_VIOLATION'
    WIFI_ADDON_VIOLATION = 'WIFI_ADDON_VIOLATION'
    RESTRICTED_BOOKING_VIOLATION = 'RESTRICTED_BOOKING_VIOLATION'
    HIGHEST_ALLOWED_CABIN_VIOLATION = 'HIGHEST_ALLOWED_CABIN_VIOLATION'
    LOWEST_FARE_PER_HOTEL_PROPERTY_VIOLATION = (
        'LOWEST_FARE_PER_HOTEL_PROPERTY_VIOLATION'
    )


class PolicyPreventBookingAction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    prevent: bool | None = Field(
        None,
        description='True if booking is to be blocked if rule is violated, else false',
    )
    reason: str | None = Field(
        None,
        description='Reason describing why was that specific itinerary not allowed to book.',
    )


class PolicyPreventBookingActionWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preventBooking: PolicyPreventBookingAction | None = None


class PostStripeVerificationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentMethodId: str | None = Field(
        None,
        description="Stripe's payment method id for which the verification was performed.",
        examples=['pm_1HzKDPI3bT9GUjvoUkRQooN3'],
    )


class PostStripeVerificationInfoWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    postStripeVerificationInfo: PostStripeVerificationInfo | None = None


class PostUrlBasedVerificationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str | None = Field(
        None,
        description='Pnr Id, to continue with the booking flow/check the status of the pnr.',
    )


class PostUrlBasedVerificationInfoWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    postUrlBasedVerificationInfo: PostUrlBasedVerificationInfo | None = None


class PostalAddress(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    addressLines: Sequence[str] = Field(..., description='Address lines')
    administrativeArea: str | None = Field(
        None,
        description='Code of administrative area. For example: DL for Delhi, India.\nHighest administrative subdivision which is used for postal\naddresses of a country or region.\nFor example, this can be a state, a province, an oblast, or a prefecture.\nSpecifically, for Spain this is the province and not the autonomous\ncommunity (e.g. "Barcelona" and not "Catalonia").\nMany countries don\'t use an administrative area in postal addresses. E.g.\nin Switzerland this should be left unpopulated.\n',
        examples=['CA'],
    )
    administrativeAreaName: str | None = Field(
        None,
        description='Name of administrative area. This is full name corresponding to administrativeArea. \nLike Delhi for DL area code. For some places, code and name maybe same as well like Tokyo.\n',
        examples=['California'],
    )
    description: str | None = Field(
        None, description='Address description', examples=['San Francisco Home']
    )
    isDefault: bool | None = Field(
        None,
        description='Whether this address is default address in case multiple addresses are specified.',
        examples=[True],
    )
    languageCode: str | None = Field(
        None,
        description='BCP-47 language code of the contents of this address (if known). This is often the UI \nlanguage of the input form or is expected to match one of the languages used in the \naddress\' country/region, or their transliterated equivalents.\nThis can affect formatting in certain countries, but is not critical to the correctness \nof the data and will never affect any validation or other non-formatting related operations.\nExamples: "zh-Hant", "ja", "ja-Latn", "en".\n',
        examples=['en'],
    )
    locality: str | None = Field(
        None,
        description='Generally refers to the city/town portion of the address.',
        examples=['San Francisco'],
    )
    locationCode: str | None = Field(
        None,
        description='IATA 3-letter location code. See https://www.iata.org/en/services/codes.',
        examples=['LAX'],
    )
    organization: str | None = Field(
        None,
        description='The name of the organization at the address.',
        examples=['Spotnana'],
    )
    postalCode: str | None = Field(
        None,
        description='Postal code of the address. This is a required field when setting for a user/legal entity/company etc.',
        examples=['94130'],
    )
    continentCode: str | None = Field(
        None,
        description='2 letter continent code of the continent this address falls in.',
        examples=['AF'],
    )
    recipients: Sequence[str] | None = Field(
        None, description='The recipient at the address.'
    )
    regionCode: str = Field(
        ...,
        description='Region code of the country/region of the address.',
        examples=['US'],
    )
    regionName: str | None = Field(
        None,
        description='Region name of the country/region of the address.',
        examples=['America'],
    )
    revision: int | None = Field(None, examples=[1])
    sortingCode: str | None = Field(
        None,
        description='Additional, country-specific, sorting code. This is not used\nin most regions. Where it is used, the value is either a string like\n"CEDEX", optionally followed by a number (e.g. "CEDEX 7"), or just a number\nalone, representing the "sector code" (Jamaica), "delivery area indicator"\n(Malawi) or "post office indicator" (e.g. Côte d\'Ivoire).\n',
        examples=['Jamaica'],
    )
    sublocality: str | None = Field(
        None,
        description='Sublocality of the address. This can be neighborhoods, boroughs, districts.',
    )
    timezone: str | None = Field(
        None, description='Time zone of the address.', examples=['America/Los_Angeles']
    )
    coordinates: Latlng | None = Field(
        None, description='Map coordinates of the address.'
    )


class PostalAddressWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    postalAddress: PostalAddress | None = None


class PowerAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['Power USB outlets'])
    powerType: str | None = Field(None, examples=['power/usb'])
    cost: str | None = Field(None, examples=['free'])
    usbPort: str | None = Field(None, examples=['yes'])
    powerOutlet: str | None = Field(None, examples=['yes'])


class PowerAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    powerAmenity: PowerAmenity | None = None


class PreCheckoutQuestionType(Enum):
    UNKNOWN_CHECKOUT_QUESTION_TYPE = 'UNKNOWN_CHECKOUT_QUESTION_TYPE'
    USER_DEFINED_QUESTION = 'USER_DEFINED_QUESTION'
    OOP_REASON_CODE = 'OOP_REASON_CODE'


class PreSearchQuestionType(Enum):
    UNKNOWN_SEARCH_QUESTION_TYPE = 'UNKNOWN_SEARCH_QUESTION_TYPE'
    PURPOSE_OF_TRIP = 'PURPOSE_OF_TRIP'


class PreferredLocationLabel(Enum):
    HOME = 'HOME'
    WORK = 'WORK'
    OTHER = 'OTHER'


class PreferredRailStation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    stationName: str | None = Field(
        None, description='Rail station name.', examples=['Chicago Union Station']
    )
    stationCode: str = Field(..., description='Rail station code.', examples=['CHI'])
    cityName: str | None = Field(
        None,
        description='Name of city where the rail station is located.',
        examples=['Chicago'],
    )
    countryCode: str | None = Field(
        None, description='Alpha-2 country code where the rail station is located.'
    )
    label: PreferredLocationLabel


class PrimaryServiceProviderTmc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tmcId: CompanyId = Field(..., description='Id of the service provider TMC.')


class PromotionCode(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    code: str = Field(..., description='Promotion code string', examples=['NEWUSER'])


class QuestionFormat(Enum):
    INPUT_BOX = 'INPUT_BOX'
    RADIO_BUTTON = 'RADIO_BUTTON'
    CHECKBOX = 'CHECKBOX'
    CHECKBOX_WITH_PERCENTAGE = 'CHECKBOX_WITH_PERCENTAGE'


class QuestionType(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preSearchQuestionType: PreSearchQuestionType | None = None
    preCheckoutQuestionType: PreCheckoutQuestionType | None = None


class RailCard(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cardNumber: str | None = Field(None, description='Number of card')
    expiryDate: DateModel | None = Field(
        None, description='Expiry date of the Rail Card.'
    )
    name: str = Field(
        ..., description='Name of the Rail Card.', examples=['Veterans Railcard']
    )
    spotnanaCode: str = Field(
        ...,
        description='Unique Spotnana code/identifier for Rail Card.',
        examples=['VET'],
    )
    vendor: str = Field(..., description='Vendor Name.', examples=['ATOC'])


class RailTravelClass(Enum):
    FIRST = 'FIRST'
    STANDARD = 'STANDARD'
    BUSINESS = 'BUSINESS'
    SLEEPER = 'SLEEPER'
    STANDARD_PREMIUM = 'STANDARD_PREMIUM'
    BUSINESS_PREMIUM = 'BUSINESS_PREMIUM'
    COACH = 'COACH'
    ROOM = 'ROOM'
    EXECUTIVE = 'EXECUTIVE'


class RateType(Enum):
    RATE_TYPE_UNKNOWN = 'RATE_TYPE_UNKNOWN'
    PUBLISHED = 'PUBLISHED'
    TMC_NEGOTIATED = 'TMC_NEGOTIATED'
    COMPANY_NEGOTIATED = 'COMPANY_NEGOTIATED'


class RatingWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    rating: float | None = None


class RazorpayPostVerificationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentId: str = Field(
        ...,
        description='Razorpay payment id for which the verification was performed.',
        examples=['pay_Ot3t93Xwmh2hOg'],
    )
    orderId: str | None = Field(
        None,
        description='Razorpay order id in which the payment was added.',
        examples=['order_OshJ61KRRSzb2Q'],
    )
    signature: str | None = Field(
        None,
        description='Razorpay signature to validate the payment.',
        examples=['f2c0494b77cb067a0ac76973d65cdce8c9de593a55ef2f81969cbabbe7ce493d'],
    )


class RazorpayPostVerificationInfoWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    postRazorpayVerificationInfo: RazorpayPostVerificationInfo | None = None


class RazorpayVerificationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentProcessor: str = Field(
        ..., description='Payment processor involved.', examples=['RAZORPAY']
    )
    paymentAuthUrl: str | None = Field(
        None, description='URL for authorizing the payment on the card.'
    )


class RazorpayVerificationInfoWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    razorPayVerificationInfo: RazorpayVerificationInfo | None = None


class RedressNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    number: str = Field(..., examples=['12345'])
    issueCountry: str = Field(..., examples=['US'])


class RedressNumberWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    redress: RedressNumber | None = None


class Reference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID
    name: str | None = None


class Remark(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    text: str = Field(..., description='Remark text.', examples=['Seat 11A selected'])


class RestrictedKeywordsWithReason(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    keyword: str | None = Field(
        None, description='Restricted keyword', examples=['Test Keyword']
    )
    reason: str | None = Field(
        None, description='Reason for restriction', examples=['Test Reason']
    )


class BedCount(Enum):
    ONE_BED = 'ONE_BED'
    TWO_BEDS = 'TWO_BEDS'


class RoomType(Enum):
    SMOKING = 'SMOKING'
    NON_SMOKING = 'NON_SMOKING'


class MostImportantFact(Enum):
    ROOM_TYPE = 'ROOM_TYPE'
    BED_COUNT = 'BED_COUNT'
    ROOM_LOCATION = 'ROOM_LOCATION'


class RoomLocation(Enum):
    HIGH_FLOOR = 'HIGH_FLOOR'
    LOW_FLOOR = 'LOW_FLOOR'
    NEAR_ELEVATOR = 'NEAR_ELEVATOR'


class PillowType(Enum):
    FOAM = 'FOAM'
    EXTRA_FOAM = 'EXTRA_FOAM'
    EXTRA_FEATHER = 'EXTRA_FEATHER'


class RoomAmenityPref(Enum):
    FEATHER_FREE_ROOM = 'FEATHER_FREE_ROOM'
    EXTRA_TOWELS = 'EXTRA_TOWELS'
    REFRIGERATOR = 'REFRIGERATOR'


class RoomPreference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    isMobilityAccessible: bool | None = Field(
        False,
        description='Whether or not mobility accessible room, tub.',
        examples=[False],
    )
    bedCount: BedCount | None = Field(
        None, description='The number of bed in the room.', examples=['ONE_BED']
    )
    roomType: RoomType | None = Field(
        None, description='Single selection of type of room.', examples=['SMOKING']
    )
    mostImportantFact: MostImportantFact | None = Field(
        None,
        description='Single selection of the most import fact.',
        examples=['BED_COUNT'],
    )
    roomLocation: RoomLocation | None = Field(
        None, description='Location of the hotel room', examples=['HIGH_FLOOR']
    )
    pillowType: PillowType | None = Field(
        None, description='The type of pillow in hotel room.', examples=['FOAM']
    )
    roomAmenityPrefs: Sequence[RoomAmenityPref] | None = None


class RowType(Enum):
    ROW = 'ROW'
    BUFFER = 'BUFFER'
    EXIT = 'EXIT'
    EXTRA_LEG_ROOM = 'EXTRA_LEG_ROOM'
    NO_ROW = 'NO_ROW'


class RuleDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    title: str | None = None
    text: str | None = None


class Rules(RootModel[Sequence[RuleDetail]]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Sequence[RuleDetail]


class SSRIndexes(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legIndex: int = Field(
        ...,
        description='Index referencing the flight leg for which the SSR should be added. Should be used in conjunction with the flight Index.',
        examples=[1],
    )
    flightIndex: int = Field(
        ...,
        description='Index referencing the flight for which the SSR should be added. Should be used in conjunction with the leg Index.',
        examples=[2],
    )


class SeatAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['Full flat all aisle access'])
    seatType: str | None = Field(None, examples=['full flat pod'])
    width: str | None = None
    legroom: str | None = None
    pitch: float | None = Field(None, examples=[78])


class SeatAmenityType(Enum):
    UNKNOWN_AIR_SEAT_AMENITY_TYPE = 'UNKNOWN_AIR_SEAT_AMENITY_TYPE'
    FLAT_BED = 'FLAT_BED'
    WIFI = 'WIFI'
    IN_SEAT_POWER = 'IN_SEAT_POWER'


class SeatAmenityPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    seatAmenityTypes: Sequence[SeatAmenityType]


class SeatAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    seatAmenity: SeatAmenity | None = None


class SeatDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightIndex: int = Field(
        ...,
        description='Index referencing the flight for which the changes are requested.',
        examples=[1],
    )
    seatNumber: str = Field(
        ..., description='Selected seat number for the flight.', examples=['10A']
    )


class SeatIndexes(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legIndex: int = Field(
        ..., description='The leg index for which the seat map is requested.'
    )
    flightIndex: Sequence[int] | None = Field(
        None,
        description='The flight index for which the seat map is requested. It is optional and if it not present, then all the flights will be returned for this leg',
    )


class SeatLocation(Enum):
    FRONT = 'FRONT'
    MIDDLE = 'MIDDLE'
    REAR = 'REAR'
    LEFT = 'LEFT'
    LEFT_CENTER = 'LEFT_CENTER'
    RIGHT = 'RIGHT'
    RIGHT_CENTER = 'RIGHT_CENTER'
    CENTER = 'CENTER'
    UPPER_DECK = 'UPPER_DECK'
    LOWER_DECK = 'LOWER_DECK'
    MAIN_DECK = 'MAIN_DECK'


class Position(Enum):
    UNKNOWN_POSITION = 'UNKNOWN_POSITION'
    AISLE = 'AISLE'
    WINDOW = 'WINDOW'
    AISLE_OR_WINDOW = 'AISLE_OR_WINDOW'


class SeatLocationPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cabins: Sequence[Cabin] | None = None
    isBulkHeadPref: bool | None = Field(None, examples=[False])
    maxFlightDurationInHours: int | None = Field(None, examples=[3])
    position: Position | None = Field(None, examples=['WINDOW'])


class SeatLoyaltyInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightId: str | None = Field(
        None,
        description='The flight identifier for which this loyalty is being applied.',
        examples=['flight_0'],
    )
    loyaltyInfos: Sequence[LoyaltyInfo] | None = None


class SeatPrefDirection(Enum):
    FORWARD = 'FORWARD'
    BACKWARD = 'BACKWARD'


class SeatPrefLocation(Enum):
    AISLE = 'AISLE'
    WINDOW = 'WINDOW'
    SOLO = 'SOLO'


class SeatPrefType(Enum):
    SLEEPER_BED = 'SLEEPER_BED'
    NORMAL = 'NORMAL'
    TABLE_SEAT = 'TABLE_SEAT'


class Type8(Enum):
    SEAT = 'SEAT'
    NO_SEAT = 'NO_SEAT'
    BULKHEAD = 'BULKHEAD'
    STAIRS = 'STAIRS'
    LAVATORY = 'LAVATORY'
    BAR = 'BAR'
    CLOSET = 'CLOSET'
    AIR_PHONE = 'AIR_PHONE'
    EXIT_DOOR = 'EXIT_DOOR'
    EMERGENCY_EXIT = 'EMERGENCY_EXIT'
    GALLEY = 'GALLEY'
    LUGGAGE_STORAGE = 'LUGGAGE_STORAGE'
    STORAGE_SPACE = 'STORAGE_SPACE'
    TABLE = 'TABLE'


class Limitation(Enum):
    RESTRICTED_RECLINE = 'RESTRICTED_RECLINE'
    NOT_ALLOWED_FOR_INFANT = 'NOT_ALLOWED_FOR_INFANT'
    NOT_ALLOWED_FOR_UNACCOMPANIED_MINOR = 'NOT_ALLOWED_FOR_UNACCOMPANIED_MINOR'
    NOT_SUITABLE_FOR_CHILD = 'NOT_SUITABLE_FOR_CHILD'
    WINDOW_SEAT_WITHOUT_WINDOW = 'WINDOW_SEAT_WITHOUT_WINDOW'
    CREW_SEAT = 'CREW_SEAT'
    NOT_BOOKABLE_ON_OBT = 'NOT_BOOKABLE_ON_OBT'
    RESTRICTED_GENERAL = 'RESTRICTED_GENERAL'
    NO_FARE_INFORMATION = 'NO_FARE_INFORMATION'
    LOYALTY_LEVEL_REQUIRED = 'LOYALTY_LEVEL_REQUIRED'


class Facility1(Enum):
    LEG_SPACE = 'LEG_SPACE'
    BASSINET = 'BASSINET'
    SMOKING = 'SMOKING'
    NON_SMOKING = 'NON_SMOKING'
    PET_IN_CABIN = 'PET_IN_CABIN'
    SUITABLE_FOR_ADULT_WITH_INFANT = 'SUITABLE_FOR_ADULT_WITH_INFANT'
    SUITABLE_FOR_UNACCOMPANIED_MINOR = 'SUITABLE_FOR_UNACCOMPANIED_MINOR'
    FACILITIES_FOR_HANDICAPPED_INCAPACITATED = (
        'FACILITIES_FOR_HANDICAPPED_INCAPACITATED'
    )
    PREFERRED_SEAT = 'PREFERRED_SEAT'


class LocationEnum(Enum):
    BULKHEAD_ROW = 'BULKHEAD_ROW'
    EXIT_ROW = 'EXIT_ROW'
    LEFT_FACING_FLAT_BED = 'LEFT_FACING_FLAT_BED'
    RIGHT_FACING_FLAT_BED = 'RIGHT_FACING_FLAT_BED'


class SeatSelectionRule(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str = Field(
        ...,
        description='Seat selection policy text',
        examples=['Seat selection for free'],
    )


class SeatType(Enum):
    UNKNOWN = 'UNKNOWN'
    FLAT = 'FLAT'
    RECLINER = 'RECLINER'
    SKYCOUCH = 'SKYCOUCH'
    PRIVATE_SUITE = 'PRIVATE_SUITE'


class SeatTypeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    seatType: str = Field(
        ...,
        description='Description of the type of seat available',
        examples=['Recliner seat'],
    )


class SelectedAncillary(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ancillaryId: str = Field(
        ...,
        description='The ID of the selected ancillary option.',
        examples=['ancillary_0'],
    )


class SelectedBaggage(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legId: str = Field(
        ..., description='The ID of the leg associated with this baggage.'
    )
    baggageIds: Sequence[str] = Field(
        ...,
        description='The IDs of the selected baggage options from the flight checkout API response.',
    )


class SelectedSeat(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightId: str = Field(
        ..., description='The ID of the flight associated with this baggage.'
    )
    seatNumbers: Sequence[str] | None = Field(
        None, description='The seat numbers selected for the associated flight.'
    )


class Unit1(Enum):
    CENTIMETER = 'CENTIMETER'
    METER = 'METER'


class SizeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    length: float | None = Field(
        None, description='The weight limit of a baggage option.', examples=[32]
    )
    unit: Unit1 | None = Field(
        None, description='The unit of measurement for the bag size.'
    )


class SortBy(Enum):
    PRICE = 'PRICE'
    DEPARTURE_TIME = 'DEPARTURE_TIME'
    ARRIVAL_TIME = 'ARRIVAL_TIME'
    DURATION = 'DURATION'


class SortOrder(Enum):
    ASCENDING = 'ASCENDING'
    DESCENDING = 'DESCENDING'


class SpecialServiceRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    code: str = Field(
        ...,
        description='Unique 4-letter code for the service request.',
        examples=['WCHC'],
    )
    category: Category = Field(
        ..., description='Category of the Special Service Request.'
    )
    subCategory: str = Field(
        ...,
        description='The type of Special Service Request (SSR).',
        examples=['Wheelchair with wet cell battery.'],
    )
    isFreeTextMandatory: bool | None = Field(
        False,
        description='Indicates if free text input is mandatory for the Special Service Request (SSR).',
        examples=[True],
    )
    isFreeTextAllowed: bool | None = Field(
        False,
        description='Indicates whether adding free text is supported.\nSSRs that support free text will allow you to add additional information about the service request.\n',
        examples=[True],
    )


class SpecialServiceRequestInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    indexes: SSRIndexes | None = None
    flightIndex: int | None = Field(
        None,
        description='Index referencing the flight for which the changes are requested.',
        examples=[0],
    )
    code: str = Field(
        ...,
        description='Unique 4-letter code for the Special Service Request (SSR).',
        examples=['WCHC'],
    )
    info: str | None = Field(
        None,
        description='Free text field for adding additional details about the Special Service Request (SSR).',
        examples=['Special meal without egg.'],
    )


class SpecialServiceRequestsResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    specialServiceRequests: Sequence[SpecialServiceRequest] | None = None


class StringListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    sList: Sequence[str] | None = None


class StringWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    s: str | None = None


class StripeCardTokens(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentProcessor: str = Field(
        ..., description='Payment processor involved.', examples=['STRIPE']
    )
    paymentMethodId: str = Field(
        ...,
        description="Stripe's payment-method-id for the card.",
        examples=['pm_1HzKDPI3bT9GUjvoUkRQooN3'],
    )
    setupIntentClientSecret: str = Field(
        ...,
        description='Stripe client secret for attaching the card to the setup-intent.',
        examples=[
            'seti_1IBwEiKXFM6BpWYhfdCGZ7sg_secret_InXJDifWGBcVqekJvhAxidON6vHAvfS'
        ],
    )
    connectedAccountId: str | None = Field(
        None,
        description='Account Id of the connected account in which above payment-method and setup-intent exist. This is populated only when a connected account is used.',
        examples=['acct_1NT8oyCZnFgI0lFY'],
    )
    publishableKey: str | None = Field(
        None,
        description='Publishable key of the Stripe platform account.',
        examples=['pk_test_51Hx7XyI3bT9GUjvoUkRQooN3'],
    )


class StripeVerificationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    stripeCardTokens: StripeCardTokens | None = None


class StripeVerificationInfoWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    stripeVerificationInfo: StripeVerificationInfo | None = None


class SupplierType(Enum):
    SABRE = 'SABRE'
    AMADEUS = 'AMADEUS'
    TRAVEL_FUSION = 'TRAVEL_FUSION'
    FARELOGIX_NDC = 'FARELOGIX_NDC'
    ATPCO_NDC = 'ATPCO_NDC'
    TRAINLINE = 'TRAINLINE'
    AVIA = 'AVIA'
    QBR = 'QBR'
    BCD = 'BCD'
    QANTAS_HOTELS = 'QANTAS_HOTELS'
    SOUTHWEST = 'SOUTHWEST'
    EXPEDIA = 'EXPEDIA'
    HOTEL_HUB = 'HOTEL_HUB'
    NDC = 'NDC'
    MARRIOTT = 'MARRIOTT'
    CLEARTRIP = 'CLEARTRIP'
    KYTE = 'KYTE'
    GROUNDSPAN = 'GROUNDSPAN'
    SABRE_NDC = 'SABRE_NDC'
    BOOKING_COM = 'BOOKING_COM'


class TemperatureAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['No pre-flight temperature check'])
    temperatureDescription: str | None = Field(
        None, examples=['A temperature check is not required before boarding']
    )
    temperatureAttrDescription: str | None = Field(None, examples=['no'])


class TemperatureAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    temperatureAmenity: TemperatureAmenity | None = None


class ThirdPartySource(Enum):
    UNKNOWN_SOURCE = 'UNKNOWN_SOURCE'
    SABRE = 'SABRE'
    TRAVEL_FUSION = 'TRAVEL_FUSION'
    AVIA = 'AVIA'
    NDC = 'NDC'
    TRAINLINE = 'TRAINLINE'
    ATPCO_NDC = 'ATPCO_NDC'
    FARELOGIX_NDC = 'FARELOGIX_NDC'
    OFFLINE = 'OFFLINE'
    CONNEXUS = 'CONNEXUS'
    ROUTEHAPPY = 'ROUTEHAPPY'
    AMADEUS = 'AMADEUS'
    GIATA = 'GIATA'
    QBR = 'QBR'
    BCD = 'BCD'
    QANTAS_HOTELS = 'QANTAS_HOTELS'
    SOUTHWEST = 'SOUTHWEST'
    EXPEDIA = 'EXPEDIA'
    HOTEL_HUB = 'HOTEL_HUB'
    MARRIOTT = 'MARRIOTT'
    CLEARTRIP = 'CLEARTRIP'
    KYTE = 'KYTE'
    GROUNDSPAN = 'GROUNDSPAN'
    SABRE_NDC = 'SABRE_NDC'
    BOOKING_COM = 'BOOKING_COM'


class ThreeDSecure2VerificationInfoItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cardId: str = Field(
        ...,
        description='Card id for which the verification was performed.',
        examples=['550e8400-e29b-41d4-a716-446655440000'],
    )
    sessionId: str = Field(
        ...,
        description='session id of the payment service provider.',
        examples=['sid_7q3lpqopuwmuxct3l3rkz225ae'],
    )
    redirectUrl: str = Field(
        ...,
        description='3DS2 verification redirect url.',
        examples=[
            'https://api.checkout.com/sessions-interceptor/sid_7q3lpqopuwmuxct3l3rkz225ae'
        ],
    )


class ThreeDSecure2VerificationResultItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cardId: str = Field(
        ...,
        description='Card id for which the verification was performed.',
        examples=['550e8400-e29b-41d4-a716-446655440000'],
    )
    sessionId: str = Field(
        ...,
        description='session id of the payment service provider.',
        examples=['sid_7q3lpqopuwmuxct3l3rkz225ae'],
    )
    success: bool = Field(
        ...,
        description='Whether the 3DSecure2 authentication was successful.',
        examples=[True],
    )


class TicketType(Enum):
    SINGLE = 'SINGLE'
    MULTI = 'MULTI'


class Tier(Enum):
    BASIC = 'BASIC'
    SEAT1A = 'SEAT1A'


class TimeLocal(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(pattern=r'^([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$') = (
        Field(..., examples=['17:32'])
    )


class TimeRange(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    min: TimeLocal | None = Field(None, description='Minimum value - inclusive.')
    max: TimeLocal | None = Field(None, description='Maximum value - inclusive.')


class TokenizedExpiry(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiryMonth: str = Field(
        ..., description='Tokenized Expiry month', examples=['KvAuPANQWCpjwRQxcC8EXg==']
    )
    expiryYear: str = Field(
        ..., description='Tokenized Expiry year', examples=['fPBm0OWrKwPyIrCVcbg4cA==']
    )


class TokenizedExpiryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tokenizedExpiry: TokenizedExpiry | None = None


class TransmissionSearchFilter(Enum):
    MANUAL = 'MANUAL'
    AUTOMATIC = 'AUTOMATIC'


class TravelClassHierarchy(Enum):
    UNKNOWN = 'UNKNOWN'
    STANDARD = 'STANDARD'
    COACH = 'COACH'
    STANDARD_PREMIUM = 'STANDARD_PREMIUM'
    BUSINESS = 'BUSINESS'
    BUSINESS_PREMIUM = 'BUSINESS_PREMIUM'
    FIRST = 'FIRST'
    SLEEPER = 'SLEEPER'


class TravelClassHierarchyWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    railTravelClass: TravelClassHierarchy | None = None


class TravelRegionType(Enum):
    DOMESTIC = 'DOMESTIC'
    INTERNATIONAL = 'INTERNATIONAL'


class TravelType(Enum):
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    CAR = 'CAR'
    RAIL = 'RAIL'
    LIMO = 'LIMO'
    MISC = 'MISC'
    ALL = 'ALL'


class Applicability1(Enum):
    PER_TRAVELER = 'PER_TRAVELER'
    ALL_TRAVELERS = 'ALL_TRAVELERS'


class TravelerApplicability(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    applicability: Applicability1 = Field(..., examples=['PER_TRAVELER'])


class SeatPreference(Enum):
    AISLE = 'AISLE'
    WINDOW = 'WINDOW'


class UpdateType(Enum):
    LOYALTY_INFO = 'LOYALTY_INFO'
    REDRESS_NUMBER = 'REDRESS_NUMBER'
    KNOWN_TRAVELER_NUMBER = 'KNOWN_TRAVELER_NUMBER'
    SEAT = 'SEAT'
    SPECIAL_SERVICE_REQUEST = 'SPECIAL_SERVICE_REQUEST'
    OTHER_ANCILLARY = 'OTHER_ANCILLARY'
    UNDO_CHECK_IN = 'UNDO_CHECK_IN'
    CHECK_IN = 'CHECK_IN'
    SEAT_PREFERENCE = 'SEAT_PREFERENCE'
    PASSPORT = 'PASSPORT'
    NATIONAL_DOC = 'NATIONAL_DOC'


class TripId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., description='Id.', examples=['2783425534'])


class TripUsageType(Enum):
    STANDARD = 'STANDARD'
    EVENT = 'EVENT'


class UnusablePaymentSource(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID = Field(
        ...,
        description='Unique identifier identifying this payment source',
        examples=['f49d00fe-1eda-4304-ba79-a980f565281d'],
    )


class UnusedCreditFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hideWithoutCredits: bool | None = Field(
        None,
        description='If true, only itineraries with applicable unused credits are returned.',
    )


class TicketType2(Enum):
    TICKET_TYPE_UNKNOWN = 'TICKET_TYPE_UNKNOWN'
    ETICKET = 'ETICKET'
    MCO = 'MCO'
    NON_GDS = 'NON_GDS'


class RedeemVia(Enum):
    REDEEM_VIA_OBT = 'REDEEM_VIA_OBT'
    CONTACT_AGENT = 'CONTACT_AGENT'


class SourceOfTruth(Enum):
    SPOTNANA = 'SPOTNANA'
    MANUAL_FORM = 'MANUAL_FORM'


class UrlVerificationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ccVerificationUrl: str | None = Field(
        None, description='Verification url for the payment source.'
    )


class UrlVerificationInfoWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    urlVerificationInfo: UrlVerificationInfo | None = None


class UserId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID


class UserIdWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId | None = None


class UserTitle(Enum):
    TITLE_UNKNOWN = 'TITLE_UNKNOWN'
    MR = 'MR'
    MS = 'MS'
    MRS = 'MRS'
    MX = 'MX'
    MASTER = 'MASTER'
    MISS = 'MISS'
    DR = 'DR'
    PROFESSOR = 'PROFESSOR'
    CAPTAIN = 'CAPTAIN'
    REVEREND = 'REVEREND'
    HONOURABLE = 'HONOURABLE'
    SIR = 'SIR'
    LADY = 'LADY'
    AMBASSADOR = 'AMBASSADOR'
    LORD = 'LORD'
    BRIGADIER = 'BRIGADIER'
    SENATOR = 'SENATOR'
    DAME = 'DAME'
    JUSTICE = 'JUSTICE'
    UK = 'UK'


class VaccineAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['Vaccination facility available'])
    vaccineDescription: str | None = Field(None, description='chek')
    vaccineAttrDescription: str | None = None


class VaccineAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vaccineAmenity: VaccineAmenity | None = None


class VariableName(Enum):
    PUBLISHED_FARE = 'PUBLISHED_FARE'
    LLF = 'LLF'


class VendorProgramType(Enum):
    UA_PASS_PLUS = 'UA_PASS_PLUS'
    GENERIC_PROGRAM_TYPE = 'GENERIC_PROGRAM_TYPE'


class WaiveOffFeeReason(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    reasonCode: str | None = Field(
        None, description='Fee waive off reason code.', examples=['WA05']
    )
    reason: str | None = Field(
        None,
        description='Fee waive off reason description.',
        examples=['Customer goodwill'],
    )


class WaiveOffInfoPerModule(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    moduleId: ModuleId | None = None
    waiveOffFeeReason: WaiveOffFeeReason | None = None


class Unit2(Enum):
    kg = 'kg'
    lb = 'lb'


class WeightLimit(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    weight: float | None = Field(
        None, description='The weight limit of a baggage option.', examples=[32]
    )
    unit: Unit2 | None = Field(
        None,
        description='The unit of measurement for the weight limit.',
        examples=['kg'],
    )


class WifiAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayText: str | None = Field(None, examples=['Basic web browsing (fee)'])
    cost: str | None = Field(None, examples=['paid'])


class WifiAmenityWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    wifiAmenity: WifiAmenity | None = None


class WorkerType(Enum):
    EMPLOYEE = 'EMPLOYEE'
    CONTINGENT = 'CONTINGENT'
    SEASONAL = 'SEASONAL'
    INTERN = 'INTERN'
    GUEST = 'GUEST'


class WorkerTypeListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    workerTypeList: Sequence[WorkerType] | None = None


class WorkerTypeWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    workerType: WorkerType | None = None


class WorkflowIds(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    checkoutResponseId: str | None = Field(
        None, description='ID sent by the backend in the air checkout response.'
    )
    seatMapResponseId: str | None = Field(
        None, description='ID sent by the backend in the air seat map response.'
    )
    paymentSetupResponseId: str | None = Field(
        None, description='ID set by the backend in the payment setup API response.'
    )
    initiateBookingId: str | None = Field(
        None, description='ID set by the backend in the initiate booking API response.'
    )


class AdhocTravelerInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    registrarUserId: UserId | None = Field(
        None, description='The registrar of the adhoc traveler.'
    )
    externalId: str | None = Field(
        None, description='External Id of user', examples=['qwert123']
    )


class AdhocTravelerInfoWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    adhocTravelerInfo: AdhocTravelerInfo | None = None


class AirAmenity(
    RootModel[
        SeatAmenityWrapper
        | WifiAmenityWrapper
        | PowerAmenityWrapper
        | EntertainmentAmenityWrapper
        | BeverageAmenityWrapper
        | AircraftAmenityWrapper
        | LayoutAmenityWrapper
        | FreshFoodAmenityWrapper
        | CleaningAmenityWrapper
        | MaskAmenityWrapper
        | TemperatureAmenityWrapper
        | PassengerCapacityAmenityWrapper
        | BlockedAdjacentSeatsAmenityWrapper
        | CovidTestingAmenityWrapper
        | VaccineAmenityWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        SeatAmenityWrapper
        | WifiAmenityWrapper
        | PowerAmenityWrapper
        | EntertainmentAmenityWrapper
        | BeverageAmenityWrapper
        | AircraftAmenityWrapper
        | LayoutAmenityWrapper
        | FreshFoodAmenityWrapper
        | CleaningAmenityWrapper
        | MaskAmenityWrapper
        | TemperatureAmenityWrapper
        | PassengerCapacityAmenityWrapper
        | BlockedAdjacentSeatsAmenityWrapper
        | CovidTestingAmenityWrapper
        | VaccineAmenityWrapper
    ) = Field(..., title='AirAmenity')


class AirApplicableLoyaltyResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    loyaltyPrograms: Sequence[AirLoyaltyProgram]
    applicableLoyalties: Sequence[LoyaltyInfo] | None = None


class AirCancelPnrResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    sourcePnrId: str | None = Field(
        None, description='Source pnr id of the cancellation.', examples=['ABCDEF']
    )
    status: CancellationStatus | None = None


class CreatePnrApplicationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    applicationWarning: Sequence[ApplicationWarnings] | None = None


class AirCreatePnrResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str | None = Field(None, description='Spotnana PNR ID.')
    sourcePnrId: str | None = Field(None, description='Source PNR ID')
    pnrStatus: PnrStatus | None = Field(
        None, description='PNR status (for example success, approval pending)'
    )
    createPnrApplicationInfo: CreatePnrApplicationInfo | None = Field(
        None,
        description='Create PNR application info, will return warning or error if present',
    )


class AirInitiateBookingResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    initiateBookingResponseId: str | None = Field(
        None,
        description="Initiated booking's reference id",
        examples=['CuwDQ2hCa09HVTJZemsyWVRFNFlUUm'],
    )
    paymentSourcePrePaymentInformation: (
        Sequence[PaymentSourcePrePaymentInformation] | None
    ) = None


class AirItineraryIdWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airItineraryId: AirItineraryIdentifier | None = None


class AirRequestTravelerInfo(RootModel[UserIdWrapper | AdhocTravelerInfoWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: UserIdWrapper | AdhocTravelerInfoWrapper = Field(
        ...,
        description='The traveler identifiers. These can be either the Spotnana user IDs of the travelers or information regarding\nthe adhoc travelers.\n',
        title='AirRequestTravelerInfo',
    )


class Airport(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airportCode: str = Field(
        ..., description='Unique code for the Airport', examples=['LHR']
    )
    airportName: str = Field(
        ..., description='Full Name of the Airport', examples=['Heathrow Airport']
    )
    cityCode: str = Field(
        ..., description='City Code for the location', examples=['LON']
    )
    location: Location | None = None


class AirportLocation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    city: str | None = Field(
        None,
        description='The code of a city used to identify a set of airports associated with it. For example, NYC or LON.',
        examples=['NYC'],
    )
    airport: constr(pattern=r'^[A-Z]{3}$') | None = Field(
        None,
        description='The airport code identifying a specific airport. For example, JFK or EWR.',
        examples=['JFK'],
    )
    multiAirports: MultiAirports | None = Field(
        None, description='List of airports to search for this location.'
    )


class AmadeusCheckoutVerificationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentActionTokens: Sequence[AmadeusPaymentActionToken]
    paymentRequestId: str | None = Field(
        None,
        description='Initial payment id generated by checkout SDK to initiate payment',
    )
    actionToken: str | None = Field(
        None,
        description='Token returned by checkout SDK to create/validate FOP in PSS order',
    )
    confirmPayment: bool | None = Field(
        None,
        description='Flag which indicates that the payment confirmation is now required (after 3ds or redirection)',
    )


class AmadeusCheckoutVerificationInfoWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amadeusCheckoutVerificationInfo: AmadeusCheckoutVerificationInfo | None = None


class BagWeightLimit(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    weightLimit: Sequence[WeightLimit] | None = Field(
        None, description='Weight limit in different units'
    )
    applicability: BagPolicyApplicability | None = None


class BookingContact(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    emailAddress: str | None = Field(
        None,
        description='Email address of the booking contact',
        examples=['<EMAIL>'],
    )
    phoneNumber: PhoneNumber | None = None


class CabinFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: CabinFilterType | None = 'DEFAULT'
    cabin: Cabin | None = None


class CarPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendors: Sequence[CarVendor] | None = Field(
        None, description='A list of car vendors.'
    )
    carTypes: Sequence[CarType] | None = Field(
        None, description='A list of types of car.'
    )
    engineTypes: Sequence[EngineType] | None = Field(
        None, description='A list of types of engine.'
    )
    transmissionTypes: Sequence[TransmissionSearchFilter] | None = Field(
        None, description='A list of types of transmission.'
    )
    conditionalRates: Sequence[ConditionalRate] | None = Field(
        None, description='A list of conditional rates for rail.'
    )


class CardAccessEntity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    entityId: str = Field(
        ...,
        description='Holds the id for for the user who can access the card or organization id or legal entity',
    )
    centralCardAccessLevel: CentralCardAccessLevel | None = None


class CardExpiry(RootModel[TokenizedExpiryWrapper | ExpiryWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: TokenizedExpiryWrapper | ExpiryWrapper = Field(
        ..., description='Contains the expiry of a Card.', title='CardExpiry'
    )


class Column(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    columnNumber: str | None = Field(None, examples=['32'])
    position: ColumnPosition | None = None


class ColumnSection(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    columns: Sequence[Column] | None = None


class CompanyConfigSource(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    optionsParam: CustomFieldOptionsParam


class CompanyConfigSourceWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyConfig: CompanyConfigSource | None = None


class ExpiryDateRange(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiryDateStart: DateModel | None = Field(None, examples=['2023-01-31'])
    expiryDateEnd: DateModel | None = Field(None, examples=['2023-12-01'])


class CompanyCreditFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    supplier: SupplierType | None = Field(
        None, description='Supplier for which this service provider should be used.'
    )
    selectedAirlines: Sequence[str] | None = None
    expiryDateRange: ExpiryDateRange | None = None


class CompanyRef(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CompanyId
    name: str | None = None
    logo: Image | None = Field(None, description='Company logo')


class CompanyTransferableUnusedCreditsListRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyId: CompanyId1
    companyCreditFilter: CompanyCreditFilter | None = Field(
        None, description='Filter for fetching company credits.'
    )
    offset: conint(ge=0) | None = Field(0, examples=[0])


class CompanyUnusedCreditsListRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyId: CompanyId2 | None = None
    companyCreditFilter: CompanyCreditFilter | None = Field(
        None, description='Filter for fetching company credits.'
    )
    offset: conint(ge=0) | None = Field(None, examples=[0])


class ConnectingAirportsFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legFilters: Sequence[ConnectingAirportsLegFilter] | None = None


class CostCenter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CostCenterId
    name: str = Field(..., examples=['CostCenter'])
    externalId: str | None = Field(None, examples=['external-id'])


class CovidFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vaccine: CovidFilterPreference | None = 'NO_PREFERENCE'
    covidTest: CovidFilterPreference | None = 'NO_PREFERENCE'
    faceMask: CovidFilterPreference | None = 'NO_PREFERENCE'
    temperatureCheck: CovidFilterPreference | None = 'NO_PREFERENCE'
    blockedAdjacentSeats: CovidFilterPreference | None = 'NO_PREFERENCE'


class CreditCardAccess(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    accessType: CreditCardAccessType
    entityIds: Sequence[str] = Field(
        ...,
        description='Holds the ids for for all users who can access the card or organization id',
    )
    entities: Sequence[CardAccessEntity] | None = Field(
        None,
        description='A list of cardAccessEntity consisting of central card access level if present and entity id.',
    )


class Department(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: DepartmentId
    name: str = Field(..., examples=['IT Department'])
    externalId: str | None = Field(
        None,
        description='External id of the department',
        examples=['department-ext-id'],
    )
    employeeCount: int | None = Field(
        None, description='Count of employees in the department', examples=[57]
    )


class EmergencyContact(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Full name of contact.', examples=['John Smith'])
    email: EmailStr | None = Field(
        None,
        description='Email address of contact.',
        examples=['<EMAIL>'],
    )
    designation: str | None = Field(
        None, description='Job title of contact.', examples=['MANAGER']
    )
    relation: Relation | None = Field(
        None, description='Relation of contact to user.', examples=['SPOUSE']
    )
    phoneNumbers: Sequence[PhoneNumber] = Field(
        ..., description='Phone numbers of contact.'
    )
    preferredLanguage: str | None = Field(
        None, description='Language preferred by user.', examples=['en-US']
    )


class Expression(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['EXPRESSION'] = Field('EXPRESSION', examples=['EXPRESSION'])
    formatExpression: str = Field(
        ...,
        description='The expression must be of format : `${expression}`.The expression can consist of a \ncombination of variables and mathematical operations.\n Variable names must begin with `var` followed by a number, which is used to identify \nthe variable in the variables list. The numbering should follow a 1-based index.\n  To define mathematical operations, the operation name should follow the format\n`math.<math_op>(arg1, arg2)`. Both `arg1` and `arg2` can be variables or constants. \nThe supported math operations (math_op) include: `add, mul, div, sub, min,\nand max`. All keywords, such as `<math_op>, math, and var` must be written in lowercase.\n',
        examples=['Result:  ${math.mul(var1,5)}  ${var2}'],
    )
    variables: Sequence[VariableName] | None = Field(
        None, description='Reference names of the variables present in the expression.'
    )


class Facility(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    location: SeatLocation | None = None
    facilityType: FacilityType | None = None


class FacilitySection(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    location: SeatLocation | None = None
    facilities: Sequence[Facility] | None = None


class FareBasisRule(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fareBasisCode: str | None = None
    rules: Rules | None = None


class FareCategory(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ngsCategory: NGSFareCategory | None = 'UNKNOWN_NGS_CATEGORY'
    cabinViewCategory: CabinViewFareCategory | None = 'UNKNOWN_CABIN_CATEGORY'


class FareCategoryFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fareCategories: Sequence[FareCategory] | None = Field(
        None, title='Fare categories to return in the response'
    )


class FetchTravelerUnusedCreditsRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerInfo: AirRequestTravelerInfo = Field(
        ..., description='Traveler information to get userOrgId.'
    )


class FlightAttribute(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str | None = Field(
        None,
        description='Marketing description and images associated with the flight attribute.',
        examples=['A fully flat bed'],
    )
    categories: Sequence[FlightAttributeCategory] | None = Field(
        None, description='List of categories in which this flight attribute falls into'
    )
    photos: Sequence[ImageGroup] | None = Field(
        None, description='List of images associated with the flight attribute.'
    )


class FlightCommon(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightRef: str | None = Field(
        None,
        description='Unique ID for this flight in the response.',
        examples=['flight_3'],
    )
    departureDateTime: DateTimeLocal = Field(
        ..., description='Departure date and time of the flight.'
    )
    arrivalDateTime: DateTimeLocal = Field(
        ..., description='Arrival date and time of the flight.'
    )
    origin: AirportInfo = Field(
        ..., description='The airport from which the flight departs (origin).'
    )
    destination: AirportInfo = Field(
        ..., description='The airport at which the flight arrives (destination).'
    )
    departureGate: GateInfo | None = Field(
        None, description='Departure gate and terminal.'
    )
    arrivalGate: GateInfo | None = Field(None, description='Arrival gate and terminal.')
    marketing: FlightNumber = Field(..., description='Marketing flight number.')
    operating: FlightNumber = Field(
        ...,
        description='Operating flight number (for the airline which actually operates the flight).',
    )
    operationalDisclosure: str | None = Field(
        None,
        description='Operating airline name that has to be displayed to the user',
        examples=['SKYWEST DBA UNITED EXPRESS'],
    )
    hiddenStops: Sequence[HiddenStop] | None = Field(
        None, description='Any stops for refueling or boarding additional passengers.'
    )
    duration: Duration | None = Field(
        None, description='The duration of the flight (travel time).'
    )
    equipment: Equipment | None = Field(
        None, description='The equipment or aircraft type used for the flight'
    )


class FlightCommonWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flightData: FlightCommon | None = None


class FlightData(RootModel[FlightRefWrapper | FlightCommonWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: FlightRefWrapper | FlightCommonWrapper = Field(..., title='FlightData')


class FlightDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(
        ...,
        description='The unique ID for this flight in the itinerary.',
        examples=['flight_0'],
    )
    flightData: FlightData | None = None
    cabin: Cabin = Field(
        ..., description='The cabin type for this flight in the associated itinerary'
    )
    bookingCode: str | None = Field(
        None,
        description="The airline's one-letter code for the associated fare",
        examples=['B'],
    )
    seatAvailability: int | None = Field(
        None, description='The availability of seats on this flight', examples=[9]
    )
    corpAccountCode: str | None = Field(
        None,
        description='The account code is used to get corporate negotiated price',
        examples=['DFG'],
    )
    carbonEmission: CO2EmissionDetail | None = Field(
        None, description='The CO2 emission details for this flight'
    )
    amenities: Sequence[AirAmenity] | None = Field(
        None, description='The amenities associated with the flight.'
    )
    flightAttributes: Sequence[FlightAttribute] | None = Field(
        None, description='The set of product attributes associated with the flight.'
    )


class Grade(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    employeeCount: int | None = Field(
        None, description='Count of employees in the grade', examples=[75]
    )
    id: GradeId
    name: str = Field(..., examples=['Grade'])


class HotelPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelParentChains: Sequence[HotelChain] | None = Field(
        None, description='A list of hotel parent chains.'
    )
    hotelBrands: Sequence[HotelBrand] | None = Field(
        None, description='A list of hotel brands.'
    )
    hotelAmenityTypes: Sequence[HotelPrefAmenity] | None = Field(
        None, description='A list of HotelAmenities.'
    )
    roomPreference: RoomPreference | None = None
    conditionalRates: Sequence[ConditionalRate] | None = Field(
        None, description='A list of conditional rates for rail.'
    )


class IdentityDocument(
    RootModel[
        PassportWrapper
        | ImmigrationDocumentWrapper
        | RedressNumberWrapper
        | KnownTravelerNumberWrapper
        | NationalDocWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        PassportWrapper
        | ImmigrationDocumentWrapper
        | RedressNumberWrapper
        | KnownTravelerNumberWrapper
        | NationalDocWrapper
    ) = Field(
        ...,
        description='Identity document details. Currently supported documents are passport, immigration document, \nknown traveler number, redress number and national document.\n',
        title='IdentityDocument',
    )


class KeywordsWithReasonList(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    keywords: Sequence[RestrictedKeywordsWithReason] | None = None


class Leg(RootModel[NewLeg | ExistingLeg]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: NewLeg | ExistingLeg


class LegDepartureArrivalTimeRangeFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legIndex: int | None = None
    departure: TimeRange | None = None
    arrival: TimeRange | None = None


class LegRuleInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    origin: str | None = None
    destination: str | None = None
    travelDate: DateTimeLocal | None = None
    flights: Sequence[FlightInLegRule] | None = None
    fareBasisRules: Sequence[FareBasisRule] | None = None


class MandatoryParamForCheckout(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerApplicability: TravelerApplicability
    parameter: Parameter | None = Field(
        None,
        description='The mandatory parameter to be collected.',
        examples=['DATE_OF_BIRTH'],
    )


class MealPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    exclMealPrefs: Sequence[MealType] | None = None
    inclMealPrefs: Sequence[MealType] | None = None
    specialMealDescription: str | None = Field(None, examples=['Veg only meal'])


class MigrationResponseInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentSourceId: str | None = Field(
        None,
        description='Unique identifier of the payment source.',
        examples=['d059044a-cacd-4b53-883d-7355530f54e7'],
    )
    status: MigrationStatus | None = None


class OtherCoinageItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    coinageCode: PaymentMethod | None = Field(None, description='Payment method')
    amount: float | None = Field(None, examples=[1000])
    conversionRate: float | None = Field(
        None,
        description='1 coin in this system equals to how many currency value',
        examples=[0.01],
    )
    preferredCurrencyConversionRate: float | None = Field(
        None,
        description='1 coin in this system equals to how many currency value',
        examples=[0.01],
    )


class Money(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: float = Field(
        ..., description='The numeric value for the amount of money.', examples=[510]
    )
    currencyCode: str = Field(
        ...,
        description='The 3-letter currency code for the money amount (defined using ISO 4217 standard).',
        examples=['GBP'],
    )
    convertedAmount: float | None = Field(
        None,
        description="The converted currency and amount that has been converted (if a currency conversion has been requested).\nFor example, if the call requests that money be sent in a specified currency (because the frontend requested\nthe backend to send money in the user's preferred currency).\n",
        examples=[715.42],
    )
    convertedCurrency: str | None = Field(
        None,
        description='The 3-letter currency code for the converted currency (defined using ISO 4217 standard).',
        examples=['USD'],
    )
    otherCoinage: Sequence[OtherCoinageItem] | None = Field(
        None,
        description='List of the dollar amount in other coinage systems like reward points, cryptocurrency etc.',
        title='OtherCoinage',
    )


class MoneyRange(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    min: Money | None = Field(None, description='Minimum value - inclusive.')
    max: Money | None = Field(None, description='Maximum value - inclusive.')


class MoneyWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    money: Money | None = None


class Name(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    family1: str = Field(..., description='Last (family) name.', examples=['Gandas'])
    family2: str | None = Field(None, examples=['FamilyTwo'])
    given: str = Field(..., description='First (given) name.', examples=['Vichitr'])
    middle: str | None = Field(None, description='Middle name.', examples=['Kumar'])
    suffix: NameSuffix | None = Field(
        None,
        description='Suffix used with the name. For example SR or JR.',
        examples=['SR'],
    )
    preferred: str | None = Field(
        None,
        description='Informal preferred name added by traveler. This is not used on any PNR or tickets',
        examples=['Don'],
    )


class Office(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress | None = None
    id: OfficeId
    name: str = Field(..., examples=['Office'])
    latlng: Latlng | None = None
    taxId: str | None = Field(None, examples=['133232'])


class OptionSourceMetadata(RootModel[CompanyConfigSourceWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: CompanyConfigSourceWrapper = Field(
        ...,
        description='Metadata information for the option source.',
        title='OptionSourceMetadata',
    )


class POSInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    source: ThirdPartySource = Field(
        ..., description='The third party source for the point of sale'
    )
    posDescriptor: str | None = Field(
        None,
        description='The identifier for the point of sale. This would be the PCC for GDS bookings.',
        examples=['6CA4'],
    )


class PaymentVerificationInfo1(StripeVerificationInfoWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    gatewayIdentifier: GatewayIdentifier | None = None
    paymentKey: str | None = Field(
        None,
        description='Serialized key to identify the payment executed on payment gateway that will be used for post payment verification.',
    )


class PaymentVerificationInfo2(UrlVerificationInfoWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    gatewayIdentifier: GatewayIdentifier | None = None
    paymentKey: str | None = Field(
        None,
        description='Serialized key to identify the payment executed on payment gateway that will be used for post payment verification.',
    )


class PaymentVerificationInfo4(RazorpayVerificationInfoWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    gatewayIdentifier: GatewayIdentifier | None = None
    paymentKey: str | None = Field(
        None,
        description='Serialized key to identify the payment executed on payment gateway that will be used for post payment verification.',
    )


class PostPaymentVerificationInfo1(PostStripeVerificationInfoWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentKey: str | None = Field(
        None,
        description='Serialized key to identify the payment executed on payment gateway that will be used for post payment verification.',
    )


class PostPaymentVerificationInfo2(PostUrlBasedVerificationInfoWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentKey: str | None = Field(
        None,
        description='Serialized key to identify the payment executed on payment gateway that will be used for post payment verification.',
    )


class PostPaymentVerificationInfo3(AmadeusCheckoutVerificationInfoWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentKey: str | None = Field(
        None,
        description='Serialized key to identify the payment executed on payment gateway that will be used for post payment verification.',
    )


class PostPaymentVerificationInfo5(RazorpayPostVerificationInfoWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentKey: str | None = Field(
        None,
        description='Serialized key to identify the payment executed on payment gateway that will be used for post payment verification.',
    )


class PreferredAirport(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airportName: str | None = Field(
        None,
        description='Airport name.',
        examples=['San Francisco International Airport'],
    )
    airportCode: str = Field(..., description='IATA airport code.', examples=['SFO'])
    label: PreferredLocationLabel


class Price(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalAmount: Money | None = Field(None, description='Total amount for the seat.')
    tax: Money | None = Field(None, description='Tax for the seat.')
    base: Money | None = Field(None, description='Base price for the seat.')
    merchantFee: Money | None = Field(
        None,
        description='Merchant fee for the seat. This fee is applicable if purchased via OBT.',
    )


class ProfileOwner(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId


class SearchLeg(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    origin: AirportLocation = Field(
        ..., description='3 letter IATA airport or metropolitan code for the origin'
    )
    destination: AirportLocation = Field(
        ...,
        description='3 letter IATA airport or metropolitan code for the destination',
    )
    date: DateModel = Field(..., description='The date of travel for this leg')


class SeatMapItinerary(RootModel[AirItineraryIdWrapper | PnrItineraryWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: AirItineraryIdWrapper | PnrItineraryWrapper = Field(
        ..., description='The itinerary for which the seat map needs to be returned.'
    )


class SeatPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hasAccessibility: bool | None = Field(
        False,
        description='Whether or not requires assistance for disability.',
        examples=[False],
    )
    seatTypes: Sequence[SeatPrefType] | None = None
    seatLocations: Sequence[SeatPrefLocation] | None = None
    deckLevels: Sequence[DeckLevel] | None = None
    seatDirections: Sequence[SeatPrefDirection] | None = None


class SeatPrefFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    seatTypes: Sequence[SeatType] | None = None
    minPitchInch: int | None = None


class SeatSection(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    columnNumbers: Sequence[str] | None = Field(
        None, description='The seat columns present in the seat section.'
    )
    type: Type8 | None = None
    limitations: Sequence[Limitation] | None = None
    facilities: Sequence[Facility1] | None = None
    location: Sequence[LocationEnum] | None = None
    commercialName: str | None = Field(
        None, description='Commercial name for this seat.', examples=['Economy plus']
    )
    price: Price | None = Field(None, description='Price for this seat.')


class SecondaryServiceProviderTmc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tmcId: CompanyId = Field(..., description='Id of the service provider TMC.')
    supplier: SupplierType = Field(
        ..., description='Supplier for which this service provider should be used.'
    )
    travelType: TravelType = Field(
        ..., description='Travel type for which this service provider should be used.'
    )


class SortOption(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    sortBy: SortBy | None = 'PRICE'
    sortOrder: SortOrder | None = 'ASCENDING'
    shelfNumber: int | None = Field(
        None,
        description="Only used when 'SortBy' is 'PRICE' and represents which shelf should be used for sorting. If shelf_number is 0 (default), minimum of all shelf prices is used.",
    )


class SplitAmount(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Sequence[Money] | None = None


class SplitAmountWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    splitAmount: SplitAmount | None = None


class Tax(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Money = Field(..., description='Tax amount')
    taxCode: str | None = Field(None, description='Tax code', examples=['VAT'])
    percentage: float | None = Field(
        None, description='Tax amount to total amount', examples=[9]
    )


class TaxBreakdown(RootModel[Sequence[Tax]]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Sequence[Tax]


class ThreeDSecure2PostVerificationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    results: Sequence[ThreeDSecure2VerificationResultItem] = Field(
        ..., description='List of 3DS2 verification results.'
    )


class ThreeDSecure2PostVerificationInfoWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    threeDSecure2PostVerificationInfo: ThreeDSecure2PostVerificationInfo | None = None


class ThreeDSecure2VerificationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    verificationInfoList: Sequence[ThreeDSecure2VerificationInfoItem] = Field(
        ..., description='List of 3DS2 verification info.'
    )


class ThreeDSecure2VerificationInfoWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    threeDSecure2VerificationInfo: ThreeDSecure2VerificationInfo | None = None


class TimeRangeFilter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    timeRanges: Sequence[LegDepartureArrivalTimeRangeFilter] | None = None


class TmcBasicInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    contractingTmc: CompanyRef = Field(
        ..., description='Contracting TMC is the TMC the user/organization contracted.'
    )
    bookingTmc: CompanyRef = Field(
        ...,
        description='Booking TMC is the TMC used for the bookings for the user/organization.',
    )


class TmcInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CompanyId = Field(..., description='TMC id.')
    primaryServiceProviderTmc: PrimaryServiceProviderTmc = Field(
        ..., description='Primary service provider TMC for the TMC.'
    )
    secondaryServiceProviderTmcs: Sequence[SecondaryServiceProviderTmc] | None = Field(
        None, description='Secondary service provider TMCs for the TMC.'
    )
    partnerTmcId: CompanyId | None = Field(
        None, description='Useful to identify the clients onboarded by a PARTNER_TMC'
    )


class TotalWeightBreakdownWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalWeightBreakdown: Sequence[WeightLimit] | None = Field(
        None, description='The total weight breakdown of a baggage option.'
    )


class TotalWeightWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalWeight: WeightLimit | None = Field(
        None, description='The total weight of a baggage option.'
    )


class TravelerInfoResponse(RootModel[UserIdWrapper | AdhocTravelerInfoWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: UserIdWrapper | AdhocTravelerInfoWrapper = Field(
        ...,
        description='The traveler identifiers. These can be either the Spotnana user IDs of the travelers or information regarding\nthe adhoc travelers.\n',
        title='TravelerInfoResponse',
    )


class TravelerMatchConditions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    workerTypes: Sequence[WorkerType] | None = Field(
        None, description='Worker types. Users belonging to any of these would match.'
    )
    countries: Sequence[str] | None = Field(None, description='Countries.')
    legalEntities: Sequence[Reference] | None = Field(
        None, description='Legal entities'
    )
    departments: Sequence[Reference] | None = Field(None, description='Departments')
    costCenters: Sequence[Reference] | None = Field(None, description='Cost centers')
    offices: Sequence[Reference] | None = Field(None, description='Offices')


class TravelerSearchInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerType: PassengerType
    travelerAge: PassengerAge | None = Field(
        None, description='Age of the traveler. Only required if traveler type is Child'
    )
    travelerInfo: AirRequestTravelerInfo | None = Field(
        None, description='The information about the traveler of given travelerType.'
    )


class TravelerSeatInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerType: PassengerType | None = None
    travelerAge: PassengerAge | None = Field(
        None,
        description='Age of the traveler. Only required if traveler type is Child.',
    )
    travelerInfo: AirRequestTravelerInfo | None = Field(
        None, description='The information around the given traveler.'
    )
    loyaltyInfos: Sequence[SeatLoyaltyInfo] | None = Field(
        None,
        description="Information about the traveler's loyalty program memberships.",
    )


class TravelerSeatMap(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerId: UserId | None = Field(
        None, description='Unique identifier for the traveler in this response.'
    )
    travelerInfo: TravelerInfoResponse | None = Field(
        None, description='Unique identifier for the traveler in this response.'
    )
    flightSeatMapIds: Sequence[str] | None = Field(
        None,
        description='Seat maps for the specified travelerId. Ids refer to the id in AirSeatMapResponse -> seatMaps.',
    )


class TripData(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    approverEmail: str | None = Field(
        None,
        description='Email address of the approver who should receives approval email for the current booking.',
    )
    approverName: str | None = Field(None, description='Name of the approver.')
    hardApprovalRequired: bool | None = Field(
        None,
        description='Whether the current booking requires hard approval or soft approval.\nThis flag should be used only if valid approver is present.\n',
    )
    outOfPolicy: bool | None = Field(
        None, description='If the given booking is out of policy.'
    )
    policyId: str | None = Field(
        None, description='Policy Id for which violation is done.'
    )
    policyVersion: int | None = Field(None, description='Version of policy.')
    tripId: TripId = Field(..., description='Id of the trip.')


class UnusedCreditInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    sourcePnr: str | None = Field(
        None,
        description='PNR number corresponding to third party through which booking was made.',
        examples=['MC5ONS'],
    )
    spotnanaPnr: str | None = Field(
        None, description='Spotnana pnr ID.', examples=['2345678']
    )
    ticketNumber: str | None = Field(
        None,
        description='Ticket number for the ticket that was converted into an unused credit.',
        examples=['5267779139217'],
    )
    airlineCode: str | None = Field(
        None,
        description='2 letter airline code of the airline associated with this unused credit.',
        examples=['AA'],
    )
    airlineInfo: AirlineInfo | None = Field(
        None, description='Airline info with airline name and code'
    )
    totalFare: Money | None = Field(
        None, description='Total airfare associated with the original ticket.'
    )
    issueDate: DateTimeOffset | None = Field(
        None, description='Issue date for the unused credit.'
    )
    expiryDate: DateTimeOffset | None = Field(
        None, description='Expiry date for the unused credit.'
    )
    usedDate: DateTimeOffset | None = Field(
        None, description='Date on which the unused credit was used.'
    )
    departureDate: DateTimeOffset | None = Field(
        None,
        description='Date for the departure of the first flight associated with the unused credit.',
    )
    segmentsAvailable: SegmentsAvailable | None = Field(
        None,
        description='Whether all segments are unused or some have already been used.',
    )
    passengerName: Name | None = Field(
        None, description='Name of the passenger associated with the credit.'
    )
    departureCountry: str | None = Field(
        None,
        description='3 letter country code of the departure country associated with the original ticket.',
        examples=['USA'],
    )
    arrivalCountry: str | None = Field(
        None,
        description='3 letter country code of the arrival country associated with the original ticket.',
        examples=['USA'],
    )
    ticketType: TicketType2 | None = Field(None, description='Type of credit.')
    pcc: str | None = Field(None, description='PCC the credit was issued on.')
    status: CreditStatus | None = None
    source: ThirdPartySource | None = Field(
        'SABRE', description='Source of unused credit e.g. Sabre, NDC etc.'
    )
    tripId: str | None = Field(
        None,
        description='Trip ID that contains the unused credit',
        examples=['1234567'],
    )
    redeemVia: RedeemVia | None = Field(
        None,
        description='Credit redemption method. \nIf the value contains `CONTACT_AGENT`, then the agent must book the ticket and redeem the credits on behalf of the traveler.\n',
        examples=['REDEEM_VIA_OBT'],
    )
    sourceOfTruth: SourceOfTruth | None = Field(
        None, description='The system that owns the credit.'
    )
    owningPcc: str | None = Field(None, description='PCC the PNR was created on.')
    paymentSourceId: UUID | None = Field(
        None,
        description='Payment source ID associated with the credit.',
        examples=['edd5b835-8001-430c-98f8-fedeccebe4cf'],
    )
    creditUsageType: CreditUsageType | None = Field(
        None,
        description='The type of credit usage. This can be either COMPANY or PERSONAL.',
    )
    email: str | None = Field(
        None, description='Email of the passenger owning the unused credit.'
    )


class UserOrgId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    organizationAgencyId: OrganizationAgencyId | None = None
    organizationId: OrganizationId
    userId: UserId
    tmcInfo: TmcInfo | None = None
    tmcBasicInfo: TmcBasicInfo | None = None


class UserOrgIdListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userOrgIdList: Sequence[UserOrgId] | None = None


class UserOrgIdWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userOrgId: UserOrgId | None = None


class Variable(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['VARIABLE'] = Field('VARIABLE', examples=['VARIABLE'])
    name: VariableName


class WaiveOffInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    waiveOffInfoPerModules: Sequence[WaiveOffInfoPerModule] | None = Field(
        None, description='Waive off info per module.'
    )


class AdditionalInfo(RootModel[Variable | Expression]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Variable | Expression = Field(
        ...,
        description='Additional data need to be sent along with the custom field response.',
        discriminator='type',
        title='AdditionalInfo',
    )


class AdhocUserInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    profileOwner: ProfileOwner
    isSaved: bool | None = Field(
        False,
        description='A boolean flag to show if ad-hoc traveler is visible in search. While updating the user \nif client tries to update this field, it will throw exception.\n',
    )


class AirAutocompleteResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airports: Sequence[Airport] = Field(
        ..., description='List of Airports matching the query'
    )


class AirMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlineInfo: Sequence[AirlineInfo] | None = Field(
        None, description='Metadata for all airlines available in the specific route.'
    )
    applicableAlliances: Sequence[Alliance] | None = Field(
        None, description='Alliances which are applicable based on the airlines info'
    )
    legBylegPricing: bool | None = Field(
        None,
        description='Flag indicating whether itineraries are priced on a leg-by-leg basis (instead of the overall itinerary\npricing, which is the default).\n',
    )
    enableFareCategories: Sequence[FareCategory] | None = Field(
        None,
        description='Fare categories that need to be used for the given air search',
    )


class AirPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlinePrefs: Sequence[AirlinePref] | None = None
    alliancePref: AlliancePref | None = None
    farePref: FarePref | None = None
    homeAirport: str | None = Field(None, examples=['NEW YORK'])
    mealPref: MealPref | None = None
    numStopPref: NumStopsPref | None = None
    seatAmenityPref: SeatAmenityPref | None = None
    seatLocationPrefs: Sequence[SeatLocationPref] | None = None
    preferredAirports: Sequence[PreferredAirport] | None = Field(
        None, description='A list of user preferred airports.'
    )


class AirSeatMapRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    itinerary: SeatMapItinerary
    travelerInfos: Sequence[TravelerSeatInfo] = Field(
        ..., description='Information about the travelers on the itinerary.'
    )
    indexes: Sequence[SeatIndexes] | None = Field(
        None,
        description='Indexes for the seat map request must be provided only when specific legs or flights need to be selected. If not provided, all legs and flights will be considered.',
    )


class AirlineCardFee(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type = Field(..., examples=['CREDIT'])
    company: CardCompany
    fees: Money | None = None


class AncillaryOption(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerId: UserId | None = Field(
        None, description='Traveler ID to which this ancillary applies.'
    )
    status: Status = Field(
        ...,
        description='Ancillary status (purchased, eligible for purchase, not applicable, etc.).',
        examples=['ELIGIBLE'],
    )
    fare: Money = Field(..., description='The fare for this ancillary.')
    flightIds: Sequence[str] = Field(
        ...,
        description='ID of the flights on which this ancillary applies.',
        min_length=1,
    )
    ancillaryId: str = Field(
        ...,
        description='Unique ID for purchasing this ancillary.',
        examples=['ancillary_0'],
    )


class AncillarySelectionInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: AncillaryType
    displayName: str = Field(
        ..., description='Display name for this ancillary type', examples=['Early Bird']
    )
    description: str = Field(
        ...,
        description='Description for this ancillary type',
        examples=[
            'Purchasing early bird provides the customer with a higher boarding priority compared to WannaGetAway/Anytime customers that don’t have a special rapid rewards status irrespective of the time they check in.'
        ],
    )
    ancillaryOptions: Sequence[AncillaryOption] = Field(
        ..., description='Ancillary options for this ancillary type', min_length=1
    )


class BagFees(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fee: Money | None = None
    applicability: BagPolicyApplicability | None = None


class BaggageInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str | None = Field(
        None, description='Baggage policy text', examples=['1 checked bag, 33 lbs']
    )
    count: int | None = Field(None, description='Count of bags', examples=[3])
    sizeLimitInfo: Sequence[SizeInfo] | None = Field(
        None, description='Size of bag in cm'
    )
    weightLimitInfo: Sequence[BagWeightLimit] | None = Field(
        None, description='Array of bag weight limits'
    )
    fee: Sequence[BagFees] | None = Field(None, description='Array of bag weight fees')


class BaggagePolicy(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    checkedIn: Sequence[BaggageInfo] = Field(
        ..., description='Policies for checked-in baggage'
    )
    carryOn: Sequence[BaggageInfo] = Field(
        ..., description='Policies for carry-on baggage'
    )


class CancellationOverride(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    waiverCode: str | None = Field(
        None,
        description='A waiver code to be applied to the refund. Typically a waiver code will be supplied by \nthe airline for the agent to use to override a cancellation fee.\n',
        examples=['WAV123'],
    )
    tourCode: str | None = Field(
        None,
        description='A tourcode to be applied to the refund. Input of a tourcode will override any tourcode \non the original ticket.\n',
        examples=['TRAC345'],
    )
    newCancellationPenalty: Money | None = Field(
        None,
        description='The new cancellation penalty, this will override the existing cancellation penalty.\n',
    )


class CancellationPolicy(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str | None = Field(
        None,
        description='Text describing the cancellation policy.',
        examples=['Non-refundable'],
    )
    fee: Money | None = None
    assessmentType: AssessmentType | None = Field(None, description='Assessment Type')
    isCat16: bool | None = Field(None, description='Is source cat16', examples=[True])
    isConditional: bool | None = Field(
        None, description='Is conditional', examples=[True]
    )


class Card(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID | None = Field(
        None,
        description='Unique identifier for this card',
        examples=['34d536b6-f8ff-11eb-9a61-0242ac180002'],
    )
    type: Type1 | None = Field(None, description='Type of card', examples=['CREDIT'])
    company: CardCompany | None = None
    name: str | None = Field(
        None, description='Name on card', examples=['Harrison Schwartz']
    )
    address: PostalAddress | None = Field(None, description='Billing address')
    number: str = Field(..., description='Card number', examples=['****************'])
    expiryMonth: conint(ge=1, le=12) | None = Field(
        None, description='Expiry month', examples=[1]
    )
    expiryYear: conint(ge=2000) | None = Field(
        None, description='Expiry year', examples=[2010]
    )
    cvv: str | None = Field(None, description='Card cvv number', examples=['012'])
    label: str | None = Field(None, description='Card Label', examples=['Label amex'])
    currency: str | None = Field(
        None, description='Native currency of the card.', examples=['USD']
    )
    externalId: str | None = Field(
        None,
        description='Spotnana partner card id.',
        examples=['bxt_RNGsNfzgJDaTstKIKqK4xEuhGYAnMdYK8T40'],
    )
    vaultId: UUID | None = Field(
        None,
        description='ID of the vault used for creating the card.',
        examples=['34d536b6-f8ff-11eb-9a61-0242ac180002'],
    )
    expiry: CardExpiry | None = Field(None, description='Card Expiry.')
    ownershipLabel: OwnershipLabel | None = Field(None, examples=['PERSONAL'])


class CardDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    company: CardCompany
    token: str = Field(
        ..., description='Tokenized Card Number', examples=['****************']
    )
    expiry: CardExpiry


class CompanyTransferableUnusedCreditsListResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    unusedCreditInfos: Sequence[UnusedCreditInfo] | None = None
    paginationParams: PaginationParams | None = None


class CompanyUnusedCreditsListResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    unusedCreditInfos: Sequence[UnusedCreditInfo] | None = None
    paginationParams: PaginationParams | None = None


class ComplexBagWeight(RootModel[TotalWeightWrapper | TotalWeightBreakdownWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: TotalWeightWrapper | TotalWeightBreakdownWrapper = Field(
        ...,
        description='Weight of bags in complex baggage option. If the breakdown is present, it will always have numBag items.\n',
    )


class Credit(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['CREDIT'] = Field(
        'CREDIT',
        description='Payment source type. Should be set to UNUSED_CREDIT for unused credit details.',
    )
    pnrOwningPcc: str | None = Field(None, description='PCC the PNR was created on.')
    unusedCreditPcc: str | None = Field(
        None, description='PCC the credit was issued on.'
    )
    departureCountry: str | None = Field(
        None,
        description='3 letter country code of the departure country associated with the original ticket.',
        examples=['USA'],
    )
    arrivalCountry: str | None = Field(
        None,
        description='3 letter country code of the arrival country associated with the original ticket.',
        examples=['USA'],
    )
    ticketType: TicketType1 = Field(..., description='Type of credit.')
    departureDate: DateTimeOffset | None = Field(
        None,
        description='Date for the departure of the first flight associated with the unused credit.',
    )
    segmentsAvailable: SegmentsAvailable = Field(
        ...,
        description='Whether all segments are unused or some have already been used.',
    )
    traveler: AirRequestTravelerInfo = Field(
        ...,
        description='Information about the traveler for which the credit should be redeemed.',
    )
    passengerName: Name = Field(
        ..., description='Name of the passenger associated with the credit.'
    )
    airlineInfo: AirlineInfo = Field(
        ..., description='Airline info with airline name and code'
    )
    totalFare: Money = Field(
        ..., description='Total airfare associated with the original ticket.'
    )
    issueDate: DateTimeOffset | None = Field(
        None, description='Issue date for the unused credit.'
    )
    expiryDate: DateTimeOffset | None = Field(
        None, description='Expiry date for the unused credit.'
    )
    source: ThirdPartySource | None = Field(
        'SABRE', description='Source of unused credit e.g. Sabre, NDC etc.'
    )
    sourcePnr: str = Field(
        ...,
        description='PNR number corresponding to third party through which booking was made.',
        examples=['MC5ONS'],
    )
    flightIds: Sequence[str] | None = Field(
        None,
        description='ID of the flights on which this credit applies.',
        min_length=1,
    )
    ticketNumber: str = Field(
        ...,
        description='Ticket number for the ticket that was converted into an unused credit.',
        examples=['5267779139217'],
    )


class CustomFieldMatchConditions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerConditions: TravelerMatchConditions | None = None
    travelTypes: Sequence[TravelType] | None = Field(
        None, description='Travel types to match.'
    )
    travelRegionTypes: Sequence[TravelRegionType] | None = Field(
        None, description='Travel region types to match.'
    )
    tripUsageTypes: Sequence[TripUsageType] | None = Field(
        None,
        description='Trip usage types to match. If empty, all trip usage types will be matched.',
    )


class CustomFieldSelectedOption(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Value of the selection')
    description: str | None = Field(None, description='Description of the selection')
    additionalUserInput: str | None = Field(None, description='Additional user input')
    additionalInfos: Sequence[str] | None = Field(
        None, description='Actual values of the additional infos'
    )
    additionalInfoConfigs: Sequence[AdditionalInfo] | None = Field(
        None, description='Additional info configs for the selected option'
    )


class CustomFieldV3Response(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fieldId: UUID = Field(..., description='Custom field id')
    fieldName: str | None = Field(None, description='Name of the custom field')
    armId: UUID = Field(..., description='Arm id which is applicable')
    includeLocations: Sequence[IncludeLocation] | None = None
    selectedOptions: Sequence[CustomFieldSelectedOption] = Field(
        ...,
        description='The list of options that are selected by user or auto populated.',
    )


class Dpan(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['DPAN'] = Field(
        'DPAN',
        description='Payment source type. Should be set to DPAN for card details.',
    )
    cardDetails: CardDetails


class EmergencyContactInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress | None = None
    designation: str | None = Field(None, examples=['MANAGER'])
    email: EmailStr = Field(..., examples=['<EMAIL>'])
    name: Name | None = None
    phoneNumber: PhoneNumber | None = None
    userOrgId: UserOrgId | None = None


class ExchangePolicy(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str | None = Field(
        None,
        description='Text describing the exchange policy.',
        examples=['Change allowed for free'],
    )
    fee: Money | None = None
    assessmentType: AssessmentType | None = None
    isCat16: bool | None = Field(None, description='Is source cat16', examples=[True])
    isConditional: bool | None = Field(
        None, description='Is conditional', examples=[True]
    )


class FareAmount(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    base: Money = Field(..., description='Base fare amount.')
    tax: Money = Field(..., description='Tax amount.')


class FareRules(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    baggagePolicy: BaggagePolicy | None = None
    cancellationPolicy: CancellationPolicy | None = None
    postDepartureCancellationPolicy: CancellationPolicy | None = Field(
        None, description='Information about the post departure cancellation policy.'
    )
    exchangePolicy: ExchangePolicy | None = None
    postDepartureExchangePolicy: ExchangePolicy | None = Field(
        None, description='Information about the post departure exchange policy.'
    )
    seatSelectionPolicy: SeatSelectionRule | None = None
    boardingPolicy: BoardingPolicy | None = None
    checkInPolicy: CheckInPolicy | None = None
    loungePolicy: LoungePolicy | None = None
    seatType: SeatTypeInfo | None = None


class FetchAirFareRuleResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legRuleInfos: Sequence[LegRuleInfo] | None = None


class FetchTravelerUnusedCreditsResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    unusedCreditInfo: Sequence[UnusedCreditInfo] | None = None


class Filter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cabin: CabinFilter | None = None
    maxNumStops: int | None = None
    timeRange: TimeRangeFilter | None = None
    alliance: AllianceFilter | None = None
    fareRange: MoneyRange | None = None
    airlinePref: AirlinePrefsFilter | None = None
    fareType: FareTypeFilter | None = 'UNKNOWN'
    changeable: ChangeableFilter | None = 'NO_CHANGEABLE_FILTER'
    connectingAirports: ConnectingAirportsFilter | None = None
    seatPref: SeatPrefFilter | None = None
    covid: CovidFilter | None = None
    baggage: BaggageFilter | None = None
    flightNumber: FlightNumberFilter | None = None
    policyFilter: PolicyFilter | None = None
    multiTicketFilter: MultiTicketFilter | None = None
    fareCategoryFilter: FareCategoryFilter | None = None
    corporateCodeFilter: CorporateCodeFilter | None = None
    unusedCreditFilter: UnusedCreditFilter | None = None


class KeywordWithReasonListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    keywordWithReasonList: KeywordsWithReasonList | None = None


class MerchantFeeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: FareAmount


class MigrateUnusedCreditsToCompanyCreditsResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    migrationStatus: Sequence[MigrationResponseInfo] | None = None


class MinMaxAmount(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    minLimit: Money | None = Field(
        None, description='The minimum amount that must be charged using this fop'
    )
    maxLimit: Money | None = Field(
        None, description='The maximum amount that could be charged using this fop'
    )


class MinMaxAmountWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    minMaxAmount: MinMaxAmount | None = None


class OptionInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    source: OptionSource
    sourceMetadata: OptionSourceMetadata | None = None
    totalNumOptions: int | None = Field(None, description='Total number of options')
    options: Sequence[Option] | None = Field(
        None,
        description='Available options for the question. This will contain only max 10 options if only \nsummary is requested.\n',
    )


class PaymentInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    applicableTo: Sequence[ApplicableToEnum] | None = None
    card: Card
    accessType: CreditCardAccessType | None = None
    access: CreditCardAccess | None = None


class PaymentSplitDetails(RootModel[MinMaxAmountWrapper | SplitAmountWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: MinMaxAmountWrapper | SplitAmountWrapper = Field(
        ...,
        description='Payment details of slider payment function.',
        title='PaymentSplitDetails',
    )


class PaymentVerificationInfo3(ThreeDSecure2VerificationInfoWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    gatewayIdentifier: GatewayIdentifier | None = None
    paymentKey: str | None = Field(
        None,
        description='Serialized key to identify the payment executed on payment gateway that will be used for post payment verification.',
    )


class PaymentVerificationInfo(
    RootModel[
        PaymentVerificationInfo1
        | PaymentVerificationInfo2
        | PaymentVerificationInfo3
        | PaymentVerificationInfo4
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        PaymentVerificationInfo1
        | PaymentVerificationInfo2
        | PaymentVerificationInfo3
        | PaymentVerificationInfo4
    ) = Field(
        ...,
        description='VerificationInfo required for the payment operation.',
        title='PaymentVerificationInfo',
    )


class PerTravelerPrice(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerType: PassengerType = Field(..., description='Type of passenger.')
    travelerAge: PassengerAge = Field(..., description='Age of passenger.')
    numTravelers: int = Field(
        ...,
        description='Number of travelers of the specified type and age.',
        examples=[3],
    )
    amount: FareAmount = Field(
        ..., description='Leg fare per passenger of the specified type and age.'
    )
    airlineFee: Money | None = Field(
        None, description='Ob fees applicable on the traveler fare.'
    )


class PolicyConstValue(
    RootModel[
        Int32Wrapper
        | Int64Wrapper
        | StringWrapper
        | DoubleWrapper
        | BoolWrapper
        | IntListWrapper
        | DoubleListWrapper
        | StringListWrapper
        | MoneyWrapper
        | LengthWrapper
        | PostalAddressWrapper
        | UserOrgIdWrapper
        | LegalEntityIdWrapper
        | OfficeIdWrapper
        | UserOrgIdListWrapper
        | LegalEntityIdListWrapper
        | OfficeIdListWrapper
        | RatingWrapper
        | PercentageWrapper
        | Int32RangeWrapper
        | DoubleRangeWrapper
        | PersonaWrapper
        | PersonaListWrapper
        | TravelClassHierarchyWrapper
        | KeywordWithReasonListWrapper
        | WorkerTypeWrapper
        | WorkerTypeListWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        Int32Wrapper
        | Int64Wrapper
        | StringWrapper
        | DoubleWrapper
        | BoolWrapper
        | IntListWrapper
        | DoubleListWrapper
        | StringListWrapper
        | MoneyWrapper
        | LengthWrapper
        | PostalAddressWrapper
        | UserOrgIdWrapper
        | LegalEntityIdWrapper
        | OfficeIdWrapper
        | UserOrgIdListWrapper
        | LegalEntityIdListWrapper
        | OfficeIdListWrapper
        | RatingWrapper
        | PercentageWrapper
        | Int32RangeWrapper
        | DoubleRangeWrapper
        | PersonaWrapper
        | PersonaListWrapper
        | TravelClassHierarchyWrapper
        | KeywordWithReasonListWrapper
        | WorkerTypeWrapper
        | WorkerTypeListWrapper
    )


class PolicyTakeApprovalAction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numHierarchyLevels: int | None = Field(
        None,
        description="How many levels of hierarchy should approval be taken? If it's just immediate manager, \nthis value would be 1. If it's manager and their manager, this would ge 2 and so on.\n",
    )
    positionTitles: Sequence[str] | None = Field(
        None,
        description='What position in the cost center or department. For eg, any business class upgrade \nmight require VP approval.\n',
    )
    userOrgIds: Sequence[UserOrgId] | None = Field(
        None,
        description='The specific users from whom to take approval. For eg, say for a company, all approvals\ngo through Adam.\n',
    )
    allRequired: bool | None = Field(
        None,
        description='This tells whether all the people above needs to approve or if any of these approve, \nit is sufficient.\n',
    )
    hardApprovalRequired: bool | None = Field(
        None,
        description='Whether to take soft approval (false) or hard approval (true).',
    )


class PolicyTakeApprovalActionWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    takeApproval: PolicyTakeApprovalAction | None = None


class PolicyViolationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    predicateString: str | None = None
    predicate: PolicyPredicate | None = None
    expectedValue: PolicyConstValue | None = None
    actualValue: PolicyConstValue | None = None


class PostPaymentVerificationInfo4(ThreeDSecure2PostVerificationInfoWrapper):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentKey: str | None = Field(
        None,
        description='Serialized key to identify the payment executed on payment gateway that will be used for post payment verification.',
    )


class PostPaymentVerificationInfo(
    RootModel[
        PostPaymentVerificationInfo1
        | PostPaymentVerificationInfo2
        | PostPaymentVerificationInfo3
        | PostPaymentVerificationInfo4
        | PostPaymentVerificationInfo5
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        PostPaymentVerificationInfo1
        | PostPaymentVerificationInfo2
        | PostPaymentVerificationInfo3
        | PostPaymentVerificationInfo4
        | PostPaymentVerificationInfo5
    ) = Field(
        ...,
        description='Payment information sent after the verification of payment method.',
        title='PostPaymentVerificationInfo',
    )


class Question(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID
    name: str = Field(
        ...,
        description="Question display name that the user will see. For eg, 'Choose the purpose of your trip'.",
    )
    questionFormat: QuestionFormat | None = None
    optionInfo: OptionInfo | None = None
    isRequired: bool = Field(
        ...,
        description='Whether its compulsory to answer the question or not.',
        examples=[True],
    )
    isDisabled: bool = Field(
        ...,
        description='Whether the question is disabled or not. If true, this should not be asked.',
        examples=[True],
    )
    includeInItinerary: bool | None = Field(
        False,
        description='Whether to include this question in the itinerary related emails.',
        examples=[True],
    )
    customFieldLocations: Sequence[CustomFieldLocation] | None = None
    matchConditions: CustomFieldMatchConditions | None = None
    questionType: QuestionType | None = None


class RailPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preferredRailStations: Sequence[PreferredRailStation] | None = Field(
        None, description='A list of user preferred rail stations.'
    )
    seatPreference: SeatPref | None = None
    travelClasses: Sequence[RailTravelClass] | None = Field(
        None, description='A list of class of service for rail.'
    )
    coachPreferences: Sequence[CoachPref] | None = Field(
        None, description='A list of coach preference for rail.'
    )
    conditionalRates: Sequence[ConditionalRate] | None = Field(
        None, description='A list of conditional rates for rail.'
    )


class RawPaymentSourceDetails(RootModel[Dpan | Credit]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Dpan | Credit = Field(
        ...,
        description='Raw Details of the Payment Source',
        discriminator='type',
        title='RawPaymentSourceDetails',
    )


class RowSection(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    rowNumbers: Int32Range | None = None
    availableSeats: Sequence[AvailableSeats] | None = Field(
        None, description='Available seats for each row in this Row Section.'
    )
    rowTypes: Sequence[RowType] | None = None
    facilitySections: Sequence[FacilitySection] | None = None
    seatSections: Sequence[SeatSection] | None = None


class SeatRemarkIdentifier(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legIndex: int = Field(
        ...,
        description='Index referencing the leg for which the changes are requested.',
        examples=[1],
    )
    flightIndex: int = Field(
        ...,
        description='Index referencing the flight for which the changes are requested.',
        examples=[1],
    )
    seatIdentifier: str = Field(
        ..., description='Selected seat number for the flight.', examples=['10A']
    )
    travelerInfo: UserOrgId | None = None


class SelectedPaymentSource(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentSourceId: UUID | None = Field(
        None,
        description='Unique identifier identifying this payment source.',
        examples=['f49d00fe-1eda-4304-ba79-a980f565281d'],
    )
    rawPaymentSource: RawPaymentSourceDetails | None = None
    postPaymentRedirectionUrl: str | None = Field(
        None,
        description='Url for post payment redirection if payment source navigates user to a third party url',
        examples=[
            'https://mycompany.com/checkout?paymentSourceId=f49d00fe-1eda-4304-ba79-a980f565281d'
        ],
    )
    cvv: str | None = Field(
        None, description='CVV associated with associated payment source, if any.'
    )
    amount: Money | None = Field(
        None, description='Total amount to be charged for specified payment items.'
    )


class SelectedPaymentSourceWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    selectedPaymentSource: SelectedPaymentSource | None = None


class TicketToExchangeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ticketNumber: str | None = Field(
        None,
        description='Ticket number. For non-ticketed PNRs, this field will not be present.',
        examples=['0111234567890'],
    )
    exchangeState: AirExchangeState | None = Field(
        None, description='Current exchange state for this ticket.'
    )
    isRefundable: bool | None = Field(
        None,
        description='A flag to indicate if ticket is refundable or not.',
        examples=[False],
    )
    exchangeNotSupportedReasons: Sequence[ExchangeNotSupportedReason] | None = Field(
        None, description='Reasons why exchange is not supported via OBT.', min_length=1
    )
    supplierPreferredEnabledFields: AirExchangeAvailableOptions | None = Field(
        None,
        description='Contains the information of the options that are allowed to exchange.',
    )
    userOrgId: UserOrgId | None = Field(
        None, description='User org ID of associated traveler.'
    )
    travelerInfo: TravelerInfoResponse | None = Field(
        None, description='Traveler info for this traveler'
    )


class TravelPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airPref: AirPref | None = None
    preferredCurrency: str | None = Field(None, examples=['USD'])
    railCards: Sequence[RailCard] | None = None
    railPref: RailPref | None = None
    carPref: CarPref | None = None
    hotelPref: HotelPref | None = None


class TravelerBookingInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerId: UserId | None = Field(None, description='The ID of the traveler.')
    travelerInfo: AirRequestTravelerInfo | None = Field(
        None, description='The information about the traveler of given travelerType.'
    )
    travelerType: PassengerType | None = None
    title: UserTitle | None = None
    name: Name | None = None
    gender: Gender | None = None
    dob: DateModel | None = None
    phoneNumber: PhoneNumber | None = None
    email: EmailStr | None = Field(None, examples=['<EMAIL>'])
    identityDocs: Sequence[IdentityDocument] | None = None
    address: PostalAddress | None = None
    emergencyContactInfo: EmergencyContactInfo | None = None
    loyaltyInfos: Sequence[LoyaltyInfo] | None = None
    emergencyContact: EmergencyContact | None = None


class TravelerLegInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerId: str = Field(
        ...,
        description='Unique identifier for the traveler in this response',
        examples=['adult_0'],
    )
    travelerLegFare: FareAmount | None = Field(
        None, description='The fare amount for the traveler for a given leg'
    )
    fareRules: FareRules | None = Field(
        None, description='Fare rules applicable for the traveler'
    )


class TravelerPersonalInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    loyaltyInfos: Sequence[LoyaltyInfo] | None = None
    travelPref: TravelPref | None = None


class User(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    addresses: Sequence[PostalAddress] | None = None
    dob: DateModel | None = None
    email: EmailStr | None = Field(None, examples=['<EMAIL>'])
    emergencyContactInfo: EmergencyContactInfo | None = None
    emergencyContact: EmergencyContact | None = None
    gender: Gender | None = None
    identityDocs: Sequence[IdentityDocument] | None = Field(
        None,
        description='List of user identity documents.',
        examples=[
            [
                {
                    'passport': {
                        'docId': 'PASSPORTID',
                        'expiryDate': {'iso8601': '2017-07-21'},
                        'issueCountry': 'IN',
                        'issuedDate': {'iso8601': '2017-07-21'},
                        'nationalityCountry': 'IN',
                        'type': 'REGULAR',
                    }
                },
                {'ktn': {'number': '123456', 'issueCountry': 'US'}},
            ]
        ],
    )
    name: Name | None = None
    paymentInfos: Sequence[PaymentInfo] | None = None
    phoneNumbers: Sequence[PhoneNumber] | None = None
    profilePicture: Image | None = None
    nationality: str | None = Field(
        None, description='Nationality of user', examples=['Indian']
    )
    title: UserTitle | None = None


class UserBasicInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userOrgId: UserOrgId | None = None
    persona: Persona | None = None
    email: EmailStr | None = Field(None, examples=['<EMAIL>'])
    name: Name | None = None
    profilePicture: Image | None = None
    tier: Tier | None = 'BASIC'
    phoneNumber: PhoneNumber | None = None
    employeeId: str | None = Field(None, description='Employee id of the user')
    isActive: bool | None = Field(
        None, description='Whether user profile is active or not.', examples=[True]
    )


class UserBusinessInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    department: Department | None = None
    designation: str | None = Field(None, examples=['MANAGER'])
    email: EmailStr = Field(..., examples=['<EMAIL>'])
    employeeId: str = Field(
        ...,
        description="Unique employee id. Can use email if a company don't use employee ids.",
        examples=['101'],
    )
    grade: Grade | None = None
    legalEntityId: LegalEntityId
    managerBasicInfo: UserBasicInfo | None = None
    office: Office | None = None
    organizationId: OrganizationId
    phoneNumbers: Sequence[PhoneNumber] | None = None
    userCostCenter: CostCenter | None = None
    designatedApproverInfos: Sequence[UserBasicInfo] | None = Field(
        None, description='A list of user basic info for designated approvers.'
    )
    designatedApproverUserIds: Sequence[UserId] | None = Field(
        None, description='A list of userId for designated approvers.'
    )
    authorizerEmail: str | None = Field(
        None,
        description='Email address to be used as approval authorizer, when a manager is not present.',
        examples=['<EMAIL>'],
    )


class AirBookTravelerInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerDetails: TravelerBookingInfo
    seats: Sequence[SelectedSeat]
    baggages: Sequence[SelectedBaggage]
    ancillaries: Sequence[SelectedAncillary] | None = Field(
        None, description='The ancillary options selected during checkout.'
    )
    shareContactInfo: bool | None = Field(
        None,
        description="Flag to support IATA regulation 830d that requires user's consent to share email \nand phone with airline. If set to true, airlines will have access to\ntraveler's email and phone to directly communicate events like operational disruption.\n",
        examples=[False],
    )
    specialServiceRequests: Sequence[SpecialServiceRequestInfo] | None = Field(
        None, description='List of all Special Service Requests (SSR) for the traveler.'
    )


class AirCancelPnrRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str = Field(
        ...,
        description='The spotnana assigned unique PNR id of the booking.',
        examples=['1234567819'],
    )
    optionId: str = Field(
        ...,
        description='Cancellation option id chosen by the traveler prior to cancelling the booking. The\ntrip->cancellation-details API returns a bunch of options like whether the traveler\nwants to keep the ticket as unused or get the refunds with applicable penalties.\n',
        examples=['OPTION123'],
    )
    cancellationOverride: CancellationOverride | None = Field(
        None,
        description='Cancellation info override details, like cancellation penalty, waiver code, etc.',
    )
    customFieldV3Responses: Sequence[CustomFieldV3Response] | None = Field(
        None, description='Custom field responses for the booking.'
    )


class AirEditPnrResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentVerificationInfo: PaymentVerificationInfo | None = None


class AirLegInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., description='The unique ID for this leg.', examples=['leg_0'])
    flights: Sequence[FlightDetail] = Field(
        ..., description='Details of the list of flights present in the leg.'
    )
    travelerInfos: Sequence[TravelerLegInfo] = Field(
        ..., description='Details of passenger(s) information for this leg.'
    )
    totalLegFare: FareAmount = Field(
        ...,
        description='Total cumulative fare for this leg summed across all passengers',
    )
    fareCategory: FareCategory = Field(
        ...,
        description='Fare category of this leg. It describes the cabin class for this flight.',
    )
    brandName: str | None = Field(
        None,
        description='The brand name advertised by the airline.',
        examples=['Economy Flex'],
    )
    brandCode: str | None = Field(
        None,
        description='The brand code assigned by the airline corresponding brandName.',
        examples=['AADOM-MAIN'],
    )
    validatingAirline: str | None = Field(
        None,
        description='The airline code for the airline which is validating this booking and orchestrating the payment.',
        examples=['AA'],
    )
    rateType: RateType | None = Field(
        None,
        description='Rate or fare type for this leg (for example "public" or "corporate").',
    )
    vendorProgramType: VendorProgramType | None = None


class AirModifyBookingRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingId: str = Field(
        ...,
        description='Booking id returned in successful response of air-revalidate-itin api call in the booking flow.',
        examples=['ChBlMTFmOTVkZTcwZmZjMmI2EhAwZjVkNDhhNGJjNWExZTMw'],
    )
    tripData: TripData
    skipTicketing: bool | None = Field(
        None,
        description='If this is true, the PNR is not set up for ticketing.',
        examples=[True],
    )
    postPaymentVerificationInfo: PostPaymentVerificationInfo | None = None
    customFieldV3Responses: Sequence[CustomFieldV3Response] | None = Field(
        None, description='Custom field responses for the booking.'
    )
    waiveOffInfo: WaiveOffInfo | None = None


class AirModifyBookingResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrNumber: str | None = Field(None, examples=['ABXDFZ'], title='Source PNR ID')
    pnrStatus: PnrStatusModel | None = None
    pnrId: str | None = Field(
        None, description='Spotnana PNR ID', examples=['1213124111']
    )
    paymentVerificationInfo: PaymentVerificationInfo | None = None


class AirPnrExchangeDetailsResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str = Field(
        ..., description='PNR ID for the booking.', examples=['1234567890']
    )
    ticketToExchangeInfos: Sequence[TicketToExchangeInfo] | None = Field(
        None, description='List of tickets and their exchange info.'
    )


class BagFareInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalFare: FareAmount | None = Field(
        None,
        description='The fare amount (cost of) associated with the baggage option.',
    )


class BookingFareBreakdown(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalFare: FareAmount = Field(
        ...,
        description='Total amount charged on card including ancillaries and all other charges like OB fees, merchant fees, and penalty.',
    )
    merchantFee: Money | None = Field(
        None, description='Total merchant fee for this booking.'
    )
    airlineFee: Money | None = Field(
        None, description='Total OB fees applicable for this booking.'
    )


class CabinSection(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cabin: Cabin | None = None
    bookingCode: str | None = Field(None, examples=['B'])
    locations: Sequence[SeatLocation] | None = Field(
        None, description='The list of locations associated with the cabin section.'
    )
    facilitySections: Sequence[FacilitySection] | None = Field(
        None, description='The set of associated facility sections.'
    )
    columnSections: Sequence[ColumnSection] | None = Field(
        None, description='The columns associated with the cabin section.'
    )
    rowSections: Sequence[RowSection] | None = Field(
        None, description='The rows associated with the cabin section.'
    )


class EntityAnswer(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    entityId: str | None = Field(None, description='The unique ID for the question.')
    userInput: str | None = Field(
        None, description='The text input given by user (if any).'
    )
    itemIds: Sequence[int] | None = Field(
        None,
        description='The id/enum value corresponding to the option chosen by the user as\nanswer.\n',
    )
    answers: Sequence[AnswerPair] | None = None
    customFieldType: CustomFieldType | None = 'QUESTION'
    questionDisplayText: str | None = Field(
        None, description='The question text to be displayed to the user.'
    )
    question: Question | None = None


class FareInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalFare: FareAmount = Field(
        ..., description='Total fare applicable to the associated entity.'
    )
    taxBreakdown: TaxBreakdown | None = Field(
        None, description='Details about of the tax amount in totalFare field.'
    )
    merchantFeeInfo: MerchantFeeInfo | None = None


class FlightSeatMap(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    seatMapId: str | None = Field(
        None,
        description='Unique identifier for this flight seat map.',
        examples=['seat_map_0'],
    )
    wingRows: Int32Range | None = Field(
        None, description='Defines the wing rows in the seat map.'
    )
    cabinSections: Sequence[CabinSection] | None = Field(
        None, description='Cabin sections available in the flight.'
    )


class LegPrice(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legId: str | None = Field(None, description='The leg ID.', examples=['leg-id'])
    amount: FareAmount | None = Field(
        None,
        description='The price breakdown of leg. If the price breakdown of leg is not available, price will\ndenote the price of entire itinerary.\n',
    )
    travelerPrices: Sequence[PerTravelerPrice] | None = Field(
        None, description='Price breakdown for each traveler type, if available.'
    )
    airlineFee: Money | None = Field(
        None,
        description='Total OB Fees for this leg. OB fees are generally charged by airlines in addition to the base ticket price.\nThese fees can include service or credit card fees charged by the airline.\n',
    )


class PaymentRule(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentSplitDetails: PaymentSplitDetails | None = Field(
        None, description='Payment details of slider payment function'
    )
    paymentSourceIds: Sequence[UUID] | None = None
    allowAddCard: bool | None = Field(
        None,
        description='Whether to allow user to add a new card for the selected split payment.',
    )
    splitCriterion: PaymentSplitCriterion | None = Field(
        None, description='Defines the criteria for splitting a payment'
    )
    unusablePaymentSources: Sequence[UnusablePaymentSource] | None = Field(
        None,
        description='List of payment sources that are not usable for this payment rule',
    )
    paymentSourceConditions: Sequence[PaymentSourceCondition] | None = Field(
        None, description='List of conditions for payment sources'
    )


class PolicyAction(
    RootModel[
        PolicyFlagActionWrapper
        | PolicyHideActionWrapper
        | PolicyAlertOnSelectionActionWrapper
        | PolicyTakeApprovalActionWrapper
        | PolicyPreventBookingActionWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        PolicyFlagActionWrapper
        | PolicyHideActionWrapper
        | PolicyAlertOnSelectionActionWrapper
        | PolicyTakeApprovalActionWrapper
        | PolicyPreventBookingActionWrapper
    ) = Field(..., description='Action that is required / done for policy.')


class PolicyRuleResultInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    violationInfos: Sequence[PolicyViolationInfo] | None = None
    subViolationInfos: Sequence[PolicyViolationInfo] | None = Field(
        None,
        description='In case of complex rules this will contain extra information as to how the rule was \ncalculated.\n',
    )
    actions: Sequence[PolicyAction] | None = Field(
        None,
        description='Followed actions if rule was satisfied else violated actions.',
    )


class PreBookAnswers(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    answers: Sequence[EntityAnswer] | None = None
    preBookQuestionResponseId: str | None = Field(
        None,
        description='The unique id sent back in the pre book questions API response',
    )


class PreSearchAnswers(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    answers: Sequence[EntityAnswer] | None = None
    userEntitiesResponseId: str | None = None


class SeatNoteRemark(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    remarkType: Literal['SEAT_SELECTION_REMARK'] = Field(
        'SEAT_SELECTION_REMARK',
        description='Type of remark. Always set to SEAT_SELECTION_REMARK.',
    )
    remarkIdentifier: SeatRemarkIdentifier
    remark: Remark


class SelectedFormOfPayment(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentItems: Sequence[PaymentItem]
    selectedPaymentSources: Sequence[SelectedPaymentSource]


class SelectedPaymentMethod(RootModel[SelectedPaymentSourceWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: SelectedPaymentSourceWrapper = Field(
        ..., description='Selected payment method for the booking.'
    )


class SimpleBag(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    weightLimit: WeightLimit | None = None
    bagFareInfo: BagFareInfo | None = None


class SimpleBagOption(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    baggageOptionId: str | None = Field(None, examples=['bag_0'])
    description: str | None = Field(None, examples=['1 bag for $20'])
    simpleBag: SimpleBag | None = None


class TotalAmountBreakdownWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalAmountBreakdown: Sequence[BagFareInfo] | None = Field(
        None, description='The total amount breakdown of a baggage fare.'
    )


class TotalAmountWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    totalAmount: BagFareInfo | None = Field(
        None, description='The total amount of a baggage fare.'
    )


class Traveler(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerPersonalInfo: TravelerPersonalInfo | None = None
    user: User | None = None
    userBusinessInfo: UserBusinessInfo | None = None
    userOrgId: UserOrgId | None = None
    persona: Persona | None = None
    isActive: bool | None = Field(
        None,
        description='A boolean flag to show if traveler is active.',
        examples=[True],
    )
    tier: Tier | None = 'BASIC'
    adhocUserInfo: AdhocUserInfo | None = None
    externalId: str | None = Field(None, description='External id of this user.')


class TravelerDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerId: str = Field(
        ...,
        description='Unique identifier for the traveler in this response',
        examples=['adult_0'],
    )
    travelerType: PassengerType
    travelerAge: PassengerAge | None = None
    fareInfo: FareInfo
    fareMetadata: FareMetadata | None = None


class AirCreatePnrRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingId: str | None = Field(
        None,
        description='The booking ID for which the PNR is being created. This is returned in the revalidate itinerary API response.',
        examples=['booking-id'],
    )
    initiateBookingId: str | None = Field(
        None,
        description='Initiate booking id is returned in response to air-initiate-booking API call. This id is required if in case the booking id is not available due to third-party redirection.',
        examples=['eCfghty567jkHG56DFgh'],
    )
    preBookAnswers: PreBookAnswers | None = None
    preSearchAnswers: PreSearchAnswers | None = None
    postPaymentVerificationInfo: PostPaymentVerificationInfo | None = None
    isPreAuthApprovalRequired: bool | None = Field(
        None,
        description='Flag to check if the pre-authorization approval is enabled.',
        examples=[False],
    )
    customFieldV3Responses: Sequence[CustomFieldV3Response] | None = Field(
        None, description='Custom field responses for the booking.'
    )


class PreSearchAnswers1(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    answers: Sequence[EntityAnswer] | None = None


class AirModifySearchRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str = Field(
        ...,
        description='The unique spotnana pnr id which needs to be exchanged.',
        examples=['1213124111'],
    )
    legs: Sequence[Leg] = Field(
        ...,
        description='The list of all legs (to keep as same, to remove and to be added). In the example shared, if there is a trip from SFO to ANC with one Leg containing a direct flight from mentioned source to destination, and traveller needs to add a visit to Seattle in between changing his trip to now contain two legs SFO->SEA and SEA->ANC, then we would remove the first existing leg(indexed 0) SFO->ANC and add two new legs SFO->SEA and SEA->ANC.',
        examples=[
            [
                {'index': 0, 'remove': True},
                {'source': 'SFO', 'destination': 'SEA', 'date': '2022-01-21T17:00'},
                {'source': 'SEA', 'destination': 'ANC', 'date': '2022-01-22T17:00'},
            ]
        ],
    )
    filters: Sequence[Filter] | None = None
    sortOptions: Sequence[SortOption] | None = None
    legSearchParams: LegSearchParams | None = None
    preModifySearchAnswers: Sequence[PreSearchAnswers] | None = Field(
        None, title='optional Q and A for business management and audit'
    )


class AirSeatMapResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    seatMapResponseId: str | None = Field(
        None,
        description='Unique identifier for this seat map response, which would need to be passed on in subsequent booking APIs.',
        examples=['seat1234'],
    )
    travelerSeatMaps: Sequence[TravelerSeatMap] | None = Field(
        None, description='Seat maps for a flight for each traveler type.'
    )
    seatMaps: Sequence[FlightSeatMap] | None = Field(
        None, description='The list of seat maps available in the response.'
    )


class AirTravelerInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerId: str | None = Field(
        None, description='Unique ID for traveler', examples=['1']
    )
    travelerInfo: Traveler | None = Field(None, description='Traveler Information')
    adhocTravelerInfo: TravelerInfoResponse | None = Field(
        None, description='Adhoc Traveler info for this traveler'
    )


class AllowedFopRule(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentItems: Sequence[PaymentItem] | None = None
    amount: Money | None = Field(
        None, description='Total amount for specified fare components'
    )
    paymentRules: Sequence[PaymentRule] | None = None


class BookingCharge(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: FareAmount | None = Field(
        None,
        description="The amount to be charged for this payment method. This is optional if\nthere's only one payment method provided for the booking.\n",
    )
    paymentMethod: SelectedPaymentMethod | None = None


class BookingTravelerPaymentDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    selectedFormOfPayments: Sequence[SelectedFormOfPayment]


class ComplexBagFareInfo(RootModel[TotalAmountWrapper | TotalAmountBreakdownWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: TotalAmountWrapper | TotalAmountBreakdownWrapper = Field(
        ...,
        description='Fare (cost) information for bags. If the breakdown is present it will always have numBag items.',
    )


class CorporateInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preSearchAnswers: PreSearchAnswers


class MultiSelectOptionGroup(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    multiSelectOptions: Sequence[SimpleBagOption] = Field(
        ..., description='A list of baggage options that may be selected.'
    )


class PnrPolicyInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    policies: Sequence[PnrPolicyId] | None = Field(
        None, description='List of policies applied to the itinerary.'
    )
    ruleResultInfos: Sequence[PolicyRuleResultInfo] | None = None


class RemarkObject(RootModel[SeatNoteRemark]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: SeatNoteRemark = Field(
        ...,
        description='Wrapper object for storing one of the pnr remark types.',
        discriminator='remarkType',
        title='RemarkObject',
    )


class TravelerUpdateInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    traveler: AirTravelerInfo
    seats: Sequence[SeatDetail] | None = None
    seatPreference: SeatPreference | None = Field(
        None,
        description='In case seat is not selected for the traveler, this field can be used to set the seat preference and reserve seat if it is free of cost',
    )
    otherAncillaries: Sequence[OtherAncillaryDetail] | None = None
    specialServiceRequests: Sequence[SpecialServiceRequestInfo] | None = Field(
        None, description='List of all Special Service Requests (SSR) for the traveler.'
    )
    updateTypes: Sequence[UpdateType]


class AirInitiateBookingRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    initiateBookingWorkflowIds: InitiateBookingWorkflowIds | None = None
    travelers: Sequence[AirBookTravelerInfo] | None = None
    bookingCharges: Sequence[BookingCharge] | None = None
    bookingContact: BookingContact | None = Field(
        None,
        description='Booking contact for the itinerary. Airlines send all update and change information to booking contact as well.',
    )
    useExistingBooking: bool | None = Field(
        None, description='Use existing booking if it exists to initiate'
    )


class AirItin(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    itineraryId: str = Field(
        ..., description='The unique ID for this itinerary.', examples=['kjdUjak8hX']
    )
    legs: Sequence[AirLegInfo]
    travelers: Sequence[TravelerDetail]
    fareInfo: FareInfo | None = Field(
        None, description='Describes the fare amount and taxes.'
    )
    ticketType: TicketType = Field(
        ...,
        description='Indicates whether this itinerary will be a single or multi ticket',
    )
    policyInfos: PnrPolicyInfo | None = Field(
        None, description='The policy details evaluated on the itinerary.'
    )
    posInfo: Sequence[POSInfo] | None = Field(
        None, description='The point of sales which offer the itinerary.'
    )
    publicFare: FareAmount | None = Field(
        None,
        description='The public fare attribute in case private fare is applied for any pax',
    )
    allowedFopRules: Sequence[AllowedFopRule] | None = Field(
        None, description='The payment method types applicable to the itinerary.'
    )
    fareAttributes: FareAttributes | None = Field(
        None, description='The fare attributes applicable to the itinerary.'
    )


class AirRevalidateItineraryRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    workflowIds: WorkflowIds | None = None
    tripId: TripId | None = Field(
        None, description='The trip ID with which this booking will be associated.'
    )
    travelers: Sequence[AirBookTravelerInfo] | None = None
    bookingCharges: Sequence[BookingCharge] | None = None
    promotionCode: PromotionCode | None = Field(
        None, description='Promo code applied to the order (optional)'
    )


class AirRevalidateItineraryResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingId: str | None = Field(
        None, description='The booking ID for the booking.', examples=['bookingid']
    )
    fareBreakDown: BookingFareBreakdown | None = None
    legPrices: Sequence[LegPrice] | None = None
    perTravelerPrices: Sequence[PerTravelerPrice] | None = None
    policyDetails: PnrPolicyInfo | None = None


class AirSearchRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelers: Sequence[TravelerSearchInfo] = Field(
        ...,
        description='Information about each of the travelers in the search request. This information is required.',
    )
    legs: Sequence[SearchLeg] = Field(
        ...,
        description='The list of all legs for which search results need to be returned.',
    )
    filters: Sequence[Filter] | None = Field(
        None, description='The list of filters to be applied in the search request.'
    )
    sortOptions: Sequence[SortOption] | None = Field(
        None, description='The sort options to be used for ordering the itineraries.'
    )
    legSearchParams: LegSearchParams | None = None
    corporateInfo: CorporateInfo | None = None


class AirSelectedItineraryResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    itinerary: AirItin = Field(..., description='The itinerary selected by the user.')


class BookingPaymentDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingTravelerPaymentDetails: Sequence[BookingTravelerPaymentDetails]


class ComplexBag(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numBag: int = Field(..., description='Number of bags in this complex bag option.')
    complexBagWeight: ComplexBagWeight
    complexBagFareInfo: ComplexBagFareInfo


class ComplexBagOption(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    baggageOptionId: str | None = Field(None, examples=['bag_0'])
    description: str | None = Field(
        None, examples=['3 bags of 10+20+30kg worth 100$ $(20+30+50).']
    )
    complexBag: ComplexBag | None = None


class ItineraryInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    itineraries: Sequence[AirItin] | None = None
    flightData: Sequence[FlightCommon] | None = Field(
        None,
        description='The set of flights and their details that are contained in this response.',
    )


class PnrNoteRemark(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    remark: RemarkObject


class SingleSelectOptionGroup(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    singleSelectOptions: Sequence[ComplexBagOption]


class AirEditPnrRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrRemarks: Sequence[PnrNoteRemark] | None = Field(
        None, description='Note remarks to be added for pnr at time of pnr update'
    )
    pnrId: str = Field(
        ...,
        description='PNR ID created by Spotnana for the booking.',
        examples=['1213124111'],
    )
    travelerInfo: Sequence[TravelerUpdateInfo] = Field(
        ..., description='Traveler information.', examples=[[]]
    )
    seatMapResponseId: str | None = Field(
        None,
        description='The `seatMapResponseId` generated using the [Get flight seat map](#operation/airSeatMap) API.\nThis field is only required if the seat selection is being changed for the traveler.\n',
        examples=['8bc4ec0e1839aabc'],
    )
    preSearchAnswers: PreSearchAnswers1 | None = Field(
        None, description='Contains a list of custom fields or pre search answers.'
    )
    pnrUpdateTypes: Sequence[Literal['OTHER_SERVICE_INFO']] | None = None
    otherServiceInformationItems: Sequence[OtherServiceInformation] | None = Field(
        None,
        description='The list of all Other Service Information (OSI) to be included in the PNR.',
    )
    bookingPaymentDetails: BookingPaymentDetails | None = None
    postPaymentVerificationInfo: PostPaymentVerificationInfo | None = None
    waiveOffInfo: WaiveOffInfo | None = None


class AirSearchResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchId: str = Field(
        ...,
        description='The unique ID for this search response. This value must be passed on in the subsequent booking API calls.',
        examples=['ChBjZDg3ZjRjZmRmMTFmMWFiEhBjZDg3Z'],
    )
    itineraryDetails: ItineraryInfo = Field(
        ..., description='The list of itineraries in the search response.'
    )
    paginationParams: PaginationResponseParams | None = None
    metadata: AirMetadata | None = None


class BaggageOptionGroup(RootModel[SingleSelectOptionGroup | MultiSelectOptionGroup]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: SingleSelectOptionGroup | MultiSelectOptionGroup = Field(
        ...,
        description='Describes a group of baggage options from which the user can select one or more.',
        title='BaggageOptionGroup',
    )


class TravelerBaggageOptions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerType: PassengerType | None = Field(
        None,
        description='Type of the traveler. Present when traveler applicability is PER_TRAVELER.',
    )
    travelerAge: PassengerAge | None = Field(
        None,
        description='Age of the traveler. Present when traveler applicability is PER_TRAVELER.',
    )
    baggageOptions: BaggageOptionGroup | None = None


class BaggageSelectionGroup(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerApplicability: TravelerApplicability | None = None
    legApplicability: LegApplicability | None = None
    travelerBaggageOptions: Sequence[TravelerBaggageOptions] | None = None


class BaggageSelectionInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    baggageSelectionGroups: Sequence[BaggageSelectionGroup] | None = None


class AirCheckoutResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    checkoutResponseId: str = Field(
        ...,
        description='The unique ID which identifies this checkout response. This value must passed on in the subsequent booking API calls.\n',
        examples=['ChBjZDg3ZjRjZmRmMTFmMWFiEhBjZDg3Z'],
    )
    baggageInfo: BaggageSelectionInfo | None = None
    ancillaries: Sequence[AncillarySelectionInfo] | None = Field(
        None, description='List of ancillaries available for purchase.'
    )
    mandatoryCheckoutParams: Sequence[MandatoryParamForCheckout] | None = None
    airlineCardFees: Sequence[AirlineCardFee] | None = Field(
        None,
        description='List of all the supported card options. If this list is empty, all cards are supported.',
    )
