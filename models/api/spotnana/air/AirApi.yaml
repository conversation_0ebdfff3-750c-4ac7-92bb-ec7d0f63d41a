openapi: 3.1.0
info:
  title: Air API
  version: v2
servers:
- url: https://apis.spotnana.com
  description: Staging URL
security:
- Bearer: []
components:
  securitySchemes:
    Bearer:
      type: http
      scheme: bearer
  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFound:
      description: The specified resource was not found.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
  schemas:
    AccessType:
      type: string
      description: Access Type of the Payment Source being created.
      enum:
      - CENTRALISED
      - PERSONAL
      - APPLICATION
      - TMC
      - ITINERARY
      - EVENT
      - EVENT_TEMPLATE
      example: CENTRALISED
      x-ignoreBreakingChanges:
      - AccessType->EVENT_TEMPLATE
    AdditionalInfo:
      type: object
      title: AdditionalInfo
      description: Additional data need to be sent along with the custom field response.
      discriminator:
        propertyName: type
        mapping:
          VARIABLE: '#/components/schemas/Variable'
          EXPRESSION: '#/components/schemas/Expression'
      oneOf:
      - $ref: '#/components/schemas/Variable'
      - $ref: '#/components/schemas/Expression'
    AdhocTravelerInfo:
      title: AdhocTravelerInfo
      type: object
      description: Describes an adhoc traveler.
      properties:
        registrarUserId:
          description: The registrar of the adhoc traveler.
          $ref: '#/components/schemas/UserId'
        externalId:
          type: string
          description: External Id of user
          example: qwert123
    AdhocTravelerInfoWrapper:
      title: AdhocTravelerInfoWrapper
      description: Wrapper over AdhocTravelerInfo object to be used as an identifier
        in a oneOf case.
      properties:
        adhocTravelerInfo:
          $ref: '#/components/schemas/AdhocTravelerInfo'
    AdhocUserInfo:
      type: object
      title: AdhocUserInfo
      description: Basic information related to ad-hoc traveler profile.
      required:
      - profileOwner
      properties:
        profileOwner:
          $ref: '#/components/schemas/ProfileOwner'
        isSaved:
          type: boolean
          description: "A boolean flag to show if ad-hoc traveler is visible in search.\
            \ While updating the user \nif client tries to update this field, it will\
            \ throw exception.\n"
          default: false
    AirAmenity:
      type: object
      title: AirAmenity
      oneOf:
      - $ref: '#/components/schemas/SeatAmenityWrapper'
      - $ref: '#/components/schemas/WifiAmenityWrapper'
      - $ref: '#/components/schemas/PowerAmenityWrapper'
      - $ref: '#/components/schemas/EntertainmentAmenityWrapper'
      - $ref: '#/components/schemas/BeverageAmenityWrapper'
      - $ref: '#/components/schemas/AircraftAmenityWrapper'
      - $ref: '#/components/schemas/LayoutAmenityWrapper'
      - $ref: '#/components/schemas/FreshFoodAmenityWrapper'
      - $ref: '#/components/schemas/CleaningAmenityWrapper'
      - $ref: '#/components/schemas/MaskAmenityWrapper'
      - $ref: '#/components/schemas/TemperatureAmenityWrapper'
      - $ref: '#/components/schemas/PassengerCapacityAmenityWrapper'
      - $ref: '#/components/schemas/BlockedAdjacentSeatsAmenityWrapper'
      - $ref: '#/components/schemas/CovidTestingAmenityWrapper'
      - $ref: '#/components/schemas/VaccineAmenityWrapper'
    AirApplicableLoyaltyResponse:
      type: object
      required:
      - loyaltyPrograms
      properties:
        loyaltyPrograms:
          type: array
          items:
            $ref: '#/components/schemas/AirLoyaltyProgram'
        applicableLoyalties:
          type: array
          items:
            $ref: '#/components/schemas/LoyaltyInfo'
    AirAttributesRequest:
      title: AirAttributesRequest
      type: object
      description: Describes the search response for which the attributes are being
        requested.
      required:
      - searchId
      properties:
        searchId:
          type: string
          description: The unique ID for the search response for which the attributes
            are being requested.
          example: ChBjZDg3ZjRjZmRmMTFmMWFiEhBjZDg3Z
    AirAutocompleteResponse:
      title: AirAutocompleteResponse
      type: object
      required:
      - airports
      properties:
        airports:
          type: array
          description: List of Airports matching the query
          items:
            $ref: '#/components/schemas/Airport'
    AirBookTravelerInfo:
      type: object
      required:
      - travelerDetails
      - seats
      - baggages
      properties:
        travelerDetails:
          $ref: '#/components/schemas/TravelerBookingInfo'
        seats:
          type: array
          items:
            $ref: '#/components/schemas/SelectedSeat'
        baggages:
          type: array
          items:
            $ref: '#/components/schemas/SelectedBaggage'
        ancillaries:
          type: array
          description: The ancillary options selected during checkout.
          items:
            $ref: '#/components/schemas/SelectedAncillary'
        shareContactInfo:
          type: boolean
          description: "Flag to support IATA regulation 830d that requires user's\
            \ consent to share email \nand phone with airline. If set to true, airlines\
            \ will have access to\ntraveler's email and phone to directly communicate\
            \ events like operational disruption.\n"
          example: false
        specialServiceRequests:
          type: array
          description: List of all Special Service Requests (SSR) for the traveler.
          items:
            $ref: '#/components/schemas/SpecialServiceRequestInfo'
    AirCancelPnrRequest:
      type: object
      required:
      - pnrId
      - optionId
      properties:
        pnrId:
          type: string
          description: The spotnana assigned unique PNR id of the booking.
          example: '1234567819'
        optionId:
          type: string
          description: 'Cancellation option id chosen by the traveler prior to cancelling
            the booking. The

            trip->cancellation-details API returns a bunch of options like whether
            the traveler

            wants to keep the ticket as unused or get the refunds with applicable
            penalties.

            '
          example: OPTION123
        cancellationOverride:
          description: Cancellation info override details, like cancellation penalty,
            waiver code, etc.
          $ref: '#/components/schemas/CancellationOverride'
        customFieldV3Responses:
          type: array
          description: Custom field responses for the booking.
          items:
            $ref: '#/components/schemas/CustomFieldV3Response'
    AirCancelPnrResponse:
      type: object
      properties:
        sourcePnrId:
          type: string
          description: Source pnr id of the cancellation.
          example: ABCDEF
        status:
          $ref: '#/components/schemas/CancellationStatus'
    AirCheckoutRequest:
      title: AirCheckoutRequest
      type: object
      description: Describes the itinerary selected by the user for checkout.
      required:
      - searchId
      - itineraryId
      properties:
        searchId:
          type: string
          description: Search id returned in the corresponding air search API response.
          example: ChBjZDg3ZjRjZmRmMTFm
        itineraryId:
          type: string
          description: Value of the itineraryId for the itinerary selected by the
            user in the corresponding air search API response.
          example: IthBjZDg3ZjRjZmRmMTFm
    AirCheckoutResponse:
      title: AirCheckoutResponse
      type: object
      description: Describes the air ancillaries and checkout options available for
        selection for the chosen itinerary.
      required:
      - checkoutResponseId
      properties:
        checkoutResponseId:
          type: string
          description: 'The unique ID which identifies this checkout response. This
            value must passed on in the subsequent booking API calls.

            '
          example: ChBjZDg3ZjRjZmRmMTFmMWFiEhBjZDg3Z
        baggageInfo:
          $ref: '#/components/schemas/BaggageSelectionInfo'
        ancillaries:
          type: array
          description: List of ancillaries available for purchase.
          items:
            $ref: '#/components/schemas/AncillarySelectionInfo'
        mandatoryCheckoutParams:
          type: array
          items:
            $ref: '#/components/schemas/MandatoryParamForCheckout'
        airlineCardFees:
          type: array
          description: List of all the supported card options. If this list is empty,
            all cards are supported.
          items:
            $ref: '#/components/schemas/AirlineCardFee'
    AirCreatePnrRequest:
      type: object
      properties:
        bookingId:
          type: string
          description: The booking ID for which the PNR is being created. This is
            returned in the revalidate itinerary API response.
          example: booking-id
        initiateBookingId:
          type: string
          description: Initiate booking id is returned in response to air-initiate-booking
            API call. This id is required if in case the booking id is not available
            due to third-party redirection.
          example: eCfghty567jkHG56DFgh
        preBookAnswers:
          deprecated: true
          $ref: '#/components/schemas/PreBookAnswers'
        preSearchAnswers:
          $ref: '#/components/schemas/PreSearchAnswers'
        postPaymentVerificationInfo:
          $ref: '#/components/schemas/PostPaymentVerificationInfo'
        isPreAuthApprovalRequired:
          type: boolean
          description: Flag to check if the pre-authorization approval is enabled.
          example: false
        customFieldV3Responses:
          type: array
          description: Custom field responses for the booking.
          items:
            $ref: '#/components/schemas/CustomFieldV3Response'
    AirCreatePnrResponse:
      type: object
      properties:
        pnrId:
          type: string
          description: Spotnana PNR ID.
        sourcePnrId:
          type: string
          description: Source PNR ID
        pnrStatus:
          type: string
          description: PNR status (for example success, approval pending)
          enum:
          - SUCCESS
          - APPROVAL_PENDING
          - CC_VERIFICATION_REQUIRED
          - PAYMENT_PENDING
          - CONFIRMATION_PENDING
          - ERROR
        createPnrApplicationInfo:
          type: object
          description: Create PNR application info, will return warning or error if
            present
          properties:
            applicationWarning:
              type: array
              items:
                $ref: '#/components/schemas/ApplicationWarnings'
    AirEditPnrRequest:
      type: object
      title: AirEditPnrRequest
      required:
      - pnrId
      - travelerInfo
      example:
        pnrId: '1234567890'
        seatMapResponseId: 8bc4ec0e1839aabc
        pnrUpdateTypes:
        - OTHER_SERVICE_INFO
        travelerInfo:
        - traveler:
            travelerId: '1'
            travelerInfo:
              userOrgId:
                userId:
                  id: 8ec7ef81-e314-4b2a-ad62-9157262aeb11
                organizationId:
                  id: 5ec7ef81-e314-4b2a-ad62-9157262aeb11
                organizationAgencyId:
                  id: 2ec7ef81-e314-4b2a-ad62-9157262aeb11
                tmcInfo:
                  id:
                    id: 2ec7ef81-e314-4b2a-ad62-9157262aeb11
                  primaryServiceProviderTmc:
                    tmcId:
                      id: 2ec7ef81-e314-4b2a-ad62-9157262aeb11
                  secondaryServiceProviderTmcs: []
                tmcBasicInfo:
                  contractingTmc:
                    id:
                      id: 2ec7ef81-e314-4b2a-ad62-9157262aeb11
                  bookingTmc:
                    id:
                      id: 2ec7ef81-e314-4b2a-ad62-9157262aeb11
          seats:
          - flightIndex: 1
            seatNumber: 10A
          specialServiceRequests:
          - flightIndex: 1
            code: SPML
            info: Special meal without egg.
          updateTypes:
          - SPECIAL_SERVICE_REQUEST
          - SEAT
      properties:
        pnrRemarks:
          description: Note remarks to be added for pnr at time of pnr update
          type: array
          items:
            $ref: '#/components/schemas/PnrNoteRemark'
        pnrId:
          description: PNR ID created by Spotnana for the booking.
          type: string
          example: '**********'
        travelerInfo:
          description: Traveler information.
          type: array
          items:
            $ref: '#/components/schemas/TravelerUpdateInfo'
          example: []
        seatMapResponseId:
          description: 'The `seatMapResponseId` generated using the [Get flight seat
            map](#operation/airSeatMap) API.

            This field is only required if the seat selection is being changed for
            the traveler.

            '
          type: string
          example: 8bc4ec0e1839aabc
        preSearchAnswers:
          type: object
          description: Contains a list of custom fields or pre search answers.
          properties:
            answers:
              type: array
              items:
                $ref: '#/components/schemas/EntityAnswer'
        pnrUpdateTypes:
          type: array
          items:
            description: Contains a list of updates to be made in the PNR.
            type: string
            enum:
            - OTHER_SERVICE_INFO
        otherServiceInformationItems:
          type: array
          description: The list of all Other Service Information (OSI) to be included
            in the PNR.
          items:
            $ref: '#/components/schemas/OtherServiceInformation'
        bookingPaymentDetails:
          $ref: '#/components/schemas/BookingPaymentDetails'
        postPaymentVerificationInfo:
          $ref: '#/components/schemas/PostPaymentVerificationInfo'
        waiveOffInfo:
          $ref: '#/components/schemas/WaiveOffInfo'
    AirEditPnrResponse:
      type: object
      title: AirEditPnrResponse
      description: Air edit pnr response.
      properties:
        paymentVerificationInfo:
          $ref: '#/components/schemas/PaymentVerificationInfo'
    AirExchangeAvailableOptions:
      type: object
      title: AirExchangeAvailableOptions
      description: Contains the information of the options that are allowed to exchange.
      properties:
        travelDate:
          type: boolean
          description: If true it means Airline allows to change the date.
        route:
          type: boolean
          description: If true it means Airline allows to change the route.
        addLeg:
          type: boolean
          description: If true it means Airline allows to add new leg to the existing
            itinerary.
        removeLeg:
          type: boolean
          description: If true it means Airline allows to remove leg from the existing
            itinerary.
        modifyPartialItinerary:
          type: boolean
          description: If true it means Airline allows to modify partial itinerary.
        sameEndpointRestriction:
          type: boolean
          description: If true it means Airline allows to change the endpoint across
            legs for the return itinerary.
    AirExchangeState:
      type: string
      title: AirExchangeState
      description: 'Current exchange state of the ticket:

        - EXCHANGEABLE_BY_OBT: Exchange possible via API.

        - EXCHANGEABLE_BY_SUPPORT: Exchange possible via agent support.

        '
      example: EXCHANGEABLE_BY_OBT
      enum:
      - EXCHANGEABLE_BY_OBT
      - EXCHANGEABLE_BY_SUPPORT
    AirInitiateBookingRequest:
      type: object
      description: Travel, Traveler and Payment source details to initiate a booking
      properties:
        initiateBookingWorkflowIds:
          $ref: '#/components/schemas/InitiateBookingWorkflowIds'
        travelers:
          type: array
          items:
            $ref: '#/components/schemas/AirBookTravelerInfo'
        bookingCharges:
          type: array
          items:
            $ref: '#/components/schemas/BookingCharge'
        bookingContact:
          description: Booking contact for the itinerary. Airlines send all update
            and change information to booking contact as well.
          $ref: '#/components/schemas/BookingContact'
        useExistingBooking:
          description: Use existing booking if it exists to initiate
          type: boolean
    AirInitiateBookingResponse:
      type: object
      properties:
        initiateBookingResponseId:
          type: string
          description: Initiated booking's reference id
          example: CuwDQ2hCa09HVTJZemsyWVRFNFlUUm
        paymentSourcePrePaymentInformation:
          type: array
          items:
            $ref: '#/components/schemas/PaymentSourcePrePaymentInformation'
    AirIntermediateRevalidateItineraryRequest:
      type: object
      required:
      - searchId
      - itineraryId
      properties:
        searchId:
          type: string
          description: The unique `searchId` corresponding to the selected itinerary.
        itineraryId:
          type: string
          description: The selected `itineraryId` which you would like to validate.
    AirItin:
      type: object
      description: Describes an itinerary containing legs and flights.
      required:
      - itineraryId
      - legs
      - travelers
      - fareinfo
      - fareCategory
      - ticketType
      properties:
        itineraryId:
          type: string
          description: The unique ID for this itinerary.
          example: kjdUjak8hX
        legs:
          type: array
          items:
            $ref: '#/components/schemas/AirLegInfo'
            description: List of legs present in the itinerary.
        travelers:
          type: array
          items:
            $ref: '#/components/schemas/TravelerDetail'
            description: List of passenger details in the itinerary.
        fareInfo:
          description: Describes the fare amount and taxes.
          $ref: '#/components/schemas/FareInfo'
        ticketType:
          description: Indicates whether this itinerary will be a single or multi
            ticket
          $ref: '#/components/schemas/TicketType'
        policyInfos:
          description: The policy details evaluated on the itinerary.
          $ref: '#/components/schemas/PnrPolicyInfo'
        posInfo:
          type: array
          description: The point of sales which offer the itinerary.
          items:
            $ref: '#/components/schemas/POSInfo'
        publicFare:
          description: The public fare attribute in case private fare is applied for
            any pax
          $ref: '#/components/schemas/FareAmount'
        allowedFopRules:
          type: array
          description: The payment method types applicable to the itinerary.
          items:
            $ref: '#/components/schemas/AllowedFopRule'
        fareAttributes:
          description: The fare attributes applicable to the itinerary.
          $ref: '#/components/schemas/FareAttributes'
    AirItineraryIdWrapper:
      type: object
      title: AirItineraryIdWrapper
      properties:
        airItineraryId:
          $ref: '#/components/schemas/AirItineraryIdentifier'
    AirItineraryIdentifier:
      description: An air itinerary ID wrapper.
      type: object
      required:
      - searchId
      - itineraryId
      properties:
        searchId:
          description: The search ID associated with the itinerary, returned in the
            air search response.
          type: string
          example: Xjdks78X
        itineraryId:
          description: The itinerary ID of the itinerary selected by the user, for
            which the seat map is requested.
          type: string
          example: ijdks78X
    AirLegInfo:
      type: object
      description: 'Describes a leg in the itinerary. Each leg may have multiple flight
        segments within it, which combined together

        take the traveler from the leg''s origin to its destination.

        '
      required:
      - id
      - flights
      - travelerInfos
      - totalLegFare
      - fareCategory
      properties:
        id:
          type: string
          description: The unique ID for this leg.
          example: leg_0
        flights:
          description: Details of the list of flights present in the leg.
          type: array
          items:
            $ref: '#/components/schemas/FlightDetail'
        travelerInfos:
          description: Details of passenger(s) information for this leg.
          type: array
          items:
            $ref: '#/components/schemas/TravelerLegInfo'
        totalLegFare:
          description: Total cumulative fare for this leg summed across all passengers
          $ref: '#/components/schemas/FareAmount'
        fareCategory:
          description: Fare category of this leg. It describes the cabin class for
            this flight.
          $ref: '#/components/schemas/FareCategory'
        brandName:
          type: string
          description: The brand name advertised by the airline.
          example: Economy Flex
        brandCode:
          type: string
          description: The brand code assigned by the airline corresponding brandName.
          example: AADOM-MAIN
        validatingAirline:
          type: string
          description: The airline code for the airline which is validating this booking
            and orchestrating the payment.
          example: AA
        rateType:
          description: Rate or fare type for this leg (for example "public" or "corporate").
          $ref: '#/components/schemas/RateType'
        vendorProgramType:
          $ref: '#/components/schemas/VendorProgramType'
    AirLoyaltyProgram:
      type: object
      required:
      - programName
      - airlineCode
      - airlineName
      - loyaltyType
      properties:
        programName:
          description: Name of the loyalty program.
          type: string
          example: AA Advantage
        airlineCode:
          description: IATA code of the airline that hosts the loyalty program.
          type: string
          example: AA
        airlineName:
          description: Name of the airline that hosts the loyalty program.
          type: string
          example: American Airlines
        loyaltyType:
          description: Type of the loyalty program. This indicates how the loyalty
            program is associated with the airline mentioned in the API request.
          type: string
          enum:
          - OWN
          - PARTNER
          - ALLIANCE
          example: OWN
    AirMetadata:
      type: object
      description: Metadata associated with the air search response.
      properties:
        airlineInfo:
          type: array
          description: Metadata for all airlines available in the specific route.
          items:
            $ref: '#/components/schemas/AirlineInfo'
        applicableAlliances:
          type: array
          description: Alliances which are applicable based on the airlines info
          items:
            $ref: '#/components/schemas/Alliance'
        legBylegPricing:
          type: boolean
          description: 'Flag indicating whether itineraries are priced on a leg-by-leg
            basis (instead of the overall itinerary

            pricing, which is the default).

            '
        enableFareCategories:
          type: array
          description: Fare categories that need to be used for the given air search
          items:
            $ref: '#/components/schemas/FareCategory'
    AirModifyBookingRequest:
      type: object
      example:
        bookingId: ChBlMTFmOTVkZTcwZmZjMmI2EhAwZjVkNDhhNGJjNWExZTMw
        tripData:
          tripId:
            id: '9527251668'
      required:
      - bookingId
      - tripData
      properties:
        bookingId:
          type: string
          description: Booking id returned in successful response of air-revalidate-itin
            api call in the booking flow.
          example: ChBlMTFmOTVkZTcwZmZjMmI2EhAwZjVkNDhhNGJjNWExZTMw
        tripData:
          $ref: '#/components/schemas/TripData'
        skipTicketing:
          type: boolean
          description: If this is true, the PNR is not set up for ticketing.
          example: true
        postPaymentVerificationInfo:
          $ref: '#/components/schemas/PostPaymentVerificationInfo'
        customFieldV3Responses:
          type: array
          description: Custom field responses for the booking.
          items:
            $ref: '#/components/schemas/CustomFieldV3Response'
        waiveOffInfo:
          $ref: '#/components/schemas/WaiveOffInfo'
    AirModifyBookingResponse:
      type: object
      example:
        pnrNumber: AXKJDF
        pnrStatus: CONFIRMED
        pnrId: '**********'
      properties:
        pnrNumber:
          type: string
          title: Source PNR ID
          example: ABXDFZ
        pnrStatus:
          $ref: '#/components/schemas/PnrStatus'
        pnrId:
          type: string
          description: Spotnana PNR ID
          example: '**********'
        paymentVerificationInfo:
          $ref: '#/components/schemas/PaymentVerificationInfo'
    AirModifySearchRequest:
      type: object
      required:
      - pnrId
      - legs
      example:
        pnrId: '**********'
        legs:
        - index: 0
          remove: true
        - origin: SFO
          destination: SEA
          date: 2022-01-21T17:00
        - origin: SEA
          destination: ANC
          date: 2022-01-22T17:00
      properties:
        pnrId:
          description: The unique spotnana pnr id which needs to be exchanged.
          type: string
          example: '**********'
        legs:
          type: array
          items:
            $ref: '#/components/schemas/Leg'
          description: The list of all legs (to keep as same, to remove and to be
            added). In the example shared, if there is a trip from SFO to ANC with
            one Leg containing a direct flight from mentioned source to destination,
            and traveller needs to add a visit to Seattle in between changing his
            trip to now contain two legs SFO->SEA and SEA->ANC, then we would remove
            the first existing leg(indexed 0) SFO->ANC and add two new legs SFO->SEA
            and SEA->ANC.
          example:
          - index: 0
            remove: true
          - source: SFO
            destination: SEA
            date: 2022-01-21T17:00
          - source: SEA
            destination: ANC
            date: 2022-01-22T17:00
        filters:
          type: array
          items:
            $ref: '#/components/schemas/Filter'
        sortOptions:
          type: array
          items:
            $ref: '#/components/schemas/SortOption'
        legSearchParams:
          $ref: '#/components/schemas/LegSearchParams'
        preModifySearchAnswers:
          type: array
          items:
            $ref: '#/components/schemas/PreSearchAnswers'
          title: optional Q and A for business management and audit
    AirPnrExchangeDetailsResponse:
      type: object
      title: AirPnrExchangeDetailsResponse
      description: Exchange details for the booking.
      required:
      - pnrId
      properties:
        pnrId:
          type: string
          description: PNR ID for the booking.
          example: '1234567890'
        ticketToExchangeInfos:
          type: array
          description: List of tickets and their exchange info.
          items:
            $ref: '#/components/schemas/TicketToExchangeInfo'
    AirPref:
      type: object
      title: AirPref
      description: Travel preferences related to air travel.
      properties:
        airlinePrefs:
          type: array
          items:
            $ref: '#/components/schemas/AirlinePref'
        alliancePref:
          $ref: '#/components/schemas/AlliancePref'
        farePref:
          $ref: '#/components/schemas/FarePref'
        homeAirport:
          type: string
          example: NEW YORK
        mealPref:
          $ref: '#/components/schemas/MealPref'
        numStopPref:
          $ref: '#/components/schemas/NumStopsPref'
        seatAmenityPref:
          $ref: '#/components/schemas/SeatAmenityPref'
        seatLocationPrefs:
          type: array
          items:
            $ref: '#/components/schemas/SeatLocationPref'
        preferredAirports:
          type: array
          description: A list of user preferred airports.
          items:
            $ref: '#/components/schemas/PreferredAirport'
    AirRequestTravelerInfo:
      title: AirRequestTravelerInfo
      description: 'The traveler identifiers. These can be either the Spotnana user
        IDs of the travelers or information regarding

        the adhoc travelers.

        '
      type: object
      oneOf:
      - $ref: '#/components/schemas/UserIdWrapper'
      - $ref: '#/components/schemas/AdhocTravelerInfoWrapper'
    AirRevalidateItineraryRequest:
      type: object
      properties:
        workflowIds:
          $ref: '#/components/schemas/WorkflowIds'
        tripId:
          description: The trip ID with which this booking will be associated.
          $ref: '#/components/schemas/TripId'
        travelers:
          type: array
          items:
            $ref: '#/components/schemas/AirBookTravelerInfo'
        bookingCharges:
          type: array
          items:
            $ref: '#/components/schemas/BookingCharge'
        promotionCode:
          description: Promo code applied to the order (optional)
          $ref: '#/components/schemas/PromotionCode'
    AirRevalidateItineraryResponse:
      type: object
      properties:
        bookingId:
          type: string
          description: The booking ID for the booking.
          example: bookingid
        fareBreakDown:
          $ref: '#/components/schemas/BookingFareBreakdown'
        legPrices:
          type: array
          items:
            $ref: '#/components/schemas/LegPrice'
        perTravelerPrices:
          type: array
          items:
            $ref: '#/components/schemas/PerTravelerPrice'
        policyDetails:
          $ref: '#/components/schemas/PnrPolicyInfo'
    AirSearchRequest:
      title: AirSearchRequest
      type: object
      description: 'Provides information about where and when the given travelers
        want to travel. Also provides support for

        filtering and sorting.

        '
      required:
      - travelers
      - legs
      properties:
        travelers:
          description: Information about each of the travelers in the search request.
            This information is required.
          type: array
          items:
            $ref: '#/components/schemas/TravelerSearchInfo'
        legs:
          description: The list of all legs for which search results need to be returned.
          type: array
          items:
            $ref: '#/components/schemas/SearchLeg'
        filters:
          description: The list of filters to be applied in the search request.
          type: array
          items:
            $ref: '#/components/schemas/Filter'
        sortOptions:
          description: The sort options to be used for ordering the itineraries.
          type: array
          items:
            $ref: '#/components/schemas/SortOption'
        legSearchParams:
          $ref: '#/components/schemas/LegSearchParams'
        corporateInfo:
          $ref: '#/components/schemas/CorporateInfo'
    AirSearchResponse:
      title: AirSearchResponse
      type: object
      description: A list of itineraries with detailed flight information and prices
        for each.
      required:
      - searchId
      - itineraryDetails
      properties:
        searchId:
          type: string
          description: The unique ID for this search response. This value must be
            passed on in the subsequent booking API calls.
          example: ChBjZDg3ZjRjZmRmMTFmMWFiEhBjZDg3Z
        itineraryDetails:
          description: The list of itineraries in the search response.
          $ref: '#/components/schemas/ItineraryInfo'
        paginationParams:
          $ref: '#/components/schemas/PaginationResponseParams'
        metadata:
          $ref: '#/components/schemas/AirMetadata'
    AirSeatMapRequest:
      title: AirSeatMapRequest
      type: object
      description: Describes the traveler and the itinerary for which the seat map
        is to be returned.
      required:
      - itinerary
      - travelerInfos
      properties:
        itinerary:
          $ref: '#/components/schemas/SeatMapItinerary'
        travelerInfos:
          description: Information about the travelers on the itinerary.
          type: array
          items:
            $ref: '#/components/schemas/TravelerSeatInfo'
        indexes:
          description: Indexes for the seat map request must be provided only when
            specific legs or flights need to be selected. If not provided, all legs
            and flights will be considered.
          type: array
          items:
            $ref: '#/components/schemas/SeatIndexes'
    AirSeatMapResponse:
      type: object
      description: The overall seat map response for the air seat map request.
      properties:
        seatMapResponseId:
          description: Unique identifier for this seat map response, which would need
            to be passed on in subsequent booking APIs.
          type: string
          example: seat1234
        travelerSeatMaps:
          description: Seat maps for a flight for each traveler type.
          type: array
          items:
            $ref: '#/components/schemas/TravelerSeatMap'
        seatMaps:
          description: The list of seat maps available in the response.
          type: array
          items:
            $ref: '#/components/schemas/FlightSeatMap'
    AirSelectedItineraryRequest:
      title: AirSelectedItineraryRequest
      type: object
      description: Describes the itinerary selected by the user.
      required:
      - searchId
      - itineraryId
      properties:
        searchId:
          type: string
          description: The searchId returned in the corresponding air search API response.
          example: ChBjZDg3ZjRjZmRmMTFm
        itineraryId:
          type: string
          description: The itineraryId of the itinerary selected by the user in the
            corresponding air search API response.
          example: IthBjZDg3ZjRjZmRmMTFm
    AirSelectedItineraryResponse:
      title: AirSelectedItineraryResponse
      type: object
      description: Describes the itinerary selected by the user.
      required:
      - itinerary
      properties:
        itinerary:
          description: The itinerary selected by the user.
          $ref: '#/components/schemas/AirItin'
    AirTravelerInfo:
      type: object
      title: AirTravelerInfo
      description: Traveler details
      properties:
        travelerId:
          type: string
          description: Unique ID for traveler
          example: '1'
        travelerInfo:
          $ref: '#/components/schemas/Traveler'
          description: Traveler Information
        adhocTravelerInfo:
          $ref: '#/components/schemas/TravelerInfoResponse'
          description: Adhoc Traveler info for this traveler
    AircraftAmenity:
      type: object
      title: AircraftAmenity
      description: Aircraft Amenity properties
      properties:
        displayText:
          type: string
          example: 789 (widebody)
        aircraftType:
          type: string
          example: widebody
        aircraftModel:
          type: string
          example: '787'
    AircraftAmenityWrapper:
      type: object
      title: AircraftAmenityWrapper
      properties:
        aircraftAmenity:
          $ref: '#/components/schemas/AircraftAmenity'
    AirlineCardFee:
      type: object
      description: Supported card options and associated fee with those cards
      required:
      - type
      - company
      properties:
        type:
          type: string
          enum:
          - CREDIT
          - DEBIT
          example: CREDIT
        company:
          $ref: '#/components/schemas/CardCompany'
        fees:
          $ref: '#/components/schemas/Money'
    AirlineInfo:
      title: AirlineInfo
      type: object
      required:
      - airlineCode
      - airlineName
      properties:
        airlineCode:
          type: string
          description: IATA code for airline.
          example: AA
        airlineName:
          type: string
          description: Airline name
          example: American Airlines
    AirlineInfoComplete:
      type: object
      title: AirlineInfoComplete
      required:
      - airlineCode
      - airlineName
      - country
      - twoLetterCountryCode
      - isActive
      - loyaltyProgramName
      - loyaltyProgramCode
      - airlineTicketPrefix
      properties:
        airlineCode:
          type: string
          description: Airline code
          example: AA
        airlineName:
          type: string
          description: Airline name
          example: American Airlines
        country:
          type: string
          description: Airline country
          example: United States
        twoLetterCountryCode:
          type: string
          description: Two letter country code
          example: US
        isActive:
          type: boolean
          description: Airline active status
          example: true
        loyaltyProgramName:
          type: string
          description: Airline loyalty program name
          example: AA Advantage
        loyaltyProgramCode:
          type: string
          description: Airline loyalty program code
          example: AA Adv
        airlineTicketPrefix:
          type: string
          description: Airline ticket prefix
          example: '001'
    AirlineInfoResponse:
      type: object
      title: AirlineInfoResponse
      required:
      - airlineInfo
      properties:
        airlineInfo:
          type: array
          items:
            $ref: '#/components/schemas/AirlineInfoComplete'
    AirlinePref:
      type: object
      title: AirlinePref
      description: Airline preferences.
      properties:
        airlines:
          type: array
          items:
            type: string
            example: Indigo
        flightType:
          type: string
          enum:
          - UNKNOWN_FLIGHT_TYPE
          - DOMESTIC
          - INTERNATIONAL
          - ALL
          example: DOMESTIC
    AirlinePrefs:
      type: object
      properties:
        legIndex:
          type: integer
          format: int32
        airline:
          type: array
          items:
            type: string
    AirlinePrefsFilter:
      type: object
      properties:
        airlinePrefs:
          type: array
          items:
            $ref: '#/components/schemas/AirlinePrefs'
    Airport:
      title: Airport
      description: Airport Details
      type: object
      required:
      - airportCode
      - airportName
      - cityCode
      properties:
        airportCode:
          type: string
          description: Unique code for the Airport
          example: LHR
        airportName:
          type: string
          description: Full Name of the Airport
          example: Heathrow Airport
        cityCode:
          type: string
          description: City Code for the location
          example: LON
        location:
          $ref: '#/components/schemas/Location'
    AirportInfo:
      title: AirportInfo
      description: Detailed information about the airport.
      type: object
      properties:
        airportCode:
          type: string
          description: 3-letter code of the airport.
          example: WRA
        airportName:
          type: string
          description: Full name of the airport.
          example: Warder Airport
        cityName:
          type: string
          description: Name of the city in which the airport is located (or is nearest
            to).
          example: Werder
        countryName:
          type: string
          description: Name of the country in which the airport is located.
          example: Ethiopia
        countryCode:
          type: string
          description: 2-letter IATA country code associated with the airport.
          example: ET
        zoneName:
          type: string
          description: Name of the time zone associated with the airport.
          example: Africa/Addis_Ababa
        stateCode:
          type: string
          description: 2-letter IATA code for the state in which the airport is located.
          example: CA
    AirportLocation:
      type: object
      properties:
        city:
          type: string
          description: The code of a city used to identify a set of airports associated
            with it. For example, NYC or LON.
          example: NYC
        airport:
          type: string
          description: The airport code identifying a specific airport. For example,
            JFK or EWR.
          example: JFK
          pattern: ^[A-Z]{3}$
        multiAirports:
          description: List of airports to search for this location.
          $ref: '#/components/schemas/MultiAirports'
    Alliance:
      type: string
      enum:
      - UNKNOWN_ALLIANCE
      - STAR_ALLIANCE
      - ONEWORLD
      - SKYTEAM
      - VANILLA_ALLIANCE
      - U_FLY_ALLIANCE
      - VALUE_ALLIANCE
      default: UNKNOWN_ALLIANCE
      example: STAR_ALLIANCE
    AllianceFilter:
      type: object
      properties:
        alliances:
          type: array
          items:
            $ref: '#/components/schemas/Alliance'
        airlines:
          type: array
          items:
            type: string
          description: 2-letter IATA codes for the preferred airlines.
    AlliancePref:
      type: object
      title: AlliancePref
      description: Alliance preferences.
      required:
      - alliances
      properties:
        alliances:
          type: array
          items:
            $ref: '#/components/schemas/Alliance'
    AllowedFopRule:
      type: object
      description: The payment method applicable for the itinerary
      properties:
        paymentItems:
          type: array
          items:
            $ref: '#/components/schemas/PaymentItem'
            description: List of payment items eligible for associated payment rules.
        amount:
          $ref: '#/components/schemas/Money'
          description: Total amount for specified fare components
        paymentRules:
          type: array
          items:
            $ref: '#/components/schemas/PaymentRule'
            description: List of payment rules applicable for the itinerary
    AmadeusCheckoutVerificationInfo:
      type: object
      required:
      - paymentActionTokens
      properties:
        paymentActionTokens:
          type: array
          items:
            $ref: '#/components/schemas/AmadeusPaymentActionToken'
        paymentRequestId:
          type: string
          deprecated: true
          description: Initial payment id generated by checkout SDK to initiate payment
        actionToken:
          type: string
          deprecated: true
          description: Token returned by checkout SDK to create/validate FOP in PSS
            order
        confirmPayment:
          type: boolean
          description: Flag which indicates that the payment confirmation is now required
            (after 3ds or redirection)
          deprecated: true
    AmadeusCheckoutVerificationInfoWrapper:
      type: object
      title: AmadeusCheckoutVerificationInfoWrapper
      description: Wrapper for amadeus checkout verification info object
      properties:
        amadeusCheckoutVerificationInfo:
          $ref: '#/components/schemas/AmadeusCheckoutVerificationInfo'
    AmadeusPaymentActionToken:
      type: object
      required:
      - paymentRequestId
      - actionToken
      properties:
        paymentRequestId:
          type: string
          description: Initial payment id generated by checkout SDK to initiate payment
        actionToken:
          type: string
          description: Token returned by checkout SDK to create/validate FOP in PSS
            order
    AncillaryOption:
      type: object
      title: AncillaryOption
      description: Details of a single ancillary option
      required:
      - status
      - fare
      - flightIds
      - ancillaryId
      properties:
        travelerId:
          description: Traveler ID to which this ancillary applies.
          $ref: '#/components/schemas/UserId'
        status:
          type: string
          description: Ancillary status (purchased, eligible for purchase, not applicable,
            etc.).
          enum:
          - ELIGIBLE
          - NOT_APPLICABLE
          - PURCHASED
          example: ELIGIBLE
        fare:
          description: The fare for this ancillary.
          $ref: '#/components/schemas/Money'
        flightIds:
          type: array
          description: ID of the flights on which this ancillary applies.
          minItems: 1
          items:
            type: string
            example: flight_0
        ancillaryId:
          type: string
          description: Unique ID for purchasing this ancillary.
          example: ancillary_0
    AncillarySelectionInfo:
      type: object
      title: AncillarySelectionInfo
      description: Ancillaries available for the user to buy on the flight checkout
        page.
      required:
      - type
      - displayName
      - description
      - ancillaryOptions
      properties:
        type:
          $ref: '#/components/schemas/AncillaryType'
        displayName:
          type: string
          description: Display name for this ancillary type
          example: Early Bird
        description:
          type: string
          description: Description for this ancillary type
          example: "Purchasing early bird provides the customer with a higher boarding\
            \ priority compared to WannaGetAway/Anytime customers that don\u2019t\
            \ have a special rapid rewards status irrespective of the time they check\
            \ in."
        ancillaryOptions:
          type: array
          description: Ancillary options for this ancillary type
          minItems: 1
          items:
            $ref: '#/components/schemas/AncillaryOption'
    AncillaryType:
      type: string
      title: AncillaryType
      description: Type of ancillary
      enum:
      - EARLY_BIRD
      - WIFI
      - CARBON_OFFSET
      example: EARLY_BIRD
    AnswerPair:
      type: object
      properties:
        item:
          type: string
          description: The option selected from the list of available choices.
        value:
          type: string
          description: The additional input provided (by the user) while selecting
            one of the options.
        description:
          type: string
          description: Description of the selected option.
    ApplicationWarnings:
      type: object
      description: Warnings associated with the booking.
      properties:
        warningType:
          type: string
          description: Warning Type
          enum:
          - LOYALTY_NAME_MISMATCH
        description:
          type: string
          description: Warning description
          example: FREQUENT TRAVELER NUMBER DOES NOT EXIST FOR THIS AIRLINE
    AssessmentType:
      type: string
      enum:
      - NEUTRAL
      - BENEFIT
      - RESTRICTION
      - FEE
      example: BENEFIT
    AvailableSeats:
      description: Represents the available seats in a row.
      type: object
      properties:
        columnNumber:
          type: array
          items:
            type: string
    BagFareInfo:
      description: Information about the fare for (cost of) a baggage option.
      type: object
      properties:
        totalFare:
          description: The fare amount (cost of) associated with the baggage option.
          $ref: '#/components/schemas/FareAmount'
    BagFees:
      type: object
      description: This contains price info for the baggage
      properties:
        fee:
          $ref: '#/components/schemas/Money'
        applicability:
          $ref: '#/components/schemas/BagPolicyApplicability'
    BagPolicyApplicability:
      type: string
      description: This represents that the info is applicable for per bag/combined
        bags
      enum:
      - EACH
      - TOTAL
    BagWeightLimit:
      type: object
      description: This contains weight limit details of bag
      properties:
        weightLimit:
          type: array
          description: Weight limit in different units
          items:
            $ref: '#/components/schemas/WeightLimit'
        applicability:
          $ref: '#/components/schemas/BagPolicyApplicability'
    BaggageFilter:
      type: object
      properties:
        freeCheckedBag:
          type: boolean
          description: 'If true, only itineraries with free checked bag are returned.
            Otherwise, no

            preference.

            '
        freeCarryOn:
          type: boolean
          description: 'If true, only itineraries with free carry on bag are returned.
            Otherwise, no

            preference.

            '
    BaggageInfo:
      type: object
      description: Information about the baggage policy.
      properties:
        description:
          type: string
          description: Baggage policy text
          example: 1 checked bag, 33 lbs
        count:
          type: integer
          description: Count of bags
          example: 3
        sizeLimitInfo:
          type: array
          description: Size of bag in cm
          items:
            $ref: '#/components/schemas/SizeInfo'
        weightLimitInfo:
          type: array
          description: Array of bag weight limits
          items:
            $ref: '#/components/schemas/BagWeightLimit'
        fee:
          type: array
          description: Array of bag weight fees
          items:
            $ref: '#/components/schemas/BagFees'
    BaggageOptionGroup:
      title: BaggageOptionGroup
      type: object
      description: Describes a group of baggage options from which the user can select
        one or more.
      oneOf:
      - $ref: '#/components/schemas/SingleSelectOptionGroup'
      - $ref: '#/components/schemas/MultiSelectOptionGroup'
    BaggagePolicy:
      type: object
      description: Baggage policy for this traveler.
      required:
      - checkedIn
      - carryOn
      properties:
        checkedIn:
          type: array
          description: Policies for checked-in baggage
          items:
            $ref: '#/components/schemas/BaggageInfo'
        carryOn:
          type: array
          description: Policies for carry-on baggage
          items:
            $ref: '#/components/schemas/BaggageInfo'
    BaggageSelectionGroup:
      title: BaggageSelectionGroup
      type: object
      description: "Describes the baggage selection group, which has information around\
        \ which traveler \nor air-leg the given baggage selection options are applicable\
        \ to.\n"
      properties:
        travelerApplicability:
          $ref: '#/components/schemas/TravelerApplicability'
        legApplicability:
          $ref: '#/components/schemas/LegApplicability'
        travelerBaggageOptions:
          type: array
          items:
            $ref: '#/components/schemas/TravelerBaggageOptions'
    BaggageSelectionInfo:
      title: BaggageSelectionInfo
      type: object
      description: Describes the baggage selection options available for the user
        to buy on the flight checkout page.
      properties:
        baggageSelectionGroups:
          type: array
          items:
            $ref: '#/components/schemas/BaggageSelectionGroup'
    BeverageAmenity:
      type: object
      title: BeverageAmenity
      description: Beverage amenity option properties.
      properties:
        displayText:
          type: string
          example: Premium alcohol beverages provided
        beverageType:
          type: string
          example: premium alcoholic
        alcoholCost:
          type: string
          example: free
    BeverageAmenityWrapper:
      type: object
      title: BeverageAmenityWrapper
      properties:
        beverageAmenity:
          $ref: '#/components/schemas/BeverageAmenity'
    BlockedAdjacentSeatsAmenity:
      type: object
      title: BlockedAdjacentSeatsAmenity
      description: Passenger Capacity Amenity properties
      properties:
        displayText:
          type: string
          example: No blocked adjacent seats
        blockedAdjacentSeatsDescription:
          type: string
          example: Adjacent seats are not blocked on this flight
        blockedAdjacentSeatsAttrDescription:
          type: string
          example: 'no'
    BlockedAdjacentSeatsAmenityWrapper:
      type: object
      title: BlockedAdjacentSeatsAmenityWrapper
      properties:
        blockedAdjacentSeatsAmenity:
          $ref: '#/components/schemas/BlockedAdjacentSeatsAmenity'
    BoardingPolicy:
      type: object
      description: Priority boarding information.
      required:
      - description
      properties:
        description:
          type: string
          description: Text describing the priority boarding policy.
          example: Priority boarding provided for free
    BookingCharge:
      type: object
      description: Details associated with a given payment method.
      properties:
        amount:
          $ref: '#/components/schemas/FareAmount'
          description: 'The amount to be charged for this payment method. This is
            optional if

            there''s only one payment method provided for the booking.

            '
        paymentMethod:
          $ref: '#/components/schemas/SelectedPaymentMethod'
    BookingContact:
      type: object
      properties:
        emailAddress:
          type: string
          description: Email address of the booking contact
          example: <EMAIL>
        phoneNumber:
          $ref: '#/components/schemas/PhoneNumber'
    BookingFareBreakdown:
      type: object
      required:
      - totalFare
      properties:
        totalFare:
          description: Total amount charged on card including ancillaries and all
            other charges like OB fees, merchant fees, and penalty.
          $ref: '#/components/schemas/FareAmount'
        merchantFee:
          description: Total merchant fee for this booking.
          $ref: '#/components/schemas/Money'
        airlineFee:
          description: Total OB fees applicable for this booking.
          $ref: '#/components/schemas/Money'
    BookingPaymentDetails:
      type: object
      description: Payment details used for booking.
      required:
      - bookingTravelerPaymentDetails
      properties:
        bookingTravelerPaymentDetails:
          type: array
          items:
            $ref: '#/components/schemas/BookingTravelerPaymentDetails'
    BookingTravelerPaymentDetails:
      type: object
      description: List of different forms of payments used for booking for a set
        of travelers.
      required:
      - selectedFormOfPayments
      properties:
        selectedFormOfPayments:
          type: array
          items:
            $ref: '#/components/schemas/SelectedFormOfPayment'
    BoolWrapper:
      type: object
      title: BoolWrapper
      properties:
        b:
          type: boolean
    CO2EmissionDetail:
      type: object
      title: CO2EmissionDetail
      description: CO2 emission details for the flight.
      required:
      - emissionValue
      properties:
        emissionValue:
          type: number
          format: double
          description: Estimated C02 emissions value for selected flight and seating
            class, per passenger (in tons)
          example: 10
        averageEmissionValue:
          type: number
          format: double
          description: Average estimated C02 emissions per passenger for same route
            (in tons)
          example: 10
        flightDistanceKm:
          type: number
          format: double
          description: Total distance flown by the flight in kilometres.
          example: 10
        isApproximate:
          type: boolean
          description: Indicates whether the emissions value is approximate or not.
          example: true
    Cabin:
      title: Cabin
      description: Flight cabin
      type: string
      enum:
      - UNKNOWN_CABIN
      - ECONOMY
      - PREMIUM_ECONOMY
      - BUSINESS
      - FIRST
      example: ECONOMY
    CabinFilter:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/CabinFilterType'
        cabin:
          $ref: '#/components/schemas/Cabin'
    CabinFilterType:
      type: string
      enum:
      - DEFAULT
      - ALL
      - AT_LEAST_ONE
      default: DEFAULT
      description: 'Determines how cabin type selection will be applied.

        - DEFAULT: By default AT_LEAST_ONE cabin type is used.

        - ALL: Ensures that all the segments in the itinerary have the specified cabin.

        - AT_LEAST_ONE: Ensures that at least one segment in the itinerary has the
        specified cabin.

        '
    CabinSection:
      description: Represents a section of a cabin on a plane.
      type: object
      properties:
        cabin:
          $ref: '#/components/schemas/Cabin'
        bookingCode:
          type: string
          example: B
        locations:
          description: The list of locations associated with the cabin section.
          type: array
          items:
            $ref: '#/components/schemas/SeatLocation'
        facilitySections:
          description: The set of associated facility sections.
          type: array
          items:
            $ref: '#/components/schemas/FacilitySection'
        columnSections:
          description: The columns associated with the cabin section.
          type: array
          items:
            $ref: '#/components/schemas/ColumnSection'
        rowSections:
          description: The rows associated with the cabin section.
          type: array
          items:
            $ref: '#/components/schemas/RowSection'
    CabinViewFareCategory:
      type: string
      enum:
      - UNKNOWN_CABIN_CATEGORY
      - BASIC
      - ECONOMY
      - PREMIUM_ECONOMY
      - ECONOMY_PLUS
      - BUSINESS
      - FIRST
      default: UNKNOWN_CABIN_CATEGORY
    CancellationOverride:
      type: object
      title: CancellationOverride
      description: Cancellation info override details, like cancellation penalty,
        waiver code, etc.
      properties:
        waiverCode:
          type: string
          description: "A waiver code to be applied to the refund. Typically a waiver\
            \ code will be supplied by \nthe airline for the agent to use to override\
            \ a cancellation fee.\n"
          example: WAV123
        tourCode:
          type: string
          description: "A tourcode to be applied to the refund. Input of a tourcode\
            \ will override any tourcode \non the original ticket.\n"
          example: TRAC345
        newCancellationPenalty:
          description: 'The new cancellation penalty, this will override the existing
            cancellation penalty.

            '
          $ref: '#/components/schemas/Money'
    CancellationPolicy:
      type: object
      description: Information about the cancellation policy.
      properties:
        description:
          type: string
          description: Text describing the cancellation policy.
          example: Non-refundable
        fee:
          $ref: '#/components/schemas/Money'
        assessmentType:
          $ref: '#/components/schemas/AssessmentType'
          description: Assessment Type
        isCat16:
          type: boolean
          description: Is source cat16
          example: true
        isConditional:
          type: boolean
          description: Is conditional
          example: true
    CancellationStatus:
      type: string
      title: CancellationStatus
      description: '- CANCELLED: PNR is cancelled.

        - AGENT_TASK_CREATED: Agent task is created for the cancellation.

        '
      example: CANCELLED
      enum:
      - CANCELLED
      - AGENT_TASK_CREATED
    CarPref:
      type: object
      title: CarPref
      description: Travel preferences related to car.
      properties:
        vendors:
          type: array
          description: A list of car vendors.
          items:
            $ref: '#/components/schemas/CarVendor'
        carTypes:
          type: array
          description: A list of types of car.
          items:
            $ref: '#/components/schemas/CarType'
        engineTypes:
          type: array
          description: A list of types of engine.
          items:
            $ref: '#/components/schemas/EngineType'
        transmissionTypes:
          type: array
          description: A list of types of transmission.
          items:
            $ref: '#/components/schemas/TransmissionSearchFilter'
        conditionalRates:
          type: array
          description: A list of conditional rates for rail.
          items:
            $ref: '#/components/schemas/ConditionalRate'
    CarType:
      type: string
      title: CarType
      description: Car type.
      enum:
      - OTHER
      - MINI
      - ECONOMY
      - COMPACT
      - MID_SIZE
      - STANDARD
      - FULL_SIZE
      - PREMIUM
      - LUXURY
      - CONVERTIBLE
      - MINIVAN
      - SUV
      - VAN
      - PICKUP
      - SPORTS
      - SPECIAL
      - RECREATIONAL_VEHICLE
      - WAGON
      example: ECONOMY
    CarVendor:
      type: object
      title: CarVendor
      description: Car vendor.
      required:
      - code
      - name
      properties:
        code:
          type: string
          description: Vendor code
          example: ZE
        name:
          type: string
          description: Vendor name
          example: HERTZ
        isPresentInPreferredVendors:
          type: boolean
          description: Whether the car vendor is present in preferred vendor list.
            This is an optional field which gets populated only in the preferred vendor
            autocomplete API.
          example: true
    Card:
      type: object
      title: Card
      description: User card info
      required:
      - number
      properties:
        id:
          type: string
          description: Unique identifier for this card
          format: uuid
          example: 34d536b6-f8ff-11eb-9a61-0242ac180002
        type:
          type: string
          enum:
          - UNKNOWN
          - CREDIT
          - DEBIT
          description: Type of card
          example: CREDIT
        company:
          $ref: '#/components/schemas/CardCompany'
        name:
          description: Name on card
          type: string
          example: Harrison Schwartz
        address:
          description: Billing address
          $ref: '#/components/schemas/PostalAddress'
        number:
          type: string
          description: Card number
          example: '****************'
        expiryMonth:
          type: integer
          format: int32
          minimum: 1
          maximum: 12
          description: Expiry month
          example: 1
          deprecated: true
        expiryYear:
          type: integer
          format: int32
          description: Expiry year
          minimum: 2000
          example: 2010
          deprecated: true
        cvv:
          type: string
          description: Card cvv number
          example: '012'
        label:
          type: string
          description: Card Label
          example: Label amex
        currency:
          type: string
          description: Native currency of the card.
          example: USD
        externalId:
          type: string
          description: Spotnana partner card id.
          example: bxt_RNGsNfzgJDaTstKIKqK4xEuhGYAnMdYK8T40
        vaultId:
          type: string
          description: ID of the vault used for creating the card.
          format: uuid
          example: 34d536b6-f8ff-11eb-9a61-0242ac180002
        expiry:
          description: Card Expiry.
          $ref: '#/components/schemas/CardExpiry'
        ownershipLabel:
          $ref: '#/components/schemas/OwnershipLabel'
          example: PERSONAL
    CardAccessEntity:
      title: CardAccessEntity
      type: object
      description: Contains the central access level and entity id.
      required:
      - entityId
      properties:
        entityId:
          type: string
          description: Holds the id for for the user who can access the card or organization
            id or legal entity
        centralCardAccessLevel:
          $ref: '#/components/schemas/CentralCardAccessLevel'
    CardCompany:
      type: string
      enum:
      - NONE
      - VISA
      - MASTERCARD
      - AMEX
      - DISCOVER
      - AIR_TRAVEL_UATP
      - CARTE_BLANCHE
      - DINERS_CLUB
      - JCB
      - BREX
      - UNION_PAY
      - EURO_CARD
      - ACCESS_CARD
      - ELO_CARD
      description: The issuing company of card
      example: VISA
    CardCondition:
      type: object
      title: CardCondition
      description: The card payment source fop condition
      required:
      - type
      properties:
        type:
          type: string
          enum:
          - CARD
        cardCompanies:
          type: array
          items:
            $ref: '#/components/schemas/CardCompany'
          description: List of card companies allowed for the payment
    CardDetails:
      type: object
      title: Card Details
      description: Card Details
      required:
      - token
      - expiry
      - company
      properties:
        company:
          $ref: '#/components/schemas/CardCompany'
        token:
          type: string
          description: Tokenized Card Number
          example: '****************'
        expiry:
          $ref: '#/components/schemas/CardExpiry'
    CardExpiry:
      title: CardExpiry
      type: object
      description: Contains the expiry of a Card.
      oneOf:
      - $ref: '#/components/schemas/TokenizedExpiryWrapper'
      - $ref: '#/components/schemas/ExpiryWrapper'
    Category:
      title: Category
      type: object
      description: Details about the category of the Special Service Request (SSR).
      properties:
        type:
          type: string
          description: Enum of supported category types.
          enum:
          - BASSINET
          - SPECIAL_ASSISTANCE_WHEELCHAIR
          - BAGGAGE
          - UNACCOMPANIED_MINOR
          - PETS
          - MEET_AND_ASSIST
          - OTHERS
          - MEAL
          - SPECIAL_ASSISTANCE_DISABILITY
          example: SPECIAL_ASSISTANCE_WHEELCHAIR
        description:
          type: string
          description: Category description.
          example: Special Assistance - Wheelchair
    CentralCardAccessLevel:
      title: CentralCardAccessLevel
      type: string
      description: Identifies at which level central card is applicable, for example
        organization or legal entity.
      enum:
      - UNKNOWN
      - ORGANIZATION
      - LEGAL_ENTITY
      - TMC
    ChangeableFilter:
      type: string
      enum:
      - NO_CHANGEABLE_FILTER
      - CHANGEABLE_FLEXIBLE_REFUNDABLE
      - REFUNDABLE_WITH_PENALTY
      - REFUNDABLE_WITHOUT_PENALTY
      default: NO_CHANGEABLE_FILTER
      description: List of filters related to refund and change options for flights.
    CheckInPolicy:
      type: object
      description: Information about the check-in policy.
      required:
      - description
      properties:
        description:
          type: string
          description: Description text for the check-in policy.
          example: Standard check-in priority
    CleaningAmenity:
      type: object
      title: CleaningAmenity
      description: Cleaning amenity properties.
      properties:
        displayText:
          type: string
          example: Enhanced aircraft cleaning every flight
        cleaningDescription:
          type: string
          example: This flight features an aircraft that will be thoroughly cleaned
            using disinfectants for every flight
        cleaningAttrDescription:
          type: string
          example: enhanced every flight
    CleaningAmenityWrapper:
      type: object
      title: CleaningAmenityWrapper
      properties:
        cleaningAmenity:
          $ref: '#/components/schemas/CleaningAmenity'
    CoachPref:
      type: string
      description: The preference of coach.
      enum:
      - SMOKING
      - NON_SMOKING
      - PETS_ALLOWED
      - RESTAURANT
      - QUIET
      example: NON_SMOKING
    Column:
      description: Represents a column in a row.
      type: object
      properties:
        columnNumber:
          type: string
          example: '32'
        position:
          $ref: '#/components/schemas/ColumnPosition'
    ColumnPosition:
      description: Represents the position of a column in a row.
      type: string
      enum:
      - AISLE
      - WINDOW
      - WINDOW_AISLE
      - CENTER
    ColumnSection:
      description: Represents a section of columns within a row which have same properties.
      type: object
      properties:
        columns:
          type: array
          items:
            $ref: '#/components/schemas/Column'
    CompanyConfigSource:
      type: object
      title: CompanyConfigSource
      description: For this option source, options would be auto generated based on
        specified parameter.
      required:
      - optionsParam
      properties:
        optionsParam:
          $ref: '#/components/schemas/CustomFieldOptionsParam'
    CompanyConfigSourceWrapper:
      type: object
      title: CompanyConfigSourceWrapper
      description: Wrapper for option source company config.
      properties:
        companyConfig:
          $ref: '#/components/schemas/CompanyConfigSource'
    CompanyCreditFilter:
      type: object
      title: CompanyCreditFilter
      description: Filter for fetching company credits.
      properties:
        supplier:
          $ref: '#/components/schemas/SupplierType'
          description: Supplier for which this service provider should be used.
        selectedAirlines:
          type: array
          items:
            type: string
            description: Airline codes for which the credits should be fetched.
            example: AA
        expiryDateRange:
          type: object
          properties:
            expiryDateStart:
              $ref: '#/components/schemas/DateModel'
              example: '2023-01-31'
            expiryDateEnd:
              $ref: '#/components/schemas/DateModel'
              example: '2023-12-01'
    CompanyId:
      type: object
      title: CompanyId
      description: Company ID
      required:
      - id
      properties:
        id:
          type: string
          format: uuid
          example: f49d00fe-1eda-4304-ba79-a980f565281d
    CompanyRef:
      type: object
      description: Basic information about a company.
      required:
      - id
      properties:
        id:
          $ref: '#/components/schemas/CompanyId'
        name:
          type: string
        logo:
          $ref: '#/components/schemas/Image'
          description: Company logo
    CompanyTransferableUnusedCreditsListRequest:
      type: object
      title: CompanyUnusedCreditsListRequest
      description: Request for fetching unused credits for a company.
      required:
      - companyId
      properties:
        companyId:
          type: object
          required:
          - id
          properties:
            id:
              type: string
              format: uuid
              example: edd5b835-8001-430c-98f8-fedeccebe4cf
        companyCreditFilter:
          $ref: '#/components/schemas/CompanyCreditFilter'
          description: Filter for fetching company credits.
        offset:
          type: integer
          minimum: 0
          default: 0
          example: 0
    CompanyTransferableUnusedCreditsListResponse:
      type: object
      title: CompanyUnusedCreditsListResponse
      description: Response body for company unused credits
      properties:
        unusedCreditInfos:
          type: array
          items:
            $ref: '#/components/schemas/UnusedCreditInfo'
            description: Information about the unused credit
        paginationParams:
          type: object
          properties:
            offset:
              type: integer
              example: 0
            totalRecords:
              type: integer
              example: 100
    CompanyUnusedCreditsListRequest:
      type: object
      title: CompanyUnusedCreditsListRequest
      description: Request for fetching unused credits for a company.
      properties:
        companyId:
          type: object
          properties:
            id:
              type: string
              format: uuid
              example: edd5b835-8001-430c-98f8-fedeccebe4cf
        companyCreditFilter:
          $ref: '#/components/schemas/CompanyCreditFilter'
          description: Filter for fetching company credits.
        offset:
          type: integer
          minimum: 0
          example: 0
    CompanyUnusedCreditsListResponse:
      type: object
      title: CompanyUnusedCreditsListResponse
      description: Response body for company unused credits
      properties:
        unusedCreditInfos:
          type: array
          items:
            $ref: '#/components/schemas/UnusedCreditInfo'
            description: Information about the unused credit
        paginationParams:
          type: object
          properties:
            offset:
              type: integer
              example: 0
            totalRecords:
              type: integer
              example: 100
    ComplexBag:
      type: object
      description: Describes a complex baggage option. For example, '3 bags for $50
        with allowed weights (10, 20, 30) kg'.
      required:
      - numBag
      - complexBagWeight
      - complexBagFareInfo
      properties:
        numBag:
          type: integer
          description: Number of bags in this complex bag option.
        complexBagWeight:
          $ref: '#/components/schemas/ComplexBagWeight'
        complexBagFareInfo:
          $ref: '#/components/schemas/ComplexBagFareInfo'
    ComplexBagFareInfo:
      type: object
      description: Fare (cost) information for bags. If the breakdown is present it
        will always have numBag items.
      oneOf:
      - $ref: '#/components/schemas/TotalAmountWrapper'
      - $ref: '#/components/schemas/TotalAmountBreakdownWrapper'
    ComplexBagOption:
      type: object
      description: An option containing a complex baggage description.
      properties:
        baggageOptionId:
          type: string
          example: bag_0
        description:
          type: string
          example: 3 bags of 10+20+30kg worth 100$ $(20+30+50).
        complexBag:
          $ref: '#/components/schemas/ComplexBag'
    ComplexBagWeight:
      type: object
      description: 'Weight of bags in complex baggage option. If the breakdown is
        present, it will always have numBag items.

        '
      oneOf:
      - $ref: '#/components/schemas/TotalWeightWrapper'
      - $ref: '#/components/schemas/TotalWeightBreakdownWrapper'
    ConditionalRate:
      type: string
      title: ConditionalRate
      description: Conditional rate.
      enum:
      - MILITARY
      - AAA
      - GOVERNMENT
      example: AAA
    ConnectingAirportsFilter:
      type: object
      properties:
        legFilters:
          type: array
          items:
            $ref: '#/components/schemas/ConnectingAirportsLegFilter'
    ConnectingAirportsLegFilter:
      type: object
      properties:
        legIndex:
          type: integer
          format: int32
        airportCodes:
          type: array
          items:
            type: string
    CorporateCodeFilter:
      type: object
      properties:
        corpAccountCodes:
          type: array
          items:
            type: string
            example: PPF03
          description: List of Corporate code to be filtered
    CorporateInfo:
      type: object
      description: Corporate data to be collected during the search request workflow.
      required:
      - preSearchAnswers
      properties:
        preSearchAnswers:
          $ref: '#/components/schemas/PreSearchAnswers'
    CostCenter:
      type: object
      title: CostCenter
      description: Cost center details.
      required:
      - id
      - name
      properties:
        id:
          $ref: '#/components/schemas/CostCenterId'
        name:
          type: string
          example: CostCenter
        externalId:
          type: string
          example: external-id
    CostCenterId:
      type: object
      title: Cost center id.
      required:
      - id
      properties:
        id:
          type: string
          example: 731ccbca-0415-6fe1-d235-c324dfbe7423
    CovidFilter:
      type: object
      properties:
        vaccine:
          $ref: '#/components/schemas/CovidFilterPreference'
        covidTest:
          $ref: '#/components/schemas/CovidFilterPreference'
        faceMask:
          $ref: '#/components/schemas/CovidFilterPreference'
        temperatureCheck:
          $ref: '#/components/schemas/CovidFilterPreference'
        blockedAdjacentSeats:
          $ref: '#/components/schemas/CovidFilterPreference'
    CovidFilterPreference:
      type: string
      enum:
      - NO_PREFERENCE
      - REQUIRED
      - NOT_REQUIRED
      default: NO_PREFERENCE
    CovidTestingAmenity:
      type: object
      title: CovidTestingAmenity
      description: Covid testing amenity properties.
      properties:
        displayText:
          type: string
          example: No COVID-19 test required
        covidTestingDescription:
          type: string
          example: A negative COVID-19 test is not required for this flight; check
            with the airline for possible destination requirements or other restrictions.
        covidTestingAttrDescription:
          type: string
    CovidTestingAmenityWrapper:
      type: object
      title: CovidTestingAmenityWrapper
      properties:
        covidTestingAmenity:
          $ref: '#/components/schemas/CovidTestingAmenity'
    Credit:
      type: object
      title: Credit
      required:
      - ticketNumber
      - ticketType
      - sourcePnr
      - totalFare
      - passengerName
      - segmentsAvailable
      - type
      - airlineInfo
      - traveler
      properties:
        type:
          type: string
          description: Payment source type. Should be set to UNUSED_CREDIT for unused
            credit details.
        pnrOwningPcc:
          type: string
          description: PCC the PNR was created on.
        unusedCreditPcc:
          type: string
          description: PCC the credit was issued on.
        departureCountry:
          type: string
          description: 3 letter country code of the departure country associated with
            the original ticket.
          example: USA
        arrivalCountry:
          type: string
          description: 3 letter country code of the arrival country associated with
            the original ticket.
          example: USA
        ticketType:
          type: string
          description: Type of credit.
          enum:
          - TICKET_TYPE_UNKNOWN
          - ETICKET
          - MCO
        departureDate:
          description: Date for the departure of the first flight associated with
            the unused credit.
          $ref: '#/components/schemas/DateTimeOffset'
        segmentsAvailable:
          type: string
          description: Whether all segments are unused or some have already been used.
          enum:
          - UNKNOWN
          - ALL_OPEN
          - PARTIAL
          - OTHER
        traveler:
          description: Information about the traveler for which the credit should
            be redeemed.
          $ref: '#/components/schemas/AirRequestTravelerInfo'
        passengerName:
          description: Name of the passenger associated with the credit.
          $ref: '#/components/schemas/Name'
        airlineInfo:
          description: Airline info with airline name and code
          $ref: '#/components/schemas/AirlineInfo'
        totalFare:
          description: Total airfare associated with the original ticket.
          $ref: '#/components/schemas/Money'
        issueDate:
          description: Issue date for the unused credit.
          $ref: '#/components/schemas/DateTimeOffset'
        expiryDate:
          description: Expiry date for the unused credit.
          $ref: '#/components/schemas/DateTimeOffset'
        source:
          $ref: '#/components/schemas/ThirdPartySource'
          default: SABRE
          description: Source of unused credit e.g. Sabre, NDC etc.
        sourcePnr:
          type: string
          description: PNR number corresponding to third party through which booking
            was made.
          example: MC5ONS
        flightIds:
          type: array
          description: ID of the flights on which this credit applies.
          minItems: 1
          items:
            type: string
        ticketNumber:
          type: string
          description: Ticket number for the ticket that was converted into an unused
            credit.
          example: '5267779139217'
    CreditCardAccess:
      type: object
      title: CreditCardAccess
      description: Access type for cards to check who in the organization has permission
        to use it
      required:
      - accessType
      - entityIds
      properties:
        accessType:
          $ref: '#/components/schemas/CreditCardAccessType'
        entityIds:
          type: array
          items:
            type: string
          description: Holds the ids for for all users who can access the card or
            organization id
        entities:
          type: array
          description: A list of cardAccessEntity consisting of central card access
            level if present and entity id.
          items:
            $ref: '#/components/schemas/CardAccessEntity'
    CreditCardAccessType:
      title: CreditCardAccessType
      type: string
      description: Identifies the different ways the card can be used across an organization
      enum:
      - UNKNOWN_TYPE
      - CENTRALISED
      - INDIVIDUAL
      - PERSONAL
      - TMC
      - APPLICATION
      - ITINERARY
      - EVENTS
      - TRAVEL_ARRANGER_MANAGED
      - COMPANY_TRAVEL_ARRANGER_MANAGED
      - EVENT_TEMPLATE
      x-ignoreBreakingChanges:
      - CreditCardAccessType->EVENT_TEMPLATE
    CreditStatus:
      type: string
      description: Status of the credit.
      enum:
      - STATUS_UNKNOWN
      - OPEN
      - USED
      - RESERVED
      x-ignoreBreakingChanges:
      - CreditStatus->RESERVED
    CreditUsageType:
      type: string
      description: Usage type of the credit.
      enum:
      - CREDIT_USAGE_TYPE_UNKNOWN
      - COMPANY
      - PERSONAL
    CustomFieldLocation:
      title: CustomFieldLocation
      description: Display the custom fields in all these locations.
      type: string
      enum:
      - POLICY_APPROVAL_EMAIL
      - PNR_EMAIL
      - TRIP_EMAIL
      example: POLICY_APPROVAL_EMAIL
    CustomFieldMatchConditions:
      type: object
      title: CustomFieldMatchConditions
      description: Conditions to select the custom field for given context.
      properties:
        travelerConditions:
          $ref: '#/components/schemas/TravelerMatchConditions'
        travelTypes:
          type: array
          description: Travel types to match.
          items:
            $ref: '#/components/schemas/TravelType'
        travelRegionTypes:
          type: array
          description: Travel region types to match.
          items:
            $ref: '#/components/schemas/TravelRegionType'
        tripUsageTypes:
          type: array
          description: Trip usage types to match. If empty, all trip usage types will
            be matched.
          items:
            $ref: '#/components/schemas/TripUsageType'
    CustomFieldOptionsParam:
      type: string
      title: CustomFieldOptionsParam
      enum:
      - COST_CENTER
      - LEGAL_ENTITY
      - OFFICE
      - DEPARTMENT
      description: Parameter to form options for the custom field.
      example: COST_CENTER
    CustomFieldSelectedOption:
      type: object
      title: CustomFieldSelectedOption
      description: Selected response for the custom field.
      required:
      - name
      properties:
        name:
          type: string
          description: Value of the selection
        description:
          type: string
          description: Description of the selection
        additionalUserInput:
          type: string
          description: Additional user input
        additionalInfos:
          type: array
          description: Actual values of the additional infos
          items:
            type: string
        additionalInfoConfigs:
          type: array
          description: Additional info configs for the selected option
          items:
            $ref: '#/components/schemas/AdditionalInfo'
    CustomFieldType:
      type: string
      description: The type of custom field.
      enum:
      - QUESTION
      - MEETING
      - BUDGET
      - BREX_TOKEN
      default: QUESTION
    CustomFieldV3Response:
      type: object
      title: CustomFieldV3Response
      description: Custom field responses.
      required:
      - fieldId
      - armId
      - selectedOptions
      properties:
        fieldId:
          type: string
          format: uuid
          description: Custom field id
        fieldName:
          type: string
          description: Name of the custom field
        armId:
          type: string
          format: uuid
          description: Arm id which is applicable
        includeLocations:
          type: array
          items:
            $ref: '#/components/schemas/IncludeLocation'
        selectedOptions:
          type: array
          description: The list of options that are selected by user or auto populated.
          items:
            $ref: '#/components/schemas/CustomFieldSelectedOption'
    DateModel:
      title: Date
      description: Date in ISO 8601 standard.
      type: object
      required:
      - iso8601
      properties:
        iso8601:
          type: string
          pattern: ^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$
          example: '2017-07-21'
    DateTimeLocal:
      title: DateTimeLocal
      description: Local date and time in ISO 8601 format.
      type: object
      required:
      - iso8601
      properties:
        iso8601:
          type: string
          pattern: ^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$
          example: 2017-07-21T17:32
    DateTimeOffset:
      title: DateTimeOffset
      description: ISO8601 UTC Date Time
      type: object
      required:
      - iso8601
      properties:
        iso8601:
          type: string
          pattern: ^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?(Z|([+-](0[0-9]|1[0-4]):([0-5][0-9])))$
          example: 2017-07-21T17:32Z
    DeckLevel:
      type: string
      description: The deck level of the rail.
      enum:
      - UPPER_DECK
      - LOWER_DECK
      example: UPPER_DECK
    Department:
      type: object
      title: Department
      description: Department details.
      required:
      - id
      - name
      properties:
        id:
          $ref: '#/components/schemas/DepartmentId'
        name:
          type: string
          example: IT Department
        externalId:
          type: string
          description: External id of the department
          example: department-ext-id
        employeeCount:
          type: integer
          format: int32
          description: Count of employees in the department
          example: 57
    DepartmentId:
      type: object
      title: DepartmentId
      description: Department id.
      required:
      - id
      properties:
        id:
          type: string
          example: 631ccbcf-9414-5fe0-c234-b324dfbe7422
    Dimensions:
      type: object
      title: Dimensions
      description: Image dimensions ie width and height.
      properties:
        height:
          type: integer
          format: int32
          example: 120
        width:
          type: integer
          format: int32
          example: 240
    DoubleListWrapper:
      type: object
      title: DoubleListWrapper
      properties:
        dList:
          type: array
          items:
            type: number
            format: double
    DoubleRange:
      type: object
      properties:
        min:
          type: number
          format: double
          description: Minimum value - inclusive.
        max:
          type: number
          format: double
          description: Maximum value - inclusive.
    DoubleRangeWrapper:
      type: object
      title: DoubleRangeWrapper
      properties:
        dRange:
          $ref: '#/components/schemas/DoubleRange'
    DoubleWrapper:
      type: object
      title: DoubleWrapper
      properties:
        d:
          type: number
          format: double
    Dpan:
      type: object
      title: Dpan
      description: Object to pass through dpan payment source details through the
        api call.
      required:
      - type
      - cardDetails
      properties:
        type:
          type: string
          description: Payment source type. Should be set to DPAN for card details.
        cardDetails:
          $ref: '#/components/schemas/CardDetails'
    Duration:
      type: object
      title: Duration
      description: Represents a duration of time with specific units of time.
      properties:
        iso8601:
          description: 'Durations define the amount of intervening time in a time
            interval and are represented by the

            format P[n]Y[n]M[n]DT[n]H[n]M[n]S.

            The [n] is replaced by the value for each of the date and time elements
            that follow the [n].

            Leading zeros are not required. The capital letters P, Y, M, W, D, T,
            H, M, and S are

            designators for each of the date and time elements and are not replaced.
            P is the duration

            designator (for period) placed at the start of the duration representation.

            Y is the year designator.

            M is the month designator.

            W is the week designator.

            D is the day designator.

            T is the time designator.

            H is the hour designator.

            M is the minute designator.

            S is the second designator and can include decimal digits with arbitrary
            precision.

            '
          type: string
          example: PT19H55M
    EmergencyContact:
      type: object
      title: EmergencyContact
      description: Emergency contact information.
      required:
      - name
      - phoneNumbers
      properties:
        name:
          type: string
          description: Full name of contact.
          example: John Smith
        email:
          type: string
          description: Email address of contact.
          format: email
          example: <EMAIL>
        designation:
          deprecated: true
          type: string
          description: Job title of contact.
          example: MANAGER
        relation:
          type: string
          description: Relation of contact to user.
          example: SPOUSE
          enum:
          - RELATION_UNKNOWN
          - SPOUSE
          - PARENT
          - SIBLING
          - CHILD
          - FRIEND
          - RELATIVE
          - COLLEAGUE
          - OTHER
        phoneNumbers:
          type: array
          description: Phone numbers of contact.
          items:
            $ref: '#/components/schemas/PhoneNumber'
        preferredLanguage:
          type: string
          description: Language preferred by user.
          example: en-US
    EmergencyContactInfo:
      type: object
      title: EmergencyContactInfo
      description: Emergency contact information.
      required:
      - email
      properties:
        address:
          $ref: '#/components/schemas/PostalAddress'
        designation:
          type: string
          example: MANAGER
        email:
          type: string
          format: email
          example: <EMAIL>
        name:
          $ref: '#/components/schemas/Name'
        phoneNumber:
          $ref: '#/components/schemas/PhoneNumber'
        userOrgId:
          $ref: '#/components/schemas/UserOrgId'
    EngineType:
      type: string
      title: EngineType
      description: Engine types.
      enum:
      - UNKNOWN_ENGINE
      - PETROL
      - DIESEL
      - ELECTRIC
      - CNG
      - HYBRID
      - HYDROGEN
      - MULTI_FUEL
      - ETHANOL
      example: PETROL
    EntertainmentAmenity:
      type: object
      title: EntertainmentAmenity
      description: The properties of an entertainment amenity.
      properties:
        displayText:
          type: string
          example: Seatback on-demand video
        entertainmentType:
          type: string
          example: on-demand
        cost:
          type: string
          example: free
    EntertainmentAmenityWrapper:
      type: object
      title: EntertainmentAmenityWrapper
      properties:
        entertainmentAmenity:
          $ref: '#/components/schemas/EntertainmentAmenity'
    EntityAnswer:
      type: object
      properties:
        entityId:
          type: string
          description: The unique ID for the question.
        userInput:
          type: string
          description: The text input given by user (if any).
        itemIds:
          type: array
          items:
            type: integer
            format: int32
          description: 'The id/enum value corresponding to the option chosen by the
            user as

            answer.

            '
        answers:
          type: array
          items:
            $ref: '#/components/schemas/AnswerPair'
        customFieldType:
          $ref: '#/components/schemas/CustomFieldType'
        questionDisplayText:
          type: string
          description: The question text to be displayed to the user.
        question:
          $ref: '#/components/schemas/Question'
    Equipment:
      type: object
      title: Equipment
      description: The type of the aircraft equipment
      properties:
        code:
          type: string
          description: Aircraft equipment code
          example: '777'
        type:
          type: string
          description: Code representing the type of the equipment
          example: N
          deprecated: true
        name:
          type: string
          description: The name of the flight aircraft type
          example: Boeing 737-800
    ErrorParameter:
      type: object
      title: ErrorParameter
      description: Error parameter
      properties:
        name:
          type: string
          description: Parameter name
        value:
          type: string
          description: Parameter value
    ErrorResponse:
      type: object
      properties:
        debugIdentifier:
          type: string
          description: Link to debug the error internally.
        errorMessages:
          type: array
          items:
            type: object
            properties:
              errorCode:
                type: string
                description: Error code to identify the specific errors.
              message:
                type: string
                description: Message containing details of error.
              errorParameters:
                type: array
                description: Error message parameters.
                items:
                  $ref: '#/components/schemas/ErrorParameter'
              errorDetail:
                type: string
                description: More details about the error.
    ExchangeNotSupportedReason:
      type: string
      title: ExchangeNotSupportedReason
      description: "Reason why exchange is not supported via OBT.\n- EXCHANGE_NOT_SUPPORTED_REASON_UNKNOWN:\
        \ We were not able to figure out the reason why exchange was not supported.\n\
        - FLIGHT_CHECKED_IN: Any one segment of the current ticket is checked-in.\n\
        - NON_EXCHANGEABLE: Ticket is non-exchangeable.\n- AUTOMATED_EXCHANGE_NOT_SUPPORTED:\
        \ Automated exchange via API is not supported for the ticket.\n- MULTI_PAX_EXCHANGE_NOT_SUPPORTED:\
        \ Multi-pax exchange is not supported via OBT.\n- EXCHANGE_NOT_SUPPORTED_FOR_MARKUP_FARES:\
        \ Exchange for markup fares not supported via OBT.\n- REWARDS_EXCHANGE_NOT_ENABLED:\
        \ Automated exchange disabled for rewards.\n- FARE_RULES_UNKNOWN: Correct\
        \ fare rules unknown.\n- ALL_FLIGHTS_USED: All the flights in the current\
        \ ticket is flown.\n- MISSING_PQ: The PQ for the ticket we are tying to exchange\
        \ is no longer present in the PNR.\n- AIRLINE_TICKET: The ticket is an airline\
        \ ticket. \n- AIRLINE_CONTROL: The PNR is under airline control.\n- SOURCE_NOT_IMPLEMENTED:\
        \ Exchange is not implemented for the source of the given PNR.\n- SCHEDULE_CHANGE_PENDING:\
        \ We can't exchange a ticket that has schedule change segment in it.\n- APPROVAL_PENDING:\
        \ Exchange is not allowed for the PNRs that are pending for approval.\n- APPROVAL_DENIED:\
        \ Exchange is not allowed for the PNRs that are denied after approval.\n-\
        \ EXCHANGE_NOT_ALLOWED_WITHIN_24_HRS_TICKETING: Exchange is not allowed within\
        \ 24 hrs of ticketing.\n"
      example: FLIGHT_CHECKED_IN
      enum:
      - EXCHANGE_NOT_SUPPORTED_REASON_UNKNOWN
      - FLIGHT_CHECKED_IN
      - NON_EXCHANGEABLE
      - AUTOMATED_EXCHANGE_NOT_SUPPORTED
      - MULTI_PAX_EXCHANGE_NOT_SUPPORTED
      - EXCHANGE_NOT_SUPPORTED_FOR_MARKUP_FARES
      - REWARDS_EXCHANGE_NOT_ENABLED
      - FARE_RULES_UNKNOWN
      - ALL_FLIGHTS_USED
      - MISSING_PQ
      - AIRLINE_TICKET
      - AIRLINE_CONTROL
      - SOURCE_NOT_IMPLEMENTED
      - SCHEDULE_CHANGE_PENDING
      - APPROVAL_PENDING
      - APPROVAL_DENIED
      - EXCHANGE_NOT_ALLOWED_WITHIN_24_HRS_TICKETING
    ExchangePolicy:
      type: object
      description: Information about the exchange policy.
      properties:
        description:
          type: string
          description: Text describing the exchange policy.
          example: Change allowed for free
        fee:
          $ref: '#/components/schemas/Money'
        assessmentType:
          $ref: '#/components/schemas/AssessmentType'
        isCat16:
          type: boolean
          description: Is source cat16
          example: true
        isConditional:
          type: boolean
          description: Is conditional
          example: true
    ExistingLeg:
      type: object
      title: ExistingLeg
      example:
        index: 1
        remove: true
      properties:
        index:
          type: integer
          example: 1
          format: int32
          description: The index of the leg in the existing PNR which needs to be
            kept or removed(see remove field) during the exchange process with the
            following origin-destination information. For new legs leave empty (null).
            Fields origin, destination and date are required only if this leg is a
            new leg to be added.
        remove:
          type: boolean
          description: This should be set to true, if this leg is to be cancelled.
          example: true
    Expiry:
      title: Expiry
      type: object
      description: Contains the expiry of a Card.
      required:
      - expiryMonth
      - expiryYear
      properties:
        expiryMonth:
          type: integer
          format: int32
          minimum: 1
          maximum: 12
          description: Expiry month
          example: 1
        expiryYear:
          type: integer
          format: int32
          description: Expiry year
          minimum: 2000
          example: 2010
    ExpiryWrapper:
      type: object
      title: ExpiryWrapper
      properties:
        expiry:
          $ref: '#/components/schemas/Expiry'
    Expression:
      type: object
      title: Expression
      description: 'Expression consisting of variables and constants which needs to
        be sent along with custom

        field response.

        '
      required:
      - type
      - formatExpression
      properties:
        type:
          type: string
          example: EXPRESSION
          default: EXPRESSION
        formatExpression:
          type: string
          description: "The expression must be of format : `${expression}`.The expression\
            \ can consist of a \ncombination of variables and mathematical operations.\n\
            \ Variable names must begin with `var` followed by a number, which is\
            \ used to identify \nthe variable in the variables list. The numbering\
            \ should follow a 1-based index.\n  To define mathematical operations,\
            \ the operation name should follow the format\n`math.<math_op>(arg1, arg2)`.\
            \ Both `arg1` and `arg2` can be variables or constants. \nThe supported\
            \ math operations (math_op) include: `add, mul, div, sub, min,\nand max`.\
            \ All keywords, such as `<math_op>, math, and var` must be written in\
            \ lowercase.\n"
          example: 'Result:  ${math.mul(var1,5)}  ${var2}'
        variables:
          type: array
          description: Reference names of the variables present in the expression.
          items:
            $ref: '#/components/schemas/VariableName'
    Facility:
      description: Describes a facility available in the flight.
      type: object
      properties:
        location:
          $ref: '#/components/schemas/SeatLocation'
        facilityType:
          $ref: '#/components/schemas/FacilityType'
    FacilitySection:
      description: Describes a facility section in a flight.
      type: object
      properties:
        location:
          $ref: '#/components/schemas/SeatLocation'
        facilities:
          type: array
          items:
            $ref: '#/components/schemas/Facility'
    FacilityType:
      description: Facility type is used to represent row facilities and cabin facilities.
      type: string
      enum:
      - BULKHEAD
      - STAIRS
      - TABLE
      - LAVATORY
      - BAR
      - CLOSET
      - AIR_PHONE
      - EXIT_DOOR
      - EMERGENCY_EXIT
      - GALLEY
      - LUGGAGE_STORAGE
      - STORAGE_SPACE
    FareAmount:
      type: object
      title: FareAmount
      description: Fare amount including base fare and tax.
      required:
      - base
      - tax
      properties:
        base:
          description: Base fare amount.
          $ref: '#/components/schemas/Money'
        tax:
          description: Tax amount.
          $ref: '#/components/schemas/Money'
    FareAttributes:
      type: object
      description: The rules and attributes related to the fare.
      properties:
        isVoidable:
          type: boolean
          description: Designates whether the fare is voidable or not.
        holdDeadline:
          description: Designates the dateTime until which the fare will be held (when
            a traveler places a hold on the fare).
          $ref: '#/components/schemas/DateTimeLocal'
        isNonVerifiedExchangeOffer:
          type: boolean
          description: "Designates whether the fare is a non-verified exchange offer.\
            \ For these fares, the \nexchange price guarantee is not provided by the\
            \ supplier.\n"
    FareBasisRule:
      type: object
      properties:
        fareBasisCode:
          type: string
        rules:
          $ref: '#/components/schemas/Rules'
    FareCategory:
      type: object
      properties:
        ngsCategory:
          $ref: '#/components/schemas/NGSFareCategory'
        cabinViewCategory:
          $ref: '#/components/schemas/CabinViewFareCategory'
    FareCategoryFilter:
      type: object
      properties:
        fareCategories:
          type: array
          items:
            $ref: '#/components/schemas/FareCategory'
          title: Fare categories to return in the response
    FareComponent:
      type: string
      description: Fare component eligible for this fop rule
      enum:
      - BASE
      - TAX
      example: BASE
    FareInfo:
      title: FareInfo
      description: The detailed information about the fare (including tax and merchant
        fee details).
      required:
      - totalFare
      properties:
        totalFare:
          description: Total fare applicable to the associated entity.
          $ref: '#/components/schemas/FareAmount'
        taxBreakdown:
          description: Details about of the tax amount in totalFare field.
          $ref: '#/components/schemas/TaxBreakdown'
        merchantFeeInfo:
          $ref: '#/components/schemas/MerchantFeeInfo'
    FareMetadata:
      description: Metadata about pax fare
      required:
      - totalFare
      properties:
        corpAccountCode:
          type: string
          description: The corp account code for each pax.
          example: 6CA4
    FarePref:
      type: object
      title: FarePref
      description: Fare preferences.
      required:
      - fareTypes
      properties:
        fareTypes:
          type: array
          items:
            type: string
            enum:
            - UNKNOWN_TYPE
            - CHANGEABLE
            - REFUNDABLE
            example: CHANGEABLE
    FareRules:
      type: object
      description: The rules and restrictions associated with the fare.
      properties:
        baggagePolicy:
          $ref: '#/components/schemas/BaggagePolicy'
        cancellationPolicy:
          $ref: '#/components/schemas/CancellationPolicy'
        postDepartureCancellationPolicy:
          description: Information about the post departure cancellation policy.
          $ref: '#/components/schemas/CancellationPolicy'
        exchangePolicy:
          $ref: '#/components/schemas/ExchangePolicy'
        postDepartureExchangePolicy:
          description: Information about the post departure exchange policy.
          $ref: '#/components/schemas/ExchangePolicy'
        seatSelectionPolicy:
          $ref: '#/components/schemas/SeatSelectionRule'
        boardingPolicy:
          $ref: '#/components/schemas/BoardingPolicy'
        checkInPolicy:
          $ref: '#/components/schemas/CheckInPolicy'
        loungePolicy:
          $ref: '#/components/schemas/LoungePolicy'
        seatType:
          $ref: '#/components/schemas/SeatTypeInfo'
    FareTypeFilter:
      type: string
      enum:
      - UNKNOWN
      - PUBLIC
      - PRIVATE
      - CORPORATE
      default: UNKNOWN
    FetchAirFareRuleRequest:
      type: object
      properties:
        searchId:
          type: string
          description: 'The unique ID (identifying the current workflow) returned
            by the backend in the air

            search results response (AirSearchResponse.searchId) of the previous leg.
            If set, the

            rateOptionId and userId must also be specified.

            '
        rateOptionId:
          type: string
          description: The unique ID identifying the selected flights in the previous
            leg's response.
        pnrId:
          description: The unique Spotnana PNR ID against which flights are booked.
          type: string
          example: '**********'
        userId:
          type: string
          format: uuid
          example: 4974a66b-7493-4f41-908c-58ba81093947
          description: Unique ID of the user.
    FetchAirFareRuleResponse:
      type: object
      properties:
        legRuleInfos:
          type: array
          items:
            $ref: '#/components/schemas/LegRuleInfo'
    FetchTravelerUnusedCreditsRequest:
      type: object
      title: FetchTravelerUnusedCreditsRequest
      description: Request body for fetching traveler's unused credits
      required:
      - travelerInfo
      properties:
        travelerInfo:
          description: Traveler information to get userOrgId.
          $ref: '#/components/schemas/AirRequestTravelerInfo'
    FetchTravelerUnusedCreditsResponse:
      type: object
      title: FetchTravelerUnusedCreditsResponse
      description: Response body for fetching traveler's unused credits
      properties:
        unusedCreditInfo:
          type: array
          items:
            $ref: '#/components/schemas/UnusedCreditInfo'
            description: Information about the unused credit
    Filter:
      type: object
      properties:
        cabin:
          $ref: '#/components/schemas/CabinFilter'
        maxNumStops:
          type: integer
          format: int32
        timeRange:
          $ref: '#/components/schemas/TimeRangeFilter'
        alliance:
          $ref: '#/components/schemas/AllianceFilter'
        fareRange:
          $ref: '#/components/schemas/MoneyRange'
        airlinePref:
          $ref: '#/components/schemas/AirlinePrefsFilter'
        fareType:
          $ref: '#/components/schemas/FareTypeFilter'
        changeable:
          $ref: '#/components/schemas/ChangeableFilter'
        connectingAirports:
          $ref: '#/components/schemas/ConnectingAirportsFilter'
        seatPref:
          $ref: '#/components/schemas/SeatPrefFilter'
        covid:
          $ref: '#/components/schemas/CovidFilter'
        baggage:
          $ref: '#/components/schemas/BaggageFilter'
        flightNumber:
          $ref: '#/components/schemas/FlightNumberFilter'
        policyFilter:
          $ref: '#/components/schemas/PolicyFilter'
        multiTicketFilter:
          $ref: '#/components/schemas/MultiTicketFilter'
        fareCategoryFilter:
          $ref: '#/components/schemas/FareCategoryFilter'
        corporateCodeFilter:
          $ref: '#/components/schemas/CorporateCodeFilter'
        unusedCreditFilter:
          $ref: '#/components/schemas/UnusedCreditFilter'
    FlightAttribute:
      type: object
      title: FlightAttribute
      description: 'Information about flight product attributes such as marketing
        descriptions and images for superior retailing

        experience.

        '
      properties:
        description:
          type: string
          description: Marketing description and images associated with the flight
            attribute.
          example: A fully flat bed
        categories:
          type: array
          description: List of categories in which this flight attribute falls into
          items:
            $ref: '#/components/schemas/FlightAttributeCategory'
        photos:
          type: array
          description: List of images associated with the flight attribute.
          items:
            $ref: '#/components/schemas/ImageGroup'
    FlightAttributeCategory:
      type: string
      enum:
      - AIRCRAFT
      - SEAT
      - MEALS
      - BEVERAGES
      - ENTERTAINMENT
      - POWER
      - WIFI
      - SERVICE_INFLIGHT
      - UPGRADE
      - TRANSPORT_TO_AIRPORT
      - CHECK_IN
      - DEPARTURE_TERMINAL
      - DEPARTMENT_LOUNGE
      - GATE_BOARDING
      - SERVICE_DEPARTURE
      - STOPOVER
      - CONNECTING_EASE
      - CONNECTING_TERMINAL
      - CONNECTING_LOUNGE
      - SERVICE_CONNECTING
      - BAGGAGE_COLLECTION
      - ARRIVAL_TERMINAL
      - ARRIVAL_LOUNGE
      - ARRIVAL_TRANSPORT
      - SERVICE_ARRIVAL
      - SCHEDULE_ROUTE
      - LOYALITY_PROGRAM
      - BRAND
      - PROMOTION
      example: STOPOVER
    FlightCommon:
      title: FlightCommon
      description: Basic information about the flight.
      type: object
      required:
      - departureDateTime
      - arrivalDateTime
      - origin
      - destination
      - marketing
      - operating
      properties:
        flightRef:
          type: string
          description: Unique ID for this flight in the response.
          example: flight_3
        departureDateTime:
          description: Departure date and time of the flight.
          $ref: '#/components/schemas/DateTimeLocal'
        arrivalDateTime:
          description: Arrival date and time of the flight.
          $ref: '#/components/schemas/DateTimeLocal'
        origin:
          description: The airport from which the flight departs (origin).
          $ref: '#/components/schemas/AirportInfo'
        destination:
          description: The airport at which the flight arrives (destination).
          $ref: '#/components/schemas/AirportInfo'
        departureGate:
          description: Departure gate and terminal.
          $ref: '#/components/schemas/GateInfo'
        arrivalGate:
          description: Arrival gate and terminal.
          $ref: '#/components/schemas/GateInfo'
        marketing:
          description: Marketing flight number.
          $ref: '#/components/schemas/FlightNumber'
        operating:
          description: Operating flight number (for the airline which actually operates
            the flight).
          $ref: '#/components/schemas/FlightNumber'
        operationalDisclosure:
          type: string
          description: Operating airline name that has to be displayed to the user
          example: SKYWEST DBA UNITED EXPRESS
        hiddenStops:
          type: array
          description: Any stops for refueling or boarding additional passengers.
          items:
            type: object
            title: FlightHiddenStop
            required:
            - airport
            - arrivalDateTime
            - departureDateTime
            properties:
              airport:
                description: The airport where the flight will make a stop (not a
                  traditional layover stop).
                $ref: '#/components/schemas/AirportInfo'
              arrivalDateTime:
                description: The start date and time for the stop.
                $ref: '#/components/schemas/DateTimeLocal'
              departureDateTime:
                description: The end date and time for the stop.
                $ref: '#/components/schemas/DateTimeLocal'
        duration:
          description: The duration of the flight (travel time).
          $ref: '#/components/schemas/Duration'
        equipment:
          description: The equipment or aircraft type used for the flight
          $ref: '#/components/schemas/Equipment'
    FlightCommonWrapper:
      title: FlightCommonWrapper
      description: Wrapper of FlightCommon data to be used in oneOf.
      properties:
        flightData:
          $ref: '#/components/schemas/FlightCommon'
    FlightData:
      type: object
      title: FlightData
      oneOf:
      - $ref: '#/components/schemas/FlightRefWrapper'
      - $ref: '#/components/schemas/FlightCommonWrapper'
    FlightDetail:
      type: object
      description: Describes the details of a flight segment (including the origin,
        destination, cabin, etc).
      required:
      - id
      - cabin
      properties:
        id:
          type: string
          description: The unique ID for this flight in the itinerary.
          example: flight_0
        flightData:
          $ref: '#/components/schemas/FlightData'
        cabin:
          description: The cabin type for this flight in the associated itinerary
          $ref: '#/components/schemas/Cabin'
        bookingCode:
          type: string
          description: The airline's one-letter code for the associated fare
          example: B
        seatAvailability:
          type: integer
          format: int32
          description: The availability of seats on this flight
          example: 9
        corpAccountCode:
          type: string
          description: The account code is used to get corporate negotiated price
          example: DFG
        carbonEmission:
          description: The CO2 emission details for this flight
          $ref: '#/components/schemas/CO2EmissionDetail'
        amenities:
          description: The amenities associated with the flight.
          type: array
          items:
            $ref: '#/components/schemas/AirAmenity'
        flightAttributes:
          description: The set of product attributes associated with the flight.
          type: array
          items:
            $ref: '#/components/schemas/FlightAttribute'
    FlightInLegRule:
      type: object
      properties:
        origin:
          type: string
        destination:
          type: string
        departureDateTime:
          $ref: '#/components/schemas/DateTimeLocal'
        flightId:
          type: string
    FlightNumber:
      title: FlightNumber
      description: Flight number and airline code.
      required:
      - airlineCode
      - num
      properties:
        num:
          type: string
          description: Flight number.
          example: '321'
        airlineCode:
          type: string
          description: Two-letter IATA airline code.
          example: AA
    FlightNumberFilter:
      type: object
      description: Filters itineraries where the flight number matches that of either
        the marketing or operating carrier.
      properties:
        flightNumber:
          $ref: '#/components/schemas/FlightNumber'
    FlightRef:
      title: FlightRef
      description: Flight reference information.
      type: object
      required:
      - flightRef
      properties:
        flightRef:
          type: string
          description: 'Points to the detailed flight information in auxiliary flight
            data fields. flightRef is present in

            FlightCommon. As a result, FlightRef can be used to point to the FlightCommon
            object.

            '
          example: flight_3
    FlightRefWrapper:
      title: FlightRefWrapper
      description: Wrapper of flight reference to be used in oneOf.
      properties:
        flightRef:
          $ref: '#/components/schemas/FlightRef'
    FlightSeatMap:
      type: object
      description: Seat map for a flight.
      properties:
        seatMapId:
          description: Unique identifier for this flight seat map.
          type: string
          example: seat_map_0
        wingRows:
          description: Defines the wing rows in the seat map.
          $ref: '#/components/schemas/Int32Range'
        cabinSections:
          description: Cabin sections available in the flight.
          type: array
          items:
            $ref: '#/components/schemas/CabinSection'
    FreshFoodAmenity:
      type: object
      title: FreshFoodAmenity
      description: Properties associated with the fresh food amenity.
      properties:
        displayText:
          type: string
        freshFoodType:
          type: string
        cost:
          type: string
    FreshFoodAmenityWrapper:
      type: object
      title: FreshFoodAmenityWrapper
      properties:
        freshFoodAmenity:
          $ref: '#/components/schemas/FreshFoodAmenity'
    GateInfo:
      type: object
      title: GateInfo
      description: Information about the flight's departure / arrival gates and terminals.
      properties:
        gate:
          type: string
          description: Gate number
          example: 1A
        terminal:
          type: string
          description: Airport terminal
          example: '1'
    GatewayIdentifier:
      type: object
      properties:
        gatewayId:
          type: string
          description: Gateway Id for which the payment method should be verified.
        gatewayType:
          type: string
          description: Gateway Type for of the verification gateway.
          enum:
          - PAYMENT_GATEWAY_UNKNOWN
          - STRIPE
          - BREX
          - RAZORPAY
    Gender:
      title: Gender
      type: string
      enum:
      - MALE
      - FEMALE
      - UNSPECIFIED
      - UNDISCLOSED
      example: FEMALE
    Grade:
      type: object
      title: Grade
      description: Grade details.
      required:
      - id
      - name
      properties:
        employeeCount:
          type: integer
          format: int32
          description: Count of employees in the grade
          example: 75
        id:
          $ref: '#/components/schemas/GradeId'
        name:
          type: string
          example: Grade
    GradeId:
      type: object
      title: GradeId
      description: Grade id.
      required:
      - id
      properties:
        id:
          type: string
          example: 831ccbcb-1416-7fe2-e236-d324dfbe7424
    HotelBrand:
      type: object
      title: HotelBrand
      description: The brand of hotel.
      properties:
        brandCode:
          type: string
          description: The code of hotel brand.
          example: HY
        brandName:
          type: string
          description: The name of hotel brand.
          example: Global Hytt Corp.
    HotelChain:
      type: object
      title: HotelChain
      description: The chain of hotel.
      properties:
        chainCode:
          type: string
          description: The code of hotel chain.
          example: EM
        chainName:
          type: string
          description: The name of hotel chain.
          example: Mariott
    HotelPref:
      type: object
      title: HotelPref
      description: Travel preferences related to hotel.
      properties:
        hotelParentChains:
          type: array
          description: A list of hotel parent chains.
          items:
            $ref: '#/components/schemas/HotelChain'
        hotelBrands:
          type: array
          description: A list of hotel brands.
          items:
            $ref: '#/components/schemas/HotelBrand'
        hotelAmenityTypes:
          type: array
          description: A list of HotelAmenities.
          items:
            $ref: '#/components/schemas/HotelPrefAmenity'
        roomPreference:
          $ref: '#/components/schemas/RoomPreference'
        conditionalRates:
          type: array
          description: A list of conditional rates for rail.
          items:
            $ref: '#/components/schemas/ConditionalRate'
    HotelPrefAmenity:
      type: string
      description: Preferred hotel amenity.
      enum:
      - PARKING
      - FREE_PARKING
      - FREE_BREAKFAST
      - POOL
      - WIFI
      - FITNESS_CENTER
      - FAMILY_FRIENDLY
      - RECEPTION
      - SPA
      - RESTAURANT
      - BAR
      - TRANSPORTATION
      - PET_FRIENDLY
      - BUSINESS_CENTER
      - AIR_CONDITIONING
      - BEACH_ACCESS
      - LAUNDRY_SERVICES
      - ROOM_SERVICE
      - ACCESSIBLE
    IdentityDocument:
      type: object
      title: IdentityDocument
      description: "Identity document details. Currently supported documents are passport,\
        \ immigration document, \nknown traveler number, redress number and national\
        \ document.\n"
      oneOf:
      - $ref: '#/components/schemas/PassportWrapper'
      - $ref: '#/components/schemas/ImmigrationDocumentWrapper'
      - $ref: '#/components/schemas/RedressNumberWrapper'
      - $ref: '#/components/schemas/KnownTravelerNumberWrapper'
      - $ref: '#/components/schemas/NationalDocWrapper'
    Image:
      type: object
      title: Image
      description: An image with meta data. Either the `data` or `url` property must
        be supplied to load the image.
      properties:
        data:
          type: string
          format: byte
          example: 6935813e12584abda0e43d71cd2ea260
        dimensions:
          $ref: '#/components/schemas/Dimensions'
        url:
          type: string
          example: https://static.wixstatic.com/media/73f2e2_6935813e12584abda0e43d71cd2ea260~mv2.png/v1/fill/w_630,h_94,al_c,q_85,usm_0.66_1.00_0.01/Spotnana%403x.webp
    ImageGroup:
      type: object
      title: ImageGroup
      description: Contains different sizes of the same image.
      required:
      - images
      properties:
        caption:
          type: string
          description: Caption for the image.
          example: Exterior
        images:
          type: array
          description: List of images.
          items:
            $ref: '#/components/schemas/Image'
    ImmigrationDocument:
      type: object
      title: ImmigrationDocument
      description: Immigration document details.
      required:
      - docId
      - expiryDate
      - issueCountry
      properties:
        authorizedStayDuration:
          description: Duration of the stay authorized by the immigration document.
          $ref: '#/components/schemas/Duration'
        docId:
          type: string
          description: The ID of the immigration document.
          example: ImmigrationDocumentID
        expiryDate:
          description: The date on which the immigration document expires.
          $ref: '#/components/schemas/DateModel'
        issueCountry:
          type: string
          description: The country that issued the immigration document.
          example: IN
        issuedDate:
          description: The date on which the immigration document was issued.
          $ref: '#/components/schemas/DateModel'
        nationalityCountry:
          type: string
          example: IN
        reentryRequirementDuration:
          $ref: '#/components/schemas/Duration'
        type:
          type: string
          enum:
          - UNKNOWN
          - VISA
          example: VISA
    ImmigrationDocumentWrapper:
      type: object
      title: ImmigrationDocumentWrapper
      properties:
        immigrationDoc:
          $ref: '#/components/schemas/ImmigrationDocument'
    IncludeLocation:
      type: string
      title: IncludeLocation
      description: Various traveler notifications (e.g. emails) where the responses
        to this field should be included.
      enum:
      - BOOKING_CONFIRMATION_EMAILS
      - APPROVAL_EMAILS
      - COMPANY_REPORTS
      - CONSOLIDATED_ITINERARY_EMAILS
      example: APPROVAL_EMAILS
    InitiateBookingWorkflowIds:
      type: object
      description: The set of unique response ids associated with initiate booking
        workflow.
      properties:
        checkoutResponseId:
          type: string
          description: ID sent by the backend in the air checkout response.
        seatMapResponseId:
          type: string
          description: ID sent by the backend in the air seat map response.
    Int32Range:
      type: object
      properties:
        min:
          type: integer
          format: int32
          description: Minimum value - inclusive.
        max:
          type: integer
          format: int32
          description: Maximum value - inclusive.
    Int32RangeWrapper:
      type: object
      title: Int32RangeWrapper
      properties:
        iRange:
          $ref: '#/components/schemas/Int32Range'
    Int32Wrapper:
      type: object
      title: Int32Wrapper
      properties:
        i:
          type: integer
          format: int32
    Int64Wrapper:
      type: object
      title: Int64Wrapper
      properties:
        l:
          type: integer
          format: int64
    IntListWrapper:
      type: object
      title: IntListWrapper
      properties:
        iList:
          type: array
          items:
            type: integer
            format: int32
    ItemType:
      type: string
      description: Type of payment item eligible for this fop rule
      enum:
      - SERVICE_FEE
      - TRAVEL_TICKET
      - SEAT
      - BAGGAGE
      - EARLY_BIRD
      example: SEAT
    ItineraryInfo:
      type: object
      description: Describes the detailed itinerary information provided in the search
        response.
      properties:
        itineraries:
          type: array
          items:
            $ref: '#/components/schemas/AirItin'
            description: List of itineraries available for selection in the search
              results.
        flightData:
          type: array
          description: The set of flights and their details that are contained in
            this response.
          items:
            $ref: '#/components/schemas/FlightCommon'
    KeywordWithReasonListWrapper:
      type: object
      title: KeywordWithReasonListWrapper
      properties:
        keywordWithReasonList:
          $ref: '#/components/schemas/KeywordsWithReasonList'
    KeywordsWithReasonList:
      type: object
      properties:
        keywords:
          type: array
          items:
            $ref: '#/components/schemas/RestrictedKeywordsWithReason'
    KnownTravelerNumber:
      type: object
      title: KnownTravelerNumber
      description: Information about the Known Traveler Number (KTN).
      required:
      - number
      - issueCountry
      properties:
        number:
          type: string
          example: '12345'
        issueCountry:
          type: string
          example: US
    KnownTravelerNumberWrapper:
      type: object
      title: KnownTravelerNumberWrapper
      properties:
        ktn:
          $ref: '#/components/schemas/KnownTravelerNumber'
    Latlng:
      title: Latlng
      description: Latitude and Longitude for a Location
      type: object
      required:
      - latitude
      - longitude
      properties:
        latitude:
          type: number
          description: Latitude of the Location
          format: double
          example: 77.1025
        longitude:
          type: number
          description: Longitude of the Location
          format: double
          example: 28.7041
    LayoutAmenity:
      type: object
      title: LayoutAmenity
      description: Layout Amenity properties
      properties:
        displayText:
          type: string
          example: 1-2-1 seat layout
        directAisleAccess:
          type: string
          example: 'yes'
    LayoutAmenityWrapper:
      type: object
      title: LayoutAmenityWrapper
      properties:
        layoutAmenity:
          $ref: '#/components/schemas/LayoutAmenity'
    Leg:
      oneOf:
      - $ref: '#/components/schemas/NewLeg'
      - $ref: '#/components/schemas/ExistingLeg'
    LegApplicability:
      type: object
      description: Describes the leg level applicability and the leg association.
      required:
      - applicability
      properties:
        applicability:
          type: string
          enum:
          - PER_LEG
          - ALL_LEGS
          example: PER_LEG
        legId:
          type: string
          description: Identifier for the leg. Present when applicability is PER_LEG.
          example: leg_0
    LegDepartureArrivalTimeRangeFilter:
      type: object
      description: 'Provides information on how to filter the range of leg departure
        and arrival times (e.g. departing between

        6 a.m.- 11 a.m. and arriving between 11 a.m. - 4 p.m.).

        '
      properties:
        legIndex:
          type: integer
          format: int32
        departure:
          $ref: '#/components/schemas/TimeRange'
        arrival:
          $ref: '#/components/schemas/TimeRange'
    LegPrice:
      type: object
      description: The fare breakdown per leg (if the information is available).
      properties:
        legId:
          type: string
          description: The leg ID.
          example: leg-id
        amount:
          description: 'The price breakdown of leg. If the price breakdown of leg
            is not available, price will

            denote the price of entire itinerary.

            '
          $ref: '#/components/schemas/FareAmount'
        travelerPrices:
          type: array
          items:
            $ref: '#/components/schemas/PerTravelerPrice'
          description: Price breakdown for each traveler type, if available.
        airlineFee:
          description: 'Total OB Fees for this leg. OB fees are generally charged
            by airlines in addition to the base ticket price.

            These fees can include service or credit card fees charged by the airline.

            '
          $ref: '#/components/schemas/Money'
    LegRuleInfo:
      type: object
      title: LegRuleInfo
      description: Contains raw fare rules of a particular leg
      properties:
        origin:
          type: string
        destination:
          type: string
        travelDate:
          $ref: '#/components/schemas/DateTimeLocal'
        flights:
          type: array
          items:
            $ref: '#/components/schemas/FlightInLegRule'
        fareBasisRules:
          type: array
          items:
            $ref: '#/components/schemas/FareBasisRule'
    LegSearchParams:
      type: object
      properties:
        searchId:
          type: string
          description: The unique ID returned by the air search response of the previous
            leg.
        selectedRateOptionId:
          type: string
          description: The unique ID identifying the selected flights in the previous
            leg's response.
        legIndex:
          type: integer
          format: int32
          description: The index ID for the leg for which the results are to be returned
            for this request.
        asyncRouteHappy:
          type: boolean
          description: 'Designates if RouteHappy content is fetched asynchronously.
            If true, RouteHappy content will be fetched

            asynchronously and not returned in response.

            '
        pageNumber:
          type: integer
          format: int32
          description: The page number for which data is to be fetched. If pageNumber
            is 0, the complete air search response (without pagination) will be returned.
        pageSize:
          type: integer
          format: int32
          description: Optional parameter to specify the max number of results per
            page. If not set or set as 0, default page size of 50 is used.
    LegalEntityId:
      type: object
      title: LegalEntityId
      description: The ID of the legal entity.
      required:
      - id
      properties:
        id:
          type: string
          example: fc1ccbce-8413-4fe9-b233-a324dfbe7421
    LegalEntityIdListWrapper:
      type: object
      title: LegalEntityIdListWrapper
      properties:
        legalEntityIdList:
          type: array
          items:
            $ref: '#/components/schemas/LegalEntityId'
    LegalEntityIdWrapper:
      type: object
      title: LegalEntityIdWrapper
      properties:
        legalEntityId:
          $ref: '#/components/schemas/LegalEntityId'
    Length:
      type: object
      title: Length
      description: Specifies the length or a distance.
      required:
      - length
      - unit
      properties:
        length:
          type: number
          description: Distance from search point.
          format: double
          example: 150
        unit:
          type: string
          description: Unit of measure being applied.
          enum:
          - UNKNOWN_UNIT
          - KM
          - MILE
          example: MILE
    LengthWrapper:
      type: object
      title: LengthWrapper
      properties:
        length:
          $ref: '#/components/schemas/Length'
    Location:
      title: Location
      description: Location details
      type: object
      required:
      - name
      properties:
        countryCode:
          type: string
          description: Unique country code for a location
          example: TR
        countryName:
          type: string
          description: Full name of the country
          example: Turkey
        googlePlaceId:
          type: string
          description: Unique place ID for the location assigned by Google
          example: ChIJL_P_CXMEDTkRw0ZdG-0GVvw
        latlong:
          $ref: '#/components/schemas/Latlng'
        name:
          type: string
          description: Full name of the Location
          example: Denver
        stateName:
          type: string
          description: Full name of the state
          example: Colorado
    LoungePolicy:
      type: object
      description: Information about the lounge policy.
      required:
      - description
      properties:
        description:
          type: string
          description: Lounge policy description text (may include whether a fee is
            charged).
          example: Lounge access for a fee
    LoyaltyInfo:
      type: object
      title: LoyaltyInfo
      description: Loyalty Info details.
      required:
      - id
      - type
      - issuedBy
      properties:
        appliedTo:
          type: array
          items:
            type: string
            example: TAJ
        id:
          type: string
          example: firstId
        issuedBy:
          type: string
          example: firstIssuedBy
        type:
          type: string
          enum:
          - UNKNOWN_TYPE
          - AIR
          - HOTEL
          - CAR
          - RAIL
          example: AIR
    MandatoryParamForCheckout:
      type: object
      description: Describes the parameters which must be collected from travelers
        during the checkout.
      required:
      - travelerApplicability
      properties:
        travelerApplicability:
          $ref: '#/components/schemas/TravelerApplicability'
        parameter:
          type: string
          description: The mandatory parameter to be collected.
          enum:
          - DATE_OF_BIRTH
          - BILLING_ADDRESS
          - POSTAL_CODE
          - CVV
          - PASSPORT_ID
          - PASSPORT_EXPIRY_DATE
          - PASSPORT_ISSUE_COUNTRY
          - NATIONALITY
          - GENDER
          - CVV_ON_PERSONAL_CARD
          example: DATE_OF_BIRTH
    MaskAmenity:
      type: object
      title: MaskAmenity
      description: Properties associated with the mask amenity.
      properties:
        displayText:
          type: string
          description: The basic text displayed for mask amenity.
          example: Face covering required
        maskDescription:
          type: string
          description: A full description of the mask amenity.
          example: All passengers are required to wear a face covering throughout
            their journey
        maskAttrDescription:
          type: string
          example: 'yes'
    MaskAmenityWrapper:
      type: object
      title: MaskAmenityWrapper
      properties:
        maskAmenity:
          $ref: '#/components/schemas/MaskAmenity'
    MealPref:
      type: object
      title: MealPref
      description: Meal preferences.
      properties:
        exclMealPrefs:
          type: array
          items:
            $ref: '#/components/schemas/MealType'
        inclMealPrefs:
          type: array
          items:
            $ref: '#/components/schemas/MealType'
        specialMealDescription:
          type: string
          example: Veg only meal
    MealType:
      type: string
      title: MealType
      description: Type of the meal
      enum:
      - UNKNOWN_MEAL
      - AVML
      - BBML
      - BLML
      - CHML
      - DBML
      - FPML
      - GFML
      - HFML
      - HNML
      - KSML
      - LCML
      - LFML
      - LPML
      - LSML
      - MOML
      - NLML
      - NSML
      - ORML
      - PFML
      - RVML
      - SFML
      - SPML
      - VGML
      - VJML
      - VLML
      - VOML
      example: VGML
    MerchantFeeInfo:
      title: MerchantFeeInfo
      description: Describes the merchant fee applicable on the itinerary.
      required:
      - amount
      properties:
        amount:
          $ref: '#/components/schemas/FareAmount'
    MigrateUnusedCreditsToCompanyCreditsRequest:
      type: object
      title: MigrateUnusedCreditsToCompanyCreditsRequest
      description: Request body for migrating unused credits
      required:
      - paymentSourceIds
      - updateCreditUsageTypeTo
      properties:
        paymentSourceIds:
          type: array
          items:
            type: string
            description: Payment source ids of the unused credits to be migrated
        updateCreditUsageTypeTo:
          $ref: '#/components/schemas/CreditUsageType'
          description: The type of credit usage to be updated to. This can be either
            COMPANY or PERSONAL. If passed Company, it means you are trying to update
            from personal to Company
    MigrateUnusedCreditsToCompanyCreditsResponse:
      type: object
      title: MigrateUnusedCreditsToCompanyCreditsResponse
      description: Response body for migrating unused credits
      properties:
        migrationStatus:
          type: array
          items:
            $ref: '#/components/schemas/MigrationResponseInfo'
            description: Information about the migration of the unused credit
    MigrationResponseInfo:
      type: object
      title: MigrationResponseInfo
      description: Information regarding the migration of the payment source Id.
      properties:
        paymentSourceId:
          type: string
          description: Unique identifier of the payment source.
          example: d059044a-cacd-4b53-883d-7355530f54e7
        status:
          $ref: '#/components/schemas/MigrationStatus'
    MigrationStatus:
      type: string
      description: Status of the credit.
      enum:
      - STATUS_UNKNOWN
      - SUCCESS
      - FAILED
    MinMaxAmount:
      type: object
      title: MinMaxAmount
      description: Payment amount details if slider payment function is linear.
      properties:
        minLimit:
          $ref: '#/components/schemas/Money'
          description: The minimum amount that must be charged using this fop
        maxLimit:
          $ref: '#/components/schemas/Money'
          description: The maximum amount that could be charged using this fop
    MinMaxAmountWrapper:
      type: object
      title: MinMaxAmountWrapper
      description: Min max amount wrapper.
      properties:
        minMaxAmount:
          $ref: '#/components/schemas/MinMaxAmount'
    ModuleId:
      type: string
      title: ModuleId
      description: Module id
      enum:
      - BOOKING
      - AGENT
    Money:
      type: object
      title: Money
      description: 'Money object containing details such as the amount, the currency
        code, and the converted amount.

        '
      required:
      - amount
      - currencyCode
      properties:
        amount:
          type: number
          format: double
          description: The numeric value for the amount of money.
          example: 510
        currencyCode:
          type: string
          description: The 3-letter currency code for the money amount (defined using
            ISO 4217 standard).
          example: GBP
        convertedAmount:
          type: number
          format: double
          description: 'The converted currency and amount that has been converted
            (if a currency conversion has been requested).

            For example, if the call requests that money be sent in a specified currency
            (because the frontend requested

            the backend to send money in the user''s preferred currency).

            '
          example: 715.42
        convertedCurrency:
          type: string
          description: The 3-letter currency code for the converted currency (defined
            using ISO 4217 standard).
          example: USD
        otherCoinage:
          type: array
          title: OtherCoinage
          description: List of the dollar amount in other coinage systems like reward
            points, cryptocurrency etc.
          items:
            type: object
            properties:
              coinageCode:
                $ref: '#/components/schemas/PaymentMethod'
                description: Payment method
              amount:
                type: number
                format: double
                example: 1000
              conversionRate:
                type: number
                format: double
                description: 1 coin in this system equals to how many currency value
                example: 0.01
              preferredCurrencyConversionRate:
                type: number
                format: double
                description: 1 coin in this system equals to how many currency value
                example: 0.01
    MoneyRange:
      type: object
      properties:
        min:
          $ref: '#/components/schemas/Money'
          description: Minimum value - inclusive.
        max:
          $ref: '#/components/schemas/Money'
          description: Maximum value - inclusive.
    MoneyWrapper:
      type: object
      title: MoneyWrapper
      properties:
        money:
          $ref: '#/components/schemas/Money'
    MultiAirports:
      type: object
      title: MultiAirports
      description: List of airports. All airports should belong to the same country.
      required:
      - airports
      properties:
        airports:
          type: array
          description: List of 3-letter airport IATA codes.
          items:
            type: string
            description: The airport code identifying a specific airport. For example,
              JFK or EWR.
            example: JFK
            pattern: ^[A-Z]{3}$
    MultiSelectOptionGroup:
      title: MultiSelectOptionGroup
      description: 'Describes the baggage option group which provides the users with
        multiple baggage options. Users can select one

        or more of them. For example:

        (1) 1st 20kg bag for $20

        (2) 2nd 20kg bag for $30

        (3) 3rd bag of 40kg for $40

        '
      type: object
      required:
      - multiSelectOptions
      properties:
        multiSelectOptions:
          type: array
          description: A list of baggage options that may be selected.
          items:
            $ref: '#/components/schemas/SimpleBagOption'
    MultiTicketFilter:
      type: object
      properties:
        hideMultiTicket:
          type: boolean
          description: Designates whether multi ticket options are to be shown. If
            true, only single ticket itineraries are returned.
    NGSFareCategory:
      type: string
      enum:
      - UNKNOWN_NGS_CATEGORY
      - BASE
      - STANDARD
      - ENHANCED
      - PREMIUM
      - LUXURY
      - ULTRA_LUXURY
      default: UNKNOWN_NGS_CATEGORY
    Name:
      type: object
      title: Name
      description: Full name containing first, middle, last (family) names, and suffix.
      required:
      - given
      - family1
      properties:
        family1:
          type: string
          description: Last (family) name.
          example: Gandas
        family2:
          type: string
          example: FamilyTwo
        given:
          type: string
          description: First (given) name.
          example: Vichitr
        middle:
          type: string
          description: Middle name.
          example: Kumar
        suffix:
          $ref: '#/components/schemas/NameSuffix'
          description: Suffix used with the name. For example SR or JR.
          example: SR
        preferred:
          type: string
          description: Informal preferred name added by traveler. This is not used
            on any PNR or tickets
          example: Don
    NameSuffix:
      title: NameSuffix
      description: Suffix for name
      type: string
      enum:
      - NAME_SUFFIX_UNKNOWN
      - SR
      - JR
      - MD
      - PHD
      - II
      - III
      - IV
      - DO
      - ATTY
      - V
      - VI
      - ESQ
      - DC
      - DDS
      - VM
      - JD
      - SECOND
      - THIRD
      example: SR
    NationalDoc:
      type: object
      title: NationalDoc
      description: National Document Id details.
      required:
      - docId
      - issueCountry
      properties:
        docId:
          type: string
          description: Unique id identifying the national document.
          example: NationalDocId
        issueCountry:
          type: string
          description: IS0 2 letter country code of the country issuing this id.
          example: IN
        issuedDate:
          $ref: '#/components/schemas/DateModel'
        expiryDate:
          $ref: '#/components/schemas/DateModel'
        type:
          type: string
          enum:
          - DNI
          - NIE
          example: DNI
    NationalDocWrapper:
      type: object
      title: NationalDocWrapper
      properties:
        nationalDoc:
          $ref: '#/components/schemas/NationalDoc'
    NewLeg:
      type: object
      title: NewLeg
      example:
        source: SFO
        destination: SEA
        date: 2022-01-21T17:00
      description: New leg to be added in the pnr.
      properties:
        origin:
          type: string
          description: 3 letter IATA airport code for origin
          pattern: ^[A-Z]{3}$
          example: SFO
        destination:
          type: string
          description: 3 letter IATA airport code for origin
          pattern: ^[A-Z]{3}$
          example: SFO
        date:
          $ref: '#/components/schemas/DateTimeLocal'
    NumStopsPref:
      type: object
      title: NumStopsPref
      description: Preferred number of stops.
      required:
      - numOfStops
      properties:
        numOfStops:
          type: integer
          format: int32
          example: 34
    Office:
      type: object
      title: Office
      description: Office details.
      required:
      - id
      - name
      properties:
        address:
          $ref: '#/components/schemas/PostalAddress'
        id:
          $ref: '#/components/schemas/OfficeId'
        name:
          type: string
          example: Office
        latlng:
          $ref: '#/components/schemas/Latlng'
        taxId:
          type: string
          example: '133232'
    OfficeId:
      type: object
      title: OfficeId
      description: The unique ID of the office.
      required:
      - id
      properties:
        id:
          type: string
          description: The value of the unique ID for the office.
          example: 531ccbce-8413-4fe9-b233-a324dfbe7421
    OfficeIdListWrapper:
      type: object
      title: OfficeIdListWrapper
      properties:
        officeIdList:
          type: array
          description: A list of office IDs.
          items:
            $ref: '#/components/schemas/OfficeId'
    OfficeIdWrapper:
      type: object
      title: OfficeIdWrapper
      properties:
        officeId:
          $ref: '#/components/schemas/OfficeId'
    Option:
      type: object
      title: Option
      description: Answer option for a question
      required:
      - displayCode
      properties:
        displayCode:
          type: string
          description: The code which is sent in answer response.
        displayValue:
          type: string
          description: The text to be displayed to the user beside this option.
    OptionInfo:
      type: object
      title: OptionInfo
      description: Options related information for the question.
      required:
      - source
      properties:
        source:
          $ref: '#/components/schemas/OptionSource'
          default: MANUAL
        sourceMetadata:
          $ref: '#/components/schemas/OptionSourceMetadata'
        totalNumOptions:
          type: integer
          description: Total number of options
        options:
          type: array
          description: "Available options for the question. This will contain only\
            \ max 10 options if only \nsummary is requested.\n"
          items:
            $ref: '#/components/schemas/Option'
    OptionSource:
      type: string
      enum:
      - MANUAL
      - COMPANY_CONFIG
      description: Option source
      example: MANUAL
    OptionSourceMetadata:
      type: object
      title: OptionSourceMetadata
      description: Metadata information for the option source.
      oneOf:
      - $ref: '#/components/schemas/CompanyConfigSourceWrapper'
    OrganizationAgencyId:
      type: object
      title: OrganizationAgencyId
      description: Agency id for the organization.
      deprecated: true
      required:
      - id
      properties:
        id:
          type: string
    OrganizationId:
      type: object
      title: OrganizationId
      description: Organization id for traveler.
      required:
      - id
      properties:
        id:
          type: string
    OtherAncillaryDetail:
      type: object
      description: Ancillary detail which needs to be updated.
      required:
      - ancillaryType
      - flightIndex
      - legIndex
      properties:
        ancillaryType:
          $ref: '#/components/schemas/AncillaryType'
        flightIndex:
          type: integer
          description: Index referencing the flight for which the changes are requested.
          example: 1
        legIndex:
          type: integer
          description: Index referencing the leg for which the changes are requested.
          example: 1
    OtherServiceInformation:
      type: object
      description: Other Service Information (OSI) to be added.
      title: OtherServiceInformation
      required:
      - info
      properties:
        info:
          type: string
          description: Free text field for adding Other Service Information.
          example: VIP Passenger.
        flightIndex:
          type: integer
          description: Index referencing the flight for which the changes are requested.
          example: 2
    OwnershipLabel:
      title: OwnershipLabel
      type: string
      description: Ownership label of the card whether it is personal, corporate or
        central.
      enum:
      - CORPORATE
      - PERSONAL
      - CENTRAL
    POSInfo:
      type: object
      description: Point of sale information
      required:
      - source
      properties:
        source:
          description: The third party source for the point of sale
          $ref: '#/components/schemas/ThirdPartySource'
        posDescriptor:
          type: string
          description: The identifier for the point of sale. This would be the PCC
            for GDS bookings.
          example: 6CA4
    PaginationResponseParams:
      type: object
      title: PaginationResponseParams
      description: Pagination parameters for response.
      required:
      - numResults
      - numPages
      properties:
        numResults:
          type: integer
          format: int32
          description: Total number of results in the paginated list.
        numPages:
          type: integer
          format: int32
          description: Total number of pages in the paginated list.
    PassengerAge:
      title: PassengerAge
      type: object
      required:
      - numYears
      description: Age of the traveler
      properties:
        numYears:
          type: integer
          description: Age of the passenger
          example: 22
    PassengerCapacityAmenity:
      type: object
      title: PassengerCapacityAmenity
      description: Passenger capacity amenity properties.
      properties:
        displayText:
          type: string
          example: Standard passenger capacity
        passengerCapacityDescription:
          type: string
          example: Ticket sales are not limited for this flight
        passengerCapacityAttrDescription:
          type: string
          example: 'no'
    PassengerCapacityAmenityWrapper:
      type: object
      title: PassengerCapacityAmenityWrapper
      properties:
        passengerCapacityAmenity:
          $ref: '#/components/schemas/PassengerCapacityAmenity'
    PassengerType:
      title: PassengerType
      description: Type of passenger
      type: string
      enum:
      - UNKNOWN_PASSENGER_TYPE
      - ADULT
      - CHILD
      - INFANT
      - INFANT_ON_LAP
      - YOUTH
      - SENIOR
      - TEEN
      example: ADULT
    Passport:
      type: object
      title: Passport
      description: Passport details.
      required:
      - docId
      - expiryDate
      - issueCountry
      - nationalityCountry
      properties:
        docId:
          type: string
          example: PassportID
        expiryDate:
          $ref: '#/components/schemas/DateModel'
        issueCountry:
          type: string
          example: IN
        issuedDate:
          $ref: '#/components/schemas/DateModel'
        nationalityCountry:
          type: string
          example: IN
        type:
          type: string
          enum:
          - UNKNOWN
          - REGULAR
          example: REGULAR
    PassportWrapper:
      type: object
      title: PassportWrapper
      properties:
        passport:
          $ref: '#/components/schemas/Passport'
    PaymentInfo:
      type: object
      title: PaymentInfo
      description: Payment information.
      required:
      - card
      properties:
        applicableTo:
          type: array
          items:
            type: string
            enum:
            - UNKNOWN_APPLICABLE_TO
            - AIR
            - HOTEL
            - RAIL
            - CAR
            - SERVICE_FEE
            example: HOTEL
        card:
          $ref: '#/components/schemas/Card'
        accessType:
          $ref: '#/components/schemas/CreditCardAccessType'
        access:
          $ref: '#/components/schemas/CreditCardAccess'
    PaymentItem:
      type: object
      description: The payment item eligible for associated payment rules
      properties:
        itemType:
          $ref: '#/components/schemas/ItemType'
          description: Type of payment item eligible for this fop rule
        fareComponent:
          type: array
          items:
            $ref: '#/components/schemas/FareComponent'
            description: List of fare components of associated payment item
    PaymentMethod:
      type: string
      description: Payment method
      enum:
      - PAYMENT_METHOD_UNKNOWN
      - CREDIT_CARD
      - BREX_POINTS
      - CASH
      - QANTAS_POINTS
      - VENDOR_PROGRAM_PAYMENT
      - DELAYED_INVOICING
      - FLIGHT_CREDITS
      - QANTAS_TRAVEL_FUND
      - CUSTOM_VIRTUAL_PAYMENT
      example: BREX_POINTS
    PaymentRule:
      type: object
      description: The payment rule applicable for a booking
      properties:
        paymentSplitDetails:
          $ref: '#/components/schemas/PaymentSplitDetails'
          description: Payment details of slider payment function
        paymentSourceIds:
          type: array
          items:
            type: string
            format: uuid
            description: Unique identifier identifying this payment source
            example: f49d00fe-1eda-4304-ba79-a980f565281d
        allowAddCard:
          type: boolean
          description: Whether to allow user to add a new card for the selected split
            payment.
        splitCriterion:
          $ref: '#/components/schemas/PaymentSplitCriterion'
          description: Defines the criteria for splitting a payment
        unusablePaymentSources:
          type: array
          items:
            $ref: '#/components/schemas/UnusablePaymentSource'
          description: List of payment sources that are not usable for this payment
            rule
        paymentSourceConditions:
          type: array
          items:
            $ref: '#/components/schemas/PaymentSourceCondition'
          description: List of conditions for payment sources
    PaymentSourceCondition:
      type: object
      title: PaymentSourceCondition
      description: The payment source fop condition
      discriminator:
        propertyName: type
        mapping:
          CARD: '#/components/schemas/CardCondition'
      oneOf:
      - $ref: '#/components/schemas/CardCondition'
    PaymentSourcePrePaymentAdditionalInformation:
      type: object
      description: Payment source specific additional pre-payment information needed
        to initialize payment provider
      properties:
        loyaltyAccountNumber:
          type: string
          description: Loyalty account number in case rewards program is used
        loyaltyProgramName:
          type: string
          description: Loyalty program name in case rewards program is used
        canStoreCreditCard:
          type: boolean
          description: Is payment provider allowed to store user's credit card information
    PaymentSourcePrePaymentInformation:
      type: object
      description: Information shared by a payment source post initialization and
        before payment (like payment request Id, required fields, etc.)
      properties:
        paymentSourceId:
          type: string
          format: uuid
          description: Unique identifier identifying this payment source.
          example: f49d00fe-1eda-4304-ba79-a980f565281d
        paymentRequestId:
          type: string
          description: Payment source initialization request id
          example: XHY675ER7Y2
        additionalInformation:
          $ref: '#/components/schemas/PaymentSourcePrePaymentAdditionalInformation'
    PaymentSplitCriterion:
      type: object
      description: The criterion for the payment split.
      properties:
        accessType:
          description: Access Type of the Payment Sources applicable for a portion
            of payment.
          $ref: '#/components/schemas/AccessType'
    PaymentSplitDetails:
      type: object
      title: PaymentSplitDetails
      description: Payment details of slider payment function.
      oneOf:
      - $ref: '#/components/schemas/MinMaxAmountWrapper'
      - $ref: '#/components/schemas/SplitAmountWrapper'
    PaymentVerificationInfo:
      type: object
      title: PaymentVerificationInfo
      description: VerificationInfo required for the payment operation.
      oneOf:
      - $ref: '#/components/schemas/StripeVerificationInfoWrapper'
      - $ref: '#/components/schemas/UrlVerificationInfoWrapper'
      - $ref: '#/components/schemas/ThreeDSecure2VerificationInfoWrapper'
      - $ref: '#/components/schemas/RazorpayVerificationInfoWrapper'
      properties:
        gatewayIdentifier:
          $ref: '#/components/schemas/GatewayIdentifier'
        paymentKey:
          type: string
          description: Serialized key to identify the payment executed on payment
            gateway that will be used for post payment verification.
    PerTravelerPrice:
      type: object
      required:
      - travelerType
      - travelerAge
      - amount
      - numTravelers
      properties:
        travelerType:
          description: Type of passenger.
          $ref: '#/components/schemas/PassengerType'
        travelerAge:
          description: Age of passenger.
          $ref: '#/components/schemas/PassengerAge'
        numTravelers:
          type: integer
          description: Number of travelers of the specified type and age.
          example: 3
        amount:
          description: Leg fare per passenger of the specified type and age.
          $ref: '#/components/schemas/FareAmount'
        airlineFee:
          description: Ob fees applicable on the traveler fare.
          $ref: '#/components/schemas/Money'
    PercentageWrapper:
      type: object
      title: PercentageWrapper
      properties:
        percentage:
          type: number
          format: double
    Persona:
      type: string
      title: Persona
      description: Persona of the user
      enum:
      - UNKNOWN_PERSONA
      - EMPLOYEE
      - GUEST
      - PERSONAL
      - RELATIVE
      - ADHOC
      example: EMPLOYEE
    PersonaListWrapper:
      type: object
      title: PersonaListWrapper
      properties:
        personaList:
          type: array
          items:
            $ref: '#/components/schemas/Persona'
    PersonaWrapper:
      type: object
      title: PersonaWrapper
      properties:
        persona:
          $ref: '#/components/schemas/Persona'
    PhoneNumber:
      type: object
      title: PhoneNumber
      description: Properly formatted phone number.
      properties:
        countryCode:
          type: integer
          format: int32
          description: two digit country code
          example: 91
        countryCodeSource:
          type: string
          enum:
          - UNSPECIFIED
          - FROM_NUMBER_WITH_PLUS_SIGN
          - FROM_NUMBER_WITH_IDD
          - FROM_NUMBER_WITHOUT_PLUS_SIGN
          - FROM_DEFAULT_COUNTRY
          example: FROM_NUMBER_WITH_PLUS_SIGN
        extension:
          description: phone number extension
          type: string
          example: '222'
        isoCountryCode:
          description: ISO alpha-2 code
          type: string
          example: IN
        italianLeadingZero:
          type: boolean
          default: false
          example: true
        nationalNumber:
          type: integer
          format: int64
          example: 8150
        numberOfLeadingZeros:
          type: integer
          format: int32
          default: 0
          example: 1
        preferredDomesticCarrierCode:
          type: string
          example: '7'
        rawInput:
          type: string
          example: '77777'
        type:
          type: string
          enum:
          - UNKNOWN_TYPE
          - MOBILE
          - LANDLINE
          example: MOBILE
    PnrItinerary:
      description: The pnr's itinerary for which the seat map needs to be returned.
      type: object
      required:
      - pnrId
      properties:
        pnrId:
          description: The PNR ID.
          type: string
          example: '1234567812'
    PnrItineraryWrapper:
      type: object
      title: PnrItineraryWrapper
      properties:
        pnr:
          $ref: '#/components/schemas/PnrItinerary'
    PnrNoteRemark:
      title: PnrNoteRemark
      description: Object for the Pnr Remarks
      required:
      - remark
      properties:
        remark:
          $ref: '#/components/schemas/RemarkObject'
    PnrPolicyId:
      title: PnrPolicyId
      description: Pnr Policy id
      type: object
      required:
      - id
      - version
      properties:
        id:
          type: string
          description: Policy id
        version:
          type: string
          description: version of the policy
        policyName:
          type: string
          description: Name of the policy applied.
        approvalType:
          type: string
          enum:
          - HARD_APPROVAL
          - SOFT_APPROVAL
          - PASSIVE_APPROVAL
          example: SOFT_APPROVAL
        policyType:
          type: string
          enum:
          - GLOBAL
          - DEFAULT
          - GROUP
          example: GLOBAL
    PnrPolicyInfo:
      title: PnrPolicyInfo
      description: Policy Info applicable for the associated itinerary
      type: object
      properties:
        policies:
          type: array
          description: List of policies applied to the itinerary.
          items:
            $ref: '#/components/schemas/PnrPolicyId'
        ruleResultInfos:
          type: array
          items:
            $ref: '#/components/schemas/PolicyRuleResultInfo'
    PnrStatus:
      title: PnrStatus
      type: string
      enum:
      - UNKNOWN
      - INITIATED
      - CANCELLED
      - CONFIRMED
      - GROUP_BOOKING_ON_REQUEST
      - WAITLISTED
      - PENDING
      - AIRLINE_UPGRADE
      - WAITLIST_CONFIRMED
      - BOOKING_DENIED_CONTACT_SUPPORT
      - NO_SHOW
      - CONTACT_SUPPORT
      - STATUS_CHANGED_CONTACT_SUPPORT
      - SCHEDULE_CHANGE
      - SEGMENT_REQUEST
      - SCHEDULE_CHANGE_WAITLISTED_BOOKING
      - REQUEST_PENDING
      - WAITLISTED_NOT_CONFIRMED
      - SCHEDULE_CHANGE_NOT_CONFIRMED
      - SCHEDULE_CHANGE_PENDING_STATUS
      - MIS_CONNECTION
      - REQUESTED
      - TICKETED
      - VOIDED
      - CANCELLED_BY_VENDOR
      - CANCELLATION_IN_PROGRESS
      - REINSTATED
      - BOOKING_ON_HOLD
      - AIRLINE_CONTROL
      - MODIFIED
      - PAYMENT_DECLINED
      - INOPERATIVE
      - UNCONFIRMED
      description: Status of PNR
      example: CONFIRMED
    PolicyAction:
      type: object
      description: Action that is required / done for policy.
      oneOf:
      - $ref: '#/components/schemas/PolicyFlagActionWrapper'
      - $ref: '#/components/schemas/PolicyHideActionWrapper'
      - $ref: '#/components/schemas/PolicyAlertOnSelectionActionWrapper'
      - $ref: '#/components/schemas/PolicyTakeApprovalActionWrapper'
      - $ref: '#/components/schemas/PolicyPreventBookingActionWrapper'
    PolicyAlertOnSelectionAction:
      type: object
      title: PolicyAlertOnSelectionAction
      properties:
        message:
          type: string
    PolicyAlertOnSelectionActionWrapper:
      type: object
      title: PolicyAlertOnSelectionActionWrapper
      properties:
        alertOnSelection:
          $ref: '#/components/schemas/PolicyAlertOnSelectionAction'
    PolicyConstValue:
      type: object
      oneOf:
      - $ref: '#/components/schemas/Int32Wrapper'
      - $ref: '#/components/schemas/Int64Wrapper'
      - $ref: '#/components/schemas/StringWrapper'
      - $ref: '#/components/schemas/DoubleWrapper'
      - $ref: '#/components/schemas/BoolWrapper'
      - $ref: '#/components/schemas/IntListWrapper'
      - $ref: '#/components/schemas/DoubleListWrapper'
      - $ref: '#/components/schemas/StringListWrapper'
      - $ref: '#/components/schemas/MoneyWrapper'
      - $ref: '#/components/schemas/LengthWrapper'
      - $ref: '#/components/schemas/PostalAddressWrapper'
      - $ref: '#/components/schemas/UserOrgIdWrapper'
      - $ref: '#/components/schemas/LegalEntityIdWrapper'
      - $ref: '#/components/schemas/OfficeIdWrapper'
      - $ref: '#/components/schemas/UserOrgIdListWrapper'
      - $ref: '#/components/schemas/LegalEntityIdListWrapper'
      - $ref: '#/components/schemas/OfficeIdListWrapper'
      - $ref: '#/components/schemas/RatingWrapper'
      - $ref: '#/components/schemas/PercentageWrapper'
      - $ref: '#/components/schemas/Int32RangeWrapper'
      - $ref: '#/components/schemas/DoubleRangeWrapper'
      - $ref: '#/components/schemas/PersonaWrapper'
      - $ref: '#/components/schemas/PersonaListWrapper'
      - $ref: '#/components/schemas/TravelClassHierarchyWrapper'
      - $ref: '#/components/schemas/KeywordWithReasonListWrapper'
      - $ref: '#/components/schemas/WorkerTypeWrapper'
      - $ref: '#/components/schemas/WorkerTypeListWrapper'
    PolicyFilter:
      type: object
      properties:
        onlyInPolicy:
          type: boolean
          description: If true, only in-policy itineraries are returned.
    PolicyFlagAction:
      type: object
      title: PolicyFlagAction
      properties:
        message:
          type: string
    PolicyFlagActionWrapper:
      type: object
      title: PolicyFlagActionWrapper
      properties:
        flag:
          $ref: '#/components/schemas/PolicyFlagAction'
    PolicyHideActionWrapper:
      type: object
      title: PolicyHideActionWrapper
      properties:
        hide:
          type: boolean
    PolicyPredicate:
      type: string
      description: This contains the enum mentioning the policy rule which was violated.
      enum:
      - UNKNOWN_PREDICATE_STRING
      - MAX_FARE_PER_TRAVELLER_VIOLATION
      - FARE_MORE_THAN_MINIMUM
      - FARE_MORE_THAN_MEDIAN
      - FARE_LESS_THAN_MEDIAN
      - FARE_MORE_THAN_LLF
      - MAX_FARE_PER_TRAVELLER_VIOLATION_INCLUDING_TAX
      - MAX_FARE_PER_TRAVELLER_VIOLATION_EXCLUDING_TAX
      - HOTEL_PAYMENT_OPTIONS_VIOLATION
      - RAIL_BOOKING_WINDOW_GAP_VIOLATION
      - RAIL_TRAVEL_CLASS_VIOLATION
      - RAIL_TICKET_REFUNDABLE_VIOLATION
      - RAIL_MAX_BOOKING_PRICE_VIOLATION_INCLUDING_TAX
      - RAIL_MAX_BOOKING_PRICE_VIOLATION_EXCLUDING_TAX
      - AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_INCLUDING_TAX
      - AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_EXCLUDING_TAX
      - HOTEL_RESTRICTED_KEYWORDS_VIOLATION
      - RESTRICTED_LOCATION_VIOLATION
      - FLIGHT_ADVANCE_BOOKING_WINDOW_DOMESTIC_VIOLATION
      - FLIGHT_ADVANCE_BOOKING_WINDOW_INTERNATIONAL_VIOLATION
      - FLIGHT_ADVANCE_BOOKING_WINDOW_VIOLATION
      - ITINERARY_WITHIN_EVENT_TRAVEL_WINDOW
      - HOTEL_IN_ALLOWED_HOTEL_LIST
      - PAYMENT_ACCESS_VIOLATION
      - AIRPORT_IN_ALLOWED_AIRPORT_LIST
      - ITINERARY_TYPE_IS_NOT_IN_ALLOWED_BOOKING_TYPES
      - PAYMENT_AIR_ADDON_VIOLATION
      - MAX_HOTEL_BOOKING_PRICE_INCLUDING_TAX
      - MAX_HOTEL_BOOKING_PRICE_EXCLUDING_TAX
      - AIR_NUM_TRAVELERS_ALLOWED
      - PREFERRED_VENDOR_VIOLATION
      - SEAT_ADDON_VIOLATION
      - BAGGAGE_ADDON_VIOLATION
      - EARLY_BIRD_ADDON_VIOLATION
      - WIFI_ADDON_VIOLATION
      - RESTRICTED_BOOKING_VIOLATION
      - HIGHEST_ALLOWED_CABIN_VIOLATION
      - LOWEST_FARE_PER_HOTEL_PROPERTY_VIOLATION
      x-ignoreBreakingChanges:
      - PolicyPredicate->LOWEST_FARE_PER_HOTEL_PROPERTY_VIOLATION
    PolicyPreventBookingAction:
      type: object
      title: PolicyPreventBookingAction
      description: Whether to allow booking if a rule is violated.
      properties:
        prevent:
          type: boolean
          description: True if booking is to be blocked if rule is violated, else
            false
        reason:
          type: string
          description: Reason describing why was that specific itinerary not allowed
            to book.
    PolicyPreventBookingActionWrapper:
      type: object
      title: PolicyPreventBookingActionWrapper
      properties:
        preventBooking:
          $ref: '#/components/schemas/PolicyPreventBookingAction'
    PolicyRuleResultInfo:
      type: object
      properties:
        violationInfos:
          type: array
          items:
            $ref: '#/components/schemas/PolicyViolationInfo'
        subViolationInfos:
          type: array
          description: "In case of complex rules this will contain extra information\
            \ as to how the rule was \ncalculated.\n"
          items:
            $ref: '#/components/schemas/PolicyViolationInfo'
        actions:
          type: array
          description: Followed actions if rule was satisfied else violated actions.
          items:
            $ref: '#/components/schemas/PolicyAction'
    PolicyTakeApprovalAction:
      type: object
      title: PolicyTakeApprovalAction
      properties:
        numHierarchyLevels:
          type: integer
          format: int32
          description: "How many levels of hierarchy should approval be taken? If\
            \ it's just immediate manager, \nthis value would be 1. If it's manager\
            \ and their manager, this would ge 2 and so on.\n"
        positionTitles:
          type: array
          description: "What position in the cost center or department. For eg, any\
            \ business class upgrade \nmight require VP approval.\n"
          items:
            type: string
        userOrgIds:
          type: array
          description: 'The specific users from whom to take approval. For eg, say
            for a company, all approvals

            go through Adam.

            '
          items:
            $ref: '#/components/schemas/UserOrgId'
        allRequired:
          type: boolean
          description: "This tells whether all the people above needs to approve or\
            \ if any of these approve, \nit is sufficient.\n"
        hardApprovalRequired:
          type: boolean
          description: Whether to take soft approval (false) or hard approval (true).
    PolicyTakeApprovalActionWrapper:
      type: object
      title: PolicyTakeApprovalActionWrapper
      properties:
        takeApproval:
          $ref: '#/components/schemas/PolicyTakeApprovalAction'
    PolicyViolationInfo:
      type: object
      description: Policy Violation info.
      title: PolicyViolationInfo
      properties:
        predicateString:
          type: string
        predicate:
          $ref: '#/components/schemas/PolicyPredicate'
        expectedValue:
          $ref: '#/components/schemas/PolicyConstValue'
        actualValue:
          $ref: '#/components/schemas/PolicyConstValue'
    PostPaymentVerificationInfo:
      type: object
      title: PostPaymentVerificationInfo
      description: Payment information sent after the verification of payment method.
      oneOf:
      - $ref: '#/components/schemas/PostStripeVerificationInfoWrapper'
      - $ref: '#/components/schemas/PostUrlBasedVerificationInfoWrapper'
      - $ref: '#/components/schemas/AmadeusCheckoutVerificationInfoWrapper'
      - $ref: '#/components/schemas/ThreeDSecure2PostVerificationInfoWrapper'
      - $ref: '#/components/schemas/RazorpayPostVerificationInfoWrapper'
      properties:
        paymentKey:
          type: string
          description: Serialized key to identify the payment executed on payment
            gateway that will be used for post payment verification.
    PostStripeVerificationInfo:
      type: object
      description: Object to indicate successful stripe verification and provide payment
        information for next steps.
      properties:
        paymentMethodId:
          type: string
          description: Stripe's payment method id for which the verification was performed.
          example: pm_1HzKDPI3bT9GUjvoUkRQooN3
    PostStripeVerificationInfoWrapper:
      type: object
      title: PostStripeVerificationInfoWrapper
      description: Wrapper for post stripe verification info object.
      properties:
        postStripeVerificationInfo:
          $ref: '#/components/schemas/PostStripeVerificationInfo'
    PostUrlBasedVerificationInfo:
      type: object
      properties:
        pnrId:
          type: string
          description: Pnr Id, to continue with the booking flow/check the status
            of the pnr.
    PostUrlBasedVerificationInfoWrapper:
      type: object
      title: PostUrlBasedVerificationInfoWrapper
      description: Wrapper for post url based verification info object.
      properties:
        postUrlBasedVerificationInfo:
          $ref: '#/components/schemas/PostUrlBasedVerificationInfo'
    PostalAddress:
      title: PostalAddress
      description: Postal Address Details
      type: object
      required:
      - addressLines
      - regionCode
      properties:
        addressLines:
          description: Address lines
          type: array
          items:
            type: string
            example: Golden Gate Bridge
        administrativeArea:
          type: string
          description: 'Code of administrative area. For example: DL for Delhi, India.

            Highest administrative subdivision which is used for postal

            addresses of a country or region.

            For example, this can be a state, a province, an oblast, or a prefecture.

            Specifically, for Spain this is the province and not the autonomous

            community (e.g. "Barcelona" and not "Catalonia").

            Many countries don''t use an administrative area in postal addresses.
            E.g.

            in Switzerland this should be left unpopulated.

            '
          example: CA
        administrativeAreaName:
          type: string
          description: "Name of administrative area. This is full name corresponding\
            \ to administrativeArea. \nLike Delhi for DL area code. For some places,\
            \ code and name maybe same as well like Tokyo.\n"
          example: California
        description:
          description: Address description
          type: string
          example: San Francisco Home
        isDefault:
          description: Whether this address is default address in case multiple addresses
            are specified.
          type: boolean
          example: true
        languageCode:
          description: "BCP-47 language code of the contents of this address (if known).\
            \ This is often the UI \nlanguage of the input form or is expected to\
            \ match one of the languages used in the \naddress' country/region, or\
            \ their transliterated equivalents.\nThis can affect formatting in certain\
            \ countries, but is not critical to the correctness \nof the data and\
            \ will never affect any validation or other non-formatting related operations.\n\
            Examples: \"zh-Hant\", \"ja\", \"ja-Latn\", \"en\".\n"
          type: string
          example: en
        locality:
          description: Generally refers to the city/town portion of the address.
          type: string
          example: San Francisco
        locationCode:
          description: IATA 3-letter location code. See https://www.iata.org/en/services/codes.
          type: string
          example: LAX
        organization:
          description: The name of the organization at the address.
          type: string
          example: Spotnana
        postalCode:
          description: Postal code of the address. This is a required field when setting
            for a user/legal entity/company etc.
          type: string
          example: '94130'
        continentCode:
          description: 2 letter continent code of the continent this address falls
            in.
          type: string
          example: AF
        recipients:
          description: The recipient at the address.
          type: array
          items:
            type: string
        regionCode:
          description: Region code of the country/region of the address.
          type: string
          example: US
        regionName:
          description: Region name of the country/region of the address.
          type: string
          example: America
        revision:
          type: integer
          format: int32
          example: 1
        sortingCode:
          description: "Additional, country-specific, sorting code. This is not used\n\
            in most regions. Where it is used, the value is either a string like\n\
            \"CEDEX\", optionally followed by a number (e.g. \"CEDEX 7\"), or just\
            \ a number\nalone, representing the \"sector code\" (Jamaica), \"delivery\
            \ area indicator\"\n(Malawi) or \"post office indicator\" (e.g. C\xF4\
            te d'Ivoire).\n"
          type: string
          example: Jamaica
        sublocality:
          description: Sublocality of the address. This can be neighborhoods, boroughs,
            districts.
          type: string
        timezone:
          description: Time zone of the address.
          type: string
          example: America/Los_Angeles
        coordinates:
          description: Map coordinates of the address.
          $ref: '#/components/schemas/Latlng'
    PostalAddressWrapper:
      type: object
      title: PostalAddressWrapper
      properties:
        postalAddress:
          $ref: '#/components/schemas/PostalAddress'
    PowerAmenity:
      type: object
      title: PowerAmenity
      description: Power Amenity properties
      properties:
        displayText:
          type: string
          example: Power USB outlets
        powerType:
          type: string
          example: power/usb
        cost:
          type: string
          example: free
        usbPort:
          type: string
          example: 'yes'
        powerOutlet:
          type: string
          example: 'yes'
    PowerAmenityWrapper:
      type: object
      title: PowerAmenityWrapper
      properties:
        powerAmenity:
          $ref: '#/components/schemas/PowerAmenity'
    PreBookAnswers:
      type: object
      properties:
        answers:
          type: array
          items:
            $ref: '#/components/schemas/EntityAnswer'
        preBookQuestionResponseId:
          deprecated: true
          type: string
          description: The unique id sent back in the pre book questions API response
    PreCheckoutQuestionType:
      type: string
      enum:
      - UNKNOWN_CHECKOUT_QUESTION_TYPE
      - USER_DEFINED_QUESTION
      - OOP_REASON_CODE
      description: 'Types of pre-checkout questions.

        USER_DEFINED_QUESTION the default question type for all pre checkout questions
        which have been created from UI.

        OOP_REASON_CODE is kept separate so that existing OOP flow doesn''t break.

        '
      example: OOP_REASON_CODE
    PreSearchAnswers:
      type: object
      properties:
        answers:
          type: array
          items:
            $ref: '#/components/schemas/EntityAnswer'
        userEntitiesResponseId:
          type: string
    PreSearchQuestionType:
      type: string
      enum:
      - UNKNOWN_SEARCH_QUESTION_TYPE
      - PURPOSE_OF_TRIP
      description: 'Types of pre-search questions.

        PURPOSE_OF_TRIP required to ask purpose of the trip user is going to. For
        example: meeting, training, interview.

        '
      example: PURPOSE_OF_TRIP
    PreferredAirport:
      type: object
      title: PreferredAirport
      description: Airport preferred by traveler.
      required:
      - airportCode
      - label
      properties:
        airportName:
          type: string
          description: Airport name.
          example: San Francisco International Airport
        airportCode:
          type: string
          description: IATA airport code.
          example: SFO
        label:
          $ref: '#/components/schemas/PreferredLocationLabel'
    PreferredLocationLabel:
      type: string
      title: PreferredLocationLabel
      description: The label of preferred airport or rail station.
      enum:
      - HOME
      - WORK
      - OTHER
      example: HOME
    PreferredRailStation:
      type: object
      title: PreferredRailStation
      description: Rail station preferred by traveler.
      required:
      - stationCode
      - label
      properties:
        stationName:
          type: string
          description: Rail station name.
          example: Chicago Union Station
        stationCode:
          type: string
          description: Rail station code.
          example: CHI
        cityName:
          type: string
          description: Name of city where the rail station is located.
          example: Chicago
        countryCode:
          type: string
          description: Alpha-2 country code where the rail station is located.
        label:
          $ref: '#/components/schemas/PreferredLocationLabel'
    Price:
      type: object
      description: Price for a seat.
      properties:
        totalAmount:
          description: Total amount for the seat.
          $ref: '#/components/schemas/Money'
        tax:
          description: Tax for the seat.
          $ref: '#/components/schemas/Money'
        base:
          description: Base price for the seat.
          $ref: '#/components/schemas/Money'
        merchantFee:
          description: Merchant fee for the seat. This fee is applicable if purchased
            via OBT.
          $ref: '#/components/schemas/Money'
    PrimaryServiceProviderTmc:
      type: object
      title: PrimaryServiceProviderTmc
      description: Primary service provider TMC info
      required:
      - tmcId
      properties:
        tmcId:
          $ref: '#/components/schemas/CompanyId'
          description: Id of the service provider TMC.
    ProfileOwner:
      type: object
      title: ProfileOwner
      description: Basic information about the owner of the ad-hoc user.
      required:
      - userId
      properties:
        userId:
          $ref: '#/components/schemas/UserId'
    PromotionCode:
      type: object
      title: PromotionCode
      description: Promotion code
      required:
      - code
      properties:
        code:
          type: string
          description: Promotion code string
          example: NEWUSER
    Question:
      type: object
      title: Question
      description: The message defines the format of a question which can be asked
        to a user in any kind of workflows.
      required:
      - id
      - name
      - isRequired
      - isDisabled
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          description: Question display name that the user will see. For eg, 'Choose
            the purpose of your trip'.
        questionFormat:
          $ref: '#/components/schemas/QuestionFormat'
        optionInfo:
          $ref: '#/components/schemas/OptionInfo'
        isRequired:
          type: boolean
          description: Whether its compulsory to answer the question or not.
          default: false
          example: true
        isDisabled:
          type: boolean
          description: Whether the question is disabled or not. If true, this should
            not be asked.
          default: true
          example: true
        includeInItinerary:
          type: boolean
          description: Whether to include this question in the itinerary related emails.
          default: false
          example: true
          deprecated: true
        customFieldLocations:
          type: array
          items:
            $ref: '#/components/schemas/CustomFieldLocation'
        matchConditions:
          $ref: '#/components/schemas/CustomFieldMatchConditions'
        questionType:
          $ref: '#/components/schemas/QuestionType'
    QuestionFormat:
      type: string
      enum:
      - INPUT_BOX
      - RADIO_BUTTON
      - CHECKBOX
      - CHECKBOX_WITH_PERCENTAGE
      description: 'Question types. INPUT_BOX will make user enter a free flowing
        text.

        RADIO_BUTTON will have multiple options, user can select only one.

        CHECKBOX questions contain the possible set of options, from which the user
        can choose multiple options.

        CHECKBOX_WITH_PERCENTAGE is similar to checkbox, with the difference being
        that each option having an additional input field whose values must add up
        to 100.

        '
      example: CHECKBOX
    QuestionType:
      type: object
      title: QuestionType
      description: Question type.
      properties:
        preSearchQuestionType:
          $ref: '#/components/schemas/PreSearchQuestionType'
        preCheckoutQuestionType:
          $ref: '#/components/schemas/PreCheckoutQuestionType'
    RailCard:
      type: object
      title: RailCard
      description: Rail card
      required:
      - name
      - spotnanaCode
      - vendor
      properties:
        cardNumber:
          type: string
          description: Number of card
        expiryDate:
          description: Expiry date of the Rail Card.
          $ref: '#/components/schemas/DateModel'
        name:
          type: string
          description: Name of the Rail Card.
          example: Veterans Railcard
        spotnanaCode:
          description: Unique Spotnana code/identifier for Rail Card.
          type: string
          example: VET
        vendor:
          type: string
          description: Vendor Name.
          example: ATOC
    RailPref:
      type: object
      title: RailPref
      description: Travel preferences related to rail station.
      properties:
        preferredRailStations:
          type: array
          description: A list of user preferred rail stations.
          items:
            $ref: '#/components/schemas/PreferredRailStation'
        seatPreference:
          $ref: '#/components/schemas/SeatPref'
        travelClasses:
          type: array
          description: A list of class of service for rail.
          items:
            $ref: '#/components/schemas/RailTravelClass'
        coachPreferences:
          type: array
          description: A list of coach preference for rail.
          items:
            $ref: '#/components/schemas/CoachPref'
        conditionalRates:
          type: array
          description: A list of conditional rates for rail.
          items:
            $ref: '#/components/schemas/ConditionalRate'
    RailTravelClass:
      description: Travel class
      type: string
      enum:
      - FIRST
      - STANDARD
      - BUSINESS
      - SLEEPER
      - STANDARD_PREMIUM
      - BUSINESS_PREMIUM
      - COACH
      - ROOM
      - EXECUTIVE
      example: FIRST
    RateType:
      type: string
      title: RateType
      description: Type of booked fare
      enum:
      - RATE_TYPE_UNKNOWN
      - PUBLISHED
      - TMC_NEGOTIATED
      - COMPANY_NEGOTIATED
      example: PUBLISHED
    RatingWrapper:
      type: object
      title: RatingWrapper
      properties:
        rating:
          type: number
          format: double
    RawPaymentSourceDetails:
      type: object
      title: RawPaymentSourceDetails
      description: Raw Details of the Payment Source
      discriminator:
        propertyName: type
        mapping:
          DPAN: '#/components/schemas/Dpan'
          CREDIT: '#/components/schemas/Credit'
      oneOf:
      - $ref: '#/components/schemas/Dpan'
      - $ref: '#/components/schemas/Credit'
    RazorpayPostVerificationInfo:
      type: object
      description: Object to indicate successful stripe verification and provide payment
        information for next steps.
      required:
      - paymentId
      properties:
        paymentId:
          type: string
          description: Razorpay payment id for which the verification was performed.
          example: pay_Ot3t93Xwmh2hOg
        orderId:
          type: string
          description: Razorpay order id in which the payment was added.
          example: order_OshJ61KRRSzb2Q
        signature:
          type: string
          description: Razorpay signature to validate the payment.
          example: f2c0494b77cb067a0ac76973d65cdce8c9de593a55ef2f81969cbabbe7ce493d
    RazorpayPostVerificationInfoWrapper:
      type: object
      title: RazorpayPostVerificationInfoWrapper
      description: Wrapper for post razorpay verification info object.
      properties:
        postRazorpayVerificationInfo:
          $ref: '#/components/schemas/RazorpayPostVerificationInfo'
    RazorpayVerificationInfo:
      type: object
      title: RazorpayVerificationInfo
      description: Verification information required when card is tokenized via Razorpay
        for Indian markets.
      required:
      - paymentProcessor
      properties:
        paymentProcessor:
          type: string
          description: Payment processor involved.
          example: RAZORPAY
        paymentAuthUrl:
          description: URL for authorizing the payment on the card.
          type: string
    RazorpayVerificationInfoWrapper:
      type: object
      title: RazorpayVerificationInfoWrapper
      description: Wrapper for Razorpay verification Info.
      properties:
        razorPayVerificationInfo:
          $ref: '#/components/schemas/RazorpayVerificationInfo'
    RedressNumber:
      type: object
      title: RedressNumber
      description: Redress details
      required:
      - number
      - issueCountry
      properties:
        number:
          type: string
          example: '12345'
        issueCountry:
          type: string
          example: US
    RedressNumberWrapper:
      type: object
      title: RedressNumberWrapper
      properties:
        redress:
          $ref: '#/components/schemas/RedressNumber'
    Reference:
      type: object
      title: Reference object containing uuid and name of the entity.
      description: Reference of an entity
      required:
      - id
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
    Remark:
      title: Remark
      type: object
      description: Object for storing remark information.
      required:
      - text
      properties:
        text:
          type: string
          description: Remark text.
          example: Seat 11A selected
    RemarkObject:
      type: object
      description: Wrapper object for storing one of the pnr remark types.
      title: RemarkObject
      discriminator:
        propertyName: remarkType
        mapping:
          SEAT_SELECTION_REMARK: '#/components/schemas/SeatNoteRemark'
      oneOf:
      - $ref: '#/components/schemas/SeatNoteRemark'
    RestrictedKeywordsWithReason:
      type: object
      properties:
        keyword:
          type: string
          description: Restricted keyword
          example: Test Keyword
        reason:
          type: string
          description: Reason for restriction
          example: Test Reason
    RoomPreference:
      type: object
      title: RoomPreference
      description: Room options and stay preference.
      properties:
        isMobilityAccessible:
          type: boolean
          description: Whether or not mobility accessible room, tub.
          default: false
          example: false
        bedCount:
          type: string
          description: The number of bed in the room.
          enum:
          - ONE_BED
          - TWO_BEDS
          example: ONE_BED
        roomType:
          type: string
          description: Single selection of type of room.
          enum:
          - SMOKING
          - NON_SMOKING
          example: SMOKING
        mostImportantFact:
          type: string
          description: Single selection of the most import fact.
          enum:
          - ROOM_TYPE
          - BED_COUNT
          - ROOM_LOCATION
          example: BED_COUNT
        roomLocation:
          type: string
          description: Location of the hotel room
          enum:
          - HIGH_FLOOR
          - LOW_FLOOR
          - NEAR_ELEVATOR
          example: HIGH_FLOOR
        pillowType:
          type: string
          description: The type of pillow in hotel room.
          enum:
          - FOAM
          - EXTRA_FOAM
          - EXTRA_FEATHER
          example: FOAM
        roomAmenityPrefs:
          type: array
          items:
            type: string
            description: Amenity preference of the room.
            enum:
            - FEATHER_FREE_ROOM
            - EXTRA_TOWELS
            - REFRIGERATOR
            example: EXTRA_TOWELS
    RowSection:
      description: Represents a set of consecutive rows within a cabin which have
        same properties.
      type: object
      properties:
        rowNumbers:
          $ref: '#/components/schemas/Int32Range'
        availableSeats:
          type: array
          items:
            $ref: '#/components/schemas/AvailableSeats'
          description: Available seats for each row in this Row Section.
        rowTypes:
          type: array
          items:
            $ref: '#/components/schemas/RowType'
        facilitySections:
          type: array
          items:
            $ref: '#/components/schemas/FacilitySection'
        seatSections:
          type: array
          items:
            $ref: '#/components/schemas/SeatSection'
    RowType:
      description: Represents the type of a row.
      type: string
      enum:
      - ROW
      - BUFFER
      - EXIT
      - EXTRA_LEG_ROOM
      - NO_ROW
    RuleDetail:
      type: object
      properties:
        title:
          type: string
        text:
          type: string
    Rules:
      type: array
      items:
        $ref: '#/components/schemas/RuleDetail'
    SSRIndexes:
      type: object
      description: Indexes for the flight and leg for which the changes are requested.
      required:
      - flightIndex
      - legIndex
      properties:
        legIndex:
          type: integer
          description: Index referencing the flight leg for which the SSR should be
            added. Should be used in conjunction with the flight Index.
          example: 1
        flightIndex:
          type: integer
          description: Index referencing the flight for which the SSR should be added.
            Should be used in conjunction with the leg Index.
          example: 2
    SearchLeg:
      type: object
      required:
      - origin
      - destination
      - date
      description: A leg representing the origin, destination and the departure date
      properties:
        origin:
          description: 3 letter IATA airport or metropolitan code for the origin
          $ref: '#/components/schemas/AirportLocation'
        destination:
          description: 3 letter IATA airport or metropolitan code for the destination
          $ref: '#/components/schemas/AirportLocation'
        date:
          description: The date of travel for this leg
          $ref: '#/components/schemas/DateModel'
    SeatAmenity:
      type: object
      title: SeatAmenity
      description: Seat Amenity properties
      properties:
        displayText:
          type: string
          example: Full flat all aisle access
        seatType:
          type: string
          example: full flat pod
        width:
          type: string
        legroom:
          type: string
        pitch:
          type: number
          format: double
          example: 78
    SeatAmenityPref:
      type: object
      title: SeatAmenityPref
      description: Seat amenity preference.
      required:
      - seatAmenityTypes
      properties:
        seatAmenityTypes:
          type: array
          items:
            type: string
            enum:
            - UNKNOWN_AIR_SEAT_AMENITY_TYPE
            - FLAT_BED
            - WIFI
            - IN_SEAT_POWER
            example: WIFI
    SeatAmenityWrapper:
      type: object
      title: SeatAmenityWrapper
      properties:
        seatAmenity:
          $ref: '#/components/schemas/SeatAmenity'
    SeatDetail:
      type: object
      required:
      - flightIndex
      - seatNumber
      properties:
        flightIndex:
          type: integer
          description: Index referencing the flight for which the changes are requested.
          example: 1
        seatNumber:
          type: string
          description: Selected seat number for the flight.
          example: 10A
    SeatIndexes:
      type: object
      required:
      - legIndex
      properties:
        legIndex:
          type: integer
          description: The leg index for which the seat map is requested.
        flightIndex:
          type: array
          items:
            type: integer
          description: The flight index for which the seat map is requested. It is
            optional and if it not present, then all the flights will be returned
            for this leg
    SeatLocation:
      description: Describes a location in the flight.
      type: string
      enum:
      - FRONT
      - MIDDLE
      - REAR
      - LEFT
      - LEFT_CENTER
      - RIGHT
      - RIGHT_CENTER
      - CENTER
      - UPPER_DECK
      - LOWER_DECK
      - MAIN_DECK
    SeatLocationPref:
      type: object
      title: SeatLocationPref
      description: Seat location preference.
      properties:
        cabins:
          type: array
          items:
            type: string
            enum:
            - UNKNOWN_CABIN
            - ECONOMY
            - PREMIUM_ECONOMY
            - BUSINESS
            - FIRST
            example: FIRST
        isBulkHeadPref:
          type: boolean
          example: false
        maxFlightDurationInHours:
          type: integer
          format: int32
          example: 3
        position:
          type: string
          enum:
          - UNKNOWN_POSITION
          - AISLE
          - WINDOW
          - AISLE_OR_WINDOW
          example: WINDOW
    SeatLoyaltyInfo:
      description: Information about a traveler.
      type: object
      properties:
        flightId:
          type: string
          description: The flight identifier for which this loyalty is being applied.
          example: flight_0
        loyaltyInfos:
          type: array
          items:
            $ref: '#/components/schemas/LoyaltyInfo'
    SeatMapItinerary:
      description: The itinerary for which the seat map needs to be returned.
      oneOf:
      - $ref: '#/components/schemas/AirItineraryIdWrapper'
      - $ref: '#/components/schemas/PnrItineraryWrapper'
    SeatNoteRemark:
      title: SeatNoteRemark
      type: object
      description: Note remark for seat selection.
      required:
      - remarkType
      - remarkIdentifier
      - remark
      properties:
        remarkType:
          type: string
          description: Type of remark. Always set to SEAT_SELECTION_REMARK.
          enum:
          - SEAT_SELECTION_REMARK
        remarkIdentifier:
          $ref: '#/components/schemas/SeatRemarkIdentifier'
        remark:
          $ref: '#/components/schemas/Remark'
    SeatPref:
      type: object
      title: SeatPref
      description: Preference about seat of rail.
      properties:
        hasAccessibility:
          type: boolean
          description: Whether or not requires assistance for disability.
          default: false
          example: false
        seatTypes:
          type: array
          items:
            $ref: '#/components/schemas/SeatPrefType'
        seatLocations:
          type: array
          items:
            $ref: '#/components/schemas/SeatPrefLocation'
        deckLevels:
          type: array
          items:
            $ref: '#/components/schemas/DeckLevel'
        seatDirections:
          type: array
          items:
            $ref: '#/components/schemas/SeatPrefDirection'
    SeatPrefDirection:
      type: string
      description: The direction of seat of the rail.
      enum:
      - FORWARD
      - BACKWARD
      example: FORWARD
    SeatPrefFilter:
      type: object
      properties:
        seatTypes:
          type: array
          items:
            $ref: '#/components/schemas/SeatType'
        minPitchInch:
          type: integer
          format: int32
    SeatPrefLocation:
      type: string
      description: The location of seat of the rail.
      enum:
      - AISLE
      - WINDOW
      - SOLO
      example: AISLE
    SeatPrefType:
      type: string
      description: The type of seat of the rail.
      enum:
      - SLEEPER_BED
      - NORMAL
      - TABLE_SEAT
      example: NORMAL
    SeatRemarkIdentifier:
      type: object
      title: SeatRemarkIdentifier
      description: Identifier for seat remark.
      required:
      - legIndex
      - flightIndex
      - seatIdentifier
      properties:
        legIndex:
          type: integer
          description: Index referencing the leg for which the changes are requested.
          example: 1
        flightIndex:
          type: integer
          description: Index referencing the flight for which the changes are requested.
          example: 1
        seatIdentifier:
          type: string
          description: Selected seat number for the flight.
          example: 10A
        travelerInfo:
          $ref: '#/components/schemas/UserOrgId'
    SeatSection:
      type: object
      description: 'SeatSection represents a group of seats, within a row, which has
        the same properties.

        The seats'' column numbers might not be consecutive. For, eg 2 window seats
        on the

        left and right side of the row which have same properties would become one
        SeatSection.

        '
      properties:
        columnNumbers:
          type: array
          description: The seat columns present in the seat section.
          items:
            type: string
            example: '32'
        type:
          type: string
          enum:
          - SEAT
          - NO_SEAT
          - BULKHEAD
          - STAIRS
          - LAVATORY
          - BAR
          - CLOSET
          - AIR_PHONE
          - EXIT_DOOR
          - EMERGENCY_EXIT
          - GALLEY
          - LUGGAGE_STORAGE
          - STORAGE_SPACE
          - TABLE
        limitations:
          type: array
          items:
            type: string
            enum:
            - RESTRICTED_RECLINE
            - NOT_ALLOWED_FOR_INFANT
            - NOT_ALLOWED_FOR_UNACCOMPANIED_MINOR
            - NOT_SUITABLE_FOR_CHILD
            - WINDOW_SEAT_WITHOUT_WINDOW
            - CREW_SEAT
            - NOT_BOOKABLE_ON_OBT
            - RESTRICTED_GENERAL
            - NO_FARE_INFORMATION
            - LOYALTY_LEVEL_REQUIRED
        facilities:
          type: array
          items:
            type: string
            enum:
            - LEG_SPACE
            - BASSINET
            - SMOKING
            - NON_SMOKING
            - PET_IN_CABIN
            - SUITABLE_FOR_ADULT_WITH_INFANT
            - SUITABLE_FOR_UNACCOMPANIED_MINOR
            - FACILITIES_FOR_HANDICAPPED_INCAPACITATED
            - PREFERRED_SEAT
        location:
          type: array
          items:
            type: string
            enum:
            - BULKHEAD_ROW
            - EXIT_ROW
            - LEFT_FACING_FLAT_BED
            - RIGHT_FACING_FLAT_BED
        commercialName:
          description: Commercial name for this seat.
          type: string
          example: Economy plus
        price:
          description: Price for this seat.
          $ref: '#/components/schemas/Price'
    SeatSelectionRule:
      type: object
      description: Seat selection rule
      required:
      - description
      properties:
        description:
          type: string
          description: Seat selection policy text
          example: Seat selection for free
    SeatType:
      type: string
      enum:
      - UNKNOWN
      - FLAT
      - RECLINER
      - SKYCOUCH
      - PRIVATE_SUITE
      default: UNKNOWN
    SeatTypeInfo:
      type: object
      description: Seat information
      required:
      - seatType
      properties:
        seatType:
          type: string
          description: Description of the type of seat available
          example: Recliner seat
    SecondaryServiceProviderTmc:
      type: object
      title: SecondaryServiceProviderTmc
      description: Secondary service provider TMC info
      required:
      - tmcId
      - supplier
      - travelType
      properties:
        tmcId:
          $ref: '#/components/schemas/CompanyId'
          description: Id of the service provider TMC.
        supplier:
          $ref: '#/components/schemas/SupplierType'
          description: Supplier for which this service provider should be used.
        travelType:
          $ref: '#/components/schemas/TravelType'
          description: Travel type for which this service provider should be used.
    SelectedAncillary:
      type: object
      title: SelectedAncillary
      description: The ancillary option selected during checkout.
      required:
      - ancillaryId
      properties:
        ancillaryId:
          type: string
          description: The ID of the selected ancillary option.
          example: ancillary_0
    SelectedBaggage:
      type: object
      description: The baggage option selected during checkout.
      required:
      - legId
      - baggageIds
      properties:
        legId:
          type: string
          description: The ID of the leg associated with this baggage.
        baggageIds:
          type: array
          items:
            type: string
          description: The IDs of the selected baggage options from the flight checkout
            API response.
    SelectedFormOfPayment:
      type: object
      description: List of different forms of payments used for booking for a set
        of travelers.
      required:
      - paymentItems
      - selectedPaymentSources
      properties:
        paymentItems:
          type: array
          items:
            $ref: '#/components/schemas/PaymentItem'
        selectedPaymentSources:
          type: array
          items:
            $ref: '#/components/schemas/SelectedPaymentSource'
    SelectedPaymentMethod:
      type: object
      description: Selected payment method for the booking.
      oneOf:
      - $ref: '#/components/schemas/SelectedPaymentSourceWrapper'
    SelectedPaymentSource:
      type: object
      description: Describes the selected payment source for the booking.
      properties:
        paymentSourceId:
          type: string
          format: uuid
          description: Unique identifier identifying this payment source.
          example: f49d00fe-1eda-4304-ba79-a980f565281d
        rawPaymentSource:
          $ref: '#/components/schemas/RawPaymentSourceDetails'
        postPaymentRedirectionUrl:
          type: string
          description: Url for post payment redirection if payment source navigates
            user to a third party url
          example: https://mycompany.com/checkout?paymentSourceId=f49d00fe-1eda-4304-ba79-a980f565281d
        cvv:
          type: string
          description: CVV associated with associated payment source, if any.
        amount:
          description: Total amount to be charged for specified payment items.
          $ref: '#/components/schemas/Money'
    SelectedPaymentSourceWrapper:
      type: object
      title: SelectedPaymentSourceWrapper
      properties:
        selectedPaymentSource:
          $ref: '#/components/schemas/SelectedPaymentSource'
    SelectedSeat:
      type: object
      description: The seat numbers selected during checkout.
      required:
      - flightId
      - seatNumber
      properties:
        flightId:
          type: string
          description: The ID of the flight associated with this baggage.
        seatNumbers:
          type: array
          items:
            type: string
          description: The seat numbers selected for the associated flight.
    SimpleBag:
      type: object
      description: Describes a simple bag with a given weight and fare amount.
      properties:
        weightLimit:
          $ref: '#/components/schemas/WeightLimit'
        bagFareInfo:
          $ref: '#/components/schemas/BagFareInfo'
    SimpleBagOption:
      type: object
      description: An option containing a simple bag description.
      properties:
        baggageOptionId:
          type: string
          example: bag_0
        description:
          type: string
          example: 1 bag for $20
        simpleBag:
          $ref: '#/components/schemas/SimpleBag'
    SingleSelectOptionGroup:
      title: SingleSelectOptionGroup
      type: object
      description: "Describes the baggage option group which provides the users with\
        \ multiple options to choose\none from. Example options: (1) 1 bag for $20\
        \ for 20kg (2) 2 bags for 30kg worth $50 \n(3) 3 bags 10+20+30kg worth 100$\
        \ $(20+30+50).\n"
      required:
      - singleSelectOptions
      properties:
        singleSelectOptions:
          type: array
          items:
            $ref: '#/components/schemas/ComplexBagOption'
    SizeInfo:
      type: object
      description: Information about the baggage size.
      properties:
        length:
          description: The weight limit of a baggage option.
          type: number
          example: 32
        unit:
          description: The unit of measurement for the bag size.
          type: string
          enum:
          - CENTIMETER
          - METER
    SortBy:
      type: string
      enum:
      - PRICE
      - DEPARTURE_TIME
      - ARRIVAL_TIME
      - DURATION
      default: PRICE
    SortOption:
      type: object
      properties:
        sortBy:
          $ref: '#/components/schemas/SortBy'
        sortOrder:
          $ref: '#/components/schemas/SortOrder'
        shelfNumber:
          type: integer
          format: int32
          description: Only used when 'SortBy' is 'PRICE' and represents which shelf
            should be used for sorting. If shelf_number is 0 (default), minimum of
            all shelf prices is used.
    SortOrder:
      type: string
      enum:
      - ASCENDING
      - DESCENDING
      default: ASCENDING
    SpecialServiceRequest:
      type: object
      description: Special Service Request (SSR) Schema.
      title: SpecialServiceRequest
      required:
      - code
      - category
      - subCategory
      properties:
        code:
          type: string
          description: Unique 4-letter code for the service request.
          example: WCHC
        category:
          description: Category of the Special Service Request.
          $ref: '#/components/schemas/Category'
        subCategory:
          type: string
          description: The type of Special Service Request (SSR).
          example: Wheelchair with wet cell battery.
        isFreeTextMandatory:
          description: Indicates if free text input is mandatory for the Special Service
            Request (SSR).
          type: boolean
          default: false
          example: true
        isFreeTextAllowed:
          description: 'Indicates whether adding free text is supported.

            SSRs that support free text will allow you to add additional information
            about the service request.

            '
          type: boolean
          default: false
          example: true
    SpecialServiceRequestInfo:
      type: object
      description: Special Service Request (SSR) Info.
      required:
      - code
      properties:
        indexes:
          $ref: '#/components/schemas/SSRIndexes'
        flightIndex:
          type: integer
          description: Index referencing the flight for which the changes are requested.
          example: 0
        code:
          type: string
          description: Unique 4-letter code for the Special Service Request (SSR).
          example: WCHC
        info:
          type: string
          description: Free text field for adding additional details about the Special
            Service Request (SSR).
          example: Special meal without egg.
    SpecialServiceRequestsResponse:
      type: object
      description: List of the Special Service Requests (SSR) Spotnana support.
      properties:
        specialServiceRequests:
          type: array
          items:
            $ref: '#/components/schemas/SpecialServiceRequest'
    SplitAmount:
      type: object
      title: SplitAmount
      description: Payment amount details if slider payment function is non-linear.
      properties:
        amount:
          type: array
          items:
            $ref: '#/components/schemas/Money'
            description: List of split amounts required for slider at checkout.
    SplitAmountWrapper:
      type: object
      title: SplitAmountWrapper
      description: Split amount wrapper.
      properties:
        splitAmount:
          $ref: '#/components/schemas/SplitAmount'
    StringListWrapper:
      type: object
      title: StringListWrapper
      properties:
        sList:
          type: array
          items:
            type: string
    StringWrapper:
      type: object
      title: StringWrapper
      properties:
        s:
          type: string
    StripeCardTokens:
      type: object
      title: StripeCardTokens
      description: Stripe card tokens used by clients in authorizing newly added cards.
      required:
      - paymentMethodId
      - setupIntentClientSecret
      - paymentProcessor
      properties:
        paymentProcessor:
          type: string
          description: Payment processor involved.
          example: STRIPE
        paymentMethodId:
          type: string
          description: Stripe's payment-method-id for the card.
          example: pm_1HzKDPI3bT9GUjvoUkRQooN3
        setupIntentClientSecret:
          type: string
          description: Stripe client secret for attaching the card to the setup-intent.
          example: seti_1IBwEiKXFM6BpWYhfdCGZ7sg_secret_InXJDifWGBcVqekJvhAxidON6vHAvfS
        connectedAccountId:
          type: string
          description: Account Id of the connected account in which above payment-method
            and setup-intent exist. This is populated only when a connected account
            is used.
          example: acct_1NT8oyCZnFgI0lFY
        publishableKey:
          type: string
          description: Publishable key of the Stripe platform account.
          example: pk_test_51Hx7XyI3bT9GUjvoUkRQooN3
    StripeVerificationInfo:
      type: object
      description: Verification information required for card stripe verification.
      properties:
        stripeCardTokens:
          $ref: '#/components/schemas/StripeCardTokens'
    StripeVerificationInfoWrapper:
      type: object
      title: StripeVerificationInfoWrapper
      description: Wrapper for stripe verification Info.
      properties:
        stripeVerificationInfo:
          $ref: '#/components/schemas/StripeVerificationInfo'
    SupplierType:
      type: string
      description: Supplier type like Sabre, TravelFusion etc.
      enum:
      - SABRE
      - AMADEUS
      - TRAVEL_FUSION
      - FARELOGIX_NDC
      - ATPCO_NDC
      - TRAINLINE
      - AVIA
      - QBR
      - BCD
      - QANTAS_HOTELS
      - SOUTHWEST
      - EXPEDIA
      - HOTEL_HUB
      - NDC
      - MARRIOTT
      - CLEARTRIP
      - KYTE
      - GROUNDSPAN
      - SABRE_NDC
      - BOOKING_COM
      x-ignoreBreakingChanges:
      - SupplierType->BOOKING_COM
      example: SABRE
    Tax:
      type: object
      title: Tax
      description: Tax details
      required:
      - amount
      properties:
        amount:
          $ref: '#/components/schemas/Money'
          description: Tax amount
        taxCode:
          type: string
          description: Tax code
          example: VAT
        percentage:
          type: number
          format: double
          description: Tax amount to total amount
          example: 9
    TaxBreakdown:
      type: array
      items:
        $ref: '#/components/schemas/Tax'
    TemperatureAmenity:
      type: object
      title: TemperatureAmenity
      description: Temperature Amenity properties
      properties:
        displayText:
          type: string
          example: No pre-flight temperature check
        temperatureDescription:
          type: string
          example: A temperature check is not required before boarding
        temperatureAttrDescription:
          type: string
          example: 'no'
    TemperatureAmenityWrapper:
      type: object
      title: TemperatureAmenityWrapper
      properties:
        temperatureAmenity:
          $ref: '#/components/schemas/TemperatureAmenity'
    ThirdPartySource:
      type: string
      enum:
      - UNKNOWN_SOURCE
      - SABRE
      - TRAVEL_FUSION
      - AVIA
      - NDC
      - TRAINLINE
      - ATPCO_NDC
      - FARELOGIX_NDC
      - OFFLINE
      - CONNEXUS
      - ROUTEHAPPY
      - AMADEUS
      - GIATA
      - QBR
      - BCD
      - QANTAS_HOTELS
      - SOUTHWEST
      - EXPEDIA
      - HOTEL_HUB
      - MARRIOTT
      - CLEARTRIP
      - KYTE
      - GROUNDSPAN
      - SABRE_NDC
      - BOOKING_COM
      x-ignoreBreakingChanges:
      - ThirdPartySource->BOOKING_COM
      example: SABRE
    ThreeDSecure2PostVerificationInfo:
      type: object
      description: Result of 3DS2 verification.
      required:
      - results
      properties:
        results:
          type: array
          description: List of 3DS2 verification results.
          items:
            $ref: '#/components/schemas/ThreeDSecure2VerificationResultItem'
    ThreeDSecure2PostVerificationInfoWrapper:
      type: object
      title: ThreeDSecure2PostVerificationInfoWrapper
      description: Wrapper for 3DS2 post verification info object
      properties:
        threeDSecure2PostVerificationInfo:
          $ref: '#/components/schemas/ThreeDSecure2PostVerificationInfo'
    ThreeDSecure2VerificationInfo:
      type: object
      description: Verification information required for 3DS2 authentication.
      required:
      - verificationInfoList
      properties:
        verificationInfoList:
          type: array
          description: List of 3DS2 verification info.
          items:
            $ref: '#/components/schemas/ThreeDSecure2VerificationInfoItem'
    ThreeDSecure2VerificationInfoItem:
      type: object
      description: 3DS2 verification info.
      required:
      - cardId
      - sessionId
      - redirectUrl
      properties:
        cardId:
          type: string
          description: Card id for which the verification was performed.
          example: 550e8400-e29b-41d4-a716-446655440000
        sessionId:
          type: string
          description: session id of the payment service provider.
          example: sid_7q3lpqopuwmuxct3l3rkz225ae
        redirectUrl:
          type: string
          description: 3DS2 verification redirect url.
          example: https://api.checkout.com/sessions-interceptor/sid_7q3lpqopuwmuxct3l3rkz225ae
    ThreeDSecure2VerificationInfoWrapper:
      type: object
      title: ThreeDSecure2VerificationInfoWrapper
      description: Wrapper for 3DS2 verification Info.
      properties:
        threeDSecure2VerificationInfo:
          $ref: '#/components/schemas/ThreeDSecure2VerificationInfo'
    ThreeDSecure2VerificationResultItem:
      type: object
      description: Result of 3DS2 verification.
      required:
      - cardId
      - sessionId
      - success
      properties:
        cardId:
          type: string
          description: Card id for which the verification was performed.
          example: 550e8400-e29b-41d4-a716-446655440000
        sessionId:
          type: string
          description: session id of the payment service provider.
          example: sid_7q3lpqopuwmuxct3l3rkz225ae
        success:
          type: boolean
          description: Whether the 3DSecure2 authentication was successful.
          example: true
    TicketToExchangeInfo:
      type: object
      title: TicketToExchangeInfo
      description: Ticket with its exchange info.
      properties:
        ticketNumber:
          type: string
          description: Ticket number. For non-ticketed PNRs, this field will not be
            present.
          example: 0111234567890
        exchangeState:
          description: Current exchange state for this ticket.
          $ref: '#/components/schemas/AirExchangeState'
        isRefundable:
          type: boolean
          description: A flag to indicate if ticket is refundable or not.
          example: false
        exchangeNotSupportedReasons:
          type: array
          description: Reasons why exchange is not supported via OBT.
          minItems: 1
          items:
            $ref: '#/components/schemas/ExchangeNotSupportedReason'
        supplierPreferredEnabledFields:
          description: Contains the information of the options that are allowed to
            exchange.
          $ref: '#/components/schemas/AirExchangeAvailableOptions'
        userOrgId:
          $ref: '#/components/schemas/UserOrgId'
          description: User org ID of associated traveler.
        travelerInfo:
          $ref: '#/components/schemas/TravelerInfoResponse'
          description: Traveler info for this traveler
    TicketType:
      type: string
      description: The ticket issuance type for a given itinerary, whether single
        or multiple
      enum:
      - SINGLE
      - MULTI
      default: SINGLE
    Tier:
      type: string
      title: Tier
      description: 'Tier of User.

        Note: In default UI, BASIC is displayed as Standard, and SEAT1A is displayed
        as VIP.

        '
      enum:
      - BASIC
      - SEAT1A
      x-enum-varnames:
      - BASIC
      - SEAT1A
      example: SEAT1A
      default: BASIC
    TimeLocal:
      title: TimeLocal
      description: ISO8601 Local Time
      type: object
      required:
      - iso8601
      properties:
        iso8601:
          type: string
          pattern: ^([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$
          example: '17:32'
    TimeRange:
      type: object
      properties:
        min:
          $ref: '#/components/schemas/TimeLocal'
          description: Minimum value - inclusive.
        max:
          $ref: '#/components/schemas/TimeLocal'
          description: Maximum value - inclusive.
    TimeRangeFilter:
      type: object
      properties:
        timeRanges:
          type: array
          items:
            $ref: '#/components/schemas/LegDepartureArrivalTimeRangeFilter'
    TmcBasicInfo:
      type: object
      title: TmcBasicInfo
      description: TMC related basic information.
      required:
      - contractingTmc
      - bookingTmc
      properties:
        contractingTmc:
          $ref: '#/components/schemas/CompanyRef'
          description: Contracting TMC is the TMC the user/organization contracted.
        bookingTmc:
          $ref: '#/components/schemas/CompanyRef'
          description: Booking TMC is the TMC used for the bookings for the user/organization.
    TmcInfo:
      type: object
      title: TmcInfo
      description: TMC config information.
      required:
      - id
      - primaryServiceProviderTmc
      properties:
        id:
          $ref: '#/components/schemas/CompanyId'
          description: TMC id.
        primaryServiceProviderTmc:
          $ref: '#/components/schemas/PrimaryServiceProviderTmc'
          description: Primary service provider TMC for the TMC.
        secondaryServiceProviderTmcs:
          type: array
          description: Secondary service provider TMCs for the TMC.
          items:
            $ref: '#/components/schemas/SecondaryServiceProviderTmc'
        partnerTmcId:
          $ref: '#/components/schemas/CompanyId'
          description: Useful to identify the clients onboarded by a PARTNER_TMC
    TokenizedExpiry:
      title: TokenizedExpiry
      type: object
      description: Contains the tokenized expiry of a Card.
      required:
      - expiryMonth
      - expiryYear
      properties:
        expiryMonth:
          type: string
          description: Tokenized Expiry month
          example: KvAuPANQWCpjwRQxcC8EXg==
        expiryYear:
          type: string
          description: Tokenized Expiry year
          example: fPBm0OWrKwPyIrCVcbg4cA==
    TokenizedExpiryWrapper:
      type: object
      title: TokenizedExpiryWrapper
      properties:
        tokenizedExpiry:
          $ref: '#/components/schemas/TokenizedExpiry'
    TotalAmountBreakdownWrapper:
      description: Wrapper for the total amount breakdown of a baggage fare.
      type: object
      title: TotalAmountBreakdownWrapper
      properties:
        totalAmountBreakdown:
          description: The total amount breakdown of a baggage fare.
          type: array
          items:
            $ref: '#/components/schemas/BagFareInfo'
    TotalAmountWrapper:
      description: Wrapper for the total amount of a baggage fare.
      type: object
      title: TotalAmountWrapper
      properties:
        totalAmount:
          description: The total amount of a baggage fare.
          $ref: '#/components/schemas/BagFareInfo'
    TotalWeightBreakdownWrapper:
      description: Wrapper for the total weight breakdown of a baggage option.
      type: object
      title: TotalWeightBreakdownWrapper
      properties:
        totalWeightBreakdown:
          description: The total weight breakdown of a baggage option.
          type: array
          items:
            $ref: '#/components/schemas/WeightLimit'
    TotalWeightWrapper:
      description: Wrapper for the total weight of a baggage option.
      type: object
      title: TotalWeightWrapper
      properties:
        totalWeight:
          description: The total weight of a baggage option.
          $ref: '#/components/schemas/WeightLimit'
    TransmissionSearchFilter:
      type: string
      description: Transmission type for Filters
      title: TransmissionSearchFilter
      enum:
      - MANUAL
      - AUTOMATIC
      example: AUTOMATIC
    TravelClassHierarchy:
      type: string
      description: "Rail Travel classes in hierarchy numbering higher to keep scope\
        \ for future additions in \nbetween.\n"
      enum:
      - UNKNOWN
      - STANDARD
      - COACH
      - STANDARD_PREMIUM
      - BUSINESS
      - BUSINESS_PREMIUM
      - FIRST
      - SLEEPER
      example: STANDARD
    TravelClassHierarchyWrapper:
      type: object
      title: TravelClassHierarchyWrapper
      properties:
        railTravelClass:
          $ref: '#/components/schemas/TravelClassHierarchy'
    TravelPref:
      type: object
      title: TravelPref
      description: Travel preferences.
      properties:
        airPref:
          $ref: '#/components/schemas/AirPref'
        preferredCurrency:
          type: string
          example: USD
        railCards:
          type: array
          items:
            $ref: '#/components/schemas/RailCard'
        railPref:
          $ref: '#/components/schemas/RailPref'
        carPref:
          $ref: '#/components/schemas/CarPref'
        hotelPref:
          $ref: '#/components/schemas/HotelPref'
    TravelRegionType:
      type: string
      title: TravelRegionType
      description: Travel region Type
      enum:
      - DOMESTIC
      - INTERNATIONAL
      example: DOMESTIC
    TravelType:
      type: string
      title: TravelType
      description: Travel Type
      enum:
      - AIR
      - HOTEL
      - CAR
      - RAIL
      - LIMO
      - MISC
      - ALL
      example: AIR
    Traveler:
      type: object
      title: Traveler
      description: Traveler details.
      deprecated: true
      properties:
        travelerPersonalInfo:
          $ref: '#/components/schemas/TravelerPersonalInfo'
        user:
          $ref: '#/components/schemas/User'
        userBusinessInfo:
          $ref: '#/components/schemas/UserBusinessInfo'
        userOrgId:
          $ref: '#/components/schemas/UserOrgId'
        persona:
          $ref: '#/components/schemas/Persona'
        isActive:
          type: boolean
          description: A boolean flag to show if traveler is active.
          example: true
        tier:
          $ref: '#/components/schemas/Tier'
        adhocUserInfo:
          $ref: '#/components/schemas/AdhocUserInfo'
        externalId:
          type: string
          description: External id of this user.
    TravelerApplicability:
      type: object
      required:
      - applicability
      properties:
        applicability:
          type: string
          enum:
          - PER_TRAVELER
          - ALL_TRAVELERS
          example: PER_TRAVELER
    TravelerBaggageOptions:
      title: TravelerBaggageOptions
      type: object
      description: Describes a group of baggage options for the below traveler type.
      properties:
        travelerType:
          description: Type of the traveler. Present when traveler applicability is
            PER_TRAVELER.
          $ref: '#/components/schemas/PassengerType'
        travelerAge:
          description: Age of the traveler. Present when traveler applicability is
            PER_TRAVELER.
          $ref: '#/components/schemas/PassengerAge'
        baggageOptions:
          $ref: '#/components/schemas/BaggageOptionGroup'
    TravelerBookingInfo:
      type: object
      properties:
        travelerId:
          description: The ID of the traveler.
          $ref: '#/components/schemas/UserId'
          deprecated: true
        travelerInfo:
          description: The information about the traveler of given travelerType.
          $ref: '#/components/schemas/AirRequestTravelerInfo'
        travelerType:
          $ref: '#/components/schemas/PassengerType'
        title:
          $ref: '#/components/schemas/UserTitle'
        name:
          $ref: '#/components/schemas/Name'
        gender:
          $ref: '#/components/schemas/Gender'
        dob:
          $ref: '#/components/schemas/DateModel'
        phoneNumber:
          $ref: '#/components/schemas/PhoneNumber'
        email:
          type: string
          format: email
          example: <EMAIL>
        identityDocs:
          type: array
          items:
            $ref: '#/components/schemas/IdentityDocument'
        address:
          $ref: '#/components/schemas/PostalAddress'
        emergencyContactInfo:
          deprecated: true
          $ref: '#/components/schemas/EmergencyContactInfo'
        loyaltyInfos:
          type: array
          items:
            $ref: '#/components/schemas/LoyaltyInfo'
        emergencyContact:
          $ref: '#/components/schemas/EmergencyContact'
    TravelerDetail:
      title: TravelerDetail
      description: Passenger information in the search response
      type: object
      required:
      - travelerId
      - travelerType
      - fareInfo
      properties:
        travelerId:
          type: string
          description: Unique identifier for the traveler in this response
          example: adult_0
        travelerType:
          $ref: '#/components/schemas/PassengerType'
        travelerAge:
          $ref: '#/components/schemas/PassengerAge'
        fareInfo:
          $ref: '#/components/schemas/FareInfo'
        fareMetadata:
          $ref: '#/components/schemas/FareMetadata'
    TravelerInfoResponse:
      title: TravelerInfoResponse
      description: 'The traveler identifiers. These can be either the Spotnana user
        IDs of the travelers or information regarding

        the adhoc travelers.

        '
      type: object
      oneOf:
      - $ref: '#/components/schemas/UserIdWrapper'
      - $ref: '#/components/schemas/AdhocTravelerInfoWrapper'
    TravelerLegInfo:
      title: TravelerLegInfo
      type: object
      description: Per leg information for the associated traveler
      required:
      - travelerId
      properties:
        travelerId:
          type: string
          description: Unique identifier for the traveler in this response
          example: adult_0
        travelerLegFare:
          description: The fare amount for the traveler for a given leg
          $ref: '#/components/schemas/FareAmount'
        fareRules:
          description: Fare rules applicable for the traveler
          $ref: '#/components/schemas/FareRules'
    TravelerMatchConditions:
      type: object
      title: TravelerMatchConditions
      description: Matching conditions for traveler.
      properties:
        workerTypes:
          type: array
          description: Worker types. Users belonging to any of these would match.
          items:
            $ref: '#/components/schemas/WorkerType'
        countries:
          type: array
          description: Countries.
          items:
            type: string
        legalEntities:
          type: array
          description: Legal entities
          items:
            $ref: '#/components/schemas/Reference'
        departments:
          type: array
          description: Departments
          items:
            $ref: '#/components/schemas/Reference'
        costCenters:
          type: array
          description: Cost centers
          items:
            $ref: '#/components/schemas/Reference'
        offices:
          type: array
          description: Offices
          items:
            $ref: '#/components/schemas/Reference'
    TravelerPersonalInfo:
      type: object
      title: TravelerPersonalInfo
      description: Traveler personal information.
      properties:
        loyaltyInfos:
          type: array
          items:
            $ref: '#/components/schemas/LoyaltyInfo'
        travelPref:
          $ref: '#/components/schemas/TravelPref'
    TravelerSearchInfo:
      type: object
      description: Traveler information required in the search request
      required:
      - travelerType
      properties:
        travelerType:
          $ref: '#/components/schemas/PassengerType'
        travelerAge:
          description: Age of the traveler. Only required if traveler type is Child
          $ref: '#/components/schemas/PassengerAge'
        travelerInfo:
          description: The information about the traveler of given travelerType.
          $ref: '#/components/schemas/AirRequestTravelerInfo'
    TravelerSeatInfo:
      description: Information about a traveler.
      type: object
      properties:
        travelerType:
          $ref: '#/components/schemas/PassengerType'
        travelerAge:
          description: Age of the traveler. Only required if traveler type is Child.
          $ref: '#/components/schemas/PassengerAge'
        travelerInfo:
          description: The information around the given traveler.
          $ref: '#/components/schemas/AirRequestTravelerInfo'
        loyaltyInfos:
          description: Information about the traveler's loyalty program memberships.
          type: array
          items:
            $ref: '#/components/schemas/SeatLoyaltyInfo'
    TravelerSeatMap:
      type: object
      description: Seat maps for a specific traveler.
      properties:
        travelerId:
          description: Unique identifier for the traveler in this response.
          $ref: '#/components/schemas/UserId'
        travelerInfo:
          description: Unique identifier for the traveler in this response.
          $ref: '#/components/schemas/TravelerInfoResponse'
        flightSeatMapIds:
          description: Seat maps for the specified travelerId. Ids refer to the id
            in AirSeatMapResponse -> seatMaps.
          type: array
          items:
            type: string
            example: seat_map_0
    TravelerUpdateInfo:
      type: object
      required:
      - traveler
      - updateTypes
      properties:
        traveler:
          $ref: '#/components/schemas/AirTravelerInfo'
        seats:
          type: array
          items:
            $ref: '#/components/schemas/SeatDetail'
        seatPreference:
          type: string
          description: In case seat is not selected for the traveler, this field can
            be used to set the seat preference and reserve seat if it is free of cost
          enum:
          - AISLE
          - WINDOW
        otherAncillaries:
          type: array
          items:
            $ref: '#/components/schemas/OtherAncillaryDetail'
        specialServiceRequests:
          type: array
          description: List of all Special Service Requests (SSR) for the traveler.
          items:
            $ref: '#/components/schemas/SpecialServiceRequestInfo'
        updateTypes:
          type: array
          uniqueItems: true
          items:
            description: fields to be updated
            type: string
            enum:
            - LOYALTY_INFO
            - REDRESS_NUMBER
            - KNOWN_TRAVELER_NUMBER
            - SEAT
            - SPECIAL_SERVICE_REQUEST
            - OTHER_ANCILLARY
            - UNDO_CHECK_IN
            - CHECK_IN
            - SEAT_PREFERENCE
            - PASSPORT
            - NATIONAL_DOC
    TripData:
      type: object
      title: TripData
      description: Trip data to book a PNR.
      required:
      - tripId
      properties:
        approverEmail:
          description: Email address of the approver who should receives approval
            email for the current booking.
          type: string
        approverName:
          description: Name of the approver.
          type: string
        hardApprovalRequired:
          description: 'Whether the current booking requires hard approval or soft
            approval.

            This flag should be used only if valid approver is present.

            '
          type: boolean
        outOfPolicy:
          description: If the given booking is out of policy.
          type: boolean
        policyId:
          description: Policy Id for which violation is done.
          type: string
        policyVersion:
          description: Version of policy.
          type: integer
          format: int32
        tripId:
          description: Id of the trip.
          $ref: '#/components/schemas/TripId'
    TripId:
      type: object
      title: TripId
      description: Id of trip.
      required:
      - id
      properties:
        id:
          description: Id.
          type: string
          example: '2783425534'
    TripUsageType:
      title: TripUsageType
      description: Trip usage type.
      type: string
      enum:
      - STANDARD
      - EVENT
    UnusablePaymentSource:
      type: object
      title: UnusablePaymentSource
      description: The payment sources which are not usable for the payment rule
      required:
      - id
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier identifying this payment source
          example: f49d00fe-1eda-4304-ba79-a980f565281d
    UnusedCreditFilter:
      type: object
      properties:
        hideWithoutCredits:
          type: boolean
          description: If true, only itineraries with applicable unused credits are
            returned.
    UnusedCreditInfo:
      type: object
      title: UnusedCreditInfo
      description: Information regarding an unused credit.
      properties:
        sourcePnr:
          type: string
          description: PNR number corresponding to third party through which booking
            was made.
          example: MC5ONS
        spotnanaPnr:
          type: string
          description: Spotnana pnr ID.
          example: '2345678'
        ticketNumber:
          type: string
          description: Ticket number for the ticket that was converted into an unused
            credit.
          example: '5267779139217'
        airlineCode:
          type: string
          description: 2 letter airline code of the airline associated with this unused
            credit.
          example: AA
        airlineInfo:
          description: Airline info with airline name and code
          $ref: '#/components/schemas/AirlineInfo'
        totalFare:
          description: Total airfare associated with the original ticket.
          $ref: '#/components/schemas/Money'
        issueDate:
          description: Issue date for the unused credit.
          $ref: '#/components/schemas/DateTimeOffset'
        expiryDate:
          description: Expiry date for the unused credit.
          $ref: '#/components/schemas/DateTimeOffset'
        usedDate:
          description: Date on which the unused credit was used.
          $ref: '#/components/schemas/DateTimeOffset'
        departureDate:
          description: Date for the departure of the first flight associated with
            the unused credit.
          $ref: '#/components/schemas/DateTimeOffset'
        segmentsAvailable:
          type: string
          description: Whether all segments are unused or some have already been used.
          enum:
          - UNKNOWN
          - ALL_OPEN
          - PARTIAL
          - OTHER
        passengerName:
          description: Name of the passenger associated with the credit.
          $ref: '#/components/schemas/Name'
        departureCountry:
          type: string
          description: 3 letter country code of the departure country associated with
            the original ticket.
          example: USA
        arrivalCountry:
          type: string
          description: 3 letter country code of the arrival country associated with
            the original ticket.
          example: USA
        ticketType:
          type: string
          description: Type of credit.
          enum:
          - TICKET_TYPE_UNKNOWN
          - ETICKET
          - MCO
          - NON_GDS
        pcc:
          type: string
          description: PCC the credit was issued on.
        status:
          $ref: '#/components/schemas/CreditStatus'
        source:
          $ref: '#/components/schemas/ThirdPartySource'
          default: SABRE
          description: Source of unused credit e.g. Sabre, NDC etc.
        tripId:
          type: string
          description: Trip ID that contains the unused credit
          example: '1234567'
        redeemVia:
          type: string
          description: "Credit redemption method. \nIf the value contains `CONTACT_AGENT`,\
            \ then the agent must book the ticket and redeem the credits on behalf\
            \ of the traveler.\n"
          enum:
          - REDEEM_VIA_OBT
          - CONTACT_AGENT
          example: REDEEM_VIA_OBT
        sourceOfTruth:
          type: string
          description: The system that owns the credit.
          enum:
          - SPOTNANA
          - MANUAL_FORM
        owningPcc:
          type: string
          description: PCC the PNR was created on.
        paymentSourceId:
          type: string
          format: uuid
          description: Payment source ID associated with the credit.
          example: edd5b835-8001-430c-98f8-fedeccebe4cf
        creditUsageType:
          $ref: '#/components/schemas/CreditUsageType'
          description: The type of credit usage. This can be either COMPANY or PERSONAL.
        email:
          type: string
          description: Email of the passenger owning the unused credit.
    UrlVerificationInfo:
      type: object
      description: Verification information required for url based payment verification.
      properties:
        ccVerificationUrl:
          description: Verification url for the payment source.
          type: string
    UrlVerificationInfoWrapper:
      type: object
      title: UrlVerificationInfoWrapper
      description: Wrapper for stripe verification Info.
      properties:
        urlVerificationInfo:
          $ref: '#/components/schemas/UrlVerificationInfo'
    User:
      type: object
      title: User
      description: User details.
      properties:
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/PostalAddress'
        dob:
          $ref: '#/components/schemas/DateModel'
        email:
          type: string
          format: email
          example: <EMAIL>
        emergencyContactInfo:
          deprecated: true
          $ref: '#/components/schemas/EmergencyContactInfo'
        emergencyContact:
          $ref: '#/components/schemas/EmergencyContact'
        gender:
          $ref: '#/components/schemas/Gender'
        identityDocs:
          description: List of user identity documents.
          type: array
          items:
            $ref: '#/components/schemas/IdentityDocument'
          example:
          - passport:
              docId: PASSPORTID
              expiryDate:
                iso8601: '2017-07-21'
              issueCountry: IN
              issuedDate:
                iso8601: '2017-07-21'
              nationalityCountry: IN
              type: REGULAR
          - ktn:
              number: '123456'
              issueCountry: US
        name:
          $ref: '#/components/schemas/Name'
        paymentInfos:
          type: array
          items:
            $ref: '#/components/schemas/PaymentInfo'
        phoneNumbers:
          type: array
          items:
            $ref: '#/components/schemas/PhoneNumber'
        profilePicture:
          $ref: '#/components/schemas/Image'
        nationality:
          type: string
          example: Indian
          description: Nationality of user
        title:
          $ref: '#/components/schemas/UserTitle'
    UserBasicInfo:
      type: object
      title: UserBasicInfo
      description: Basic user information.
      properties:
        userOrgId:
          $ref: '#/components/schemas/UserOrgId'
        persona:
          $ref: '#/components/schemas/Persona'
        email:
          type: string
          format: email
          example: <EMAIL>
        name:
          $ref: '#/components/schemas/Name'
        profilePicture:
          $ref: '#/components/schemas/Image'
        tier:
          $ref: '#/components/schemas/Tier'
        phoneNumber:
          $ref: '#/components/schemas/PhoneNumber'
        employeeId:
          type: string
          description: Employee id of the user
        isActive:
          type: boolean
          description: Whether user profile is active or not.
          example: true
    UserBusinessInfo:
      type: object
      title: UserBusinessInfo
      description: User business information.
      required:
      - email
      - employeeId
      - organizationId
      - legalEntityId
      properties:
        department:
          $ref: '#/components/schemas/Department'
        designation:
          type: string
          example: MANAGER
        email:
          type: string
          format: email
          example: <EMAIL>
        employeeId:
          description: Unique employee id. Can use email if a company don't use employee
            ids.
          type: string
          example: '101'
        grade:
          $ref: '#/components/schemas/Grade'
        legalEntityId:
          $ref: '#/components/schemas/LegalEntityId'
        managerBasicInfo:
          $ref: '#/components/schemas/UserBasicInfo'
        office:
          $ref: '#/components/schemas/Office'
        organizationId:
          $ref: '#/components/schemas/OrganizationId'
        phoneNumbers:
          type: array
          items:
            $ref: '#/components/schemas/PhoneNumber'
        userCostCenter:
          $ref: '#/components/schemas/CostCenter'
        designatedApproverInfos:
          deprecated: true
          type: array
          description: A list of user basic info for designated approvers.
          items:
            $ref: '#/components/schemas/UserBasicInfo'
        designatedApproverUserIds:
          type: array
          description: A list of userId for designated approvers.
          items:
            $ref: '#/components/schemas/UserId'
        authorizerEmail:
          type: string
          description: Email address to be used as approval authorizer, when a manager
            is not present.
          example: <EMAIL>
    UserId:
      type: object
      title: UserId
      description: User identifier
      required:
      - id
      properties:
        id:
          type: string
          format: uuid
    UserIdWrapper:
      title: UserIdWrapper
      description: Wrapper over UserId object to be used as an identifier in a oneOf
        case
      properties:
        userId:
          $ref: '#/components/schemas/UserId'
    UserOrgId:
      type: object
      title: UserOrgId
      description: User and user's organization information.
      required:
      - userId
      - organizationId
      properties:
        organizationAgencyId:
          $ref: '#/components/schemas/OrganizationAgencyId'
        organizationId:
          $ref: '#/components/schemas/OrganizationId'
        userId:
          $ref: '#/components/schemas/UserId'
        tmcInfo:
          $ref: '#/components/schemas/TmcInfo'
          deprecated: true
        tmcBasicInfo:
          $ref: '#/components/schemas/TmcBasicInfo'
    UserOrgIdListWrapper:
      type: object
      title: UserOrgIdListWrapper
      properties:
        userOrgIdList:
          type: array
          items:
            $ref: '#/components/schemas/UserOrgId'
    UserOrgIdWrapper:
      type: object
      title: UserOrgIdWrapper
      properties:
        userOrgId:
          $ref: '#/components/schemas/UserOrgId'
    UserTitle:
      title: UserTitle
      type: string
      enum:
      - TITLE_UNKNOWN
      - MR
      - MS
      - MRS
      - MX
      - MASTER
      - MISS
      - DR
      - PROFESSOR
      - CAPTAIN
      - REVEREND
      - HONOURABLE
      - SIR
      - LADY
      - AMBASSADOR
      - LORD
      - BRIGADIER
      - SENATOR
      - DAME
      - JUSTICE
      - UK
      example: MR
    VaccineAmenity:
      type: object
      title: VaccineAmenity
      description: Vaccine Amenity properties
      properties:
        displayText:
          type: string
          example: Vaccination facility available
        vaccineDescription:
          type: string
          description: chek
        vaccineAttrDescription:
          type: string
    VaccineAmenityWrapper:
      type: object
      title: VaccineAmenityWrapper
      properties:
        vaccineAmenity:
          $ref: '#/components/schemas/VaccineAmenity'
    Variable:
      type: object
      title: Variable
      description: Variable defining the additional value to be sent along with custom
        field response.
      required:
      - type
      - name
      properties:
        type:
          type: string
          example: VARIABLE
          default: VARIABLE
        name:
          $ref: '#/components/schemas/VariableName'
    VariableName:
      type: string
      title: VariableName
      description: Name of the variable.
      enum:
      - PUBLISHED_FARE
      - LLF
      example: PUBLISHED_FARE
    VendorProgramType:
      type: string
      description: The type of the vendor program
      enum:
      - UA_PASS_PLUS
      - GENERIC_PROGRAM_TYPE
    WaiveOffFeeReason:
      type: object
      title: WaiveOffFeeReason
      description: Fee waive off reason info.
      properties:
        reasonCode:
          type: string
          description: Fee waive off reason code.
          example: WA05
        reason:
          type: string
          description: Fee waive off reason description.
          example: Customer goodwill
    WaiveOffInfo:
      type: object
      title: WaiveOffInfo
      description: Waive off info.
      properties:
        waiveOffInfoPerModules:
          type: array
          description: Waive off info per module.
          items:
            $ref: '#/components/schemas/WaiveOffInfoPerModule'
    WaiveOffInfoPerModule:
      type: object
      title: WaiveOffInfoPerModule
      description: Waive off info per module.
      properties:
        moduleId:
          $ref: '#/components/schemas/ModuleId'
        waiveOffFeeReason:
          $ref: '#/components/schemas/WaiveOffFeeReason'
    WeightLimit:
      description: The weight limit of a baggage option.
      type: object
      properties:
        weight:
          description: The weight limit of a baggage option.
          type: number
          example: 32
        unit:
          description: The unit of measurement for the weight limit.
          type: string
          enum:
          - kg
          - lb
          example: kg
    WifiAmenity:
      type: object
      title: WifiAmenity
      description: Wifi Amenity properties
      properties:
        displayText:
          type: string
          example: Basic web browsing (fee)
        cost:
          type: string
          example: paid
    WifiAmenityWrapper:
      type: object
      title: WifiAmenityWrapper
      properties:
        wifiAmenity:
          $ref: '#/components/schemas/WifiAmenity'
    WorkerType:
      type: string
      description: The type of worker.
      enum:
      - EMPLOYEE
      - CONTINGENT
      - SEASONAL
      - INTERN
      - GUEST
      example: EMPLOYEE
      x-enumValidFrom:
        GUEST: '2025-06-03'
    WorkerTypeListWrapper:
      type: object
      title: WorkerTypeListWrapper
      properties:
        workerTypeList:
          type: array
          items:
            $ref: '#/components/schemas/WorkerType'
    WorkerTypeWrapper:
      type: object
      title: WorkerTypeWrapper
      properties:
        workerType:
          $ref: '#/components/schemas/WorkerType'
    WorkflowIds:
      type: object
      description: The set of unique response ids associated with the booking workflow.
      properties:
        checkoutResponseId:
          type: string
          description: ID sent by the backend in the air checkout response.
        seatMapResponseId:
          type: string
          description: ID sent by the backend in the air seat map response.
        paymentSetupResponseId:
          type: string
          description: ID set by the backend in the payment setup API response.
        initiateBookingId:
          type: string
          description: ID set by the backend in the initiate booking API response.
tags:
- name: Air
  description: APIs to perform search, checkout and book an air pnr
paths:
  /v2/air-autocomplete:
    get:
      tags:
      - Autocomplete
      summary: airAutocomplete
      operationId: airAutocomplete
      parameters:
      - name: query
        in: query
        description: Query to get autocomplete response
        required: true
        schema:
          type: string
        example: LHR
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirAutocompleteResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/air/search-flights:
    post:
      tags:
      - Air
      summary: Get air itineraries
      description: Get Air Itineraries available for the given search parameters
      operationId: airSearch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AirSearchRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirSearchResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/flight-attributes:
    post:
      tags:
      - Air
      summary: Get flight attributes
      description: "Get Flight attributes, amenities and fare rules for the given\
        \ search response id. \nReturns the full air search response along with attribute\
        \ details.\n"
      operationId: airAttributes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AirAttributesRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirSearchResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/selected-itinerary:
    post:
      tags:
      - Air
      summary: Get selected itinerary
      description: Get the selected itinerary for the traveler in the search and checkout
        flow.
      operationId: airSelectedItinerary
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AirSelectedItineraryRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirSelectedItineraryResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/loyalty-programs:
    parameters:
    - name: marketingAirline
      in: query
      description: IATA code of the marketing airline for which the loyalty programs
        need to be fetched.
      required: false
      schema:
        type: string
      example: AA
    - name: operatingAirline
      in: query
      description: IATA code of the operating airline for which the loyalty programs
        need to be fetched.
      required: false
      schema:
        type: string
      example: BA
    - name: travelerId
      in: query
      description: User ID of the traveler.
      required: false
      schema:
        type: string
      example: 4974a66b-7493-4f41-908c-58ba81093947
    - name: travelArrangerId
      in: query
      description: User ID of the travel arranger.
      required: false
      schema:
        type: string
      example: 4974a66b-7493-4f41-908c-58ba81093947
    get:
      tags:
      - Loyalty Programs
      summary: Get Loyalty Programs (Air)
      description: Returns all the active loyalty programs for the requested marketing
        and/or operating airlines.
      operationId: getAirApplicableLoyalties
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirApplicableLoyaltyResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/airlines-info:
    parameters:
    - name: includeInactive
      in: query
      description: Include Airlines with inactive status along with active.
      required: true
      schema:
        type: boolean
    get:
      tags:
      - Air
      summary: Get Airlines Information (Air)
      description: Returns airlines information for active marked airlines.
      operationId: getAirlinesInformation
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirlineInfoResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/flight-checkout:
    post:
      tags:
      - Air
      summary: Get flight checkout details
      description: Get the flight checkout details associated with the selected itinerary.
      operationId: airFlightCheckout
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AirCheckoutRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirCheckoutResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/seat-map:
    post:
      tags:
      - Air
      summary: Get flight seat map
      description: Get seat map for the flights in the given itinerary.
      operationId: airSeatMap
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AirSeatMapRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirSeatMapResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/initiate-booking:
    post:
      tags:
      - Air
      summary: Initiate booking
      description: "Initiates a new air booking for the trip. \nThe `identityDocs`\
        \ field supports the following traveler identity documents: \nPassport, National\
        \ ID, Known Traveler Number (KTN), redress number, and immigration documents.\
        \ \nThe traveler can choose one among the list of identity documents available\
        \ and provide its information during checkout.\n"
      operationId: airInitiateBooking
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AirInitiateBookingRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirInitiateBookingResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/revalidate-itinerary:
    post:
      tags:
      - Air
      summary: Validate air itinerary
      description: Validates the air itinerary for availability and pricing
      operationId: airRevalidateItinerary
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AirRevalidateItineraryRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirRevalidateItineraryResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/intermediate-revalidate-itinerary:
    post:
      tags:
      - Air
      summary: Validate air itinerary (interim)
      description: Validates an itinerary at any interim stage of air booking workflow
        and allows you to keep track of fare details before proceeding to checkout.
        Using this endpoint is optional. It requires a `searchId` and the `itineraryId`
        to obtain the latest fare details of the selected itinerary. The `travelerType`
        field in this endpoint response currently only accepts `ADULT`, `CHILD` and
        `INFANT`.
      operationId: airIntermediateRevalidateItinerary
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AirIntermediateRevalidateItineraryRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirRevalidateItineraryResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/create-pnr:
    post:
      tags:
      - Air
      summary: Create air pnr
      description: Creates a new air booking with the given itinerary
      operationId: airCreatePnr
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AirCreatePnrRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirCreatePnrResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/cancel-pnr:
    post:
      tags:
      - Air
      summary: Cancel Air Pnr
      description: Cancel the air pnr and issues applicable refunds
      operationId: airCancelPnr
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AirCancelPnrRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirCancelPnrResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/pnrs/{pnrId}/exchange-details:
    parameters:
    - name: pnrId
      in: path
      description: Spotnana PNR ID.
      required: true
      schema:
        type: string
        example: '6926658168'
    get:
      tags:
      - Air
      summary: Get PNR exchange details
      description: 'This endpoint gets exchange details of all the tickets present
        in the given PNR ID of the

        given trip ID.

        '
      operationId: getAirPnrExchangeDetails
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirPnrExchangeDetailsResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/modify-search:
    post:
      tags:
      - Air
      summary: Modify Search
      description: Get Air Itineraries available for exchange/modification/deletion
        against current pnr.
      operationId: airModifySearchV2
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AirModifySearchRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirSearchResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/modify-book:
    post:
      tags:
      - Air
      summary: Modify Book
      description: Book[cancel] new[existing] legs into an existing pnr using the
        selected rate option from airModifySearch operation.
      operationId: airModifyBookV2
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AirModifyBookingRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirModifyBookingResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/pnrs/{pnrId}/update:
    post:
      tags:
      - Air
      summary: Edit PNR
      description: "Edit specific traveler information in an existing PNR.\nThis API\
        \ allows you to edit and modify Special Service Requests (SSR), \nseat selection,\
        \ loyalty number, ancillaries, redress number, Known Traveler Number (KTN),\
        \ \ncheck-in preferences, and Other Service Information (OSI).\n"
      operationId: airPnrEdit
      parameters:
      - in: path
        name: pnrId
        description: PNR ID created by Spotnana for the booking.
        required: true
        schema:
          type: string
        example: '**********'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AirEditPnrRequest'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirEditPnrResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/air/special-service-requests:
    get:
      tags:
      - Air
      summary: Get Special Service Requests (SSR)
      description: "Returns the list of all Special Service Requests (SSR) supported\
        \ by Spotnana.\nThis API doesn't require a request payload or a query parameter.\n\
        You can use this endpoint anytime during the air booking or exchange workflow\
        \ \nto view the list of SSRs currently supported by Spotnana.\n"
      operationId: getSpecialServiceRequests
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SpecialServiceRequestsResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/air/special-service-requests/categories:
    get:
      tags:
      - Air
      summary: Get Special Service Requests (SSR) for a Category
      description: "Returns the list of Special Service Requests (SSR) for a selected\
        \ category.\nEvery `category` type can contain multiple service requests.\n\
        For example, the `category: MEAL` includes service requests such as a vegetarian\
        \ meal, baby/infant meal, \ngluten free meal, and so on. These service requests\
        \ are displayed as `subCategory` fields within the `category: MEAL`. \nTo\
        \ learn more about a selected category such as its `subCategory` values, category\
        \ code, and other relevant\ninformation, run this API using the `category`\
        \ type as the query parameter.\n"
      operationId: getSpecialServiceRequestsFilterByCategory
      parameters:
      - name: category
        in: query
        description: The category type of a Special Service Request (SSR).
        required: true
        schema:
          type: string
          description: Category details of the Special Service Request (SSR).
          enum:
          - BASSINET
          - SPECIAL_ASSISTANCE_WHEELCHAIR
          - BAGGAGE
          - UNACCOMPANIED_MINOR
          - PETS
          - MEET_AND_ASSIST
          - OTHERS
          - MEAL
          - SPECIAL_ASSISTANCE_DISABILITY
          example: SPECIAL_ASSISTANCE_WHEELCHAIR
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SpecialServiceRequestsResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/air/fare-rules/fetch:
    post:
      tags:
      - Air
      summary: Returns the Air Raw Fare Rules for a Segment or Pnr
      operationId: fetchAirFareRules
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FetchAirFareRuleRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FetchAirFareRuleResponse'
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v3/air/fetch-traveler-unused-credits:
    post:
      tags:
      - Air
      operationId: fetchTravelerUnusedCredits
      summary: Get unused credits for a traveler
      description: "Returns a list of unused credits associated with a traveler \n\
        along with details such as the PNR ID, trip ID, airline details, credit expiry\
        \ date, and the source of booking (e.g., NDC). \nTravelers can redeem unused\
        \ credit for future bookings with the same airline where the credit was earned.\n"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              title: FetchTravelerUnusedCreditsRequest
              description: Request for fetching unused credits for a traveler.
              $ref: '#/components/schemas/FetchTravelerUnusedCreditsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                title: FetchTravelerUnusedCreditsResponse
                $ref: '#/components/schemas/FetchTravelerUnusedCreditsResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v3/air/company-unused-credits/list:
    post:
      tags:
      - Air
      operationId: fetchCompanyUnusedCredits
      summary: Get unused credits for a company
      description: 'Returns the list of company unused credits which can be used by
        any employees of the company.

        '
      requestBody:
        required: true
        content:
          application/json:
            schema:
              title: CompanyUnusedCreditsListRequest
              description: Request for fetching unused credits for a company.
              $ref: '#/components/schemas/CompanyUnusedCreditsListRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                title: CompanyUnusedCreditsListResponse
                $ref: '#/components/schemas/CompanyUnusedCreditsListResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v3/air/company-transferable-unused-credits/list:
    post:
      tags:
      - Air
      operationId: fetchCompanyTransferableUnusedCredits
      summary: Get transferable unused credits for a company
      description: 'Returns the list of transferable company unused credits which
        can be used by any employees of the company.

        The unused credits can be transferred to another traveler.

        '
      requestBody:
        required: true
        content:
          application/json:
            schema:
              title: CompanyTransferableUnusedCreditsListRequest
              description: Request for fetching transferable unused credits for a
                company.
              $ref: '#/components/schemas/CompanyTransferableUnusedCreditsListRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                title: CompanyTransferableUnusedCreditsListResponse
                $ref: '#/components/schemas/CompanyTransferableUnusedCreditsListResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v3/air/company-credits/migrations:
    post:
      tags:
      - Air
      operationId: migrateUnusedCreditsInCompanyCredits
      summary: Migrate Unused Credits of a traveler to and fro Company Credits
      description: This API will be migrate unused credits from individual credits
        to company credits and vice versa.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              title: MigrateUnusedCreditsToCompanyCreditsRequest
              description: Request for migrating unused credits.
              $ref: '#/components/schemas/MigrateUnusedCreditsToCompanyCreditsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                title: MigrateUnusedCreditsToCompanyCreditsResponse
                $ref: '#/components/schemas/MigrateUnusedCreditsToCompanyCreditsResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
