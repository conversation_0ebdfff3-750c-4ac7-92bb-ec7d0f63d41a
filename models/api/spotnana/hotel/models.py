# Code generated from OpenAPI spec. DO NOT EDIT.
# Source: HotelApi.yaml
# Generated by: datamodel-code-generator
# 
# This file is automatically generated from the Spotnana Hotel API OpenAPI specification.
# Any manual changes will be overwritten when the models are regenerated.
# 
# To regenerate: uv run python manage_spotnana_models.py generate

# generated by datamodel-codegen:
#   filename:  HotelApi.yaml
#   timestamp: 2025-07-15T00:32:24+00:00

from __future__ import annotations

from collections.abc import Sequence
from enum import Enum
from typing import Literal
from uuid import UUID

from pydantic import (
    BaseModel,
    ConfigDict,
    EmailStr,
    Field,
    RootModel,
    confloat,
    conint,
    constr,
)


class Accessibility(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['ACCESSIBLE_ROOM'] = Field(
        'ACCESSIBLE_ROOM', description='The type of accessibility feature.'
    )
    additionalInfo: str | None = Field(
        None, description='Additional information about the accessibility feature.'
    )
    isSelectionRequired: bool = Field(
        ...,
        description='Indicates if the selection of this accessibility feature is required.',
    )


class AirlineInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlineCode: str = Field(..., description='IATA code for airline.', examples=['AA'])
    airlineName: str = Field(
        ..., description='Airline name', examples=['American Airlines']
    )


class FlightType(Enum):
    UNKNOWN_FLIGHT_TYPE = 'UNKNOWN_FLIGHT_TYPE'
    DOMESTIC = 'DOMESTIC'
    INTERNATIONAL = 'INTERNATIONAL'
    ALL = 'ALL'


class AirlinePref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlines: Sequence[str] | None = None
    flightType: FlightType | None = Field(None, examples=['DOMESTIC'])


class AirportSearch(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchType: Literal['AIRPORT'] = Field('AIRPORT', examples=['AIRPORT'])
    airport: str = Field(
        ...,
        description='IATA code of the airport. The search result will contain the list of hotels in close proximity to the specified airport.',
        examples=['LHR'],
    )


class Alliance(Enum):
    UNKNOWN_ALLIANCE = 'UNKNOWN_ALLIANCE'
    STAR_ALLIANCE = 'STAR_ALLIANCE'
    ONEWORLD = 'ONEWORLD'
    SKYTEAM = 'SKYTEAM'
    VANILLA_ALLIANCE = 'VANILLA_ALLIANCE'
    U_FLY_ALLIANCE = 'U_FLY_ALLIANCE'
    VALUE_ALLIANCE = 'VALUE_ALLIANCE'


class AlliancePref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    alliances: Sequence[Alliance]


class AnswerPair(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    item: str | None = Field(
        None, description='The option selected from the list of available choices.'
    )
    value: str | None = Field(
        None,
        description='The additional input provided (by the user) while selecting one of the options.',
    )
    description: str | None = Field(
        None, description='Description of the selected option.'
    )


class ApprovalType(Enum):
    HARD_APPROVAL = 'HARD_APPROVAL'
    SOFT_APPROVAL = 'SOFT_APPROVAL'
    PASSIVE_APPROVAL = 'PASSIVE_APPROVAL'


class BedType(Enum):
    UNKNOWN_BED_TYPE = 'UNKNOWN_BED_TYPE'
    DOUBLE = 'DOUBLE'
    FUTON = 'FUTON'
    KING = 'KING'
    MURPHY = 'MURPHY'
    QUEEN = 'QUEEN'
    SOFA = 'SOFA'
    TATAMI_MATS = 'TATAMI_MATS'
    TWIN = 'TWIN'
    SINGLE = 'SINGLE'
    FULL = 'FULL'
    RUN_OF_THE_HOUSE = 'RUN_OF_THE_HOUSE'
    DORM = 'DORM'
    WATER = 'WATER'
    PULL_OUT = 'PULL_OUT'
    TWIN_XL = 'TWIN_XL'


class BoolWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    b: bool | None = None


class CarType(Enum):
    OTHER = 'OTHER'
    MINI = 'MINI'
    ECONOMY = 'ECONOMY'
    COMPACT = 'COMPACT'
    MID_SIZE = 'MID_SIZE'
    STANDARD = 'STANDARD'
    FULL_SIZE = 'FULL_SIZE'
    PREMIUM = 'PREMIUM'
    LUXURY = 'LUXURY'
    CONVERTIBLE = 'CONVERTIBLE'
    MINIVAN = 'MINIVAN'
    SUV = 'SUV'
    VAN = 'VAN'
    PICKUP = 'PICKUP'
    SPORTS = 'SPORTS'
    SPECIAL = 'SPECIAL'
    RECREATIONAL_VEHICLE = 'RECREATIONAL_VEHICLE'
    WAGON = 'WAGON'


class CarVendor(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    code: str = Field(..., description='Vendor code', examples=['ZE'])
    name: str = Field(..., description='Vendor name', examples=['HERTZ'])
    isPresentInPreferredVendors: bool | None = Field(
        None,
        description='Whether the car vendor is present in preferred vendor list. This is an optional field which gets populated only in the preferred vendor autocomplete API.',
        examples=[True],
    )


class Type(Enum):
    UNKNOWN = 'UNKNOWN'
    CREDIT = 'CREDIT'
    DEBIT = 'DEBIT'


class CardCompany(Enum):
    NONE = 'NONE'
    VISA = 'VISA'
    MASTERCARD = 'MASTERCARD'
    AMEX = 'AMEX'
    DISCOVER = 'DISCOVER'
    AIR_TRAVEL_UATP = 'AIR_TRAVEL_UATP'
    CARTE_BLANCHE = 'CARTE_BLANCHE'
    DINERS_CLUB = 'DINERS_CLUB'
    JCB = 'JCB'
    BREX = 'BREX'
    UNION_PAY = 'UNION_PAY'
    EURO_CARD = 'EURO_CARD'
    ACCESS_CARD = 'ACCESS_CARD'
    ELO_CARD = 'ELO_CARD'


class CardType(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cardCompany: CardCompany = Field(
        ..., description='The company that issued the card.'
    )
    description: str | None = Field(None, description='A description of the card type.')


class CentralCardAccessLevel(Enum):
    UNKNOWN = 'UNKNOWN'
    ORGANIZATION = 'ORGANIZATION'
    LEGAL_ENTITY = 'LEGAL_ENTITY'
    TMC = 'TMC'


class CoachPref(Enum):
    SMOKING = 'SMOKING'
    NON_SMOKING = 'NON_SMOKING'
    PETS_ALLOWED = 'PETS_ALLOWED'
    RESTAURANT = 'RESTAURANT'
    QUIET = 'QUIET'


class CompanyId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID = Field(..., examples=['f49d00fe-1eda-4304-ba79-a980f565281d'])


class Condition(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    url: str = Field(
        ...,
        description='Url for terms and conditions.',
        examples=['https://www.amtrak.com/terms-and-conditions.html'],
    )
    text: str = Field(..., description='Display text for the url.', examples=['Amtrak'])


class ConditionalRate(Enum):
    MILITARY = 'MILITARY'
    AAA = 'AAA'
    GOVERNMENT = 'GOVERNMENT'


class CostCenterId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['731ccbca-0415-6fe1-d235-c324dfbe7423'])


class TicketType(Enum):
    TICKET_TYPE_UNKNOWN = 'TICKET_TYPE_UNKNOWN'
    ETICKET = 'ETICKET'
    MCO = 'MCO'


class SegmentsAvailable(Enum):
    UNKNOWN = 'UNKNOWN'
    ALL_OPEN = 'ALL_OPEN'
    PARTIAL = 'PARTIAL'
    OTHER = 'OTHER'


class CreditCardAccessType(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    CENTRALISED = 'CENTRALISED'
    INDIVIDUAL = 'INDIVIDUAL'
    PERSONAL = 'PERSONAL'
    TMC = 'TMC'
    APPLICATION = 'APPLICATION'
    ITINERARY = 'ITINERARY'
    EVENTS = 'EVENTS'
    TRAVEL_ARRANGER_MANAGED = 'TRAVEL_ARRANGER_MANAGED'
    COMPANY_TRAVEL_ARRANGER_MANAGED = 'COMPANY_TRAVEL_ARRANGER_MANAGED'
    EVENT_TEMPLATE = 'EVENT_TEMPLATE'


class CustomFieldLocation(Enum):
    POLICY_APPROVAL_EMAIL = 'POLICY_APPROVAL_EMAIL'
    PNR_EMAIL = 'PNR_EMAIL'
    TRIP_EMAIL = 'TRIP_EMAIL'


class CustomFieldOptionsParam(Enum):
    COST_CENTER = 'COST_CENTER'
    LEGAL_ENTITY = 'LEGAL_ENTITY'
    OFFICE = 'OFFICE'
    DEPARTMENT = 'DEPARTMENT'


class CustomFieldType(Enum):
    QUESTION = 'QUESTION'
    MEETING = 'MEETING'
    BUDGET = 'BUDGET'
    BREX_TOKEN = 'BREX_TOKEN'


class DateModel(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$') = (
        Field(..., examples=['2017-07-21'])
    )


class DateTimeLocal(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(
        pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$'
    ) = Field(..., examples=['2017-07-21T17:32'])


class DateTimeOffset(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(
        pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?(Z|([+-](0[0-9]|1[0-4]):([0-5][0-9])))$'
    ) = Field(..., examples=['2017-07-21T17:32Z'])


class DeckLevel(Enum):
    UPPER_DECK = 'UPPER_DECK'
    LOWER_DECK = 'LOWER_DECK'


class DepartmentId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['631ccbcf-9414-5fe0-c234-b324dfbe7422'])


class Dimensions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    height: int | None = Field(None, examples=[120])
    width: int | None = Field(None, examples=[240])


class DoubleListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    dList: Sequence[float] | None = None


class DoubleRange(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    min: float | None = Field(None, description='Minimum value - inclusive.')
    max: float | None = Field(None, description='Maximum value - inclusive.')


class DoubleRangeWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    dRange: DoubleRange | None = None


class DoubleWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    d: float | None = None


class Duration(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: str | None = Field(
        None,
        description='Durations define the amount of intervening time in a time interval and are represented by the\nformat P[n]Y[n]M[n]DT[n]H[n]M[n]S.\nThe [n] is replaced by the value for each of the date and time elements that follow the [n].\nLeading zeros are not required. The capital letters P, Y, M, W, D, T, H, M, and S are\ndesignators for each of the date and time elements and are not replaced. P is the duration\ndesignator (for period) placed at the start of the duration representation.\nY is the year designator.\nM is the month designator.\nW is the week designator.\nD is the day designator.\nT is the time designator.\nH is the hour designator.\nM is the minute designator.\nS is the second designator and can include decimal digits with arbitrary precision.\n',
        examples=['PT19H55M'],
    )


class Relation(Enum):
    RELATION_UNKNOWN = 'RELATION_UNKNOWN'
    SPOUSE = 'SPOUSE'
    PARENT = 'PARENT'
    SIBLING = 'SIBLING'
    CHILD = 'CHILD'
    FRIEND = 'FRIEND'
    RELATIVE = 'RELATIVE'
    COLLEAGUE = 'COLLEAGUE'
    OTHER = 'OTHER'


class EngineType(Enum):
    UNKNOWN_ENGINE = 'UNKNOWN_ENGINE'
    PETROL = 'PETROL'
    DIESEL = 'DIESEL'
    ELECTRIC = 'ELECTRIC'
    CNG = 'CNG'
    HYBRID = 'HYBRID'
    HYDROGEN = 'HYDROGEN'
    MULTI_FUEL = 'MULTI_FUEL'
    ETHANOL = 'ETHANOL'


class EntityId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID


class ErrorParameter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str | None = Field(None, description='Parameter name')
    value: str | None = Field(None, description='Parameter value')


class ErrorMessage(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    errorCode: str | None = Field(
        None, description='Error code to identify the specific errors.'
    )
    message: str | None = Field(
        None, description='Message containing details of error.'
    )
    errorParameters: Sequence[ErrorParameter] | None = Field(
        None, description='Error message parameters.'
    )
    errorDetail: str | None = Field(None, description='More details about the error.')


class ErrorResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    debugIdentifier: str | None = Field(
        None, description='Link to debug the error internally.'
    )
    errorMessages: Sequence[ErrorMessage] | None = None


class Expiry(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiryMonth: conint(ge=1, le=12) = Field(
        ..., description='Expiry month', examples=[1]
    )
    expiryYear: conint(ge=2000) = Field(..., description='Expiry year', examples=[2010])


class ExpiryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiry: Expiry | None = None


class FareComponent(Enum):
    BASE = 'BASE'
    TAX = 'TAX'


class FareType(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    CHANGEABLE = 'CHANGEABLE'
    REFUNDABLE = 'REFUNDABLE'


class FarePref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fareTypes: Sequence[FareType]


class Gender(Enum):
    MALE = 'MALE'
    FEMALE = 'FEMALE'
    UNSPECIFIED = 'UNSPECIFIED'
    UNDISCLOSED = 'UNDISCLOSED'


class GradeId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['831ccbcb-1416-7fe2-e236-d324dfbe7424'])


class HotelAccessibleFeatureType(Enum):
    MOBILITY_ACCESSIBLE_ROOM_WITH_TUB = 'MOBILITY_ACCESSIBLE_ROOM_WITH_TUB'
    MOBILITY_ACCESSIBLE_ROOM_WITH_ROLL_IN_SHOWER = (
        'MOBILITY_ACCESSIBLE_ROOM_WITH_ROLL_IN_SHOWER'
    )
    HEARING_ACCESSIBLE_ROOM = 'HEARING_ACCESSIBLE_ROOM'
    MOBILITY_ACCESSIBLE_ROOM_WITH_TUB_AND_ROLL_IN_SHOWER = (
        'MOBILITY_ACCESSIBLE_ROOM_WITH_TUB_AND_ROLL_IN_SHOWER'
    )
    MOBILITY_ACCESSIBLE_ROOM_WITH_TUB_AND_HEARING_ACCESSIBLE_ROOM = (
        'MOBILITY_ACCESSIBLE_ROOM_WITH_TUB_AND_HEARING_ACCESSIBLE_ROOM'
    )
    MOBILITY_ACCESSIBLE_ROOM_WITH_ROLL_IN_SHOWER_AND_HEARING_ACCESSIBLE_ROOM = (
        'MOBILITY_ACCESSIBLE_ROOM_WITH_ROLL_IN_SHOWER_AND_HEARING_ACCESSIBLE_ROOM'
    )
    MOBILITY_ACCESSIBLE_ROOM_WITH_TRANSFER_SHOWER = (
        'MOBILITY_ACCESSIBLE_ROOM_WITH_TRANSFER_SHOWER'
    )
    MOBILITY_ACCESSIBLE_ROOM_WITH_TUB_AND_TRANSFER_SHOWER = (
        'MOBILITY_ACCESSIBLE_ROOM_WITH_TUB_AND_TRANSFER_SHOWER'
    )
    MOBILITY_ACCESSIBLE_ROOM_WITH_TRANSFER_SHOWER_AND_HEARING_ACCESSIBLE_ROOM = (
        'MOBILITY_ACCESSIBLE_ROOM_WITH_TRANSFER_SHOWER_AND_HEARING_ACCESSIBLE_ROOM'
    )


class HotelAdditionalDetailType(Enum):
    ADDITIONAL_DETAIL_TYPE_UNKNOWN = 'ADDITIONAL_DETAIL_TYPE_UNKNOWN'
    RATE_DESCRIPTION = 'RATE_DESCRIPTION'
    PROPERTY_DESCRIPTION = 'PROPERTY_DESCRIPTION'
    PROPERTY_LOCATION = 'PROPERTY_LOCATION'
    ROOM_INFORMATION = 'ROOM_INFORMATION'
    GUARANTEE_INFORMATION = 'GUARANTEE_INFORMATION'
    DEPOSIT_INFORMATION = 'DEPOSIT_INFORMATION'
    CANCELLATION_INFORMATION = 'CANCELLATION_INFORMATION'
    CHECK_IN_CHECK_OUT_INFORMATION = 'CHECK_IN_CHECK_OUT_INFORMATION'
    EXTRA_CHARGE_INFORMATION = 'EXTRA_CHARGE_INFORMATION'
    TAX_INFORMATION = 'TAX_INFORMATION'
    SERVICE_CHARGE_INFORMATION = 'SERVICE_CHARGE_INFORMATION'
    PACKAGE_INFORMATION = 'PACKAGE_INFORMATION'
    COMMISSION_INFORMATION = 'COMMISSION_INFORMATION'
    MISCELLANEOUS_INFORMATION = 'MISCELLANEOUS_INFORMATION'
    PROMOTIONAL_INFORMATION = 'PROMOTIONAL_INFORMATION'
    INCLUSION_INFORMATION = 'INCLUSION_INFORMATION'
    AMENITY_INFORMATION = 'AMENITY_INFORMATION'
    LATE_ARRIVAL_INFORMATION = 'LATE_ARRIVAL_INFORMATION'
    LATE_DEPARTURE_INFORMATION = 'LATE_DEPARTURE_INFORMATION'
    ADVANCED_BOOKING_INFORMATION = 'ADVANCED_BOOKING_INFORMATION'
    EXTRA_PERSON_INFORMATION = 'EXTRA_PERSON_INFORMATION'
    AREAS_SERVED = 'AREAS_SERVED'
    ONSITE_FACILITIES_INFORMATION = 'ONSITE_FACILITIES_INFORMATION'
    OFFSITE_FACILITIES_INFORMATION = 'OFFSITE_FACILITIES_INFORMATION'
    ONSITE_SERVICES_INFORMATION = 'ONSITE_SERVICES_INFORMATION'
    OFFSITE_SERVICES_INFORMATION = 'OFFSITE_SERVICES_INFORMATION'
    EXTENDED_STAY_INFORMATION = 'EXTENDED_STAY_INFORMATION'
    CORPORATE_BOOKING_INFORMATION = 'CORPORATE_BOOKING_INFORMATION'
    BOOKING_GUIDELINES = 'BOOKING_GUIDELINES'
    GOVERNMENT_BOOKING_POLICY = 'GOVERNMENT_BOOKING_POLICY'
    GROUP_BOOKING_INFORMATION = 'GROUP_BOOKING_INFORMATION'
    RATE_DISCLAIMER_INFORMATION = 'RATE_DISCLAIMER_INFORMATION'
    VISA_TRAVEL_REQUIREMENT_INFORMATION = 'VISA_TRAVEL_REQUIREMENT_INFORMATION'
    SECURITY_INFORMATION = 'SECURITY_INFORMATION'
    ONSITE_RECREATIONAL_ACTIVITIES_INFORMATION = (
        'ONSITE_RECREATIONAL_ACTIVITIES_INFORMATION'
    )
    OFFSITE_RECREATIONAL_ACTIVITIES_INFORMATION = (
        'OFFSITE_RECREATIONAL_ACTIVITIES_INFORMATION'
    )
    GENERAL_MEETING_PLANNING_INFORMATION = 'GENERAL_MEETING_PLANNING_INFORMATION'
    GROUP_MEETING_PLANNING_INFORMATION = 'GROUP_MEETING_PLANNING_INFORMATION'
    CONTRACT_NEGOTIATED_BOOKING_INFORMATION = 'CONTRACT_NEGOTIATED_BOOKING_INFORMATION'
    TRAVEL_INDUSTRY_BOOKING_INFORMATION = 'TRAVEL_INDUSTRY_BOOKING_INFORMATION'
    MEETING_ROOM_DESCRIPTION = 'MEETING_ROOM_DESCRIPTION'
    PET_POLICY_DESCRIPTION = 'PET_POLICY_DESCRIPTION'
    MEAL_PLAN_DESCRIPTION = 'MEAL_PLAN_DESCRIPTION'
    FAMILY_PLAN_DESCRIPTION = 'FAMILY_PLAN_DESCRIPTION'
    CHILDREN_INFORMATION = 'CHILDREN_INFORMATION'
    EARLY_CHECKOUT_DESCRIPTION = 'EARLY_CHECKOUT_DESCRIPTION'
    SPECIAL_OFFERS_DESCRIPTION = 'SPECIAL_OFFERS_DESCRIPTION'
    CATERING_DESCRIPTION = 'CATERING_DESCRIPTION'
    ROOM_DECOR_DESCRIPTION = 'ROOM_DECOR_DESCRIPTION'
    OVERSOLD_POLICY_DESCRIPTION = 'OVERSOLD_POLICY_DESCRIPTION'
    LAST_ROOM_AVAILABILITY_DESCRIPTION = 'LAST_ROOM_AVAILABILITY_DESCRIPTION'
    ROOM_TYPE_UPGRADE_DESCRIPTION = 'ROOM_TYPE_UPGRADE_DESCRIPTION'
    DRIVING_DIRECTIONS = 'DRIVING_DIRECTIONS'
    DRIVING_DIRECTIONS_FROM_THE_NORTH = 'DRIVING_DIRECTIONS_FROM_THE_NORTH'
    DRIVING_DIRECTIONS_FROM_THE_SOUTH = 'DRIVING_DIRECTIONS_FROM_THE_SOUTH'
    DRIVING_DIRECTIONS_FROM_THE_EAST = 'DRIVING_DIRECTIONS_FROM_THE_EAST'
    DRIVING_DIRECTIONS_FROM_THE_WEST = 'DRIVING_DIRECTIONS_FROM_THE_WEST'
    SURCHARGE_INFORMATION = 'SURCHARGE_INFORMATION'
    MINIMUM_STAY_INFORMATION = 'MINIMUM_STAY_INFORMATION'
    MAXIMUM_STAY_INFORMATION = 'MAXIMUM_STAY_INFORMATION'
    CHECK_IN_POLICY = 'CHECK_IN_POLICY'
    CHECK_OUT_POLICY = 'CHECK_OUT_POLICY'
    EXPRESS_CHECK_IN_POLICY = 'EXPRESS_CHECK_IN_POLICY'
    EXPRESS_CHECK_OUT_POLICY = 'EXPRESS_CHECK_OUT_POLICY'
    FACILITY_RESTRICTIONS = 'FACILITY_RESTRICTIONS'
    CUSTOMS_INFORMATION_FOR_MATERIAL = 'CUSTOMS_INFORMATION_FOR_MATERIAL'
    SEASONS = 'SEASONS'
    FOOD_AND_BEVERAGE_MINIMUMS_FOR_GROUPS = 'FOOD_AND_BEVERAGE_MINIMUMS_FOR_GROUPS'
    DEPOSIT_POLICY_FOR_MASTER_ACCOUNT = 'DEPOSIT_POLICY_FOR_MASTER_ACCOUNT'
    DEPOSIT_POLICY_FOR_RESERVATIONS = 'DEPOSIT_POLICY_FOR_RESERVATIONS'
    RESTAURANT_SERVICES = 'RESTAURANT_SERVICES'
    SPECIAL_EVENTS = 'SPECIAL_EVENTS'
    CUISINE_DESCRIPTION = 'CUISINE_DESCRIPTION'


class HotelAmenityType(Enum):
    TWENTY_FOUR_HOUR_FRONT_DESK = 'TWENTY_FOUR_HOUR_FRONT_DESK'
    TWENTY_FOUR_HOUR_ROOM_SERVICE = 'TWENTY_FOUR_HOUR_ROOM_SERVICE'
    TWENTY_FOUR_HOUR_SECURITY = 'TWENTY_FOUR_HOUR_SECURITY'
    ADJOINING_ROOMS = 'ADJOINING_ROOMS'
    AIR_CONDITIONING = 'AIR_CONDITIONING'
    AIRLINE_DESK = 'AIRLINE_DESK'
    ATM_CASH_MACHINE = 'ATM_CASH_MACHINE'
    BABY_SITTING = 'BABY_SITTING'
    BBQ_PICNIC_AREA = 'BBQ_PICNIC_AREA'
    BILINGUAL_STAFF = 'BILINGUAL_STAFF'
    BOOKSTORE = 'BOOKSTORE'
    BOUTIQUES_STORES = 'BOUTIQUES_STORES'
    BRAILED_ELEVATORS = 'BRAILED_ELEVATORS'
    BUSINESS_LIBRARY = 'BUSINESS_LIBRARY'
    CAR_RENTAL_DESK = 'CAR_RENTAL_DESK'
    CASINO = 'CASINO'
    CHECK_CASHING_POLICY = 'CHECK_CASHING_POLICY'
    CHECK_IN_KIOSK = 'CHECK_IN_KIOSK'
    COCKTAIL_LOUNGE = 'COCKTAIL_LOUNGE'
    COFFEE_SHOP = 'COFFEE_SHOP'
    COIN_OPERATED_LAUNDRY = 'COIN_OPERATED_LAUNDRY'
    CONCIERGE_DESK = 'CONCIERGE_DESK'
    CONCIERGE_FLOOR = 'CONCIERGE_FLOOR'
    CONFERENCE_FACILITIES = 'CONFERENCE_FACILITIES'
    COURTYARD = 'COURTYARD'
    CURRENCY_EXCHANGE = 'CURRENCY_EXCHANGE'
    DESK_WITH_ELECTRICAL_OUTLET = 'DESK_WITH_ELECTRICAL_OUTLET'
    DOCTOR_ON_CALL = 'DOCTOR_ON_CALL'
    DOOR_MAN = 'DOOR_MAN'
    DRIVING_RANGE = 'DRIVING_RANGE'
    DRUGSTORE_PHARMACY = 'DRUGSTORE_PHARMACY'
    DUTY_FREE_SHOP = 'DUTY_FREE_SHOP'
    ELEVATORS = 'ELEVATORS'
    EXECUTIVE_FLOOR = 'EXECUTIVE_FLOOR'
    EXERCISE_GYM = 'EXERCISE_GYM'
    EXPRESS_CHECK_IN = 'EXPRESS_CHECK_IN'
    EXPRESS_CHECK_OUT = 'EXPRESS_CHECK_OUT'
    FAMILY_PLAN = 'FAMILY_PLAN'
    FLORIST = 'FLORIST'
    FOLIOS = 'FOLIOS'
    FREE_AIRPORT_SHUTTLE = 'FREE_AIRPORT_SHUTTLE'
    FREE_PARKING = 'FREE_PARKING'
    FREE_TRANSPORTATION = 'FREE_TRANSPORTATION'
    GAME_ROOM = 'GAME_ROOM'
    GIFT_NEWS_STAND = 'GIFT_NEWS_STAND'
    HAIRDRESSER_BARBER = 'HAIRDRESSER_BARBER'
    ACCESSIBLE_FACILITIES = 'ACCESSIBLE_FACILITIES'
    HEALTH_CLUB = 'HEALTH_CLUB'
    HEATED_POOL = 'HEATED_POOL'
    HOUSEKEEPING_DAILY = 'HOUSEKEEPING_DAILY'
    HOUSEKEEPING_WEEKLY = 'HOUSEKEEPING_WEEKLY'
    ICE_MACHINE = 'ICE_MACHINE'
    INDOOR_PARKING = 'INDOOR_PARKING'
    INDOOR_POOL = 'INDOOR_POOL'
    JACUZZI = 'JACUZZI'
    JOGGING_TRACK = 'JOGGING_TRACK'
    KENNELS = 'KENNELS'
    LAUNDRY_VALET_SERVICE = 'LAUNDRY_VALET_SERVICE'
    LIQUOR_STORE = 'LIQUOR_STORE'
    LIVE_ENTERTAINMENT = 'LIVE_ENTERTAINMENT'
    MASSAGE_SERVICES = 'MASSAGE_SERVICES'
    NIGHTCLUB = 'NIGHTCLUB'
    OFF_SITE_PARKING = 'OFF_SITE_PARKING'
    ON_SITE_PARKING = 'ON_SITE_PARKING'
    OUTDOOR_PARKING = 'OUTDOOR_PARKING'
    OUTDOOR_POOL = 'OUTDOOR_POOL'
    PACKAGE_PARCEL_SERVICES = 'PACKAGE_PARCEL_SERVICES'
    PARKING = 'PARKING'
    PHOTOCOPY_CENTER = 'PHOTOCOPY_CENTER'
    PLAYGROUND = 'PLAYGROUND'
    POOL = 'POOL'
    POOLSIDE_SNACK_BAR = 'POOLSIDE_SNACK_BAR'
    PUBLIC_ADDRESS_SYSTEM = 'PUBLIC_ADDRESS_SYSTEM'
    RAMP_ACCESS = 'RAMP_ACCESS'
    RECREATIONAL_VEHICLE_PARKING = 'RECREATIONAL_VEHICLE_PARKING'
    RESTAURANT = 'RESTAURANT'
    ROOM_SERVICE = 'ROOM_SERVICE'
    SAFE_DEPOSIT_BOX = 'SAFE_DEPOSIT_BOX'
    SAUNA = 'SAUNA'
    SECURITY = 'SECURITY'
    SHOE_SHINE_STAND = 'SHOE_SHINE_STAND'
    SHOPPING_MALL = 'SHOPPING_MALL'
    SOLARIUM = 'SOLARIUM'
    SPA = 'SPA'
    SPORTS_BAR = 'SPORTS_BAR'
    STEAM_BATH = 'STEAM_BATH'
    STORAGE_SPACE = 'STORAGE_SPACE'
    SUNDRY_CONVENIENCE_STORE = 'SUNDRY_CONVENIENCE_STORE'
    TECHNICAL_CONCIERGE = 'TECHNICAL_CONCIERGE'
    THEATRE_DESK = 'THEATRE_DESK'
    TOUR_SIGHTSEEING_DESK = 'TOUR_SIGHTSEEING_DESK'
    TRANSLATION_SERVICES = 'TRANSLATION_SERVICES'
    TRAVEL_AGENCY = 'TRAVEL_AGENCY'
    TRUCK_PARKING = 'TRUCK_PARKING'
    VALET_CLEANING = 'VALET_CLEANING'
    DRY_CLEANING = 'DRY_CLEANING'
    VALET_PARKING = 'VALET_PARKING'
    VENDING_MACHINES = 'VENDING_MACHINES'
    VIDEO_TAPES = 'VIDEO_TAPES'
    WAKEUP_SERVICE = 'WAKEUP_SERVICE'
    WHEELCHAIR_ACCESS = 'WHEELCHAIR_ACCESS'
    WHIRLPOOL = 'WHIRLPOOL'
    MULTILINGUAL_STAFF = 'MULTILINGUAL_STAFF'
    WEDDING_SERVICES = 'WEDDING_SERVICES'
    BANQUET_FACILITIES = 'BANQUET_FACILITIES'
    BELL_STAFF_PORTER = 'BELL_STAFF_PORTER'
    BEAUTY_SHOP_SALON = 'BEAUTY_SHOP_SALON'
    COMPLIMENTARY_SELF_SERVICE_LAUNDRY = 'COMPLIMENTARY_SELF_SERVICE_LAUNDRY'
    DIRECT_DIAL_TELEPHONE = 'DIRECT_DIAL_TELEPHONE'
    FEMALE_TRAVELER_ROOM_FLOOR = 'FEMALE_TRAVELER_ROOM_FLOOR'
    PHARMACY = 'PHARMACY'
    STABLES = 'STABLES'
    ONE_TWENTY_AC = 'ONE_TWENTY_AC'
    ONE_TWENTY_DC = 'ONE_TWENTY_DC'
    TWO_TWENTY_AC = 'TWO_TWENTY_AC'
    ACCESSIBLE_PARKING = 'ACCESSIBLE_PARKING'
    TWO_TWENTY_DC = 'TWO_TWENTY_DC'
    BARBEQUE_GRILLS = 'BARBEQUE_GRILLS'
    WOMENS_CLOTHING = 'WOMENS_CLOTHING'
    MENS_CLOTHING = 'MENS_CLOTHING'
    CHILDRENS_CLOTHING = 'CHILDRENS_CLOTHING'
    SHOPS_AND_COMMERCIAL_SERVICES = 'SHOPS_AND_COMMERCIAL_SERVICES'
    VIDEO_GAMES = 'VIDEO_GAMES'
    SPORTS_BAR_OPEN_FOR_LUNCH = 'SPORTS_BAR_OPEN_FOR_LUNCH'
    SPORTS_BAR_OPEN_FOR_DINNER = 'SPORTS_BAR_OPEN_FOR_DINNER'
    ROOM_SERVICE_FULL_MENU = 'ROOM_SERVICE_FULL_MENU'
    ROOM_SERVICE_LIMITED_MENU = 'ROOM_SERVICE_LIMITED_MENU'
    ROOM_SERVICE_LIMITED_HOURS = 'ROOM_SERVICE_LIMITED_HOURS'
    VALET_SAME_DAY_DRY_CLEANING = 'VALET_SAME_DAY_DRY_CLEANING'
    BODY_SCRUB = 'BODY_SCRUB'
    BODY_WRAP = 'BODY_WRAP'
    PUBLIC_AREA_AIR_CONDITIONED = 'PUBLIC_AREA_AIR_CONDITIONED'
    EFOLIO_AVAILABLE_TO_COMPANY = 'EFOLIO_AVAILABLE_TO_COMPANY'
    INDIVIDUAL_EFOLIO_AVAILABLE = 'INDIVIDUAL_EFOLIO_AVAILABLE'
    VIDEO_REVIEW_BILLING = 'VIDEO_REVIEW_BILLING'
    BUTLER_SERVICE = 'BUTLER_SERVICE'
    COMPLIMENTARY_IN_ROOM_COFFEE_OR_TEA = 'COMPLIMENTARY_IN_ROOM_COFFEE_OR_TEA'
    COMPLIMENTARY_BUFFET_BREAKFAST = 'COMPLIMENTARY_BUFFET_BREAKFAST'
    COMPLIMENTARY_COCKTAILS = 'COMPLIMENTARY_COCKTAILS'
    COMPLIMENTARY_COFFEE_IN_LOBBY = 'COMPLIMENTARY_COFFEE_IN_LOBBY'
    COMPLIMENTARY_CONTINENTAL_BREAKFAST = 'COMPLIMENTARY_CONTINENTAL_BREAKFAST'
    COMPLIMENTARY_FULL_AMERICAN_BREAKFAST = 'COMPLIMENTARY_FULL_AMERICAN_BREAKFAST'
    DINNER_DELIVERY_SERVICE_FROM_LOCAL_RESTAURANT = (
        'DINNER_DELIVERY_SERVICE_FROM_LOCAL_RESTAURANT'
    )
    COMPLIMENTARY_NEWSPAPER_DELIVERED_TO_ROOM = (
        'COMPLIMENTARY_NEWSPAPER_DELIVERED_TO_ROOM'
    )
    COMPLIMENTARY_NEWSPAPER_IN_LOBBY = 'COMPLIMENTARY_NEWSPAPER_IN_LOBBY'
    COMPLIMENTARY_SHOESHINE = 'COMPLIMENTARY_SHOESHINE'
    EVENING_RECEPTION = 'EVENING_RECEPTION'
    FRONT_DESK = 'FRONT_DESK'
    GROCERY_SHOPPING_SERVICE_AVAILABLE = 'GROCERY_SHOPPING_SERVICE_AVAILABLE'
    HALAL_FOOD_AVAILABLE = 'HALAL_FOOD_AVAILABLE'
    KOSHER_FOOD_AVAILABLE = 'KOSHER_FOOD_AVAILABLE'
    LIMOUSINE_SERVICE = 'LIMOUSINE_SERVICE'
    MANAGERS_RECEPTION = 'MANAGERS_RECEPTION'
    MEDICAL_FACILITIES_SERVICE = 'MEDICAL_FACILITIES_SERVICE'
    TELEPHONE_JACK_ADAPTOR_AVAILABLE = 'TELEPHONE_JACK_ADAPTOR_AVAILABLE'
    ALL_INCLUSIVE_MEAL_PLAN = 'ALL_INCLUSIVE_MEAL_PLAN'
    BUFFET_BREAKFAST = 'BUFFET_BREAKFAST'
    COMMUNAL_BAR_AREA = 'COMMUNAL_BAR_AREA'
    CONTINENTAL_BREAKFAST = 'CONTINENTAL_BREAKFAST'
    FULL_MEAL_PLAN = 'FULL_MEAL_PLAN'
    FULL_AMERICAN_BREAKFAST = 'FULL_AMERICAN_BREAKFAST'
    MEAL_PLAN_AVAILABLE = 'MEAL_PLAN_AVAILABLE'
    MODIFIED_AMERICAN_MEAL_PLAN = 'MODIFIED_AMERICAN_MEAL_PLAN'
    FOOD_AND_BEVERAGE_OUTLETS = 'FOOD_AND_BEVERAGE_OUTLETS'
    LOUNGES_BARS = 'LOUNGES_BARS'
    BARBER_SHOP = 'BARBER_SHOP'
    VIDEO_CHECKOUT = 'VIDEO_CHECKOUT'
    ONSITE_LAUNDRY = 'ONSITE_LAUNDRY'
    TWENTY_FOUR_HOUR_FOOD_AND_BEVERAGE_KIOSK = (
        'TWENTY_FOUR_HOUR_FOOD_AND_BEVERAGE_KIOSK'
    )
    CONCIERGE_LOUNGE = 'CONCIERGE_LOUNGE'
    PARKING_FEE_MANAGED_BY_HOTEL = 'PARKING_FEE_MANAGED_BY_HOTEL'
    TRANSPORTATION = 'TRANSPORTATION'
    BREAKFAST_SERVED_IN_RESTAURANT = 'BREAKFAST_SERVED_IN_RESTAURANT'
    LUNCH_SERVED_IN_RESTAURANT = 'LUNCH_SERVED_IN_RESTAURANT'
    DINNER_SERVED_IN_RESTAURANT = 'DINNER_SERVED_IN_RESTAURANT'
    FULL_SERVICE_HOUSEKEEPING = 'FULL_SERVICE_HOUSEKEEPING'
    LIMITED_SERVICE_HOUSEKEEPING = 'LIMITED_SERVICE_HOUSEKEEPING'
    HIGH_SPEED_INTERNET_ACCESS_FOR_LAPTOP_IN_PUBLIC_AREAS = (
        'HIGH_SPEED_INTERNET_ACCESS_FOR_LAPTOP_IN_PUBLIC_AREAS'
    )
    WIRELESS_INTERNET_CONNECTION_IN_PUBLIC_AREAS = (
        'WIRELESS_INTERNET_CONNECTION_IN_PUBLIC_AREAS'
    )
    ADDITIONAL_SERVICES_AMENITIES_FACILITIES_ON_PROPERTY = (
        'ADDITIONAL_SERVICES_AMENITIES_FACILITIES_ON_PROPERTY'
    )
    TRANSPORTATION_SERVICES_LOCAL_AREA = 'TRANSPORTATION_SERVICES_LOCAL_AREA'
    TRANSPORTATION_SERVICES_LOCAL_OFFICE = 'TRANSPORTATION_SERVICES_LOCAL_OFFICE'
    DVD_VIDEO_RENTAL = 'DVD_VIDEO_RENTAL'
    PARKING_LOT = 'PARKING_LOT'
    PARKING_DECK = 'PARKING_DECK'
    STREET_SIDE_PARKING = 'STREET_SIDE_PARKING'
    COCKTAIL_LOUNGE_WITH_ENTERTAINMENT = 'COCKTAIL_LOUNGE_WITH_ENTERTAINMENT'
    COCKTAIL_LOUNGE_WITH_LIGHT_FARE = 'COCKTAIL_LOUNGE_WITH_LIGHT_FARE'
    MOTORCYCLE_PARKING = 'MOTORCYCLE_PARKING'
    PHONE_SERVICES = 'PHONE_SERVICES'
    BALLROOM = 'BALLROOM'
    BUS_PARKING = 'BUS_PARKING'
    CHILDRENS_PLAY_AREA = 'CHILDRENS_PLAY_AREA'
    CHILDRENS_NURSERY = 'CHILDRENS_NURSERY'
    DISCO = 'DISCO'
    EARLY_CHECK_IN = 'EARLY_CHECK_IN'
    LOCKER_ROOM = 'LOCKER_ROOM'
    NON_SMOKING_ROOMS_GENERIC = 'NON_SMOKING_ROOMS_GENERIC'
    TRAIN_ACCESS = 'TRAIN_ACCESS'
    AEROBICS_INSTRUCTION = 'AEROBICS_INSTRUCTION'
    BAGGAGE_HOLD = 'BAGGAGE_HOLD'
    BICYCLE_RENTALS = 'BICYCLE_RENTALS'
    DIETICIAN = 'DIETICIAN'
    LATE_CHECK_OUT_AVAILABLE = 'LATE_CHECK_OUT_AVAILABLE'
    PET_SITTING_SERVICES = 'PET_SITTING_SERVICES'
    PRAYER_MATS = 'PRAYER_MATS'
    SPORTS_TRAINER = 'SPORTS_TRAINER'
    TURNDOWN_SERVICE = 'TURNDOWN_SERVICE'
    DVDS_VIDEOS_CHILDREN = 'DVDS_VIDEOS_CHILDREN'
    BANK = 'BANK'
    LOBBY_COFFEE_SERVICE = 'LOBBY_COFFEE_SERVICE'
    BANKING_SERVICES = 'BANKING_SERVICES'
    STAIRWELLS = 'STAIRWELLS'
    PET_AMENITIES_AVAILABLE = 'PET_AMENITIES_AVAILABLE'
    EXHIBITION_CONVENTION_FLOOR = 'EXHIBITION_CONVENTION_FLOOR'
    LONG_TERM_PARKING = 'LONG_TERM_PARKING'
    CHILDREN_NOT_ALLOWED = 'CHILDREN_NOT_ALLOWED'
    CHILDREN_WELCOME = 'CHILDREN_WELCOME'
    COURTESY_CAR = 'COURTESY_CAR'
    HOTEL_DOES_NOT_PROVIDE_PORNOGRAPHIC_FILMS_TV = (
        'HOTEL_DOES_NOT_PROVIDE_PORNOGRAPHIC_FILMS_TV'
    )
    HOTSPOTS = 'HOTSPOTS'
    FREE_HIGH_SPEED_INTERNET_CONNECTION = 'FREE_HIGH_SPEED_INTERNET_CONNECTION'
    INTERNET_SERVICES = 'INTERNET_SERVICES'
    PETS_ALLOWED = 'PETS_ALLOWED'
    GOURMET_HIGHLIGHTS = 'GOURMET_HIGHLIGHTS'
    CATERING_SERVICES = 'CATERING_SERVICES'
    COMPLIMENTARY_BREAKFAST = 'COMPLIMENTARY_BREAKFAST'
    BUSINESS_CENTER = 'BUSINESS_CENTER'
    BUSINESS_SERVICES = 'BUSINESS_SERVICES'
    SECURED_PARKING = 'SECURED_PARKING'
    RACQUETBALL = 'RACQUETBALL'
    SNOW_SPORTS = 'SNOW_SPORTS'
    TENNIS_COURT = 'TENNIS_COURT'
    WATER_SPORTS = 'WATER_SPORTS'
    CHILD_PROGRAMS = 'CHILD_PROGRAMS'
    GOLF = 'GOLF'
    HORSEBACK_RIDING = 'HORSEBACK_RIDING'
    OCEANFRONT = 'OCEANFRONT'
    BEACHFRONT = 'BEACHFRONT'
    HAIR_DRYER = 'HAIR_DRYER'
    IRONING_BOARD = 'IRONING_BOARD'
    HEATED_GUEST_ROOMS = 'HEATED_GUEST_ROOMS'
    TOILET = 'TOILET'
    PARLOR = 'PARLOR'
    VIDEO_GAME_PLAYER = 'VIDEO_GAME_PLAYER'
    THALASSOTHERAPY = 'THALASSOTHERAPY'
    PRIVATE_DINING_FOR_GROUPS = 'PRIVATE_DINING_FOR_GROUPS'
    HEARING_IMPAIRED_SERVICES = 'HEARING_IMPAIRED_SERVICES'
    CARRYOUT_BREAKFAST = 'CARRYOUT_BREAKFAST'
    DELUXE_CONTINENTAL_BREAKFAST = 'DELUXE_CONTINENTAL_BREAKFAST'
    HOT_CONTINENTAL_BREAKFAST = 'HOT_CONTINENTAL_BREAKFAST'
    HOT_BREAKFAST = 'HOT_BREAKFAST'
    PRIVATE_POOL = 'PRIVATE_POOL'
    CONNECTING_ROOMS = 'CONNECTING_ROOMS'
    DATA_PORT = 'DATA_PORT'
    EXTERIOR_CORRIDORS = 'EXTERIOR_CORRIDORS'
    GULF_VIEW = 'GULF_VIEW'
    ACCESSIBLE_ROOMS = 'ACCESSIBLE_ROOMS'
    HIGH_SPEED_INTERNET_ACCESS = 'HIGH_SPEED_INTERNET_ACCESS'
    INTERIOR_CORRIDORS = 'INTERIOR_CORRIDORS'
    HIGH_SPEED_WIRELESS = 'HIGH_SPEED_WIRELESS'
    KITCHENETTE = 'KITCHENETTE'
    PRIVATE_BATH_OR_SHOWER = 'PRIVATE_BATH_OR_SHOWER'
    FIRE_SAFETY_COMPLIANT = 'FIRE_SAFETY_COMPLIANT'
    WELCOME_DRINK = 'WELCOME_DRINK'
    BOARDING_PASS_PRINT_OUT_AVAILABLE = 'BOARDING_PASS_PRINT_OUT_AVAILABLE'
    PRINTING_SERVICES_AVAILABLE = 'PRINTING_SERVICES_AVAILABLE'
    ALL_PUBLIC_AREAS_NON_SMOKING = 'ALL_PUBLIC_AREAS_NON_SMOKING'
    MEETING_ROOMS = 'MEETING_ROOMS'
    MOVIES_IN_ROOM = 'MOVIES_IN_ROOM'
    SECRETARIAL_SERVICE = 'SECRETARIAL_SERVICE'
    SNOW_SKIING = 'SNOW_SKIING'
    WATER_SKIING = 'WATER_SKIING'
    FAX_SERVICE = 'FAX_SERVICE'
    GREAT_ROOM = 'GREAT_ROOM'
    LOBBY = 'LOBBY'
    MULTIPLE_PHONE_LINES_BILLED_SEPARATELY = 'MULTIPLE_PHONE_LINES_BILLED_SEPARATELY'
    UMBRELLAS = 'UMBRELLAS'
    GAS_STATION = 'GAS_STATION'
    GROCERY_STORE = 'GROCERY_STORE'
    TWENTY_FOUR_HOUR_COFFEE_SHOP = 'TWENTY_FOUR_HOUR_COFFEE_SHOP'
    AIRPORT_SHUTTLE_SERVICE = 'AIRPORT_SHUTTLE_SERVICE'
    LUGGAGE_SERVICE = 'LUGGAGE_SERVICE'
    PIANO_BAR = 'PIANO_BAR'
    VIP_SECURITY = 'VIP_SECURITY'
    COMPLIMENTARY_WIRELESS_INTERNET = 'COMPLIMENTARY_WIRELESS_INTERNET'
    CONCIERGE_BREAKFAST = 'CONCIERGE_BREAKFAST'
    SAME_GENDER_FLOOR = 'SAME_GENDER_FLOOR'
    CHILDREN_PROGRAMS = 'CHILDREN_PROGRAMS'
    BUILDING_MEETS_LOCAL_STATE_AND_COUNTRY_BUILDING_CODES = (
        'BUILDING_MEETS_LOCAL_STATE_AND_COUNTRY_BUILDING_CODES'
    )
    INTERNET_BROWSER_ON_TV = 'INTERNET_BROWSER_ON_TV'
    NEWSPAPER = 'NEWSPAPER'
    PARKING_CONTROLLED_ACCESS_GATES_TO_ENTER_PARKING_AREA = (
        'PARKING_CONTROLLED_ACCESS_GATES_TO_ENTER_PARKING_AREA'
    )
    HOTEL_SAFE_DEPOSIT_BOX_NOT_ROOM_SAFE_BOX = (
        'HOTEL_SAFE_DEPOSIT_BOX_NOT_ROOM_SAFE_BOX'
    )
    STORAGE_SPACE_AVAILABLE_FEE = 'STORAGE_SPACE_AVAILABLE_FEE'
    TYPE_OF_ENTRANCES_TO_GUEST_ROOMS = 'TYPE_OF_ENTRANCES_TO_GUEST_ROOMS'
    BEVERAGE_COCKTAIL = 'BEVERAGE_COCKTAIL'
    CELL_PHONE_RENTAL = 'CELL_PHONE_RENTAL'
    COFFEE_TEA = 'COFFEE_TEA'
    EARLY_CHECK_IN_GUARANTEE = 'EARLY_CHECK_IN_GUARANTEE'
    FOOD_AND_BEVERAGE_DISCOUNT = 'FOOD_AND_BEVERAGE_DISCOUNT'
    LATE_CHECK_OUT_GUARANTEE = 'LATE_CHECK_OUT_GUARANTEE'
    ROOM_UPGRADE_CONFIRMED = 'ROOM_UPGRADE_CONFIRMED'
    ROOM_UPGRADE_ON_AVAILABILITY = 'ROOM_UPGRADE_ON_AVAILABILITY'
    SHUTTLE_TO_LOCAL_BUSINESSES = 'SHUTTLE_TO_LOCAL_BUSINESSES'
    SHUTTLE_TO_LOCAL_ATTRACTIONS = 'SHUTTLE_TO_LOCAL_ATTRACTIONS'
    SOCIAL_HOUR = 'SOCIAL_HOUR'
    VIDEO_BILLING = 'VIDEO_BILLING'
    WELCOME_GIFT = 'WELCOME_GIFT'
    HYPOALLERGENIC_ROOMS = 'HYPOALLERGENIC_ROOMS'
    ROOM_AIR_FILTRATION = 'ROOM_AIR_FILTRATION'
    SMOKE_FREE_PROPERTY = 'SMOKE_FREE_PROPERTY'
    WATER_PURIFICATION_SYSTEM_IN_USE = 'WATER_PURIFICATION_SYSTEM_IN_USE'
    POOLSIDE_SERVICE = 'POOLSIDE_SERVICE'
    CLOTHING_STORE = 'CLOTHING_STORE'
    ELECTRIC_CAR_CHARGING_STATIONS = 'ELECTRIC_CAR_CHARGING_STATIONS'
    OFFICE_RENTAL = 'OFFICE_RENTAL'
    PIANO = 'PIANO'
    INCOMING_FAX = 'INCOMING_FAX'
    OUTGOING_FAX = 'OUTGOING_FAX'
    SEMI_PRIVATE_SPACE = 'SEMI_PRIVATE_SPACE'
    LOADING_DOCK = 'LOADING_DOCK'
    BABY_KIT = 'BABY_KIT'
    CHILDRENS_BREAKFAST = 'CHILDRENS_BREAKFAST'
    CLOAKROOM_SERVICE = 'CLOAKROOM_SERVICE'
    COFFEE_LOUNGE = 'COFFEE_LOUNGE'
    EVENTS_TICKET_SERVICE = 'EVENTS_TICKET_SERVICE'
    LATE_CHECK_IN = 'LATE_CHECK_IN'
    LIMITED_PARKING = 'LIMITED_PARKING'
    OUTDOOR_SUMMER_BAR_CAFE = 'OUTDOOR_SUMMER_BAR_CAFE'
    NO_PARKING_AVAILABLE = 'NO_PARKING_AVAILABLE'
    BEER_GARDEN = 'BEER_GARDEN'
    GARDEN_LOUNGE_BAR = 'GARDEN_LOUNGE_BAR'
    SUMMER_TERRACE = 'SUMMER_TERRACE'
    WINTER_TERRACE = 'WINTER_TERRACE'
    ROOF_TERRACE = 'ROOF_TERRACE'
    BEACH_BAR = 'BEACH_BAR'
    HELICOPTER_SERVICE = 'HELICOPTER_SERVICE'
    FERRY = 'FERRY'
    TAPAS_BAR = 'TAPAS_BAR'
    CAFE_BAR = 'CAFE_BAR'
    SNACK_BAR = 'SNACK_BAR'
    GUESTROOM_WIRED_INTERNET = 'GUESTROOM_WIRED_INTERNET'
    GUESTROOM_WIRELESS_INTERNET = 'GUESTROOM_WIRELESS_INTERNET'
    FITNESS_CENTER = 'FITNESS_CENTER'
    ALCOHOLIC_BEVERAGES = 'ALCOHOLIC_BEVERAGES'
    NON_ALCOHOLIC_BEVERAGES = 'NON_ALCOHOLIC_BEVERAGES'
    HEALTH_AND_BEAUTY_SERVICES = 'HEALTH_AND_BEAUTY_SERVICES'
    LOCAL_CALLS = 'LOCAL_CALLS'
    MINIBAR = 'MINIBAR'
    REFRIGERATOR = 'REFRIGERATOR'
    IN_ROOM_SAFE = 'IN_ROOM_SAFE'
    SMOKING_ROOMS_AVAILBLE = 'SMOKING_ROOMS_AVAILBLE'
    MOUNTAIN_VIEW = 'MOUNTAIN_VIEW'
    POOL_VIEW = 'POOL_VIEW'
    BEACH_VIEW = 'BEACH_VIEW'
    OCEAN_VIEW = 'OCEAN_VIEW'
    ROOMS_WITH_BALCONY = 'ROOMS_WITH_BALCONY'
    FAMILY_ROOM = 'FAMILY_ROOM'
    CRIB_CHARGE = 'CRIB_CHARGE'
    ROLLAWAY_ADULT = 'ROLLAWAY_ADULT'
    FREE_WIFI_IN_MEETING_ROOMS = 'FREE_WIFI_IN_MEETING_ROOMS'
    ECO_FRIENDLY = 'ECO_FRIENDLY'
    EXTRA_PERSON = 'EXTRA_PERSON'
    STAY_SAFE = 'STAY_SAFE'
    ENHANCED_HYGIENE_CLEANLINESS_PROTOCOLS = 'ENHANCED_HYGIENE_CLEANLINESS_PROTOCOLS'


class HotelAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelAmenityType: HotelAmenityType = Field(..., examples=['ECO_FRIENDLY'])
    additionalInfo: str | None = Field(
        None,
        description='Amenity description',
        examples=['Complimentary in-room coffee or tea'],
    )
    isComplimentary: bool | None = Field(
        None, description='Is Amenity complimentary', examples=[True]
    )


class HotelBrand(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    brandCode: str | None = Field(
        None, description='The code of hotel brand.', examples=['HY']
    )
    brandName: str | None = Field(
        None, description='The name of hotel brand.', examples=['Global Hytt Corp.']
    )


class HotelCancelPnrResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    confirmationId: str = Field(
        ...,
        description='The confirmation ID of the cancelled hotel pnr.',
        examples=['231241232'],
    )


class Refundable(Enum):
    UNKNOWN = 'UNKNOWN'
    TRUE = 'TRUE'
    FALSE = 'FALSE'


class HotelChain(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    chainCode: str | None = Field(
        None, description='The code of hotel chain.', examples=['EM']
    )
    chainName: str | None = Field(
        None, description='The name of hotel chain.', examples=['Mariott']
    )


class HotelCo2EmissionDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    co2EmissionValue: float = Field(
        ...,
        description='CO2 emission value in kg per room for the entire stay.',
        examples=[10.5],
    )


class HotelCodeSearch(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchType: Literal['HOTEL_CODE'] = Field('HOTEL_CODE', examples=['HOTEL_CODE'])
    hotelCode: str = Field(
        ...,
        description='Unique code for a hotel. The search result will also contain other hotels in close proximity to the requested hotel code.\n',
        examples=['SPOTNANA:12345'],
    )


class PnrStatus(Enum):
    SUCCESS = 'SUCCESS'
    APPROVAL_PENDING = 'APPROVAL_PENDING'
    CONFIRMATION_PENDING = 'CONFIRMATION_PENDING'


class HotelCreatePnrResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str = Field(
        ...,
        description='A unique PNR ID created by Spotnana for the new hotel booking.',
        examples=['7373737373'],
    )
    pnrStatus: PnrStatus = Field(
        ..., description='The status of the PNR.', examples=['SUCCESS']
    )
    externalPnrId: str | None = Field(
        None,
        description='The external PNR ID for the hotel booking, created by the third party suppliers.',
        examples=['ABC123'],
    )


class Type1(Enum):
    GENERAL = 'GENERAL'
    ALERTS = 'ALERTS'
    DINING = 'DINING'
    FACILITIES = 'FACILITIES'
    RECREATION = 'RECREATION'
    SERVICES = 'SERVICES'
    ATTRACTIONS = 'ATTRACTIONS'
    CANCELLATION_POLICY = 'CANCELLATION_POLICY'
    DEPOSIT_POLICY = 'DEPOSIT_POLICY'
    DIRECTIONS = 'DIRECTIONS'
    POLICIES = 'POLICIES'
    SAFETY = 'SAFETY'
    TRANSPORTATION = 'TRANSPORTATION'


class HotelDescription(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type1 | None = Field(
        None, description='Hotel description type', examples=['']
    )
    value: str | None = Field(
        None, description='Hotel description value', examples=['']
    )


class HotelImageCategory(Enum):
    UNKNOWN_CATEGORY = 'UNKNOWN_CATEGORY'
    EXTERIOR_VIEW = 'EXTERIOR_VIEW'
    LOBBY_VIEW = 'LOBBY_VIEW'
    POOL_VIEW = 'POOL_VIEW'
    RESTAURANT = 'RESTAURANT'
    HEALTH_CLUB = 'HEALTH_CLUB'
    GUEST_ROOM = 'GUEST_ROOM'
    SUITE = 'SUITE'
    MEETING_ROOM = 'MEETING_ROOM'
    BALLROOM = 'BALLROOM'
    GOLF_COURSE = 'GOLF_COURSE'
    BEACH = 'BEACH'
    SPA = 'SPA'
    BAR_OR_LOUNGE = 'BAR_OR_LOUNGE'
    RECREATIONAL_FACILITY = 'RECREATIONAL_FACILITY'
    LOGO = 'LOGO'
    BASICS = 'BASICS'
    MAP = 'MAP'
    PROMOTIONAL = 'PROMOTIONAL'
    HOT_NEWS = 'HOT_NEWS'
    MISCELLANEOUS = 'MISCELLANEOUS'
    GUEST_ROOM_AMENITY = 'GUEST_ROOM_AMENITY'
    PROPERTY_AMENITY = 'PROPERTY_AMENITY'
    BUSINESS_CENTER = 'BUSINESS_CENTER'


class PnrStatus1(Enum):
    INITIATED = 'INITIATED'
    CANCELLED = 'CANCELLED'
    CONFIRMED = 'CONFIRMED'
    PENDING = 'PENDING'
    CONTACT_SUPPORT = 'CONTACT_SUPPORT'
    REQUEST_PENDING = 'REQUEST_PENDING'
    REQUESTED = 'REQUESTED'


class HotelModifyBookingResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str = Field(
        ...,
        description='The unique PNR ID for the modified hotel booking',
        examples=['7373737373'],
    )
    pnrStatus: PnrStatus1 = Field(
        ..., description='The status of the PNR', examples=['CONFIRMED']
    )
    externalPnrId: str | None = Field(
        None,
        description='The external PNR ID for the modified hotel booking, provided by the third party suppliers',
        examples=['ABC123'],
    )


class HotelPrefAmenity(Enum):
    PARKING = 'PARKING'
    FREE_PARKING = 'FREE_PARKING'
    FREE_BREAKFAST = 'FREE_BREAKFAST'
    POOL = 'POOL'
    WIFI = 'WIFI'
    FITNESS_CENTER = 'FITNESS_CENTER'
    FAMILY_FRIENDLY = 'FAMILY_FRIENDLY'
    RECEPTION = 'RECEPTION'
    SPA = 'SPA'
    RESTAURANT = 'RESTAURANT'
    BAR = 'BAR'
    TRANSPORTATION = 'TRANSPORTATION'
    PET_FRIENDLY = 'PET_FRIENDLY'
    BUSINESS_CENTER = 'BUSINESS_CENTER'
    AIR_CONDITIONING = 'AIR_CONDITIONING'
    BEACH_ACCESS = 'BEACH_ACCESS'
    LAUNDRY_SERVICES = 'LAUNDRY_SERVICES'
    ROOM_SERVICE = 'ROOM_SERVICE'
    ACCESSIBLE = 'ACCESSIBLE'


class HotelPriceCheckRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    priceValidateKey: str = Field(
        ..., description='The key used to validate the price.'
    )
    tripId: str | None = Field(None, description='The trip ID for the booking.')


class HotelPropertyEnum(Enum):
    ALL_SUITE = 'ALL_SUITE'
    ALL_INCLUSIVE_RESORT = 'ALL_INCLUSIVE_RESORT'
    APARTMENT = 'APARTMENT'
    BED_AND_BREAKFAST = 'BED_AND_BREAKFAST'
    CABIN_OR_BUNGALOW = 'CABIN_OR_BUNGALOW'
    CAMPGROUND = 'CAMPGROUND'
    CHALET = 'CHALET'
    CONDOMINIUM = 'CONDOMINIUM'
    CONFERENCE_CENTER = 'CONFERENCE_CENTER'
    CORPORATE_BUSINESS_TRANSIENT = 'CORPORATE_BUSINESS_TRANSIENT'
    CRUISE = 'CRUISE'
    EXTENDED_STAY = 'EXTENDED_STAY'
    GUEST_FARM = 'GUEST_FARM'
    GUEST_HOUSE_LIMITED_SERVICE = 'GUEST_HOUSE_LIMITED_SERVICE'
    HEALTH_SPA = 'HEALTH_SPA'
    HOLIDAY_RESORT = 'HOLIDAY_RESORT'
    HOSTEL = 'HOSTEL'
    HOTEL = 'HOTEL'
    INN = 'INN'
    LODGE = 'LODGE'
    MONASTERY = 'MONASTERY'
    MOTEL = 'MOTEL'
    RANCH = 'RANCH'
    RESIDENTIAL_APARTMENT = 'RESIDENTIAL_APARTMENT'
    RESORT = 'RESORT'
    TENT = 'TENT'
    VACATION_HOME = 'VACATION_HOME'
    VILLA = 'VILLA'
    WILDLIFE_RESERVE = 'WILDLIFE_RESERVE'
    CASTLE = 'CASTLE'
    GOLF = 'GOLF'
    PENSION = 'PENSION'
    SKI = 'SKI'
    SPA = 'SPA'
    BOATEL = 'BOATEL'
    HISTORICAL = 'HISTORICAL'
    RECREATIONAL_VEHICLE_PARK = 'RECREATIONAL_VEHICLE_PARK'
    CHARM_HOTEL = 'CHARM_HOTEL'
    OTHER = 'OTHER'


class HotelPropertyType(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: HotelPropertyEnum = Field(..., description='Type of the property.')
    description: str | None = Field(
        None, description='Description of the property type.'
    )


class RateSource(Enum):
    SABRE = 'SABRE'
    BOOKING_COM = 'BOOKING_COM'
    EXPEDIA = 'EXPEDIA'
    OFFLINE = 'OFFLINE'
    HRS = 'HRS'
    BCD = 'BCD'
    QANTAS_HOTELS = 'QANTAS_HOTELS'
    HOTEL_HUB = 'HOTEL_HUB'
    MARRIOTT = 'MARRIOTT'
    AMADEUS = 'AMADEUS'


class PolicyType1(Enum):
    DEFAULT = 'DEFAULT'
    SET_BY_ADMIN = 'SET_BY_ADMIN'


class HotelRateType(Enum):
    PUBLISHED = 'PUBLISHED'
    CORPORATE = 'CORPORATE'
    SPOTNANA = 'SPOTNANA'
    REGULAR = 'REGULAR'
    AAA = 'AAA'
    AARP = 'AARP'
    SENIOR_CITIZEN = 'SENIOR_CITIZEN'
    GOVERNMENT = 'GOVERNMENT'
    MILITARY = 'MILITARY'
    MEMBERSHIP = 'MEMBERSHIP'
    BEST_AVAILABLE_RATE = 'BEST_AVAILABLE_RATE'
    TMC = 'TMC'


class Type2(Enum):
    UNKNOWN = 'UNKNOWN'
    ADJOINING_ROOMS = 'ADJOINING_ROOMS'
    AIR_CONDITIONING = 'AIR_CONDITIONING'
    ALARM_CLOCK = 'ALARM_CLOCK'
    ALL_NEWS_CHANNEL = 'ALL_NEWS_CHANNEL'
    AM_FM_RADIO = 'AM_FM_RADIO'
    BABY_LISTENING_DEVICE = 'BABY_LISTENING_DEVICE'
    BALCONY_LANAI_TERRACE = 'BALCONY_LANAI_TERRACE'
    BARBEQUE_GRILLS = 'BARBEQUE_GRILLS'
    BATH_TUB_WITH_SPRAY_JETS = 'BATH_TUB_WITH_SPRAY_JETS'
    BATHROBE = 'BATHROBE'
    BATHROOM_AMENITIES = 'BATHROOM_AMENITIES'
    BATHROOM_TELEPHONE = 'BATHROOM_TELEPHONE'
    BATHTUB = 'BATHTUB'
    BATHTUB_ONLY = 'BATHTUB_ONLY'
    BATHTUB_SHOWER_COMBINATION = 'BATHTUB_SHOWER_COMBINATION'
    BIDET = 'BIDET'
    BOTTLED_WATER = 'BOTTLED_WATER'
    CABLE_TELEVISION = 'CABLE_TELEVISION'
    COFFEE_TEA_MAKER = 'COFFEE_TEA_MAKER'
    COLOR_TELEVISION = 'COLOR_TELEVISION'
    COMPUTER = 'COMPUTER'
    CONNECTING_ROOMS = 'CONNECTING_ROOMS'
    CONVERTERS_VOLTAGE_ADAPTORS = 'CONVERTERS_VOLTAGE_ADAPTORS'
    COPIER = 'COPIER'
    CORDLESS_PHONE = 'CORDLESS_PHONE'
    CRIBS = 'CRIBS'
    DATA_PORT = 'DATA_PORT'
    DESK = 'DESK'
    DESK_WITH_LAMP = 'DESK_WITH_LAMP'
    DINING_GUIDE = 'DINING_GUIDE'
    DIRECT_DIAL_PHONE_NUMBER = 'DIRECT_DIAL_PHONE_NUMBER'
    DISHWASHER = 'DISHWASHER'
    DOUBLE_BEDS = 'DOUBLE_BEDS'
    DUAL_VOLTAGE_OUTLET = 'DUAL_VOLTAGE_OUTLET'
    ELECTRICAL_CURRENT_VOLTAGE = 'ELECTRICAL_CURRENT_VOLTAGE'
    ERGONOMIC_CHAIR = 'ERGONOMIC_CHAIR'
    EXTENDED_PHONE_CORD = 'EXTENDED_PHONE_CORD'
    FAX_MACHINE = 'FAX_MACHINE'
    FIRE_ALARM = 'FIRE_ALARM'
    FIRE_ALARM_WITH_LIGHT = 'FIRE_ALARM_WITH_LIGHT'
    FIREPLACE = 'FIREPLACE'
    FREE_TOLL_FREE_CALLS = 'FREE_TOLL_FREE_CALLS'
    FREE_CALLS = 'FREE_CALLS'
    FREE_CREDIT_CARD_ACCESS_CALLS = 'FREE_CREDIT_CARD_ACCESS_CALLS'
    FREE_LOCAL_CALLS = 'FREE_LOCAL_CALLS'
    FREE_MOVIES_VIDEO = 'FREE_MOVIES_VIDEO'
    FULL_KITCHEN = 'FULL_KITCHEN'
    GRAB_BARS_IN_BATHROOM = 'GRAB_BARS_IN_BATHROOM'
    GRECIAN_TUB = 'GRECIAN_TUB'
    HAIRDRYER = 'HAIRDRYER'
    HIGH_SPEED_INTERNET_CONNECTION = 'HIGH_SPEED_INTERNET_CONNECTION'
    INTERACTIVE_WEB_TV = 'INTERACTIVE_WEB_TV'
    INTERNATIONAL_DIRECT_DIALING = 'INTERNATIONAL_DIRECT_DIALING'
    INTERNET_ACCESS = 'INTERNET_ACCESS'
    IRON = 'IRON'
    IRONING_BOARD = 'IRONING_BOARD'
    WHIRPOOL = 'WHIRPOOL'
    KING_BED = 'KING_BED'
    KITCHEN = 'KITCHEN'
    KITCHEN_SUPPLIES = 'KITCHEN_SUPPLIES'
    KITCHENETTE = 'KITCHENETTE'
    KNOCK_LIGHT = 'KNOCK_LIGHT'
    LAPTOP = 'LAPTOP'
    LARGE_DESK = 'LARGE_DESK'
    LARGE_WORK_AREA = 'LARGE_WORK_AREA'
    LAUNDRY_BASKET_CLOTHES_HAMPER = 'LAUNDRY_BASKET_CLOTHES_HAMPER'
    LOFT = 'LOFT'
    MICROWAVE = 'MICROWAVE'
    MINIBAR = 'MINIBAR'
    MODEM = 'MODEM'
    MODEM_JACK = 'MODEM_JACK'
    MULTILINE_PHONE = 'MULTILINE_PHONE'
    NEWSPAPER = 'NEWSPAPER'
    NONSMOKING = 'NONSMOKING'
    NOTEPADS = 'NOTEPADS'
    OFFICE_SUPPLIES = 'OFFICE_SUPPLIES'
    OVEN = 'OVEN'
    PAY_PER_VIEW_MOVIES_ON_TV = 'PAY_PER_VIEW_MOVIES_ON_TV'
    PENS = 'PENS'
    PHONE_IN_BATHROOM = 'PHONE_IN_BATHROOM'
    PLATES_AND_BOWLS = 'PLATES_AND_BOWLS'
    POTS_AND_PANS = 'POTS_AND_PANS'
    PRAYER_MATS = 'PRAYER_MATS'
    PRINTER = 'PRINTER'
    PRIVATE_BATHROOM = 'PRIVATE_BATHROOM'
    QUEEN_BED = 'QUEEN_BED'
    RECLINER = 'RECLINER'
    REFRIGERATOR = 'REFRIGERATOR'
    REFRIGERATOR_WITH_ICE_MAKER = 'REFRIGERATOR_WITH_ICE_MAKER'
    REMOTE_CONTROL_TELEVISION = 'REMOTE_CONTROL_TELEVISION'
    ROLLAWAY_BED = 'ROLLAWAY_BED'
    SAFE = 'SAFE'
    SCANNER = 'SCANNER'
    SEPARATE_CLOSET = 'SEPARATE_CLOSET'
    SEPARATE_MODEM_LINE_AVAILABLE = 'SEPARATE_MODEM_LINE_AVAILABLE'
    SHOE_POLISHER = 'SHOE_POLISHER'
    SHOWER_ONLY = 'SHOWER_ONLY'
    SILVERWARE_UTENSILS = 'SILVERWARE_UTENSILS'
    SITTING_AREA = 'SITTING_AREA'
    SMOKE_DETECTORS = 'SMOKE_DETECTORS'
    SMOKING = 'SMOKING'
    SOFA_BED = 'SOFA_BED'
    SPEAKER_PHONE = 'SPEAKER_PHONE'
    STEREO = 'STEREO'
    STOVE = 'STOVE'
    TAPE_RECORDER = 'TAPE_RECORDER'
    TELEPHONE = 'TELEPHONE'
    TELEPHONE_FOR_HEARING_IMPAIRED = 'TELEPHONE_FOR_HEARING_IMPAIRED'
    TELEPHONES_WITH_MESSAGE_LIGHT = 'TELEPHONES_WITH_MESSAGE_LIGHT'
    TOASTER_OVEN = 'TOASTER_OVEN'
    TROUSER_PANT_PRESS = 'TROUSER_PANT_PRESS'
    TURN_DOWN_SERVICE = 'TURN_DOWN_SERVICE'
    TWIN_BED = 'TWIN_BED'
    VAULTED_CEILINGS = 'VAULTED_CEILINGS'
    VCR_MOVIES = 'VCR_MOVIES'
    VCR_PLAYER = 'VCR_PLAYER'
    VIDEO_GAMES_AMENITY = 'VIDEO_GAMES_AMENITY'
    VOICE_MAIL = 'VOICE_MAIL'
    WAKEUP_CALLS = 'WAKEUP_CALLS'
    WATER_CLOSET = 'WATER_CLOSET'
    WATER_PURIFICATION_SYSTEM = 'WATER_PURIFICATION_SYSTEM'
    WET_BAR = 'WET_BAR'
    WIRELESS_INTERNET_CONNECTION = 'WIRELESS_INTERNET_CONNECTION'
    WIRELESS_KEYBOARD = 'WIRELESS_KEYBOARD'
    ADAPTOR_AVAILABLE_FOR_TELEPHONE_PC_USE = 'ADAPTOR_AVAILABLE_FOR_TELEPHONE_PC_USE'
    AIR_CONDITIONING_INDIVIDUALLY_CONTROLLED_IN_ROOM = (
        'AIR_CONDITIONING_INDIVIDUALLY_CONTROLLED_IN_ROOM'
    )
    BATHTUB_ANDWHIRLPOOL_SEPARATE = 'BATHTUB_ANDWHIRLPOOL_SEPARATE'
    TELEPHONE_WITH_DATA_PORTS = 'TELEPHONE_WITH_DATA_PORTS'
    CD_PLAYER = 'CD_PLAYER'
    COMPLIMENTARY_LOCAL_CALLS_TIME_LIMIT = 'COMPLIMENTARY_LOCAL_CALLS_TIME_LIMIT'
    EXTRA_PERSON_CHARGE_FOR_ROLLAWAY_USE = 'EXTRA_PERSON_CHARGE_FOR_ROLLAWAY_USE'
    DOWN_FEATHER_PILLOWS = 'DOWN_FEATHER_PILLOWS'
    DESK_WITH_ELECTRICAL_OUTLET = 'DESK_WITH_ELECTRICAL_OUTLET'
    ESPN_AVAILABLE = 'ESPN_AVAILABLE'
    FOAM_PILLOWS = 'FOAM_PILLOWS'
    HBO_AVAILABLE = 'HBO_AVAILABLE'
    HIGH_CEILINGS = 'HIGH_CEILINGS'
    MARBLE_BATHROOM = 'MARBLE_BATHROOM'
    LIST_OF_MOVIE_CHANNELS_AVAILABLE = 'LIST_OF_MOVIE_CHANNELS_AVAILABLE'
    PETS_ALLOWED = 'PETS_ALLOWED'
    OVERSIZED_BATHTUB = 'OVERSIZED_BATHTUB'
    SHOWER = 'SHOWER'
    SINK_INROOM = 'SINK_INROOM'
    SOUNDPROOFED_ROOM = 'SOUNDPROOFED_ROOM'
    STORAGE_SPACE = 'STORAGE_SPACE'
    TABLES_AND_CHAIRS = 'TABLES_AND_CHAIRS'
    TWOLINE_PHONE = 'TWOLINE_PHONE'
    WALKIN_CLOSET = 'WALKIN_CLOSET'
    WASHER_DRYER = 'WASHER_DRYER'
    WEIGHT_SCALE = 'WEIGHT_SCALE'
    WELCOME_GIFT = 'WELCOME_GIFT'
    SPARE_ELECTRICAL_OUTLET_AVAILABLE_AT_DESK = (
        'SPARE_ELECTRICAL_OUTLET_AVAILABLE_AT_DESK'
    )
    NONREFUNDABLE_CHARGE_FOR_PETS = 'NONREFUNDABLE_CHARGE_FOR_PETS'
    REFUNDABLE_DEPOSIT_FOR_PETS = 'REFUNDABLE_DEPOSIT_FOR_PETS'
    SEPARATE_TUB_AND_SHOWER = 'SEPARATE_TUB_AND_SHOWER'
    ENTRANCE_TYPE_TO_GUEST_ROOM = 'ENTRANCE_TYPE_TO_GUEST_ROOM'
    CEILING_FAN = 'CEILING_FAN'
    CNN_AVAILABLE = 'CNN_AVAILABLE'
    ELECTRICAL_ADAPTORS_AVAILABLE = 'ELECTRICAL_ADAPTORS_AVAILABLE'
    BUFFET_BREAKFAST = 'BUFFET_BREAKFAST'
    ACCESSIBLE_ROOM = 'ACCESSIBLE_ROOM'
    CLOSETS_IN_ROOM = 'CLOSETS_IN_ROOM'
    DVD_PLAYER = 'DVD_PLAYER'
    MINIREFRIGERATOR = 'MINIREFRIGERATOR'
    SEPARATE_LINE_BILLING_FOR_MULTILINE_PHONE = (
        'SEPARATE_LINE_BILLING_FOR_MULTILINE_PHONE'
    )
    SELFCONTROLLED_HEATING_COOLING_SYSTEM = 'SELFCONTROLLED_HEATING_COOLING_SYSTEM'
    TOASTER = 'TOASTER'
    ANALOG_DATA_PORT = 'ANALOG_DATA_PORT'
    COLLECT_CALLS = 'COLLECT_CALLS'
    INTERNATIONAL_CALLS = 'INTERNATIONAL_CALLS'
    CARRIER_ACCESS = 'CARRIER_ACCESS'
    INTERSTATE_CALLS = 'INTERSTATE_CALLS'
    INTRASTATE_CALLS = 'INTRASTATE_CALLS'
    LOCAL_CALLS = 'LOCAL_CALLS'
    LONG_DISTANCE_CALLS = 'LONG_DISTANCE_CALLS'
    OPERATORASSISTED_CALLS = 'OPERATORASSISTED_CALLS'
    CREDIT_CARD_ACCESS_CALLS = 'CREDIT_CARD_ACCESS_CALLS'
    CALLING_CARD_CALLS = 'CALLING_CARD_CALLS'
    TOLL_FREE_CALLS = 'TOLL_FREE_CALLS'
    UNIVERSAL_AC_DC_ADAPTORS = 'UNIVERSAL_AC_DC_ADAPTORS'
    BATHTUB_SEAT = 'BATHTUB_SEAT'
    CANOPY_POSTER_BED = 'CANOPY_POSTER_BED'
    CUPS_GLASSWARE = 'CUPS_GLASSWARE'
    ENTERTAINMENT_CENTER = 'ENTERTAINMENT_CENTER'
    FAMILY_OVERSIZED_ROOM = 'FAMILY_OVERSIZED_ROOM'
    HYPOALLERGENIC_BED = 'HYPOALLERGENIC_BED'
    HYPOALLERGENIC_PILLOWS = 'HYPOALLERGENIC_PILLOWS'
    LAMP = 'LAMP'
    MEAL_INCLUDED_BREAKFAST = 'MEAL_INCLUDED_BREAKFAST'
    MEAL_INCLUDED_CONTINENTAL_BREAKFAST = 'MEAL_INCLUDED_CONTINENTAL_BREAKFAST'
    MEAL_INCLUDED_DINNER = 'MEAL_INCLUDED_DINNER'
    MEAL_INCLUDED_LUNCH = 'MEAL_INCLUDED_LUNCH'
    SHARED_BATHROOM = 'SHARED_BATHROOM'
    TELEPHONE_TDD_TEXTPHONE = 'TELEPHONE_TDD_TEXTPHONE'
    WATER_BED = 'WATER_BED'
    EXTRA_ADULT_CHARGE = 'EXTRA_ADULT_CHARGE'
    EXTRA_CHILD_CHARGE = 'EXTRA_CHILD_CHARGE'
    EXTRA_CHILD_CHARGE_FOR_ROLLAWAY_USE = 'EXTRA_CHILD_CHARGE_FOR_ROLLAWAY_USE'
    MEAL_INCLUDED_FULL_AMERICAN_BREAKFAST = 'MEAL_INCLUDED_FULL_AMERICAN_BREAKFAST'
    FUTON = 'FUTON'
    MURPHY_BED = 'MURPHY_BED'
    TATAMI_MATS = 'TATAMI_MATS'
    SINGLE_BED = 'SINGLE_BED'
    ANNEX_ROOM = 'ANNEX_ROOM'
    FREE_NEWSPAPER = 'FREE_NEWSPAPER'
    HONEYMOON_SUITES = 'HONEYMOON_SUITES'
    COMPLIMENTARY_HIGH_SPEED_INTERNET_IN_ROOM = (
        'COMPLIMENTARY_HIGH_SPEED_INTERNET_IN_ROOM'
    )
    MAID_SERVICE = 'MAID_SERVICE'
    PC_HOOKUP_IN_ROOM = 'PC_HOOKUP_IN_ROOM'
    SATELLITE_TELEVISION = 'SATELLITE_TELEVISION'
    VIP_ROOMS = 'VIP_ROOMS'
    CELL_PHONE_RECHARGER = 'CELL_PHONE_RECHARGER'
    DVR_PLAYER = 'DVR_PLAYER'
    IPOD_DOCKING_STATION = 'IPOD_DOCKING_STATION'
    MEDIA_CENTER = 'MEDIA_CENTER'
    PLUG_AND_PLAY_PANEL = 'PLUG_AND_PLAY_PANEL'
    SATELLITE_RADIO = 'SATELLITE_RADIO'
    VIDEO_ON_DEMAND = 'VIDEO_ON_DEMAND'
    EXTERIOR_CORRIDORS = 'EXTERIOR_CORRIDORS'
    GULF_VIEW = 'GULF_VIEW'
    ACCESSIBLE_ROOM_AMENITY = 'ACCESSIBLE_ROOM_AMENITY'
    INTERIOR_CORRIDORS = 'INTERIOR_CORRIDORS'
    MOUNTAIN_VIEW = 'MOUNTAIN_VIEW'
    OCEAN_VIEW = 'OCEAN_VIEW'
    HIGH_SPEED_INTERNET_ACCESS_FEE = 'HIGH_SPEED_INTERNET_ACCESS_FEE'
    HIGH_SPEED_WIRELESS = 'HIGH_SPEED_WIRELESS'
    PREMIUM_MOVIE_CHANNELS = 'PREMIUM_MOVIE_CHANNELS'
    SLIPPERS = 'SLIPPERS'
    FIRST_NIGHTERS_KIT = 'FIRST_NIGHTERS_KIT'
    CHAIR_PROVIDED_WITH_DESK = 'CHAIR_PROVIDED_WITH_DESK'
    PILLOW_TOP_MATTRESS = 'PILLOW_TOP_MATTRESS'
    FEATHER_BED = 'FEATHER_BED'
    DUVET = 'DUVET'
    LUXURY_LINEN_TYPE = 'LUXURY_LINEN_TYPE'
    INTERNATIONAL_CHANNELS = 'INTERNATIONAL_CHANNELS'
    PANTRY = 'PANTRY'
    DISHCLEANING_SUPPLIES = 'DISHCLEANING_SUPPLIES'
    DOUBLE_VANITY = 'DOUBLE_VANITY'
    LIGHTED_MAKEUP_MIRROR = 'LIGHTED_MAKEUP_MIRROR'
    UPGRADED_BATHROOM_AMENITIES = 'UPGRADED_BATHROOM_AMENITIES'
    VCR_PLAYER_AVAILABLE_AT_FRONT_DESK = 'VCR_PLAYER_AVAILABLE_AT_FRONT_DESK'
    INSTANT_HOT_WATER = 'INSTANT_HOT_WATER'
    OUTDOOR_SPACE = 'OUTDOOR_SPACE'
    HINOKI_TUB = 'HINOKI_TUB'
    PRIVATE_POOL = 'PRIVATE_POOL'
    HIGH_DEFINITION_HD_FLAT_PANEL_TELEVISION_32_INCHES_OR_GREATER = (
        'HIGH_DEFINITION_HD_FLAT_PANEL_TELEVISION_32_INCHES_OR_GREATER'
    )
    ROOM_WINDOWS_OPEN = 'ROOM_WINDOWS_OPEN'
    BEDDING_TYPE_UNKNOWN_OR_UNSPECIFIED = 'BEDDING_TYPE_UNKNOWN_OR_UNSPECIFIED'
    FULL_BED = 'FULL_BED'
    ROUND_BED = 'ROUND_BED'
    TV = 'TV'
    CHILD_ROLLAWAY = 'CHILD_ROLLAWAY'
    DVD_PLAYER_AVAILABLE_AT_FRONT_DESK = 'DVD_PLAYER_AVAILABLE_AT_FRONT_DESK'
    VIDEO_GAME_PLAYER = 'VIDEO_GAME_PLAYER'
    VIDEO_GAME_PLAYER_AVAILABLE_AT_FRONT_DESK = (
        'VIDEO_GAME_PLAYER_AVAILABLE_AT_FRONT_DESK'
    )
    DINING_ROOM_SEATS = 'DINING_ROOM_SEATS'
    FULL_SIZE_MIRROR = 'FULL_SIZE_MIRROR'
    MOBILE_CELLULAR_PHONES = 'MOBILE_CELLULAR_PHONES'
    MOVIES = 'MOVIES'
    MULTIPLE_CLOSETS = 'MULTIPLE_CLOSETS'
    PLATES_GLASSWARE = 'PLATES_GLASSWARE'
    SAFE_LARGE_ENOUGH_TO_ACCOMMODATE_A_LAPTOP = (
        'SAFE_LARGE_ENOUGH_TO_ACCOMMODATE_A_LAPTOP'
    )
    BED_LINEN_THREAD_COUNT = 'BED_LINEN_THREAD_COUNT'
    BLACKOUT_CURTAIN = 'BLACKOUT_CURTAIN'
    BLURAY_PLAYER = 'BLURAY_PLAYER'
    DEVICE_WITH_MP3 = 'DEVICE_WITH_MP3'
    NO_ADULT_CHANNELS_OR_ADULT_CHANNEL_LOCK = 'NO_ADULT_CHANNELS_OR_ADULT_CHANNEL_LOCK'
    NONALLERGENIC_ROOM = 'NONALLERGENIC_ROOM'
    PILLOW_TYPE = 'PILLOW_TYPE'
    SEATING_AREA_WITH_SOFA_CHAIR = 'SEATING_AREA_WITH_SOFA_CHAIR'
    SEPARATE_TOILET_AREA = 'SEPARATE_TOILET_AREA'
    WEB_ENABLED = 'WEB_ENABLED'
    WIDESCREEN_TV = 'WIDESCREEN_TV'
    OTHER_DATA_CONNECTION = 'OTHER_DATA_CONNECTION'
    PHONELINE_BILLED_SEPARATELY = 'PHONELINE_BILLED_SEPARATELY'
    SEPARATE_TUB_OR_SHOWER = 'SEPARATE_TUB_OR_SHOWER'
    VIDEO_GAMES = 'VIDEO_GAMES'
    ROOF_VENTILATOR = 'ROOF_VENTILATOR'
    CHILDRENS_PLAYPEN = 'CHILDRENS_PLAYPEN'
    PLUNGE_POOL = 'PLUNGE_POOL'
    DVD_MOVIES = 'DVD_MOVIES'
    AIR_FILTRATION = 'AIR_FILTRATION'


class HotelRoomAmenity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    additionalInfo: str | None = Field(
        None, description='Extra information about the room amenity'
    )
    complimentary: bool | None = Field(
        None, description='Is amenity complimentary', examples=[True]
    )
    type: Type2 | None = Field(
        None, description='Room amenity type', examples=['WEB_ENABLED']
    )


class HotelRoomMealType(Enum):
    UNKNOWN = 'UNKNOWN'
    ALL_INCLUSIVE = 'ALL_INCLUSIVE'
    AMERICAN = 'AMERICAN'
    BED_AND_BREAKFAST = 'BED_AND_BREAKFAST'
    BUFFET_BREAKFAST = 'BUFFET_BREAKFAST'
    CARIBBEAN_BREAKFAST = 'CARIBBEAN_BREAKFAST'
    CONTINENTAL_BREAKFAST = 'CONTINENTAL_BREAKFAST'
    ENGLISH_BREAKFAST = 'ENGLISH_BREAKFAST'
    EUROPEAN_PLAN = 'EUROPEAN_PLAN'
    FAMILY_PLAN = 'FAMILY_PLAN'
    FULL_BOARD = 'FULL_BOARD'
    FULL_BREAKFAST = 'FULL_BREAKFAST'
    HALF_BOARD_MODIFIED_AMERICAN_PLAN = 'HALF_BOARD_MODIFIED_AMERICAN_PLAN'
    AS_BROCHURED = 'AS_BROCHURED'
    ROOM_ONLY = 'ROOM_ONLY'
    SELF_CATERING = 'SELF_CATERING'
    BERMUDA = 'BERMUDA'
    DINNER_BED_AND_BREAKFAST_PLAN = 'DINNER_BED_AND_BREAKFAST_PLAN'
    FAMILY_AMERICAN = 'FAMILY_AMERICAN'
    BREAKFAST_MEAL_PLAN = 'BREAKFAST_MEAL_PLAN'
    MODIFIED = 'MODIFIED'
    LUNCH_MEAL_PLAN = 'LUNCH_MEAL_PLAN'
    DINNER_MEAL_PLAN = 'DINNER_MEAL_PLAN'
    BREAKFAST_AND_LUNCH = 'BREAKFAST_AND_LUNCH'


class HotelRoomMealsIncluded(Enum):
    BREAKFAST = 'BREAKFAST'
    LUNCH = 'LUNCH'
    DINNER = 'DINNER'


class SortBy(Enum):
    PRICE = 'PRICE'
    DISTANCE = 'DISTANCE'
    SABRE_RATING = 'SABRE_RATING'


class SortOrder(Enum):
    ASCENDING = 'ASCENDING'
    DESCENDING = 'DESCENDING'


class HotelSortOptions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    sortBy: SortBy = Field(
        ..., description='Sorting criteria for the hotel search results.'
    )
    sortOrder: SortOrder = Field(
        ..., description='The order in which the search results should be displayed.'
    )


class RoomLocation(Enum):
    HIGH_FLOOR = 'HIGH_FLOOR'
    LOW_FLOOR = 'LOW_FLOOR'


class RoomFeature(Enum):
    CRIB = 'CRIB'
    ROLLAWAY_BED = 'ROLLAWAY_BED'
    FEATHER_FREE_ROOM = 'FEATHER_FREE_ROOM'
    ACCESSIBLE_ROOM = 'ACCESSIBLE_ROOM'
    NEAR_ELEVATOR = 'NEAR_ELEVATOR'


class CheckIn(Enum):
    EARLY_CHECK_IN = 'EARLY_CHECK_IN'
    LATE_CHECK_IN = 'LATE_CHECK_IN'


class RatingType(Enum):
    OFFICIAL = 'OFFICIAL'
    SELF_RATED = 'SELF_RATED'


class HotelStarRatingInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    starRating: confloat(ge=0.0, le=5.0) = Field(
        ..., description='Star rating of the hotel.', examples=[3]
    )
    ratingType: RatingType = Field(
        ...,
        description='Enum representing the type of star rating.',
        title='Star Rating Type',
    )


class HotelValidateRebookingResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    isValid: bool = Field(
        ..., description='Indicates if the validation is successful.', examples=[True]
    )
    spotnanaPnrId: str = Field(
        ...,
        description='The Spotnana PNR ID of the corresponding source PNR ID provided in the request.',
        examples=['1cf76aba18e4015f'],
    )


class Image(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    data: str | None = Field(None, examples=['6935813e12584abda0e43d71cd2ea260'])
    dimensions: Dimensions | None = None
    url: str | None = Field(
        None,
        examples=[
            'https://static.wixstatic.com/media/73f2e2_6935813e12584abda0e43d71cd2ea260~mv2.png/v1/fill/w_630,h_94,al_c,q_85,usm_0.66_1.00_0.01/Spotnana%403x.webp'
        ],
    )


class ImageGroup(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    caption: str | None = Field(
        None, description='Caption for the image.', examples=['Exterior']
    )
    images: Sequence[Image] = Field(..., description='List of images.')


class Type3(Enum):
    UNKNOWN = 'UNKNOWN'
    VISA = 'VISA'


class ImmigrationDocument(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    authorizedStayDuration: Duration | None = Field(
        None, description='Duration of the stay authorized by the immigration document.'
    )
    docId: str = Field(
        ...,
        description='The ID of the immigration document.',
        examples=['ImmigrationDocumentID'],
    )
    expiryDate: DateModel = Field(
        ..., description='The date on which the immigration document expires.'
    )
    issueCountry: str = Field(
        ...,
        description='The country that issued the immigration document.',
        examples=['IN'],
    )
    issuedDate: DateModel | None = Field(
        None, description='The date on which the immigration document was issued.'
    )
    nationalityCountry: str | None = Field(None, examples=['IN'])
    reentryRequirementDuration: Duration | None = None
    type: Type3 | None = Field(None, examples=['VISA'])


class ImmigrationDocumentWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    immigrationDoc: ImmigrationDocument | None = None


class IncludeLocation(Enum):
    BOOKING_CONFIRMATION_EMAILS = 'BOOKING_CONFIRMATION_EMAILS'
    APPROVAL_EMAILS = 'APPROVAL_EMAILS'
    COMPANY_REPORTS = 'COMPANY_REPORTS'
    CONSOLIDATED_ITINERARY_EMAILS = 'CONSOLIDATED_ITINERARY_EMAILS'


class Int32Range(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    min: int | None = Field(None, description='Minimum value - inclusive.')
    max: int | None = Field(None, description='Maximum value - inclusive.')


class Int32RangeWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iRange: Int32Range | None = None


class Int32Wrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    i: int | None = None


class Int64Wrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    l: int | None = None


class IntListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iList: Sequence[int] | None = None


class ItemType(Enum):
    SERVICE_FEE = 'SERVICE_FEE'
    TRAVEL_TICKET = 'TRAVEL_TICKET'
    SEAT = 'SEAT'
    BAGGAGE = 'BAGGAGE'
    EARLY_BIRD = 'EARLY_BIRD'


class KnownTravelerNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    number: str = Field(..., examples=['12345'])
    issueCountry: str = Field(..., examples=['US'])


class KnownTravelerNumberWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ktn: KnownTravelerNumber | None = None


class Latlng(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    latitude: float = Field(
        ..., description='Latitude of the Location', examples=[77.1025]
    )
    longitude: float = Field(
        ..., description='Longitude of the Location', examples=[28.7041]
    )


class LegalEntityId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['fc1ccbce-8413-4fe9-b233-a324dfbe7421'])


class LegalEntityIdListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legalEntityIdList: Sequence[LegalEntityId] | None = None


class LegalEntityIdWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    legalEntityId: LegalEntityId | None = None


class Unit(Enum):
    UNKNOWN_UNIT = 'UNKNOWN_UNIT'
    KM = 'KM'
    MILE = 'MILE'


class Length(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    length: float = Field(
        ..., description='Distance from search point.', examples=[150]
    )
    unit: Unit = Field(
        ..., description='Unit of measure being applied.', examples=['MILE']
    )


class LengthWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    length: Length | None = None


class Location(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    countryCode: str | None = Field(
        None, description='Unique country code for a location', examples=['TR']
    )
    countryName: str | None = Field(
        None, description='Full name of the country', examples=['Turkey']
    )
    googlePlaceId: str | None = Field(
        None,
        description='Unique place ID for the location assigned by Google',
        examples=['ChIJL_P_CXMEDTkRw0ZdG-0GVvw'],
    )
    latlong: Latlng | None = None
    name: str = Field(..., description='Full name of the Location', examples=['Denver'])
    stateName: str | None = Field(
        None, description='Full name of the state', examples=['Colorado']
    )


class Type4(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    CAR = 'CAR'
    RAIL = 'RAIL'


class LoyaltyInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    appliedTo: Sequence[str] | None = None
    id: str = Field(..., examples=['firstId'])
    issuedBy: str = Field(..., examples=['firstIssuedBy'])
    type: Type4 = Field(..., examples=['AIR'])


class MealType(Enum):
    UNKNOWN_MEAL = 'UNKNOWN_MEAL'
    AVML = 'AVML'
    BBML = 'BBML'
    BLML = 'BLML'
    CHML = 'CHML'
    DBML = 'DBML'
    FPML = 'FPML'
    GFML = 'GFML'
    HFML = 'HFML'
    HNML = 'HNML'
    KSML = 'KSML'
    LCML = 'LCML'
    LFML = 'LFML'
    LPML = 'LPML'
    LSML = 'LSML'
    MOML = 'MOML'
    NLML = 'NLML'
    NSML = 'NSML'
    ORML = 'ORML'
    PFML = 'PFML'
    RVML = 'RVML'
    SFML = 'SFML'
    SPML = 'SPML'
    VGML = 'VGML'
    VJML = 'VJML'
    VLML = 'VLML'
    VOML = 'VOML'


class MultiHotelCodesSearch(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchType: Literal['HOTEL_CODES'] = Field('HOTEL_CODES', examples=['HOTEL_CODES'])
    hotelCodes: Sequence[str] = Field(
        ..., description='List of hotel codes to be included in the search result.'
    )


class NameSuffix(Enum):
    NAME_SUFFIX_UNKNOWN = 'NAME_SUFFIX_UNKNOWN'
    SR = 'SR'
    JR = 'JR'
    MD = 'MD'
    PHD = 'PHD'
    II = 'II'
    III = 'III'
    IV = 'IV'
    DO = 'DO'
    ATTY = 'ATTY'
    V = 'V'
    VI = 'VI'
    ESQ = 'ESQ'
    DC = 'DC'
    DDS = 'DDS'
    VM = 'VM'
    JD = 'JD'
    SECOND = 'SECOND'
    THIRD = 'THIRD'


class Type5(Enum):
    DNI = 'DNI'
    NIE = 'NIE'


class NationalDoc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    docId: str = Field(
        ...,
        description='Unique id identifying the national document.',
        examples=['NationalDocId'],
    )
    issueCountry: str = Field(
        ...,
        description='IS0 2 letter country code of the country issuing this id.',
        examples=['IN'],
    )
    issuedDate: DateModel | None = None
    expiryDate: DateModel | None = None
    type: Type5 | None = Field(None, examples=['DNI'])


class NationalDocWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    nationalDoc: NationalDoc | None = None


class NumStopsPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numOfStops: int = Field(..., examples=[34])


class Occupancy(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numAdults: conint(ge=1) = Field(
        ..., description='The number of adults staying in the room.', examples=[1]
    )
    numChildren: int | None = Field(
        None, description='The number of children staying in the room.', examples=[0]
    )


class OccupancyDates(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    occupancy: Sequence[Occupancy] = Field(
        ...,
        description='List of occupancy details including the number of adults and children.',
    )
    checkInDate: DateTimeLocal = Field(
        ..., description='The check-in date for the hotel stay.'
    )
    checkOutDate: DateTimeLocal = Field(
        ..., description='The check-out date for the hotel stay.'
    )


class OfficeId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(
        ...,
        description='The value of the unique ID for the office.',
        examples=['531ccbce-8413-4fe9-b233-a324dfbe7421'],
    )


class OfficeIdListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    officeIdList: Sequence[OfficeId] | None = Field(
        None, description='A list of office IDs.'
    )


class OfficeIdWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    officeId: OfficeId | None = None


class Option(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    displayCode: str = Field(
        ..., description='The code which is sent in answer response.'
    )
    displayValue: str | None = Field(
        None, description='The text to be displayed to the user beside this option.'
    )


class OptionSource(Enum):
    MANUAL = 'MANUAL'
    COMPANY_CONFIG = 'COMPANY_CONFIG'


class OrganizationAgencyId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str


class OrganizationId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str


class OwnershipLabel(Enum):
    CORPORATE = 'CORPORATE'
    PERSONAL = 'PERSONAL'
    CENTRAL = 'CENTRAL'


class PageSize(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    requestType: Literal['PAGE_SIZE'] = Field(
        'PAGE_SIZE', description='Discriminator for the type of pagination.'
    )
    pageSize: conint(le=100) | None = Field(
        50, description='The page size.', examples=[10]
    )


class PageToken(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    requestType: Literal['PAGE_TOKEN'] = Field(
        'PAGE_TOKEN', description='Discriminator for the type of pagination.'
    )
    pageToken: str = Field(..., description='The page token.', examples=['token'])


class Type6(Enum):
    UNKNOWN = 'UNKNOWN'
    REGULAR = 'REGULAR'


class Passport(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    docId: str = Field(..., examples=['PassportID'])
    expiryDate: DateModel
    issueCountry: str = Field(..., examples=['IN'])
    issuedDate: DateModel | None = None
    nationalityCountry: str = Field(..., examples=['IN'])
    type: Type6 | None = Field(None, examples=['REGULAR'])


class PassportWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    passport: Passport | None = None


class ApplicableToEnum(Enum):
    UNKNOWN_APPLICABLE_TO = 'UNKNOWN_APPLICABLE_TO'
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    RAIL = 'RAIL'
    CAR = 'CAR'
    SERVICE_FEE = 'SERVICE_FEE'


class PaymentItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    itemType: ItemType | None = Field(
        None, description='Type of payment item eligible for this fop rule'
    )
    fareComponent: Sequence[FareComponent] | None = None


class PaymentMethod(Enum):
    PAYMENT_METHOD_UNKNOWN = 'PAYMENT_METHOD_UNKNOWN'
    CREDIT_CARD = 'CREDIT_CARD'
    BREX_POINTS = 'BREX_POINTS'
    CASH = 'CASH'
    QANTAS_POINTS = 'QANTAS_POINTS'
    VENDOR_PROGRAM_PAYMENT = 'VENDOR_PROGRAM_PAYMENT'
    DELAYED_INVOICING = 'DELAYED_INVOICING'
    FLIGHT_CREDITS = 'FLIGHT_CREDITS'
    QANTAS_TRAVEL_FUND = 'QANTAS_TRAVEL_FUND'
    CUSTOM_VIRTUAL_PAYMENT = 'CUSTOM_VIRTUAL_PAYMENT'


class PenaltyPercentage(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    penaltyPercentage: float | None = Field(None, examples=[10])


class PercentageWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    percentage: float | None = None


class Persona(Enum):
    UNKNOWN_PERSONA = 'UNKNOWN_PERSONA'
    EMPLOYEE = 'EMPLOYEE'
    GUEST = 'GUEST'
    PERSONAL = 'PERSONAL'
    RELATIVE = 'RELATIVE'
    ADHOC = 'ADHOC'


class PersonaListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    personaList: Sequence[Persona] | None = None


class PersonaWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    persona: Persona | None = None


class CountryCodeSource(Enum):
    UNSPECIFIED = 'UNSPECIFIED'
    FROM_NUMBER_WITH_PLUS_SIGN = 'FROM_NUMBER_WITH_PLUS_SIGN'
    FROM_NUMBER_WITH_IDD = 'FROM_NUMBER_WITH_IDD'
    FROM_NUMBER_WITHOUT_PLUS_SIGN = 'FROM_NUMBER_WITHOUT_PLUS_SIGN'
    FROM_DEFAULT_COUNTRY = 'FROM_DEFAULT_COUNTRY'


class Type7(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    MOBILE = 'MOBILE'
    LANDLINE = 'LANDLINE'


class PhoneNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    countryCode: int | None = Field(
        None, description='two digit country code', examples=[91]
    )
    countryCodeSource: CountryCodeSource | None = Field(
        None, examples=['FROM_NUMBER_WITH_PLUS_SIGN']
    )
    extension: str | None = Field(
        None, description='phone number extension', examples=['222']
    )
    isoCountryCode: str | None = Field(
        None, description='ISO alpha-2 code', examples=['IN']
    )
    italianLeadingZero: bool | None = Field(False, examples=[True])
    nationalNumber: int | None = Field(None, examples=[8150])
    numberOfLeadingZeros: int | None = Field(0, examples=[1])
    preferredDomesticCarrierCode: str | None = Field(None, examples=['7'])
    rawInput: str | None = Field(None, examples=['77777'])
    type: Type7 | None = Field(None, examples=['MOBILE'])


class PolicyAlertOnSelectionAction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    message: str | None = None


class PolicyAlertOnSelectionActionWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    alertOnSelection: PolicyAlertOnSelectionAction | None = None


class PolicyFlagAction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    message: str | None = None


class PolicyFlagActionWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    flag: PolicyFlagAction | None = None


class PolicyHideActionWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hide: bool | None = None


class PolicyPredicate(Enum):
    UNKNOWN_PREDICATE_STRING = 'UNKNOWN_PREDICATE_STRING'
    MAX_FARE_PER_TRAVELLER_VIOLATION = 'MAX_FARE_PER_TRAVELLER_VIOLATION'
    FARE_MORE_THAN_MINIMUM = 'FARE_MORE_THAN_MINIMUM'
    FARE_MORE_THAN_MEDIAN = 'FARE_MORE_THAN_MEDIAN'
    FARE_LESS_THAN_MEDIAN = 'FARE_LESS_THAN_MEDIAN'
    FARE_MORE_THAN_LLF = 'FARE_MORE_THAN_LLF'
    MAX_FARE_PER_TRAVELLER_VIOLATION_INCLUDING_TAX = (
        'MAX_FARE_PER_TRAVELLER_VIOLATION_INCLUDING_TAX'
    )
    MAX_FARE_PER_TRAVELLER_VIOLATION_EXCLUDING_TAX = (
        'MAX_FARE_PER_TRAVELLER_VIOLATION_EXCLUDING_TAX'
    )
    HOTEL_PAYMENT_OPTIONS_VIOLATION = 'HOTEL_PAYMENT_OPTIONS_VIOLATION'
    RAIL_BOOKING_WINDOW_GAP_VIOLATION = 'RAIL_BOOKING_WINDOW_GAP_VIOLATION'
    RAIL_TRAVEL_CLASS_VIOLATION = 'RAIL_TRAVEL_CLASS_VIOLATION'
    RAIL_TICKET_REFUNDABLE_VIOLATION = 'RAIL_TICKET_REFUNDABLE_VIOLATION'
    RAIL_MAX_BOOKING_PRICE_VIOLATION_INCLUDING_TAX = (
        'RAIL_MAX_BOOKING_PRICE_VIOLATION_INCLUDING_TAX'
    )
    RAIL_MAX_BOOKING_PRICE_VIOLATION_EXCLUDING_TAX = (
        'RAIL_MAX_BOOKING_PRICE_VIOLATION_EXCLUDING_TAX'
    )
    AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_INCLUDING_TAX = (
        'AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_INCLUDING_TAX'
    )
    AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_EXCLUDING_TAX = (
        'AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_EXCLUDING_TAX'
    )
    HOTEL_RESTRICTED_KEYWORDS_VIOLATION = 'HOTEL_RESTRICTED_KEYWORDS_VIOLATION'
    RESTRICTED_LOCATION_VIOLATION = 'RESTRICTED_LOCATION_VIOLATION'
    FLIGHT_ADVANCE_BOOKING_WINDOW_DOMESTIC_VIOLATION = (
        'FLIGHT_ADVANCE_BOOKING_WINDOW_DOMESTIC_VIOLATION'
    )
    FLIGHT_ADVANCE_BOOKING_WINDOW_INTERNATIONAL_VIOLATION = (
        'FLIGHT_ADVANCE_BOOKING_WINDOW_INTERNATIONAL_VIOLATION'
    )
    FLIGHT_ADVANCE_BOOKING_WINDOW_VIOLATION = 'FLIGHT_ADVANCE_BOOKING_WINDOW_VIOLATION'
    ITINERARY_WITHIN_EVENT_TRAVEL_WINDOW = 'ITINERARY_WITHIN_EVENT_TRAVEL_WINDOW'
    HOTEL_IN_ALLOWED_HOTEL_LIST = 'HOTEL_IN_ALLOWED_HOTEL_LIST'
    PAYMENT_ACCESS_VIOLATION = 'PAYMENT_ACCESS_VIOLATION'
    AIRPORT_IN_ALLOWED_AIRPORT_LIST = 'AIRPORT_IN_ALLOWED_AIRPORT_LIST'
    ITINERARY_TYPE_IS_NOT_IN_ALLOWED_BOOKING_TYPES = (
        'ITINERARY_TYPE_IS_NOT_IN_ALLOWED_BOOKING_TYPES'
    )
    PAYMENT_AIR_ADDON_VIOLATION = 'PAYMENT_AIR_ADDON_VIOLATION'
    MAX_HOTEL_BOOKING_PRICE_INCLUDING_TAX = 'MAX_HOTEL_BOOKING_PRICE_INCLUDING_TAX'
    MAX_HOTEL_BOOKING_PRICE_EXCLUDING_TAX = 'MAX_HOTEL_BOOKING_PRICE_EXCLUDING_TAX'
    AIR_NUM_TRAVELERS_ALLOWED = 'AIR_NUM_TRAVELERS_ALLOWED'
    PREFERRED_VENDOR_VIOLATION = 'PREFERRED_VENDOR_VIOLATION'
    SEAT_ADDON_VIOLATION = 'SEAT_ADDON_VIOLATION'
    BAGGAGE_ADDON_VIOLATION = 'BAGGAGE_ADDON_VIOLATION'
    EARLY_BIRD_ADDON_VIOLATION = 'EARLY_BIRD_ADDON_VIOLATION'
    WIFI_ADDON_VIOLATION = 'WIFI_ADDON_VIOLATION'
    RESTRICTED_BOOKING_VIOLATION = 'RESTRICTED_BOOKING_VIOLATION'
    HIGHEST_ALLOWED_CABIN_VIOLATION = 'HIGHEST_ALLOWED_CABIN_VIOLATION'
    LOWEST_FARE_PER_HOTEL_PROPERTY_VIOLATION = (
        'LOWEST_FARE_PER_HOTEL_PROPERTY_VIOLATION'
    )


class PolicyPreventBookingAction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    prevent: bool | None = Field(
        None,
        description='True if booking is to be blocked if rule is violated, else false',
    )
    reason: str | None = Field(
        None,
        description='Reason describing why was that specific itinerary not allowed to book.',
    )


class PolicyPreventBookingActionWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preventBooking: PolicyPreventBookingAction | None = None


class PolicyType(Enum):
    DEFAULT = 'DEFAULT'
    GROUP = 'GROUP'
    INTERNAL = 'INTERNAL'


class PostalAddress(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    addressLines: Sequence[str] = Field(..., description='Address lines')
    administrativeArea: str | None = Field(
        None,
        description='Code of administrative area. For example: DL for Delhi, India.\nHighest administrative subdivision which is used for postal\naddresses of a country or region.\nFor example, this can be a state, a province, an oblast, or a prefecture.\nSpecifically, for Spain this is the province and not the autonomous\ncommunity (e.g. "Barcelona" and not "Catalonia").\nMany countries don\'t use an administrative area in postal addresses. E.g.\nin Switzerland this should be left unpopulated.\n',
        examples=['CA'],
    )
    administrativeAreaName: str | None = Field(
        None,
        description='Name of administrative area. This is full name corresponding to administrativeArea. \nLike Delhi for DL area code. For some places, code and name maybe same as well like Tokyo.\n',
        examples=['California'],
    )
    description: str | None = Field(
        None, description='Address description', examples=['San Francisco Home']
    )
    isDefault: bool | None = Field(
        None,
        description='Whether this address is default address in case multiple addresses are specified.',
        examples=[True],
    )
    languageCode: str | None = Field(
        None,
        description='BCP-47 language code of the contents of this address (if known). This is often the UI \nlanguage of the input form or is expected to match one of the languages used in the \naddress\' country/region, or their transliterated equivalents.\nThis can affect formatting in certain countries, but is not critical to the correctness \nof the data and will never affect any validation or other non-formatting related operations.\nExamples: "zh-Hant", "ja", "ja-Latn", "en".\n',
        examples=['en'],
    )
    locality: str | None = Field(
        None,
        description='Generally refers to the city/town portion of the address.',
        examples=['San Francisco'],
    )
    locationCode: str | None = Field(
        None,
        description='IATA 3-letter location code. See https://www.iata.org/en/services/codes.',
        examples=['LAX'],
    )
    organization: str | None = Field(
        None,
        description='The name of the organization at the address.',
        examples=['Spotnana'],
    )
    postalCode: str | None = Field(
        None,
        description='Postal code of the address. This is a required field when setting for a user/legal entity/company etc.',
        examples=['94130'],
    )
    continentCode: str | None = Field(
        None,
        description='2 letter continent code of the continent this address falls in.',
        examples=['AF'],
    )
    recipients: Sequence[str] | None = Field(
        None, description='The recipient at the address.'
    )
    regionCode: str = Field(
        ...,
        description='Region code of the country/region of the address.',
        examples=['US'],
    )
    regionName: str | None = Field(
        None,
        description='Region name of the country/region of the address.',
        examples=['America'],
    )
    revision: int | None = Field(None, examples=[1])
    sortingCode: str | None = Field(
        None,
        description='Additional, country-specific, sorting code. This is not used\nin most regions. Where it is used, the value is either a string like\n"CEDEX", optionally followed by a number (e.g. "CEDEX 7"), or just a number\nalone, representing the "sector code" (Jamaica), "delivery area indicator"\n(Malawi) or "post office indicator" (e.g. Côte d\'Ivoire).\n',
        examples=['Jamaica'],
    )
    sublocality: str | None = Field(
        None,
        description='Sublocality of the address. This can be neighborhoods, boroughs, districts.',
    )
    timezone: str | None = Field(
        None, description='Time zone of the address.', examples=['America/Los_Angeles']
    )
    coordinates: Latlng | None = Field(
        None, description='Map coordinates of the address.'
    )


class PostalAddressWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    postalAddress: PostalAddress | None = None


class PreCheckoutQuestionType(Enum):
    UNKNOWN_CHECKOUT_QUESTION_TYPE = 'UNKNOWN_CHECKOUT_QUESTION_TYPE'
    USER_DEFINED_QUESTION = 'USER_DEFINED_QUESTION'
    OOP_REASON_CODE = 'OOP_REASON_CODE'


class PreSearchQuestionType(Enum):
    UNKNOWN_SEARCH_QUESTION_TYPE = 'UNKNOWN_SEARCH_QUESTION_TYPE'
    PURPOSE_OF_TRIP = 'PURPOSE_OF_TRIP'


class PreferredLocationLabel(Enum):
    HOME = 'HOME'
    WORK = 'WORK'
    OTHER = 'OTHER'


class PreferredRailStation(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    stationName: str | None = Field(
        None, description='Rail station name.', examples=['Chicago Union Station']
    )
    stationCode: str = Field(..., description='Rail station code.', examples=['CHI'])
    cityName: str | None = Field(
        None,
        description='Name of city where the rail station is located.',
        examples=['Chicago'],
    )
    countryCode: str | None = Field(
        None, description='Alpha-2 country code where the rail station is located.'
    )
    label: PreferredLocationLabel


class PreferredType(Enum):
    NOT_PREFERRED = 'NOT_PREFERRED'
    COMPANY_PREFERRED = 'COMPANY_PREFERRED'
    SPOTTERS_CHOICE = 'SPOTTERS_CHOICE'
    COMPANY_BLOCKED = 'COMPANY_BLOCKED'
    TMC_PREFERRED = 'TMC_PREFERRED'


class PrepaidQualifier(Enum):
    INCLUDE_PREPAID = 'INCLUDE_PREPAID'
    EXCLUDE_PREPAID = 'EXCLUDE_PREPAID'
    PREPAID_ONLY = 'PREPAID_ONLY'


class PrimaryServiceProviderTmc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tmcId: CompanyId = Field(..., description='Id of the service provider TMC.')


class PromotionalOffer(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(
        ..., description='Name of the promotional offer applied on this hotel rate.'
    )


class QuestionFormat(Enum):
    INPUT_BOX = 'INPUT_BOX'
    RADIO_BUTTON = 'RADIO_BUTTON'
    CHECKBOX = 'CHECKBOX'
    CHECKBOX_WITH_PERCENTAGE = 'CHECKBOX_WITH_PERCENTAGE'


class QuestionType(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preSearchQuestionType: PreSearchQuestionType | None = None
    preCheckoutQuestionType: PreCheckoutQuestionType | None = None


class RailCard(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cardNumber: str | None = Field(None, description='Number of card')
    expiryDate: DateModel | None = Field(
        None, description='Expiry date of the Rail Card.'
    )
    name: str = Field(
        ..., description='Name of the Rail Card.', examples=['Veterans Railcard']
    )
    spotnanaCode: str = Field(
        ...,
        description='Unique Spotnana code/identifier for Rail Card.',
        examples=['VET'],
    )
    vendor: str = Field(..., description='Vendor Name.', examples=['ATOC'])


class RailTravelClass(Enum):
    FIRST = 'FIRST'
    STANDARD = 'STANDARD'
    BUSINESS = 'BUSINESS'
    SLEEPER = 'SLEEPER'
    STANDARD_PREMIUM = 'STANDARD_PREMIUM'
    BUSINESS_PREMIUM = 'BUSINESS_PREMIUM'
    COACH = 'COACH'
    ROOM = 'ROOM'
    EXECUTIVE = 'EXECUTIVE'


class GuaranteeType(Enum):
    GUARANTEE = 'GUARANTEE'
    DEPOSIT = 'DEPOSIT'


class RatingWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    rating: float | None = None


class RedressNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    number: str = Field(..., examples=['12345'])
    issueCountry: str = Field(..., examples=['US'])


class RedressNumberWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    redress: RedressNumber | None = None


class Reference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID
    name: str | None = None


class RestrictedKeywordsWithReason(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    keyword: str | None = Field(
        None, description='Restricted keyword', examples=['Test Keyword']
    )
    reason: str | None = Field(
        None, description='Reason for restriction', examples=['Test Reason']
    )


class RewardPointsType(Enum):
    QBR = 'QBR'
    QFF = 'QFF'


class RewardPointsEarned(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    rewardPointsType: RewardPointsType = Field(
        ..., description='Type of reward points.'
    )
    totalPointsEarned: float = Field(
        ..., description='Total points that will be credited.'
    )
    conversionMultiplier: float = Field(
        ..., description='Multiplier for converting points.'
    )


class RoomClass(Enum):
    STANDARD = 'STANDARD'
    PREMIUM = 'PREMIUM'
    DELUXE = 'DELUXE'
    BUSINESS = 'BUSINESS'
    PRESIDENTIAL = 'PRESIDENTIAL'
    SUPERIOR = 'SUPERIOR'
    JUNIOR = 'JUNIOR'
    CLUB = 'CLUB'
    UNKNOWN = 'UNKNOWN'


class FeeType(Enum):
    UNKNOWN = 'UNKNOWN'
    BED_TAX = 'BED_TAX'
    CITY_HOTEL_FEE = 'CITY_HOTEL_FEE'
    CITY_TAX = 'CITY_TAX'
    COUNTRY_TAX = 'COUNTRY_TAX'
    ENERGY_TAX = 'ENERGY_TAX'
    FEDERAL_TAX = 'FEDERAL_TAX'
    FOOD_AND_BEVERAGE_TAX = 'FOOD_AND_BEVERAGE_TAX'
    LODGING_TAX = 'LODGING_TAX'
    MAINTENANCE_FEE = 'MAINTENANCE_FEE'
    OCCUPANCY_TAX = 'OCCUPANCY_TAX'
    PACKAGE_FEE = 'PACKAGE_FEE'
    RESORT_FEE = 'RESORT_FEE'
    SALES_TAX = 'SALES_TAX'
    SERVICE_CHARGE = 'SERVICE_CHARGE'
    STATE_TAX = 'STATE_TAX'
    SURCHARGE = 'SURCHARGE'
    TOTAL_TAX = 'TOTAL_TAX'
    TOURISM_TAX = 'TOURISM_TAX'
    VAT_GST_TAX = 'VAT_GST_TAX'
    SURPLUS_LINES_TAX = 'SURPLUS_LINES_TAX'
    INSURANCE_PREMIUM_TAX = 'INSURANCE_PREMIUM_TAX'
    APPLICATON_FEE = 'APPLICATON_FEE'
    EXPRESS_HANDLING_FEE = 'EXPRESS_HANDLING_FEE'
    EXEMPT = 'EXEMPT'
    STANDARD = 'STANDARD'
    ZERO_RATED = 'ZERO_RATED'
    MISCELLANEOUS = 'MISCELLANEOUS'
    ROOM_TAX = 'ROOM_TAX'
    EARLY_CHECKOUT_FEE = 'EARLY_CHECKOUT_FEE'
    COUNTRY_TAXES = 'COUNTRY_TAXES'
    EXTRA_PERSON_CHARGE = 'EXTRA_PERSON_CHARGE'
    BANQUET_SERVICE_FEE = 'BANQUET_SERVICE_FEE'
    ROOM_SERVICE_FEE = 'ROOM_SERVICE_FEE'
    LOCAL_FEE = 'LOCAL_FEE'
    GOODS_AND_SERVICES_TAX = 'GOODS_AND_SERVICES_TAX'
    VALUE_ADDED_TAX = 'VALUE_ADDED_TAX'
    CRIB_FEE = 'CRIB_FEE'
    ROLLAWAY_FEE = 'ROLLAWAY_FEE'
    ASSESSMENT_LICENSE_TAX = 'ASSESSMENT_LICENSE_TAX'
    PET_SANITATION_FEE = 'PET_SANITATION_FEE'
    NOT_KNOWN = 'NOT_KNOWN'
    CHILD_ROLLAWAY_CHARGE = 'CHILD_ROLLAWAY_CHARGE'
    CONVENTION_TAX = 'CONVENTION_TAX'
    EXTRA_CHILD_CHARGE = 'EXTRA_CHILD_CHARGE'
    STANDARD_FOOD_AND_BEVERAGE_GRATUITY = 'STANDARD_FOOD_AND_BEVERAGE_GRATUITY'
    NATIONAL_GOVERNMENT_TAX = 'NATIONAL_GOVERNMENT_TAX'
    ADULT_ROLLAWAY_FEE = 'ADULT_ROLLAWAY_FEE'
    BEVERAGE_WITH_ALCOHOL = 'BEVERAGE_WITH_ALCOHOL'
    BEVERAGE_WITHOUT_ALCOHOL = 'BEVERAGE_WITHOUT_ALCOHOL'
    TOBACCO = 'TOBACCO'
    FOOD = 'FOOD'
    TOTAL_SURCHARGES = 'TOTAL_SURCHARGES'
    STATE_COST_RECOVERY_FEE = 'STATE_COST_RECOVERY_FEE'
    MISCELLANEOUS_FEE = 'MISCELLANEOUS_FEE'
    DESTINATION_AMENITY_FEE = 'DESTINATION_AMENITY_FEE'
    REFUNDABLE_PET_FEE = 'REFUNDABLE_PET_FEE'
    CHARITY_SUPPORT_FEE = 'CHARITY_SUPPORT_FEE'
    LOCAL_AMENITY_USAGE_MAINTENANCE_FEE = 'LOCAL_AMENITY_USAGE_MAINTENANCE_FEE'
    CONVENTION_TOURISM_FEE = 'CONVENTION_TOURISM_FEE'
    DESTINATION_MARKETING_FEE = 'DESTINATION_MARKETING_FEE'
    HOTEL_DEVELOPMENT_FEE = 'HOTEL_DEVELOPMENT_FEE'
    EVENT_FEE = 'EVENT_FEE'
    SUSTAINABILITY_FEE = 'SUSTAINABILITY_FEE'
    TRANSPORTATION_TRANSFER_FEE = 'TRANSPORTATION_TRANSFER_FEE'
    INSURANCE_FEE = 'INSURANCE_FEE'
    LOCAL_GOVERNMENT_FEE = 'LOCAL_GOVERNMENT_FEE'
    LOCAL_ORDINANCE_SURCHARGE = 'LOCAL_ORDINANCE_SURCHARGE'
    GUARANTEED_EARLY_CHECK_IN_FEE = 'GUARANTEED_EARLY_CHECK_IN_FEE'
    GUARANTEED_LATE_CHECK_OUT_FEE = 'GUARANTEED_LATE_CHECK_OUT_FEE'


class BedCount(Enum):
    ONE_BED = 'ONE_BED'
    TWO_BEDS = 'TWO_BEDS'


class RoomType1(Enum):
    SMOKING = 'SMOKING'
    NON_SMOKING = 'NON_SMOKING'


class MostImportantFact(Enum):
    ROOM_TYPE = 'ROOM_TYPE'
    BED_COUNT = 'BED_COUNT'
    ROOM_LOCATION = 'ROOM_LOCATION'


class RoomLocation1(Enum):
    HIGH_FLOOR = 'HIGH_FLOOR'
    LOW_FLOOR = 'LOW_FLOOR'
    NEAR_ELEVATOR = 'NEAR_ELEVATOR'


class PillowType(Enum):
    FOAM = 'FOAM'
    EXTRA_FOAM = 'EXTRA_FOAM'
    EXTRA_FEATHER = 'EXTRA_FEATHER'


class RoomAmenityPref(Enum):
    FEATHER_FREE_ROOM = 'FEATHER_FREE_ROOM'
    EXTRA_TOWELS = 'EXTRA_TOWELS'
    REFRIGERATOR = 'REFRIGERATOR'


class RoomPreference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    isMobilityAccessible: bool | None = Field(
        False,
        description='Whether or not mobility accessible room, tub.',
        examples=[False],
    )
    bedCount: BedCount | None = Field(
        None, description='The number of bed in the room.', examples=['ONE_BED']
    )
    roomType: RoomType1 | None = Field(
        None, description='Single selection of type of room.', examples=['SMOKING']
    )
    mostImportantFact: MostImportantFact | None = Field(
        None,
        description='Single selection of the most import fact.',
        examples=['BED_COUNT'],
    )
    roomLocation: RoomLocation1 | None = Field(
        None, description='Location of the hotel room', examples=['HIGH_FLOOR']
    )
    pillowType: PillowType | None = Field(
        None, description='The type of pillow in hotel room.', examples=['FOAM']
    )
    roomAmenityPrefs: Sequence[RoomAmenityPref] | None = None


class RoomType(Enum):
    ROOM = 'ROOM'
    SUITE = 'SUITE'
    VILLA = 'VILLA'
    APARTMENT = 'APARTMENT'
    COTTAGE = 'COTTAGE'
    STUDIO = 'STUDIO'
    UNKNOWN_ROOM = 'UNKNOWN_ROOM'


class RoomView(Enum):
    UNKNOWN = 'UNKNOWN'
    AIRPORT = 'AIRPORT'
    BAY = 'BAY'
    CITY = 'CITY'
    COURTYARD = 'COURTYARD'
    GOLF = 'GOLF'
    HARBOR = 'HARBOR'
    INTERCOASTAL = 'INTERCOASTAL'
    LAKE = 'LAKE'
    MARINA = 'MARINA'
    MOUNTAIN = 'MOUNTAIN'
    OCEAN = 'OCEAN'
    POOL = 'POOL'
    RIVER = 'RIVER'
    WATER = 'WATER'
    BEACH = 'BEACH'
    GARDEN = 'GARDEN'
    PARK = 'PARK'
    FOREST = 'FOREST'
    RAIN_FOREST = 'RAIN_FOREST'
    VARIOUS = 'VARIOUS'
    LIMITED = 'LIMITED'
    SLOPE = 'SLOPE'
    STRIP = 'STRIP'
    COUNTRYSIDE = 'COUNTRYSIDE'
    SEA = 'SEA'
    GULF = 'GULF'


class SeatAmenityType(Enum):
    UNKNOWN_AIR_SEAT_AMENITY_TYPE = 'UNKNOWN_AIR_SEAT_AMENITY_TYPE'
    FLAT_BED = 'FLAT_BED'
    WIFI = 'WIFI'
    IN_SEAT_POWER = 'IN_SEAT_POWER'


class SeatAmenityPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    seatAmenityTypes: Sequence[SeatAmenityType]


class Cabin(Enum):
    UNKNOWN_CABIN = 'UNKNOWN_CABIN'
    ECONOMY = 'ECONOMY'
    PREMIUM_ECONOMY = 'PREMIUM_ECONOMY'
    BUSINESS = 'BUSINESS'
    FIRST = 'FIRST'


class Position(Enum):
    UNKNOWN_POSITION = 'UNKNOWN_POSITION'
    AISLE = 'AISLE'
    WINDOW = 'WINDOW'
    AISLE_OR_WINDOW = 'AISLE_OR_WINDOW'


class SeatLocationPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cabins: Sequence[Cabin] | None = None
    isBulkHeadPref: bool | None = Field(None, examples=[False])
    maxFlightDurationInHours: int | None = Field(None, examples=[3])
    position: Position | None = Field(None, examples=['WINDOW'])


class SeatPrefDirection(Enum):
    FORWARD = 'FORWARD'
    BACKWARD = 'BACKWARD'


class SeatPrefLocation(Enum):
    AISLE = 'AISLE'
    WINDOW = 'WINDOW'
    SOLO = 'SOLO'


class SeatPrefType(Enum):
    SLEEPER_BED = 'SLEEPER_BED'
    NORMAL = 'NORMAL'
    TABLE_SEAT = 'TABLE_SEAT'


class SimpleMoney(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: float = Field(..., description='Amount', examples=[510])
    currencyCode: str = Field(
        ...,
        description='The 3-letter currency code defined in ISO 4217.',
        examples=['GBP'],
    )


class SimpleMoneyRange(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    min: SimpleMoney | None = Field(None, description='Minimum value - inclusive.')
    max: SimpleMoney | None = Field(None, description='Maximum value - inclusive.')


class StringListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    sList: Sequence[str] | None = None


class StringWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    s: str | None = None


class SupplierType(Enum):
    SABRE = 'SABRE'
    AMADEUS = 'AMADEUS'
    TRAVEL_FUSION = 'TRAVEL_FUSION'
    FARELOGIX_NDC = 'FARELOGIX_NDC'
    ATPCO_NDC = 'ATPCO_NDC'
    TRAINLINE = 'TRAINLINE'
    AVIA = 'AVIA'
    QBR = 'QBR'
    BCD = 'BCD'
    QANTAS_HOTELS = 'QANTAS_HOTELS'
    SOUTHWEST = 'SOUTHWEST'
    EXPEDIA = 'EXPEDIA'
    HOTEL_HUB = 'HOTEL_HUB'
    NDC = 'NDC'
    MARRIOTT = 'MARRIOTT'
    CLEARTRIP = 'CLEARTRIP'
    KYTE = 'KYTE'
    GROUNDSPAN = 'GROUNDSPAN'
    SABRE_NDC = 'SABRE_NDC'
    BOOKING_COM = 'BOOKING_COM'


class TermsAndConditions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    conditions: Sequence[Condition] | None = Field(
        None, description='List of conditions'
    )


class ThirdPartySource(Enum):
    UNKNOWN_SOURCE = 'UNKNOWN_SOURCE'
    SABRE = 'SABRE'
    TRAVEL_FUSION = 'TRAVEL_FUSION'
    AVIA = 'AVIA'
    NDC = 'NDC'
    TRAINLINE = 'TRAINLINE'
    ATPCO_NDC = 'ATPCO_NDC'
    FARELOGIX_NDC = 'FARELOGIX_NDC'
    OFFLINE = 'OFFLINE'
    CONNEXUS = 'CONNEXUS'
    ROUTEHAPPY = 'ROUTEHAPPY'
    AMADEUS = 'AMADEUS'
    GIATA = 'GIATA'
    QBR = 'QBR'
    BCD = 'BCD'
    QANTAS_HOTELS = 'QANTAS_HOTELS'
    SOUTHWEST = 'SOUTHWEST'
    EXPEDIA = 'EXPEDIA'
    HOTEL_HUB = 'HOTEL_HUB'
    MARRIOTT = 'MARRIOTT'
    CLEARTRIP = 'CLEARTRIP'
    KYTE = 'KYTE'
    GROUNDSPAN = 'GROUNDSPAN'
    SABRE_NDC = 'SABRE_NDC'
    BOOKING_COM = 'BOOKING_COM'


class Tier(Enum):
    BASIC = 'BASIC'
    SEAT1A = 'SEAT1A'


class TimeLocal(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(pattern=r'^([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$') = (
        Field(..., examples=['17:32'])
    )


class TokenBasedPaginationRequest(RootModel[PageToken | PageSize]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: PageToken | PageSize = Field(
        ...,
        description='Pagination request for token-based pagination, where a token is used to retrieve the next page of results.',
        discriminator='requestType',
        title='Token Based Pagination Request',
    )


class TokenBasedPaginationResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    nextPageToken: str | None = Field(
        None,
        description='The next page token to retrieve the next page of results. If it is empty, it indicates that there are no more pages to retrieve.',
    )
    currentPageSize: int = Field(
        ..., description='The number of results in the current page.', examples=[10]
    )


class TokenizedExpiry(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiryMonth: str = Field(
        ..., description='Tokenized Expiry month', examples=['KvAuPANQWCpjwRQxcC8EXg==']
    )
    expiryYear: str = Field(
        ..., description='Tokenized Expiry year', examples=['fPBm0OWrKwPyIrCVcbg4cA==']
    )


class TokenizedExpiryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tokenizedExpiry: TokenizedExpiry | None = None


class TransmissionSearchFilter(Enum):
    MANUAL = 'MANUAL'
    AUTOMATIC = 'AUTOMATIC'


class TravelClassHierarchy(Enum):
    UNKNOWN = 'UNKNOWN'
    STANDARD = 'STANDARD'
    COACH = 'COACH'
    STANDARD_PREMIUM = 'STANDARD_PREMIUM'
    BUSINESS = 'BUSINESS'
    BUSINESS_PREMIUM = 'BUSINESS_PREMIUM'
    FIRST = 'FIRST'
    SLEEPER = 'SLEEPER'


class TravelClassHierarchyWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    railTravelClass: TravelClassHierarchy | None = None


class TravelRegionType(Enum):
    DOMESTIC = 'DOMESTIC'
    INTERNATIONAL = 'INTERNATIONAL'


class TravelType(Enum):
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    CAR = 'CAR'
    RAIL = 'RAIL'
    LIMO = 'LIMO'
    MISC = 'MISC'
    ALL = 'ALL'


class TripId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., description='Id.', examples=['2783425534'])


class TripUsageType(Enum):
    STANDARD = 'STANDARD'
    EVENT = 'EVENT'


class UserId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID


class UserIdSearch(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userIdType: Literal['USER_ID'] = Field('USER_ID', examples=['USER_ID'])
    userId: UserId


class UserIdWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId | None = None


class UserTitle(Enum):
    TITLE_UNKNOWN = 'TITLE_UNKNOWN'
    MR = 'MR'
    MS = 'MS'
    MRS = 'MRS'
    MX = 'MX'
    MASTER = 'MASTER'
    MISS = 'MISS'
    DR = 'DR'
    PROFESSOR = 'PROFESSOR'
    CAPTAIN = 'CAPTAIN'
    REVEREND = 'REVEREND'
    HONOURABLE = 'HONOURABLE'
    SIR = 'SIR'
    LADY = 'LADY'
    AMBASSADOR = 'AMBASSADOR'
    LORD = 'LORD'
    BRIGADIER = 'BRIGADIER'
    SENATOR = 'SENATOR'
    DAME = 'DAME'
    JUSTICE = 'JUSTICE'
    UK = 'UK'


class VariableName(Enum):
    PUBLISHED_FARE = 'PUBLISHED_FARE'
    LLF = 'LLF'


class WorkerType(Enum):
    EMPLOYEE = 'EMPLOYEE'
    CONTINGENT = 'CONTINGENT'
    SEASONAL = 'SEASONAL'
    INTERN = 'INTERN'
    GUEST = 'GUEST'


class WorkerTypeListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    workerTypeList: Sequence[WorkerType] | None = None


class WorkerTypeWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    workerType: WorkerType | None = None


class AdhocTravelerInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    registrarUserId: UserId | None = Field(
        None, description='The registrar of the adhoc traveler.'
    )
    externalId: str | None = Field(
        None, description='External Id of user', examples=['qwert123']
    )


class AdhocTravelerInfoWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    adhocTravelerInfo: AdhocTravelerInfo | None = None


class AirRequestTravelerInfo(RootModel[UserIdWrapper | AdhocTravelerInfoWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: UserIdWrapper | AdhocTravelerInfoWrapper = Field(
        ...,
        description='The traveler identifiers. These can be either the Spotnana user IDs of the travelers or information regarding\nthe adhoc travelers.\n',
        title='AirRequestTravelerInfo',
    )


class BedInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bedCount: int | None = Field(None, description='Number of beds.', examples=[1])
    bedType: BedType | None = Field(None, description='Bed type.')


class BookingContact(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    emailAddress: str | None = Field(
        None,
        description='Email address of the booking contact',
        examples=['<EMAIL>'],
    )
    phoneNumber: PhoneNumber | None = None


class CancellationDuration(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    deadlineAbsolute: DateTimeLocal | None = None
    deadlineDurationBeforeArrival: Duration | None = None


class CarPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendors: Sequence[CarVendor] | None = Field(
        None, description='A list of car vendors.'
    )
    carTypes: Sequence[CarType] | None = Field(
        None, description='A list of types of car.'
    )
    engineTypes: Sequence[EngineType] | None = Field(
        None, description='A list of types of engine.'
    )
    transmissionTypes: Sequence[TransmissionSearchFilter] | None = Field(
        None, description='A list of types of transmission.'
    )
    conditionalRates: Sequence[ConditionalRate] | None = Field(
        None, description='A list of conditional rates for rail.'
    )


class CardAccessEntity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    entityId: str = Field(
        ...,
        description='Holds the id for for the user who can access the card or organization id or legal entity',
    )
    centralCardAccessLevel: CentralCardAccessLevel | None = None


class CardExpiry(RootModel[TokenizedExpiryWrapper | ExpiryWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: TokenizedExpiryWrapper | ExpiryWrapper = Field(
        ..., description='Contains the expiry of a Card.', title='CardExpiry'
    )


class CompanyConfigSource(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    optionsParam: CustomFieldOptionsParam


class CompanyConfigSourceWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyConfig: CompanyConfigSource | None = None


class CompanyRef(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CompanyId
    name: str | None = None
    logo: Image | None = Field(None, description='Company logo')


class ContactInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    phone: Sequence[PhoneNumber] = Field(
        ..., description='List of phone numbers for the hotel.'
    )
    fax: Sequence[str] | None = Field(
        None, description='List of fax numbers for the hotel.'
    )
    email: Sequence[str] = Field(
        ..., description='List of email addresses for the hotel.'
    )


class CoordinatesSearch(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchType: Literal['COORDINATES'] = Field('COORDINATES', examples=['COORDINATES'])
    coordinates: Latlng = Field(
        ..., description='Search for hotels near the given coordinates.'
    )


class CostCenter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CostCenterId
    name: str = Field(..., examples=['CostCenter'])
    externalId: str | None = Field(None, examples=['external-id'])


class CreditCardAccess(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    accessType: CreditCardAccessType
    entityIds: Sequence[str] = Field(
        ...,
        description='Holds the ids for for all users who can access the card or organization id',
    )
    entities: Sequence[CardAccessEntity] | None = Field(
        None,
        description='A list of cardAccessEntity consisting of central card access level if present and entity id.',
    )


class Department(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: DepartmentId
    name: str = Field(..., examples=['IT Department'])
    externalId: str | None = Field(
        None,
        description='External id of the department',
        examples=['department-ext-id'],
    )
    employeeCount: int | None = Field(
        None, description='Count of employees in the department', examples=[57]
    )


class EmergencyContact(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Full name of contact.', examples=['John Smith'])
    email: EmailStr | None = Field(
        None,
        description='Email address of contact.',
        examples=['<EMAIL>'],
    )
    designation: str | None = Field(
        None, description='Job title of contact.', examples=['MANAGER']
    )
    relation: Relation | None = Field(
        None, description='Relation of contact to user.', examples=['SPOUSE']
    )
    phoneNumbers: Sequence[PhoneNumber] = Field(
        ..., description='Phone numbers of contact.'
    )
    preferredLanguage: str | None = Field(
        None, description='Language preferred by user.', examples=['en-US']
    )


class Expression(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['EXPRESSION'] = Field('EXPRESSION', examples=['EXPRESSION'])
    formatExpression: str = Field(
        ...,
        description='The expression must be of format : `${expression}`.The expression can consist of a \ncombination of variables and mathematical operations.\n Variable names must begin with `var` followed by a number, which is used to identify \nthe variable in the variables list. The numbering should follow a 1-based index.\n  To define mathematical operations, the operation name should follow the format\n`math.<math_op>(arg1, arg2)`. Both `arg1` and `arg2` can be variables or constants. \nThe supported math operations (math_op) include: `add, mul, div, sub, min,\nand max`. All keywords, such as `<math_op>, math, and var` must be written in lowercase.\n',
        examples=['Result:  ${math.mul(var1,5)}  ${var2}'],
    )
    variables: Sequence[VariableName] | None = Field(
        None, description='Reference names of the variables present in the expression.'
    )


class Grade(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    employeeCount: int | None = Field(
        None, description='Count of employees in the grade', examples=[75]
    )
    id: GradeId
    name: str = Field(..., examples=['Grade'])


class HotelAdditionalDetail(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    additionalDetailType: HotelAdditionalDetailType | None = Field(
        None, description='Type of the Additional Detail for the room.'
    )
    text: str | None = None


class HotelDetailsFilters(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    rateTypes: Sequence[HotelRateType] = Field(
        ..., description='Filter rates based on rate type.'
    )


class HotelImageSet(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    category: HotelImageCategory
    imageGroup: ImageGroup


class HotelPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelParentChains: Sequence[HotelChain] | None = Field(
        None, description='A list of hotel parent chains.'
    )
    hotelBrands: Sequence[HotelBrand] | None = Field(
        None, description='A list of hotel brands.'
    )
    hotelAmenityTypes: Sequence[HotelPrefAmenity] | None = Field(
        None, description='A list of HotelAmenities.'
    )
    roomPreference: RoomPreference | None = None
    conditionalRates: Sequence[ConditionalRate] | None = Field(
        None, description='A list of conditional rates for rail.'
    )


class HotelRateAssuranceMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    selfReportedSavings: SimpleMoney | None = None
    actualSavings: SimpleMoney | None = None


class HotelRoomInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    roomType: RoomType | None = Field(None, description='The type of room.')
    typeClassDescription: str | None = Field(
        None, description='A description of the room type class.'
    )
    roomClasses: Sequence[RoomClass] | None = Field(
        None, description='List of room class.'
    )
    roomView: RoomView | None = Field(None, description='The type of room view.')


class HotelRoomMeals(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    mealsIncluded: Sequence[HotelRoomMealsIncluded] | None = None
    mealType: HotelRoomMealType = Field(..., description='Meal type')


class HotelSearchFilters(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    prepaidQualifier: PrepaidQualifier | None = Field(
        None, description='Filter for prepaid qualifiers.'
    )
    priceRange: SimpleMoneyRange | None = Field(
        None,
        description='Filter based on range of starting average nightly prices for hotels.',
    )
    amenities: Sequence[HotelAmenityType] | None = Field(
        None,
        description='List of preferred amenities to be included in the hotel search result.',
    )
    chainCodes: Sequence[str] | None = Field(
        None,
        description='List of preferred hotel chain codes to be included in the search result.\nA hotel chain is a company that owns or operates multiple hotels across different locations.\nThese hotel chains have a unique code which can be used here as an array.\n',
    )
    radius: Length | None = Field(
        None, description='Radius to search within from a specified central point.'
    )
    starRatings: Sequence[int] | None = Field(
        None,
        description='Indicates the star quality rating of a hotel. Filter search result using preferred star ratings.',
    )
    nameQuery: str | None = Field(
        None, description='The name of the hotel to be included in the search query.'
    )
    eligibleForLoyalty: bool | None = Field(
        None,
        description='Include hotels that are eligible for loyalty points.',
        examples=[False],
    )
    showUnavailable: bool | None = Field(
        None, description='Include unavailable hotels.', examples=[False]
    )
    payByPoints: bool | None = Field(
        None,
        description='Include hotels that support payment by points.',
        examples=[False],
    )
    modifiableOnly: bool | None = Field(
        None,
        description='Include hotels that allow users to modify their booking.',
        examples=[False],
    )
    rateTypes: Sequence[HotelRateType] | None = Field(
        None, description='List of rate type identifiers to filter the search results.'
    )
    propertyTypes: Sequence[HotelPropertyType] | None = Field(
        None,
        description='Filter by property type such as a hotel, an apartment, bed and breakfast, and so on.',
    )
    refundableOnly: bool | None = Field(
        None,
        description='Include hotels that support refund of the booking.',
        examples=[False],
    )
    preferredOnly: bool | None = Field(
        None, description='Filter to show only preferred hotels.', examples=[False]
    )


class HotelSearchParams(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    occupancyDates: OccupancyDates = Field(
        ..., description='Occupancy values and dates for the hotel search.'
    )
    searchBy: (
        AirportSearch | CoordinatesSearch | HotelCodeSearch | MultiHotelCodesSearch
    ) = Field(
        ...,
        description='Criteria used for searching hotels. The `searchType` field can contain one criteria per request.',
        discriminator='searchType',
    )


class HotelSpec(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(
        ...,
        description='Name of the hotel.',
        examples=['London Heathrow Marriott Hotel'],
    )
    address: PostalAddress = Field(..., description='Address of the hotel.')
    coordinates: Latlng = Field(..., description='Coordinates of the hotel.')
    contactInfo: ContactInfo = Field(
        ..., description='Contact information for the hotel.'
    )
    amenities: Sequence[HotelAmenity] = Field(
        ..., description='List of hotel amenities.'
    )
    descriptions: Sequence[HotelDescription] | None = Field(
        None, description='List of hotel descriptions.'
    )
    imageSets: Sequence[HotelImageSet] | None = Field(
        None, description='List of hotel image sets.'
    )
    hotelId: str = Field(
        ..., description='Unique hotel identifier.', examples=['SPOTNANA:1001']
    )
    brandCode: str | None = Field(
        None, description='The code of hotel brand.', examples=['MC']
    )
    brandName: str | None = Field(
        None, description='Brand name of the hotel.', examples=['Marriott Hotel Brands']
    )
    chainCode: str | None = Field(
        None, description='The code of hotel chain.', examples=['EM']
    )
    chainName: str | None = Field(
        None,
        description='Name of the hotel chain.',
        examples=['Marriott Hotels & Resorts'],
    )
    propertyTypes: Sequence[HotelPropertyType] = Field(
        ..., description='List of property types for the hotel.'
    )
    starRating: HotelStarRatingInfo = Field(
        ..., description='Hotel star rating detail.'
    )
    additionalAmenities: Sequence[str] | None = Field(
        None, description='Additional amenities provided by the hotel.'
    )
    checkinTime: TimeLocal | None = Field(
        None, description='The check-in time for the hotel.'
    )
    checkoutTime: TimeLocal | None = Field(
        None, description='The check-out time for the hotel.'
    )


class HotelSpecialRequests(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    roomLocations: Sequence[RoomLocation] | None = Field(
        None, description='Room Location special request'
    )
    roomFeatures: Sequence[RoomFeature] | None = Field(
        None, description='Room Features List'
    )
    checkIn: CheckIn | None = Field(
        None, description='Early or Late Check-in', examples=['LATE_CHECK_IN']
    )
    checkInTime: TimeLocal | None = Field(
        None, description='Requested time for check-in'
    )
    flightNumber: str | None = Field(
        None, description='Attach flight number', examples=['AC1234']
    )
    additionalNote: str | None = Field(
        None,
        description='Free form text to describe special request',
        examples=['Extra pillows and blankets for added comfort during the stay.'],
    )
    accessibleFeatures: Sequence[HotelAccessibleFeatureType] | None = Field(
        None, description='Accessible Features List'
    )


class AdhocInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    profileOwnerId: UserId | None = Field(
        None, description='The profile owner of the adhoc user.'
    )


class IdentityDocument(
    RootModel[
        PassportWrapper
        | ImmigrationDocumentWrapper
        | RedressNumberWrapper
        | KnownTravelerNumberWrapper
        | NationalDocWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        PassportWrapper
        | ImmigrationDocumentWrapper
        | RedressNumberWrapper
        | KnownTravelerNumberWrapper
        | NationalDocWrapper
    ) = Field(
        ...,
        description='Identity document details. Currently supported documents are passport, immigration document, \nknown traveler number, redress number and national document.\n',
        title='IdentityDocument',
    )


class KeywordsWithReasonList(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    keywords: Sequence[RestrictedKeywordsWithReason] | None = None


class MealPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    exclMealPrefs: Sequence[MealType] | None = None
    inclMealPrefs: Sequence[MealType] | None = None
    specialMealDescription: str | None = Field(None, examples=['Veg only meal'])


class OtherCoinageItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    coinageCode: PaymentMethod | None = Field(None, description='Payment method')
    amount: float | None = Field(None, examples=[1000])
    conversionRate: float | None = Field(
        None,
        description='1 coin in this system equals to how many currency value',
        examples=[0.01],
    )
    preferredCurrencyConversionRate: float | None = Field(
        None,
        description='1 coin in this system equals to how many currency value',
        examples=[0.01],
    )


class Money(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: float = Field(
        ..., description='The numeric value for the amount of money.', examples=[510]
    )
    currencyCode: str = Field(
        ...,
        description='The 3-letter currency code for the money amount (defined using ISO 4217 standard).',
        examples=['GBP'],
    )
    convertedAmount: float | None = Field(
        None,
        description="The converted currency and amount that has been converted (if a currency conversion has been requested).\nFor example, if the call requests that money be sent in a specified currency (because the frontend requested\nthe backend to send money in the user's preferred currency).\n",
        examples=[715.42],
    )
    convertedCurrency: str | None = Field(
        None,
        description='The 3-letter currency code for the converted currency (defined using ISO 4217 standard).',
        examples=['USD'],
    )
    otherCoinage: Sequence[OtherCoinageItem] | None = Field(
        None,
        description='List of the dollar amount in other coinage systems like reward points, cryptocurrency etc.',
        title='OtherCoinage',
    )


class MoneyWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    money: Money | None = None


class Name(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    family1: str = Field(..., description='Last (family) name.', examples=['Gandas'])
    family2: str | None = Field(None, examples=['FamilyTwo'])
    given: str = Field(..., description='First (given) name.', examples=['Vichitr'])
    middle: str | None = Field(None, description='Middle name.', examples=['Kumar'])
    suffix: NameSuffix | None = Field(
        None,
        description='Suffix used with the name. For example SR or JR.',
        examples=['SR'],
    )
    preferred: str | None = Field(
        None,
        description='Informal preferred name added by traveler. This is not used on any PNR or tickets',
        examples=['Don'],
    )


class Office(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress | None = None
    id: OfficeId
    name: str = Field(..., examples=['Office'])
    latlng: Latlng | None = None
    taxId: str | None = Field(None, examples=['133232'])


class OptionSourceMetadata(RootModel[CompanyConfigSourceWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: CompanyConfigSourceWrapper = Field(
        ...,
        description='Metadata information for the option source.',
        title='OptionSourceMetadata',
    )


class PenaltyAmount(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    penaltyAmount: Money | None = None


class Preference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preferredType: PreferredType
    blockedReason: str | None = Field(
        None, description='Reason for blocking the leg, hotel or car.'
    )
    label: str | None = Field(
        None, description='The label assigned to a specific tier of preference.'
    )


class PreferredAirport(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airportName: str | None = Field(
        None,
        description='Airport name.',
        examples=['San Francisco International Airport'],
    )
    airportCode: str = Field(..., description='IATA airport code.', examples=['SFO'])
    label: PreferredLocationLabel


class ProfileOwner(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userId: UserId


class RegistrarUserIdSearch(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userIdType: Literal['REGISTRAR_USER_ID'] = Field(
        'REGISTRAR_USER_ID', examples=['REGISTRAR_USER_ID']
    )
    registrarUserId: UserId


class RoomFee(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Money = Field(..., description='Fees amount')
    feeInclusions: Sequence[str] | None = Field(
        None, description='Amenities included as part of the fee'
    )
    displayFee: bool | None = Field(
        None,
        description='Flag to determine whether to explicitly show this fee type on UI',
    )
    feeType: FeeType | None = Field(
        None, description='Fees Type', examples=['ROOM_TAX']
    )


class SeatPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hasAccessibility: bool | None = Field(
        False,
        description='Whether or not requires assistance for disability.',
        examples=[False],
    )
    seatTypes: Sequence[SeatPrefType] | None = None
    seatLocations: Sequence[SeatPrefLocation] | None = None
    deckLevels: Sequence[DeckLevel] | None = None
    seatDirections: Sequence[SeatPrefDirection] | None = None


class SecondaryServiceProviderTmc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tmcId: CompanyId = Field(..., description='Id of the service provider TMC.')
    supplier: SupplierType = Field(
        ..., description='Supplier for which this service provider should be used.'
    )
    travelType: TravelType = Field(
        ..., description='Travel type for which this service provider should be used.'
    )


class Tax(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Money = Field(..., description='Tax amount')
    taxCode: str | None = Field(None, description='Tax code', examples=['VAT'])
    percentage: float | None = Field(
        None, description='Tax amount to total amount', examples=[9]
    )


class TaxBreakdown(RootModel[Sequence[Tax]]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Sequence[Tax]


class TmcBasicInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    contractingTmc: CompanyRef = Field(
        ..., description='Contracting TMC is the TMC the user/organization contracted.'
    )
    bookingTmc: CompanyRef = Field(
        ...,
        description='Booking TMC is the TMC used for the bookings for the user/organization.',
    )


class TmcInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CompanyId = Field(..., description='TMC id.')
    primaryServiceProviderTmc: PrimaryServiceProviderTmc = Field(
        ..., description='Primary service provider TMC for the TMC.'
    )
    secondaryServiceProviderTmcs: Sequence[SecondaryServiceProviderTmc] | None = Field(
        None, description='Secondary service provider TMCs for the TMC.'
    )
    partnerTmcId: CompanyId | None = Field(
        None, description='Useful to identify the clients onboarded by a PARTNER_TMC'
    )


class TravelerMatchConditions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    workerTypes: Sequence[WorkerType] | None = Field(
        None, description='Worker types. Users belonging to any of these would match.'
    )
    countries: Sequence[str] | None = Field(None, description='Countries.')
    legalEntities: Sequence[Reference] | None = Field(
        None, description='Legal entities'
    )
    departments: Sequence[Reference] | None = Field(None, description='Departments')
    costCenters: Sequence[Reference] | None = Field(None, description='Cost centers')
    offices: Sequence[Reference] | None = Field(None, description='Offices')


class TripData(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    approverEmail: str | None = Field(
        None,
        description='Email address of the approver who should receives approval email for the current booking.',
    )
    approverName: str | None = Field(None, description='Name of the approver.')
    hardApprovalRequired: bool | None = Field(
        None,
        description='Whether the current booking requires hard approval or soft approval.\nThis flag should be used only if valid approver is present.\n',
    )
    outOfPolicy: bool | None = Field(
        None, description='If the given booking is out of policy.'
    )
    policyId: str | None = Field(
        None, description='Policy Id for which violation is done.'
    )
    policyVersion: int | None = Field(None, description='Version of policy.')
    tripId: TripId = Field(..., description='Id of the trip.')


class UserOrgId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    organizationAgencyId: OrganizationAgencyId | None = None
    organizationId: OrganizationId
    userId: UserId
    tmcInfo: TmcInfo | None = None
    tmcBasicInfo: TmcBasicInfo | None = None


class UserOrgIdListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userOrgIdList: Sequence[UserOrgId] | None = None


class UserOrgIdWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userOrgId: UserOrgId | None = None


class Variable(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['VARIABLE'] = Field('VARIABLE', examples=['VARIABLE'])
    name: VariableName


class AdditionalInfo(RootModel[Variable | Expression]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Variable | Expression = Field(
        ...,
        description='Additional data need to be sent along with the custom field response.',
        discriminator='type',
        title='AdditionalInfo',
    )


class AdhocUserInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    profileOwner: ProfileOwner
    isSaved: bool | None = Field(
        False,
        description='A boolean flag to show if ad-hoc traveler is visible in search. While updating the user \nif client tries to update this field, it will throw exception.\n',
    )


class AirPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airlinePrefs: Sequence[AirlinePref] | None = None
    alliancePref: AlliancePref | None = None
    farePref: FarePref | None = None
    homeAirport: str | None = Field(None, examples=['NEW YORK'])
    mealPref: MealPref | None = None
    numStopPref: NumStopsPref | None = None
    seatAmenityPref: SeatAmenityPref | None = None
    seatLocationPrefs: Sequence[SeatLocationPref] | None = None
    preferredAirports: Sequence[PreferredAirport] | None = Field(
        None, description='A list of user preferred airports.'
    )


class AutocompleteHotel(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelCode: str | None = Field(
        None, description='Unique Code for the hotel', examples=['*********']
    )
    hotelName: str = Field(
        ..., description='Full Name of the hotel', examples=['Delphin Deluxe Resort']
    )
    location: Location | None = None
    address: PostalAddress | None = None
    isPresentInPreferredVendors: bool | None = Field(
        None,
        description='Whether the hotel is present in preferred vendor list. This is an optional field which gets populated only in the preferred vendor autocomplete API.',
        examples=[True],
    )
    brandCode: str | None = Field(
        None, description='The code of hotel brand.', examples=['MC']
    )
    chainCode: str | None = Field(
        None, description='The code of hotel chain.', examples=['EM']
    )
    starRating: HotelStarRatingInfo | None = Field(
        None, description='Hotel star rating detail.'
    )
    contactInfo: ContactInfo | None = Field(
        None, description='Contact information for the hotel.'
    )


class CancellationPenalty(RootModel[PenaltyAmount | PenaltyPercentage]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: PenaltyAmount | PenaltyPercentage


class CancellationPolicyTerm(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    penalty: CancellationPenalty
    deadline: CancellationDuration


class Card(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID | None = Field(
        None,
        description='Unique identifier for this card',
        examples=['34d536b6-f8ff-11eb-9a61-0242ac180002'],
    )
    type: Type | None = Field(None, description='Type of card', examples=['CREDIT'])
    company: CardCompany | None = None
    name: str | None = Field(
        None, description='Name on card', examples=['Harrison Schwartz']
    )
    address: PostalAddress | None = Field(None, description='Billing address')
    number: str = Field(..., description='Card number', examples=['****************'])
    expiryMonth: conint(ge=1, le=12) | None = Field(
        None, description='Expiry month', examples=[1]
    )
    expiryYear: conint(ge=2000) | None = Field(
        None, description='Expiry year', examples=[2010]
    )
    cvv: str | None = Field(None, description='Card cvv number', examples=['012'])
    label: str | None = Field(None, description='Card Label', examples=['Label amex'])
    currency: str | None = Field(
        None, description='Native currency of the card.', examples=['USD']
    )
    externalId: str | None = Field(
        None,
        description='Spotnana partner card id.',
        examples=['bxt_RNGsNfzgJDaTstKIKqK4xEuhGYAnMdYK8T40'],
    )
    vaultId: UUID | None = Field(
        None,
        description='ID of the vault used for creating the card.',
        examples=['34d536b6-f8ff-11eb-9a61-0242ac180002'],
    )
    expiry: CardExpiry | None = Field(None, description='Card Expiry.')
    ownershipLabel: OwnershipLabel | None = Field(None, examples=['PERSONAL'])


class CardDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    company: CardCompany
    token: str = Field(
        ..., description='Tokenized Card Number', examples=['****************']
    )
    expiry: CardExpiry


class Commission(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Money = Field(..., description='Commission amount')
    commissionPercent: float | None = Field(
        None, description='Commission percentage', examples=[7.5]
    )


class Credit(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['CREDIT'] = Field(
        'CREDIT',
        description='Payment source type. Should be set to UNUSED_CREDIT for unused credit details.',
    )
    pnrOwningPcc: str | None = Field(None, description='PCC the PNR was created on.')
    unusedCreditPcc: str | None = Field(
        None, description='PCC the credit was issued on.'
    )
    departureCountry: str | None = Field(
        None,
        description='3 letter country code of the departure country associated with the original ticket.',
        examples=['USA'],
    )
    arrivalCountry: str | None = Field(
        None,
        description='3 letter country code of the arrival country associated with the original ticket.',
        examples=['USA'],
    )
    ticketType: TicketType = Field(..., description='Type of credit.')
    departureDate: DateTimeOffset | None = Field(
        None,
        description='Date for the departure of the first flight associated with the unused credit.',
    )
    segmentsAvailable: SegmentsAvailable = Field(
        ...,
        description='Whether all segments are unused or some have already been used.',
    )
    traveler: AirRequestTravelerInfo = Field(
        ...,
        description='Information about the traveler for which the credit should be redeemed.',
    )
    passengerName: Name = Field(
        ..., description='Name of the passenger associated with the credit.'
    )
    airlineInfo: AirlineInfo = Field(
        ..., description='Airline info with airline name and code'
    )
    totalFare: Money = Field(
        ..., description='Total airfare associated with the original ticket.'
    )
    issueDate: DateTimeOffset | None = Field(
        None, description='Issue date for the unused credit.'
    )
    expiryDate: DateTimeOffset | None = Field(
        None, description='Expiry date for the unused credit.'
    )
    source: ThirdPartySource | None = Field(
        'SABRE', description='Source of unused credit e.g. Sabre, NDC etc.'
    )
    sourcePnr: str = Field(
        ...,
        description='PNR number corresponding to third party through which booking was made.',
        examples=['MC5ONS'],
    )
    flightIds: Sequence[str] | None = Field(
        None,
        description='ID of the flights on which this credit applies.',
        min_length=1,
    )
    ticketNumber: str = Field(
        ...,
        description='Ticket number for the ticket that was converted into an unused credit.',
        examples=['5267779139217'],
    )


class CustomFieldMatchConditions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerConditions: TravelerMatchConditions | None = None
    travelTypes: Sequence[TravelType] | None = Field(
        None, description='Travel types to match.'
    )
    travelRegionTypes: Sequence[TravelRegionType] | None = Field(
        None, description='Travel region types to match.'
    )
    tripUsageTypes: Sequence[TripUsageType] | None = Field(
        None,
        description='Trip usage types to match. If empty, all trip usage types will be matched.',
    )


class CustomFieldSelectedOption(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Value of the selection')
    description: str | None = Field(None, description='Description of the selection')
    additionalUserInput: str | None = Field(None, description='Additional user input')
    additionalInfos: Sequence[str] | None = Field(
        None, description='Actual values of the additional infos'
    )
    additionalInfoConfigs: Sequence[AdditionalInfo] | None = Field(
        None, description='Additional info configs for the selected option'
    )


class CustomFieldV3Response(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fieldId: UUID = Field(..., description='Custom field id')
    fieldName: str | None = Field(None, description='Name of the custom field')
    armId: UUID = Field(..., description='Arm id which is applicable')
    includeLocations: Sequence[IncludeLocation] | None = None
    selectedOptions: Sequence[CustomFieldSelectedOption] = Field(
        ...,
        description='The list of options that are selected by user or auto populated.',
    )


class Dpan(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Literal['DPAN'] = Field(
        'DPAN',
        description='Payment source type. Should be set to DPAN for card details.',
    )
    cardDetails: CardDetails


class EmergencyContactInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress | None = None
    designation: str | None = Field(None, examples=['MANAGER'])
    email: EmailStr = Field(..., examples=['<EMAIL>'])
    name: Name | None = None
    phoneNumber: PhoneNumber | None = None
    userOrgId: UserOrgId | None = None


class HotelAutocompleteResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotels: Sequence[AutocompleteHotel] = Field(
        ..., description='List of Hotels matching the Autocomplete query'
    )


class HotelCancelPnrRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    customFieldV3Responses: Sequence[CustomFieldV3Response] | None = Field(
        None, description='Custom field responses for the booking.'
    )


class HotelCancellationPolicy(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    refundable: Refundable | None = Field(
        None, description='Is refundable or not', examples=['TRUE']
    )
    terms: Sequence[CancellationPolicyTerm] | None = None


class HotelMedianRateInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    total: Money = Field(..., description='Total Amount.')
    base: Money = Field(..., description='Base Amount.')


class HotelRateStatistics(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    median: HotelMedianRateInfo
    sampleSize: int = Field(
        ...,
        description='Size of the sample set used to calculate the statistics.',
        examples=[100],
    )
    policyType: PolicyType1 = Field(
        ..., description='Enumeration of policy types for rate statistics.'
    )


class HotelRoomRate(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    base: Money = Field(..., description='Base amount')
    tax: Money = Field(..., description='Tax')
    roomFees: Sequence[RoomFee] | None = None
    commission: Commission | None = Field(None, description='Commission')
    taxBreakdown: TaxBreakdown | None = Field(None, description='Tax breakdown')
    includesCommission: bool | None = Field(
        None, description='Whether the rate includes commission', examples=[False]
    )


class HotelSearchMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    availableHotelChains: Sequence[HotelChain] = Field(
        ..., description='A list of available hotel chains.'
    )
    rateStatistics: HotelRateStatistics
    showOnlyBaseFare: bool = Field(
        ...,
        description='Indicates if only base fare should be shown.',
        examples=[False],
    )
    sessionId: str | None = Field(
        None, description='Unique session identifier for the search.'
    )


class HotelSearchRateInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    averageNightlyRate: HotelRoomRate = Field(
        ..., description='Average nightly rate for the hotel.'
    )
    totalRate: HotelRoomRate = Field(..., description='Total rate for the hotel stay.')
    isRefundable: bool = Field(
        ..., description='Set to true if the hotel rate is refundable.', examples=[True]
    )
    rewardPointsEarned: Sequence[RewardPointsEarned] | None = Field(
        None, description='Information about reward points earned.'
    )
    promotionalOffers: Sequence[PromotionalOffer] | None = Field(
        None, description='List of promotional offers applied to the hotel rate.'
    )


class HotelTraveler(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerId: UserId = Field(..., description='The ID of the traveler.')
    name: Name = Field(..., description='Name of the traveler.')
    dob: DateModel | None = Field(None, description='Date of birth of the traveler.')
    phoneNumber: PhoneNumber = Field(..., description='Phone number of the traveler.')
    email: str = Field(
        ..., description='Email of the traveler.', examples=['<EMAIL>']
    )
    loyaltyInfo: LoyaltyInfo | None = Field(
        None, description='Loyalty Info of the traveler.'
    )
    adhocInfo: AdhocInfo | None = Field(
        None,
        description='Adhoc Info of the traveler. It is only present if the traveler is adhoc user.',
    )


class HotelUserId(RootModel[UserIdSearch | RegistrarUserIdSearch]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: UserIdSearch | RegistrarUserIdSearch = Field(
        ...,
        description='An identifier for the user involved in the hotel booking or search process.',
        discriminator='userIdType',
        title='Hotel User Identifier',
    )


class KeywordWithReasonListWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    keywordWithReasonList: KeywordsWithReasonList | None = None


class OptionInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    source: OptionSource
    sourceMetadata: OptionSourceMetadata | None = None
    totalNumOptions: int | None = Field(None, description='Total number of options')
    options: Sequence[Option] | None = Field(
        None,
        description='Available options for the question. This will contain only max 10 options if only \nsummary is requested.\n',
    )


class PaymentInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    applicableTo: Sequence[ApplicableToEnum] | None = None
    card: Card
    accessType: CreditCardAccessType | None = None
    access: CreditCardAccess | None = None


class PolicyConstValue(
    RootModel[
        Int32Wrapper
        | Int64Wrapper
        | StringWrapper
        | DoubleWrapper
        | BoolWrapper
        | IntListWrapper
        | DoubleListWrapper
        | StringListWrapper
        | MoneyWrapper
        | LengthWrapper
        | PostalAddressWrapper
        | UserOrgIdWrapper
        | LegalEntityIdWrapper
        | OfficeIdWrapper
        | UserOrgIdListWrapper
        | LegalEntityIdListWrapper
        | OfficeIdListWrapper
        | RatingWrapper
        | PercentageWrapper
        | Int32RangeWrapper
        | DoubleRangeWrapper
        | PersonaWrapper
        | PersonaListWrapper
        | TravelClassHierarchyWrapper
        | KeywordWithReasonListWrapper
        | WorkerTypeWrapper
        | WorkerTypeListWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        Int32Wrapper
        | Int64Wrapper
        | StringWrapper
        | DoubleWrapper
        | BoolWrapper
        | IntListWrapper
        | DoubleListWrapper
        | StringListWrapper
        | MoneyWrapper
        | LengthWrapper
        | PostalAddressWrapper
        | UserOrgIdWrapper
        | LegalEntityIdWrapper
        | OfficeIdWrapper
        | UserOrgIdListWrapper
        | LegalEntityIdListWrapper
        | OfficeIdListWrapper
        | RatingWrapper
        | PercentageWrapper
        | Int32RangeWrapper
        | DoubleRangeWrapper
        | PersonaWrapper
        | PersonaListWrapper
        | TravelClassHierarchyWrapper
        | KeywordWithReasonListWrapper
        | WorkerTypeWrapper
        | WorkerTypeListWrapper
    )


class PolicyTakeApprovalAction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    numHierarchyLevels: int | None = Field(
        None,
        description="How many levels of hierarchy should approval be taken? If it's just immediate manager, \nthis value would be 1. If it's manager and their manager, this would ge 2 and so on.\n",
    )
    positionTitles: Sequence[str] | None = Field(
        None,
        description='What position in the cost center or department. For eg, any business class upgrade \nmight require VP approval.\n',
    )
    userOrgIds: Sequence[UserOrgId] | None = Field(
        None,
        description='The specific users from whom to take approval. For eg, say for a company, all approvals\ngo through Adam.\n',
    )
    allRequired: bool | None = Field(
        None,
        description='This tells whether all the people above needs to approve or if any of these approve, \nit is sufficient.\n',
    )
    hardApprovalRequired: bool | None = Field(
        None,
        description='Whether to take soft approval (false) or hard approval (true).',
    )


class PolicyTakeApprovalActionWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    takeApproval: PolicyTakeApprovalAction | None = None


class PolicyViolationInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    predicateString: str | None = None
    predicate: PolicyPredicate | None = None
    expectedValue: PolicyConstValue | None = None
    actualValue: PolicyConstValue | None = None


class Question(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID
    name: str = Field(
        ...,
        description="Question display name that the user will see. For eg, 'Choose the purpose of your trip'.",
    )
    questionFormat: QuestionFormat | None = None
    optionInfo: OptionInfo | None = None
    isRequired: bool = Field(
        ...,
        description='Whether its compulsory to answer the question or not.',
        examples=[True],
    )
    isDisabled: bool = Field(
        ...,
        description='Whether the question is disabled or not. If true, this should not be asked.',
        examples=[True],
    )
    includeInItinerary: bool | None = Field(
        False,
        description='Whether to include this question in the itinerary related emails.',
        examples=[True],
    )
    customFieldLocations: Sequence[CustomFieldLocation] | None = None
    matchConditions: CustomFieldMatchConditions | None = None
    questionType: QuestionType | None = None


class RailPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preferredRailStations: Sequence[PreferredRailStation] | None = Field(
        None, description='A list of user preferred rail stations.'
    )
    seatPreference: SeatPref | None = None
    travelClasses: Sequence[RailTravelClass] | None = Field(
        None, description='A list of class of service for rail.'
    )
    coachPreferences: Sequence[CoachPref] | None = Field(
        None, description='A list of coach preference for rail.'
    )
    conditionalRates: Sequence[ConditionalRate] | None = Field(
        None, description='A list of conditional rates for rail.'
    )


class RawPaymentSourceDetails(RootModel[Dpan | Credit]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Dpan | Credit = Field(
        ...,
        description='Raw Details of the Payment Source',
        discriminator='type',
        title='RawPaymentSourceDetails',
    )


class SelectedPaymentSource(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentSourceId: UUID | None = Field(
        None,
        description='Unique identifier identifying this payment source.',
        examples=['f49d00fe-1eda-4304-ba79-a980f565281d'],
    )
    rawPaymentSource: RawPaymentSourceDetails | None = None
    postPaymentRedirectionUrl: str | None = Field(
        None,
        description='Url for post payment redirection if payment source navigates user to a third party url',
        examples=[
            'https://mycompany.com/checkout?paymentSourceId=f49d00fe-1eda-4304-ba79-a980f565281d'
        ],
    )
    cvv: str | None = Field(
        None, description='CVV associated with associated payment source, if any.'
    )
    amount: Money | None = Field(
        None, description='Total amount to be charged for specified payment items.'
    )


class TravelPref(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airPref: AirPref | None = None
    preferredCurrency: str | None = Field(None, examples=['USD'])
    railCards: Sequence[RailCard] | None = None
    railPref: RailPref | None = None
    carPref: CarPref | None = None
    hotelPref: HotelPref | None = None


class TravelerPersonalInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    loyaltyInfos: Sequence[LoyaltyInfo] | None = None
    travelPref: TravelPref | None = None


class User(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    addresses: Sequence[PostalAddress] | None = None
    dob: DateModel | None = None
    email: EmailStr | None = Field(None, examples=['<EMAIL>'])
    emergencyContactInfo: EmergencyContactInfo | None = None
    emergencyContact: EmergencyContact | None = None
    gender: Gender | None = None
    identityDocs: Sequence[IdentityDocument] | None = Field(
        None,
        description='List of user identity documents.',
        examples=[
            [
                {
                    'passport': {
                        'docId': 'PASSPORTID',
                        'expiryDate': {'iso8601': '2017-07-21'},
                        'issueCountry': 'IN',
                        'issuedDate': {'iso8601': '2017-07-21'},
                        'nationalityCountry': 'IN',
                        'type': 'REGULAR',
                    }
                },
                {'ktn': {'number': '123456', 'issueCountry': 'US'}},
            ]
        ],
    )
    name: Name | None = None
    paymentInfos: Sequence[PaymentInfo] | None = None
    phoneNumbers: Sequence[PhoneNumber] | None = None
    profilePicture: Image | None = None
    nationality: str | None = Field(
        None, description='Nationality of user', examples=['Indian']
    )
    title: UserTitle | None = None


class UserBasicInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    userOrgId: UserOrgId | None = None
    persona: Persona | None = None
    email: EmailStr | None = Field(None, examples=['<EMAIL>'])
    name: Name | None = None
    profilePicture: Image | None = None
    tier: Tier | None = 'BASIC'
    phoneNumber: PhoneNumber | None = None
    employeeId: str | None = Field(None, description='Employee id of the user')
    isActive: bool | None = Field(
        None, description='Whether user profile is active or not.', examples=[True]
    )


class UserBusinessInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    department: Department | None = None
    designation: str | None = Field(None, examples=['MANAGER'])
    email: EmailStr = Field(..., examples=['<EMAIL>'])
    employeeId: str = Field(
        ...,
        description="Unique employee id. Can use email if a company don't use employee ids.",
        examples=['101'],
    )
    grade: Grade | None = None
    legalEntityId: LegalEntityId
    managerBasicInfo: UserBasicInfo | None = None
    office: Office | None = None
    organizationId: OrganizationId
    phoneNumbers: Sequence[PhoneNumber] | None = None
    userCostCenter: CostCenter | None = None
    designatedApproverInfos: Sequence[UserBasicInfo] | None = Field(
        None, description='A list of user basic info for designated approvers.'
    )
    designatedApproverUserIds: Sequence[UserId] | None = Field(
        None, description='A list of userId for designated approvers.'
    )
    authorizerEmail: str | None = Field(
        None,
        description='Email address to be used as approval authorizer, when a manager is not present.',
        examples=['<EMAIL>'],
    )


class EntityAnswer(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    entityId: str | None = Field(None, description='The unique ID for the question.')
    userInput: str | None = Field(
        None, description='The text input given by user (if any).'
    )
    itemIds: Sequence[int] | None = Field(
        None,
        description='The id/enum value corresponding to the option chosen by the user as\nanswer.\n',
    )
    answers: Sequence[AnswerPair] | None = None
    customFieldType: CustomFieldType | None = 'QUESTION'
    questionDisplayText: str | None = Field(
        None, description='The question text to be displayed to the user.'
    )
    question: Question | None = None


class HotelPriceCheckResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingKey: str = Field(
        ..., description='The unique key used to make the hotel booking.'
    )
    timeout: int = Field(..., description='Timeout for the booking key in seconds.')
    priceChange: bool = Field(..., description='Indicates if the price has changed.')
    price: HotelRoomRate = Field(..., description='Final price for the booking.')


class HotelRateInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    rateSource: RateSource = Field(..., description='The source of the rate.')
    totalRate: HotelRoomRate = Field(..., description='The total rate for the room.')
    rateType: HotelRateType = Field(..., description='The type of rate.')
    nightlyRate: Sequence[HotelRoomRate] = Field(
        ..., description='The nightly rate for the room.'
    )
    ratePlanName: str = Field(..., description='The name of the rate plan.')
    isCvvRequired: bool = Field(
        ..., description='Indicates if CVV is required for payment.'
    )
    averageNightlyRate: HotelRoomRate = Field(
        ..., description='The average nightly rate for the room.'
    )
    refundAmount: Money | None = Field(
        None, description='The amount refundable for the room.'
    )
    rateSupplier: ThirdPartySource = Field(..., description='The supplier of the rate.')
    publishedRate: HotelRoomRate | None = Field(
        None, description='The published rate for the room.'
    )
    publishedNightlyRate: HotelRoomRate | None = Field(
        None, description='The published nightly rate for the room.'
    )
    rateDifference: HotelRoomRate | None = Field(
        None,
        description='The difference between the published rate and the actual rate.',
    )
    prepaidRate: HotelRoomRate = Field(
        ..., description='The prepaid rate for the room.'
    )
    postpaidRate: HotelRoomRate = Field(
        ..., description='The postpaid rate for the room.'
    )
    penaltyAmount: Money | None = Field(
        None, description='The penalty amount for the rate.'
    )
    rateTag: str | None = Field(None, description='A tag associated with the rate.')


class HotelSearchRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    searchParams: HotelSearchParams
    filters: HotelSearchFilters | None = None
    sortOptions: Sequence[HotelSortOptions] | None = Field(
        None, description='Sort options for the search results.'
    )
    userId: HotelUserId = Field(..., description='Hotel User Identifier.')
    paginationParams: TokenBasedPaginationRequest | None = None


class PolicyAction(
    RootModel[
        PolicyFlagActionWrapper
        | PolicyHideActionWrapper
        | PolicyAlertOnSelectionActionWrapper
        | PolicyTakeApprovalActionWrapper
        | PolicyPreventBookingActionWrapper
    ]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: (
        PolicyFlagActionWrapper
        | PolicyHideActionWrapper
        | PolicyAlertOnSelectionActionWrapper
        | PolicyTakeApprovalActionWrapper
        | PolicyPreventBookingActionWrapper
    ) = Field(..., description='Action that is required / done for policy.')


class PolicyRuleResultInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    violationInfos: Sequence[PolicyViolationInfo] | None = None
    subViolationInfos: Sequence[PolicyViolationInfo] | None = Field(
        None,
        description='In case of complex rules this will contain extra information as to how the rule was \ncalculated.\n',
    )
    actions: Sequence[PolicyAction] | None = Field(
        None,
        description='Followed actions if rule was satisfied else violated actions.',
    )


class PreBookAnswers(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    answers: Sequence[EntityAnswer] | None = None
    preBookQuestionResponseId: str | None = Field(
        None,
        description='The unique id sent back in the pre book questions API response',
    )


class PreSearchAnswers(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    answers: Sequence[EntityAnswer] | None = None
    userEntitiesResponseId: str | None = None


class SelectedFormOfPayment(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    paymentItems: Sequence[PaymentItem]
    selectedPaymentSources: Sequence[SelectedPaymentSource]


class Traveler(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    travelerPersonalInfo: TravelerPersonalInfo | None = None
    user: User | None = None
    userBusinessInfo: UserBusinessInfo | None = None
    userOrgId: UserOrgId | None = None
    persona: Persona | None = None
    isActive: bool | None = Field(
        None,
        description='A boolean flag to show if traveler is active.',
        examples=[True],
    )
    tier: Tier | None = 'BASIC'
    adhocUserInfo: AdhocUserInfo | None = None
    externalId: str | None = Field(None, description='External id of this user.')


class BookingTravelerPaymentDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    selectedFormOfPayments: Sequence[SelectedFormOfPayment]


class CommonPolicyInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: EntityId = Field(..., description='Unique identifier for the policy.')
    policyType: PolicyType = Field(..., description='Type of policy.')
    policyName: str = Field(..., description='Name of the policy.')
    ruleResultInfos: Sequence[PolicyRuleResultInfo] | None = Field(
        None, description='Information about the policy rules.'
    )
    version: int = Field(..., description='Version of the policy.')
    approvalType: ApprovalType = Field(
        ..., description='Type of approval for the policy.'
    )


class CorporateInfoV2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    preSearchAnswers: PreSearchAnswers


class HotelDetailsRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelDetailsKey: str = Field(
        ..., description='A unique identifier for the hotel details.'
    )
    corporateInfo: CorporateInfoV2 | None = Field(
        None, description='Corporate information related to the hotel booking.'
    )
    occupancyDates: OccupancyDates | None = Field(
        None, description='Occupancy values and dates for the hotel details.'
    )
    filters: HotelDetailsFilters | None = Field(
        None, description='Filters to apply when fetching hotel details.'
    )
    userId: HotelUserId | None = Field(None, description='Hotel User Identifier.')


class HotelModifyDetailsRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    occupancyDates: OccupancyDates = Field(
        ..., description='Occupancy values and dates for the hotel details.'
    )
    corporateInfo: CorporateInfoV2 | None = Field(
        None, description='Corporate information related to the hotel booking.'
    )


class HotelSearchData(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelDetailsKey: str = Field(
        ...,
        description='Unique key for the hotel details.',
        examples=['Cg5TUE9UTkFOQTozMDc3NhIgCgIIARIMCgoyMDI0LTA='],
    )
    hotelSpec: HotelSpec = Field(
        ..., description='Specifications and details of the hotel.'
    )
    distance: Length = Field(..., description='Distance from the search point.')
    policyInfo: CommonPolicyInfo | None = Field(
        None, description='Policy information related to the hotel.'
    )
    hasNegotiatedRates: bool = Field(
        ...,
        description='Indicates if the hotel has negotiated rates.',
        examples=[False],
    )
    preferences: Sequence[Preference] | None = Field(
        None, description='List of preferences applicable to the hotel.'
    )
    rateInfo: HotelSearchRateInfo = Field(
        ...,
        description='Rate related information for the cheapest rate available in the hotel.',
    )
    hotelCo2EmissionDetail: HotelCo2EmissionDetail | None = Field(
        None, description='The co2 emission details for the hotel.'
    )


class HotelSearchResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotels: Sequence[HotelSearchData] = Field(
        ..., description='List of hotels matching the search criteria.'
    )
    metadata: HotelSearchMetadata
    paginationParams: TokenBasedPaginationResponse


class HotelValidateRebookingRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    cancelSourcePnrId: str = Field(
        ...,
        description='Source PNR ID that needs to be validated.',
        examples=['1cf76aba18e4015f'],
    )
    travelers: Sequence[Traveler] = Field(..., description='List of travelers')
    tripId: TripId = Field(
        ..., description='The unique ID created for the respective trip.'
    )


class RateOption(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bedInfos: Sequence[BedInfo] = Field(
        ..., description='Information about the beds in the room.'
    )
    priceValidateKey: str = Field(..., description='A key used to validate the price.')
    description: str = Field(..., description='A description of the rate option.')
    rateInfo: HotelRateInfo = Field(..., description='Information about the rate.')
    meals: HotelRoomMeals | None = Field(
        None, description='Information about meals included with the rate.'
    )
    amenities: Sequence[HotelRoomAmenity] = Field(
        ..., description='A list of amenities included with the rate.'
    )
    cancellationPolicy: HotelCancellationPolicy = Field(
        ..., description='The cancellation policy for the rate.'
    )
    policyInfo: CommonPolicyInfo = Field(
        ..., description='Information about applicable policies.'
    )
    guaranteeType: GuaranteeType = Field(
        ..., description='The type of guarantee required for the rate.'
    )
    displayName: str = Field(..., description='The display name of the rate option.')
    roomInfo: HotelRoomInfo = Field(..., description='Information about the room.')
    rateGroupKey: str = Field(..., description='A key used to group similar rates.')
    additionalAmenities: Sequence[str] | None = Field(
        None, description='A list of additional amenities included with the rate.'
    )
    isPrepaidRoom: bool = Field(..., description='Indicates if the room is prepaid.')
    supportedCardTypes: Sequence[CardType] | None = Field(
        None, description='A list of card types supported for payment.'
    )
    numRoomsRequired: int = Field(
        ..., description='The number of rooms required for this rate option.'
    )
    maxOccupancy: Occupancy = Field(
        ..., description='The maximum occupancy for the room.'
    )
    isModifiable: bool = Field(..., description='Indicates if the rate is modifiable.')
    additionalDetails: Sequence[HotelAdditionalDetail] | None = Field(
        None, description='A list of additional details about the rate option.'
    )
    earnLoyaltyPoints: bool = Field(
        ..., description='Indicates if loyalty points can be earned with this rate.'
    )
    rewardPointsEarned: Sequence[RewardPointsEarned] = Field(
        ..., description='A list of reward points earned with this rate.'
    )
    promotionalOffers: Sequence[PromotionalOffer] | None = Field(
        None, description='A list of promotional offers applicable to the rate.'
    )
    paymentDescription: Sequence[str] | None = Field(
        None, description='A list of descriptions related to payment for the rate.'
    )
    isFopModifiable: bool = Field(
        ..., description='Indicates if the form of payment is modifiable.'
    )
    accessibilityInfo: Sequence[Accessibility] | None = Field(
        None, description='A list of accessibility features for the rate.'
    )


class BookingPaymentDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingTravelerPaymentDetails: Sequence[BookingTravelerPaymentDetails]


class HotelCreatePnrRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingKey: str = Field(
        ...,
        description='The booking key for the hotel booking.',
        examples=['example_booking_key'],
    )
    travelers: Sequence[HotelTraveler] = Field(
        ...,
        description='The list of travelers and their details. \nThe traveler at index 0 will be considered as the primary traveler \nand their details will be passed to the supplier.\n',
        title='Travelers',
    )
    tripData: TripData
    preBookAnswers: PreBookAnswers | None = None
    bookingPaymentDetails: BookingPaymentDetails
    bookingContact: BookingContact
    hotelSpecialRequests: HotelSpecialRequests | None = None
    cancelSourcePnrId: str | None = Field(
        None,
        description='The source PNR ID within the booking source that needs to be canceled in favor of the new booking that is being created.',
        examples=['ABC123'],
    )
    hotelRateAssuranceInfo: HotelRateAssuranceMetadata | None = None
    customFieldV3Responses: Sequence[CustomFieldV3Response] | None = Field(
        None, description='Custom field responses for the booking.'
    )


class HotelModifyBookingRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingKey: str = Field(
        ...,
        description='The booking key for the hotel booking',
        examples=['example_booking_key'],
    )
    travelers: Sequence[HotelTraveler] = Field(
        ...,
        description='The list of travelers, the first traveler is considered as primary traveler',
        title='Travelers',
    )
    tripData: TripData
    preBookAnswers: PreBookAnswers | None = None
    hotelSpecialRequests: HotelSpecialRequests | None = None
    bookingPaymentDetails: BookingPaymentDetails | None = None
    bookingContact: BookingContact
    customFieldV3Responses: Sequence[CustomFieldV3Response] | None = Field(
        None, description='Custom field responses for the booking.'
    )


class HotelRoomData(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    description: str = Field(
        ..., description='A detailed description of the hotel room.'
    )
    rateOptions: Sequence[RateOption] = Field(
        ..., description='A list of rate options available for the hotel room.'
    )
    imageSets: Sequence[HotelImageSet] | None = Field(
        None, description='A list of image sets for the hotel room.'
    )
    roomGroupKey: str | None = Field(
        None, description='A unique key identifying the room group.'
    )
    penaltyAmount: Money | None = Field(
        None, description='The amount of penalty applied for this room.'
    )


class HotelDetailsResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    hotelSpec: HotelSpec = Field(..., description='Specification of the hotel.')
    rooms: Sequence[HotelRoomData] = Field(
        ..., description='List of available rooms in the hotel.'
    )
    bookedRooms: Sequence[HotelRoomData] | None = Field(
        None,
        description='List of booked rooms in the hotel, present only as part of modification details response.',
    )
    occupancyDates: OccupancyDates = Field(
        ..., description='Occupancy values and dates for the hotel details.'
    )
    rateStatistics: HotelRateStatistics | None = Field(
        None, description='Statistical information about the hotel rates.'
    )
    showOnlyBaseFare: bool | None = Field(
        None, description='Flag to indicate if only the base fare should be shown.'
    )
    preferences: Sequence[Preference] | None = Field(
        None, description='List of user preferences.'
    )
    termsAndConditions: Sequence[TermsAndConditions] | None = Field(
        None, description='List of terms and conditions applicable to the booking.'
    )
