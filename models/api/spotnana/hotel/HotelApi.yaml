openapi: 3.1.0
info:
  title: Hotel API
  version: v2
servers:
  - url: https://apis.spotnana.com
    description: Staging URL
security:
  - Bearer: []
components:
  securitySchemes:
    Bearer:
      type: http
      scheme: bearer
  responses:
    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFound:
      description: The specified resource was not found.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
  schemas:
    Accessibility:
      type: object
      title: Accessibility
      description: Information about the accessibility of the hotel room.
      required:
        - type
        - isSelectionRequired
      properties:
        type:
          type: string
          description: The type of accessibility feature.
          enum:
            - ACCESSIBLE_ROOM
        additionalInfo:
          type: string
          description: Additional information about the accessibility feature.
        isSelectionRequired:
          type: boolean
          description: >-
            Indicates if the selection of this accessibility feature is
            required.
    AdditionalInfo:
      type: object
      title: AdditionalInfo
      description: Additional data need to be sent along with the custom field response.
      discriminator:
        propertyName: type
        mapping:
          VARIABLE: '#/components/schemas/Variable'
          EXPRESSION: '#/components/schemas/Expression'
      oneOf:
        - $ref: '#/components/schemas/Variable'
        - $ref: '#/components/schemas/Expression'
    AdhocTravelerInfo:
      title: AdhocTravelerInfo
      type: object
      description: Describes an adhoc traveler.
      properties:
        registrarUserId:
          description: The registrar of the adhoc traveler.
          $ref: '#/components/schemas/UserId'
        externalId:
          type: string
          description: External Id of user
          example: qwert123
    AdhocTravelerInfoWrapper:
      title: AdhocTravelerInfoWrapper
      description: >-
        Wrapper over AdhocTravelerInfo object to be used as an identifier in a
        oneOf case.
      properties:
        adhocTravelerInfo:
          $ref: '#/components/schemas/AdhocTravelerInfo'
    AdhocUserInfo:
      type: object
      title: AdhocUserInfo
      description: Basic information related to ad-hoc traveler profile.
      required:
        - profileOwner
      properties:
        profileOwner:
          $ref: '#/components/schemas/ProfileOwner'
        isSaved:
          type: boolean
          description: >
            A boolean flag to show if ad-hoc traveler is visible in search.
            While updating the user 

            if client tries to update this field, it will throw exception.
          default: false
    AirPref:
      type: object
      title: AirPref
      description: Travel preferences related to air travel.
      properties:
        airlinePrefs:
          type: array
          items:
            $ref: '#/components/schemas/AirlinePref'
        alliancePref:
          $ref: '#/components/schemas/AlliancePref'
        farePref:
          $ref: '#/components/schemas/FarePref'
        homeAirport:
          type: string
          example: NEW YORK
        mealPref:
          $ref: '#/components/schemas/MealPref'
        numStopPref:
          $ref: '#/components/schemas/NumStopsPref'
        seatAmenityPref:
          $ref: '#/components/schemas/SeatAmenityPref'
        seatLocationPrefs:
          type: array
          items:
            $ref: '#/components/schemas/SeatLocationPref'
        preferredAirports:
          type: array
          description: A list of user preferred airports.
          items:
            $ref: '#/components/schemas/PreferredAirport'
    AirRequestTravelerInfo:
      title: AirRequestTravelerInfo
      description: >
        The traveler identifiers. These can be either the Spotnana user IDs of
        the travelers or information regarding

        the adhoc travelers.
      type: object
      oneOf:
        - $ref: '#/components/schemas/UserIdWrapper'
        - $ref: '#/components/schemas/AdhocTravelerInfoWrapper'
    AirlineInfo:
      title: AirlineInfo
      type: object
      required:
        - airlineCode
        - airlineName
      properties:
        airlineCode:
          type: string
          description: IATA code for airline.
          example: AA
        airlineName:
          type: string
          description: Airline name
          example: American Airlines
    AirlinePref:
      type: object
      title: AirlinePref
      description: Airline preferences.
      properties:
        airlines:
          type: array
          items:
            type: string
            example: Indigo
        flightType:
          type: string
          enum:
            - UNKNOWN_FLIGHT_TYPE
            - DOMESTIC
            - INTERNATIONAL
            - ALL
          example: DOMESTIC
    AirportSearch:
      type: object
      title: Airport Search
      required:
        - searchType
        - airport
      properties:
        searchType:
          type: string
          example: AIRPORT
        airport:
          type: string
          example: LHR
          description: >-
            IATA code of the airport. The search result will contain the list of
            hotels in close proximity to the specified airport.
    Alliance:
      type: string
      enum:
        - UNKNOWN_ALLIANCE
        - STAR_ALLIANCE
        - ONEWORLD
        - SKYTEAM
        - VANILLA_ALLIANCE
        - U_FLY_ALLIANCE
        - VALUE_ALLIANCE
      default: UNKNOWN_ALLIANCE
      example: STAR_ALLIANCE
    AlliancePref:
      type: object
      title: AlliancePref
      description: Alliance preferences.
      required:
        - alliances
      properties:
        alliances:
          type: array
          items:
            $ref: '#/components/schemas/Alliance'
    AnswerPair:
      type: object
      properties:
        item:
          type: string
          description: The option selected from the list of available choices.
        value:
          type: string
          description: >-
            The additional input provided (by the user) while selecting one of
            the options.
        description:
          type: string
          description: Description of the selected option.
    ApprovalType:
      type: string
      description: Type of APPROVAL
      enum:
        - HARD_APPROVAL
        - SOFT_APPROVAL
        - PASSIVE_APPROVAL
      example: SOFT_APPROVAL
    AutocompleteHotel:
      title: AutocompleteHotel
      description: Hotel Details
      type: object
      required:
        - hotelName
      properties:
        hotelCode:
          type: string
          description: Unique Code for the hotel
          example: '100259788'
        hotelName:
          type: string
          description: Full Name of the hotel
          example: Delphin Deluxe Resort
        location:
          $ref: '#/components/schemas/Location'
        address:
          $ref: '#/components/schemas/PostalAddress'
        isPresentInPreferredVendors:
          type: boolean
          description: >-
            Whether the hotel is present in preferred vendor list. This is an
            optional field which gets populated only in the preferred vendor
            autocomplete API.
          example: true
        brandCode:
          type: string
          example: MC
          description: The code of hotel brand.
        chainCode:
          type: string
          example: EM
          description: The code of hotel chain.
        starRating:
          $ref: '#/components/schemas/HotelStarRatingInfo'
          description: Hotel star rating detail.
        contactInfo:
          $ref: '#/components/schemas/ContactInfo'
          description: Contact information for the hotel.
    BedInfo:
      type: object
      title: BedInfo
      description: Bed count and type
      properties:
        bedCount:
          type: integer
          description: Number of beds.
          format: int32
          example: 1
        bedType:
          $ref: '#/components/schemas/BedType'
          description: Bed type.
    BedType:
      type: string
      title: BedType
      description: Bed types.
      enum:
        - UNKNOWN_BED_TYPE
        - DOUBLE
        - FUTON
        - KING
        - MURPHY
        - QUEEN
        - SOFA
        - TATAMI_MATS
        - TWIN
        - SINGLE
        - FULL
        - RUN_OF_THE_HOUSE
        - DORM
        - WATER
        - PULL_OUT
        - TWIN_XL
      example: SINGLE
    BookingContact:
      type: object
      properties:
        emailAddress:
          type: string
          description: Email address of the booking contact
          example: <EMAIL>
        phoneNumber:
          $ref: '#/components/schemas/PhoneNumber'
    BookingPaymentDetails:
      type: object
      description: Payment details used for booking.
      required:
        - bookingTravelerPaymentDetails
      properties:
        bookingTravelerPaymentDetails:
          type: array
          items:
            $ref: '#/components/schemas/BookingTravelerPaymentDetails'
    BookingTravelerPaymentDetails:
      type: object
      description: >-
        List of different forms of payments used for booking for a set of
        travelers.
      required:
        - selectedFormOfPayments
      properties:
        selectedFormOfPayments:
          type: array
          items:
            $ref: '#/components/schemas/SelectedFormOfPayment'
    BoolWrapper:
      type: object
      title: BoolWrapper
      properties:
        b:
          type: boolean
    CancellationDuration:
      type: object
      properties:
        deadlineAbsolute:
          $ref: '#/components/schemas/DateTimeLocal'
        deadlineDurationBeforeArrival:
          $ref: '#/components/schemas/Duration'
    CancellationPenalty:
      type: object
      oneOf:
        - $ref: '#/components/schemas/PenaltyAmount'
        - $ref: '#/components/schemas/PenaltyPercentage'
    CancellationPolicyTerm:
      type: object
      title: CancellationPolicyTerm
      description: Conditions and restrictions on Cancellations.
      required:
        - penalty
        - deadline
      properties:
        penalty:
          $ref: '#/components/schemas/CancellationPenalty'
        deadline:
          $ref: '#/components/schemas/CancellationDuration'
    CarPref:
      type: object
      title: CarPref
      description: Travel preferences related to car.
      properties:
        vendors:
          type: array
          description: A list of car vendors.
          items:
            $ref: '#/components/schemas/CarVendor'
        carTypes:
          type: array
          description: A list of types of car.
          items:
            $ref: '#/components/schemas/CarType'
        engineTypes:
          type: array
          description: A list of types of engine.
          items:
            $ref: '#/components/schemas/EngineType'
        transmissionTypes:
          type: array
          description: A list of types of transmission.
          items:
            $ref: '#/components/schemas/TransmissionSearchFilter'
        conditionalRates:
          type: array
          description: A list of conditional rates for rail.
          items:
            $ref: '#/components/schemas/ConditionalRate'
    CarType:
      type: string
      title: CarType
      description: Car type.
      enum:
        - OTHER
        - MINI
        - ECONOMY
        - COMPACT
        - MID_SIZE
        - STANDARD
        - FULL_SIZE
        - PREMIUM
        - LUXURY
        - CONVERTIBLE
        - MINIVAN
        - SUV
        - VAN
        - PICKUP
        - SPORTS
        - SPECIAL
        - RECREATIONAL_VEHICLE
        - WAGON
      example: ECONOMY
    CarVendor:
      type: object
      title: CarVendor
      description: Car vendor.
      required:
        - code
        - name
      properties:
        code:
          type: string
          description: Vendor code
          example: ZE
        name:
          type: string
          description: Vendor name
          example: HERTZ
        isPresentInPreferredVendors:
          type: boolean
          description: >-
            Whether the car vendor is present in preferred vendor list. This is
            an optional field which gets populated only in the preferred vendor
            autocomplete API.
          example: true
    Card:
      type: object
      title: Card
      description: User card info
      required:
        - number
      properties:
        id:
          type: string
          description: Unique identifier for this card
          format: uuid
          example: 34d536b6-f8ff-11eb-9a61-0242ac180002
        type:
          type: string
          enum:
            - UNKNOWN
            - CREDIT
            - DEBIT
          description: Type of card
          example: CREDIT
        company:
          $ref: '#/components/schemas/CardCompany'
        name:
          description: Name on card
          type: string
          example: Harrison Schwartz
        address:
          description: Billing address
          $ref: '#/components/schemas/PostalAddress'
        number:
          type: string
          description: Card number
          example: '****************'
        expiryMonth:
          type: integer
          format: int32
          minimum: 1
          maximum: 12
          description: Expiry month
          example: 1
          deprecated: true
        expiryYear:
          type: integer
          format: int32
          description: Expiry year
          minimum: 2000
          example: 2010
          deprecated: true
        cvv:
          type: string
          description: Card cvv number
          example: '012'
        label:
          type: string
          description: Card Label
          example: Label amex
        currency:
          type: string
          description: Native currency of the card.
          example: USD
        externalId:
          type: string
          description: Spotnana partner card id.
          example: bxt_RNGsNfzgJDaTstKIKqK4xEuhGYAnMdYK8T40
        vaultId:
          type: string
          description: ID of the vault used for creating the card.
          format: uuid
          example: 34d536b6-f8ff-11eb-9a61-0242ac180002
        expiry:
          description: Card Expiry.
          $ref: '#/components/schemas/CardExpiry'
        ownershipLabel:
          $ref: '#/components/schemas/OwnershipLabel'
          example: PERSONAL
    CardAccessEntity:
      title: CardAccessEntity
      type: object
      description: Contains the central access level and entity id.
      required:
        - entityId
      properties:
        entityId:
          type: string
          description: >-
            Holds the id for for the user who can access the card or
            organization id or legal entity
        centralCardAccessLevel:
          $ref: '#/components/schemas/CentralCardAccessLevel'
    CardCompany:
      type: string
      enum:
        - NONE
        - VISA
        - MASTERCARD
        - AMEX
        - DISCOVER
        - AIR_TRAVEL_UATP
        - CARTE_BLANCHE
        - DINERS_CLUB
        - JCB
        - BREX
        - UNION_PAY
        - EURO_CARD
        - ACCESS_CARD
        - ELO_CARD
      description: The issuing company of card
      example: VISA
    CardDetails:
      type: object
      title: Card Details
      description: Card Details
      required:
        - token
        - expiry
        - company
      properties:
        company:
          $ref: '#/components/schemas/CardCompany'
        token:
          type: string
          description: Tokenized Card Number
          example: '****************'
        expiry:
          $ref: '#/components/schemas/CardExpiry'
    CardExpiry:
      title: CardExpiry
      type: object
      description: Contains the expiry of a Card.
      oneOf:
        - $ref: '#/components/schemas/TokenizedExpiryWrapper'
        - $ref: '#/components/schemas/ExpiryWrapper'
    CardType:
      type: object
      title: Card Type
      description: Information about the type of card accepted.
      required:
        - cardCompany
      properties:
        cardCompany:
          $ref: '#/components/schemas/CardCompany'
          description: The company that issued the card.
        description:
          type: string
          description: A description of the card type.
    CentralCardAccessLevel:
      title: CentralCardAccessLevel
      type: string
      description: >-
        Identifies at which level central card is applicable, for example
        organization or legal entity.
      enum:
        - UNKNOWN
        - ORGANIZATION
        - LEGAL_ENTITY
        - TMC
    CoachPref:
      type: string
      description: The preference of coach.
      enum:
        - SMOKING
        - NON_SMOKING
        - PETS_ALLOWED
        - RESTAURANT
        - QUIET
      example: NON_SMOKING
    Commission:
      type: object
      title: Commission
      description: Commision on rate
      required:
        - amount
      properties:
        amount:
          $ref: '#/components/schemas/Money'
          description: Commission amount
        commissionPercent:
          type: number
          format: double
          description: Commission percentage
          example: 7.5
    CommonPolicyInfo:
      type: object
      title: Policy Information
      description: >
        Includes detailed information about a policy, such as its unique
        identifier, type, name, rules, version, and approval type.
      required:
        - id
        - policyType
        - policyName
        - version
        - approvalType
      properties:
        id:
          $ref: '#/components/schemas/EntityId'
          description: Unique identifier for the policy.
        policyType:
          $ref: '#/components/schemas/PolicyType'
          description: Type of policy.
        policyName:
          type: string
          description: Name of the policy.
        ruleResultInfos:
          type: array
          description: Information about the policy rules.
          items:
            $ref: '#/components/schemas/PolicyRuleResultInfo'
        version:
          type: integer
          description: Version of the policy.
        approvalType:
          $ref: '#/components/schemas/ApprovalType'
          description: Type of approval for the policy.
    CompanyConfigSource:
      type: object
      title: CompanyConfigSource
      description: >-
        For this option source, options would be auto generated based on
        specified parameter.
      required:
        - optionsParam
      properties:
        optionsParam:
          $ref: '#/components/schemas/CustomFieldOptionsParam'
    CompanyConfigSourceWrapper:
      type: object
      title: CompanyConfigSourceWrapper
      description: Wrapper for option source company config.
      properties:
        companyConfig:
          $ref: '#/components/schemas/CompanyConfigSource'
    CompanyId:
      type: object
      title: CompanyId
      description: Company ID
      required:
        - id
      properties:
        id:
          type: string
          format: uuid
          example: f49d00fe-1eda-4304-ba79-a980f565281d
    CompanyRef:
      type: object
      description: Basic information about a company.
      required:
        - id
      properties:
        id:
          $ref: '#/components/schemas/CompanyId'
        name:
          type: string
        logo:
          $ref: '#/components/schemas/Image'
          description: Company logo
    Condition:
      type: object
      title: Condition
      description: Details of the condition
      required:
        - url
        - text
      properties:
        url:
          type: string
          description: Url for terms and conditions.
          example: https://www.amtrak.com/terms-and-conditions.html
        text:
          type: string
          description: Display text for the url.
          example: Amtrak
    ConditionalRate:
      type: string
      title: ConditionalRate
      description: Conditional rate.
      enum:
        - MILITARY
        - AAA
        - GOVERNMENT
      example: AAA
    ContactInfo:
      type: object
      title: Contact Information
      description: Provides contact information for a hotel.
      required:
        - phone
        - email
      properties:
        phone:
          type: array
          description: List of phone numbers for the hotel.
          items:
            $ref: '#/components/schemas/PhoneNumber'
        fax:
          type: array
          description: List of fax numbers for the hotel.
          items:
            type: string
        email:
          type: array
          description: List of email addresses for the hotel.
          items:
            type: string
    CoordinatesSearch:
      type: object
      title: Coordinates Search
      required:
        - searchType
        - coordinates
      properties:
        searchType:
          type: string
          example: COORDINATES
        coordinates:
          $ref: '#/components/schemas/Latlng'
          description: Search for hotels near the given coordinates.
    CorporateInfoV2:
      type: object
      description: Corporate data to be collected during the search request workflow.
      title: CorporateInfoV2
      required:
        - preSearchAnswers
      properties:
        preSearchAnswers:
          $ref: '#/components/schemas/PreSearchAnswers'
    CostCenter:
      type: object
      title: CostCenter
      description: Cost center details.
      required:
        - id
        - name
      properties:
        id:
          $ref: '#/components/schemas/CostCenterId'
        name:
          type: string
          example: CostCenter
        externalId:
          type: string
          example: external-id
    CostCenterId:
      type: object
      title: Cost center id.
      required:
        - id
      properties:
        id:
          type: string
          example: 731ccbca-0415-6fe1-d235-c324dfbe7423
    Credit:
      type: object
      title: Credit
      required:
        - ticketNumber
        - ticketType
        - sourcePnr
        - totalFare
        - passengerName
        - segmentsAvailable
        - type
        - airlineInfo
        - traveler
      properties:
        type:
          type: string
          description: >-
            Payment source type. Should be set to UNUSED_CREDIT for unused
            credit details.
        pnrOwningPcc:
          type: string
          description: PCC the PNR was created on.
        unusedCreditPcc:
          type: string
          description: PCC the credit was issued on.
        departureCountry:
          type: string
          description: >-
            3 letter country code of the departure country associated with the
            original ticket.
          example: USA
        arrivalCountry:
          type: string
          description: >-
            3 letter country code of the arrival country associated with the
            original ticket.
          example: USA
        ticketType:
          type: string
          description: Type of credit.
          enum:
            - TICKET_TYPE_UNKNOWN
            - ETICKET
            - MCO
        departureDate:
          description: >-
            Date for the departure of the first flight associated with the
            unused credit.
          $ref: '#/components/schemas/DateTimeOffset'
        segmentsAvailable:
          type: string
          description: Whether all segments are unused or some have already been used.
          enum:
            - UNKNOWN
            - ALL_OPEN
            - PARTIAL
            - OTHER
        traveler:
          description: >-
            Information about the traveler for which the credit should be
            redeemed.
          $ref: '#/components/schemas/AirRequestTravelerInfo'
        passengerName:
          description: Name of the passenger associated with the credit.
          $ref: '#/components/schemas/Name'
        airlineInfo:
          description: Airline info with airline name and code
          $ref: '#/components/schemas/AirlineInfo'
        totalFare:
          description: Total airfare associated with the original ticket.
          $ref: '#/components/schemas/Money'
        issueDate:
          description: Issue date for the unused credit.
          $ref: '#/components/schemas/DateTimeOffset'
        expiryDate:
          description: Expiry date for the unused credit.
          $ref: '#/components/schemas/DateTimeOffset'
        source:
          $ref: '#/components/schemas/ThirdPartySource'
          default: SABRE
          description: Source of unused credit e.g. Sabre, NDC etc.
        sourcePnr:
          type: string
          description: >-
            PNR number corresponding to third party through which booking was
            made.
          example: MC5ONS
        flightIds:
          type: array
          description: ID of the flights on which this credit applies.
          minItems: 1
          items:
            type: string
        ticketNumber:
          type: string
          description: >-
            Ticket number for the ticket that was converted into an unused
            credit.
          example: '5267779139217'
    CreditCardAccess:
      type: object
      title: CreditCardAccess
      description: >-
        Access type for cards to check who in the organization has permission to
        use it
      required:
        - accessType
        - entityIds
      properties:
        accessType:
          $ref: '#/components/schemas/CreditCardAccessType'
        entityIds:
          type: array
          items:
            type: string
          description: >-
            Holds the ids for for all users who can access the card or
            organization id
        entities:
          type: array
          description: >-
            A list of cardAccessEntity consisting of central card access level
            if present and entity id.
          items:
            $ref: '#/components/schemas/CardAccessEntity'
    CreditCardAccessType:
      title: CreditCardAccessType
      type: string
      description: >-
        Identifies the different ways the card can be used across an
        organization
      enum:
        - UNKNOWN_TYPE
        - CENTRALISED
        - INDIVIDUAL
        - PERSONAL
        - TMC
        - APPLICATION
        - ITINERARY
        - EVENTS
        - TRAVEL_ARRANGER_MANAGED
        - COMPANY_TRAVEL_ARRANGER_MANAGED
        - EVENT_TEMPLATE
      x-ignoreBreakingChanges:
        - CreditCardAccessType->EVENT_TEMPLATE
    CustomFieldLocation:
      title: CustomFieldLocation
      description: Display the custom fields in all these locations.
      type: string
      enum:
        - POLICY_APPROVAL_EMAIL
        - PNR_EMAIL
        - TRIP_EMAIL
      example: POLICY_APPROVAL_EMAIL
    CustomFieldMatchConditions:
      type: object
      title: CustomFieldMatchConditions
      description: Conditions to select the custom field for given context.
      properties:
        travelerConditions:
          $ref: '#/components/schemas/TravelerMatchConditions'
        travelTypes:
          type: array
          description: Travel types to match.
          items:
            $ref: '#/components/schemas/TravelType'
        travelRegionTypes:
          type: array
          description: Travel region types to match.
          items:
            $ref: '#/components/schemas/TravelRegionType'
        tripUsageTypes:
          type: array
          description: >-
            Trip usage types to match. If empty, all trip usage types will be
            matched.
          items:
            $ref: '#/components/schemas/TripUsageType'
    CustomFieldOptionsParam:
      type: string
      title: CustomFieldOptionsParam
      enum:
        - COST_CENTER
        - LEGAL_ENTITY
        - OFFICE
        - DEPARTMENT
      description: Parameter to form options for the custom field.
      example: COST_CENTER
    CustomFieldSelectedOption:
      type: object
      title: CustomFieldSelectedOption
      description: Selected response for the custom field.
      required:
        - name
      properties:
        name:
          type: string
          description: Value of the selection
        description:
          type: string
          description: Description of the selection
        additionalUserInput:
          type: string
          description: Additional user input
        additionalInfos:
          type: array
          description: Actual values of the additional infos
          items:
            type: string
        additionalInfoConfigs:
          type: array
          description: Additional info configs for the selected option
          items:
            $ref: '#/components/schemas/AdditionalInfo'
    CustomFieldType:
      type: string
      description: The type of custom field.
      enum:
        - QUESTION
        - MEETING
        - BUDGET
        - BREX_TOKEN
      default: QUESTION
    CustomFieldV3Response:
      type: object
      title: CustomFieldV3Response
      description: Custom field responses.
      required:
        - fieldId
        - armId
        - selectedOptions
      properties:
        fieldId:
          type: string
          format: uuid
          description: Custom field id
        fieldName:
          type: string
          description: Name of the custom field
        armId:
          type: string
          format: uuid
          description: Arm id which is applicable
        includeLocations:
          type: array
          items:
            $ref: '#/components/schemas/IncludeLocation'
        selectedOptions:
          type: array
          description: The list of options that are selected by user or auto populated.
          items:
            $ref: '#/components/schemas/CustomFieldSelectedOption'
    DateModel:
      title: Date
      description: Date in ISO 8601 standard.
      type: object
      required:
        - iso8601
      properties:
        iso8601:
          type: string
          pattern: ^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$
          example: '2017-07-21'
    DateTimeLocal:
      title: DateTimeLocal
      description: Local date and time in ISO 8601 format.
      type: object
      required:
        - iso8601
      properties:
        iso8601:
          type: string
          pattern: >-
            ^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$
          example: 2017-07-21T17:32
    DateTimeOffset:
      title: DateTimeOffset
      description: ISO8601 UTC Date Time
      type: object
      required:
        - iso8601
      properties:
        iso8601:
          type: string
          pattern: >-
            ^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?(Z|([+-](0[0-9]|1[0-4]):([0-5][0-9])))$
          example: 2017-07-21T17:32Z
    DeckLevel:
      type: string
      description: The deck level of the rail.
      enum:
        - UPPER_DECK
        - LOWER_DECK
      example: UPPER_DECK
    Department:
      type: object
      title: Department
      description: Department details.
      required:
        - id
        - name
      properties:
        id:
          $ref: '#/components/schemas/DepartmentId'
        name:
          type: string
          example: IT Department
        externalId:
          type: string
          description: External id of the department
          example: department-ext-id
        employeeCount:
          type: integer
          format: int32
          description: Count of employees in the department
          example: 57
    DepartmentId:
      type: object
      title: DepartmentId
      description: Department id.
      required:
        - id
      properties:
        id:
          type: string
          example: 631ccbcf-9414-5fe0-c234-b324dfbe7422
    Dimensions:
      type: object
      title: Dimensions
      description: Image dimensions ie width and height.
      properties:
        height:
          type: integer
          format: int32
          example: 120
        width:
          type: integer
          format: int32
          example: 240
    DoubleListWrapper:
      type: object
      title: DoubleListWrapper
      properties:
        dList:
          type: array
          items:
            type: number
            format: double
    DoubleRange:
      type: object
      properties:
        min:
          type: number
          format: double
          description: Minimum value - inclusive.
        max:
          type: number
          format: double
          description: Maximum value - inclusive.
    DoubleRangeWrapper:
      type: object
      title: DoubleRangeWrapper
      properties:
        dRange:
          $ref: '#/components/schemas/DoubleRange'
    DoubleWrapper:
      type: object
      title: DoubleWrapper
      properties:
        d:
          type: number
          format: double
    Dpan:
      type: object
      title: Dpan
      description: Object to pass through dpan payment source details through the api call.
      required:
        - type
        - cardDetails
      properties:
        type:
          type: string
          description: Payment source type. Should be set to DPAN for card details.
        cardDetails:
          $ref: '#/components/schemas/CardDetails'
    Duration:
      type: object
      title: Duration
      description: Represents a duration of time with specific units of time.
      properties:
        iso8601:
          description: >
            Durations define the amount of intervening time in a time interval
            and are represented by the

            format P[n]Y[n]M[n]DT[n]H[n]M[n]S.

            The [n] is replaced by the value for each of the date and time
            elements that follow the [n].

            Leading zeros are not required. The capital letters P, Y, M, W, D,
            T, H, M, and S are

            designators for each of the date and time elements and are not
            replaced. P is the duration

            designator (for period) placed at the start of the duration
            representation.

            Y is the year designator.

            M is the month designator.

            W is the week designator.

            D is the day designator.

            T is the time designator.

            H is the hour designator.

            M is the minute designator.

            S is the second designator and can include decimal digits with
            arbitrary precision.
          type: string
          example: PT19H55M
    EmergencyContact:
      type: object
      title: EmergencyContact
      description: Emergency contact information.
      required:
        - name
        - phoneNumbers
      properties:
        name:
          type: string
          description: Full name of contact.
          example: John Smith
        email:
          type: string
          description: Email address of contact.
          format: email
          example: <EMAIL>
        designation:
          deprecated: true
          type: string
          description: Job title of contact.
          example: MANAGER
        relation:
          type: string
          description: Relation of contact to user.
          example: SPOUSE
          enum:
            - RELATION_UNKNOWN
            - SPOUSE
            - PARENT
            - SIBLING
            - CHILD
            - FRIEND
            - RELATIVE
            - COLLEAGUE
            - OTHER
        phoneNumbers:
          type: array
          description: Phone numbers of contact.
          items:
            $ref: '#/components/schemas/PhoneNumber'
        preferredLanguage:
          type: string
          description: Language preferred by user.
          example: en-US
    EmergencyContactInfo:
      type: object
      title: EmergencyContactInfo
      description: Emergency contact information.
      required:
        - email
      properties:
        address:
          $ref: '#/components/schemas/PostalAddress'
        designation:
          type: string
          example: MANAGER
        email:
          type: string
          format: email
          example: <EMAIL>
        name:
          $ref: '#/components/schemas/Name'
        phoneNumber:
          $ref: '#/components/schemas/PhoneNumber'
        userOrgId:
          $ref: '#/components/schemas/UserOrgId'
    EngineType:
      type: string
      title: EngineType
      description: Engine types.
      enum:
        - UNKNOWN_ENGINE
        - PETROL
        - DIESEL
        - ELECTRIC
        - CNG
        - HYBRID
        - HYDROGEN
        - MULTI_FUEL
        - ETHANOL
      example: PETROL
    EntityAnswer:
      type: object
      properties:
        entityId:
          type: string
          description: The unique ID for the question.
        userInput:
          type: string
          description: The text input given by user (if any).
        itemIds:
          type: array
          items:
            type: integer
            format: int32
          description: |
            The id/enum value corresponding to the option chosen by the user as
            answer.
        answers:
          type: array
          items:
            $ref: '#/components/schemas/AnswerPair'
        customFieldType:
          $ref: '#/components/schemas/CustomFieldType'
        questionDisplayText:
          type: string
          description: The question text to be displayed to the user.
        question:
          $ref: '#/components/schemas/Question'
    EntityId:
      type: object
      description: Identifier of an object
      required:
        - id
      properties:
        id:
          type: string
          format: uuid
    ErrorParameter:
      type: object
      title: ErrorParameter
      description: Error parameter
      properties:
        name:
          type: string
          description: Parameter name
        value:
          type: string
          description: Parameter value
    ErrorResponse:
      type: object
      properties:
        debugIdentifier:
          type: string
          description: Link to debug the error internally.
        errorMessages:
          type: array
          items:
            type: object
            properties:
              errorCode:
                type: string
                description: Error code to identify the specific errors.
              message:
                type: string
                description: Message containing details of error.
              errorParameters:
                type: array
                description: Error message parameters.
                items:
                  $ref: '#/components/schemas/ErrorParameter'
              errorDetail:
                type: string
                description: More details about the error.
    Expiry:
      title: Expiry
      type: object
      description: Contains the expiry of a Card.
      required:
        - expiryMonth
        - expiryYear
      properties:
        expiryMonth:
          type: integer
          format: int32
          minimum: 1
          maximum: 12
          description: Expiry month
          example: 1
        expiryYear:
          type: integer
          format: int32
          description: Expiry year
          minimum: 2000
          example: 2010
    ExpiryWrapper:
      type: object
      title: ExpiryWrapper
      properties:
        expiry:
          $ref: '#/components/schemas/Expiry'
    Expression:
      type: object
      title: Expression
      description: >
        Expression consisting of variables and constants which needs to be sent
        along with custom

        field response.
      required:
        - type
        - formatExpression
      properties:
        type:
          type: string
          example: EXPRESSION
          default: EXPRESSION
        formatExpression:
          type: string
          description: >
            The expression must be of format : `${expression}`.The expression
            can consist of a 

            combination of variables and mathematical operations.
             Variable names must begin with `var` followed by a number, which is used to identify 
            the variable in the variables list. The numbering should follow a
            1-based index.
              To define mathematical operations, the operation name should follow the format
            `math.<math_op>(arg1, arg2)`. Both `arg1` and `arg2` can be
            variables or constants. 

            The supported math operations (math_op) include: `add, mul, div,
            sub, min,

            and max`. All keywords, such as `<math_op>, math, and var` must be
            written in lowercase.
          example: 'Result:  ${math.mul(var1,5)}  ${var2}'
        variables:
          type: array
          description: Reference names of the variables present in the expression.
          items:
            $ref: '#/components/schemas/VariableName'
    FareComponent:
      type: string
      description: Fare component eligible for this fop rule
      enum:
        - BASE
        - TAX
      example: BASE
    FarePref:
      type: object
      title: FarePref
      description: Fare preferences.
      required:
        - fareTypes
      properties:
        fareTypes:
          type: array
          items:
            type: string
            enum:
              - UNKNOWN_TYPE
              - CHANGEABLE
              - REFUNDABLE
            example: CHANGEABLE
    Gender:
      title: Gender
      type: string
      enum:
        - MALE
        - FEMALE
        - UNSPECIFIED
        - UNDISCLOSED
      example: FEMALE
    Grade:
      type: object
      title: Grade
      description: Grade details.
      required:
        - id
        - name
      properties:
        employeeCount:
          type: integer
          format: int32
          description: Count of employees in the grade
          example: 75
        id:
          $ref: '#/components/schemas/GradeId'
        name:
          type: string
          example: Grade
    GradeId:
      type: object
      title: GradeId
      description: Grade id.
      required:
        - id
      properties:
        id:
          type: string
          example: 831ccbcb-1416-7fe2-e236-d324dfbe7424
    HotelAccessibleFeatureType:
      type: string
      title: Hotel Accessible Feature Type
      description: Types of accessible features available in the hotel.
      enum:
        - MOBILITY_ACCESSIBLE_ROOM_WITH_TUB
        - MOBILITY_ACCESSIBLE_ROOM_WITH_ROLL_IN_SHOWER
        - HEARING_ACCESSIBLE_ROOM
        - MOBILITY_ACCESSIBLE_ROOM_WITH_TUB_AND_ROLL_IN_SHOWER
        - MOBILITY_ACCESSIBLE_ROOM_WITH_TUB_AND_HEARING_ACCESSIBLE_ROOM
        - >-
          MOBILITY_ACCESSIBLE_ROOM_WITH_ROLL_IN_SHOWER_AND_HEARING_ACCESSIBLE_ROOM
        - MOBILITY_ACCESSIBLE_ROOM_WITH_TRANSFER_SHOWER
        - MOBILITY_ACCESSIBLE_ROOM_WITH_TUB_AND_TRANSFER_SHOWER
        - >-
          MOBILITY_ACCESSIBLE_ROOM_WITH_TRANSFER_SHOWER_AND_HEARING_ACCESSIBLE_ROOM
      example: MOBILITY_ACCESSIBLE_ROOM_WITH_TUB
    HotelAdditionalDetail:
      title: HotelAdditionalDetail
      type: object
      properties:
        additionalDetailType:
          $ref: '#/components/schemas/HotelAdditionalDetailType'
          description: Type of the Additional Detail for the room.
        text:
          type: string
    HotelAdditionalDetailType:
      title: HotelAdditionalDetailType
      type: string
      enum:
        - ADDITIONAL_DETAIL_TYPE_UNKNOWN
        - RATE_DESCRIPTION
        - PROPERTY_DESCRIPTION
        - PROPERTY_LOCATION
        - ROOM_INFORMATION
        - GUARANTEE_INFORMATION
        - DEPOSIT_INFORMATION
        - CANCELLATION_INFORMATION
        - CHECK_IN_CHECK_OUT_INFORMATION
        - EXTRA_CHARGE_INFORMATION
        - TAX_INFORMATION
        - SERVICE_CHARGE_INFORMATION
        - PACKAGE_INFORMATION
        - COMMISSION_INFORMATION
        - MISCELLANEOUS_INFORMATION
        - PROMOTIONAL_INFORMATION
        - INCLUSION_INFORMATION
        - AMENITY_INFORMATION
        - LATE_ARRIVAL_INFORMATION
        - LATE_DEPARTURE_INFORMATION
        - ADVANCED_BOOKING_INFORMATION
        - EXTRA_PERSON_INFORMATION
        - AREAS_SERVED
        - ONSITE_FACILITIES_INFORMATION
        - OFFSITE_FACILITIES_INFORMATION
        - ONSITE_SERVICES_INFORMATION
        - OFFSITE_SERVICES_INFORMATION
        - EXTENDED_STAY_INFORMATION
        - CORPORATE_BOOKING_INFORMATION
        - BOOKING_GUIDELINES
        - GOVERNMENT_BOOKING_POLICY
        - GROUP_BOOKING_INFORMATION
        - RATE_DISCLAIMER_INFORMATION
        - VISA_TRAVEL_REQUIREMENT_INFORMATION
        - SECURITY_INFORMATION
        - ONSITE_RECREATIONAL_ACTIVITIES_INFORMATION
        - OFFSITE_RECREATIONAL_ACTIVITIES_INFORMATION
        - GENERAL_MEETING_PLANNING_INFORMATION
        - GROUP_MEETING_PLANNING_INFORMATION
        - CONTRACT_NEGOTIATED_BOOKING_INFORMATION
        - TRAVEL_INDUSTRY_BOOKING_INFORMATION
        - MEETING_ROOM_DESCRIPTION
        - PET_POLICY_DESCRIPTION
        - MEAL_PLAN_DESCRIPTION
        - FAMILY_PLAN_DESCRIPTION
        - CHILDREN_INFORMATION
        - EARLY_CHECKOUT_DESCRIPTION
        - SPECIAL_OFFERS_DESCRIPTION
        - CATERING_DESCRIPTION
        - ROOM_DECOR_DESCRIPTION
        - OVERSOLD_POLICY_DESCRIPTION
        - LAST_ROOM_AVAILABILITY_DESCRIPTION
        - ROOM_TYPE_UPGRADE_DESCRIPTION
        - DRIVING_DIRECTIONS
        - DRIVING_DIRECTIONS_FROM_THE_NORTH
        - DRIVING_DIRECTIONS_FROM_THE_SOUTH
        - DRIVING_DIRECTIONS_FROM_THE_EAST
        - DRIVING_DIRECTIONS_FROM_THE_WEST
        - SURCHARGE_INFORMATION
        - MINIMUM_STAY_INFORMATION
        - MAXIMUM_STAY_INFORMATION
        - CHECK_IN_POLICY
        - CHECK_OUT_POLICY
        - EXPRESS_CHECK_IN_POLICY
        - EXPRESS_CHECK_OUT_POLICY
        - FACILITY_RESTRICTIONS
        - CUSTOMS_INFORMATION_FOR_MATERIAL
        - SEASONS
        - FOOD_AND_BEVERAGE_MINIMUMS_FOR_GROUPS
        - DEPOSIT_POLICY_FOR_MASTER_ACCOUNT
        - DEPOSIT_POLICY_FOR_RESERVATIONS
        - RESTAURANT_SERVICES
        - SPECIAL_EVENTS
        - CUISINE_DESCRIPTION
    HotelAmenity:
      title: Hotel Amenities
      type: object
      description: Type of hotel amenity
      required:
        - hotelAmenityType
      properties:
        hotelAmenityType:
          type: string
          enum:
            - TWENTY_FOUR_HOUR_FRONT_DESK
            - TWENTY_FOUR_HOUR_ROOM_SERVICE
            - TWENTY_FOUR_HOUR_SECURITY
            - ADJOINING_ROOMS
            - AIR_CONDITIONING
            - AIRLINE_DESK
            - ATM_CASH_MACHINE
            - BABY_SITTING
            - BBQ_PICNIC_AREA
            - BILINGUAL_STAFF
            - BOOKSTORE
            - BOUTIQUES_STORES
            - BRAILED_ELEVATORS
            - BUSINESS_LIBRARY
            - CAR_RENTAL_DESK
            - CASINO
            - CHECK_CASHING_POLICY
            - CHECK_IN_KIOSK
            - COCKTAIL_LOUNGE
            - COFFEE_SHOP
            - COIN_OPERATED_LAUNDRY
            - CONCIERGE_DESK
            - CONCIERGE_FLOOR
            - CONFERENCE_FACILITIES
            - COURTYARD
            - CURRENCY_EXCHANGE
            - DESK_WITH_ELECTRICAL_OUTLET
            - DOCTOR_ON_CALL
            - DOOR_MAN
            - DRIVING_RANGE
            - DRUGSTORE_PHARMACY
            - DUTY_FREE_SHOP
            - ELEVATORS
            - EXECUTIVE_FLOOR
            - EXERCISE_GYM
            - EXPRESS_CHECK_IN
            - EXPRESS_CHECK_OUT
            - FAMILY_PLAN
            - FLORIST
            - FOLIOS
            - FREE_AIRPORT_SHUTTLE
            - FREE_PARKING
            - FREE_TRANSPORTATION
            - GAME_ROOM
            - GIFT_NEWS_STAND
            - HAIRDRESSER_BARBER
            - ACCESSIBLE_FACILITIES
            - HEALTH_CLUB
            - HEATED_POOL
            - HOUSEKEEPING_DAILY
            - HOUSEKEEPING_WEEKLY
            - ICE_MACHINE
            - INDOOR_PARKING
            - INDOOR_POOL
            - JACUZZI
            - JOGGING_TRACK
            - KENNELS
            - LAUNDRY_VALET_SERVICE
            - LIQUOR_STORE
            - LIVE_ENTERTAINMENT
            - MASSAGE_SERVICES
            - NIGHTCLUB
            - OFF_SITE_PARKING
            - ON_SITE_PARKING
            - OUTDOOR_PARKING
            - OUTDOOR_POOL
            - PACKAGE_PARCEL_SERVICES
            - PARKING
            - PHOTOCOPY_CENTER
            - PLAYGROUND
            - POOL
            - POOLSIDE_SNACK_BAR
            - PUBLIC_ADDRESS_SYSTEM
            - RAMP_ACCESS
            - RECREATIONAL_VEHICLE_PARKING
            - RESTAURANT
            - ROOM_SERVICE
            - SAFE_DEPOSIT_BOX
            - SAUNA
            - SECURITY
            - SHOE_SHINE_STAND
            - SHOPPING_MALL
            - SOLARIUM
            - SPA
            - SPORTS_BAR
            - STEAM_BATH
            - STORAGE_SPACE
            - SUNDRY_CONVENIENCE_STORE
            - TECHNICAL_CONCIERGE
            - THEATRE_DESK
            - TOUR_SIGHTSEEING_DESK
            - TRANSLATION_SERVICES
            - TRAVEL_AGENCY
            - TRUCK_PARKING
            - VALET_CLEANING
            - DRY_CLEANING
            - VALET_PARKING
            - VENDING_MACHINES
            - VIDEO_TAPES
            - WAKEUP_SERVICE
            - WHEELCHAIR_ACCESS
            - WHIRLPOOL
            - MULTILINGUAL_STAFF
            - WEDDING_SERVICES
            - BANQUET_FACILITIES
            - BELL_STAFF_PORTER
            - BEAUTY_SHOP_SALON
            - COMPLIMENTARY_SELF_SERVICE_LAUNDRY
            - DIRECT_DIAL_TELEPHONE
            - FEMALE_TRAVELER_ROOM_FLOOR
            - PHARMACY
            - STABLES
            - ONE_TWENTY_AC
            - ONE_TWENTY_DC
            - TWO_TWENTY_AC
            - ACCESSIBLE_PARKING
            - TWO_TWENTY_DC
            - BARBEQUE_GRILLS
            - WOMENS_CLOTHING
            - MENS_CLOTHING
            - CHILDRENS_CLOTHING
            - SHOPS_AND_COMMERCIAL_SERVICES
            - VIDEO_GAMES
            - SPORTS_BAR_OPEN_FOR_LUNCH
            - SPORTS_BAR_OPEN_FOR_DINNER
            - ROOM_SERVICE_FULL_MENU
            - ROOM_SERVICE_LIMITED_MENU
            - ROOM_SERVICE_LIMITED_HOURS
            - VALET_SAME_DAY_DRY_CLEANING
            - BODY_SCRUB
            - BODY_WRAP
            - PUBLIC_AREA_AIR_CONDITIONED
            - EFOLIO_AVAILABLE_TO_COMPANY
            - INDIVIDUAL_EFOLIO_AVAILABLE
            - VIDEO_REVIEW_BILLING
            - BUTLER_SERVICE
            - COMPLIMENTARY_IN_ROOM_COFFEE_OR_TEA
            - COMPLIMENTARY_BUFFET_BREAKFAST
            - COMPLIMENTARY_COCKTAILS
            - COMPLIMENTARY_COFFEE_IN_LOBBY
            - COMPLIMENTARY_CONTINENTAL_BREAKFAST
            - COMPLIMENTARY_FULL_AMERICAN_BREAKFAST
            - DINNER_DELIVERY_SERVICE_FROM_LOCAL_RESTAURANT
            - COMPLIMENTARY_NEWSPAPER_DELIVERED_TO_ROOM
            - COMPLIMENTARY_NEWSPAPER_IN_LOBBY
            - COMPLIMENTARY_SHOESHINE
            - EVENING_RECEPTION
            - FRONT_DESK
            - GROCERY_SHOPPING_SERVICE_AVAILABLE
            - HALAL_FOOD_AVAILABLE
            - KOSHER_FOOD_AVAILABLE
            - LIMOUSINE_SERVICE
            - MANAGERS_RECEPTION
            - MEDICAL_FACILITIES_SERVICE
            - TELEPHONE_JACK_ADAPTOR_AVAILABLE
            - ALL_INCLUSIVE_MEAL_PLAN
            - BUFFET_BREAKFAST
            - COMMUNAL_BAR_AREA
            - CONTINENTAL_BREAKFAST
            - FULL_MEAL_PLAN
            - FULL_AMERICAN_BREAKFAST
            - MEAL_PLAN_AVAILABLE
            - MODIFIED_AMERICAN_MEAL_PLAN
            - FOOD_AND_BEVERAGE_OUTLETS
            - LOUNGES_BARS
            - BARBER_SHOP
            - VIDEO_CHECKOUT
            - ONSITE_LAUNDRY
            - TWENTY_FOUR_HOUR_FOOD_AND_BEVERAGE_KIOSK
            - CONCIERGE_LOUNGE
            - PARKING_FEE_MANAGED_BY_HOTEL
            - TRANSPORTATION
            - BREAKFAST_SERVED_IN_RESTAURANT
            - LUNCH_SERVED_IN_RESTAURANT
            - DINNER_SERVED_IN_RESTAURANT
            - FULL_SERVICE_HOUSEKEEPING
            - LIMITED_SERVICE_HOUSEKEEPING
            - HIGH_SPEED_INTERNET_ACCESS_FOR_LAPTOP_IN_PUBLIC_AREAS
            - WIRELESS_INTERNET_CONNECTION_IN_PUBLIC_AREAS
            - ADDITIONAL_SERVICES_AMENITIES_FACILITIES_ON_PROPERTY
            - TRANSPORTATION_SERVICES_LOCAL_AREA
            - TRANSPORTATION_SERVICES_LOCAL_OFFICE
            - DVD_VIDEO_RENTAL
            - PARKING_LOT
            - PARKING_DECK
            - STREET_SIDE_PARKING
            - COCKTAIL_LOUNGE_WITH_ENTERTAINMENT
            - COCKTAIL_LOUNGE_WITH_LIGHT_FARE
            - MOTORCYCLE_PARKING
            - PHONE_SERVICES
            - BALLROOM
            - BUS_PARKING
            - CHILDRENS_PLAY_AREA
            - CHILDRENS_NURSERY
            - DISCO
            - EARLY_CHECK_IN
            - LOCKER_ROOM
            - NON_SMOKING_ROOMS_GENERIC
            - TRAIN_ACCESS
            - AEROBICS_INSTRUCTION
            - BAGGAGE_HOLD
            - BICYCLE_RENTALS
            - DIETICIAN
            - LATE_CHECK_OUT_AVAILABLE
            - PET_SITTING_SERVICES
            - PRAYER_MATS
            - SPORTS_TRAINER
            - TURNDOWN_SERVICE
            - DVDS_VIDEOS_CHILDREN
            - BANK
            - LOBBY_COFFEE_SERVICE
            - BANKING_SERVICES
            - STAIRWELLS
            - PET_AMENITIES_AVAILABLE
            - EXHIBITION_CONVENTION_FLOOR
            - LONG_TERM_PARKING
            - CHILDREN_NOT_ALLOWED
            - CHILDREN_WELCOME
            - COURTESY_CAR
            - HOTEL_DOES_NOT_PROVIDE_PORNOGRAPHIC_FILMS_TV
            - HOTSPOTS
            - FREE_HIGH_SPEED_INTERNET_CONNECTION
            - INTERNET_SERVICES
            - PETS_ALLOWED
            - GOURMET_HIGHLIGHTS
            - CATERING_SERVICES
            - COMPLIMENTARY_BREAKFAST
            - BUSINESS_CENTER
            - BUSINESS_SERVICES
            - SECURED_PARKING
            - RACQUETBALL
            - SNOW_SPORTS
            - TENNIS_COURT
            - WATER_SPORTS
            - CHILD_PROGRAMS
            - GOLF
            - HORSEBACK_RIDING
            - OCEANFRONT
            - BEACHFRONT
            - HAIR_DRYER
            - IRONING_BOARD
            - HEATED_GUEST_ROOMS
            - TOILET
            - PARLOR
            - VIDEO_GAME_PLAYER
            - THALASSOTHERAPY
            - PRIVATE_DINING_FOR_GROUPS
            - HEARING_IMPAIRED_SERVICES
            - CARRYOUT_BREAKFAST
            - DELUXE_CONTINENTAL_BREAKFAST
            - HOT_CONTINENTAL_BREAKFAST
            - HOT_BREAKFAST
            - PRIVATE_POOL
            - CONNECTING_ROOMS
            - DATA_PORT
            - EXTERIOR_CORRIDORS
            - GULF_VIEW
            - ACCESSIBLE_ROOMS
            - HIGH_SPEED_INTERNET_ACCESS
            - INTERIOR_CORRIDORS
            - HIGH_SPEED_WIRELESS
            - KITCHENETTE
            - PRIVATE_BATH_OR_SHOWER
            - FIRE_SAFETY_COMPLIANT
            - WELCOME_DRINK
            - BOARDING_PASS_PRINT_OUT_AVAILABLE
            - PRINTING_SERVICES_AVAILABLE
            - ALL_PUBLIC_AREAS_NON_SMOKING
            - MEETING_ROOMS
            - MOVIES_IN_ROOM
            - SECRETARIAL_SERVICE
            - SNOW_SKIING
            - WATER_SKIING
            - FAX_SERVICE
            - GREAT_ROOM
            - LOBBY
            - MULTIPLE_PHONE_LINES_BILLED_SEPARATELY
            - UMBRELLAS
            - GAS_STATION
            - GROCERY_STORE
            - TWENTY_FOUR_HOUR_COFFEE_SHOP
            - AIRPORT_SHUTTLE_SERVICE
            - LUGGAGE_SERVICE
            - PIANO_BAR
            - VIP_SECURITY
            - COMPLIMENTARY_WIRELESS_INTERNET
            - CONCIERGE_BREAKFAST
            - SAME_GENDER_FLOOR
            - CHILDREN_PROGRAMS
            - BUILDING_MEETS_LOCAL_STATE_AND_COUNTRY_BUILDING_CODES
            - INTERNET_BROWSER_ON_TV
            - NEWSPAPER
            - PARKING_CONTROLLED_ACCESS_GATES_TO_ENTER_PARKING_AREA
            - HOTEL_SAFE_DEPOSIT_BOX_NOT_ROOM_SAFE_BOX
            - STORAGE_SPACE_AVAILABLE_FEE
            - TYPE_OF_ENTRANCES_TO_GUEST_ROOMS
            - BEVERAGE_COCKTAIL
            - CELL_PHONE_RENTAL
            - COFFEE_TEA
            - EARLY_CHECK_IN_GUARANTEE
            - FOOD_AND_BEVERAGE_DISCOUNT
            - LATE_CHECK_OUT_GUARANTEE
            - ROOM_UPGRADE_CONFIRMED
            - ROOM_UPGRADE_ON_AVAILABILITY
            - SHUTTLE_TO_LOCAL_BUSINESSES
            - SHUTTLE_TO_LOCAL_ATTRACTIONS
            - SOCIAL_HOUR
            - VIDEO_BILLING
            - WELCOME_GIFT
            - HYPOALLERGENIC_ROOMS
            - ROOM_AIR_FILTRATION
            - SMOKE_FREE_PROPERTY
            - WATER_PURIFICATION_SYSTEM_IN_USE
            - POOLSIDE_SERVICE
            - CLOTHING_STORE
            - ELECTRIC_CAR_CHARGING_STATIONS
            - OFFICE_RENTAL
            - PIANO
            - INCOMING_FAX
            - OUTGOING_FAX
            - SEMI_PRIVATE_SPACE
            - LOADING_DOCK
            - BABY_KIT
            - CHILDRENS_BREAKFAST
            - CLOAKROOM_SERVICE
            - COFFEE_LOUNGE
            - EVENTS_TICKET_SERVICE
            - LATE_CHECK_IN
            - LIMITED_PARKING
            - OUTDOOR_SUMMER_BAR_CAFE
            - NO_PARKING_AVAILABLE
            - BEER_GARDEN
            - GARDEN_LOUNGE_BAR
            - SUMMER_TERRACE
            - WINTER_TERRACE
            - ROOF_TERRACE
            - BEACH_BAR
            - HELICOPTER_SERVICE
            - FERRY
            - TAPAS_BAR
            - CAFE_BAR
            - SNACK_BAR
            - GUESTROOM_WIRED_INTERNET
            - GUESTROOM_WIRELESS_INTERNET
            - FITNESS_CENTER
            - ALCOHOLIC_BEVERAGES
            - NON_ALCOHOLIC_BEVERAGES
            - HEALTH_AND_BEAUTY_SERVICES
            - LOCAL_CALLS
            - MINIBAR
            - REFRIGERATOR
            - IN_ROOM_SAFE
            - SMOKING_ROOMS_AVAILBLE
            - MOUNTAIN_VIEW
            - POOL_VIEW
            - BEACH_VIEW
            - OCEAN_VIEW
            - ROOMS_WITH_BALCONY
            - FAMILY_ROOM
            - CRIB_CHARGE
            - ROLLAWAY_ADULT
            - FREE_WIFI_IN_MEETING_ROOMS
            - ECO_FRIENDLY
            - EXTRA_PERSON
            - STAY_SAFE
            - ENHANCED_HYGIENE_CLEANLINESS_PROTOCOLS
          example: ECO_FRIENDLY
        additionalInfo:
          type: string
          description: Amenity description
          example: Complimentary in-room coffee or tea
        isComplimentary:
          type: boolean
          description: Is Amenity complimentary
          example: true
    HotelAmenityType:
      title: HotelAmenityType
      type: string
      description: Type of hotel amenity.
      enum:
        - TWENTY_FOUR_HOUR_FRONT_DESK
        - TWENTY_FOUR_HOUR_ROOM_SERVICE
        - TWENTY_FOUR_HOUR_SECURITY
        - ADJOINING_ROOMS
        - AIR_CONDITIONING
        - AIRLINE_DESK
        - ATM_CASH_MACHINE
        - BABY_SITTING
        - BBQ_PICNIC_AREA
        - BILINGUAL_STAFF
        - BOOKSTORE
        - BOUTIQUES_STORES
        - BRAILED_ELEVATORS
        - BUSINESS_LIBRARY
        - CAR_RENTAL_DESK
        - CASINO
        - CHECK_CASHING_POLICY
        - CHECK_IN_KIOSK
        - COCKTAIL_LOUNGE
        - COFFEE_SHOP
        - COIN_OPERATED_LAUNDRY
        - CONCIERGE_DESK
        - CONCIERGE_FLOOR
        - CONFERENCE_FACILITIES
        - COURTYARD
        - CURRENCY_EXCHANGE
        - DESK_WITH_ELECTRICAL_OUTLET
        - DOCTOR_ON_CALL
        - DOOR_MAN
        - DRIVING_RANGE
        - DRUGSTORE_PHARMACY
        - DUTY_FREE_SHOP
        - ELEVATORS
        - EXECUTIVE_FLOOR
        - EXERCISE_GYM
        - EXPRESS_CHECK_IN
        - EXPRESS_CHECK_OUT
        - FAMILY_PLAN
        - FLORIST
        - FOLIOS
        - FREE_AIRPORT_SHUTTLE
        - FREE_PARKING
        - FREE_TRANSPORTATION
        - GAME_ROOM
        - GIFT_NEWS_STAND
        - HAIRDRESSER_BARBER
        - ACCESSIBLE_FACILITIES
        - HEALTH_CLUB
        - HEATED_POOL
        - HOUSEKEEPING_DAILY
        - HOUSEKEEPING_WEEKLY
        - ICE_MACHINE
        - INDOOR_PARKING
        - INDOOR_POOL
        - JACUZZI
        - JOGGING_TRACK
        - KENNELS
        - LAUNDRY_VALET_SERVICE
        - LIQUOR_STORE
        - LIVE_ENTERTAINMENT
        - MASSAGE_SERVICES
        - NIGHTCLUB
        - OFF_SITE_PARKING
        - ON_SITE_PARKING
        - OUTDOOR_PARKING
        - OUTDOOR_POOL
        - PACKAGE_PARCEL_SERVICES
        - PARKING
        - PHOTOCOPY_CENTER
        - PLAYGROUND
        - POOL
        - POOLSIDE_SNACK_BAR
        - PUBLIC_ADDRESS_SYSTEM
        - RAMP_ACCESS
        - RECREATIONAL_VEHICLE_PARKING
        - RESTAURANT
        - ROOM_SERVICE
        - SAFE_DEPOSIT_BOX
        - SAUNA
        - SECURITY
        - SHOE_SHINE_STAND
        - SHOPPING_MALL
        - SOLARIUM
        - SPA
        - SPORTS_BAR
        - STEAM_BATH
        - STORAGE_SPACE
        - SUNDRY_CONVENIENCE_STORE
        - TECHNICAL_CONCIERGE
        - THEATRE_DESK
        - TOUR_SIGHTSEEING_DESK
        - TRANSLATION_SERVICES
        - TRAVEL_AGENCY
        - TRUCK_PARKING
        - VALET_CLEANING
        - DRY_CLEANING
        - VALET_PARKING
        - VENDING_MACHINES
        - VIDEO_TAPES
        - WAKEUP_SERVICE
        - WHEELCHAIR_ACCESS
        - WHIRLPOOL
        - MULTILINGUAL_STAFF
        - WEDDING_SERVICES
        - BANQUET_FACILITIES
        - BELL_STAFF_PORTER
        - BEAUTY_SHOP_SALON
        - COMPLIMENTARY_SELF_SERVICE_LAUNDRY
        - DIRECT_DIAL_TELEPHONE
        - FEMALE_TRAVELER_ROOM_FLOOR
        - PHARMACY
        - STABLES
        - ONE_TWENTY_AC
        - ONE_TWENTY_DC
        - TWO_TWENTY_AC
        - ACCESSIBLE_PARKING
        - TWO_TWENTY_DC
        - BARBEQUE_GRILLS
        - WOMENS_CLOTHING
        - MENS_CLOTHING
        - CHILDRENS_CLOTHING
        - SHOPS_AND_COMMERCIAL_SERVICES
        - VIDEO_GAMES
        - SPORTS_BAR_OPEN_FOR_LUNCH
        - SPORTS_BAR_OPEN_FOR_DINNER
        - ROOM_SERVICE_FULL_MENU
        - ROOM_SERVICE_LIMITED_MENU
        - ROOM_SERVICE_LIMITED_HOURS
        - VALET_SAME_DAY_DRY_CLEANING
        - BODY_SCRUB
        - BODY_WRAP
        - PUBLIC_AREA_AIR_CONDITIONED
        - EFOLIO_AVAILABLE_TO_COMPANY
        - INDIVIDUAL_EFOLIO_AVAILABLE
        - VIDEO_REVIEW_BILLING
        - BUTLER_SERVICE
        - COMPLIMENTARY_IN_ROOM_COFFEE_OR_TEA
        - COMPLIMENTARY_BUFFET_BREAKFAST
        - COMPLIMENTARY_COCKTAILS
        - COMPLIMENTARY_COFFEE_IN_LOBBY
        - COMPLIMENTARY_CONTINENTAL_BREAKFAST
        - COMPLIMENTARY_FULL_AMERICAN_BREAKFAST
        - DINNER_DELIVERY_SERVICE_FROM_LOCAL_RESTAURANT
        - COMPLIMENTARY_NEWSPAPER_DELIVERED_TO_ROOM
        - COMPLIMENTARY_NEWSPAPER_IN_LOBBY
        - COMPLIMENTARY_SHOESHINE
        - EVENING_RECEPTION
        - FRONT_DESK
        - GROCERY_SHOPPING_SERVICE_AVAILABLE
        - HALAL_FOOD_AVAILABLE
        - KOSHER_FOOD_AVAILABLE
        - LIMOUSINE_SERVICE
        - MANAGERS_RECEPTION
        - MEDICAL_FACILITIES_SERVICE
        - TELEPHONE_JACK_ADAPTOR_AVAILABLE
        - ALL_INCLUSIVE_MEAL_PLAN
        - BUFFET_BREAKFAST
        - COMMUNAL_BAR_AREA
        - CONTINENTAL_BREAKFAST
        - FULL_MEAL_PLAN
        - FULL_AMERICAN_BREAKFAST
        - MEAL_PLAN_AVAILABLE
        - MODIFIED_AMERICAN_MEAL_PLAN
        - FOOD_AND_BEVERAGE_OUTLETS
        - LOUNGES_BARS
        - BARBER_SHOP
        - VIDEO_CHECKOUT
        - ONSITE_LAUNDRY
        - TWENTY_FOUR_HOUR_FOOD_AND_BEVERAGE_KIOSK
        - CONCIERGE_LOUNGE
        - PARKING_FEE_MANAGED_BY_HOTEL
        - TRANSPORTATION
        - BREAKFAST_SERVED_IN_RESTAURANT
        - LUNCH_SERVED_IN_RESTAURANT
        - DINNER_SERVED_IN_RESTAURANT
        - FULL_SERVICE_HOUSEKEEPING
        - LIMITED_SERVICE_HOUSEKEEPING
        - HIGH_SPEED_INTERNET_ACCESS_FOR_LAPTOP_IN_PUBLIC_AREAS
        - WIRELESS_INTERNET_CONNECTION_IN_PUBLIC_AREAS
        - ADDITIONAL_SERVICES_AMENITIES_FACILITIES_ON_PROPERTY
        - TRANSPORTATION_SERVICES_LOCAL_AREA
        - TRANSPORTATION_SERVICES_LOCAL_OFFICE
        - DVD_VIDEO_RENTAL
        - PARKING_LOT
        - PARKING_DECK
        - STREET_SIDE_PARKING
        - COCKTAIL_LOUNGE_WITH_ENTERTAINMENT
        - COCKTAIL_LOUNGE_WITH_LIGHT_FARE
        - MOTORCYCLE_PARKING
        - PHONE_SERVICES
        - BALLROOM
        - BUS_PARKING
        - CHILDRENS_PLAY_AREA
        - CHILDRENS_NURSERY
        - DISCO
        - EARLY_CHECK_IN
        - LOCKER_ROOM
        - NON_SMOKING_ROOMS_GENERIC
        - TRAIN_ACCESS
        - AEROBICS_INSTRUCTION
        - BAGGAGE_HOLD
        - BICYCLE_RENTALS
        - DIETICIAN
        - LATE_CHECK_OUT_AVAILABLE
        - PET_SITTING_SERVICES
        - PRAYER_MATS
        - SPORTS_TRAINER
        - TURNDOWN_SERVICE
        - DVDS_VIDEOS_CHILDREN
        - BANK
        - LOBBY_COFFEE_SERVICE
        - BANKING_SERVICES
        - STAIRWELLS
        - PET_AMENITIES_AVAILABLE
        - EXHIBITION_CONVENTION_FLOOR
        - LONG_TERM_PARKING
        - CHILDREN_NOT_ALLOWED
        - CHILDREN_WELCOME
        - COURTESY_CAR
        - HOTEL_DOES_NOT_PROVIDE_PORNOGRAPHIC_FILMS_TV
        - HOTSPOTS
        - FREE_HIGH_SPEED_INTERNET_CONNECTION
        - INTERNET_SERVICES
        - PETS_ALLOWED
        - GOURMET_HIGHLIGHTS
        - CATERING_SERVICES
        - COMPLIMENTARY_BREAKFAST
        - BUSINESS_CENTER
        - BUSINESS_SERVICES
        - SECURED_PARKING
        - RACQUETBALL
        - SNOW_SPORTS
        - TENNIS_COURT
        - WATER_SPORTS
        - CHILD_PROGRAMS
        - GOLF
        - HORSEBACK_RIDING
        - OCEANFRONT
        - BEACHFRONT
        - HAIR_DRYER
        - IRONING_BOARD
        - HEATED_GUEST_ROOMS
        - TOILET
        - PARLOR
        - VIDEO_GAME_PLAYER
        - THALASSOTHERAPY
        - PRIVATE_DINING_FOR_GROUPS
        - HEARING_IMPAIRED_SERVICES
        - CARRYOUT_BREAKFAST
        - DELUXE_CONTINENTAL_BREAKFAST
        - HOT_CONTINENTAL_BREAKFAST
        - HOT_BREAKFAST
        - PRIVATE_POOL
        - CONNECTING_ROOMS
        - DATA_PORT
        - EXTERIOR_CORRIDORS
        - GULF_VIEW
        - ACCESSIBLE_ROOMS
        - HIGH_SPEED_INTERNET_ACCESS
        - INTERIOR_CORRIDORS
        - HIGH_SPEED_WIRELESS
        - KITCHENETTE
        - PRIVATE_BATH_OR_SHOWER
        - FIRE_SAFETY_COMPLIANT
        - WELCOME_DRINK
        - BOARDING_PASS_PRINT_OUT_AVAILABLE
        - PRINTING_SERVICES_AVAILABLE
        - ALL_PUBLIC_AREAS_NON_SMOKING
        - MEETING_ROOMS
        - MOVIES_IN_ROOM
        - SECRETARIAL_SERVICE
        - SNOW_SKIING
        - WATER_SKIING
        - FAX_SERVICE
        - GREAT_ROOM
        - LOBBY
        - MULTIPLE_PHONE_LINES_BILLED_SEPARATELY
        - UMBRELLAS
        - GAS_STATION
        - GROCERY_STORE
        - TWENTY_FOUR_HOUR_COFFEE_SHOP
        - AIRPORT_SHUTTLE_SERVICE
        - LUGGAGE_SERVICE
        - PIANO_BAR
        - VIP_SECURITY
        - COMPLIMENTARY_WIRELESS_INTERNET
        - CONCIERGE_BREAKFAST
        - SAME_GENDER_FLOOR
        - CHILDREN_PROGRAMS
        - BUILDING_MEETS_LOCAL_STATE_AND_COUNTRY_BUILDING_CODES
        - INTERNET_BROWSER_ON_TV
        - NEWSPAPER
        - PARKING_CONTROLLED_ACCESS_GATES_TO_ENTER_PARKING_AREA
        - HOTEL_SAFE_DEPOSIT_BOX_NOT_ROOM_SAFE_BOX
        - STORAGE_SPACE_AVAILABLE_FEE
        - TYPE_OF_ENTRANCES_TO_GUEST_ROOMS
        - BEVERAGE_COCKTAIL
        - CELL_PHONE_RENTAL
        - COFFEE_TEA
        - EARLY_CHECK_IN_GUARANTEE
        - FOOD_AND_BEVERAGE_DISCOUNT
        - LATE_CHECK_OUT_GUARANTEE
        - ROOM_UPGRADE_CONFIRMED
        - ROOM_UPGRADE_ON_AVAILABILITY
        - SHUTTLE_TO_LOCAL_BUSINESSES
        - SHUTTLE_TO_LOCAL_ATTRACTIONS
        - SOCIAL_HOUR
        - VIDEO_BILLING
        - WELCOME_GIFT
        - HYPOALLERGENIC_ROOMS
        - ROOM_AIR_FILTRATION
        - SMOKE_FREE_PROPERTY
        - WATER_PURIFICATION_SYSTEM_IN_USE
        - POOLSIDE_SERVICE
        - CLOTHING_STORE
        - ELECTRIC_CAR_CHARGING_STATIONS
        - OFFICE_RENTAL
        - PIANO
        - INCOMING_FAX
        - OUTGOING_FAX
        - SEMI_PRIVATE_SPACE
        - LOADING_DOCK
        - BABY_KIT
        - CHILDRENS_BREAKFAST
        - CLOAKROOM_SERVICE
        - COFFEE_LOUNGE
        - EVENTS_TICKET_SERVICE
        - LATE_CHECK_IN
        - LIMITED_PARKING
        - OUTDOOR_SUMMER_BAR_CAFE
        - NO_PARKING_AVAILABLE
        - BEER_GARDEN
        - GARDEN_LOUNGE_BAR
        - SUMMER_TERRACE
        - WINTER_TERRACE
        - ROOF_TERRACE
        - BEACH_BAR
        - HELICOPTER_SERVICE
        - FERRY
        - TAPAS_BAR
        - CAFE_BAR
        - SNACK_BAR
        - GUESTROOM_WIRED_INTERNET
        - GUESTROOM_WIRELESS_INTERNET
        - FITNESS_CENTER
        - ALCOHOLIC_BEVERAGES
        - NON_ALCOHOLIC_BEVERAGES
        - HEALTH_AND_BEAUTY_SERVICES
        - LOCAL_CALLS
        - MINIBAR
        - REFRIGERATOR
        - IN_ROOM_SAFE
        - SMOKING_ROOMS_AVAILBLE
        - MOUNTAIN_VIEW
        - POOL_VIEW
        - BEACH_VIEW
        - OCEAN_VIEW
        - ROOMS_WITH_BALCONY
        - FAMILY_ROOM
        - CRIB_CHARGE
        - ROLLAWAY_ADULT
        - FREE_WIFI_IN_MEETING_ROOMS
        - ECO_FRIENDLY
        - EXTRA_PERSON
        - STAY_SAFE
        - ENHANCED_HYGIENE_CLEANLINESS_PROTOCOLS
      example: ECO_FRIENDLY
    HotelAutocompleteResponse:
      title: HotelAutocompleteResponse
      type: object
      required:
        - hotels
      properties:
        hotels:
          type: array
          description: List of Hotels matching the Autocomplete query
          items:
            $ref: '#/components/schemas/AutocompleteHotel'
    HotelBrand:
      type: object
      title: HotelBrand
      description: The brand of hotel.
      properties:
        brandCode:
          type: string
          description: The code of hotel brand.
          example: HY
        brandName:
          type: string
          description: The name of hotel brand.
          example: Global Hytt Corp.
    HotelCancelPnrRequest:
      type: object
      title: Hotel Cancel Pnr Request
      description: Request object for cancelling a hotel pnr.
      properties:
        customFieldV3Responses:
          type: array
          description: Custom field responses for the booking.
          items:
            $ref: '#/components/schemas/CustomFieldV3Response'
    HotelCancelPnrResponse:
      type: object
      title: Hotel Cancel Pnr Response
      description: Response object for cancelling a hotel pnr.
      required:
        - confirmationId
      properties:
        confirmationId:
          type: string
          description: The confirmation ID of the cancelled hotel pnr.
          example: '231241232'
    HotelCancellationPolicy:
      type: object
      title: HotelCancellationPolicy
      description: Cancellation policy for the PNR
      properties:
        refundable:
          type: string
          description: Is refundable or not
          enum:
            - UNKNOWN
            - 'TRUE'
            - 'FALSE'
          example: 'TRUE'
        terms:
          type: array
          items:
            $ref: '#/components/schemas/CancellationPolicyTerm'
    HotelChain:
      type: object
      title: HotelChain
      description: The chain of hotel.
      properties:
        chainCode:
          type: string
          description: The code of hotel chain.
          example: EM
        chainName:
          type: string
          description: The name of hotel chain.
          example: Mariott
    HotelCo2EmissionDetail:
      type: object
      description: The co2 emission detail for the hotel.
      required:
        - co2EmissionValue
      properties:
        co2EmissionValue:
          type: number
          format: double
          example: 10.5
          description: CO2 emission value in kg per room for the entire stay.
    HotelCodeSearch:
      type: object
      title: Hotel Code Search
      required:
        - searchType
        - hotelCode
      properties:
        searchType:
          type: string
          example: HOTEL_CODE
        hotelCode:
          type: string
          example: SPOTNANA:12345
          description: >
            Unique code for a hotel. The search result will also contain other
            hotels in close proximity to the requested hotel code.
    HotelCreatePnrRequest:
      type: object
      title: HotelCreatePnrRequest
      description: Defines the structure of a hotel create pnr request
      required:
        - bookingKey
        - travelers
        - tripData
        - bookingContact
        - bookingPaymentDetails
      properties:
        bookingKey:
          type: string
          description: The booking key for the hotel booking.
          example: example_booking_key
        travelers:
          type: array
          title: Travelers
          description: |
            The list of travelers and their details. 
            The traveler at index 0 will be considered as the primary traveler 
            and their details will be passed to the supplier.
          items:
            $ref: '#/components/schemas/HotelTraveler'
        tripData:
          $ref: '#/components/schemas/TripData'
        preBookAnswers:
          $ref: '#/components/schemas/PreBookAnswers'
        bookingPaymentDetails:
          $ref: '#/components/schemas/BookingPaymentDetails'
        bookingContact:
          $ref: '#/components/schemas/BookingContact'
        hotelSpecialRequests:
          $ref: '#/components/schemas/HotelSpecialRequests'
        cancelSourcePnrId:
          type: string
          description: >-
            The source PNR ID within the booking source that needs to be
            canceled in favor of the new booking that is being created.
          example: ABC123
        hotelRateAssuranceInfo:
          $ref: '#/components/schemas/HotelRateAssuranceMetadata'
        customFieldV3Responses:
          type: array
          description: Custom field responses for the booking.
          items:
            $ref: '#/components/schemas/CustomFieldV3Response'
    HotelCreatePnrResponse:
      type: object
      title: HotelCreatePnrResponse
      description: Defines the structure of a hotel create pnr response
      required:
        - pnrId
        - pnrStatus
      properties:
        pnrId:
          type: string
          description: A unique PNR ID created by Spotnana for the new hotel booking.
          example: '7373737373'
        pnrStatus:
          type: string
          description: The status of the PNR.
          enum:
            - SUCCESS
            - APPROVAL_PENDING
            - CONFIRMATION_PENDING
          example: SUCCESS
        externalPnrId:
          type: string
          description: >-
            The external PNR ID for the hotel booking, created by the third
            party suppliers.
          example: ABC123
    HotelDescription:
      type: object
      title: HotelDescription
      description: Hotel description
      properties:
        type:
          type: string
          enum:
            - GENERAL
            - ALERTS
            - DINING
            - FACILITIES
            - RECREATION
            - SERVICES
            - ATTRACTIONS
            - CANCELLATION_POLICY
            - DEPOSIT_POLICY
            - DIRECTIONS
            - POLICIES
            - SAFETY
            - TRANSPORTATION
          description: Hotel description type
          example: ''
        value:
          type: string
          description: Hotel description value
          example: ''
    HotelDetailsFilters:
      type: object
      title: Hotel Details Filters
      description: Filters for hotel details.
      required:
        - rateTypes
      properties:
        rateTypes:
          type: array
          description: Filter rates based on rate type.
          items:
            $ref: '#/components/schemas/HotelRateType'
    HotelDetailsRequest:
      type: object
      title: Hotel Details Request
      description: Request object for fetching hotel details.
      required:
        - hotelDetailsKey
      properties:
        hotelDetailsKey:
          type: string
          description: A unique identifier for the hotel details.
        corporateInfo:
          $ref: '#/components/schemas/CorporateInfoV2'
          description: Corporate information related to the hotel booking.
        occupancyDates:
          $ref: '#/components/schemas/OccupancyDates'
          description: Occupancy values and dates for the hotel details.
        filters:
          $ref: '#/components/schemas/HotelDetailsFilters'
          description: Filters to apply when fetching hotel details.
        userId:
          $ref: '#/components/schemas/HotelUserId'
          description: Hotel User Identifier.
    HotelDetailsResponse:
      type: object
      title: Hotel Details Response
      description: Response object for hotel details.
      required:
        - hotelSpec
        - rooms
        - occupancyDates
      properties:
        hotelSpec:
          $ref: '#/components/schemas/HotelSpec'
          description: Specification of the hotel.
        rooms:
          type: array
          description: List of available rooms in the hotel.
          items:
            $ref: '#/components/schemas/HotelRoomData'
        bookedRooms:
          type: array
          description: >-
            List of booked rooms in the hotel, present only as part of
            modification details response.
          items:
            $ref: '#/components/schemas/HotelRoomData'
        occupancyDates:
          $ref: '#/components/schemas/OccupancyDates'
          description: Occupancy values and dates for the hotel details.
        rateStatistics:
          $ref: '#/components/schemas/HotelRateStatistics'
          description: Statistical information about the hotel rates.
        showOnlyBaseFare:
          type: boolean
          description: Flag to indicate if only the base fare should be shown.
        preferences:
          type: array
          description: List of user preferences.
          items:
            $ref: '#/components/schemas/Preference'
        termsAndConditions:
          type: array
          description: List of terms and conditions applicable to the booking.
          items:
            $ref: '#/components/schemas/TermsAndConditions'
    HotelImageCategory:
      type: string
      description: Category of image such as exterior, spa, etc.
      enum:
        - UNKNOWN_CATEGORY
        - EXTERIOR_VIEW
        - LOBBY_VIEW
        - POOL_VIEW
        - RESTAURANT
        - HEALTH_CLUB
        - GUEST_ROOM
        - SUITE
        - MEETING_ROOM
        - BALLROOM
        - GOLF_COURSE
        - BEACH
        - SPA
        - BAR_OR_LOUNGE
        - RECREATIONAL_FACILITY
        - LOGO
        - BASICS
        - MAP
        - PROMOTIONAL
        - HOT_NEWS
        - MISCELLANEOUS
        - GUEST_ROOM_AMENITY
        - PROPERTY_AMENITY
        - BUSINESS_CENTER
    HotelImageSet:
      type: object
      description: Object containing details of images of different for a given category.
      required:
        - category
        - imageGroup
      properties:
        category:
          $ref: '#/components/schemas/HotelImageCategory'
        imageGroup:
          $ref: '#/components/schemas/ImageGroup'
    HotelMedianRateInfo:
      type: object
      title: Hotel Median Rate Info
      description: Details median rate information for a hotel.
      required:
        - total
        - base
      properties:
        total:
          description: Total Amount.
          $ref: '#/components/schemas/Money'
        base:
          description: Base Amount.
          $ref: '#/components/schemas/Money'
    HotelModifyBookingRequest:
      type: object
      title: HotelModifyBookingRequest
      description: Defines the structure of a hotel modify booking request
      required:
        - bookingKey
        - travelers
        - tripData
        - bookingContact
      properties:
        bookingKey:
          type: string
          description: The booking key for the hotel booking
          example: example_booking_key
        travelers:
          type: array
          title: Travelers
          description: >-
            The list of travelers, the first traveler is considered as primary
            traveler
          items:
            $ref: '#/components/schemas/HotelTraveler'
        tripData:
          $ref: '#/components/schemas/TripData'
        preBookAnswers:
          $ref: '#/components/schemas/PreBookAnswers'
        hotelSpecialRequests:
          $ref: '#/components/schemas/HotelSpecialRequests'
        bookingPaymentDetails:
          $ref: '#/components/schemas/BookingPaymentDetails'
        bookingContact:
          $ref: '#/components/schemas/BookingContact'
        customFieldV3Responses:
          type: array
          description: Custom field responses for the booking.
          items:
            $ref: '#/components/schemas/CustomFieldV3Response'
    HotelModifyBookingResponse:
      type: object
      title: HotelModifyBookingResponse
      description: Defines the structure of a hotel modify booking response
      required:
        - pnrId
        - pnrStatus
      properties:
        pnrId:
          type: string
          description: The unique PNR ID for the modified hotel booking
          example: '7373737373'
        pnrStatus:
          type: string
          description: The status of the PNR
          enum:
            - INITIATED
            - CANCELLED
            - CONFIRMED
            - PENDING
            - CONTACT_SUPPORT
            - REQUEST_PENDING
            - REQUESTED
          example: CONFIRMED
        externalPnrId:
          type: string
          description: >-
            The external PNR ID for the modified hotel booking, provided by the
            third party suppliers
          example: ABC123
    HotelModifyDetailsRequest:
      type: object
      title: Hotel Modify Details Request
      description: Request object for fetching hotel details if room is already booked.
      required:
        - occupancyDates
      properties:
        occupancyDates:
          $ref: '#/components/schemas/OccupancyDates'
          description: Occupancy values and dates for the hotel details.
        corporateInfo:
          $ref: '#/components/schemas/CorporateInfoV2'
          description: Corporate information related to the hotel booking.
    HotelPref:
      type: object
      title: HotelPref
      description: Travel preferences related to hotel.
      properties:
        hotelParentChains:
          type: array
          description: A list of hotel parent chains.
          items:
            $ref: '#/components/schemas/HotelChain'
        hotelBrands:
          type: array
          description: A list of hotel brands.
          items:
            $ref: '#/components/schemas/HotelBrand'
        hotelAmenityTypes:
          type: array
          description: A list of HotelAmenities.
          items:
            $ref: '#/components/schemas/HotelPrefAmenity'
        roomPreference:
          $ref: '#/components/schemas/RoomPreference'
        conditionalRates:
          type: array
          description: A list of conditional rates for rail.
          items:
            $ref: '#/components/schemas/ConditionalRate'
    HotelPrefAmenity:
      type: string
      description: Preferred hotel amenity.
      enum:
        - PARKING
        - FREE_PARKING
        - FREE_BREAKFAST
        - POOL
        - WIFI
        - FITNESS_CENTER
        - FAMILY_FRIENDLY
        - RECEPTION
        - SPA
        - RESTAURANT
        - BAR
        - TRANSPORTATION
        - PET_FRIENDLY
        - BUSINESS_CENTER
        - AIR_CONDITIONING
        - BEACH_ACCESS
        - LAUNDRY_SERVICES
        - ROOM_SERVICE
        - ACCESSIBLE
    HotelPriceCheckRequest:
      type: object
      title: Hotel Price Check Request
      description: Defines the structure of a hotel price check request.
      required:
        - priceValidateKey
      properties:
        priceValidateKey:
          type: string
          description: The key used to validate the price.
        tripId:
          type: string
          description: The trip ID for the booking.
    HotelPriceCheckResponse:
      type: object
      title: Hotel Price Check Response
      description: Defines the structure of a hotel price check response.
      required:
        - bookingKey
        - timeout
        - priceChange
        - price
      properties:
        bookingKey:
          type: string
          description: The unique key used to make the hotel booking.
        timeout:
          type: integer
          format: int32
          description: Timeout for the booking key in seconds.
        priceChange:
          type: boolean
          description: Indicates if the price has changed.
        price:
          $ref: '#/components/schemas/HotelRoomRate'
          description: Final price for the booking.
    HotelPropertyEnum:
      type: string
      enum:
        - ALL_SUITE
        - ALL_INCLUSIVE_RESORT
        - APARTMENT
        - BED_AND_BREAKFAST
        - CABIN_OR_BUNGALOW
        - CAMPGROUND
        - CHALET
        - CONDOMINIUM
        - CONFERENCE_CENTER
        - CORPORATE_BUSINESS_TRANSIENT
        - CRUISE
        - EXTENDED_STAY
        - GUEST_FARM
        - GUEST_HOUSE_LIMITED_SERVICE
        - HEALTH_SPA
        - HOLIDAY_RESORT
        - HOSTEL
        - HOTEL
        - INN
        - LODGE
        - MONASTERY
        - MOTEL
        - RANCH
        - RESIDENTIAL_APARTMENT
        - RESORT
        - TENT
        - VACATION_HOME
        - VILLA
        - WILDLIFE_RESERVE
        - CASTLE
        - GOLF
        - PENSION
        - SKI
        - SPA
        - BOATEL
        - HISTORICAL
        - RECREATIONAL_VEHICLE_PARK
        - CHARM_HOTEL
        - OTHER
    HotelPropertyType:
      type: object
      title: Hotel Property Type
      description: Describes the type of property for a hotel.
      required:
        - type
      properties:
        type:
          $ref: '#/components/schemas/HotelPropertyEnum'
          description: Type of the property.
        description:
          type: string
          description: Description of the property type.
    HotelRateAssuranceMetadata:
      title: HotelRateAssuranceMetadata
      type: object
      description: Savings information from the rate assurance rebooking
      properties:
        selfReportedSavings:
          $ref: '#/components/schemas/SimpleMoney'
        actualSavings:
          $ref: '#/components/schemas/SimpleMoney'
    HotelRateInfo:
      type: object
      title: Hotel Rate Info
      description: Information about a hotel rate.
      required:
        - rateSource
        - totalRate
        - rateType
        - nightlyRate
        - ratePlanName
        - isCvvRequired
        - averageNightlyRate
        - rateSupplier
        - prepaidRate
        - postpaidRate
      properties:
        rateSource:
          type: string
          description: The source of the rate.
          enum:
            - SABRE
            - BOOKING_COM
            - EXPEDIA
            - OFFLINE
            - HRS
            - BCD
            - QANTAS_HOTELS
            - HOTEL_HUB
            - MARRIOTT
            - AMADEUS
        totalRate:
          $ref: '#/components/schemas/HotelRoomRate'
          description: The total rate for the room.
        rateType:
          $ref: '#/components/schemas/HotelRateType'
          description: The type of rate.
        nightlyRate:
          type: array
          description: The nightly rate for the room.
          items:
            $ref: '#/components/schemas/HotelRoomRate'
        ratePlanName:
          type: string
          description: The name of the rate plan.
        isCvvRequired:
          type: boolean
          description: Indicates if CVV is required for payment.
        averageNightlyRate:
          $ref: '#/components/schemas/HotelRoomRate'
          description: The average nightly rate for the room.
        refundAmount:
          $ref: '#/components/schemas/Money'
          description: The amount refundable for the room.
        rateSupplier:
          $ref: '#/components/schemas/ThirdPartySource'
          description: The supplier of the rate.
        publishedRate:
          $ref: '#/components/schemas/HotelRoomRate'
          description: The published rate for the room.
        publishedNightlyRate:
          $ref: '#/components/schemas/HotelRoomRate'
          description: The published nightly rate for the room.
        rateDifference:
          $ref: '#/components/schemas/HotelRoomRate'
          description: The difference between the published rate and the actual rate.
        prepaidRate:
          $ref: '#/components/schemas/HotelRoomRate'
          description: The prepaid rate for the room.
        postpaidRate:
          $ref: '#/components/schemas/HotelRoomRate'
          description: The postpaid rate for the room.
        penaltyAmount:
          $ref: '#/components/schemas/Money'
          description: The penalty amount for the rate.
        rateTag:
          type: string
          description: A tag associated with the rate.
    HotelRateStatistics:
      type: object
      title: Hotel Rate Statistics
      description: Contains statistical information about hotel search results.
      required:
        - median
        - sampleSize
        - policyType
      properties:
        median:
          $ref: '#/components/schemas/HotelMedianRateInfo'
        sampleSize:
          type: integer
          format: int32
          description: Size of the sample set used to calculate the statistics.
          example: 100
        policyType:
          type: string
          description: Enumeration of policy types for rate statistics.
          enum:
            - DEFAULT
            - SET_BY_ADMIN
    HotelRateType:
      type: string
      title: HotelRate Type
      enum:
        - PUBLISHED
        - CORPORATE
        - SPOTNANA
        - REGULAR
        - AAA
        - AARP
        - SENIOR_CITIZEN
        - GOVERNMENT
        - MILITARY
        - MEMBERSHIP
        - BEST_AVAILABLE_RATE
        - TMC
    HotelRoomAmenity:
      type: object
      title: HotelRoomAmenity
      description: Hotel Room amenities
      properties:
        additionalInfo:
          type: string
          description: Extra information about the room amenity
        complimentary:
          type: boolean
          description: Is amenity complimentary
          example: true
        type:
          type: string
          description: Room amenity type
          enum:
            - UNKNOWN
            - ADJOINING_ROOMS
            - AIR_CONDITIONING
            - ALARM_CLOCK
            - ALL_NEWS_CHANNEL
            - AM_FM_RADIO
            - BABY_LISTENING_DEVICE
            - BALCONY_LANAI_TERRACE
            - BARBEQUE_GRILLS
            - BATH_TUB_WITH_SPRAY_JETS
            - BATHROBE
            - BATHROOM_AMENITIES
            - BATHROOM_TELEPHONE
            - BATHTUB
            - BATHTUB_ONLY
            - BATHTUB_SHOWER_COMBINATION
            - BIDET
            - BOTTLED_WATER
            - CABLE_TELEVISION
            - COFFEE_TEA_MAKER
            - COLOR_TELEVISION
            - COMPUTER
            - CONNECTING_ROOMS
            - CONVERTERS_VOLTAGE_ADAPTORS
            - COPIER
            - CORDLESS_PHONE
            - CRIBS
            - DATA_PORT
            - DESK
            - DESK_WITH_LAMP
            - DINING_GUIDE
            - DIRECT_DIAL_PHONE_NUMBER
            - DISHWASHER
            - DOUBLE_BEDS
            - DUAL_VOLTAGE_OUTLET
            - ELECTRICAL_CURRENT_VOLTAGE
            - ERGONOMIC_CHAIR
            - EXTENDED_PHONE_CORD
            - FAX_MACHINE
            - FIRE_ALARM
            - FIRE_ALARM_WITH_LIGHT
            - FIREPLACE
            - FREE_TOLL_FREE_CALLS
            - FREE_CALLS
            - FREE_CREDIT_CARD_ACCESS_CALLS
            - FREE_LOCAL_CALLS
            - FREE_MOVIES_VIDEO
            - FULL_KITCHEN
            - GRAB_BARS_IN_BATHROOM
            - GRECIAN_TUB
            - HAIRDRYER
            - HIGH_SPEED_INTERNET_CONNECTION
            - INTERACTIVE_WEB_TV
            - INTERNATIONAL_DIRECT_DIALING
            - INTERNET_ACCESS
            - IRON
            - IRONING_BOARD
            - WHIRPOOL
            - KING_BED
            - KITCHEN
            - KITCHEN_SUPPLIES
            - KITCHENETTE
            - KNOCK_LIGHT
            - LAPTOP
            - LARGE_DESK
            - LARGE_WORK_AREA
            - LAUNDRY_BASKET_CLOTHES_HAMPER
            - LOFT
            - MICROWAVE
            - MINIBAR
            - MODEM
            - MODEM_JACK
            - MULTILINE_PHONE
            - NEWSPAPER
            - NONSMOKING
            - NOTEPADS
            - OFFICE_SUPPLIES
            - OVEN
            - PAY_PER_VIEW_MOVIES_ON_TV
            - PENS
            - PHONE_IN_BATHROOM
            - PLATES_AND_BOWLS
            - POTS_AND_PANS
            - PRAYER_MATS
            - PRINTER
            - PRIVATE_BATHROOM
            - QUEEN_BED
            - RECLINER
            - REFRIGERATOR
            - REFRIGERATOR_WITH_ICE_MAKER
            - REMOTE_CONTROL_TELEVISION
            - ROLLAWAY_BED
            - SAFE
            - SCANNER
            - SEPARATE_CLOSET
            - SEPARATE_MODEM_LINE_AVAILABLE
            - SHOE_POLISHER
            - SHOWER_ONLY
            - SILVERWARE_UTENSILS
            - SITTING_AREA
            - SMOKE_DETECTORS
            - SMOKING
            - SOFA_BED
            - SPEAKER_PHONE
            - STEREO
            - STOVE
            - TAPE_RECORDER
            - TELEPHONE
            - TELEPHONE_FOR_HEARING_IMPAIRED
            - TELEPHONES_WITH_MESSAGE_LIGHT
            - TOASTER_OVEN
            - TROUSER_PANT_PRESS
            - TURN_DOWN_SERVICE
            - TWIN_BED
            - VAULTED_CEILINGS
            - VCR_MOVIES
            - VCR_PLAYER
            - VIDEO_GAMES_AMENITY
            - VOICE_MAIL
            - WAKEUP_CALLS
            - WATER_CLOSET
            - WATER_PURIFICATION_SYSTEM
            - WET_BAR
            - WIRELESS_INTERNET_CONNECTION
            - WIRELESS_KEYBOARD
            - ADAPTOR_AVAILABLE_FOR_TELEPHONE_PC_USE
            - AIR_CONDITIONING_INDIVIDUALLY_CONTROLLED_IN_ROOM
            - BATHTUB_ANDWHIRLPOOL_SEPARATE
            - TELEPHONE_WITH_DATA_PORTS
            - CD_PLAYER
            - COMPLIMENTARY_LOCAL_CALLS_TIME_LIMIT
            - EXTRA_PERSON_CHARGE_FOR_ROLLAWAY_USE
            - DOWN_FEATHER_PILLOWS
            - DESK_WITH_ELECTRICAL_OUTLET
            - ESPN_AVAILABLE
            - FOAM_PILLOWS
            - HBO_AVAILABLE
            - HIGH_CEILINGS
            - MARBLE_BATHROOM
            - LIST_OF_MOVIE_CHANNELS_AVAILABLE
            - PETS_ALLOWED
            - OVERSIZED_BATHTUB
            - SHOWER
            - SINK_INROOM
            - SOUNDPROOFED_ROOM
            - STORAGE_SPACE
            - TABLES_AND_CHAIRS
            - TWOLINE_PHONE
            - WALKIN_CLOSET
            - WASHER_DRYER
            - WEIGHT_SCALE
            - WELCOME_GIFT
            - SPARE_ELECTRICAL_OUTLET_AVAILABLE_AT_DESK
            - NONREFUNDABLE_CHARGE_FOR_PETS
            - REFUNDABLE_DEPOSIT_FOR_PETS
            - SEPARATE_TUB_AND_SHOWER
            - ENTRANCE_TYPE_TO_GUEST_ROOM
            - CEILING_FAN
            - CNN_AVAILABLE
            - ELECTRICAL_ADAPTORS_AVAILABLE
            - BUFFET_BREAKFAST
            - ACCESSIBLE_ROOM
            - CLOSETS_IN_ROOM
            - DVD_PLAYER
            - MINIREFRIGERATOR
            - SEPARATE_LINE_BILLING_FOR_MULTILINE_PHONE
            - SELFCONTROLLED_HEATING_COOLING_SYSTEM
            - TOASTER
            - ANALOG_DATA_PORT
            - COLLECT_CALLS
            - INTERNATIONAL_CALLS
            - CARRIER_ACCESS
            - INTERSTATE_CALLS
            - INTRASTATE_CALLS
            - LOCAL_CALLS
            - LONG_DISTANCE_CALLS
            - OPERATORASSISTED_CALLS
            - CREDIT_CARD_ACCESS_CALLS
            - CALLING_CARD_CALLS
            - TOLL_FREE_CALLS
            - UNIVERSAL_AC_DC_ADAPTORS
            - BATHTUB_SEAT
            - CANOPY_POSTER_BED
            - CUPS_GLASSWARE
            - ENTERTAINMENT_CENTER
            - FAMILY_OVERSIZED_ROOM
            - HYPOALLERGENIC_BED
            - HYPOALLERGENIC_PILLOWS
            - LAMP
            - MEAL_INCLUDED_BREAKFAST
            - MEAL_INCLUDED_CONTINENTAL_BREAKFAST
            - MEAL_INCLUDED_DINNER
            - MEAL_INCLUDED_LUNCH
            - SHARED_BATHROOM
            - TELEPHONE_TDD_TEXTPHONE
            - WATER_BED
            - EXTRA_ADULT_CHARGE
            - EXTRA_CHILD_CHARGE
            - EXTRA_CHILD_CHARGE_FOR_ROLLAWAY_USE
            - MEAL_INCLUDED_FULL_AMERICAN_BREAKFAST
            - FUTON
            - MURPHY_BED
            - TATAMI_MATS
            - SINGLE_BED
            - ANNEX_ROOM
            - FREE_NEWSPAPER
            - HONEYMOON_SUITES
            - COMPLIMENTARY_HIGH_SPEED_INTERNET_IN_ROOM
            - MAID_SERVICE
            - PC_HOOKUP_IN_ROOM
            - SATELLITE_TELEVISION
            - VIP_ROOMS
            - CELL_PHONE_RECHARGER
            - DVR_PLAYER
            - IPOD_DOCKING_STATION
            - MEDIA_CENTER
            - PLUG_AND_PLAY_PANEL
            - SATELLITE_RADIO
            - VIDEO_ON_DEMAND
            - EXTERIOR_CORRIDORS
            - GULF_VIEW
            - ACCESSIBLE_ROOM_AMENITY
            - INTERIOR_CORRIDORS
            - MOUNTAIN_VIEW
            - OCEAN_VIEW
            - HIGH_SPEED_INTERNET_ACCESS_FEE
            - HIGH_SPEED_WIRELESS
            - PREMIUM_MOVIE_CHANNELS
            - SLIPPERS
            - FIRST_NIGHTERS_KIT
            - CHAIR_PROVIDED_WITH_DESK
            - PILLOW_TOP_MATTRESS
            - FEATHER_BED
            - DUVET
            - LUXURY_LINEN_TYPE
            - INTERNATIONAL_CHANNELS
            - PANTRY
            - DISHCLEANING_SUPPLIES
            - DOUBLE_VANITY
            - LIGHTED_MAKEUP_MIRROR
            - UPGRADED_BATHROOM_AMENITIES
            - VCR_PLAYER_AVAILABLE_AT_FRONT_DESK
            - INSTANT_HOT_WATER
            - OUTDOOR_SPACE
            - HINOKI_TUB
            - PRIVATE_POOL
            - HIGH_DEFINITION_HD_FLAT_PANEL_TELEVISION_32_INCHES_OR_GREATER
            - ROOM_WINDOWS_OPEN
            - BEDDING_TYPE_UNKNOWN_OR_UNSPECIFIED
            - FULL_BED
            - ROUND_BED
            - TV
            - CHILD_ROLLAWAY
            - DVD_PLAYER_AVAILABLE_AT_FRONT_DESK
            - VIDEO_GAME_PLAYER
            - VIDEO_GAME_PLAYER_AVAILABLE_AT_FRONT_DESK
            - DINING_ROOM_SEATS
            - FULL_SIZE_MIRROR
            - MOBILE_CELLULAR_PHONES
            - MOVIES
            - MULTIPLE_CLOSETS
            - PLATES_GLASSWARE
            - SAFE_LARGE_ENOUGH_TO_ACCOMMODATE_A_LAPTOP
            - BED_LINEN_THREAD_COUNT
            - BLACKOUT_CURTAIN
            - BLURAY_PLAYER
            - DEVICE_WITH_MP3
            - NO_ADULT_CHANNELS_OR_ADULT_CHANNEL_LOCK
            - NONALLERGENIC_ROOM
            - PILLOW_TYPE
            - SEATING_AREA_WITH_SOFA_CHAIR
            - SEPARATE_TOILET_AREA
            - WEB_ENABLED
            - WIDESCREEN_TV
            - OTHER_DATA_CONNECTION
            - PHONELINE_BILLED_SEPARATELY
            - SEPARATE_TUB_OR_SHOWER
            - VIDEO_GAMES
            - ROOF_VENTILATOR
            - CHILDRENS_PLAYPEN
            - PLUNGE_POOL
            - DVD_MOVIES
            - AIR_FILTRATION
          example: WEB_ENABLED
    HotelRoomData:
      type: object
      title: Hotel Room Data
      description: Details about a specific hotel room.
      required:
        - description
        - rateOptions
      properties:
        description:
          type: string
          description: A detailed description of the hotel room.
        rateOptions:
          type: array
          description: A list of rate options available for the hotel room.
          items:
            $ref: '#/components/schemas/RateOption'
        imageSets:
          type: array
          description: A list of image sets for the hotel room.
          items:
            $ref: '#/components/schemas/HotelImageSet'
        roomGroupKey:
          type: string
          description: A unique key identifying the room group.
        penaltyAmount:
          $ref: '#/components/schemas/Money'
          description: The amount of penalty applied for this room.
    HotelRoomInfo:
      type: object
      title: Room Info
      description: Information about the room.
      properties:
        roomType:
          $ref: '#/components/schemas/RoomType'
          description: The type of room.
        typeClassDescription:
          type: string
          description: A description of the room type class.
        roomClasses:
          type: array
          description: List of room class.
          items:
            $ref: '#/components/schemas/RoomClass'
        roomView:
          $ref: '#/components/schemas/RoomView'
          description: The type of room view.
    HotelRoomMealType:
      type: string
      description: Type of meal plan
      enum:
        - UNKNOWN
        - ALL_INCLUSIVE
        - AMERICAN
        - BED_AND_BREAKFAST
        - BUFFET_BREAKFAST
        - CARIBBEAN_BREAKFAST
        - CONTINENTAL_BREAKFAST
        - ENGLISH_BREAKFAST
        - EUROPEAN_PLAN
        - FAMILY_PLAN
        - FULL_BOARD
        - FULL_BREAKFAST
        - HALF_BOARD_MODIFIED_AMERICAN_PLAN
        - AS_BROCHURED
        - ROOM_ONLY
        - SELF_CATERING
        - BERMUDA
        - DINNER_BED_AND_BREAKFAST_PLAN
        - FAMILY_AMERICAN
        - BREAKFAST_MEAL_PLAN
        - MODIFIED
        - LUNCH_MEAL_PLAN
        - DINNER_MEAL_PLAN
        - BREAKFAST_AND_LUNCH
      example: ENGLISH_BREAKFAST
    HotelRoomMeals:
      type: object
      title: Meals
      description: Meal details for the room
      required:
        - mealType
      properties:
        mealsIncluded:
          type: array
          items:
            $ref: '#/components/schemas/HotelRoomMealsIncluded'
        mealType:
          $ref: '#/components/schemas/HotelRoomMealType'
          description: Meal type
    HotelRoomMealsIncluded:
      type: string
      description: Meals included, (Breakfast/Lunch/Dinner)
      enum:
        - BREAKFAST
        - LUNCH
        - DINNER
      example: BREAKFAST
    HotelRoomRate:
      type: object
      title: HotelRoomRate
      description: Rate details for hotel room
      required:
        - base
        - tax
      properties:
        base:
          $ref: '#/components/schemas/Money'
          description: Base amount
        tax:
          $ref: '#/components/schemas/Money'
          description: Tax
        roomFees:
          type: array
          items:
            $ref: '#/components/schemas/RoomFee'
        commission:
          $ref: '#/components/schemas/Commission'
          description: Commission
        taxBreakdown:
          $ref: '#/components/schemas/TaxBreakdown'
          description: Tax breakdown
        includesCommission:
          type: boolean
          description: Whether the rate includes commission
          example: false
    HotelSearchData:
      type: object
      title: Hotel Data
      description: Defines the structure for hotel data in the search results.
      required:
        - hotelDetailsKey
        - hotelSpec
        - distance
        - hasNegotiatedRates
        - rateInfo
      properties:
        hotelDetailsKey:
          type: string
          example: Cg5TUE9UTkFOQTozMDc3NhIgCgIIARIMCgoyMDI0LTA=
          description: Unique key for the hotel details.
        hotelSpec:
          $ref: '#/components/schemas/HotelSpec'
          description: Specifications and details of the hotel.
        distance:
          $ref: '#/components/schemas/Length'
          description: Distance from the search point.
        policyInfo:
          $ref: '#/components/schemas/CommonPolicyInfo'
          description: Policy information related to the hotel.
        hasNegotiatedRates:
          type: boolean
          example: false
          description: Indicates if the hotel has negotiated rates.
        preferences:
          type: array
          items:
            $ref: '#/components/schemas/Preference'
          description: List of preferences applicable to the hotel.
        rateInfo:
          $ref: '#/components/schemas/HotelSearchRateInfo'
          description: >-
            Rate related information for the cheapest rate available in the
            hotel.
        hotelCo2EmissionDetail:
          description: The co2 emission details for the hotel.
          $ref: '#/components/schemas/HotelCo2EmissionDetail'
    HotelSearchFilters:
      type: object
      title: Hotel Search Filters
      description: >-
        Filters to refine the search result. Based on the user selected filters,
        the list of matching hotels will be displayed.
      properties:
        prepaidQualifier:
          $ref: '#/components/schemas/PrepaidQualifier'
          description: Filter for prepaid qualifiers.
        priceRange:
          $ref: '#/components/schemas/SimpleMoneyRange'
          description: Filter based on range of starting average nightly prices for hotels.
        amenities:
          type: array
          items:
            $ref: '#/components/schemas/HotelAmenityType'
          description: >-
            List of preferred amenities to be included in the hotel search
            result.
        chainCodes:
          type: array
          items:
            type: string
          description: >
            List of preferred hotel chain codes to be included in the search
            result.

            A hotel chain is a company that owns or operates multiple hotels
            across different locations.

            These hotel chains have a unique code which can be used here as an
            array.
        radius:
          $ref: '#/components/schemas/Length'
          description: Radius to search within from a specified central point.
        starRatings:
          type: array
          items:
            type: integer
          description: >-
            Indicates the star quality rating of a hotel. Filter search result
            using preferred star ratings.
        nameQuery:
          type: string
          description: The name of the hotel to be included in the search query.
        eligibleForLoyalty:
          type: boolean
          example: false
          description: Include hotels that are eligible for loyalty points.
        showUnavailable:
          type: boolean
          example: false
          description: Include unavailable hotels.
        payByPoints:
          type: boolean
          example: false
          description: Include hotels that support payment by points.
        modifiableOnly:
          type: boolean
          example: false
          description: Include hotels that allow users to modify their booking.
        rateTypes:
          type: array
          items:
            $ref: '#/components/schemas/HotelRateType'
          description: List of rate type identifiers to filter the search results.
        propertyTypes:
          type: array
          items:
            $ref: '#/components/schemas/HotelPropertyType'
          description: >-
            Filter by property type such as a hotel, an apartment, bed and
            breakfast, and so on.
        refundableOnly:
          type: boolean
          example: false
          description: Include hotels that support refund of the booking.
        preferredOnly:
          type: boolean
          example: false
          description: Filter to show only preferred hotels.
    HotelSearchMetadata:
      type: object
      title: Hotel Search Metadata
      description: Includes metadata for a hotel search.
      required:
        - availableHotelChains
        - rateStatistics
        - showOnlyBaseFare
      properties:
        availableHotelChains:
          type: array
          description: A list of available hotel chains.
          items:
            $ref: '#/components/schemas/HotelChain'
        rateStatistics:
          $ref: '#/components/schemas/HotelRateStatistics'
        showOnlyBaseFare:
          type: boolean
          example: false
          description: Indicates if only base fare should be shown.
        sessionId:
          type: string
          description: Unique session identifier for the search.
    HotelSearchParams:
      type: object
      title: Hotel Search Parameters
      description: The parameters required for searching hotels.
      required:
        - occupancyDates
        - searchBy
      properties:
        occupancyDates:
          $ref: '#/components/schemas/OccupancyDates'
          description: Occupancy values and dates for the hotel search.
        searchBy:
          type: object
          discriminator:
            propertyName: searchType
            mapping:
              AIRPORT: '#/components/schemas/AirportSearch'
              COORDINATES: '#/components/schemas/CoordinatesSearch'
              HOTEL_CODE: '#/components/schemas/HotelCodeSearch'
              HOTEL_CODES: '#/components/schemas/MultiHotelCodesSearch'
          oneOf:
            - $ref: '#/components/schemas/AirportSearch'
            - $ref: '#/components/schemas/CoordinatesSearch'
            - $ref: '#/components/schemas/HotelCodeSearch'
            - $ref: '#/components/schemas/MultiHotelCodesSearch'
          description: >-
            Criteria used for searching hotels. The `searchType` field can
            contain one criteria per request.
    HotelSearchRateInfo:
      type: object
      title: Hotel Search Rate Information
      description: Contains detailed rate information for a hotel search.
      required:
        - averageNightlyRate
        - totalRate
        - isRefundable
      properties:
        averageNightlyRate:
          $ref: '#/components/schemas/HotelRoomRate'
          description: Average nightly rate for the hotel.
        totalRate:
          $ref: '#/components/schemas/HotelRoomRate'
          description: Total rate for the hotel stay.
        isRefundable:
          type: boolean
          description: Set to true if the hotel rate is refundable.
          example: true
        rewardPointsEarned:
          type: array
          description: Information about reward points earned.
          items:
            $ref: '#/components/schemas/RewardPointsEarned'
        promotionalOffers:
          type: array
          description: List of promotional offers applied to the hotel rate.
          items:
            $ref: '#/components/schemas/PromotionalOffer'
    HotelSearchRequest:
      type: object
      title: Hotel Search Request
      description: Defines the structure of a hotel search request.
      required:
        - searchParams
        - userId
      properties:
        searchParams:
          $ref: '#/components/schemas/HotelSearchParams'
        filters:
          $ref: '#/components/schemas/HotelSearchFilters'
        sortOptions:
          description: Sort options for the search results.
          type: array
          items:
            $ref: '#/components/schemas/HotelSortOptions'
        userId:
          $ref: '#/components/schemas/HotelUserId'
          description: Hotel User Identifier.
        paginationParams:
          $ref: '#/components/schemas/TokenBasedPaginationRequest'
    HotelSearchResponse:
      type: object
      title: Hotel Search Response
      description: Defines the structure of a hotel search response.
      required:
        - hotels
        - metadata
        - paginationParams
      properties:
        hotels:
          type: array
          description: List of hotels matching the search criteria.
          items:
            $ref: '#/components/schemas/HotelSearchData'
        metadata:
          $ref: '#/components/schemas/HotelSearchMetadata'
        paginationParams:
          $ref: '#/components/schemas/TokenBasedPaginationResponse'
    HotelSortOptions:
      type: object
      title: Hotel Sort Options
      description: Defines the sorting options for hotel search results.
      required:
        - sortBy
        - sortOrder
      properties:
        sortBy:
          type: string
          description: Sorting criteria for the hotel search results.
          enum:
            - PRICE
            - DISTANCE
            - SABRE_RATING
        sortOrder:
          type: string
          description: The order in which the search results should be displayed.
          enum:
            - ASCENDING
            - DESCENDING
    HotelSpec:
      type: object
      title: HotelSpec
      description: >
        Includes detailed specifications for a hotel, such as name, star rating,
        address, coordinates, amenities, and other information.
      required:
        - name
        - hotelId
        - address
        - coordinates
        - contactInfo
        - starRating
        - amenities
        - propertyTypes
      properties:
        name:
          type: string
          example: London Heathrow Marriott Hotel
          description: Name of the hotel.
        address:
          $ref: '#/components/schemas/PostalAddress'
          description: Address of the hotel.
        coordinates:
          $ref: '#/components/schemas/Latlng'
          description: Coordinates of the hotel.
        contactInfo:
          $ref: '#/components/schemas/ContactInfo'
          description: Contact information for the hotel.
        amenities:
          type: array
          description: List of hotel amenities.
          items:
            $ref: '#/components/schemas/HotelAmenity'
        descriptions:
          type: array
          description: List of hotel descriptions.
          items:
            $ref: '#/components/schemas/HotelDescription'
        imageSets:
          type: array
          description: List of hotel image sets.
          items:
            $ref: '#/components/schemas/HotelImageSet'
        hotelId:
          type: string
          example: SPOTNANA:1001
          description: Unique hotel identifier.
        brandCode:
          type: string
          example: MC
          description: The code of hotel brand.
        brandName:
          type: string
          example: Marriott Hotel Brands
          description: Brand name of the hotel.
        chainCode:
          type: string
          example: EM
          description: The code of hotel chain.
        chainName:
          type: string
          example: Marriott Hotels & Resorts
          description: Name of the hotel chain.
        propertyTypes:
          type: array
          description: List of property types for the hotel.
          items:
            $ref: '#/components/schemas/HotelPropertyType'
        starRating:
          $ref: '#/components/schemas/HotelStarRatingInfo'
          description: Hotel star rating detail.
        additionalAmenities:
          type: array
          description: Additional amenities provided by the hotel.
          items:
            type: string
        checkinTime:
          $ref: '#/components/schemas/TimeLocal'
          description: The check-in time for the hotel.
        checkoutTime:
          $ref: '#/components/schemas/TimeLocal'
          description: The check-out time for the hotel.
    HotelSpecialRequests:
      type: object
      title: HotelSpecialRequest
      description: Hotel special requests
      properties:
        roomLocations:
          type: array
          description: Room Location special request
          items:
            type: string
            enum:
              - HIGH_FLOOR
              - LOW_FLOOR
            example: HIGH_FLOOR
        roomFeatures:
          type: array
          description: Room Features List
          items:
            type: string
            enum:
              - CRIB
              - ROLLAWAY_BED
              - FEATHER_FREE_ROOM
              - ACCESSIBLE_ROOM
              - NEAR_ELEVATOR
            example: FEATHER_FREE_ROOM
        checkIn:
          type: string
          description: Early or Late Check-in
          enum:
            - EARLY_CHECK_IN
            - LATE_CHECK_IN
          example: LATE_CHECK_IN
        checkInTime:
          description: Requested time for check-in
          $ref: '#/components/schemas/TimeLocal'
        flightNumber:
          type: string
          description: Attach flight number
          example: AC1234
        additionalNote:
          type: string
          description: Free form text to describe special request
          example: Extra pillows and blankets for added comfort during the stay.
        accessibleFeatures:
          type: array
          description: Accessible Features List
          items:
            $ref: '#/components/schemas/HotelAccessibleFeatureType'
    HotelStarRatingInfo:
      type: object
      title: Hotel Start Rating Information
      description: Details about hotel star rating.
      required:
        - starRating
        - ratingType
      properties:
        starRating:
          type: number
          format: float
          example: 3
          description: Star rating of the hotel.
          minimum: 0
          maximum: 5
        ratingType:
          type: string
          title: Star Rating Type
          enum:
            - OFFICIAL
            - SELF_RATED
          description: Enum representing the type of star rating.
    HotelTraveler:
      type: object
      title: Traveler
      required:
        - travelerId
        - name
        - phoneNumber
        - email
      properties:
        travelerId:
          description: The ID of the traveler.
          $ref: '#/components/schemas/UserId'
        name:
          description: Name of the traveler.
          $ref: '#/components/schemas/Name'
        dob:
          description: Date of birth of the traveler.
          $ref: '#/components/schemas/DateModel'
        phoneNumber:
          description: Phone number of the traveler.
          $ref: '#/components/schemas/PhoneNumber'
        email:
          description: Email of the traveler.
          type: string
          example: <EMAIL>
        loyaltyInfo:
          description: Loyalty Info of the traveler.
          $ref: '#/components/schemas/LoyaltyInfo'
        adhocInfo:
          description: >-
            Adhoc Info of the traveler. It is only present if the traveler is
            adhoc user.
          properties:
            profileOwnerId:
              description: The profile owner of the adhoc user.
              $ref: '#/components/schemas/UserId'
    HotelUserId:
      type: object
      title: Hotel User Identifier
      description: >-
        An identifier for the user involved in the hotel booking or search
        process.
      discriminator:
        propertyName: userIdType
        mapping:
          USER_ID: '#/components/schemas/UserIdSearch'
          REGISTRAR_USER_ID: '#/components/schemas/RegistrarUserIdSearch'
      oneOf:
        - $ref: '#/components/schemas/UserIdSearch'
        - $ref: '#/components/schemas/RegistrarUserIdSearch'
    HotelValidateRebookingRequest:
      type: object
      title: HotelValidateRebookingRequest
      required:
        - cancelSourcePnrId
        - travelers
        - tripId
      properties:
        cancelSourcePnrId:
          type: string
          description: Source PNR ID that needs to be validated.
          example: 1cf76aba18e4015f
        travelers:
          description: List of travelers
          type: array
          items:
            $ref: '#/components/schemas/Traveler'
        tripId:
          description: The unique ID created for the respective trip.
          $ref: '#/components/schemas/TripId'
    HotelValidateRebookingResponse:
      type: object
      title: HotelValidateRebookingResponse
      description: Hotel Validate rebooking response
      required:
        - isValid
        - spotnanaPnrId
      properties:
        isValid:
          type: boolean
          description: Indicates if the validation is successful.
          example: true
        spotnanaPnrId:
          type: string
          description: >-
            The Spotnana PNR ID of the corresponding source PNR ID provided in
            the request.
          example: 1cf76aba18e4015f
    IdentityDocument:
      type: object
      title: IdentityDocument
      description: >
        Identity document details. Currently supported documents are passport,
        immigration document, 

        known traveler number, redress number and national document.
      oneOf:
        - $ref: '#/components/schemas/PassportWrapper'
        - $ref: '#/components/schemas/ImmigrationDocumentWrapper'
        - $ref: '#/components/schemas/RedressNumberWrapper'
        - $ref: '#/components/schemas/KnownTravelerNumberWrapper'
        - $ref: '#/components/schemas/NationalDocWrapper'
    Image:
      type: object
      title: Image
      description: >-
        An image with meta data. Either the `data` or `url` property must be
        supplied to load the image.
      properties:
        data:
          type: string
          format: byte
          example: 6935813e12584abda0e43d71cd2ea260
        dimensions:
          $ref: '#/components/schemas/Dimensions'
        url:
          type: string
          example: >-
            https://static.wixstatic.com/media/73f2e2_6935813e12584abda0e43d71cd2ea260~mv2.png/v1/fill/w_630,h_94,al_c,q_85,usm_0.66_1.00_0.01/Spotnana%403x.webp
    ImageGroup:
      type: object
      title: ImageGroup
      description: Contains different sizes of the same image.
      required:
        - images
      properties:
        caption:
          type: string
          description: Caption for the image.
          example: Exterior
        images:
          type: array
          description: List of images.
          items:
            $ref: '#/components/schemas/Image'
    ImmigrationDocument:
      type: object
      title: ImmigrationDocument
      description: Immigration document details.
      required:
        - docId
        - expiryDate
        - issueCountry
      properties:
        authorizedStayDuration:
          description: Duration of the stay authorized by the immigration document.
          $ref: '#/components/schemas/Duration'
        docId:
          type: string
          description: The ID of the immigration document.
          example: ImmigrationDocumentID
        expiryDate:
          description: The date on which the immigration document expires.
          $ref: '#/components/schemas/DateModel'
        issueCountry:
          type: string
          description: The country that issued the immigration document.
          example: IN
        issuedDate:
          description: The date on which the immigration document was issued.
          $ref: '#/components/schemas/DateModel'
        nationalityCountry:
          type: string
          example: IN
        reentryRequirementDuration:
          $ref: '#/components/schemas/Duration'
        type:
          type: string
          enum:
            - UNKNOWN
            - VISA
          example: VISA
    ImmigrationDocumentWrapper:
      type: object
      title: ImmigrationDocumentWrapper
      properties:
        immigrationDoc:
          $ref: '#/components/schemas/ImmigrationDocument'
    IncludeLocation:
      type: string
      title: IncludeLocation
      description: >-
        Various traveler notifications (e.g. emails) where the responses to this
        field should be included.
      enum:
        - BOOKING_CONFIRMATION_EMAILS
        - APPROVAL_EMAILS
        - COMPANY_REPORTS
        - CONSOLIDATED_ITINERARY_EMAILS
      example: APPROVAL_EMAILS
    Int32Range:
      type: object
      properties:
        min:
          type: integer
          format: int32
          description: Minimum value - inclusive.
        max:
          type: integer
          format: int32
          description: Maximum value - inclusive.
    Int32RangeWrapper:
      type: object
      title: Int32RangeWrapper
      properties:
        iRange:
          $ref: '#/components/schemas/Int32Range'
    Int32Wrapper:
      type: object
      title: Int32Wrapper
      properties:
        i:
          type: integer
          format: int32
    Int64Wrapper:
      type: object
      title: Int64Wrapper
      properties:
        l:
          type: integer
          format: int64
    IntListWrapper:
      type: object
      title: IntListWrapper
      properties:
        iList:
          type: array
          items:
            type: integer
            format: int32
    ItemType:
      type: string
      description: Type of payment item eligible for this fop rule
      enum:
        - SERVICE_FEE
        - TRAVEL_TICKET
        - SEAT
        - BAGGAGE
        - EARLY_BIRD
      example: SEAT
    KeywordWithReasonListWrapper:
      type: object
      title: KeywordWithReasonListWrapper
      properties:
        keywordWithReasonList:
          $ref: '#/components/schemas/KeywordsWithReasonList'
    KeywordsWithReasonList:
      type: object
      properties:
        keywords:
          type: array
          items:
            $ref: '#/components/schemas/RestrictedKeywordsWithReason'
    KnownTravelerNumber:
      type: object
      title: KnownTravelerNumber
      description: Information about the Known Traveler Number (KTN).
      required:
        - number
        - issueCountry
      properties:
        number:
          type: string
          example: '12345'
        issueCountry:
          type: string
          example: US
    KnownTravelerNumberWrapper:
      type: object
      title: KnownTravelerNumberWrapper
      properties:
        ktn:
          $ref: '#/components/schemas/KnownTravelerNumber'
    Latlng:
      title: Latlng
      description: Latitude and Longitude for a Location
      type: object
      required:
        - latitude
        - longitude
      properties:
        latitude:
          type: number
          description: Latitude of the Location
          format: double
          example: 77.1025
        longitude:
          type: number
          description: Longitude of the Location
          format: double
          example: 28.7041
    LegalEntityId:
      type: object
      title: LegalEntityId
      description: The ID of the legal entity.
      required:
        - id
      properties:
        id:
          type: string
          example: fc1ccbce-8413-4fe9-b233-a324dfbe7421
    LegalEntityIdListWrapper:
      type: object
      title: LegalEntityIdListWrapper
      properties:
        legalEntityIdList:
          type: array
          items:
            $ref: '#/components/schemas/LegalEntityId'
    LegalEntityIdWrapper:
      type: object
      title: LegalEntityIdWrapper
      properties:
        legalEntityId:
          $ref: '#/components/schemas/LegalEntityId'
    Length:
      type: object
      title: Length
      description: Specifies the length or a distance.
      required:
        - length
        - unit
      properties:
        length:
          type: number
          description: Distance from search point.
          format: double
          example: 150
        unit:
          type: string
          description: Unit of measure being applied.
          enum:
            - UNKNOWN_UNIT
            - KM
            - MILE
          example: MILE
    LengthWrapper:
      type: object
      title: LengthWrapper
      properties:
        length:
          $ref: '#/components/schemas/Length'
    Location:
      title: Location
      description: Location details
      type: object
      required:
        - name
      properties:
        countryCode:
          type: string
          description: Unique country code for a location
          example: TR
        countryName:
          type: string
          description: Full name of the country
          example: Turkey
        googlePlaceId:
          type: string
          description: Unique place ID for the location assigned by Google
          example: ChIJL_P_CXMEDTkRw0ZdG-0GVvw
        latlong:
          $ref: '#/components/schemas/Latlng'
        name:
          type: string
          description: Full name of the Location
          example: Denver
        stateName:
          type: string
          description: Full name of the state
          example: Colorado
    LoyaltyInfo:
      type: object
      title: LoyaltyInfo
      description: Loyalty Info details.
      required:
        - id
        - type
        - issuedBy
      properties:
        appliedTo:
          type: array
          items:
            type: string
            example: TAJ
        id:
          type: string
          example: firstId
        issuedBy:
          type: string
          example: firstIssuedBy
        type:
          type: string
          enum:
            - UNKNOWN_TYPE
            - AIR
            - HOTEL
            - CAR
            - RAIL
          example: AIR
    MealPref:
      type: object
      title: MealPref
      description: Meal preferences.
      properties:
        exclMealPrefs:
          type: array
          items:
            $ref: '#/components/schemas/MealType'
        inclMealPrefs:
          type: array
          items:
            $ref: '#/components/schemas/MealType'
        specialMealDescription:
          type: string
          example: Veg only meal
    MealType:
      type: string
      title: MealType
      description: Type of the meal
      enum:
        - UNKNOWN_MEAL
        - AVML
        - BBML
        - BLML
        - CHML
        - DBML
        - FPML
        - GFML
        - HFML
        - HNML
        - KSML
        - LCML
        - LFML
        - LPML
        - LSML
        - MOML
        - NLML
        - NSML
        - ORML
        - PFML
        - RVML
        - SFML
        - SPML
        - VGML
        - VJML
        - VLML
        - VOML
      example: VGML
    Money:
      type: object
      title: Money
      description: >
        Money object containing details such as the amount, the currency code,
        and the converted amount.
      required:
        - amount
        - currencyCode
      properties:
        amount:
          type: number
          format: double
          description: The numeric value for the amount of money.
          example: 510
        currencyCode:
          type: string
          description: >-
            The 3-letter currency code for the money amount (defined using ISO
            4217 standard).
          example: GBP
        convertedAmount:
          type: number
          format: double
          description: >
            The converted currency and amount that has been converted (if a
            currency conversion has been requested).

            For example, if the call requests that money be sent in a specified
            currency (because the frontend requested

            the backend to send money in the user's preferred currency).
          example: 715.42
        convertedCurrency:
          type: string
          description: >-
            The 3-letter currency code for the converted currency (defined using
            ISO 4217 standard).
          example: USD
        otherCoinage:
          type: array
          title: OtherCoinage
          description: >-
            List of the dollar amount in other coinage systems like reward
            points, cryptocurrency etc.
          items:
            type: object
            properties:
              coinageCode:
                $ref: '#/components/schemas/PaymentMethod'
                description: Payment method
              amount:
                type: number
                format: double
                example: 1000
              conversionRate:
                type: number
                format: double
                description: 1 coin in this system equals to how many currency value
                example: 0.01
              preferredCurrencyConversionRate:
                type: number
                format: double
                description: 1 coin in this system equals to how many currency value
                example: 0.01
    MoneyWrapper:
      type: object
      title: MoneyWrapper
      properties:
        money:
          $ref: '#/components/schemas/Money'
    MultiHotelCodesSearch:
      type: object
      title: Multi-Hotel Codes Search
      required:
        - searchType
        - hotelCodes
      properties:
        searchType:
          type: string
          example: HOTEL_CODES
        hotelCodes:
          type: array
          items:
            type: string
          description: List of hotel codes to be included in the search result.
    Name:
      type: object
      title: Name
      description: Full name containing first, middle, last (family) names, and suffix.
      required:
        - given
        - family1
      properties:
        family1:
          type: string
          description: Last (family) name.
          example: Gandas
        family2:
          type: string
          example: FamilyTwo
        given:
          type: string
          description: First (given) name.
          example: Vichitr
        middle:
          type: string
          description: Middle name.
          example: Kumar
        suffix:
          $ref: '#/components/schemas/NameSuffix'
          description: Suffix used with the name. For example SR or JR.
          example: SR
        preferred:
          type: string
          description: >-
            Informal preferred name added by traveler. This is not used on any
            PNR or tickets
          example: Don
    NameSuffix:
      title: NameSuffix
      description: Suffix for name
      type: string
      enum:
        - NAME_SUFFIX_UNKNOWN
        - SR
        - JR
        - MD
        - PHD
        - II
        - III
        - IV
        - DO
        - ATTY
        - V
        - VI
        - ESQ
        - DC
        - DDS
        - VM
        - JD
        - SECOND
        - THIRD
      example: SR
    NationalDoc:
      type: object
      title: NationalDoc
      description: National Document Id details.
      required:
        - docId
        - issueCountry
      properties:
        docId:
          type: string
          description: Unique id identifying the national document.
          example: NationalDocId
        issueCountry:
          type: string
          description: IS0 2 letter country code of the country issuing this id.
          example: IN
        issuedDate:
          $ref: '#/components/schemas/DateModel'
        expiryDate:
          $ref: '#/components/schemas/DateModel'
        type:
          type: string
          enum:
            - DNI
            - NIE
          example: DNI
    NationalDocWrapper:
      type: object
      title: NationalDocWrapper
      properties:
        nationalDoc:
          $ref: '#/components/schemas/NationalDoc'
    NumStopsPref:
      type: object
      title: NumStopsPref
      description: Preferred number of stops.
      required:
        - numOfStops
      properties:
        numOfStops:
          type: integer
          format: int32
          example: 34
    Occupancy:
      type: object
      title: Occupancy
      required:
        - numAdults
      properties:
        numAdults:
          type: integer
          example: 1
          minimum: 1
          description: The number of adults staying in the room.
        numChildren:
          type: integer
          example: 0
          description: The number of children staying in the room.
    OccupancyDates:
      type: object
      title: Occupancy Dates
      required:
        - occupancy
        - checkInDate
        - checkOutDate
      properties:
        occupancy:
          type: array
          items:
            $ref: '#/components/schemas/Occupancy'
          description: >-
            List of occupancy details including the number of adults and
            children.
        checkInDate:
          $ref: '#/components/schemas/DateTimeLocal'
          description: The check-in date for the hotel stay.
        checkOutDate:
          $ref: '#/components/schemas/DateTimeLocal'
          description: The check-out date for the hotel stay.
    Office:
      type: object
      title: Office
      description: Office details.
      required:
        - id
        - name
      properties:
        address:
          $ref: '#/components/schemas/PostalAddress'
        id:
          $ref: '#/components/schemas/OfficeId'
        name:
          type: string
          example: Office
        latlng:
          $ref: '#/components/schemas/Latlng'
        taxId:
          type: string
          example: '133232'
    OfficeId:
      type: object
      title: OfficeId
      description: The unique ID of the office.
      required:
        - id
      properties:
        id:
          type: string
          description: The value of the unique ID for the office.
          example: 531ccbce-8413-4fe9-b233-a324dfbe7421
    OfficeIdListWrapper:
      type: object
      title: OfficeIdListWrapper
      properties:
        officeIdList:
          type: array
          description: A list of office IDs.
          items:
            $ref: '#/components/schemas/OfficeId'
    OfficeIdWrapper:
      type: object
      title: OfficeIdWrapper
      properties:
        officeId:
          $ref: '#/components/schemas/OfficeId'
    Option:
      type: object
      title: Option
      description: Answer option for a question
      required:
        - displayCode
      properties:
        displayCode:
          type: string
          description: The code which is sent in answer response.
        displayValue:
          type: string
          description: The text to be displayed to the user beside this option.
    OptionInfo:
      type: object
      title: OptionInfo
      description: Options related information for the question.
      required:
        - source
      properties:
        source:
          $ref: '#/components/schemas/OptionSource'
          default: MANUAL
        sourceMetadata:
          $ref: '#/components/schemas/OptionSourceMetadata'
        totalNumOptions:
          type: integer
          description: Total number of options
        options:
          type: array
          description: >
            Available options for the question. This will contain only max 10
            options if only 

            summary is requested.
          items:
            $ref: '#/components/schemas/Option'
    OptionSource:
      type: string
      enum:
        - MANUAL
        - COMPANY_CONFIG
      description: Option source
      example: MANUAL
    OptionSourceMetadata:
      type: object
      title: OptionSourceMetadata
      description: Metadata information for the option source.
      oneOf:
        - $ref: '#/components/schemas/CompanyConfigSourceWrapper'
    OrganizationAgencyId:
      type: object
      title: OrganizationAgencyId
      description: Agency id for the organization.
      deprecated: true
      required:
        - id
      properties:
        id:
          type: string
    OrganizationId:
      type: object
      title: OrganizationId
      description: Organization id for traveler.
      required:
        - id
      properties:
        id:
          type: string
    OwnershipLabel:
      title: OwnershipLabel
      type: string
      description: >-
        Ownership label of the card whether it is personal, corporate or
        central.
      enum:
        - CORPORATE
        - PERSONAL
        - CENTRAL
    PageSize:
      type: object
      title: PageSize
      description: The page size.
      required:
        - requestType
      properties:
        requestType:
          type: string
          description: Discriminator for the type of pagination.
          default: PAGE_SIZE
        pageSize:
          type: integer
          description: The page size.
          example: 10
          default: 50
          maximum: 100
    PageToken:
      type: object
      title: PageToken
      description: The page token.
      required:
        - requestType
        - pageToken
      properties:
        requestType:
          type: string
          default: PAGE_TOKEN
          description: Discriminator for the type of pagination.
        pageToken:
          type: string
          description: The page token.
          example: token
    Passport:
      type: object
      title: Passport
      description: Passport details.
      required:
        - docId
        - expiryDate
        - issueCountry
        - nationalityCountry
      properties:
        docId:
          type: string
          example: PassportID
        expiryDate:
          $ref: '#/components/schemas/DateModel'
        issueCountry:
          type: string
          example: IN
        issuedDate:
          $ref: '#/components/schemas/DateModel'
        nationalityCountry:
          type: string
          example: IN
        type:
          type: string
          enum:
            - UNKNOWN
            - REGULAR
          example: REGULAR
    PassportWrapper:
      type: object
      title: PassportWrapper
      properties:
        passport:
          $ref: '#/components/schemas/Passport'
    PaymentInfo:
      type: object
      title: PaymentInfo
      description: Payment information.
      required:
        - card
      properties:
        applicableTo:
          type: array
          items:
            type: string
            enum:
              - UNKNOWN_APPLICABLE_TO
              - AIR
              - HOTEL
              - RAIL
              - CAR
              - SERVICE_FEE
            example: HOTEL
        card:
          $ref: '#/components/schemas/Card'
        accessType:
          $ref: '#/components/schemas/CreditCardAccessType'
        access:
          $ref: '#/components/schemas/CreditCardAccess'
    PaymentItem:
      type: object
      description: The payment item eligible for associated payment rules
      properties:
        itemType:
          $ref: '#/components/schemas/ItemType'
          description: Type of payment item eligible for this fop rule
        fareComponent:
          type: array
          items:
            $ref: '#/components/schemas/FareComponent'
            description: List of fare components of associated payment item
    PaymentMethod:
      type: string
      description: Payment method
      enum:
        - PAYMENT_METHOD_UNKNOWN
        - CREDIT_CARD
        - BREX_POINTS
        - CASH
        - QANTAS_POINTS
        - VENDOR_PROGRAM_PAYMENT
        - DELAYED_INVOICING
        - FLIGHT_CREDITS
        - QANTAS_TRAVEL_FUND
        - CUSTOM_VIRTUAL_PAYMENT
      example: BREX_POINTS
    PenaltyAmount:
      type: object
      title: PenaltyAmount
      description: Penalty Amount
      properties:
        penaltyAmount:
          $ref: '#/components/schemas/Money'
    PenaltyPercentage:
      type: object
      title: PenaltyPercentage
      description: Penalty percentage
      properties:
        penaltyPercentage:
          type: number
          format: double
          example: 10
    PercentageWrapper:
      type: object
      title: PercentageWrapper
      properties:
        percentage:
          type: number
          format: double
    Persona:
      type: string
      title: Persona
      description: Persona of the user
      enum:
        - UNKNOWN_PERSONA
        - EMPLOYEE
        - GUEST
        - PERSONAL
        - RELATIVE
        - ADHOC
      example: EMPLOYEE
    PersonaListWrapper:
      type: object
      title: PersonaListWrapper
      properties:
        personaList:
          type: array
          items:
            $ref: '#/components/schemas/Persona'
    PersonaWrapper:
      type: object
      title: PersonaWrapper
      properties:
        persona:
          $ref: '#/components/schemas/Persona'
    PhoneNumber:
      type: object
      title: PhoneNumber
      description: Properly formatted phone number.
      properties:
        countryCode:
          type: integer
          format: int32
          description: two digit country code
          example: 91
        countryCodeSource:
          type: string
          enum:
            - UNSPECIFIED
            - FROM_NUMBER_WITH_PLUS_SIGN
            - FROM_NUMBER_WITH_IDD
            - FROM_NUMBER_WITHOUT_PLUS_SIGN
            - FROM_DEFAULT_COUNTRY
          example: FROM_NUMBER_WITH_PLUS_SIGN
        extension:
          description: phone number extension
          type: string
          example: '222'
        isoCountryCode:
          description: ISO alpha-2 code
          type: string
          example: IN
        italianLeadingZero:
          type: boolean
          default: false
          example: true
        nationalNumber:
          type: integer
          format: int64
          example: 8150
        numberOfLeadingZeros:
          type: integer
          format: int32
          default: 0
          example: 1
        preferredDomesticCarrierCode:
          type: string
          example: '7'
        rawInput:
          type: string
          example: '77777'
        type:
          type: string
          enum:
            - UNKNOWN_TYPE
            - MOBILE
            - LANDLINE
          example: MOBILE
    PolicyAction:
      type: object
      description: Action that is required / done for policy.
      oneOf:
        - $ref: '#/components/schemas/PolicyFlagActionWrapper'
        - $ref: '#/components/schemas/PolicyHideActionWrapper'
        - $ref: '#/components/schemas/PolicyAlertOnSelectionActionWrapper'
        - $ref: '#/components/schemas/PolicyTakeApprovalActionWrapper'
        - $ref: '#/components/schemas/PolicyPreventBookingActionWrapper'
    PolicyAlertOnSelectionAction:
      type: object
      title: PolicyAlertOnSelectionAction
      properties:
        message:
          type: string
    PolicyAlertOnSelectionActionWrapper:
      type: object
      title: PolicyAlertOnSelectionActionWrapper
      properties:
        alertOnSelection:
          $ref: '#/components/schemas/PolicyAlertOnSelectionAction'
    PolicyConstValue:
      type: object
      oneOf:
        - $ref: '#/components/schemas/Int32Wrapper'
        - $ref: '#/components/schemas/Int64Wrapper'
        - $ref: '#/components/schemas/StringWrapper'
        - $ref: '#/components/schemas/DoubleWrapper'
        - $ref: '#/components/schemas/BoolWrapper'
        - $ref: '#/components/schemas/IntListWrapper'
        - $ref: '#/components/schemas/DoubleListWrapper'
        - $ref: '#/components/schemas/StringListWrapper'
        - $ref: '#/components/schemas/MoneyWrapper'
        - $ref: '#/components/schemas/LengthWrapper'
        - $ref: '#/components/schemas/PostalAddressWrapper'
        - $ref: '#/components/schemas/UserOrgIdWrapper'
        - $ref: '#/components/schemas/LegalEntityIdWrapper'
        - $ref: '#/components/schemas/OfficeIdWrapper'
        - $ref: '#/components/schemas/UserOrgIdListWrapper'
        - $ref: '#/components/schemas/LegalEntityIdListWrapper'
        - $ref: '#/components/schemas/OfficeIdListWrapper'
        - $ref: '#/components/schemas/RatingWrapper'
        - $ref: '#/components/schemas/PercentageWrapper'
        - $ref: '#/components/schemas/Int32RangeWrapper'
        - $ref: '#/components/schemas/DoubleRangeWrapper'
        - $ref: '#/components/schemas/PersonaWrapper'
        - $ref: '#/components/schemas/PersonaListWrapper'
        - $ref: '#/components/schemas/TravelClassHierarchyWrapper'
        - $ref: '#/components/schemas/KeywordWithReasonListWrapper'
        - $ref: '#/components/schemas/WorkerTypeWrapper'
        - $ref: '#/components/schemas/WorkerTypeListWrapper'
    PolicyFlagAction:
      type: object
      title: PolicyFlagAction
      properties:
        message:
          type: string
    PolicyFlagActionWrapper:
      type: object
      title: PolicyFlagActionWrapper
      properties:
        flag:
          $ref: '#/components/schemas/PolicyFlagAction'
    PolicyHideActionWrapper:
      type: object
      title: PolicyHideActionWrapper
      properties:
        hide:
          type: boolean
    PolicyPredicate:
      type: string
      description: This contains the enum mentioning the policy rule which was violated.
      enum:
        - UNKNOWN_PREDICATE_STRING
        - MAX_FARE_PER_TRAVELLER_VIOLATION
        - FARE_MORE_THAN_MINIMUM
        - FARE_MORE_THAN_MEDIAN
        - FARE_LESS_THAN_MEDIAN
        - FARE_MORE_THAN_LLF
        - MAX_FARE_PER_TRAVELLER_VIOLATION_INCLUDING_TAX
        - MAX_FARE_PER_TRAVELLER_VIOLATION_EXCLUDING_TAX
        - HOTEL_PAYMENT_OPTIONS_VIOLATION
        - RAIL_BOOKING_WINDOW_GAP_VIOLATION
        - RAIL_TRAVEL_CLASS_VIOLATION
        - RAIL_TICKET_REFUNDABLE_VIOLATION
        - RAIL_MAX_BOOKING_PRICE_VIOLATION_INCLUDING_TAX
        - RAIL_MAX_BOOKING_PRICE_VIOLATION_EXCLUDING_TAX
        - AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_INCLUDING_TAX
        - AIR_MAX_PRICE_MORE_THAN_LLF_VIOLATION_EXCLUDING_TAX
        - HOTEL_RESTRICTED_KEYWORDS_VIOLATION
        - RESTRICTED_LOCATION_VIOLATION
        - FLIGHT_ADVANCE_BOOKING_WINDOW_DOMESTIC_VIOLATION
        - FLIGHT_ADVANCE_BOOKING_WINDOW_INTERNATIONAL_VIOLATION
        - FLIGHT_ADVANCE_BOOKING_WINDOW_VIOLATION
        - ITINERARY_WITHIN_EVENT_TRAVEL_WINDOW
        - HOTEL_IN_ALLOWED_HOTEL_LIST
        - PAYMENT_ACCESS_VIOLATION
        - AIRPORT_IN_ALLOWED_AIRPORT_LIST
        - ITINERARY_TYPE_IS_NOT_IN_ALLOWED_BOOKING_TYPES
        - PAYMENT_AIR_ADDON_VIOLATION
        - MAX_HOTEL_BOOKING_PRICE_INCLUDING_TAX
        - MAX_HOTEL_BOOKING_PRICE_EXCLUDING_TAX
        - AIR_NUM_TRAVELERS_ALLOWED
        - PREFERRED_VENDOR_VIOLATION
        - SEAT_ADDON_VIOLATION
        - BAGGAGE_ADDON_VIOLATION
        - EARLY_BIRD_ADDON_VIOLATION
        - WIFI_ADDON_VIOLATION
        - RESTRICTED_BOOKING_VIOLATION
        - HIGHEST_ALLOWED_CABIN_VIOLATION
        - LOWEST_FARE_PER_HOTEL_PROPERTY_VIOLATION
      x-ignoreBreakingChanges:
        - PolicyPredicate->LOWEST_FARE_PER_HOTEL_PROPERTY_VIOLATION
    PolicyPreventBookingAction:
      type: object
      title: PolicyPreventBookingAction
      description: Whether to allow booking if a rule is violated.
      properties:
        prevent:
          type: boolean
          description: True if booking is to be blocked if rule is violated, else false
        reason:
          type: string
          description: >-
            Reason describing why was that specific itinerary not allowed to
            book.
    PolicyPreventBookingActionWrapper:
      type: object
      title: PolicyPreventBookingActionWrapper
      properties:
        preventBooking:
          $ref: '#/components/schemas/PolicyPreventBookingAction'
    PolicyRuleResultInfo:
      type: object
      properties:
        violationInfos:
          type: array
          items:
            $ref: '#/components/schemas/PolicyViolationInfo'
        subViolationInfos:
          type: array
          description: >
            In case of complex rules this will contain extra information as to
            how the rule was 

            calculated.
          items:
            $ref: '#/components/schemas/PolicyViolationInfo'
        actions:
          type: array
          description: Followed actions if rule was satisfied else violated actions.
          items:
            $ref: '#/components/schemas/PolicyAction'
    PolicyTakeApprovalAction:
      type: object
      title: PolicyTakeApprovalAction
      properties:
        numHierarchyLevels:
          type: integer
          format: int32
          description: >
            How many levels of hierarchy should approval be taken? If it's just
            immediate manager, 

            this value would be 1. If it's manager and their manager, this would
            ge 2 and so on.
        positionTitles:
          type: array
          description: >
            What position in the cost center or department. For eg, any business
            class upgrade 

            might require VP approval.
          items:
            type: string
        userOrgIds:
          type: array
          description: >
            The specific users from whom to take approval. For eg, say for a
            company, all approvals

            go through Adam.
          items:
            $ref: '#/components/schemas/UserOrgId'
        allRequired:
          type: boolean
          description: >
            This tells whether all the people above needs to approve or if any
            of these approve, 

            it is sufficient.
        hardApprovalRequired:
          type: boolean
          description: Whether to take soft approval (false) or hard approval (true).
    PolicyTakeApprovalActionWrapper:
      type: object
      title: PolicyTakeApprovalActionWrapper
      properties:
        takeApproval:
          $ref: '#/components/schemas/PolicyTakeApprovalAction'
    PolicyType:
      type: string
      description: Policy type.
      title: PolicyType
      enum:
        - DEFAULT
        - GROUP
        - INTERNAL
      example: GROUP
    PolicyViolationInfo:
      type: object
      description: Policy Violation info.
      title: PolicyViolationInfo
      properties:
        predicateString:
          type: string
        predicate:
          $ref: '#/components/schemas/PolicyPredicate'
        expectedValue:
          $ref: '#/components/schemas/PolicyConstValue'
        actualValue:
          $ref: '#/components/schemas/PolicyConstValue'
    PostalAddress:
      title: PostalAddress
      description: Postal Address Details
      type: object
      required:
        - addressLines
        - regionCode
      properties:
        addressLines:
          description: Address lines
          type: array
          items:
            type: string
            example: Golden Gate Bridge
        administrativeArea:
          type: string
          description: >
            Code of administrative area. For example: DL for Delhi, India.

            Highest administrative subdivision which is used for postal

            addresses of a country or region.

            For example, this can be a state, a province, an oblast, or a
            prefecture.

            Specifically, for Spain this is the province and not the autonomous

            community (e.g. "Barcelona" and not "Catalonia").

            Many countries don't use an administrative area in postal addresses.
            E.g.

            in Switzerland this should be left unpopulated.
          example: CA
        administrativeAreaName:
          type: string
          description: >
            Name of administrative area. This is full name corresponding to
            administrativeArea. 

            Like Delhi for DL area code. For some places, code and name maybe
            same as well like Tokyo.
          example: California
        description:
          description: Address description
          type: string
          example: San Francisco Home
        isDefault:
          description: >-
            Whether this address is default address in case multiple addresses
            are specified.
          type: boolean
          example: true
        languageCode:
          description: >
            BCP-47 language code of the contents of this address (if known).
            This is often the UI 

            language of the input form or is expected to match one of the
            languages used in the 

            address' country/region, or their transliterated equivalents.

            This can affect formatting in certain countries, but is not critical
            to the correctness 

            of the data and will never affect any validation or other
            non-formatting related operations.

            Examples: "zh-Hant", "ja", "ja-Latn", "en".
          type: string
          example: en
        locality:
          description: Generally refers to the city/town portion of the address.
          type: string
          example: San Francisco
        locationCode:
          description: >-
            IATA 3-letter location code. See
            https://www.iata.org/en/services/codes.
          type: string
          example: LAX
        organization:
          description: The name of the organization at the address.
          type: string
          example: Spotnana
        postalCode:
          description: >-
            Postal code of the address. This is a required field when setting
            for a user/legal entity/company etc.
          type: string
          example: '94130'
        continentCode:
          description: 2 letter continent code of the continent this address falls in.
          type: string
          example: AF
        recipients:
          description: The recipient at the address.
          type: array
          items:
            type: string
        regionCode:
          description: Region code of the country/region of the address.
          type: string
          example: US
        regionName:
          description: Region name of the country/region of the address.
          type: string
          example: America
        revision:
          type: integer
          format: int32
          example: 1
        sortingCode:
          description: >
            Additional, country-specific, sorting code. This is not used

            in most regions. Where it is used, the value is either a string like

            "CEDEX", optionally followed by a number (e.g. "CEDEX 7"), or just a
            number

            alone, representing the "sector code" (Jamaica), "delivery area
            indicator"

            (Malawi) or "post office indicator" (e.g. Côte d'Ivoire).
          type: string
          example: Jamaica
        sublocality:
          description: >-
            Sublocality of the address. This can be neighborhoods, boroughs,
            districts.
          type: string
        timezone:
          description: Time zone of the address.
          type: string
          example: America/Los_Angeles
        coordinates:
          description: Map coordinates of the address.
          $ref: '#/components/schemas/Latlng'
    PostalAddressWrapper:
      type: object
      title: PostalAddressWrapper
      properties:
        postalAddress:
          $ref: '#/components/schemas/PostalAddress'
    PreBookAnswers:
      type: object
      properties:
        answers:
          type: array
          items:
            $ref: '#/components/schemas/EntityAnswer'
        preBookQuestionResponseId:
          deprecated: true
          type: string
          description: The unique id sent back in the pre book questions API response
    PreCheckoutQuestionType:
      type: string
      enum:
        - UNKNOWN_CHECKOUT_QUESTION_TYPE
        - USER_DEFINED_QUESTION
        - OOP_REASON_CODE
      description: >
        Types of pre-checkout questions.

        USER_DEFINED_QUESTION the default question type for all pre checkout
        questions which have been created from UI.

        OOP_REASON_CODE is kept separate so that existing OOP flow doesn't
        break.
      example: OOP_REASON_CODE
    PreSearchAnswers:
      type: object
      properties:
        answers:
          type: array
          items:
            $ref: '#/components/schemas/EntityAnswer'
        userEntitiesResponseId:
          type: string
    PreSearchQuestionType:
      type: string
      enum:
        - UNKNOWN_SEARCH_QUESTION_TYPE
        - PURPOSE_OF_TRIP
      description: >
        Types of pre-search questions.

        PURPOSE_OF_TRIP required to ask purpose of the trip user is going to.
        For example: meeting, training, interview.
      example: PURPOSE_OF_TRIP
    Preference:
      title: Preference
      type: object
      required:
        - preferredType
      properties:
        preferredType:
          $ref: '#/components/schemas/PreferredType'
        blockedReason:
          type: string
          description: Reason for blocking the leg, hotel or car.
        label:
          type: string
          description: The label assigned to a specific tier of preference.
    PreferredAirport:
      type: object
      title: PreferredAirport
      description: Airport preferred by traveler.
      required:
        - airportCode
        - label
      properties:
        airportName:
          type: string
          description: Airport name.
          example: San Francisco International Airport
        airportCode:
          type: string
          description: IATA airport code.
          example: SFO
        label:
          $ref: '#/components/schemas/PreferredLocationLabel'
    PreferredLocationLabel:
      type: string
      title: PreferredLocationLabel
      description: The label of preferred airport or rail station.
      enum:
        - HOME
        - WORK
        - OTHER
      example: HOME
    PreferredRailStation:
      type: object
      title: PreferredRailStation
      description: Rail station preferred by traveler.
      required:
        - stationCode
        - label
      properties:
        stationName:
          type: string
          description: Rail station name.
          example: Chicago Union Station
        stationCode:
          type: string
          description: Rail station code.
          example: CHI
        cityName:
          type: string
          description: Name of city where the rail station is located.
          example: Chicago
        countryCode:
          type: string
          description: Alpha-2 country code where the rail station is located.
        label:
          $ref: '#/components/schemas/PreferredLocationLabel'
    PreferredType:
      title: PreferredType
      type: string
      description: Preference type.
      enum:
        - NOT_PREFERRED
        - COMPANY_PREFERRED
        - SPOTTERS_CHOICE
        - COMPANY_BLOCKED
        - TMC_PREFERRED
    PrepaidQualifier:
      type: string
      title: Prepaid Qualifier
      enum:
        - INCLUDE_PREPAID
        - EXCLUDE_PREPAID
        - PREPAID_ONLY
      description: Prepaid qualifiers.
    PrimaryServiceProviderTmc:
      type: object
      title: PrimaryServiceProviderTmc
      description: Primary service provider TMC info
      required:
        - tmcId
      properties:
        tmcId:
          $ref: '#/components/schemas/CompanyId'
          description: Id of the service provider TMC.
    ProfileOwner:
      type: object
      title: ProfileOwner
      description: Basic information about the owner of the ad-hoc user.
      required:
        - userId
      properties:
        userId:
          $ref: '#/components/schemas/UserId'
    PromotionalOffer:
      type: object
      title: Promotional Offer
      description: Represents a promotional offer applied to a hotel rate.
      required:
        - name
      properties:
        name:
          type: string
          description: Name of the promotional offer applied on this hotel rate.
    Question:
      type: object
      title: Question
      description: >-
        The message defines the format of a question which can be asked to a
        user in any kind of workflows.
      required:
        - id
        - name
        - isRequired
        - isDisabled
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          description: >-
            Question display name that the user will see. For eg, 'Choose the
            purpose of your trip'.
        questionFormat:
          $ref: '#/components/schemas/QuestionFormat'
        optionInfo:
          $ref: '#/components/schemas/OptionInfo'
        isRequired:
          type: boolean
          description: Whether its compulsory to answer the question or not.
          default: false
          example: true
        isDisabled:
          type: boolean
          description: >-
            Whether the question is disabled or not. If true, this should not be
            asked.
          default: true
          example: true
        includeInItinerary:
          type: boolean
          description: Whether to include this question in the itinerary related emails.
          default: false
          example: true
          deprecated: true
        customFieldLocations:
          type: array
          items:
            $ref: '#/components/schemas/CustomFieldLocation'
        matchConditions:
          $ref: '#/components/schemas/CustomFieldMatchConditions'
        questionType:
          $ref: '#/components/schemas/QuestionType'
    QuestionFormat:
      type: string
      enum:
        - INPUT_BOX
        - RADIO_BUTTON
        - CHECKBOX
        - CHECKBOX_WITH_PERCENTAGE
      description: >
        Question types. INPUT_BOX will make user enter a free flowing text.

        RADIO_BUTTON will have multiple options, user can select only one.

        CHECKBOX questions contain the possible set of options, from which the
        user can choose multiple options.

        CHECKBOX_WITH_PERCENTAGE is similar to checkbox, with the difference
        being that each option having an additional input field whose values
        must add up to 100.
      example: CHECKBOX
    QuestionType:
      type: object
      title: QuestionType
      description: Question type.
      properties:
        preSearchQuestionType:
          $ref: '#/components/schemas/PreSearchQuestionType'
        preCheckoutQuestionType:
          $ref: '#/components/schemas/PreCheckoutQuestionType'
    RailCard:
      type: object
      title: RailCard
      description: Rail card
      required:
        - name
        - spotnanaCode
        - vendor
      properties:
        cardNumber:
          type: string
          description: Number of card
        expiryDate:
          description: Expiry date of the Rail Card.
          $ref: '#/components/schemas/DateModel'
        name:
          type: string
          description: Name of the Rail Card.
          example: Veterans Railcard
        spotnanaCode:
          description: Unique Spotnana code/identifier for Rail Card.
          type: string
          example: VET
        vendor:
          type: string
          description: Vendor Name.
          example: ATOC
    RailPref:
      type: object
      title: RailPref
      description: Travel preferences related to rail station.
      properties:
        preferredRailStations:
          type: array
          description: A list of user preferred rail stations.
          items:
            $ref: '#/components/schemas/PreferredRailStation'
        seatPreference:
          $ref: '#/components/schemas/SeatPref'
        travelClasses:
          type: array
          description: A list of class of service for rail.
          items:
            $ref: '#/components/schemas/RailTravelClass'
        coachPreferences:
          type: array
          description: A list of coach preference for rail.
          items:
            $ref: '#/components/schemas/CoachPref'
        conditionalRates:
          type: array
          description: A list of conditional rates for rail.
          items:
            $ref: '#/components/schemas/ConditionalRate'
    RailTravelClass:
      description: Travel class
      type: string
      enum:
        - FIRST
        - STANDARD
        - BUSINESS
        - SLEEPER
        - STANDARD_PREMIUM
        - BUSINESS_PREMIUM
        - COACH
        - ROOM
        - EXECUTIVE
      example: FIRST
    RateOption:
      type: object
      title: Rate Option
      description: Details about a specific rate option for a hotel room.
      required:
        - bedInfos
        - priceValidateKey
        - description
        - rateInfo
        - amenities
        - cancellationPolicy
        - policyInfo
        - guaranteeType
        - displayName
        - roomInfo
        - rateGroupKey
        - isPrepaidRoom
        - numRoomsRequired
        - maxOccupancy
        - isModifiable
        - earnLoyaltyPoints
        - rewardPointsEarned
        - isFopModifiable
      properties:
        bedInfos:
          type: array
          description: Information about the beds in the room.
          items:
            $ref: '#/components/schemas/BedInfo'
        priceValidateKey:
          type: string
          description: A key used to validate the price.
        description:
          type: string
          description: A description of the rate option.
        rateInfo:
          $ref: '#/components/schemas/HotelRateInfo'
          description: Information about the rate.
        meals:
          $ref: '#/components/schemas/HotelRoomMeals'
          description: Information about meals included with the rate.
        amenities:
          type: array
          description: A list of amenities included with the rate.
          items:
            $ref: '#/components/schemas/HotelRoomAmenity'
        cancellationPolicy:
          $ref: '#/components/schemas/HotelCancellationPolicy'
          description: The cancellation policy for the rate.
        policyInfo:
          $ref: '#/components/schemas/CommonPolicyInfo'
          description: Information about applicable policies.
        guaranteeType:
          type: string
          description: The type of guarantee required for the rate.
          enum:
            - GUARANTEE
            - DEPOSIT
        displayName:
          type: string
          description: The display name of the rate option.
        roomInfo:
          $ref: '#/components/schemas/HotelRoomInfo'
          description: Information about the room.
        rateGroupKey:
          type: string
          description: A key used to group similar rates.
        additionalAmenities:
          type: array
          description: A list of additional amenities included with the rate.
          items:
            type: string
        isPrepaidRoom:
          type: boolean
          description: Indicates if the room is prepaid.
        supportedCardTypes:
          type: array
          description: A list of card types supported for payment.
          items:
            $ref: '#/components/schemas/CardType'
        numRoomsRequired:
          type: integer
          format: int32
          description: The number of rooms required for this rate option.
        maxOccupancy:
          $ref: '#/components/schemas/Occupancy'
          description: The maximum occupancy for the room.
        isModifiable:
          type: boolean
          description: Indicates if the rate is modifiable.
        additionalDetails:
          type: array
          description: A list of additional details about the rate option.
          items:
            $ref: '#/components/schemas/HotelAdditionalDetail'
        earnLoyaltyPoints:
          type: boolean
          description: Indicates if loyalty points can be earned with this rate.
        rewardPointsEarned:
          type: array
          description: A list of reward points earned with this rate.
          items:
            $ref: '#/components/schemas/RewardPointsEarned'
        promotionalOffers:
          type: array
          description: A list of promotional offers applicable to the rate.
          items:
            $ref: '#/components/schemas/PromotionalOffer'
        paymentDescription:
          type: array
          description: A list of descriptions related to payment for the rate.
          items:
            type: string
        isFopModifiable:
          type: boolean
          description: Indicates if the form of payment is modifiable.
        accessibilityInfo:
          type: array
          description: A list of accessibility features for the rate.
          items:
            $ref: '#/components/schemas/Accessibility'
    RatingWrapper:
      type: object
      title: RatingWrapper
      properties:
        rating:
          type: number
          format: double
    RawPaymentSourceDetails:
      type: object
      title: RawPaymentSourceDetails
      description: Raw Details of the Payment Source
      discriminator:
        propertyName: type
        mapping:
          DPAN: '#/components/schemas/Dpan'
          CREDIT: '#/components/schemas/Credit'
      oneOf:
        - $ref: '#/components/schemas/Dpan'
        - $ref: '#/components/schemas/Credit'
    RedressNumber:
      type: object
      title: RedressNumber
      description: Redress details
      required:
        - number
        - issueCountry
      properties:
        number:
          type: string
          example: '12345'
        issueCountry:
          type: string
          example: US
    RedressNumberWrapper:
      type: object
      title: RedressNumberWrapper
      properties:
        redress:
          $ref: '#/components/schemas/RedressNumber'
    Reference:
      type: object
      title: Reference object containing uuid and name of the entity.
      description: Reference of an entity
      required:
        - id
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
    RegistrarUserIdSearch:
      type: object
      title: RegistrarUserId Search
      required:
        - userIdType
        - registrarUserId
      properties:
        userIdType:
          type: string
          example: REGISTRAR_USER_ID
        registrarUserId:
          $ref: '#/components/schemas/UserId'
    RestrictedKeywordsWithReason:
      type: object
      properties:
        keyword:
          type: string
          description: Restricted keyword
          example: Test Keyword
        reason:
          type: string
          description: Reason for restriction
          example: Test Reason
    RewardPointsEarned:
      type: object
      title: Reward Points Earned
      description: Details reward points earned.
      required:
        - rewardPointsType
        - totalPointsEarned
        - conversionMultiplier
      properties:
        rewardPointsType:
          type: string
          enum:
            - QBR
            - QFF
          description: Type of reward points.
        totalPointsEarned:
          type: number
          format: double
          description: Total points that will be credited.
        conversionMultiplier:
          type: number
          format: double
          description: Multiplier for converting points.
    RoomClass:
      type: string
      description: Room Class
      enum:
        - STANDARD
        - PREMIUM
        - DELUXE
        - BUSINESS
        - PRESIDENTIAL
        - SUPERIOR
        - JUNIOR
        - CLUB
        - UNKNOWN
      example: PRESIDENTIAL
    RoomFee:
      type: object
      title: RoomFee
      description: Room Fee
      required:
        - amount
      properties:
        amount:
          $ref: '#/components/schemas/Money'
          description: Fees amount
        feeInclusions:
          type: array
          items:
            type: string
          description: Amenities included as part of the fee
        displayFee:
          type: boolean
          description: Flag to determine whether to explicitly show this fee type on UI
        feeType:
          type: string
          description: Fees Type
          enum:
            - UNKNOWN
            - BED_TAX
            - CITY_HOTEL_FEE
            - CITY_TAX
            - COUNTRY_TAX
            - ENERGY_TAX
            - FEDERAL_TAX
            - FOOD_AND_BEVERAGE_TAX
            - LODGING_TAX
            - MAINTENANCE_FEE
            - OCCUPANCY_TAX
            - PACKAGE_FEE
            - RESORT_FEE
            - SALES_TAX
            - SERVICE_CHARGE
            - STATE_TAX
            - SURCHARGE
            - TOTAL_TAX
            - TOURISM_TAX
            - VAT_GST_TAX
            - SURPLUS_LINES_TAX
            - INSURANCE_PREMIUM_TAX
            - APPLICATON_FEE
            - EXPRESS_HANDLING_FEE
            - EXEMPT
            - STANDARD
            - ZERO_RATED
            - MISCELLANEOUS
            - ROOM_TAX
            - EARLY_CHECKOUT_FEE
            - COUNTRY_TAXES
            - EXTRA_PERSON_CHARGE
            - BANQUET_SERVICE_FEE
            - ROOM_SERVICE_FEE
            - LOCAL_FEE
            - GOODS_AND_SERVICES_TAX
            - VALUE_ADDED_TAX
            - CRIB_FEE
            - ROLLAWAY_FEE
            - ASSESSMENT_LICENSE_TAX
            - PET_SANITATION_FEE
            - NOT_KNOWN
            - CHILD_ROLLAWAY_CHARGE
            - CONVENTION_TAX
            - EXTRA_CHILD_CHARGE
            - STANDARD_FOOD_AND_BEVERAGE_GRATUITY
            - NATIONAL_GOVERNMENT_TAX
            - ADULT_ROLLAWAY_FEE
            - BEVERAGE_WITH_ALCOHOL
            - BEVERAGE_WITHOUT_ALCOHOL
            - TOBACCO
            - FOOD
            - TOTAL_SURCHARGES
            - STATE_COST_RECOVERY_FEE
            - MISCELLANEOUS_FEE
            - DESTINATION_AMENITY_FEE
            - REFUNDABLE_PET_FEE
            - CHARITY_SUPPORT_FEE
            - LOCAL_AMENITY_USAGE_MAINTENANCE_FEE
            - CONVENTION_TOURISM_FEE
            - DESTINATION_MARKETING_FEE
            - HOTEL_DEVELOPMENT_FEE
            - EVENT_FEE
            - SUSTAINABILITY_FEE
            - TRANSPORTATION_TRANSFER_FEE
            - INSURANCE_FEE
            - LOCAL_GOVERNMENT_FEE
            - LOCAL_ORDINANCE_SURCHARGE
            - GUARANTEED_EARLY_CHECK_IN_FEE
            - GUARANTEED_LATE_CHECK_OUT_FEE
          example: ROOM_TAX
    RoomPreference:
      type: object
      title: RoomPreference
      description: Room options and stay preference.
      properties:
        isMobilityAccessible:
          type: boolean
          description: Whether or not mobility accessible room, tub.
          default: false
          example: false
        bedCount:
          type: string
          description: The number of bed in the room.
          enum:
            - ONE_BED
            - TWO_BEDS
          example: ONE_BED
        roomType:
          type: string
          description: Single selection of type of room.
          enum:
            - SMOKING
            - NON_SMOKING
          example: SMOKING
        mostImportantFact:
          type: string
          description: Single selection of the most import fact.
          enum:
            - ROOM_TYPE
            - BED_COUNT
            - ROOM_LOCATION
          example: BED_COUNT
        roomLocation:
          type: string
          description: Location of the hotel room
          enum:
            - HIGH_FLOOR
            - LOW_FLOOR
            - NEAR_ELEVATOR
          example: HIGH_FLOOR
        pillowType:
          type: string
          description: The type of pillow in hotel room.
          enum:
            - FOAM
            - EXTRA_FOAM
            - EXTRA_FEATHER
          example: FOAM
        roomAmenityPrefs:
          type: array
          items:
            type: string
            description: Amenity preference of the room.
            enum:
              - FEATHER_FREE_ROOM
              - EXTRA_TOWELS
              - REFRIGERATOR
            example: EXTRA_TOWELS
    RoomType:
      type: string
      description: Room Type
      enum:
        - ROOM
        - SUITE
        - VILLA
        - APARTMENT
        - COTTAGE
        - STUDIO
        - UNKNOWN_ROOM
      example: ROOM
    RoomView:
      type: string
      enum:
        - UNKNOWN
        - AIRPORT
        - BAY
        - CITY
        - COURTYARD
        - GOLF
        - HARBOR
        - INTERCOASTAL
        - LAKE
        - MARINA
        - MOUNTAIN
        - OCEAN
        - POOL
        - RIVER
        - WATER
        - BEACH
        - GARDEN
        - PARK
        - FOREST
        - RAIN_FOREST
        - VARIOUS
        - LIMITED
        - SLOPE
        - STRIP
        - COUNTRYSIDE
        - SEA
        - GULF
    SeatAmenityPref:
      type: object
      title: SeatAmenityPref
      description: Seat amenity preference.
      required:
        - seatAmenityTypes
      properties:
        seatAmenityTypes:
          type: array
          items:
            type: string
            enum:
              - UNKNOWN_AIR_SEAT_AMENITY_TYPE
              - FLAT_BED
              - WIFI
              - IN_SEAT_POWER
            example: WIFI
    SeatLocationPref:
      type: object
      title: SeatLocationPref
      description: Seat location preference.
      properties:
        cabins:
          type: array
          items:
            type: string
            enum:
              - UNKNOWN_CABIN
              - ECONOMY
              - PREMIUM_ECONOMY
              - BUSINESS
              - FIRST
            example: FIRST
        isBulkHeadPref:
          type: boolean
          example: false
        maxFlightDurationInHours:
          type: integer
          format: int32
          example: 3
        position:
          type: string
          enum:
            - UNKNOWN_POSITION
            - AISLE
            - WINDOW
            - AISLE_OR_WINDOW
          example: WINDOW
    SeatPref:
      type: object
      title: SeatPref
      description: Preference about seat of rail.
      properties:
        hasAccessibility:
          type: boolean
          description: Whether or not requires assistance for disability.
          default: false
          example: false
        seatTypes:
          type: array
          items:
            $ref: '#/components/schemas/SeatPrefType'
        seatLocations:
          type: array
          items:
            $ref: '#/components/schemas/SeatPrefLocation'
        deckLevels:
          type: array
          items:
            $ref: '#/components/schemas/DeckLevel'
        seatDirections:
          type: array
          items:
            $ref: '#/components/schemas/SeatPrefDirection'
    SeatPrefDirection:
      type: string
      description: The direction of seat of the rail.
      enum:
        - FORWARD
        - BACKWARD
      example: FORWARD
    SeatPrefLocation:
      type: string
      description: The location of seat of the rail.
      enum:
        - AISLE
        - WINDOW
        - SOLO
      example: AISLE
    SeatPrefType:
      type: string
      description: The type of seat of the rail.
      enum:
        - SLEEPER_BED
        - NORMAL
        - TABLE_SEAT
      example: NORMAL
    SecondaryServiceProviderTmc:
      type: object
      title: SecondaryServiceProviderTmc
      description: Secondary service provider TMC info
      required:
        - tmcId
        - supplier
        - travelType
      properties:
        tmcId:
          $ref: '#/components/schemas/CompanyId'
          description: Id of the service provider TMC.
        supplier:
          $ref: '#/components/schemas/SupplierType'
          description: Supplier for which this service provider should be used.
        travelType:
          $ref: '#/components/schemas/TravelType'
          description: Travel type for which this service provider should be used.
    SelectedFormOfPayment:
      type: object
      description: >-
        List of different forms of payments used for booking for a set of
        travelers.
      required:
        - paymentItems
        - selectedPaymentSources
      properties:
        paymentItems:
          type: array
          items:
            $ref: '#/components/schemas/PaymentItem'
        selectedPaymentSources:
          type: array
          items:
            $ref: '#/components/schemas/SelectedPaymentSource'
    SelectedPaymentSource:
      type: object
      description: Describes the selected payment source for the booking.
      properties:
        paymentSourceId:
          type: string
          format: uuid
          description: Unique identifier identifying this payment source.
          example: f49d00fe-1eda-4304-ba79-a980f565281d
        rawPaymentSource:
          $ref: '#/components/schemas/RawPaymentSourceDetails'
        postPaymentRedirectionUrl:
          type: string
          description: >-
            Url for post payment redirection if payment source navigates user to
            a third party url
          example: >-
            https://mycompany.com/checkout?paymentSourceId=f49d00fe-1eda-4304-ba79-a980f565281d
        cvv:
          type: string
          description: CVV associated with associated payment source, if any.
        amount:
          description: Total amount to be charged for specified payment items.
          $ref: '#/components/schemas/Money'
    SimpleMoney:
      type: object
      title: SimpleMoney
      description: Money object containing just amount and currency code.
      required:
        - amount
        - currencyCode
      properties:
        amount:
          type: number
          format: double
          description: Amount
          example: 510
        currencyCode:
          type: string
          description: The 3-letter currency code defined in ISO 4217.
          example: GBP
    SimpleMoneyRange:
      title: SimpleMoneyRange
      type: object
      description: SimpleMoney range containing minimum and maximum amount.
      properties:
        min:
          $ref: '#/components/schemas/SimpleMoney'
          description: Minimum value - inclusive.
        max:
          $ref: '#/components/schemas/SimpleMoney'
          description: Maximum value - inclusive.
    StringListWrapper:
      type: object
      title: StringListWrapper
      properties:
        sList:
          type: array
          items:
            type: string
    StringWrapper:
      type: object
      title: StringWrapper
      properties:
        s:
          type: string
    SupplierType:
      type: string
      description: Supplier type like Sabre, TravelFusion etc.
      enum:
        - SABRE
        - AMADEUS
        - TRAVEL_FUSION
        - FARELOGIX_NDC
        - ATPCO_NDC
        - TRAINLINE
        - AVIA
        - QBR
        - BCD
        - QANTAS_HOTELS
        - SOUTHWEST
        - EXPEDIA
        - HOTEL_HUB
        - NDC
        - MARRIOTT
        - CLEARTRIP
        - KYTE
        - GROUNDSPAN
        - SABRE_NDC
        - BOOKING_COM
      x-ignoreBreakingChanges:
        - SupplierType->BOOKING_COM
      example: SABRE
    Tax:
      type: object
      title: Tax
      description: Tax details
      required:
        - amount
      properties:
        amount:
          $ref: '#/components/schemas/Money'
          description: Tax amount
        taxCode:
          type: string
          description: Tax code
          example: VAT
        percentage:
          type: number
          format: double
          description: Tax amount to total amount
          example: 9
    TaxBreakdown:
      type: array
      items:
        $ref: '#/components/schemas/Tax'
    TermsAndConditions:
      type: object
      title: TermsAndConditions
      description: Terms and conditions.
      properties:
        conditions:
          type: array
          description: List of conditions
          items:
            $ref: '#/components/schemas/Condition'
    ThirdPartySource:
      type: string
      enum:
        - UNKNOWN_SOURCE
        - SABRE
        - TRAVEL_FUSION
        - AVIA
        - NDC
        - TRAINLINE
        - ATPCO_NDC
        - FARELOGIX_NDC
        - OFFLINE
        - CONNEXUS
        - ROUTEHAPPY
        - AMADEUS
        - GIATA
        - QBR
        - BCD
        - QANTAS_HOTELS
        - SOUTHWEST
        - EXPEDIA
        - HOTEL_HUB
        - MARRIOTT
        - CLEARTRIP
        - KYTE
        - GROUNDSPAN
        - SABRE_NDC
        - BOOKING_COM
      x-ignoreBreakingChanges:
        - ThirdPartySource->BOOKING_COM
      example: SABRE
    Tier:
      type: string
      title: Tier
      description: >
        Tier of User.

        Note: In default UI, BASIC is displayed as Standard, and SEAT1A is
        displayed as VIP.
      enum:
        - BASIC
        - SEAT1A
      x-enum-varnames:
        - BASIC
        - SEAT1A
      example: SEAT1A
      default: BASIC
    TimeLocal:
      title: TimeLocal
      description: ISO8601 Local Time
      type: object
      required:
        - iso8601
      properties:
        iso8601:
          type: string
          pattern: ^([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$
          example: '17:32'
    TmcBasicInfo:
      type: object
      title: TmcBasicInfo
      description: TMC related basic information.
      required:
        - contractingTmc
        - bookingTmc
      properties:
        contractingTmc:
          $ref: '#/components/schemas/CompanyRef'
          description: Contracting TMC is the TMC the user/organization contracted.
        bookingTmc:
          $ref: '#/components/schemas/CompanyRef'
          description: >-
            Booking TMC is the TMC used for the bookings for the
            user/organization.
    TmcInfo:
      type: object
      title: TmcInfo
      description: TMC config information.
      required:
        - id
        - primaryServiceProviderTmc
      properties:
        id:
          $ref: '#/components/schemas/CompanyId'
          description: TMC id.
        primaryServiceProviderTmc:
          $ref: '#/components/schemas/PrimaryServiceProviderTmc'
          description: Primary service provider TMC for the TMC.
        secondaryServiceProviderTmcs:
          type: array
          description: Secondary service provider TMCs for the TMC.
          items:
            $ref: '#/components/schemas/SecondaryServiceProviderTmc'
        partnerTmcId:
          $ref: '#/components/schemas/CompanyId'
          description: Useful to identify the clients onboarded by a PARTNER_TMC
    TokenBasedPaginationRequest:
      type: object
      title: Token Based Pagination Request
      description: >-
        Pagination request for token-based pagination, where a token is used to
        retrieve the next page of results.
      discriminator:
        propertyName: requestType
        mapping:
          PAGE_TOKEN: '#/components/schemas/PageToken'
          PAGE_SIZE: '#/components/schemas/PageSize'
      oneOf:
        - $ref: '#/components/schemas/PageToken'
        - $ref: '#/components/schemas/PageSize'
    TokenBasedPaginationResponse:
      type: object
      title: Token Based Pagination Response
      description: >-
        Pagination response for token based pagination. Contains the next page
        token(if next page is available) and pagination parameters.
      required:
        - currentPageSize
      properties:
        nextPageToken:
          type: string
          description: >-
            The next page token to retrieve the next page of results. If it is
            empty, it indicates that there are no more pages to retrieve.
        currentPageSize:
          type: integer
          format: int32
          description: The number of results in the current page.
          example: 10
    TokenizedExpiry:
      title: TokenizedExpiry
      type: object
      description: Contains the tokenized expiry of a Card.
      required:
        - expiryMonth
        - expiryYear
      properties:
        expiryMonth:
          type: string
          description: Tokenized Expiry month
          example: KvAuPANQWCpjwRQxcC8EXg==
        expiryYear:
          type: string
          description: Tokenized Expiry year
          example: fPBm0OWrKwPyIrCVcbg4cA==
    TokenizedExpiryWrapper:
      type: object
      title: TokenizedExpiryWrapper
      properties:
        tokenizedExpiry:
          $ref: '#/components/schemas/TokenizedExpiry'
    TransmissionSearchFilter:
      type: string
      description: Transmission type for Filters
      title: TransmissionSearchFilter
      enum:
        - MANUAL
        - AUTOMATIC
      example: AUTOMATIC
    TravelClassHierarchy:
      type: string
      description: >
        Rail Travel classes in hierarchy numbering higher to keep scope for
        future additions in 

        between.
      enum:
        - UNKNOWN
        - STANDARD
        - COACH
        - STANDARD_PREMIUM
        - BUSINESS
        - BUSINESS_PREMIUM
        - FIRST
        - SLEEPER
      example: STANDARD
    TravelClassHierarchyWrapper:
      type: object
      title: TravelClassHierarchyWrapper
      properties:
        railTravelClass:
          $ref: '#/components/schemas/TravelClassHierarchy'
    TravelPref:
      type: object
      title: TravelPref
      description: Travel preferences.
      properties:
        airPref:
          $ref: '#/components/schemas/AirPref'
        preferredCurrency:
          type: string
          example: USD
        railCards:
          type: array
          items:
            $ref: '#/components/schemas/RailCard'
        railPref:
          $ref: '#/components/schemas/RailPref'
        carPref:
          $ref: '#/components/schemas/CarPref'
        hotelPref:
          $ref: '#/components/schemas/HotelPref'
    TravelRegionType:
      type: string
      title: TravelRegionType
      description: Travel region Type
      enum:
        - DOMESTIC
        - INTERNATIONAL
      example: DOMESTIC
    TravelType:
      type: string
      title: TravelType
      description: Travel Type
      enum:
        - AIR
        - HOTEL
        - CAR
        - RAIL
        - LIMO
        - MISC
        - ALL
      example: AIR
    Traveler:
      type: object
      title: Traveler
      description: Traveler details.
      deprecated: true
      properties:
        travelerPersonalInfo:
          $ref: '#/components/schemas/TravelerPersonalInfo'
        user:
          $ref: '#/components/schemas/User'
        userBusinessInfo:
          $ref: '#/components/schemas/UserBusinessInfo'
        userOrgId:
          $ref: '#/components/schemas/UserOrgId'
        persona:
          $ref: '#/components/schemas/Persona'
        isActive:
          type: boolean
          description: A boolean flag to show if traveler is active.
          example: true
        tier:
          $ref: '#/components/schemas/Tier'
        adhocUserInfo:
          $ref: '#/components/schemas/AdhocUserInfo'
        externalId:
          type: string
          description: External id of this user.
    TravelerMatchConditions:
      type: object
      title: TravelerMatchConditions
      description: Matching conditions for traveler.
      properties:
        workerTypes:
          type: array
          description: Worker types. Users belonging to any of these would match.
          items:
            $ref: '#/components/schemas/WorkerType'
        countries:
          type: array
          description: Countries.
          items:
            type: string
        legalEntities:
          type: array
          description: Legal entities
          items:
            $ref: '#/components/schemas/Reference'
        departments:
          type: array
          description: Departments
          items:
            $ref: '#/components/schemas/Reference'
        costCenters:
          type: array
          description: Cost centers
          items:
            $ref: '#/components/schemas/Reference'
        offices:
          type: array
          description: Offices
          items:
            $ref: '#/components/schemas/Reference'
    TravelerPersonalInfo:
      type: object
      title: TravelerPersonalInfo
      description: Traveler personal information.
      properties:
        loyaltyInfos:
          type: array
          items:
            $ref: '#/components/schemas/LoyaltyInfo'
        travelPref:
          $ref: '#/components/schemas/TravelPref'
    TripData:
      type: object
      title: TripData
      description: Trip data to book a PNR.
      required:
        - tripId
      properties:
        approverEmail:
          description: >-
            Email address of the approver who should receives approval email for
            the current booking.
          type: string
        approverName:
          description: Name of the approver.
          type: string
        hardApprovalRequired:
          description: |
            Whether the current booking requires hard approval or soft approval.
            This flag should be used only if valid approver is present.
          type: boolean
        outOfPolicy:
          description: If the given booking is out of policy.
          type: boolean
        policyId:
          description: Policy Id for which violation is done.
          type: string
        policyVersion:
          description: Version of policy.
          type: integer
          format: int32
        tripId:
          description: Id of the trip.
          $ref: '#/components/schemas/TripId'
    TripId:
      type: object
      title: TripId
      description: Id of trip.
      required:
        - id
      properties:
        id:
          description: Id.
          type: string
          example: '2783425534'
    TripUsageType:
      title: TripUsageType
      description: Trip usage type.
      type: string
      enum:
        - STANDARD
        - EVENT
    User:
      type: object
      title: User
      description: User details.
      properties:
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/PostalAddress'
        dob:
          $ref: '#/components/schemas/DateModel'
        email:
          type: string
          format: email
          example: <EMAIL>
        emergencyContactInfo:
          deprecated: true
          $ref: '#/components/schemas/EmergencyContactInfo'
        emergencyContact:
          $ref: '#/components/schemas/EmergencyContact'
        gender:
          $ref: '#/components/schemas/Gender'
        identityDocs:
          description: List of user identity documents.
          type: array
          items:
            $ref: '#/components/schemas/IdentityDocument'
          example:
            - passport:
                docId: PASSPORTID
                expiryDate:
                  iso8601: '2017-07-21'
                issueCountry: IN
                issuedDate:
                  iso8601: '2017-07-21'
                nationalityCountry: IN
                type: REGULAR
            - ktn:
                number: '123456'
                issueCountry: US
        name:
          $ref: '#/components/schemas/Name'
        paymentInfos:
          type: array
          items:
            $ref: '#/components/schemas/PaymentInfo'
        phoneNumbers:
          type: array
          items:
            $ref: '#/components/schemas/PhoneNumber'
        profilePicture:
          $ref: '#/components/schemas/Image'
        nationality:
          type: string
          example: Indian
          description: Nationality of user
        title:
          $ref: '#/components/schemas/UserTitle'
    UserBasicInfo:
      type: object
      title: UserBasicInfo
      description: Basic user information.
      properties:
        userOrgId:
          $ref: '#/components/schemas/UserOrgId'
        persona:
          $ref: '#/components/schemas/Persona'
        email:
          type: string
          format: email
          example: <EMAIL>
        name:
          $ref: '#/components/schemas/Name'
        profilePicture:
          $ref: '#/components/schemas/Image'
        tier:
          $ref: '#/components/schemas/Tier'
        phoneNumber:
          $ref: '#/components/schemas/PhoneNumber'
        employeeId:
          type: string
          description: Employee id of the user
        isActive:
          type: boolean
          description: Whether user profile is active or not.
          example: true
    UserBusinessInfo:
      type: object
      title: UserBusinessInfo
      description: User business information.
      required:
        - email
        - employeeId
        - organizationId
        - legalEntityId
      properties:
        department:
          $ref: '#/components/schemas/Department'
        designation:
          type: string
          example: MANAGER
        email:
          type: string
          format: email
          example: <EMAIL>
        employeeId:
          description: >-
            Unique employee id. Can use email if a company don't use employee
            ids.
          type: string
          example: '101'
        grade:
          $ref: '#/components/schemas/Grade'
        legalEntityId:
          $ref: '#/components/schemas/LegalEntityId'
        managerBasicInfo:
          $ref: '#/components/schemas/UserBasicInfo'
        office:
          $ref: '#/components/schemas/Office'
        organizationId:
          $ref: '#/components/schemas/OrganizationId'
        phoneNumbers:
          type: array
          items:
            $ref: '#/components/schemas/PhoneNumber'
        userCostCenter:
          $ref: '#/components/schemas/CostCenter'
        designatedApproverInfos:
          deprecated: true
          type: array
          description: A list of user basic info for designated approvers.
          items:
            $ref: '#/components/schemas/UserBasicInfo'
        designatedApproverUserIds:
          type: array
          description: A list of userId for designated approvers.
          items:
            $ref: '#/components/schemas/UserId'
        authorizerEmail:
          type: string
          description: >-
            Email address to be used as approval authorizer, when a manager is
            not present.
          example: <EMAIL>
    UserId:
      type: object
      title: UserId
      description: User identifier
      required:
        - id
      properties:
        id:
          type: string
          format: uuid
    UserIdSearch:
      type: object
      title: UserId Search
      required:
        - userIdType
        - userId
      properties:
        userIdType:
          type: string
          example: USER_ID
        userId:
          $ref: '#/components/schemas/UserId'
    UserIdWrapper:
      title: UserIdWrapper
      description: Wrapper over UserId object to be used as an identifier in a oneOf case
      properties:
        userId:
          $ref: '#/components/schemas/UserId'
    UserOrgId:
      type: object
      title: UserOrgId
      description: User and user's organization information.
      required:
        - userId
        - organizationId
      properties:
        organizationAgencyId:
          $ref: '#/components/schemas/OrganizationAgencyId'
        organizationId:
          $ref: '#/components/schemas/OrganizationId'
        userId:
          $ref: '#/components/schemas/UserId'
        tmcInfo:
          $ref: '#/components/schemas/TmcInfo'
          deprecated: true
        tmcBasicInfo:
          $ref: '#/components/schemas/TmcBasicInfo'
    UserOrgIdListWrapper:
      type: object
      title: UserOrgIdListWrapper
      properties:
        userOrgIdList:
          type: array
          items:
            $ref: '#/components/schemas/UserOrgId'
    UserOrgIdWrapper:
      type: object
      title: UserOrgIdWrapper
      properties:
        userOrgId:
          $ref: '#/components/schemas/UserOrgId'
    UserTitle:
      title: UserTitle
      type: string
      enum:
        - TITLE_UNKNOWN
        - MR
        - MS
        - MRS
        - MX
        - MASTER
        - MISS
        - DR
        - PROFESSOR
        - CAPTAIN
        - REVEREND
        - HONOURABLE
        - SIR
        - LADY
        - AMBASSADOR
        - LORD
        - BRIGADIER
        - SENATOR
        - DAME
        - JUSTICE
        - UK
      example: MR
    Variable:
      type: object
      title: Variable
      description: >-
        Variable defining the additional value to be sent along with custom
        field response.
      required:
        - type
        - name
      properties:
        type:
          type: string
          example: VARIABLE
          default: VARIABLE
        name:
          $ref: '#/components/schemas/VariableName'
    VariableName:
      type: string
      title: VariableName
      description: Name of the variable.
      enum:
        - PUBLISHED_FARE
        - LLF
      example: PUBLISHED_FARE
    WorkerType:
      type: string
      description: The type of worker.
      enum:
        - EMPLOYEE
        - CONTINGENT
        - SEASONAL
        - INTERN
        - GUEST
      example: EMPLOYEE
      x-enumValidFrom:
        GUEST: '2025-06-03'
    WorkerTypeListWrapper:
      type: object
      title: WorkerTypeListWrapper
      properties:
        workerTypeList:
          type: array
          items:
            $ref: '#/components/schemas/WorkerType'
    WorkerTypeWrapper:
      type: object
      title: WorkerTypeWrapper
      properties:
        workerType:
          $ref: '#/components/schemas/WorkerType'
tags:
  - name: Hotel
    description: >
      These endpoints provide comprehensive functionality for managing hotel
      bookings. They enable users to search for hotels, check room rates and
      availability, create and modify bookings, and handle cancellations and
      rebooking. 
paths:
  /v2/hotel-supplier-autocomplete:
    get:
      tags:
        - Autocomplete
      summary: Hotel Supplier Autocomplete
      description: >-
        This endpoint provides autocomplete suggestions for hotels based on the
        query provided.
      operationId: hotelSupplierAutocomplete
      parameters:
        - name: query
          in: query
          description: Query to get autocomplete response
          required: true
          schema:
            type: string
          example: New York Marquis
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HotelAutocompleteResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/hotel/search:
    post:
      tags:
        - Hotel
      summary: Get hotel search results
      description: Get all available hotels for the requested search parameters.
      operationId: hotelSearch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HotelSearchRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HotelSearchResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/hotel/details:
    post:
      tags:
        - Hotel
      summary: Get hotel details
      description: Get details for a specific hotel.
      operationId: hotelDetails
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HotelDetailsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HotelDetailsResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/hotel/price-check:
    post:
      tags:
        - Hotel
      summary: Verify hotel price
      description: >
        Checks if the previously selected hotel price has been modified. To
        indicate the change in hotel price, the `priceChange` field in the
        response will be set to `true` and the `price`  object will contain the
        latest hotel price. If the price remains unchanged, the `priceChange`
        field will be set as `false` and the `price` object will contain the
        original price.
      operationId: hotelPriceCheck
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HotelPriceCheckRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HotelPriceCheckResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/hotel/validate-rebooking:
    post:
      tags:
        - Hotel
      summary: Validate rebooking for price optimization
      description: >
        Validates if a hotel rebooking can be performed for price optimization
        purpose.

        The Source PNR ID provided in the request will be checked if it's
        eligible for rebooking. 

        In the response, if `isValid` is `true` then users can proceed to
        checkout flow, which cancels the existing Source PNR 

        and creates a new PNR to save booking cost.
      operationId: hotelValidateRebooking
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HotelValidateRebookingRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HotelValidateRebookingResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/hotel/create-pnr:
    post:
      tags:
        - Hotel
      summary: Create a new hotel booking
      description: Creates a new hotel booking for the given request parameters.
      operationId: hotelCreatePnr
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HotelCreatePnrRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HotelCreatePnrResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/hotel/pnrs/{pnrId}/cancel-pnr:
    parameters:
      - name: pnrId
        in: path
        description: Pnr Id of the Pnr to be cancelled.
        required: true
        schema:
          type: string
        example: 4974a66b
    post:
      tags:
        - Hotel
      summary: Cancel hotel pnr
      description: This endpoint deletes a pnr by pnrID.
      operationId: hotelCancelPnr
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HotelCancelPnrRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HotelCancelPnrResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/hotel/pnrs/{pnrId}/modify-details:
    parameters:
      - name: pnrId
        in: path
        description: PNR ID of the hotel booking for which the details need to be modified.
        required: true
        schema:
          type: string
        example: 4974a66b
    post:
      tags:
        - Hotel
      summary: Modify hotel details
      description: >
        Modify the details of an existing hotel booking. 

        This API displays the list of modifications available for the same
        hotel. 

        Currently, the details such as occupancy date and room type can be
        modified. 

        To confirm the selected changes, use the **Modify hotel booking** API.
      operationId: hotelModifyDetails
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HotelModifyDetailsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HotelDetailsResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/hotel/pnrs/{pnrId}/modify-book:
    parameters:
      - name: pnrId
        in: path
        description: PNR ID of the hotel booking that needs to be modified.
        required: true
        schema:
          type: string
        example: 4974a66b
    post:
      tags:
        - Hotel
      summary: Modify hotel booking
      description: |
        Modify an existing hotel booking based on the request parameters.
        This API is used to confirm the requested changes for the booking.
      operationId: hotelModifyBooking
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HotelModifyBookingRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HotelModifyBookingResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
