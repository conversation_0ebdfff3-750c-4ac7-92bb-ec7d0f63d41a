openapi: 3.1.0
info:
  title: Company API
  version: v2
servers:
- url: https://apis.spotnana.com
  description: Staging URL
security:
- Bearer: []
components:
  securitySchemes:
    Bearer:
      type: http
      scheme: bearer
  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFound:
      description: The specified resource was not found.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
  schemas:
    AgentContact:
      type: object
      title: AgentContact
      description: Details of agent contact event.
      required:
      - contactType
      - contactId
      - contactOp
      - billable
      properties:
        contactType:
          description: Mode of agent contact.
          $ref: '#/components/schemas/AgentContactType'
          example: PHONE
        contactId:
          type: string
          description: Unique ID for agent contact.
          example: '1234567890'
        contactOp:
          description: Operation that the agent performed.
          $ref: '#/components/schemas/AgentContactOp'
          example: AGENT_BOOKING
        agentId:
          $ref: '#/components/schemas/UserId'
          description: User ID of agent.
        billable:
          type: boolean
          description: Is the agent contact event billable?
          example: true
        pnrId:
          type: string
          description: PNR ID (if applicable).
          example: '1234567890'
        travelType:
          description: Travel type.
          $ref: '#/components/schemas/TravelType'
          example: AIR
        notes:
          type: string
          description: Agent notes.
          example: Requested by customer
        nonBillableReason:
          $ref: '#/components/schemas/NonBillableReason'
          description: Reason for non-billable agent contact
    AgentContactOp:
      type: string
      title: AgentContactOp
      description: Agent contact operation.
      enum:
      - AGENT_BOOKING
      - AGENT_MODIFY
      - AGENT_CANCEL
      - AGENT_OTHER
      - AGENT_EXCHANGE
      - ANCILLARY_PURCHASE
      - SHELL_PNR_CREATE
      - SHELL_PNR_MODIFY
      - SHELL_PNR_CANCEL
      - SHELL_PNR_EXCHANGE
      - MANUAL_FORM_CREATE
      - MANUAL_FORM_MODIFY
      - MANUAL_FORM_CANCEL
      - MANUAL_FORM_EXCHANGE
      - UNUSED_TICKET_MANAGEMENT
    AgentContactType:
      type: string
      title: Agent contact type
      description: Agent contact type
      enum:
      - PHONE
      - CHAT
      - EMAIL
    AirConfig:
      type: object
      title: AirConfig
      description: Air content config
      properties:
        fareCategoryFilter:
          type: array
          description: List of fare category filter
          items:
            $ref: '#/components/schemas/FareCategoryFilterConfig'
    Announcement:
      type: object
      title: Announcement
      description: Announcement details
      required:
      - title
      - description
      properties:
        title:
          type: string
          description: Title of the announcement.
          example: Spotnana on Mission to Unbundle Travel
        description:
          type: string
          description: Description of the announcement.
          example: Amex Ventures Joins Spotnana on Mission to Unbundle Travel.
        linkUrl:
          type: string
          description: Announcement link url which points to the atual announcement
            page.
          example: https://medium.com/inside-spotnana/amex-ventures-joins-spotnana-on-mission-to-unbundle-travel-3cdb2ecceff4
        linkDisplayText:
          type: string
          description: Announcement link display text.
          example: New announcement
    AppDownloadLinksConfig:
      type: object
      title: AppDownloadLinksConfig
      description: App store or QR links for downloading app.
      properties:
        androidPlayStoreUrl:
          type: string
          example: https://play.google.com/store/apps/details?id=com.spotnana
        iosAppStoreUrl:
          type: string
          example: https://apps.apple.com/in/app/spotnana-travel-booking/id1580021446
        qrCodeUrl:
          type: string
          example: https://www.spotnana.com
    ArrayOfReference:
      type: object
      properties:
        length:
          type: integer
          format: int32
        elements:
          type: array
          description: List of references containing id and name.
          items:
            $ref: '#/components/schemas/Reference'
        totalNumResults:
          type: integer
          format: int32
          description: Total number of results
    BookingDetails:
      type: object
      title: BookingDetails
      description: Booking information
      properties:
        pnrId:
          type: string
          description: PNR Id
        pnrVersion:
          type: integer
          format: int32
          description: Version number associated with the PNR
        tripUsageType:
          type: string
          description: Trip Usage Type
          enum:
          - TRIP_USAGE_TYPE_UNKNOWN
          - STANDARD
          - EVENT
        source:
          description: Third party source
          $ref: '#/components/schemas/ThirdPartySource'
        sourcePnrId:
          type: string
          description: Source PNR Id from thirdparty source
    BookingFeeInfo:
      type: object
      title: BookingFeeInfo
      required:
      - feeType
      discriminator:
        propertyName: bookingFeeType
        mapping:
          TRIP_FEE: '#/components/schemas/TripFeeInfo'
          TRANSACTION_FEE: '#/components/schemas/TransactionFeeInfo'
          VALUE_ADDED_SERVICE_FEE: '#/components/schemas/ValueAddedServiceFeeInfo'
          OTHER_FEE: '#/components/schemas/OtherFeeInfo'
      oneOf:
      - $ref: '#/components/schemas/TripFeeInfo'
      - $ref: '#/components/schemas/TransactionFeeInfo'
      - $ref: '#/components/schemas/ValueAddedServiceFeeInfo'
      - $ref: '#/components/schemas/OtherFeeInfo'
      properties:
        feeType:
          type: string
          description: Type of the fee
          example: BOOKING_FEE
          enum:
          - BOOKING_FEE
    CabinViewFareCategory:
      type: string
      enum:
      - UNKNOWN_CABIN_CATEGORY
      - BASIC
      - ECONOMY
      - PREMIUM_ECONOMY
      - ECONOMY_PLUS
      - BUSINESS
      - FIRST
      default: UNKNOWN_CABIN_CATEGORY
    CarbonCostConfig:
      type: object
      title: CarbonCostConfig
      description: The config is used to calculate carbon cost of CO2 emission.
      required:
      - price
      - weight
      properties:
        price:
          description: The price for the carbon emission per weight.
          $ref: '#/components/schemas/Money'
        weight:
          description: The weight unit and amount to calculate cost of carbon emission.
          $ref: '#/components/schemas/Weight'
    Card:
      type: object
      title: Card
      description: User card info
      required:
      - number
      properties:
        id:
          type: string
          description: Unique identifier for this card
          format: uuid
          example: 34d536b6-f8ff-11eb-9a61-0242ac180002
        type:
          type: string
          enum:
          - UNKNOWN
          - CREDIT
          - DEBIT
          description: Type of card
          example: CREDIT
        company:
          $ref: '#/components/schemas/CardCompany'
        name:
          description: Name on card
          type: string
          example: Harrison Schwartz
        address:
          description: Billing address
          $ref: '#/components/schemas/PostalAddress'
        number:
          type: string
          description: Card number
          example: '****************'
        expiryMonth:
          type: integer
          format: int32
          minimum: 1
          maximum: 12
          description: Expiry month
          example: 1
          deprecated: true
        expiryYear:
          type: integer
          format: int32
          description: Expiry year
          minimum: 2000
          example: 2010
          deprecated: true
        cvv:
          type: string
          description: Card cvv number
          example: '012'
        label:
          type: string
          description: Card Label
          example: Label amex
        currency:
          type: string
          description: Native currency of the card.
          example: USD
        externalId:
          type: string
          description: Spotnana partner card id.
          example: bxt_RNGsNfzgJDaTstKIKqK4xEuhGYAnMdYK8T40
        vaultId:
          type: string
          description: ID of the vault used for creating the card.
          format: uuid
          example: 34d536b6-f8ff-11eb-9a61-0242ac180002
        expiry:
          description: Card Expiry.
          $ref: '#/components/schemas/CardExpiry'
        ownershipLabel:
          $ref: '#/components/schemas/OwnershipLabel'
          example: PERSONAL
    CardAccessEntity:
      title: CardAccessEntity
      type: object
      description: Contains the central access level and entity id.
      required:
      - entityId
      properties:
        entityId:
          type: string
          description: Holds the id for for the user who can access the card or organization
            id or legal entity
        centralCardAccessLevel:
          $ref: '#/components/schemas/CentralCardAccessLevel'
    CardCompany:
      type: string
      enum:
      - NONE
      - VISA
      - MASTERCARD
      - AMEX
      - DISCOVER
      - AIR_TRAVEL_UATP
      - CARTE_BLANCHE
      - DINERS_CLUB
      - JCB
      - BREX
      - UNION_PAY
      - EURO_CARD
      - ACCESS_CARD
      - ELO_CARD
      description: The issuing company of card
      example: VISA
    CardExpiry:
      title: CardExpiry
      type: object
      description: Contains the expiry of a Card.
      oneOf:
      - $ref: '#/components/schemas/TokenizedExpiryWrapper'
      - $ref: '#/components/schemas/ExpiryWrapper'
    CardMetadata2:
      type: object
      title: CardMetadata
      description: Metadata for Card payment source.
      properties:
        card:
          $ref: '#/components/schemas/Card'
        accessType:
          $ref: '#/components/schemas/CreditCardAccess'
        isLodgeCard:
          type: boolean
          description: Whether the payment is made using a lodge card
          example: false
        bta:
          type: string
          description: Whether this is a BTA card. Possible values are 'Y' or 'N'.
            Relevant only for lodge cards.
          example: Y
    CentralCardAccessLevel:
      title: CentralCardAccessLevel
      type: string
      description: Identifies at which level central card is applicable, for example
        organization or legal entity.
      enum:
      - UNKNOWN
      - ORGANIZATION
      - LEGAL_ENTITY
      - TMC
    ChargeProcessorInfo:
      title: ChargeProcessorInfo
      type: object
      description: Information about the processor that calculated the charge
      discriminator:
        propertyName: chargeProcessorType
        mapping:
          RULE_BASED_INFO: '#/components/schemas/RuleBasedInfo'
          TMC_CALCULATOR_INFO: '#/components/schemas/TmcCalculatorInfo'
      oneOf:
      - $ref: '#/components/schemas/RuleBasedInfo'
      - $ref: '#/components/schemas/TmcCalculatorInfo'
    ColorConfig:
      type: object
      title: ColorConfig
      description: Color configuration for customizable colors in the UI.
      properties:
        profileColors:
          $ref: '#/components/schemas/ProfileColors'
    ColorTitle:
      title: ColorTitle
      description: Title of the color.
      type: string
      enum:
      - PROFILE_TIER_MEMBER_COLOR
      - PROFILE_TIER_SILVER_COLOR
      - PROFILE_TIER_GOLD_COLOR
      - PROFILE_TIER_PLATINUM_COLOR
      - PROFILE_TIER_TITANIUM_COLOR
      - PROFILE_TIER_AMBASSADOR_COLOR
    ColorWhiteLabelConfig:
      type: object
      title: ColorWhitelabelConfig
      description: Whitelabel configuration related to colours.
      properties:
        primary:
          type: string
          example: '#D33B47'
        primaryDark:
          type: string
          example: '#D33B47'
        primaryLight:
          type: string
          example: '#D33B47'
        primaryPale:
          type: string
          example: '#D33B47'
        onPrimary:
          type: string
          example: '#D33B47'
        secondary:
          type: string
          example: '#D33B47'
        secondaryDark:
          type: string
          example: '#D33B47'
        secondaryLight:
          type: string
          example: '#D33B47'
        secondaryPale:
          type: string
          example: '#D33B47'
        onSecondary:
          type: string
          example: '#D33B47'
        error:
          type: string
          example: '#D33B47'
        errorDark:
          type: string
          example: '#D33B47'
        errorLight:
          type: string
          example: '#D33B47'
        errorPale:
          type: string
          example: '#D33B47'
        onError:
          type: string
          example: '#D33B47'
    Company:
      type: object
      title: Company
      description: Company information.
      required:
      - id
      - name
      - billingCurrency
      - companyRoles
      properties:
        id:
          $ref: '#/components/schemas/CompanyId'
        name:
          type: string
          description: Company name
          example: Spotnana
        emailDomains:
          type: array
          description: List of the company email domains
          uniqueItems: true
          items:
            type: string
            example: spotnana.com
        billingCurrency:
          type: string
          description: Billing currency of the company
          example: INR
        phoneNumbers:
          type: array
          description: List of company phone numbers
          items:
            $ref: '#/components/schemas/PhoneNumber'
        emergencyContactInfos:
          type: array
          description: List of company emergency contacts
          items:
            $ref: '#/components/schemas/EmergencyContactInfo'
        isSelfSignUpEnabled:
          type: boolean
          description: Boolean to show if company has self sign up enabled.
          example: true
        announcements:
          type: array
          description: Company announcements
          items:
            $ref: '#/components/schemas/Announcement'
        companyLogo:
          $ref: '#/components/schemas/Image'
          description: Company logo
        contractedBy:
          $ref: '#/components/schemas/CompanyId'
          description: Company ID which have contracted this company.
        isActive:
          type: boolean
          description: Boolean to show if this is an active company.
        companyRoles:
          type: array
          description: Company roles
          items:
            $ref: '#/components/schemas/CompanyRole'
        supportConfig:
          $ref: '#/components/schemas/SupportConfig'
          description: Support Configuration for company
          deprecated: true
        ccEmailAddresses:
          type: array
          description: List of email addresses on which all confirmation emails will
            be cced. This is applicable only for company roles 'ORG'
          items:
            type: string
            example: <EMAIL>
        bccEmailAddresses:
          type: array
          description: List of email addresses on which all confirmation emails will
            be bcced. This is applicable only for company roles 'ORG'
          items:
            type: string
            example: <EMAIL>
        connectors:
          type: array
          description: Connectors related to company.
          items:
            $ref: '#/components/schemas/Connector'
        postalAddress:
          $ref: '#/components/schemas/PostalAddress'
          description: Postal address of the company.
        redirectUrl:
          type: string
          description: Redirect URL for the company. It is applicable only for company
            roles 'PARTNER_TMC'
        rewardsProgram:
          $ref: '#/components/schemas/RewardsProgram'
          description: Reward Program corresponding to partner. It is applicable only
            for company roles 'PARTNER_TMC'.
          deprecated: true
        tmcDefaultConfiguration:
          $ref: '#/components/schemas/TmcDefaultConfiguration'
          description: Default Booking configuration for the TMC. It is applicable
            only for company roles 'TMC'
        expensePartnerConfig:
          $ref: '#/components/schemas/ExpensePartnerConfig'
        mobileRedirectUrl:
          type: string
          description: Redirect URL for the company for mobile. It is applicable only
            for company roles 'PARTNER_TMC'
        applicableCustomFieldTypes:
          type: array
          description: List of custom fields supported by the company.
          items:
            $ref: '#/components/schemas/CustomFieldType'
        oauthPartnerConfig:
          $ref: '#/components/schemas/OAuthPartnerConfig'
        privacySettings:
          $ref: '#/components/schemas/PrivacySettings'
        designationTree:
          $ref: '#/components/schemas/DesignationNode'
          description: Designation tree for company showing who reports whom.
        externalId:
          type: string
          description: External id to be linked with this.
          example: MyCompanyId
        emailClientConfig:
          $ref: '#/components/schemas/EmailClientConfig'
          description: Email client configuration representing the email provider
            to be used.
        isFake:
          type: boolean
          description: Whether the company is a fake company for testing. This is
            for internal use.
          default: false
          example: false
        tmcPartnerRoleMappings:
          type: array
          description: list of individual role mappings between spotnana and partner.
          items:
            $ref: '#/components/schemas/TmcPartnerRoleMapping'
        supportConfigs:
          $ref: '#/components/schemas/SupportConfigs'
        loyaltyBlockedCountries:
          type: array
          items:
            type: string
          description: List of 2 letter country codes where business travelers should
            not be allowed to add loyalty info.
          example:
          - US
          - IN
        allowDomainBasedAuthConfig:
          type: boolean
          default: false
          description: Whether to allow user to get auth config based on email domain
        carbonCostConfig:
          $ref: '#/components/schemas/CarbonCostConfig'
          description: The config is used to calculate carbon cost of CO2 emission.
        defaultBookingTmcId:
          description: Id of the default booking TMC for the company.
          $ref: '#/components/schemas/CompanyId'
    CompanyId:
      type: object
      title: CompanyId
      description: Company ID
      required:
      - id
      properties:
        id:
          type: string
          format: uuid
          example: f49d00fe-1eda-4304-ba79-a980f565281d
    CompanyRef:
      type: object
      description: Basic information about a company.
      required:
      - id
      properties:
        id:
          $ref: '#/components/schemas/CompanyId'
        name:
          type: string
        logo:
          $ref: '#/components/schemas/Image'
          description: Company logo
    CompanyRole:
      type: string
      description: Role of the company.
      enum:
      - ORG
      - TMC
      - PARTNER_TMC
      - HR_FEED_CONNECTOR
      - TRIPS_DATA_CONNECTOR
      - GLOBAL
      example: ORG
    CompanySpecifiedAttributeLegalEntity:
      type: object
      description: Company specified attribute for legal entity.
      required:
      - fieldName
      - value
      properties:
        fieldName:
          type: string
          description: Field name of the attribute
          example: businessId
        value:
          type: string
          description: Field value of the attribute
          example: ABCD
    ConfermaInfo:
      type: object
      title: ConfermaInfo
      description: Details of conferma - one of virtual card vendors.
      required:
      - deploymentId
      properties:
        deploymentId:
          type: integer
          format: int32
          description: Unique identifier assigned to the virtual card deployment at
            the point of creation.
          example: 68793680
    Connector:
      type: object
      title: Connector
      description: Connectors related to company.
      required:
      - connectorType
      - connectorId
      properties:
        connectorType:
          type: string
          description: Type of connector.
          enum:
          - HR_FEED_CONNECTOR
          - TRIPS_DATA_CONNECTOR
          example: HR_FEED_CONNECTOR
        connectorId:
          $ref: '#/components/schemas/CompanyId'
    CostCenter:
      type: object
      title: CostCenter
      description: Cost center details.
      required:
      - id
      - name
      properties:
        id:
          $ref: '#/components/schemas/CostCenterId'
        name:
          type: string
          example: CostCenter
        externalId:
          type: string
          example: external-id
    CostCenterId:
      type: object
      title: Cost center id.
      required:
      - id
      properties:
        id:
          type: string
          example: 731ccbca-0415-6fe1-d235-c324dfbe7423
    CreateCompanyRequest:
      type: object
      title: CreateCompanyRequest
      description: Request for company create.
      required:
      - name
      - billingCurrency
      - companyRoles
      properties:
        name:
          type: string
          description: Company name
          example: Spotnana
        emailDomains:
          type: array
          description: List of the company email domains
          uniqueItems: true
          items:
            type: string
            example: spotnana.com
        billingCurrency:
          type: string
          description: Billing currency of the company
          example: INR
        phoneNumbers:
          type: array
          description: List of company phone numbers
          items:
            $ref: '#/components/schemas/PhoneNumber'
        emergencyContactInfos:
          type: array
          description: List of company emergency contacts
          items:
            $ref: '#/components/schemas/EmergencyContactInfo'
        isSelfSignUpEnabled:
          type: boolean
          description: Boolean to show if company has self sign up enabled.
          example: true
        announcements:
          type: array
          description: Company announcements
          items:
            $ref: '#/components/schemas/Announcement'
        companyLogo:
          $ref: '#/components/schemas/Image'
          description: Company logo
        contractedBy:
          $ref: '#/components/schemas/CompanyId'
          description: Company ID which have contracted this company.
        companyRoles:
          type: array
          description: Company roles
          items:
            $ref: '#/components/schemas/CompanyRole'
        supportConfig:
          $ref: '#/components/schemas/SupportConfig'
          description: Support Configuration for company
          deprecated: true
        supportConfigs:
          type: array
          description: Support Configs for company
          items:
            $ref: '#/components/schemas/SupportConfig'
        ccEmailAddresses:
          type: array
          description: List of email addresses on which all confirmation emails will
            be cced. This is applicable only for company roles 'ORG'
          items:
            type: string
            example: <EMAIL>
        bccEmailAddresses:
          type: array
          description: List of email addresses on which all confirmation emails will
            be bcced. This is applicable only for company roles 'ORG'
          items:
            type: string
            example: <EMAIL>
        postalAddress:
          $ref: '#/components/schemas/PostalAddress'
          description: Postal address of the company.
        redirectUrl:
          type: string
          description: Redirect URL for the company. It is applicable only for company
            roles 'PARTNER_TMC'
        rewardsProgram:
          $ref: '#/components/schemas/RewardsProgram'
          description: Rewards Program corresponding to partner. It is applicable
            only for company roles 'PARTNER_TMC'.
          deprecated: true
        tmcDefaultConfiguration:
          $ref: '#/components/schemas/TmcDefaultConfiguration'
          description: Default Booking configuration for the TMC. It is applicable
            only for company roles 'TMC'
        expensePartnerConfig:
          $ref: '#/components/schemas/ExpensePartnerConfig'
        mobileRedirectUrl:
          type: string
          description: Redirect URL for the company for mobile. It is applicable only
            for company roles 'PARTNER_TMC'
        applicableCustomFieldTypes:
          type: array
          description: List of custom fields supported by the company.
          items:
            $ref: '#/components/schemas/CustomFieldType'
        oauthPartnerConfig:
          $ref: '#/components/schemas/OAuthPartnerConfig'
        privacySettings:
          $ref: '#/components/schemas/PrivacySettings'
        emailClientConfig:
          $ref: '#/components/schemas/EmailClientConfig'
          description: Email client configuration representing the email provider
            to be used.
        externalId:
          type: string
          description: External id for legal entity.
          example: company-external-id
        isFake:
          type: boolean
          description: Whether the company is a fake company for testing. This is
            for internal use.
          default: false
          example: false
        tmcPartnerRoleMappings:
          type: array
          description: list of individual role mappings between spotnana and partner.
          items:
            $ref: '#/components/schemas/TmcPartnerRoleMapping'
        loyaltyBlockedCountries:
          type: array
          items:
            type: string
          description: List of 2 letter country codes where business travelers should
            not be allowed to add loyalty info.
          example:
          - US
          - IN
        allowDomainBasedAuthConfig:
          type: boolean
          default: false
          description: Whether to allow user to get auth config based on email domain
        carbonCostConfig:
          $ref: '#/components/schemas/CarbonCostConfig'
          description: The config is used to calculate carbon cost of CO2 emission.
        defaultBookingTmcId:
          description: Id of the default booking TMC for the company.
          $ref: '#/components/schemas/CompanyId'
    CreateCostCenterRequest:
      type: object
      description: Request body for cost center create API.
      required:
      - name
      properties:
        name:
          type: string
        numberOfEmployees:
          type: integer
          format: int32
        externalId:
          type: string
          example: external-id
    CreateDepartmentRequest:
      type: object
      title: CreateDepartmentRequest
      required:
      - name
      description: Create department request
      properties:
        name:
          type: string
          description: Name of the department
        externalId:
          type: string
          description: External id of the department
          example: department-external-id
    CreateOfficeRequest:
      type: object
      title: Create Office request.
      description: Office details.
      properties:
        address:
          $ref: '#/components/schemas/PostalAddress'
        name:
          type: string
          example: Office name
        latlng:
          $ref: '#/components/schemas/Latlng'
        externalId:
          type: string
          example: external-id
        taxId:
          type: string
          example: '123232'
    CreditCardAccess:
      type: object
      title: CreditCardAccess
      description: Access type for cards to check who in the organization has permission
        to use it
      required:
      - accessType
      - entityIds
      properties:
        accessType:
          $ref: '#/components/schemas/CreditCardAccessType'
        entityIds:
          type: array
          items:
            type: string
          description: Holds the ids for for all users who can access the card or
            organization id
        entities:
          type: array
          description: A list of cardAccessEntity consisting of central card access
            level if present and entity id.
          items:
            $ref: '#/components/schemas/CardAccessEntity'
    CreditCardAccessType:
      title: CreditCardAccessType
      type: string
      description: Identifies the different ways the card can be used across an organization
      enum:
      - UNKNOWN_TYPE
      - CENTRALISED
      - INDIVIDUAL
      - PERSONAL
      - TMC
      - APPLICATION
      - ITINERARY
      - EVENTS
      - TRAVEL_ARRANGER_MANAGED
      - COMPANY_TRAVEL_ARRANGER_MANAGED
      - EVENT_TEMPLATE
      x-ignoreBreakingChanges:
      - CreditCardAccessType->EVENT_TEMPLATE
    CustomFieldType:
      type: string
      description: The type of custom field.
      enum:
      - QUESTION
      - MEETING
      - BUDGET
      - BREX_TOKEN
      default: QUESTION
    DateRange:
      type: object
      description: Date range within which the virtual card can be charged.
      required:
      - startDate
      - endDate
      properties:
        startDate:
          $ref: '#/components/schemas/DateTimeLocal'
        endDate:
          $ref: '#/components/schemas/DateTimeLocal'
    DateTimeLocal:
      title: DateTimeLocal
      description: Local date and time in ISO 8601 format.
      type: object
      required:
      - iso8601
      properties:
        iso8601:
          type: string
          pattern: ^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$
          example: 2017-07-21T17:32
    DateTimeOffset:
      title: DateTimeOffset
      description: ISO8601 UTC Date Time
      type: object
      required:
      - iso8601
      properties:
        iso8601:
          type: string
          pattern: ^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?(Z|([+-](0[0-9]|1[0-4]):([0-5][0-9])))$
          example: 2017-07-21T17:32Z
    DepartmentV2:
      type: object
      title: DepartmentV2
      description: Department details.
      required:
      - id
      - name
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          description: Name of the department
          example: IT Department
        externalId:
          type: string
          description: External id of the department
          example: department-ext-id
    DesignationNode:
      type: object
      title: DesignationNode
      description: Designation tree for company showing who reports whom.
      properties:
        name:
          type: string
          description: designation name
        children:
          type: array
          description: designations under this designation.
          items:
            $ref: '#/components/schemas/DesignationNode'
    Dimensions:
      type: object
      title: Dimensions
      description: Image dimensions ie width and height.
      properties:
        height:
          type: integer
          format: int32
          example: 120
        width:
          type: integer
          format: int32
          example: 240
    DirectBilling2:
      title: DirectBilling
      type: object
      description: Metadata for Direct Billing
      required:
      - vendorId
      properties:
        vendorId:
          type: string
          description: Unique vendor id/code
          example: ZI
        directBillingCode:
          type: string
          description: Direct billing code provided by the vendor
          example: '123456'
    DirectBillingWrapper2:
      type: object
      title: DirectBillingWrapper
      description: Wrapper for DirectBilling metadata
      required:
      - directBilling
      properties:
        directBilling:
          $ref: '#/components/schemas/DirectBilling2'
    EmailClientConfig:
      type: object
      title: EmailClientConfig
      description: Email Client Config
      required:
      - client
      - credentials
      - defaultSenderAddress
      properties:
        client:
          $ref: '#/components/schemas/EmailClientType'
          description: The email client to be used for pushing out the emails
        credentials:
          $ref: '#/components/schemas/EmailClientCredentials'
          description: Credentials for connecting to the email client
        defaultSenderAddress:
          type: string
          description: The default sender address to be used for the configured client
          example: <EMAIL>
        defaultSenderName:
          type: string
          description: The default sender name to be used for the configured client
          example: Test Sender
    EmailClientCredentials:
      type: object
      title: EmailClientCredentials
      description: Email client credentials for the company.
      required:
      - emailClientSecretKey
      properties:
        emailClientSecretKey:
          $ref: '#/components/schemas/EmailClientSecretKey'
    EmailClientSecretKey:
      type: object
      title: EmailClientSecretKey
      description: EmailClientSecretKey
      properties:
        secretKey:
          type: string
          example: SECRET_KEY
        versionId:
          type: string
          example: '1'
    EmailClientType:
      type: string
      enum:
      - AWS_SES
      - SENDGRID
      example: SENDGRID
    EmailConfig:
      type: object
      title: EmailConfig
      description: The email configuration part of the ExpensePartnerConfig.
      required:
      - expensePartnerEmail
      properties:
        personalCardConfig:
          $ref: '#/components/schemas/PersonalCardConfig'
        centralCardExpenseEmail:
          type: string
          description: Central email for bookings done with central card.
          example: <EMAIL>
        expensePartnerEmail:
          type: string
          description: The expense partner email where expense receipts are sent to.
          example: <EMAIL>
        bccEmail:
          type: string
          description: Email to which we bcc expensify emails
          example: <EMAIL>
    EmergencyContactInfo:
      type: object
      title: EmergencyContactInfo
      description: Emergency contact information.
      required:
      - email
      properties:
        address:
          $ref: '#/components/schemas/PostalAddress'
        designation:
          type: string
          example: MANAGER
        email:
          type: string
          format: email
          example: <EMAIL>
        name:
          $ref: '#/components/schemas/Name'
        phoneNumber:
          $ref: '#/components/schemas/PhoneNumber'
        userOrgId:
          $ref: '#/components/schemas/UserOrgId'
    EnrollmentConfig:
      type: object
      title: EnrollmentConfig
      description: Enrollment configuration.
      required:
      - termsAndConditions
      properties:
        termsAndConditions:
          type: string
          example: https://www.spotnana.com/terms/
          description: Link to terms and conditions.
    EntityId:
      type: object
      description: Identifier of an object
      required:
      - id
      properties:
        id:
          type: string
          format: uuid
    ErrorParameter:
      type: object
      title: ErrorParameter
      description: Error parameter
      properties:
        name:
          type: string
          description: Parameter name
        value:
          type: string
          description: Parameter value
    ErrorResponse:
      type: object
      properties:
        debugIdentifier:
          type: string
          description: Link to debug the error internally.
        errorMessages:
          type: array
          items:
            type: object
            properties:
              errorCode:
                type: string
                description: Error code to identify the specific errors.
              message:
                type: string
                description: Message containing details of error.
              errorParameters:
                type: array
                description: Error message parameters.
                items:
                  $ref: '#/components/schemas/ErrorParameter'
              errorDetail:
                type: string
                description: More details about the error.
    ExpenseOwner:
      type: object
      title: ExpenseOwner
      description: Determines who should the expense be reported against.
      properties:
        traveler:
          type: boolean
          description: Report the expense against the traveler who made the booking.
            If set to true, central email should not be set.
          example: true
        centralEmail:
          type: string
          description: Report the expense against a central email.
          example: <EMAIL>
    ExpensePartner:
      title: Expense Partner
      description: The expense partner name as enum.
      type: string
      enum:
      - EXPENSIFY
      - EMBURSE_CHROMERIVER
      - BREX
      example: EXPENSIFY
    ExpensePartnerConfig:
      type: object
      title: ExpensePartnerConfig
      description: Expense Partner Configuration
      required:
      - partner
      properties:
        partner:
          $ref: '#/components/schemas/ExpensePartner'
        emailConfig:
          $ref: '#/components/schemas/EmailConfig'
        travelType:
          type: array
          description: Applicable segments
          items:
            $ref: '#/components/schemas/TravelType'
        isExpensePartnerEnabled:
          type: boolean
          description: A flag denoting is the expense partner config is enabled or
            not.
          default: true
          example: true
        reportCustomFields:
          type: boolean
          description: Send custom fields to the expense partner if enabled.
          example: true
        personalCardExpenseOwner:
          $ref: '#/components/schemas/ExpenseOwner'
        centralCardExpenseOwner:
          $ref: '#/components/schemas/ExpenseOwner'
        partnerReferralId:
          type: string
          description: Referral id used for this company/legal entity during expense
            partner setup
          example: 1234hgd
        itineraryConfig:
          $ref: '#/components/schemas/ItineraryConfig'
    Expiry:
      title: Expiry
      type: object
      description: Contains the expiry of a Card.
      required:
      - expiryMonth
      - expiryYear
      properties:
        expiryMonth:
          type: integer
          format: int32
          minimum: 1
          maximum: 12
          description: Expiry month
          example: 1
        expiryYear:
          type: integer
          format: int32
          description: Expiry year
          minimum: 2000
          example: 2010
    ExpiryWrapper:
      type: object
      title: ExpiryWrapper
      properties:
        expiry:
          $ref: '#/components/schemas/Expiry'
    ExternalEndpoint:
      type: object
      title: ExternalEndpoint
      description: Generic object to encapsulate information about any external endpoint.
      required:
      - url
      - authHeaderName
      properties:
        url:
          type: string
          description: Url from where user can be fetched.
        authHeaderName:
          type: string
          description: Auth header where we have to send authentication/authorization
            details.
        userIdentifierType:
          $ref: '#/components/schemas/UserIdentifierType'
          description: Either use pid or email as the identifier for auth code exchange
            process
        responseAttributes:
          description: List of relevant attribute names & their json xpath from response
          items:
            $ref: '#/components/schemas/UserInfoEndpointResponseAttribute'
    FailureDetails:
      type: object
      title: FailureDetails
      description: Failure details
      properties:
        failureCode:
          type: string
          description: failure code
          enum:
          - FAILURE_CODE_UNKNOWN
          - PAYMENT_METHOD_MISSING
          - PAYMENT_GATEWAY_FAILURE
        failureMessage:
          type: string
          description: failure message
    FareCategory:
      type: object
      properties:
        ngsCategory:
          $ref: '#/components/schemas/NGSFareCategory'
        cabinViewCategory:
          $ref: '#/components/schemas/CabinViewFareCategory'
    FareCategoryFilterConfig:
      type: object
      title: FareCategoryFilterConfig
      description: Air fare category filter
      required:
      - priority
      - origin
      - destination
      - fareCategoryOption
      properties:
        priority:
          type: integer
          description: priority of this filter in case multiple filters are applicable
          example: 1
        origin:
          description: origin for which this filter will be valid (* for all origins)
          $ref: '#/components/schemas/LocationItem'
        destination:
          description: destination for which this filter will be valid (* for all
            destinations)
          $ref: '#/components/schemas/LocationItem'
        fareCategoryOption:
          type: array
          items:
            $ref: '#/components/schemas/FareCategoryOptionConfig'
    FareCategoryOptionConfig:
      type: object
      title: FareCategoryOptionConfig
      description: Fare category option to disable
      required:
      - fareCategory
      - disabled
      properties:
        fareCategory:
          $ref: '#/components/schemas/FareCategory'
        disabled:
          type: boolean
          description: true if a fare category is disabled
          example: true
    FeatureConfig:
      type: object
      title: FeatureConfig
      description: Feature enabled for company.
      required:
      - name
      - enabled
      properties:
        name:
          $ref: '#/components/schemas/FeatureNameEnum'
        enabled:
          type: boolean
          description: Whether feature is enabled or not.
          deprecated: true
          default: true
          example: true
        valueType:
          type: string
          description: Value Type of Feature.
          enum:
          - INT
          - DOUBLE
          - BOOL
          - STRING
          - JSON
          example: BOOL
        value:
          type: string
          description: Value associated with Feature.
    FeatureConfigs:
      type: object
      title: Features
      description: Features related to company.
      properties:
        length:
          type: integer
          description: Number of features.
          example: 1
        features:
          type: array
          description: Features related to company.
          items:
            $ref: '#/components/schemas/FeatureConfig'
    FeatureNameEnum:
      type: string
      description: Name of the feature.
      enum:
      - FLIGHT_RESULTS_OOP
      - HOTEL_RESULTS_OOP
      - RAIL_RESULTS_OOP
      - CARS_RESULTS_OOP
      - PAYMENT_ADD_CARD
      - MENU_ITEM_POLICIES
      - INTEGRATION_EXPENSIFY
      - TRAVELER_SELECTION
      - FEATURE_PERSONAL_TRAVEL
      - PRODUCT_OBT_RAIL
      - MENU_ITEM_COMPANY_REMARKS
      - HOMEPAGE_ADD_USER
      - MENU_ITEM_CUSTOM_FIELDS
      - PAYMENT_UNUSED_CREDITS
      - EMAIL_UPDATE_BY_COMPANY_ADMIN
      - ADHOC_BOOKING_SEARCH_PAGE
      - ADHOC_BOOKING_SAVE_TRAVELER
      - ADMIN_VIRTUAL_CARD
      - CENTRAL_CARD_SERVICE_FEE
      - EXPEDIA_SOURCE_ENABLED
      - BOOKING_COM_SOURCE_ENABLED
      - HOTEL_MEDIAN_ENABLE
      - HIDE_SEARCH_CRITERIA
      - ADHOC_PAYMENT_SOURCE
      - SERVICE_CHARGE_CONFIG_DISABLED
      - HIDE_SETTINGS_MENU_ITEM_COMPANY
      - HIDE_SETTINGS_MENU_ITEM_USERS
      - HIDE_POLICY_APPROVAL_SECTION
      - HIDE_POLICY_CREATION
      - HIDE_SETTINGS_TEXT_FROM_MENU
      - HIDE_LEFT_NAV
      - HIDE_POLICY_AIR_CARRIER
      - HIDE_POLICY_AIR_CO2
      - HIDE_POLICY_HOTEL_PRICE_LOCATION_CUSTOMISATION
      - HIDE_PROFILE_PREFERENCES_AIRLINES
      - HIDE_PROFILE_PREFERENCES_ALLIANCES
      - MONEY_SET_CONVERTED_CURRENCY_AS_ORIGINAL_CURRENCY
      - PRODUCT_OBT_AIR_DISABLED
      - PRODUCT_OBT_HOTEL_DISABLED
      - PRODUCT_OBT_CAR_DISABLED
      - PRODUCT_OBT_LIMO_DISABLED
      - PRODUCT_OBT_CONCIERGE_DISABLED
      - EVENT_BOOKING_ENABLED
      - EMPLOYEE_LEVEL_DESIGNATED_APPROVER_ENABLED
      - ENRICH_SEARCH_RESPONSE_WITH_REWARD_POINTS
      - HIDE_LEFT_NAV_EMBED
      - HIDE_POLICY_AIR_NOT_ALLOWED_TO_BE_BOOKED
      - HIDE_TRIP_FEE_CONFIG
      - HIDE_AGENT_FEE_CONFIG
      - SHOW_SERVICE_FEE_TAXES
      - HIDE_GUEST_BOOKING_SETTING
      - USER_CREATION_PERSONA_TYPE_OPTIONS
      - AIR_SEARCH_RESULT_AIRLINE_SORT_ORDER
      - HIDE_SHERPA_VISA_WIDGET
      - AUTO_DEACTIVATE_GUEST
      - POLICY_USER_GROUPS
      - HIDE_SUPPLIER_MANAGEMENT
      - HIDE_BOOKING_RESTRICTIONS_BY_COUNTRY
      - LINKED_MARRIOTT_BONVOY_REQUIRED
      - EDITING_DEFAULT_POLICIES_DISABLED
      - DISALLOW_MULTI_CITY_ADD_FLIGHT
      - REQUIRE_ONBOARDING_ON_ENROLLMENT
      - GEO_CUSTOMIZATIONS
      - HIDE_PREFERRED_PARENT_CHAINS
      - HIDE_PREFERRED_HOTEL_BRANDS
      - HIDE_ROOM_OPTIONS_AND_STAY_PREFERENCES
      - HIDE_CONDITIONAL_RATES_PREFERENCES
      - HIDE_HOTELS_IN_SUPPLIER_MANAGEMENT
      - HIDE_HOTEL_BRANDS_IN_SUPPLIER_MANAGEMENT
      - HIDE_POLICY_HOTEL_STAR_RATING
      - PRODUCT_OBT_HOMEPAGE_ORDER
      - HIDE_COMPANY_SUPPLIER_MENU
      - EXTERNAL_EVENT_BOOKING_URL
      - HIDE_IDLE_SESSION_TIMEOUT
      - LE_CUSTOM_COLUMNS_CONFIG
      - PROFILE_ELEMENTS_VISIBILITY
      - SHOW_TRIP_FEE_CONFIG
      - SHOW_AGENT_FEE_CONFIG
      - DEFAULT_HOTEL_SEARCH_RADIUS_CONFIG
      - ENROLLMENT_DASHBOARD
      - PRIVACY_CONFIG_HIERARCHY
      - POLICY_CONFIGURED_IN_HR_FEED
      - COMPANION_VIEW_CONFIG
      - SHOW_INDIAN_CARD_PAYMENT_GUIDELINES
      - VISA_RESTRICTIONS_CONFIG
      - ALLOW_COMPANY_ADMIN_TO_RAISE_SUPPORT_TICKET
      - ENABLE_COMPANY_PREFERRED_HOTEL_FILTER_BY_DEFAULT
      - ENABLE_COMPANY_PREFERRED_CAR_FILTER_BY_DEFAULT
      - LOGIN_DISABLED
      - SHOW_AGENT_FEE_RULE_BASED_CONFIG
      - ENROLLMENT_EMAILS_BCC
      - SUREWARE_SYNC
      - DEFAULT_TMC_VIEW_MODE
      - SUPPORTED_CONTRACTING_TMCS
      x-ignoreBreakingChanges:
      - FeatureNameEnum->LOGIN_DISABLED
      - FeatureNameEnum->SHOW_AGENT_FEE_RULE_BASED_CONFIG
      example: ADMIN_VIRTUAL_CARD
    FeeInfo:
      type: object
      title: FeeInfo
      discriminator:
        propertyName: feeType
        mapping:
          BOOKING_FEE: '#/components/schemas/BookingFeeInfo'
      oneOf:
      - $ref: '#/components/schemas/BookingFeeInfo'
    FixedChargeConfig:
      type: object
      title: FixedChargeConfig
      description: Fixed charge configuration for service charge
      required:
      - charges
      properties:
        charges:
          type: array
          description: Amount in specified currency
          items:
            $ref: '#/components/schemas/Money'
        defaultCurrencyCode:
          type: string
          description: The 3-letter currency code defined in ISO 4217.
          example: GBP
        tierCharges:
          type: array
          description: Charges specified per traveler tier
          items:
            $ref: '#/components/schemas/TierChargeConfig'
    FormOfPayment:
      title: Form of Payment
      description: Information about form of payment to be used to charge customer
      type: object
      required:
      - type
      properties:
        type:
          type: string
          enum:
          - UNKNOWN
          - CARD
          - CASH
          - TFPAY
          - CHEQUE
          - BREX_POINTS
          - QANTAS_POINTS
          title: PaymentType
          description: Type of payment used
          example: CARD
          deprecated: true
        card:
          description: The payment card to be used to charge customer. This is only
            set if the payment type is 'CARD'
          $ref: '#/components/schemas/Card'
        additionalInfo:
          type: string
          description: Additional info to be added if payment type is 'UNKNOWN'.
        accessType:
          $ref: '#/components/schemas/CreditCardAccess'
        paymentMethod:
          $ref: '#/components/schemas/PaymentMethod'
          description: Payment method used to pay for this transaction
        paymentMetadata:
          $ref: '#/components/schemas/PaymentMetadata'
        paymentSourceType:
          $ref: '#/components/schemas/PaymentSourceType'
    FreshdeskConfig:
      type: object
      title: FreshdeskConfig
      description: Configuration for freshdesk.
      required:
      - enabled
      - hasCredentials
      properties:
        enabled:
          type: boolean
          description: Whether freshdesk is enabled or not.
          default: true
          example: true
        hasCredentials:
          type: boolean
          description: Whether the company has its own freshdesk credentials.
          default: false
          example: false
        credentials:
          description: "Credentials for the fresh desk. This field has no impact in\
            \ create/update APIs. The \nprocess to store / update the credentials\
            \ are manual.\n"
          $ref: '#/components/schemas/FreshdeskCredentials'
    FreshdeskCredentialDetails:
      type: object
      title: FreshdeskCredentialDetails
      description: Credentials related to freshdesk.
      required:
      - domain
      - appId
      - appKey
      properties:
        domain:
          type: string
          description: Domain related to fresh desk.
        appId:
          type: string
          description: An identifier for fresh desk's app.
        appKey:
          type: string
          description: A secret key assigned to user's app by fresh desk.
    FreshdeskCredentials:
      type: object
      title: FreshdeskCredentials
      description: Mobile and web Credentials related to freshdesk.
      properties:
        webCredentials:
          $ref: '#/components/schemas/FreshdeskCredentialDetails'
        mobileCredentials:
          $ref: '#/components/schemas/FreshdeskCredentialDetails'
    GatewayIdentifier:
      type: object
      properties:
        gatewayId:
          type: string
          description: Gateway Id for which the payment method should be verified.
        gatewayType:
          type: string
          description: Gateway Type for of the verification gateway.
          enum:
          - PAYMENT_GATEWAY_UNKNOWN
          - STRIPE
          - BREX
          - RAZORPAY
    GetCompanyPerTripSrvChargeResponse:
      type: object
      title: GetCompanyPerTripSrvChargeResponse
      description: Get per trip service charge configuration for company
      properties:
        config:
          $ref: '#/components/schemas/FixedChargeConfig'
        enabled:
          type: boolean
          description: True if per trip service charge configuration is enabled
          example: true
        enableDisableOpSupported:
          type: boolean
          description: True if per trip service charge configuration can be enabled/disabled
          example: true
    Image:
      type: object
      title: Image
      description: An image with meta data. Either the `data` or `url` property must
        be supplied to load the image.
      properties:
        data:
          type: string
          format: byte
          example: 6935813e12584abda0e43d71cd2ea260
        dimensions:
          $ref: '#/components/schemas/Dimensions'
        url:
          type: string
          example: https://static.wixstatic.com/media/73f2e2_6935813e12584abda0e43d71cd2ea260~mv2.png/v1/fill/w_630,h_94,al_c,q_85,usm_0.66_1.00_0.01/Spotnana%403x.webp
    ItineraryConfig:
      title: ItineraryConfig
      type: object
      description: Itinerary configuration to control what expenses to send to the
        partner
      properties:
        skipModificationUpdate:
          type: boolean
          description: Allow modification expenses to be skipped from sending to the
            partner
          example: true
          default: false
        skipCancellationUpdate:
          type: boolean
          description: Allow cancellation expenses to be skipped from sending to the
            partner
          example: true
          default: false
    Latlng:
      title: Latlng
      description: Latitude and Longitude for a Location
      type: object
      required:
      - latitude
      - longitude
      properties:
        latitude:
          type: number
          description: Latitude of the Location
          format: double
          example: 77.1025
        longitude:
          type: number
          description: Longitude of the Location
          format: double
          example: 28.7041
    LegalEntity:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier of the legal entity.
        name:
          type: string
          description: Name of the legal entity
        address:
          $ref: '#/components/schemas/PostalAddress'
          description: Address of the legal entity.
        billingCurrency:
          type: string
          description: Billing currency for the legal entity.
        dba:
          type: string
          description: Business name, sort form of Doing Business As
        ein:
          type: string
          description: Tax number
        phoneNumbers:
          type: array
          items:
            $ref: '#/components/schemas/PhoneNumber'
        isDelayedInvoicingEnabled:
          type: boolean
          description: 'Whether delayed invoicing is enabled for the legal entity.
            If enabled, travelers of the

            legal entity won''t be charged for trip on checkout, rather the bills
            would be sent to

            the company later on based on the billing cycle.

            '
        bookingTmcRef:
          $ref: '#/components/schemas/Reference'
          description: Booking TMC of the legal entity.
        expensePartnerConfig:
          $ref: '#/components/schemas/ExpensePartnerConfig'
          description: Configured details of expense partner.
        ccEmailAddresses:
          type: array
          description: List of email addresses on which all confirmation emails will
            be CCed. This is applicable only for company roles 'ORG'
          items:
            type: string
            example: <EMAIL>
        bccEmailAddresses:
          type: array
          description: List of email addresses on which all confirmation emails will
            be BCCed. This is applicable only for company roles 'ORG'
          items:
            type: string
            example: <EMAIL>
        externalId:
          description: External id for the legal entity.
          type: string
          example: my-external-id
        companySpecifiedAttributes:
          type: array
          items:
            $ref: '#/components/schemas/CompanySpecifiedAttributeLegalEntity'
          description: Company specified attributes or fields for the legal entity.
    LegalEntityCreateRequest:
      type: object
      required:
      - name
      - billingCurrency
      - address
      properties:
        name:
          type: string
          description: Name of the legal entity
        address:
          $ref: '#/components/schemas/PostalAddress'
          description: Address of the legal entity.
        billingCurrency:
          type: string
          description: Billing currency for the legal entity.
        dba:
          type: string
          description: Business name, sort form of Doing Business As
        ein:
          type: string
          description: Tax number
        phoneNumbers:
          type: array
          items:
            $ref: '#/components/schemas/PhoneNumber'
        isDelayedInvoicingEnabled:
          type: boolean
          description: 'Whether delayed invoicing is enabled for the legal entity.
            If enabled, travelers of the

            legal entity won''t be charged for trip on checkout, rather the bills
            would be sent to

            the company later on based on the billing cycle.

            '
        ccEmailAddresses:
          type: array
          description: List of email addresses on which all confirmation emails for
            this LE will be cc'ed. This is applicable only for company roles 'ORG'
          items:
            type: string
            example: <EMAIL>
        bccEmailAddresses:
          type: array
          description: List of email addresses on which all confirmation emails for
            this LE will be bcc'ed. This is applicable only for company roles 'ORG'
          items:
            type: string
            example: <EMAIL>
        externalId:
          type: string
          description: External id for legal entity.
          example: legal-entity-external-id
        companySpecifiedAttributes:
          type: array
          items:
            $ref: '#/components/schemas/CompanySpecifiedAttributeLegalEntity'
          description: Company specified attributes or fields for the legal entity.
    LegalEntityId:
      type: object
      title: LegalEntityId
      description: The ID of the legal entity.
      required:
      - id
      properties:
        id:
          type: string
          example: fc1ccbce-8413-4fe9-b233-a324dfbe7421
    LegalEntityUpdateRequest:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier of the legal entity.
        name:
          type: string
          description: Name of the legal entity
        address:
          $ref: '#/components/schemas/PostalAddress'
          description: Address of the legal entity.
        billingCurrency:
          type: string
          description: Billing currency for the legal entity.
        dba:
          type: string
          description: Business name, sort form of Doing Business As
        ein:
          type: string
          description: Tax number
        phoneNumbers:
          type: array
          items:
            $ref: '#/components/schemas/PhoneNumber'
        isDelayedInvoicingEnabled:
          type: boolean
          description: 'Whether delayed invoicing is enabled for the legal entity.
            If enabled, travelers of the

            legal entity won''t be charged for trip on checkout, rather the bills
            would be sent to

            the company later on based on the billing cycle.

            '
        expensePartnerConfig:
          $ref: '#/components/schemas/ExpensePartnerConfig'
          description: Configured details of expense partner.
        ccEmailAddresses:
          type: array
          description: List of email addresses on which all confirmation emails will
            be CCed. This is applicable only for company roles 'ORG'
          items:
            type: string
            example: <EMAIL>
        bccEmailAddresses:
          type: array
          description: List of email addresses on which all confirmation emails will
            be BCCed. This is applicable only for company roles 'ORG'
          items:
            type: string
            example: <EMAIL>
        externalId:
          description: External id for the legal entity.
          type: string
          example: my-external-id
        companySpecifiedAttributes:
          type: array
          items:
            $ref: '#/components/schemas/CompanySpecifiedAttributeLegalEntity'
          description: Company specified attributes or fields for the legal entity.
    ListServiceChargesRequest:
      type: object
      title: ListServiceChargesRequest
      description: Request for list service charges.
      properties:
        serviceType:
          $ref: '#/components/schemas/ServiceType'
          description: Type of service charge (Optional).
    ListServiceChargesResponse:
      type: object
      title: ListServiceChargesResponse
      description: Response for list service charges.
      properties:
        serviceCharges:
          type: array
          description: List of service charges.
          items:
            $ref: '#/components/schemas/ServiceChargeRecord'
    LocationItem:
      type: object
      title: LocationItem
      description: Object representing a single location object.
      required:
      - value
      - metadata
      - infos
      properties:
        value:
          type: string
          description: User facing display value.
        metadata:
          description: Metadata such as country code of the @value.
          $ref: '#/components/schemas/LocationMetadata'
        infos:
          type: array
          description: 'This represent other information for the @value.

            E.g. - This will have continent details if value is any country,

            similarly country details for city.

            '
          items:
            $ref: '#/components/schemas/LocationItem'
    LocationMetadata:
      type: object
      title: LocationMetadata
      description: Metadata of the location object.
      required:
      - type
      - code
      properties:
        type:
          description: Type of the object.
          $ref: '#/components/schemas/LocationType'
        code:
          type: string
          description: Unique code to represent the object.
    LocationType:
      description: Type of the location object.
      type: string
      title: LocationType
      enum:
      - CONTINENT
      - COUNTRY
      - CITY
      - AIRPORT
      - ADMINISTRATIVE_DIVISION
      example: CITY
    LogoConfig:
      type: object
      title: LogoConfig
      description: Logos to be shown on various parts of the app
      properties:
        logoHeader:
          type: string
          description: Logo for app header, email header.
          example: https://duploservices-stage-email-assets-************.s3.us-west-2.amazonaws.com/confirmation/spotnana3.png
        logoFooter:
          type: string
          description: Logo for app footer, email footer.
          example: https://duploservices-stage-email-assets-************.s3-us-west-2.amazonaws.com/approval/spotnana-s-logo.png
        faviconUrl:
          type: string
          description: Display logo on the browser title.
          example: http://www.spotnana.com
        thumbnailImage:
          type: string
          description: Logo for autocomplete, negotiated rates etc.
          example: https://duploservices-stage-email-assets-************.s3-us-west-2.amazonaws.com/approval/spotnana-s-logo.png
    Money:
      type: object
      title: Money
      description: 'Money object containing details such as the amount, the currency
        code, and the converted amount.

        '
      required:
      - amount
      - currencyCode
      properties:
        amount:
          type: number
          format: double
          description: The numeric value for the amount of money.
          example: 510
        currencyCode:
          type: string
          description: The 3-letter currency code for the money amount (defined using
            ISO 4217 standard).
          example: GBP
        convertedAmount:
          type: number
          format: double
          description: 'The converted currency and amount that has been converted
            (if a currency conversion has been requested).

            For example, if the call requests that money be sent in a specified currency
            (because the frontend requested

            the backend to send money in the user''s preferred currency).

            '
          example: 715.42
        convertedCurrency:
          type: string
          description: The 3-letter currency code for the converted currency (defined
            using ISO 4217 standard).
          example: USD
        otherCoinage:
          type: array
          title: OtherCoinage
          description: List of the dollar amount in other coinage systems like reward
            points, cryptocurrency etc.
          items:
            type: object
            properties:
              coinageCode:
                $ref: '#/components/schemas/PaymentMethod'
                description: Payment method
              amount:
                type: number
                format: double
                example: 1000
              conversionRate:
                type: number
                format: double
                description: 1 coin in this system equals to how many currency value
                example: 0.01
              preferredCurrencyConversionRate:
                type: number
                format: double
                description: 1 coin in this system equals to how many currency value
                example: 0.01
    NGSFareCategory:
      type: string
      enum:
      - UNKNOWN_NGS_CATEGORY
      - BASE
      - STANDARD
      - ENHANCED
      - PREMIUM
      - LUXURY
      - ULTRA_LUXURY
      default: UNKNOWN_NGS_CATEGORY
    Name:
      type: object
      title: Name
      description: Full name containing first, middle, last (family) names, and suffix.
      required:
      - given
      - family1
      properties:
        family1:
          type: string
          description: Last (family) name.
          example: Gandas
        family2:
          type: string
          example: FamilyTwo
        given:
          type: string
          description: First (given) name.
          example: Vichitr
        middle:
          type: string
          description: Middle name.
          example: Kumar
        suffix:
          $ref: '#/components/schemas/NameSuffix'
          description: Suffix used with the name. For example SR or JR.
          example: SR
        preferred:
          type: string
          description: Informal preferred name added by traveler. This is not used
            on any PNR or tickets
          example: Don
    NameSuffix:
      title: NameSuffix
      description: Suffix for name
      type: string
      enum:
      - NAME_SUFFIX_UNKNOWN
      - SR
      - JR
      - MD
      - PHD
      - II
      - III
      - IV
      - DO
      - ATTY
      - V
      - VI
      - ESQ
      - DC
      - DDS
      - VM
      - JD
      - SECOND
      - THIRD
      example: SR
    NonBillableReason:
      type: object
      title: NonBillableReason
      description: Reason for non-billable agent contact.
      required:
      - reasonCode
      properties:
        reasonCode:
          $ref: '#/components/schemas/NonBillableReasonCode'
          description: Reason code for non-billable agent contact.
          example: MINOR_REQUEST
        notes:
          type: string
          description: Notes for non-billable agent contact.
          example: Waived off
    NonBillableReasonCode:
      type: string
      title: NonBillableReasonCode
      description: Reason code for non-billable agent contact.
      enum:
      - PRODUCT_ISSUE
      - ALREADY_CHARGED
      - CUSTOMER_GOODWILL
      - MINOR_REQUEST
      - NON_BILLABLE_REASON_OTHER
    OAuthPartnerConfig:
      type: object
      title: OAuthConfig
      description: Config for token exchange in case of partner's have their own IDP.
      required:
      - name
      - tokenVerifier
      - doPersistToken
      properties:
        name:
          type: string
          description: Name of the partner.
        userDetailEndpoint:
          $ref: '#/components/schemas/ExternalEndpoint'
        tokenVerifier:
          $ref: '#/components/schemas/TokenVerifier'
        doPersistToken:
          type: boolean
          description: Boolean to define whether to persist(in-memory/disk) token
            or not.
    OfficeV2:
      type: object
      title: Office
      description: Office details.
      properties:
        address:
          $ref: '#/components/schemas/PostalAddress'
        id:
          type: string
          format: uuid
        name:
          type: string
          example: Office
        latlng:
          $ref: '#/components/schemas/Latlng'
        externalId:
          type: string
          example: external-id
        taxId:
          type: string
          example: '123232'
    OktaJwtVerifier:
      type: object
      title: OktaJwtVerifier
      description: "Details of Okta JWT verifier such as okta oauth endpoint, audience\
        \ for which the token is \nissued.\n"
      required:
      - oauthUrl
      - audience
      properties:
        oauthUrl:
          type: string
          description: Okta oauth url.
        audience:
          type: string
          description: Audience for which the token is issued.
    OktaJwtVerifierWrapper:
      type: object
      title: OktaJwtVerifierWrapper
      properties:
        oktaJwtVerifier:
          $ref: '#/components/schemas/OktaJwtVerifier'
    OrganizationAgencyId:
      type: object
      title: OrganizationAgencyId
      description: Agency id for the organization.
      deprecated: true
      required:
      - id
      properties:
        id:
          type: string
    OrganizationId:
      type: object
      title: OrganizationId
      description: Organization id for traveler.
      required:
      - id
      properties:
        id:
          type: string
    OtherFeeInfo:
      type: object
      title: OtherFeeInfo
      description: Other Fee Info
      required:
      - bookingFeeType
      - otherFeeType
      properties:
        bookingFeeType:
          type: string
          description: Type of the booking fee
          example: OTHER_FEE
        calculatedAmount:
          $ref: '#/components/schemas/Money'
        chargeProcessorInfo:
          $ref: '#/components/schemas/ChargeProcessorInfo'
        otherFeeType:
          $ref: '#/components/schemas/OtherFeeType'
        feeName:
          type: string
          description: Name of the fee to be charged.
          example: TMC Standard Fee
    OtherFeeType:
      type: string
      title: OtherFeeType
      description: Other fee type
      enum:
      - MERCHANT_FEE
      example: MERCHANT_FEE
    OwnershipLabel:
      title: OwnershipLabel
      type: string
      description: Ownership label of the card whether it is personal, corporate or
        central.
      enum:
      - CORPORATE
      - PERSONAL
      - CENTRAL
    PaymentMetadata:
      title: PaymentMetadata
      description: Payment metadata related to form of payment
      type: object
      properties:
        customPaymentMethodMetadata:
          type: object
          description: Metadata related to custom payment method
          properties:
            brexBudgetMetadata:
              type: object
              description: Metadata for Brex Budget
              properties:
                budgetName:
                  type: string
                  description: Name of the budget
                  example: Travel budget
                paidByPersonalCard:
                  type: boolean
                  description: Whether it was paid by budget card or personal card
                  example: false
        vendorProgramPaymentMetadata:
          $ref: '#/components/schemas/VendorProgramPaymentMetadata2'
        virtualCardMetadata:
          $ref: '#/components/schemas/VirtualCardPaymentMetadata'
        cardMetadata:
          $ref: '#/components/schemas/CardMetadata2'
    PaymentMethod:
      type: string
      description: Payment method
      enum:
      - PAYMENT_METHOD_UNKNOWN
      - CREDIT_CARD
      - BREX_POINTS
      - CASH
      - QANTAS_POINTS
      - VENDOR_PROGRAM_PAYMENT
      - DELAYED_INVOICING
      - FLIGHT_CREDITS
      - QANTAS_TRAVEL_FUND
      - CUSTOM_VIRTUAL_PAYMENT
      example: BREX_POINTS
    PaymentSourceType:
      type: string
      description: Type of Payment Source
      enum:
      - CARD
      - VIRTUAL_CARD
      - REWARDS_PROGRAM
      - DELAYED_INVOICING
      - CUSTOM_PAYMENT_METHOD
      - VENDOR_PROGRAM_PAYMENT
      - UNUSED_CREDIT
      - CASH
    PaymentStatus:
      type: string
      title: PaymentStatus
      description: Payment status.
      enum:
      - SUCCESS
      - DELAYED_INVOICE
      - MANUAL
      - NO_CHARGE
    PaymentTransaction:
      type: object
      title: PaymentTransaction
      description: Payment Transaction
      properties:
        gatewayIdentifier:
          description: Gateway Identifier
          $ref: '#/components/schemas/GatewayIdentifier'
        paymentReference:
          type: string
          description: payment reference
        networkTransactionId:
          type: string
          description: Network Transaction Id
    PersonalCardConfig:
      title: PersonalCardConfig
      description: Configuration for bookings done with Personal card.
      type: object
      properties:
        sendToTraveler:
          type: boolean
          description: A boolean flag to show if expenses are to be sent to traveler's
            Expense partner account.
          example: false
        personalCardExpenseEmail:
          type: string
          description: The email for the expense partner account.
          example: <EMAIL>
    PhoneNumber:
      type: object
      title: PhoneNumber
      description: Properly formatted phone number.
      properties:
        countryCode:
          type: integer
          format: int32
          description: two digit country code
          example: 91
        countryCodeSource:
          type: string
          enum:
          - UNSPECIFIED
          - FROM_NUMBER_WITH_PLUS_SIGN
          - FROM_NUMBER_WITH_IDD
          - FROM_NUMBER_WITHOUT_PLUS_SIGN
          - FROM_DEFAULT_COUNTRY
          example: FROM_NUMBER_WITH_PLUS_SIGN
        extension:
          description: phone number extension
          type: string
          example: '222'
        isoCountryCode:
          description: ISO alpha-2 code
          type: string
          example: IN
        italianLeadingZero:
          type: boolean
          default: false
          example: true
        nationalNumber:
          type: integer
          format: int64
          example: 8150
        numberOfLeadingZeros:
          type: integer
          format: int32
          default: 0
          example: 1
        preferredDomesticCarrierCode:
          type: string
          example: '7'
        rawInput:
          type: string
          example: '77777'
        type:
          type: string
          enum:
          - UNKNOWN_TYPE
          - MOBILE
          - LANDLINE
          example: MOBILE
    PostalAddress:
      title: PostalAddress
      description: Postal Address Details
      type: object
      required:
      - addressLines
      - regionCode
      properties:
        addressLines:
          description: Address lines
          type: array
          items:
            type: string
            example: Golden Gate Bridge
        administrativeArea:
          type: string
          description: 'Code of administrative area. For example: DL for Delhi, India.

            Highest administrative subdivision which is used for postal

            addresses of a country or region.

            For example, this can be a state, a province, an oblast, or a prefecture.

            Specifically, for Spain this is the province and not the autonomous

            community (e.g. "Barcelona" and not "Catalonia").

            Many countries don''t use an administrative area in postal addresses.
            E.g.

            in Switzerland this should be left unpopulated.

            '
          example: CA
        administrativeAreaName:
          type: string
          description: "Name of administrative area. This is full name corresponding\
            \ to administrativeArea. \nLike Delhi for DL area code. For some places,\
            \ code and name maybe same as well like Tokyo.\n"
          example: California
        description:
          description: Address description
          type: string
          example: San Francisco Home
        isDefault:
          description: Whether this address is default address in case multiple addresses
            are specified.
          type: boolean
          example: true
        languageCode:
          description: "BCP-47 language code of the contents of this address (if known).\
            \ This is often the UI \nlanguage of the input form or is expected to\
            \ match one of the languages used in the \naddress' country/region, or\
            \ their transliterated equivalents.\nThis can affect formatting in certain\
            \ countries, but is not critical to the correctness \nof the data and\
            \ will never affect any validation or other non-formatting related operations.\n\
            Examples: \"zh-Hant\", \"ja\", \"ja-Latn\", \"en\".\n"
          type: string
          example: en
        locality:
          description: Generally refers to the city/town portion of the address.
          type: string
          example: San Francisco
        locationCode:
          description: IATA 3-letter location code. See https://www.iata.org/en/services/codes.
          type: string
          example: LAX
        organization:
          description: The name of the organization at the address.
          type: string
          example: Spotnana
        postalCode:
          description: Postal code of the address. This is a required field when setting
            for a user/legal entity/company etc.
          type: string
          example: '94130'
        continentCode:
          description: 2 letter continent code of the continent this address falls
            in.
          type: string
          example: AF
        recipients:
          description: The recipient at the address.
          type: array
          items:
            type: string
        regionCode:
          description: Region code of the country/region of the address.
          type: string
          example: US
        regionName:
          description: Region name of the country/region of the address.
          type: string
          example: America
        revision:
          type: integer
          format: int32
          example: 1
        sortingCode:
          description: "Additional, country-specific, sorting code. This is not used\n\
            in most regions. Where it is used, the value is either a string like\n\
            \"CEDEX\", optionally followed by a number (e.g. \"CEDEX 7\"), or just\
            \ a number\nalone, representing the \"sector code\" (Jamaica), \"delivery\
            \ area indicator\"\n(Malawi) or \"post office indicator\" (e.g. C\xF4\
            te d'Ivoire).\n"
          type: string
          example: Jamaica
        sublocality:
          description: Sublocality of the address. This can be neighborhoods, boroughs,
            districts.
          type: string
        timezone:
          description: Time zone of the address.
          type: string
          example: America/Los_Angeles
        coordinates:
          description: Map coordinates of the address.
          $ref: '#/components/schemas/Latlng'
    PrimaryServiceProviderTmc:
      type: object
      title: PrimaryServiceProviderTmc
      description: Primary service provider TMC info
      required:
      - tmcId
      properties:
        tmcId:
          $ref: '#/components/schemas/CompanyId'
          description: Id of the service provider TMC.
    PrivacyDisclaimer:
      type: object
      title: PrivacyDisclaimer
      description: Html to shown on the the login page. This will be text with hyper
        links.
      properties:
        show:
          type: boolean
          default: true
        html:
          type: string
          description: String containing the html.
          example: </html>
    PrivacySettings:
      type: object
      title: PrivacySettings
      description: Privacy settings for a company.
      properties:
        allowCompanyAdminAccessToRelativesInfo:
          type: boolean
          description: Boolean to show if company admin can see information of users'
            relatives.
          example: true
    ProfileColors:
      type: array
      description: The list of colors relating to profile.
      items:
        $ref: '#/components/schemas/TitledColor'
    Reference:
      type: object
      title: Reference object containing uuid and name of the entity.
      description: Reference of an entity
      required:
      - id
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
    RewardsProgram:
      type: object
      title: RewardsProgram
      description: Rewards program details.
      properties:
        allowedUserRoles:
          type: array
          description: Roles entitled to redeem company's reward points.
          items:
            $ref: '#/components/schemas/RoleType'
        conversionRate:
          type: object
          description: Conversion rate of points to specified currency.
          title: ConversionRate
          properties:
            currencyCode:
              type: string
              example: USD
              description: The 3-letter currency code defined in ISO 4217.
            rate:
              type: number
              format: double
              description: Conversion rate of points to specified currency.
        redeemablePaymentMethods:
          type: array
          description: Payment methods to redeem company's reward points.
          items:
            $ref: '#/components/schemas/PaymentMethod'
        backingCurrency:
          type: array
          description: The currencies backing up this rewards program.
          items:
            type: string
            example: USD
    RoleType:
      type: string
      enum:
      - UNKNOWN_TYPE
      - COMPANY_ADMIN
      - COMPANY_TRAVEL_ARRANGER
      - TRAVEL_ARRANGER
      - COMPANY_REPORT_ADMIN
      - GLOBAL_ADMIN
      - GLOBAL_AGENT
      - TMC_AGENT
      - TMC_ADMIN
      example: TRAVEL_ARRANGER
    RoutingConfig:
      type: object
      title: RoutingConfig
      description: App routing configuration.
      properties:
        appHomeRoute:
          type: string
          example: /my-account
    RuleBasedInfo:
      title: RuleBasedInfo
      type: object
      description: Rule based processor info
      required:
      - ruleName
      - chargeProcessorType
      properties:
        ruleName:
          type: string
          description: Rule name
          example: Rule1
        chargeProcessorType:
          type: string
          description: Charge processor type
          example: RULE_BASED_INFO
    SecondaryServiceProviderTmc:
      type: object
      title: SecondaryServiceProviderTmc
      description: Secondary service provider TMC info
      required:
      - tmcId
      - supplier
      - travelType
      properties:
        tmcId:
          $ref: '#/components/schemas/CompanyId'
          description: Id of the service provider TMC.
        supplier:
          $ref: '#/components/schemas/SupplierType'
          description: Supplier for which this service provider should be used.
        travelType:
          $ref: '#/components/schemas/TravelType'
          description: Travel type for which this service provider should be used.
    ServiceCharge:
      type: object
      title: ServiceCharge
      description: Service charge.
      required:
      - amount
      - taxes
      - totalAmount
      properties:
        amount:
          $ref: '#/components/schemas/Money'
          description: Service charge amount.
        taxes:
          type: array
          description: Applicable taxes.
          items:
            $ref: '#/components/schemas/TaxAmount'
        totalAmount:
          $ref: '#/components/schemas/Money'
          description: Total amount (including taxes).
        allowedFoP:
          type: array
          deprecated: true
          description: List of allowed payment methods for this charge.
          items:
            $ref: '#/components/schemas/PaymentMethod'
        feeInfo:
          $ref: '#/components/schemas/FeeInfo'
    ServiceChargeEntity:
      type: object
      title: ServiceChargeEntity
      description: Entity for service charge.
      required:
      - entityType
      - entityId
      properties:
        entityType:
          $ref: '#/components/schemas/ServiceChargeEntityType'
          description: Entity type for service charge.
          example: TRIP
        entityId:
          type: string
          description: Entity ID for service charge e.g, trip ID for entityType=TRIP.
          example: '1234567890'
    ServiceChargeEntityType:
      type: string
      title: ServiceChargeEntityType
      description: Entity type for service charge.
      enum:
      - TRIP
      - USER
    ServiceChargeRecord:
      type: object
      title: ServiceChargeRecord
      description: Service charge record.
      required:
      - entity
      - serviceChargeType
      - serviceType
      - charge
      - transactionTime
      - paymentStatus
      properties:
        entity:
          $ref: '#/components/schemas/ServiceChargeEntity'
          description: Entity for service charge.
        tripId:
          type: string
          description: Trip ID (if applicable).
          example: '1234567890'
          deprecated: true
        serviceChargeType:
          $ref: '#/components/schemas/ServiceChargeType'
          description: Type of service charge.
          example: TMC_CHARGE
        serviceType:
          $ref: '#/components/schemas/ServiceType'
          description: Type of service.
          example: AGENT_CONTACT
        agentContact:
          $ref: '#/components/schemas/AgentContact'
          description: Agent contact details if serviceType is AGENT_CONTACT.
        chargeId:
          type: string
          description: Charge Id to uniquely identify service charge.
          example: e2c04834-92eb-4b62-9faa-eb73a70051d3
        charge:
          $ref: '#/components/schemas/ServiceCharge'
          description: Service charge.
        transactionTime:
          $ref: '#/components/schemas/DateTimeOffset'
          description: Transaction date and time.
        paymentStatus:
          $ref: '#/components/schemas/PaymentStatus'
          description: Payment status.
        userOrgId:
          $ref: '#/components/schemas/UserOrgId'
          description: User org details.
        legalEntityId:
          $ref: '#/components/schemas/LegalEntityId'
          description: The ID of the legal entity.
        bookingDetails:
          $ref: '#/components/schemas/BookingDetails'
          description: Booking details.
        fop:
          $ref: '#/components/schemas/FormOfPayment'
          description: Form of payment for the service charge.
        paymentTransaction:
          $ref: '#/components/schemas/PaymentTransaction'
          description: Payment transaction details.
        failureDetails:
          $ref: '#/components/schemas/FailureDetails'
          description: Failure details.
    ServiceChargeType:
      type: string
      title: ServiceChargeType
      description: 'Type of service charge. TMC_CHARGE: Service charge for a service
        charged by the TMC.

        '
      enum:
      - TMC_CHARGE
    ServiceFeeTransactionType:
      type: string
      title: ServiceFeeTransactionType
      description: Transaction type for service fee
      enum:
      - TRANSACTION_TYPE_BOOKING
      - TRANSACTION_TYPE_MODIFICATION
      - TRANSACTION_TYPE_CANCELLATION
    ServiceType:
      type: string
      title: ServiceType
      description: 'Type of service for service charge. ITINERARY_BOOKING: Service
        charge for travel booking. AGENT_CONTACT: Service charge for agent contact.

        '
      enum:
      - ITINERARY_BOOKING
      - AGENT_CONTACT
    SetCompanyPerTripSrvChargeRequest:
      type: object
      title: SetCompanyPerTripSrvChargeRequest
      description: Set per trip service charge configuration for company
      required:
      - config
      properties:
        config:
          $ref: '#/components/schemas/FixedChargeConfig'
    SocialMediaLinksConfig:
      type: object
      title: SocialMediaLinksConfig
      description: Social media links.
      properties:
        facebookUrl:
          type: string
          example: https://www.facebook.com/spotnanaofficial
        twitterUrl:
          type: string
          example: https://twitter.com/spotnana
        instagramUrl:
          type: string
          example: https://www.instagram.com/spotnana_official
    SupplierType:
      type: string
      description: Supplier type like Sabre, TravelFusion etc.
      enum:
      - SABRE
      - AMADEUS
      - TRAVEL_FUSION
      - FARELOGIX_NDC
      - ATPCO_NDC
      - TRAINLINE
      - AVIA
      - QBR
      - BCD
      - QANTAS_HOTELS
      - SOUTHWEST
      - EXPEDIA
      - HOTEL_HUB
      - NDC
      - MARRIOTT
      - CLEARTRIP
      - KYTE
      - GROUNDSPAN
      - SABRE_NDC
      - BOOKING_COM
      x-ignoreBreakingChanges:
      - SupplierType->BOOKING_COM
      example: SABRE
    SupportConfig:
      type: object
      title: SupportConfig
      description: Support Configuration for company
      properties:
        contactNumbers:
          type: array
          description: List of support phone numbers.
          items:
            $ref: '#/components/schemas/PhoneNumber'
        emailAddresses:
          type: array
          description: List of the support email addresses.
          uniqueItems: true
          items:
            type: string
            example: <EMAIL>
        freshdeskConfig:
          $ref: '#/components/schemas/FreshdeskConfig'
        tier:
          $ref: '#/components/schemas/Tier'
          default: BASIC
        genesysConfig:
          type: object
          title: GenesysConfig
          description: Configuration for genesys.
          required:
          - enabled
          - dataUrl
          properties:
            enabled:
              type: boolean
              description: Whether genesys is enabled or not.
              default: false
              example: true
            dataUrl:
              type: string
              description: Data url for genesys.
        portalUrls:
          type: array
          description: List of the support portal urls.
          uniqueItems: true
          items:
            type: string
            example: www.support.spotnana.com
        zendeskConfig:
          type: object
          title: ZendeskConfig
          description: Configuration for zendesk.
          required:
          - enabled
          - integrationId
          properties:
            enabled:
              type: boolean
              description: Whether Zendesk Chat is enabled or not.
              default: false
              example: true
            integrationId:
              type: string
              description: Integration ID for Zendesk Chat for Web.
              example: '1234567890'
            androidIntegrationId:
              type: string
              description: Integration ID for Zendesk Chat for Android.
              example: '1234567890'
            iosIntegrationId:
              type: string
              description: Integration ID for Zendesk Chat for iOS.
              example: '1234567890'
        genesysCloudConfig:
          type: object
          title: GenesysCloudConfig
          description: Configuration for Genesys cloud.
          required:
          - enabled
          - deploymentId
          - environment
          - genesysScriptUrl
          properties:
            enabled:
              type: boolean
              description: Whether Genesys Cloud is enabled or not.
              default: false
              example: true
            deploymentId:
              type: string
              description: DeploymentId for Genesys Cloud.
              example: 52dc2a6f-772a-4edf-96d3-b3f0c49400a7
            environment:
              type: string
              description: Environment for Genesys Cloud.
              example: usa-1
            genesysScriptUrl:
              type: string
              description: Url for Genesys Cloud Script.
              example: https://test.test.com
        twilioChatConfig:
          type: object
          title: TwilioChatConfig
          description: Configuration for Twilio Chat.
          required:
          - enabled
          properties:
            enabled:
              type: boolean
              description: Whether Twilio Chat is enabled or not.
        cognigyChatConfig:
          type: object
          title: CognigyChatConfig
          description: Cognigy Chat Config.
          properties:
            enabled:
              type: boolean
              description: Whether Cognigy chat is enabled or not.
              x-requiredFrom: '2025-05-12'
            scriptUrl:
              type: string
              description: Url for Cognigy Script.
              example: https://your-cognigy-instance.com/script/abcdef123456
              x-requiredFrom: '2025-05-12'
            initWebchatEndpoint:
              type: string
              description: Endpoint for Cognigy initWebchat method.
              example: https://your-cognigy-instance.com/initWebchat
              x-requiredFrom: '2025-05-12'
            sessionTimeout:
              type: integer
              format: integer
              description: Session timeout (in minutes) for Cognigy Chat.
              example: 30
              x-requiredFrom: '2025-05-12'
            cssUrl:
              type: string
              description: Url for Cognigy Chat CSS.
              example: https://your-server.com/css/chat.css
    SupportConfigs:
      title: SupportConfigs
      type: object
      properties:
        length:
          type: integer
          format: int32
        elements:
          type: array
          description: Support Configurations List.
          items:
            $ref: '#/components/schemas/SupportConfig'
    TaxAmount:
      type: object
      title: Tax
      description: Tax
      required:
      - amount
      - taxCode
      - description
      properties:
        amount:
          $ref: '#/components/schemas/Money'
        taxCode:
          type: string
          description: Tax code
          example: VAT
        description:
          type: string
          description: Tax code
        percentage:
          type: number
          format: double
          description: Amount
          example: 10
    ThirdPartySource:
      type: string
      enum:
      - UNKNOWN_SOURCE
      - SABRE
      - TRAVEL_FUSION
      - AVIA
      - NDC
      - TRAINLINE
      - ATPCO_NDC
      - FARELOGIX_NDC
      - OFFLINE
      - CONNEXUS
      - ROUTEHAPPY
      - AMADEUS
      - GIATA
      - QBR
      - BCD
      - QANTAS_HOTELS
      - SOUTHWEST
      - EXPEDIA
      - HOTEL_HUB
      - MARRIOTT
      - CLEARTRIP
      - KYTE
      - GROUNDSPAN
      - SABRE_NDC
      - BOOKING_COM
      x-ignoreBreakingChanges:
      - ThirdPartySource->BOOKING_COM
      example: SABRE
    Tier:
      type: string
      title: Tier
      description: 'Tier of User.

        Note: In default UI, BASIC is displayed as Standard, and SEAT1A is displayed
        as VIP.

        '
      enum:
      - BASIC
      - SEAT1A
      x-enum-varnames:
      - BASIC
      - SEAT1A
      example: SEAT1A
      default: BASIC
    TierChargeConfig:
      type: object
      title: Tier charge configuration for service charge
      required:
      - tier
      - charges
      properties:
        tier:
          $ref: '#/components/schemas/Tier'
        charges:
          type: array
          description: Amount in specified currency
          items:
            $ref: '#/components/schemas/Money'
    TierConfig:
      type: object
      title: TierConfig
      description: Tier configuration.
      properties:
        tiers:
          type: array
          description: A list of tiers for this company.
          items:
            $ref: '#/components/schemas/TierDefinition'
    TierDefinition:
      type: object
      title: TierDefinition
      description: Tier that controls the support features available to a user.
      required:
      - name
      - tier
      properties:
        name:
          type: string
          description: Tier name.
        icon:
          type: string
          description: URL representing tier icon.
        tier:
          $ref: '#/components/schemas/Tier'
    TitledColor:
      type: object
      description: Color with associated title.
      required:
      - title
      - color
      properties:
        title:
          $ref: '#/components/schemas/ColorTitle'
        color:
          type: string
          example: '#FF0000'
    TmcBasicInfo:
      type: object
      title: TmcBasicInfo
      description: TMC related basic information.
      required:
      - contractingTmc
      - bookingTmc
      properties:
        contractingTmc:
          $ref: '#/components/schemas/CompanyRef'
          description: Contracting TMC is the TMC the user/organization contracted.
        bookingTmc:
          $ref: '#/components/schemas/CompanyRef'
          description: Booking TMC is the TMC used for the bookings for the user/organization.
    TmcCalculatorInfo:
      title: TmcCalculatorInfo
      type: object
      description: TMC calculator processor info
      required:
      - chargeProcessorType
      - calculatorType
      properties:
        calculatorType:
          type: string
          description: TMC service charge calculator type
          example: SPOTNANA
        chargeProcessorType:
          type: string
          description: Charge processor type
          example: TMC_CALCULATOR_INFO
    TmcDefaultConfiguration:
      type: object
      title: TmcDefaultConfiguration
      description: Default TMC configuration.
      properties:
        defaultBookingTmcId:
          type: string
          format: uuid
          description: Default booking TMC to be used. If not set, then TMC itself
            will be default booking tmc.
          example: 734f4ea2-e9ed-4c90-853c-9eed62f1254b
        countryWiseBookingTmcs:
          type: array
          description: Override for booking TMCs per country.
          items:
            type: object
            title: CountryWiseBookingTmcs
            required:
            - countryCode
            - bookingTmcId
            properties:
              countryCode:
                type: string
                example: US
                description: The ISO 2-character country code for which booking TMCs
                  needs to be overriden.
              bookingTmcId:
                type: string
                format: uuid
                description: Booking TMC for above country code.
                example: 734f4ea2-e9ed-4c90-853c-9eed62f1254b
    TmcInfo:
      type: object
      title: TmcInfo
      description: TMC config information.
      required:
      - id
      - primaryServiceProviderTmc
      properties:
        id:
          $ref: '#/components/schemas/CompanyId'
          description: TMC id.
        primaryServiceProviderTmc:
          $ref: '#/components/schemas/PrimaryServiceProviderTmc'
          description: Primary service provider TMC for the TMC.
        secondaryServiceProviderTmcs:
          type: array
          description: Secondary service provider TMCs for the TMC.
          items:
            $ref: '#/components/schemas/SecondaryServiceProviderTmc'
        partnerTmcId:
          $ref: '#/components/schemas/CompanyId'
          description: Useful to identify the clients onboarded by a PARTNER_TMC
    TmcPartnerRoleMapping:
      type: object
      title: TmcPartnerRoleMapping
      description: Mapping between spotnana role and partner company's role
      required:
      - partnerRole
      - spotnanaRole
      properties:
        partnerRole:
          type: string
          description: User's role at partner.
        spotnanaRole:
          $ref: '#/components/schemas/RoleType'
    TokenVerifier:
      type: object
      title: TokenVerifier
      description: Method to verify the external token sent by partners.
      required:
      - doVerifyToken
      properties:
        doVerifyToken:
          type: boolean
          description: Token verification rules will be applied only if this is true.
        type:
          type: string
          description: Method to verify the external token sent by partners.
          enum:
          - OKTA_JWT
        verifierDetails:
          $ref: '#/components/schemas/VerifierDetails'
    TokenizedExpiry:
      title: TokenizedExpiry
      type: object
      description: Contains the tokenized expiry of a Card.
      required:
      - expiryMonth
      - expiryYear
      properties:
        expiryMonth:
          type: string
          description: Tokenized Expiry month
          example: KvAuPANQWCpjwRQxcC8EXg==
        expiryYear:
          type: string
          description: Tokenized Expiry year
          example: fPBm0OWrKwPyIrCVcbg4cA==
    TokenizedExpiryWrapper:
      type: object
      title: TokenizedExpiryWrapper
      properties:
        tokenizedExpiry:
          $ref: '#/components/schemas/TokenizedExpiry'
    TransactionFeeInfo:
      type: object
      title: TransactionFeeInfo
      description: Transaction Fee Info
      required:
      - bookingFeeType
      properties:
        bookingFeeType:
          type: string
          description: Type of the booking fee
          example: TRANSACTION_FEE
        calculatedAmount:
          $ref: '#/components/schemas/Money'
        chargeProcessorInfo:
          $ref: '#/components/schemas/ChargeProcessorInfo'
        transactionFeeType:
          $ref: '#/components/schemas/ServiceFeeTransactionType'
    TravelContentConfig:
      type: object
      title: TravelContentConfig
      description: Travel content config
      properties:
        airConfig:
          description: Air content config
          $ref: '#/components/schemas/AirConfig'
    TravelType:
      type: string
      title: TravelType
      description: Travel Type
      enum:
      - AIR
      - HOTEL
      - CAR
      - RAIL
      - LIMO
      - MISC
      - ALL
      example: AIR
    TripFeeInfo:
      type: object
      title: TripFeeInfo
      description: Trip Fee Info
      required:
      - bookingFeeType
      properties:
        bookingFeeType:
          type: string
          description: Type of the booking fee
          example: TRIP_FEE
        calculatedAmount:
          $ref: '#/components/schemas/Money'
        chargeProcessorInfo:
          $ref: '#/components/schemas/ChargeProcessorInfo'
    UAPassPlusMetadataExternal:
      type: object
      title: UAPassPlusMetadata
      description: Details for UAPassPlus Metadata.
      properties:
        uatpInfo:
          type: object
          title: uatpInfo
          description: UATP card information for UAPassPlus
    UAPassPlusMetadataWrapperExternal:
      type: object
      title: UAPassPlusMetadataResponseWrapper
      description: Wrapper for UAPassPlus response metadata
      required:
      - uaPassPlusMetadata
      properties:
        uaPassPlusMetadata:
          $ref: '#/components/schemas/UAPassPlusMetadataExternal'
    UpdateFeatureConfig:
      type: object
      title: FeatureConfig
      description: Feature enabled for company.
      required:
      - name
      - value
      properties:
        name:
          $ref: '#/components/schemas/FeatureNameEnum'
        value:
          type: string
          description: Value associated with feature converted in String format.
    UpdateFeaturesRequest:
      type: object
      title: UpdateFeaturesRequest
      properties:
        featuresToUpdate:
          type: array
          description: Features that has to be updated.
          items:
            $ref: '#/components/schemas/UpdateFeatureConfig'
        featuresToDelete:
          type: array
          description: Features that has to be deleted.
          items:
            $ref: '#/components/schemas/FeatureNameEnum'
    UserId:
      type: object
      title: UserId
      description: User identifier
      required:
      - id
      properties:
        id:
          type: string
          format: uuid
    UserIdentifierType:
      type: string
      enum:
      - PID
      - EMAIL
      description: Type of identifier for auth code Authentication
    UserInfoEndpointResponseAttribute:
      type: object
      title: UserInfoEndpointResponseAttribute
      description: auth Code metadata to get the user authenticated.
      required:
      - name
      - jsonXpath
      properties:
        name:
          description: Attribute name
          type: string
          enum:
          - PID
          - EMAIL
          example: PID
        jsonXpath:
          type: string
          description: Json xpath of the Attribute/Parameter value location
          example: $.user.email
    UserOrgId:
      type: object
      title: UserOrgId
      description: User and user's organization information.
      required:
      - userId
      - organizationId
      properties:
        organizationAgencyId:
          $ref: '#/components/schemas/OrganizationAgencyId'
        organizationId:
          $ref: '#/components/schemas/OrganizationId'
        userId:
          $ref: '#/components/schemas/UserId'
        tmcInfo:
          $ref: '#/components/schemas/TmcInfo'
          deprecated: true
        tmcBasicInfo:
          $ref: '#/components/schemas/TmcBasicInfo'
    ValueAddedServiceFeeInfo:
      type: object
      title: ValueAddedServiceFeeInfo
      description: Value Added Service Fee Info
      required:
      - bookingFeeType
      properties:
        bookingFeeType:
          type: string
          description: Type of the booking fee
          example: VALUE_ADDED_SERVICE_FEE
        calculatedAmount:
          $ref: '#/components/schemas/Money'
        chargeProcessorInfo:
          $ref: '#/components/schemas/ChargeProcessorInfo'
        valueAddedServiceFeeType:
          $ref: '#/components/schemas/ValueAddedServiceFeeType'
    ValueAddedServiceFeeType:
      type: string
      title: ValueAddedServiceFeeType
      description: Value added service fee type
      enum:
      - VIRTUAL_CARD_PAYMENT
      - PRICE_OPTIMIZATION
      - UNUSED_CREDIT_APPLICATION
      example: VIRTUAL_CARD_PAYMENT
    VendorProgramPaymentMetadata2:
      type: object
      title: VendorProgramPaymentMetadata
      description: Metadata related to vendor program payment method
      oneOf:
      - $ref: '#/components/schemas/DirectBillingWrapper2'
      - $ref: '#/components/schemas/UAPassPlusMetadataWrapperExternal'
    VerifierDetails:
      type: object
      title: VerifierDetails
      oneOf:
      - $ref: '#/components/schemas/OktaJwtVerifierWrapper'
    VirtualCardInfo:
      type: object
      title: VirtualCardInfo
      description: Virtual card information like maximum deployment amount, valid
        date range.
      properties:
        amount:
          description: Virtual card deployment amount. This amount will drive the
            maximum authorisation value permitted on the virtual card.
          $ref: '#/components/schemas/Money'
        dateRange:
          description: Date range within which the virtual card can be charged.
          $ref: '#/components/schemas/DateRange'
    VirtualCardPaymentMetadata:
      title: VirtualCardPaymentMetadata
      type: object
      description: Metadata for Virtual Card.
      required:
      - vendorInfo
      properties:
        vendorInfo:
          $ref: '#/components/schemas/VirtualCardVendorInfo'
        cardInfo:
          $ref: '#/components/schemas/VirtualCardInfo'
        paymentInstructionId:
          type: string
          description: Payment instruction id set during addition of virtual card
            payment source
          example: 1eb8b778-f0a6-4037-865c-4580982fa36e
        shouldReveal:
          type: boolean
          description: Identifier for when to reveal virtual cards as they are revealed
            to travellers only 24hrs before check-in.
          example: false
    VirtualCardVendor:
      type: string
      description: Type of Virtual card vendor
      enum:
      - CONFERMA
      example: CONFERMA
    VirtualCardVendorInfo:
      type: object
      title: VirtualCardVendorInfo
      description: Virtual card related information like vendor, cardId, card-pool
        id of vendor etc.
      required:
      - vendorCardId
      - virtualCardVendorCardPoolId
      properties:
        vendor:
          $ref: '#/components/schemas/VirtualCardVendor'
        vendorCardId:
          type: string
          description: Virtual card id.
          example: '68793680'
        virtualCardVendorCardPoolId:
          type: string
          description: Card pool id of virtual card vendor.
          example: '51907'
        confermaInfo:
          $ref: '#/components/schemas/ConfermaInfo'
    WebLinksConfig:
      type: object
      title: WebLinksConfig
      description: Weblinks which are parts of WhiteLabelConfig.
      properties:
        logoHeader:
          type: string
          example: https://duploservices-stage-email-assets-************.s3.us-west-2.amazonaws.com/confirmation/spotnana3.png
        logoFooter:
          type: string
          example: https://duploservices-stage-email-assets-************.s3-us-west-2.amazonaws.com/approval/spotnana-s-logo.png
        companyWebsiteAddress:
          type: string
          example: https://www.spotnana.com
        homePage:
          type: string
          example: https://stage-app.spotnana.com/flights
        itineraryPage:
          type: string
          example: https://stage-app.spotnana.com/trips/3375488451
        disclaimerPage:
          type: string
          example: https://www.spotnana.com/terms
        privacyPage:
          type: string
          example: https://www.spotnana.com/privacy-policy
        hardApprovalUrl:
          type: string
          example: https://stage-app.spotnana.com/hardapproval?approvalId=******************************************************************%3D%3D&approverName=Pihu%20Gupta&travelerName=Pihu%20Gupta&tripName=TESting%201&tripId=2418132231&voidDate=Tuesday,%2021%20Jun%20at%2002:00%20UTC
        itineraryFlightStatsUrl:
          type: string
          example: https://stage-app.spotnana.com/trips/4694735622
        appEmbedScriptUrl:
          type: string
          example: https://customjs.partner.com
        logoNavigationLink:
          type: string
          example: http://www.spotnana.com
        illustration:
          type: string
          example: https://duploservices-stage-email-assets-************.s3-us-west-2.amazonaws.com/approval/spotnana-s-logo.png
        poweredBySpotnana:
          type: boolean
          default: true
          example: false
        logoutRedirectUrl:
          type: string
          example: https://www.spotnana.com/signout
        faviconUrl:
          type: string
          description: Display logo on the browser title.
          example: http://www.spotnana.com
        appDownloadLinks:
          $ref: '#/components/schemas/AppDownloadLinksConfig'
        socialMediaLinks:
          $ref: '#/components/schemas/SocialMediaLinksConfig'
        termsOfUse:
          type: string
          description: Terms of service/legal agreement between a service provider
            and a person who wants to use service.
          example: https://www.spotnana.com/terms/
        illustrationActionUrl:
          type: string
          example: https://www.spotnana.com/login
    Weight:
      type: object
      title: Weight
      description: Specifies weight unit and weight amount.
      required:
      - weight
      - unit
      properties:
        weight:
          type: number
          description: Amount of weight.
          format: double
          example: 150
        unit:
          type: string
          description: Unit of weight.
          enum:
          - KG
          - LBS
          example: KG
    WhiteLabelConfig:
      type: object
      title: WhitelabelConfig
      description: Whitelabel configuration.
      required:
      - colors
      properties:
        companyName:
          type: string
          example: Spotnana
        colors:
          $ref: '#/components/schemas/ColorWhiteLabelConfig'
        webLinks:
          $ref: '#/components/schemas/WebLinksConfig'
        fromEmailAddress:
          type: string
          example: <EMAIL>
        fontFamily:
          type: string
          example: Arial
          default: Arial
        emailFontFamily:
          type: string
          example: Roboto
          default: Roboto
        fontFamilyCss:
          type: string
          example: https://duploservices-stage-email-assets-************.s3.us-west-2.amazonaws.com/partners/amazon/emberDisplay.css
        emailClientConfig:
          $ref: '#/components/schemas/EmailClientConfig'
          description: Email client configuration representing the email provider
            to be used.
        clientRoutingBasePath:
          type: string
          description: Base path used by Spotnana OBT application to invoke API calls.
            Default path value is /
        routingConfig:
          $ref: '#/components/schemas/RoutingConfig'
        privacyDisclaimer:
          $ref: '#/components/schemas/PrivacyDisclaimer'
        logoConfig:
          $ref: '#/components/schemas/LogoConfig'
tags:
- name: Companies
  description: APIs to onboard and manage company.
- name: Legal Entities
  description: APIs to create and manage company legal entities.
- name: Cost Centers
  description: APIs to create and manage company cost centers.
- name: Offices
  description: APIs to create and manage company offices.
- name: Departments
  description: APIs to create and manage departments.
- name: Service Charge Plan
  description: APIs to manage service charge plans.
paths:
  /v2/companies:
    post:
      tags:
      - Companies
      summary: Create company
      description: This endpoint creates a company. This is allowed only by TMC_ADMIN
        and above roles.
      operationId: createCompany
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCompanyRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EntityId'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
    get:
      tags:
      - Companies
      summary: List companies
      description: "This endpoint lists companies. If externalId is provided in query\
        \ param, only the company having \nthe specified external ID is returned.\n"
      operationId: listCompanies
      parameters:
      - name: externalId
        in: query
        description: External id of the entity.
        required: false
        schema:
          type: string
        example: my-external-id
      - name: companyRole
        in: query
        description: Filter by a particular role
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/CompanyRole'
        example: ORG
      - name: companyIds
        in: query
        description: Filter by a list of companies
        required: false
        schema:
          type: array
          items:
            type: string
            format: uuid
      - name: contractingTmcIds
        in: query
        description: Filter by contracting TMCs
        required: false
        schema:
          type: array
          items:
            type: string
            format: uuid
      - name: bookingTmcIds
        in: query
        description: Filter by booking TMCs
        required: false
        schema:
          type: array
          items:
            type: string
            format: uuid
      - name: offset
        in: query
        description: Indicates the start index to read companies from the server.
        schema:
          type: integer
          default: 0
          minimum: 0
      - name: limit
        in: query
        description: Maximum number of results to be fetched.
        schema:
          type: integer
          default: 100
          minimum: 0
          maximum: 100
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ArrayOfReference'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/companies/{companyId}:
    get:
      tags:
      - Companies
      summary: Get company
      description: This endpoint gets a company by ID.
      operationId: readCompany
      parameters:
      - name: companyId
        in: path
        description: Identifier for company. External id can be supplied in `X` prefixed
          form. For instance, if external_id = abc, then Xabc, has to be passed.
        required: true
        schema:
          type: string
        example: 4974a66b-7493-4f41-908c-58ba81093947
      - in: header
        name: x-application-id
        schema:
          type: string
          format: uuid
        example: 4974a66b-7493-4f41-908c-58ba81093947
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    put:
      tags:
      - Companies
      summary: Update company
      operationId: updateCompany
      description: This endpoint updates a company by ID.
      parameters:
      - name: companyId
        in: path
        description: Identifier for company.
        required: true
        schema:
          type: string
          format: uuid
        example: 4974a66b-7493-4f41-908c-58ba81093947
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Company'
      responses:
        '204':
          description: Updated Successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      tags:
      - Companies
      summary: Delete company
      description: This endpoint deletes a company by ID.
      operationId: deleteCompany
      parameters:
      - name: companyId
        in: path
        description: Identifier for company.
        required: true
        schema:
          type: string
          format: uuid
        example: 4974a66b-7493-4f41-908c-58ba81093947
      responses:
        '204':
          description: Deleted Successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/companies/{companyId}/legal-entities:
    parameters:
    - name: companyId
      in: path
      description: Identifier for company.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    post:
      tags:
      - Legal Entities
      summary: Create legal entity
      description: This endpoint creates a legal entity.
      operationId: createLegalEntity
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LegalEntityCreateRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EntityId'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
    get:
      tags:
      - Legal Entities
      summary: List legal entities
      description: "This endpoint lists legal entities of a company. If externalId\
        \ is provided in the query param \nonly the legal entity having the provided\
        \ externalId is returned.\n"
      operationId: listLegalEntities
      parameters:
      - name: pageNumber
        in: query
        description: Page number to fetch for the query. Page number starts from 1.
        required: false
        schema:
          type: integer
          format: int32
          minimum: 1
          default: 1
          example: 2
      - name: pageSize
        in: query
        description: Page size to be to fetch for the query.
        required: false
        schema:
          type: integer
          format: int32
          minimum: 1
          maximum: 1000
          default: 1000
          example: 20
      - name: externalId
        in: query
        description: External id of the entity.
        required: false
        schema:
          type: string
        example: my-external-id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ArrayOfReference'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/companies/{companyId}/legal-entities/{legalEntityId}:
    parameters:
    - name: companyId
      in: path
      description: Identifier for company.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    - name: legalEntityId
      in: path
      description: Identifier for legal-entity.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    get:
      tags:
      - Legal Entities
      summary: Get legal entity
      description: This endpoint gets a legal entity by ID.
      operationId: getLegalEntity
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LegalEntity'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    put:
      tags:
      - Legal Entities
      summary: Update legal entity
      description: This endpoint updates a legal entity by ID.
      operationId: updateLegalEntity
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LegalEntityUpdateRequest'
      responses:
        '204':
          description: Updated Successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      tags:
      - Legal Entities
      summary: Delete legal entity
      description: This endpoint deletes a legal entity by ID.
      operationId: deleteLegalEntity
      responses:
        '204':
          description: Deleted
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/companies/{companyId}/cost-centers:
    parameters:
    - name: companyId
      in: path
      description: Identifier for company.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    post:
      tags:
      - Cost Centers
      summary: Create cost center
      description: This endpoint creates a cost center.
      operationId: createCostCenter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCostCenterRequest'
      responses:
        '201':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EntityId'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
    get:
      tags:
      - Cost Centers
      summary: List cost centers
      description: "This endpoint lists cost centers of a company. If an externalId\
        \ is provided in the query parameter \nonly the cost center with the provided\
        \ externalId is returned.\n"
      operationId: listCostCenters
      deprecated: true
      parameters:
      - name: externalId
        in: query
        description: External id of the cost center.
        required: false
        schema:
          type: string
        example: cost-center-external-id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ArrayOfReference'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/companies/{companyId}/cost-centers/{costCenterId}:
    parameters:
    - name: companyId
      in: path
      description: Identifier for company.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    - name: costCenterId
      in: path
      description: Identifier for cost-center.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    get:
      tags:
      - Cost Centers
      summary: Get cost center
      description: This endpoint gets a cost center by ID.
      operationId: getCostCenter
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CostCenter'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    put:
      tags:
      - Cost Centers
      summary: Update cost center
      operationId: updateCostCenter
      description: This endpoint updates a cost center by ID.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CostCenter'
      responses:
        '204':
          description: Updated Successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      tags:
      - Cost Centers
      summary: Delete cost center
      description: This endpoint deletes a cost center by ID.
      operationId: deleteCostCenter
      parameters:
      - name: detachUsers
        in: query
        description: Whether to remove this cost center's reference from associated
          user entities.
        schema:
          type: boolean
          default: false
      responses:
        '204':
          description: No Content
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/companies/{companyId}/legal-entities/{legalEntityId}/offices:
    parameters:
    - name: companyId
      in: path
      description: Identifier for company.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    - name: legalEntityId
      in: path
      description: Identifier for legal-entity.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093946
    post:
      tags:
      - Offices
      summary: Create office
      description: This endpoint creates an office.
      operationId: createOffice
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOfficeRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EntityId'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
    get:
      tags:
      - Offices
      summary: List offices of the legal entity
      description: "This endpoint list offices of given legal entity. If externalId\
        \ is provided in query param, the office \nhaving the externalId is fetched.\n"
      operationId: listLegalEntityOffices
      parameters:
      - name: externalId
        in: query
        description: External id of the entity.
        required: false
        schema:
          type: string
        example: office-external-id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ArrayOfReference'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/companies/{companyId}/legal-entities/{legalEntityId}/offices/{officeId}:
    parameters:
    - name: companyId
      in: path
      description: Identifier for company.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    - name: legalEntityId
      in: path
      description: Identifier for legal-entity.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    - name: officeId
      in: path
      description: Identifier for office.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    get:
      tags:
      - Offices
      summary: Get office
      description: This endpoint gets an office by ID.
      operationId: getOffice
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OfficeV2'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    put:
      tags:
      - Offices
      summary: Update office
      description: This endpoing updates office by ID.
      operationId: updateOffice
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OfficeV2'
      responses:
        '204':
          description: Updated successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      tags:
      - Offices
      summary: Delete office
      description: This endpoint deletes office by ID.
      operationId: deleteOffice
      parameters:
      - name: detachUsers
        in: query
        description: Whether to remove this entity's reference from associated user
          entities.
        schema:
          type: boolean
          default: false
      responses:
        '204':
          description: Deleted
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/companies/{companyId}/departments:
    parameters:
    - name: companyId
      in: path
      description: Identifier for company.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    post:
      tags:
      - Departments
      summary: Create department
      description: This endpoint creates a department.
      operationId: createDepartment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDepartmentRequest'
      responses:
        '201':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EntityId'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
    get:
      tags:
      - Departments
      summary: List departments
      description: This endpoint lists departments of a company.
      operationId: listDepartments
      deprecated: true
      parameters:
      - name: externalId
        in: query
        description: External id of the department.
        required: false
        schema:
          type: string
        example: department-external-id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ArrayOfReference'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
  /v2/companies/{companyId}/departments/{departmentId}:
    parameters:
    - name: companyId
      in: path
      description: Identifier for company.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    - name: departmentId
      in: path
      description: Identifier for department.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    get:
      tags:
      - Departments
      summary: Get department
      description: This endpoint gets a department by ID.
      operationId: getDepartment
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DepartmentV2'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    put:
      tags:
      - Departments
      summary: Update department
      description: This endpoint updates department by ID.
      operationId: updateDepartment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepartmentV2'
      responses:
        '204':
          description: Updated Successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      tags:
      - Departments
      summary: Delete department
      description: This endpoint deletes department by ID.
      operationId: deleteDepartment
      parameters:
      - name: detachUsers
        in: query
        description: Whether to remove this entity's reference from associated user
          entities.
        schema:
          type: boolean
          default: false
      responses:
        '204':
          description: Deleted Successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/companies/{companyId}/features:
    parameters:
    - name: companyId
      in: path
      description: Identifier for company.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    - in: header
      name: X-Application-Id
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    - name: companyRole
      in: query
      schema:
        $ref: '#/components/schemas/CompanyRole'
      example: TMC
    get:
      tags:
      - Companies
      summary: Get company features
      operationId: getCompanyFeatures
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureConfigs'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    patch:
      tags:
      - Companies
      summary: Update Company Features
      operationId: updateCompanyFeaturesPatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateFeaturesRequest'
      responses:
        '200':
          description: OK
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/companies/{companyId}/travel-content-config:
    parameters:
    - name: companyId
      in: path
      description: Identifier for company.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    get:
      tags:
      - Companies
      summary: Get company travel content config
      operationId: getCompanyTravelContentConfig
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TravelContentConfig'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    put:
      tags:
      - Companies
      summary: Update company travel content config
      operationId: updateCompanyTravelContentConfig
      requestBody:
        description: Travel content config of a given company
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TravelContentConfig'
      responses:
        '200':
          description: OK
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      tags:
      - Companies
      summary: Delete company travel content config
      operationId: deleteCompanyTravelContentConfig
      responses:
        '200':
          description: OK
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/companies/{companyId}/white-label-config:
    parameters:
    - name: companyId
      in: path
      description: Identifier for company.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    put:
      tags:
      - Companies
      summary: Updates white label config
      description: This endpoint stores white label config for the company at different
        entity levels.
      operationId: updateCompanyWhiteLabelConfig
      parameters:
      - name: companyRole
        in: query
        required: true
        schema:
          $ref: '#/components/schemas/CompanyRole'
        example: ORG
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WhiteLabelConfig'
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    get:
      tags:
      - Companies
      summary: Get white label config
      description: This endpoint fetches the white label config for the company.
      operationId: getCompanyWhiteLabelConfig
      parameters:
      - name: companyRole
        in: query
        required: true
        schema:
          $ref: '#/components/schemas/CompanyRole'
        example: ORG
      - name: applicationId
        in: query
        required: false
        schema:
          type: string
          format: uuid
        example: 1234a66b-7493-4f41-908c-58ba81055667
        description: Application context to get white label config.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WhiteLabelConfig'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      tags:
      - Companies
      summary: Delete white label config
      description: This endpoint deletes the white label config for the company.
      operationId: deleteCompanyWhiteLabelConfig
      parameters:
      - name: companyRole
        in: query
        required: true
        schema:
          $ref: '#/components/schemas/CompanyRole'
        example: ORG
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/license/company/{companyId}/service-charge/per-trip:
    parameters:
    - name: companyId
      in: path
      description: Company ID
      required: true
      schema:
        type: string
        format: uuid
        example: f49d00fe-1eda-4304-ba79-a980f565281d
    get:
      tags:
      - Service Charge Plan
      summary: Get per-trip service charge configuration
      description: Get per-trip service charge configuration for company
      operationId: getCompanyPerTripSrvCharge
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCompanyPerTripSrvChargeResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    post:
      tags:
      - Service Charge Plan
      summary: Set per-trip service charge configuration
      description: Set per-trip service charge configuration for company
      operationId: setCompanyPerTripSrvCharge
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetCompanyPerTripSrvChargeRequest'
      responses:
        '204':
          description: OK
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/service-charge/{entityType}/{entityId}/types/{type}/list:
    parameters:
    - name: entityType
      in: path
      description: Entity type for service charge
      required: true
      schema:
        $ref: '#/components/schemas/ServiceChargeEntityType'
        example: TRIP
    - name: entityId
      in: path
      description: Entity ID for service charge e.g. trip ID for entityType = TRIP
      required: true
      schema:
        type: string
        example: '1234567890'
    - name: type
      in: path
      description: Service charge type.
      required: true
      schema:
        $ref: '#/components/schemas/ServiceChargeType'
        example: TMC_CHARGE
    post:
      tags:
      - Service Charge
      summary: List service charges
      description: List service charges for a given entity
      operationId: listServiceCharges
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ListServiceChargesRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListServiceChargesResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/companies/{companyId}/enrollment-config:
    parameters:
    - name: companyId
      in: path
      description: Identifier for company.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    put:
      tags:
      - Company Enrollment
      summary: Updates Enrollment config
      description: This endpoint stores Enrollment config for the company at TMC level.
      operationId: updateCompanyEnrollmentConfig
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnrollmentConfig'
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    get:
      tags:
      - Company Enrollment
      summary: Get enrollment config
      description: This endpoint fetches the enrollment config for the company.
      operationId: getCompanyEnrollmentConfig
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnrollmentConfig'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/companies/{companyId}/color-config:
    parameters:
    - name: companyId
      in: path
      description: Identifier for company.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    put:
      tags:
      - Companies
      summary: Updates Color config
      description: This endpoint stores Color config for the company at some entity
        level.
      operationId: updateColorConfig
      parameters:
      - name: companyRole
        in: query
        required: true
        schema:
          $ref: '#/components/schemas/CompanyRole'
        example: TMC
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ColorConfig'
      responses:
        '200':
          description: OK
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    get:
      tags:
      - Companies
      summary: Get color config
      description: This endpoint fetches the color config for the given entity.
      operationId: getColorConfig
      parameters:
      - name: companyRole
        in: query
        required: true
        schema:
          $ref: '#/components/schemas/CompanyRole'
        example: TMC
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ColorConfig'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
  /v2/companies/{companyId}/tier-config:
    parameters:
    - name: companyId
      in: path
      description: Identifier for company.
      required: true
      schema:
        type: string
        format: uuid
      example: 4974a66b-7493-4f41-908c-58ba81093947
    - name: companyRole
      in: query
      required: false
      schema:
        $ref: '#/components/schemas/CompanyRole'
      example: ORG
    put:
      tags:
      - Company Tier
      summary: Updates Tier config
      description: This endpoint stores Tier config for the company at TMC level.
      operationId: updateCompanyTierConfig
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TierConfig'
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    get:
      tags:
      - Company Tier
      summary: Get tier config
      description: This endpoint fetches the tier config for the company.
      operationId: getCompanyTierConfig
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TierConfig'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
