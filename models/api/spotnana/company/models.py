# Code generated from OpenAPI spec. DO NOT EDIT.
# Source: CompanyApi.yaml
# Generated by: datamodel-code-generator
# 
# This file is automatically generated from the Spotnana Company API OpenAPI specification.
# Any manual changes will be overwritten when the models are regenerated.
# 
# To regenerate: uv run python manage_spotnana_models.py generate

# generated by datamodel-codegen:
#   filename:  CompanyApi.yaml
#   timestamp: 2025-07-15T00:32:25+00:00

from __future__ import annotations

from collections.abc import Mapping, Sequence
from enum import Enum
from typing import Any, Literal
from uuid import UUID

from pydantic import BaseModel, ConfigDict, EmailStr, Field, RootModel, conint, constr


class AgentContactOp(Enum):
    AGENT_BOOKING = 'AGENT_BOOKING'
    AGENT_MODIFY = 'AGENT_MODIFY'
    AGENT_CANCEL = 'AGENT_CANCEL'
    AGENT_OTHER = 'AGENT_OTHER'
    AGENT_EXCHANGE = 'AGENT_EXCHANGE'
    ANCILLARY_PURCHASE = 'ANCILLARY_PURCHASE'
    SHELL_PNR_CREATE = 'SHELL_PNR_CREATE'
    SHELL_PNR_MODIFY = 'SHELL_PNR_MODIFY'
    SHELL_PNR_CANCEL = 'SHELL_PNR_CANCEL'
    SHELL_PNR_EXCHANGE = 'SHELL_PNR_EXCHANGE'
    MANUAL_FORM_CREATE = 'MANUAL_FORM_CREATE'
    MANUAL_FORM_MODIFY = 'MANUAL_FORM_MODIFY'
    MANUAL_FORM_CANCEL = 'MANUAL_FORM_CANCEL'
    MANUAL_FORM_EXCHANGE = 'MANUAL_FORM_EXCHANGE'
    UNUSED_TICKET_MANAGEMENT = 'UNUSED_TICKET_MANAGEMENT'


class AgentContactType(Enum):
    PHONE = 'PHONE'
    CHAT = 'CHAT'
    EMAIL = 'EMAIL'


class Announcement(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    title: str = Field(
        ...,
        description='Title of the announcement.',
        examples=['Spotnana on Mission to Unbundle Travel'],
    )
    description: str = Field(
        ...,
        description='Description of the announcement.',
        examples=['Amex Ventures Joins Spotnana on Mission to Unbundle Travel.'],
    )
    linkUrl: str | None = Field(
        None,
        description='Announcement link url which points to the atual announcement page.',
        examples=[
            'https://medium.com/inside-spotnana/amex-ventures-joins-spotnana-on-mission-to-unbundle-travel-3cdb2ecceff4'
        ],
    )
    linkDisplayText: str | None = Field(
        None,
        description='Announcement link display text.',
        examples=['New announcement'],
    )


class AppDownloadLinksConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    androidPlayStoreUrl: str | None = Field(
        None, examples=['https://play.google.com/store/apps/details?id=com.spotnana']
    )
    iosAppStoreUrl: str | None = Field(
        None,
        examples=['https://apps.apple.com/in/app/spotnana-travel-booking/id1580021446'],
    )
    qrCodeUrl: str | None = Field(None, examples=['https://www.spotnana.com'])


class TripUsageType(Enum):
    TRIP_USAGE_TYPE_UNKNOWN = 'TRIP_USAGE_TYPE_UNKNOWN'
    STANDARD = 'STANDARD'
    EVENT = 'EVENT'


class CabinViewFareCategory(Enum):
    UNKNOWN_CABIN_CATEGORY = 'UNKNOWN_CABIN_CATEGORY'
    BASIC = 'BASIC'
    ECONOMY = 'ECONOMY'
    PREMIUM_ECONOMY = 'PREMIUM_ECONOMY'
    ECONOMY_PLUS = 'ECONOMY_PLUS'
    BUSINESS = 'BUSINESS'
    FIRST = 'FIRST'


class Type(Enum):
    UNKNOWN = 'UNKNOWN'
    CREDIT = 'CREDIT'
    DEBIT = 'DEBIT'


class CardCompany(Enum):
    NONE = 'NONE'
    VISA = 'VISA'
    MASTERCARD = 'MASTERCARD'
    AMEX = 'AMEX'
    DISCOVER = 'DISCOVER'
    AIR_TRAVEL_UATP = 'AIR_TRAVEL_UATP'
    CARTE_BLANCHE = 'CARTE_BLANCHE'
    DINERS_CLUB = 'DINERS_CLUB'
    JCB = 'JCB'
    BREX = 'BREX'
    UNION_PAY = 'UNION_PAY'
    EURO_CARD = 'EURO_CARD'
    ACCESS_CARD = 'ACCESS_CARD'
    ELO_CARD = 'ELO_CARD'


class CentralCardAccessLevel(Enum):
    UNKNOWN = 'UNKNOWN'
    ORGANIZATION = 'ORGANIZATION'
    LEGAL_ENTITY = 'LEGAL_ENTITY'
    TMC = 'TMC'


class ColorTitle(Enum):
    PROFILE_TIER_MEMBER_COLOR = 'PROFILE_TIER_MEMBER_COLOR'
    PROFILE_TIER_SILVER_COLOR = 'PROFILE_TIER_SILVER_COLOR'
    PROFILE_TIER_GOLD_COLOR = 'PROFILE_TIER_GOLD_COLOR'
    PROFILE_TIER_PLATINUM_COLOR = 'PROFILE_TIER_PLATINUM_COLOR'
    PROFILE_TIER_TITANIUM_COLOR = 'PROFILE_TIER_TITANIUM_COLOR'
    PROFILE_TIER_AMBASSADOR_COLOR = 'PROFILE_TIER_AMBASSADOR_COLOR'


class ColorWhiteLabelConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    primary: str | None = Field(None, examples=['#D33B47'])
    primaryDark: str | None = Field(None, examples=['#D33B47'])
    primaryLight: str | None = Field(None, examples=['#D33B47'])
    primaryPale: str | None = Field(None, examples=['#D33B47'])
    onPrimary: str | None = Field(None, examples=['#D33B47'])
    secondary: str | None = Field(None, examples=['#D33B47'])
    secondaryDark: str | None = Field(None, examples=['#D33B47'])
    secondaryLight: str | None = Field(None, examples=['#D33B47'])
    secondaryPale: str | None = Field(None, examples=['#D33B47'])
    onSecondary: str | None = Field(None, examples=['#D33B47'])
    error: str | None = Field(None, examples=['#D33B47'])
    errorDark: str | None = Field(None, examples=['#D33B47'])
    errorLight: str | None = Field(None, examples=['#D33B47'])
    errorPale: str | None = Field(None, examples=['#D33B47'])
    onError: str | None = Field(None, examples=['#D33B47'])


class CompanyId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID = Field(..., examples=['f49d00fe-1eda-4304-ba79-a980f565281d'])


class CompanyRole(Enum):
    ORG = 'ORG'
    TMC = 'TMC'
    PARTNER_TMC = 'PARTNER_TMC'
    HR_FEED_CONNECTOR = 'HR_FEED_CONNECTOR'
    TRIPS_DATA_CONNECTOR = 'TRIPS_DATA_CONNECTOR'
    GLOBAL = 'GLOBAL'


class CompanySpecifiedAttributeLegalEntity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fieldName: str = Field(
        ..., description='Field name of the attribute', examples=['businessId']
    )
    value: str = Field(
        ..., description='Field value of the attribute', examples=['ABCD']
    )


class ConfermaInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    deploymentId: int = Field(
        ...,
        description='Unique identifier assigned to the virtual card deployment at the point of creation.',
        examples=[68793680],
    )


class ConnectorType(Enum):
    HR_FEED_CONNECTOR = 'HR_FEED_CONNECTOR'
    TRIPS_DATA_CONNECTOR = 'TRIPS_DATA_CONNECTOR'


class Connector(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    connectorType: ConnectorType = Field(
        ..., description='Type of connector.', examples=['HR_FEED_CONNECTOR']
    )
    connectorId: CompanyId


class CostCenterId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['731ccbca-0415-6fe1-d235-c324dfbe7423'])


class CreateCostCenterRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str
    numberOfEmployees: int | None = None
    externalId: str | None = Field(None, examples=['external-id'])


class CreateDepartmentRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Name of the department')
    externalId: str | None = Field(
        None,
        description='External id of the department',
        examples=['department-external-id'],
    )


class CreditCardAccessType(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    CENTRALISED = 'CENTRALISED'
    INDIVIDUAL = 'INDIVIDUAL'
    PERSONAL = 'PERSONAL'
    TMC = 'TMC'
    APPLICATION = 'APPLICATION'
    ITINERARY = 'ITINERARY'
    EVENTS = 'EVENTS'
    TRAVEL_ARRANGER_MANAGED = 'TRAVEL_ARRANGER_MANAGED'
    COMPANY_TRAVEL_ARRANGER_MANAGED = 'COMPANY_TRAVEL_ARRANGER_MANAGED'
    EVENT_TEMPLATE = 'EVENT_TEMPLATE'


class CustomFieldType(Enum):
    QUESTION = 'QUESTION'
    MEETING = 'MEETING'
    BUDGET = 'BUDGET'
    BREX_TOKEN = 'BREX_TOKEN'


class DateTimeLocal(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(
        pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?$'
    ) = Field(..., examples=['2017-07-21T17:32'])


class DateTimeOffset(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    iso8601: constr(
        pattern=r'^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-4]):([0-5][0-9])(:([0-5][0-9]))?(Z|([+-](0[0-9]|1[0-4]):([0-5][0-9])))$'
    ) = Field(..., examples=['2017-07-21T17:32Z'])


class DepartmentV2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID
    name: str = Field(
        ..., description='Name of the department', examples=['IT Department']
    )
    externalId: str | None = Field(
        None,
        description='External id of the department',
        examples=['department-ext-id'],
    )


class DesignationNode(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str | None = Field(None, description='designation name')
    children: Sequence[DesignationNode] | None = Field(
        None, description='designations under this designation.'
    )


class Dimensions(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    height: int | None = Field(None, examples=[120])
    width: int | None = Field(None, examples=[240])


class DirectBilling2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorId: str = Field(..., description='Unique vendor id/code', examples=['ZI'])
    directBillingCode: str | None = Field(
        None,
        description='Direct billing code provided by the vendor',
        examples=['123456'],
    )


class DirectBillingWrapper2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    directBilling: DirectBilling2


class EmailClientSecretKey(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    secretKey: str | None = Field(None, examples=['SECRET_KEY'])
    versionId: str | None = Field(None, examples=['1'])


class EmailClientType(Enum):
    AWS_SES = 'AWS_SES'
    SENDGRID = 'SENDGRID'


class EnrollmentConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    termsAndConditions: str = Field(
        ...,
        description='Link to terms and conditions.',
        examples=['https://www.spotnana.com/terms/'],
    )


class EntityId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID


class ErrorParameter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str | None = Field(None, description='Parameter name')
    value: str | None = Field(None, description='Parameter value')


class ErrorMessage(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    errorCode: str | None = Field(
        None, description='Error code to identify the specific errors.'
    )
    message: str | None = Field(
        None, description='Message containing details of error.'
    )
    errorParameters: Sequence[ErrorParameter] | None = Field(
        None, description='Error message parameters.'
    )
    errorDetail: str | None = Field(None, description='More details about the error.')


class ErrorResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    debugIdentifier: str | None = Field(
        None, description='Link to debug the error internally.'
    )
    errorMessages: Sequence[ErrorMessage] | None = None


class ExpenseOwner(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    traveler: bool | None = Field(
        None,
        description='Report the expense against the traveler who made the booking. If set to true, central email should not be set.',
        examples=[True],
    )
    centralEmail: str | None = Field(
        None,
        description='Report the expense against a central email.',
        examples=['<EMAIL>'],
    )


class ExpensePartner(Enum):
    EXPENSIFY = 'EXPENSIFY'
    EMBURSE_CHROMERIVER = 'EMBURSE_CHROMERIVER'
    BREX = 'BREX'


class Expiry(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiryMonth: conint(ge=1, le=12) = Field(
        ..., description='Expiry month', examples=[1]
    )
    expiryYear: conint(ge=2000) = Field(..., description='Expiry year', examples=[2010])


class ExpiryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiry: Expiry | None = None


class FailureCode(Enum):
    FAILURE_CODE_UNKNOWN = 'FAILURE_CODE_UNKNOWN'
    PAYMENT_METHOD_MISSING = 'PAYMENT_METHOD_MISSING'
    PAYMENT_GATEWAY_FAILURE = 'PAYMENT_GATEWAY_FAILURE'


class FailureDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    failureCode: FailureCode | None = Field(None, description='failure code')
    failureMessage: str | None = Field(None, description='failure message')


class ValueType(Enum):
    INT = 'INT'
    DOUBLE = 'DOUBLE'
    BOOL = 'BOOL'
    STRING = 'STRING'
    JSON = 'JSON'


class FeatureNameEnum(Enum):
    FLIGHT_RESULTS_OOP = 'FLIGHT_RESULTS_OOP'
    HOTEL_RESULTS_OOP = 'HOTEL_RESULTS_OOP'
    RAIL_RESULTS_OOP = 'RAIL_RESULTS_OOP'
    CARS_RESULTS_OOP = 'CARS_RESULTS_OOP'
    PAYMENT_ADD_CARD = 'PAYMENT_ADD_CARD'
    MENU_ITEM_POLICIES = 'MENU_ITEM_POLICIES'
    INTEGRATION_EXPENSIFY = 'INTEGRATION_EXPENSIFY'
    TRAVELER_SELECTION = 'TRAVELER_SELECTION'
    FEATURE_PERSONAL_TRAVEL = 'FEATURE_PERSONAL_TRAVEL'
    PRODUCT_OBT_RAIL = 'PRODUCT_OBT_RAIL'
    MENU_ITEM_COMPANY_REMARKS = 'MENU_ITEM_COMPANY_REMARKS'
    HOMEPAGE_ADD_USER = 'HOMEPAGE_ADD_USER'
    MENU_ITEM_CUSTOM_FIELDS = 'MENU_ITEM_CUSTOM_FIELDS'
    PAYMENT_UNUSED_CREDITS = 'PAYMENT_UNUSED_CREDITS'
    EMAIL_UPDATE_BY_COMPANY_ADMIN = 'EMAIL_UPDATE_BY_COMPANY_ADMIN'
    ADHOC_BOOKING_SEARCH_PAGE = 'ADHOC_BOOKING_SEARCH_PAGE'
    ADHOC_BOOKING_SAVE_TRAVELER = 'ADHOC_BOOKING_SAVE_TRAVELER'
    ADMIN_VIRTUAL_CARD = 'ADMIN_VIRTUAL_CARD'
    CENTRAL_CARD_SERVICE_FEE = 'CENTRAL_CARD_SERVICE_FEE'
    EXPEDIA_SOURCE_ENABLED = 'EXPEDIA_SOURCE_ENABLED'
    BOOKING_COM_SOURCE_ENABLED = 'BOOKING_COM_SOURCE_ENABLED'
    HOTEL_MEDIAN_ENABLE = 'HOTEL_MEDIAN_ENABLE'
    HIDE_SEARCH_CRITERIA = 'HIDE_SEARCH_CRITERIA'
    ADHOC_PAYMENT_SOURCE = 'ADHOC_PAYMENT_SOURCE'
    SERVICE_CHARGE_CONFIG_DISABLED = 'SERVICE_CHARGE_CONFIG_DISABLED'
    HIDE_SETTINGS_MENU_ITEM_COMPANY = 'HIDE_SETTINGS_MENU_ITEM_COMPANY'
    HIDE_SETTINGS_MENU_ITEM_USERS = 'HIDE_SETTINGS_MENU_ITEM_USERS'
    HIDE_POLICY_APPROVAL_SECTION = 'HIDE_POLICY_APPROVAL_SECTION'
    HIDE_POLICY_CREATION = 'HIDE_POLICY_CREATION'
    HIDE_SETTINGS_TEXT_FROM_MENU = 'HIDE_SETTINGS_TEXT_FROM_MENU'
    HIDE_LEFT_NAV = 'HIDE_LEFT_NAV'
    HIDE_POLICY_AIR_CARRIER = 'HIDE_POLICY_AIR_CARRIER'
    HIDE_POLICY_AIR_CO2 = 'HIDE_POLICY_AIR_CO2'
    HIDE_POLICY_HOTEL_PRICE_LOCATION_CUSTOMISATION = (
        'HIDE_POLICY_HOTEL_PRICE_LOCATION_CUSTOMISATION'
    )
    HIDE_PROFILE_PREFERENCES_AIRLINES = 'HIDE_PROFILE_PREFERENCES_AIRLINES'
    HIDE_PROFILE_PREFERENCES_ALLIANCES = 'HIDE_PROFILE_PREFERENCES_ALLIANCES'
    MONEY_SET_CONVERTED_CURRENCY_AS_ORIGINAL_CURRENCY = (
        'MONEY_SET_CONVERTED_CURRENCY_AS_ORIGINAL_CURRENCY'
    )
    PRODUCT_OBT_AIR_DISABLED = 'PRODUCT_OBT_AIR_DISABLED'
    PRODUCT_OBT_HOTEL_DISABLED = 'PRODUCT_OBT_HOTEL_DISABLED'
    PRODUCT_OBT_CAR_DISABLED = 'PRODUCT_OBT_CAR_DISABLED'
    PRODUCT_OBT_LIMO_DISABLED = 'PRODUCT_OBT_LIMO_DISABLED'
    PRODUCT_OBT_CONCIERGE_DISABLED = 'PRODUCT_OBT_CONCIERGE_DISABLED'
    EVENT_BOOKING_ENABLED = 'EVENT_BOOKING_ENABLED'
    EMPLOYEE_LEVEL_DESIGNATED_APPROVER_ENABLED = (
        'EMPLOYEE_LEVEL_DESIGNATED_APPROVER_ENABLED'
    )
    ENRICH_SEARCH_RESPONSE_WITH_REWARD_POINTS = (
        'ENRICH_SEARCH_RESPONSE_WITH_REWARD_POINTS'
    )
    HIDE_LEFT_NAV_EMBED = 'HIDE_LEFT_NAV_EMBED'
    HIDE_POLICY_AIR_NOT_ALLOWED_TO_BE_BOOKED = (
        'HIDE_POLICY_AIR_NOT_ALLOWED_TO_BE_BOOKED'
    )
    HIDE_TRIP_FEE_CONFIG = 'HIDE_TRIP_FEE_CONFIG'
    HIDE_AGENT_FEE_CONFIG = 'HIDE_AGENT_FEE_CONFIG'
    SHOW_SERVICE_FEE_TAXES = 'SHOW_SERVICE_FEE_TAXES'
    HIDE_GUEST_BOOKING_SETTING = 'HIDE_GUEST_BOOKING_SETTING'
    USER_CREATION_PERSONA_TYPE_OPTIONS = 'USER_CREATION_PERSONA_TYPE_OPTIONS'
    AIR_SEARCH_RESULT_AIRLINE_SORT_ORDER = 'AIR_SEARCH_RESULT_AIRLINE_SORT_ORDER'
    HIDE_SHERPA_VISA_WIDGET = 'HIDE_SHERPA_VISA_WIDGET'
    AUTO_DEACTIVATE_GUEST = 'AUTO_DEACTIVATE_GUEST'
    POLICY_USER_GROUPS = 'POLICY_USER_GROUPS'
    HIDE_SUPPLIER_MANAGEMENT = 'HIDE_SUPPLIER_MANAGEMENT'
    HIDE_BOOKING_RESTRICTIONS_BY_COUNTRY = 'HIDE_BOOKING_RESTRICTIONS_BY_COUNTRY'
    LINKED_MARRIOTT_BONVOY_REQUIRED = 'LINKED_MARRIOTT_BONVOY_REQUIRED'
    EDITING_DEFAULT_POLICIES_DISABLED = 'EDITING_DEFAULT_POLICIES_DISABLED'
    DISALLOW_MULTI_CITY_ADD_FLIGHT = 'DISALLOW_MULTI_CITY_ADD_FLIGHT'
    REQUIRE_ONBOARDING_ON_ENROLLMENT = 'REQUIRE_ONBOARDING_ON_ENROLLMENT'
    GEO_CUSTOMIZATIONS = 'GEO_CUSTOMIZATIONS'
    HIDE_PREFERRED_PARENT_CHAINS = 'HIDE_PREFERRED_PARENT_CHAINS'
    HIDE_PREFERRED_HOTEL_BRANDS = 'HIDE_PREFERRED_HOTEL_BRANDS'
    HIDE_ROOM_OPTIONS_AND_STAY_PREFERENCES = 'HIDE_ROOM_OPTIONS_AND_STAY_PREFERENCES'
    HIDE_CONDITIONAL_RATES_PREFERENCES = 'HIDE_CONDITIONAL_RATES_PREFERENCES'
    HIDE_HOTELS_IN_SUPPLIER_MANAGEMENT = 'HIDE_HOTELS_IN_SUPPLIER_MANAGEMENT'
    HIDE_HOTEL_BRANDS_IN_SUPPLIER_MANAGEMENT = (
        'HIDE_HOTEL_BRANDS_IN_SUPPLIER_MANAGEMENT'
    )
    HIDE_POLICY_HOTEL_STAR_RATING = 'HIDE_POLICY_HOTEL_STAR_RATING'
    PRODUCT_OBT_HOMEPAGE_ORDER = 'PRODUCT_OBT_HOMEPAGE_ORDER'
    HIDE_COMPANY_SUPPLIER_MENU = 'HIDE_COMPANY_SUPPLIER_MENU'
    EXTERNAL_EVENT_BOOKING_URL = 'EXTERNAL_EVENT_BOOKING_URL'
    HIDE_IDLE_SESSION_TIMEOUT = 'HIDE_IDLE_SESSION_TIMEOUT'
    LE_CUSTOM_COLUMNS_CONFIG = 'LE_CUSTOM_COLUMNS_CONFIG'
    PROFILE_ELEMENTS_VISIBILITY = 'PROFILE_ELEMENTS_VISIBILITY'
    SHOW_TRIP_FEE_CONFIG = 'SHOW_TRIP_FEE_CONFIG'
    SHOW_AGENT_FEE_CONFIG = 'SHOW_AGENT_FEE_CONFIG'
    DEFAULT_HOTEL_SEARCH_RADIUS_CONFIG = 'DEFAULT_HOTEL_SEARCH_RADIUS_CONFIG'
    ENROLLMENT_DASHBOARD = 'ENROLLMENT_DASHBOARD'
    PRIVACY_CONFIG_HIERARCHY = 'PRIVACY_CONFIG_HIERARCHY'
    POLICY_CONFIGURED_IN_HR_FEED = 'POLICY_CONFIGURED_IN_HR_FEED'
    COMPANION_VIEW_CONFIG = 'COMPANION_VIEW_CONFIG'
    SHOW_INDIAN_CARD_PAYMENT_GUIDELINES = 'SHOW_INDIAN_CARD_PAYMENT_GUIDELINES'
    VISA_RESTRICTIONS_CONFIG = 'VISA_RESTRICTIONS_CONFIG'
    ALLOW_COMPANY_ADMIN_TO_RAISE_SUPPORT_TICKET = (
        'ALLOW_COMPANY_ADMIN_TO_RAISE_SUPPORT_TICKET'
    )
    ENABLE_COMPANY_PREFERRED_HOTEL_FILTER_BY_DEFAULT = (
        'ENABLE_COMPANY_PREFERRED_HOTEL_FILTER_BY_DEFAULT'
    )
    ENABLE_COMPANY_PREFERRED_CAR_FILTER_BY_DEFAULT = (
        'ENABLE_COMPANY_PREFERRED_CAR_FILTER_BY_DEFAULT'
    )
    LOGIN_DISABLED = 'LOGIN_DISABLED'
    SHOW_AGENT_FEE_RULE_BASED_CONFIG = 'SHOW_AGENT_FEE_RULE_BASED_CONFIG'
    ENROLLMENT_EMAILS_BCC = 'ENROLLMENT_EMAILS_BCC'
    SUREWARE_SYNC = 'SUREWARE_SYNC'
    DEFAULT_TMC_VIEW_MODE = 'DEFAULT_TMC_VIEW_MODE'
    SUPPORTED_CONTRACTING_TMCS = 'SUPPORTED_CONTRACTING_TMCS'


class Type1(Enum):
    UNKNOWN = 'UNKNOWN'
    CARD = 'CARD'
    CASH = 'CASH'
    TFPAY = 'TFPAY'
    CHEQUE = 'CHEQUE'
    BREX_POINTS = 'BREX_POINTS'
    QANTAS_POINTS = 'QANTAS_POINTS'


class FreshdeskCredentialDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    domain: str = Field(..., description='Domain related to fresh desk.')
    appId: str = Field(..., description="An identifier for fresh desk's app.")
    appKey: str = Field(
        ..., description="A secret key assigned to user's app by fresh desk."
    )


class FreshdeskCredentials(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    webCredentials: FreshdeskCredentialDetails | None = None
    mobileCredentials: FreshdeskCredentialDetails | None = None


class GatewayType(Enum):
    PAYMENT_GATEWAY_UNKNOWN = 'PAYMENT_GATEWAY_UNKNOWN'
    STRIPE = 'STRIPE'
    BREX = 'BREX'
    RAZORPAY = 'RAZORPAY'


class GatewayIdentifier(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    gatewayId: str | None = Field(
        None, description='Gateway Id for which the payment method should be verified.'
    )
    gatewayType: GatewayType | None = Field(
        None, description='Gateway Type for of the verification gateway.'
    )


class Image(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    data: str | None = Field(None, examples=['6935813e12584abda0e43d71cd2ea260'])
    dimensions: Dimensions | None = None
    url: str | None = Field(
        None,
        examples=[
            'https://static.wixstatic.com/media/73f2e2_6935813e12584abda0e43d71cd2ea260~mv2.png/v1/fill/w_630,h_94,al_c,q_85,usm_0.66_1.00_0.01/Spotnana%403x.webp'
        ],
    )


class ItineraryConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    skipModificationUpdate: bool | None = Field(
        False,
        description='Allow modification expenses to be skipped from sending to the partner',
        examples=[True],
    )
    skipCancellationUpdate: bool | None = Field(
        False,
        description='Allow cancellation expenses to be skipped from sending to the partner',
        examples=[True],
    )


class Latlng(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    latitude: float = Field(
        ..., description='Latitude of the Location', examples=[77.1025]
    )
    longitude: float = Field(
        ..., description='Longitude of the Location', examples=[28.7041]
    )


class LegalEntityId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str = Field(..., examples=['fc1ccbce-8413-4fe9-b233-a324dfbe7421'])


class LocationType(Enum):
    CONTINENT = 'CONTINENT'
    COUNTRY = 'COUNTRY'
    CITY = 'CITY'
    AIRPORT = 'AIRPORT'
    ADMINISTRATIVE_DIVISION = 'ADMINISTRATIVE_DIVISION'


class LogoConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    logoHeader: str | None = Field(
        None,
        description='Logo for app header, email header.',
        examples=[
            'https://duploservices-stage-email-assets-************.s3.us-west-2.amazonaws.com/confirmation/spotnana3.png'
        ],
    )
    logoFooter: str | None = Field(
        None,
        description='Logo for app footer, email footer.',
        examples=[
            'https://duploservices-stage-email-assets-************.s3-us-west-2.amazonaws.com/approval/spotnana-s-logo.png'
        ],
    )
    faviconUrl: str | None = Field(
        None,
        description='Display logo on the browser title.',
        examples=['http://www.spotnana.com'],
    )
    thumbnailImage: str | None = Field(
        None,
        description='Logo for autocomplete, negotiated rates etc.',
        examples=[
            'https://duploservices-stage-email-assets-************.s3-us-west-2.amazonaws.com/approval/spotnana-s-logo.png'
        ],
    )


class NGSFareCategory(Enum):
    UNKNOWN_NGS_CATEGORY = 'UNKNOWN_NGS_CATEGORY'
    BASE = 'BASE'
    STANDARD = 'STANDARD'
    ENHANCED = 'ENHANCED'
    PREMIUM = 'PREMIUM'
    LUXURY = 'LUXURY'
    ULTRA_LUXURY = 'ULTRA_LUXURY'


class NameSuffix(Enum):
    NAME_SUFFIX_UNKNOWN = 'NAME_SUFFIX_UNKNOWN'
    SR = 'SR'
    JR = 'JR'
    MD = 'MD'
    PHD = 'PHD'
    II = 'II'
    III = 'III'
    IV = 'IV'
    DO = 'DO'
    ATTY = 'ATTY'
    V = 'V'
    VI = 'VI'
    ESQ = 'ESQ'
    DC = 'DC'
    DDS = 'DDS'
    VM = 'VM'
    JD = 'JD'
    SECOND = 'SECOND'
    THIRD = 'THIRD'


class NonBillableReasonCode(Enum):
    PRODUCT_ISSUE = 'PRODUCT_ISSUE'
    ALREADY_CHARGED = 'ALREADY_CHARGED'
    CUSTOMER_GOODWILL = 'CUSTOMER_GOODWILL'
    MINOR_REQUEST = 'MINOR_REQUEST'
    NON_BILLABLE_REASON_OTHER = 'NON_BILLABLE_REASON_OTHER'


class OktaJwtVerifier(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    oauthUrl: str = Field(..., description='Okta oauth url.')
    audience: str = Field(..., description='Audience for which the token is issued.')


class OktaJwtVerifierWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    oktaJwtVerifier: OktaJwtVerifier | None = None


class OrganizationAgencyId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str


class OrganizationId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: str


class OtherFeeType(RootModel[Literal['MERCHANT_FEE']]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Literal['MERCHANT_FEE'] = Field(
        'MERCHANT_FEE',
        description='Other fee type',
        examples=['MERCHANT_FEE'],
        title='OtherFeeType',
    )


class OwnershipLabel(Enum):
    CORPORATE = 'CORPORATE'
    PERSONAL = 'PERSONAL'
    CENTRAL = 'CENTRAL'


class BrexBudgetMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    budgetName: str | None = Field(
        None, description='Name of the budget', examples=['Travel budget']
    )
    paidByPersonalCard: bool | None = Field(
        None,
        description='Whether it was paid by budget card or personal card',
        examples=[False],
    )


class CustomPaymentMethodMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    brexBudgetMetadata: BrexBudgetMetadata | None = Field(
        None, description='Metadata for Brex Budget'
    )


class PaymentMethod(Enum):
    PAYMENT_METHOD_UNKNOWN = 'PAYMENT_METHOD_UNKNOWN'
    CREDIT_CARD = 'CREDIT_CARD'
    BREX_POINTS = 'BREX_POINTS'
    CASH = 'CASH'
    QANTAS_POINTS = 'QANTAS_POINTS'
    VENDOR_PROGRAM_PAYMENT = 'VENDOR_PROGRAM_PAYMENT'
    DELAYED_INVOICING = 'DELAYED_INVOICING'
    FLIGHT_CREDITS = 'FLIGHT_CREDITS'
    QANTAS_TRAVEL_FUND = 'QANTAS_TRAVEL_FUND'
    CUSTOM_VIRTUAL_PAYMENT = 'CUSTOM_VIRTUAL_PAYMENT'


class PaymentSourceType(Enum):
    CARD = 'CARD'
    VIRTUAL_CARD = 'VIRTUAL_CARD'
    REWARDS_PROGRAM = 'REWARDS_PROGRAM'
    DELAYED_INVOICING = 'DELAYED_INVOICING'
    CUSTOM_PAYMENT_METHOD = 'CUSTOM_PAYMENT_METHOD'
    VENDOR_PROGRAM_PAYMENT = 'VENDOR_PROGRAM_PAYMENT'
    UNUSED_CREDIT = 'UNUSED_CREDIT'
    CASH = 'CASH'


class PaymentStatus(Enum):
    SUCCESS = 'SUCCESS'
    DELAYED_INVOICE = 'DELAYED_INVOICE'
    MANUAL = 'MANUAL'
    NO_CHARGE = 'NO_CHARGE'


class PaymentTransaction(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    gatewayIdentifier: GatewayIdentifier | None = Field(
        None, description='Gateway Identifier'
    )
    paymentReference: str | None = Field(None, description='payment reference')
    networkTransactionId: str | None = Field(None, description='Network Transaction Id')


class PersonalCardConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    sendToTraveler: bool | None = Field(
        None,
        description="A boolean flag to show if expenses are to be sent to traveler's Expense partner account.",
        examples=[False],
    )
    personalCardExpenseEmail: str | None = Field(
        None,
        description='The email for the expense partner account.',
        examples=['<EMAIL>'],
    )


class CountryCodeSource(Enum):
    UNSPECIFIED = 'UNSPECIFIED'
    FROM_NUMBER_WITH_PLUS_SIGN = 'FROM_NUMBER_WITH_PLUS_SIGN'
    FROM_NUMBER_WITH_IDD = 'FROM_NUMBER_WITH_IDD'
    FROM_NUMBER_WITHOUT_PLUS_SIGN = 'FROM_NUMBER_WITHOUT_PLUS_SIGN'
    FROM_DEFAULT_COUNTRY = 'FROM_DEFAULT_COUNTRY'


class Type2(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    MOBILE = 'MOBILE'
    LANDLINE = 'LANDLINE'


class PhoneNumber(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    countryCode: int | None = Field(
        None, description='two digit country code', examples=[91]
    )
    countryCodeSource: CountryCodeSource | None = Field(
        None, examples=['FROM_NUMBER_WITH_PLUS_SIGN']
    )
    extension: str | None = Field(
        None, description='phone number extension', examples=['222']
    )
    isoCountryCode: str | None = Field(
        None, description='ISO alpha-2 code', examples=['IN']
    )
    italianLeadingZero: bool | None = Field(False, examples=[True])
    nationalNumber: int | None = Field(None, examples=[8150])
    numberOfLeadingZeros: int | None = Field(0, examples=[1])
    preferredDomesticCarrierCode: str | None = Field(None, examples=['7'])
    rawInput: str | None = Field(None, examples=['77777'])
    type: Type2 | None = Field(None, examples=['MOBILE'])


class PostalAddress(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    addressLines: Sequence[str] = Field(..., description='Address lines')
    administrativeArea: str | None = Field(
        None,
        description='Code of administrative area. For example: DL for Delhi, India.\nHighest administrative subdivision which is used for postal\naddresses of a country or region.\nFor example, this can be a state, a province, an oblast, or a prefecture.\nSpecifically, for Spain this is the province and not the autonomous\ncommunity (e.g. "Barcelona" and not "Catalonia").\nMany countries don\'t use an administrative area in postal addresses. E.g.\nin Switzerland this should be left unpopulated.\n',
        examples=['CA'],
    )
    administrativeAreaName: str | None = Field(
        None,
        description='Name of administrative area. This is full name corresponding to administrativeArea. \nLike Delhi for DL area code. For some places, code and name maybe same as well like Tokyo.\n',
        examples=['California'],
    )
    description: str | None = Field(
        None, description='Address description', examples=['San Francisco Home']
    )
    isDefault: bool | None = Field(
        None,
        description='Whether this address is default address in case multiple addresses are specified.',
        examples=[True],
    )
    languageCode: str | None = Field(
        None,
        description='BCP-47 language code of the contents of this address (if known). This is often the UI \nlanguage of the input form or is expected to match one of the languages used in the \naddress\' country/region, or their transliterated equivalents.\nThis can affect formatting in certain countries, but is not critical to the correctness \nof the data and will never affect any validation or other non-formatting related operations.\nExamples: "zh-Hant", "ja", "ja-Latn", "en".\n',
        examples=['en'],
    )
    locality: str | None = Field(
        None,
        description='Generally refers to the city/town portion of the address.',
        examples=['San Francisco'],
    )
    locationCode: str | None = Field(
        None,
        description='IATA 3-letter location code. See https://www.iata.org/en/services/codes.',
        examples=['LAX'],
    )
    organization: str | None = Field(
        None,
        description='The name of the organization at the address.',
        examples=['Spotnana'],
    )
    postalCode: str | None = Field(
        None,
        description='Postal code of the address. This is a required field when setting for a user/legal entity/company etc.',
        examples=['94130'],
    )
    continentCode: str | None = Field(
        None,
        description='2 letter continent code of the continent this address falls in.',
        examples=['AF'],
    )
    recipients: Sequence[str] | None = Field(
        None, description='The recipient at the address.'
    )
    regionCode: str = Field(
        ...,
        description='Region code of the country/region of the address.',
        examples=['US'],
    )
    regionName: str | None = Field(
        None,
        description='Region name of the country/region of the address.',
        examples=['America'],
    )
    revision: int | None = Field(None, examples=[1])
    sortingCode: str | None = Field(
        None,
        description='Additional, country-specific, sorting code. This is not used\nin most regions. Where it is used, the value is either a string like\n"CEDEX", optionally followed by a number (e.g. "CEDEX 7"), or just a number\nalone, representing the "sector code" (Jamaica), "delivery area indicator"\n(Malawi) or "post office indicator" (e.g. Côte d\'Ivoire).\n',
        examples=['Jamaica'],
    )
    sublocality: str | None = Field(
        None,
        description='Sublocality of the address. This can be neighborhoods, boroughs, districts.',
    )
    timezone: str | None = Field(
        None, description='Time zone of the address.', examples=['America/Los_Angeles']
    )
    coordinates: Latlng | None = Field(
        None, description='Map coordinates of the address.'
    )


class PrimaryServiceProviderTmc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tmcId: CompanyId = Field(..., description='Id of the service provider TMC.')


class PrivacyDisclaimer(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    show: bool | None = True
    html: str | None = Field(
        None, description='String containing the html.', examples=['</html>']
    )


class PrivacySettings(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    allowCompanyAdminAccessToRelativesInfo: bool | None = Field(
        None,
        description="Boolean to show if company admin can see information of users' relatives.",
        examples=[True],
    )


class Reference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID
    name: str | None = None


class ConversionRate(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    currencyCode: str | None = Field(
        None,
        description='The 3-letter currency code defined in ISO 4217.',
        examples=['USD'],
    )
    rate: float | None = Field(
        None, description='Conversion rate of points to specified currency.'
    )


class RoleType(Enum):
    UNKNOWN_TYPE = 'UNKNOWN_TYPE'
    COMPANY_ADMIN = 'COMPANY_ADMIN'
    COMPANY_TRAVEL_ARRANGER = 'COMPANY_TRAVEL_ARRANGER'
    TRAVEL_ARRANGER = 'TRAVEL_ARRANGER'
    COMPANY_REPORT_ADMIN = 'COMPANY_REPORT_ADMIN'
    GLOBAL_ADMIN = 'GLOBAL_ADMIN'
    GLOBAL_AGENT = 'GLOBAL_AGENT'
    TMC_AGENT = 'TMC_AGENT'
    TMC_ADMIN = 'TMC_ADMIN'


class RoutingConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    appHomeRoute: str | None = Field(None, examples=['/my-account'])


class RuleBasedInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ruleName: str = Field(..., description='Rule name', examples=['Rule1'])
    chargeProcessorType: Literal['RULE_BASED_INFO'] = Field(
        'RULE_BASED_INFO',
        description='Charge processor type',
        examples=['RULE_BASED_INFO'],
    )


class ServiceChargeEntityType(Enum):
    TRIP = 'TRIP'
    USER = 'USER'


class ServiceChargeType(RootModel[Literal['TMC_CHARGE']]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Literal['TMC_CHARGE'] = Field(
        'TMC_CHARGE',
        description='Type of service charge. TMC_CHARGE: Service charge for a service charged by the TMC.\n',
        title='ServiceChargeType',
    )


class ServiceFeeTransactionType(Enum):
    TRANSACTION_TYPE_BOOKING = 'TRANSACTION_TYPE_BOOKING'
    TRANSACTION_TYPE_MODIFICATION = 'TRANSACTION_TYPE_MODIFICATION'
    TRANSACTION_TYPE_CANCELLATION = 'TRANSACTION_TYPE_CANCELLATION'


class ServiceType(Enum):
    ITINERARY_BOOKING = 'ITINERARY_BOOKING'
    AGENT_CONTACT = 'AGENT_CONTACT'


class SocialMediaLinksConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    facebookUrl: str | None = Field(
        None, examples=['https://www.facebook.com/spotnanaofficial']
    )
    twitterUrl: str | None = Field(None, examples=['https://twitter.com/spotnana'])
    instagramUrl: str | None = Field(
        None, examples=['https://www.instagram.com/spotnana_official']
    )


class SupplierType(Enum):
    SABRE = 'SABRE'
    AMADEUS = 'AMADEUS'
    TRAVEL_FUSION = 'TRAVEL_FUSION'
    FARELOGIX_NDC = 'FARELOGIX_NDC'
    ATPCO_NDC = 'ATPCO_NDC'
    TRAINLINE = 'TRAINLINE'
    AVIA = 'AVIA'
    QBR = 'QBR'
    BCD = 'BCD'
    QANTAS_HOTELS = 'QANTAS_HOTELS'
    SOUTHWEST = 'SOUTHWEST'
    EXPEDIA = 'EXPEDIA'
    HOTEL_HUB = 'HOTEL_HUB'
    NDC = 'NDC'
    MARRIOTT = 'MARRIOTT'
    CLEARTRIP = 'CLEARTRIP'
    KYTE = 'KYTE'
    GROUNDSPAN = 'GROUNDSPAN'
    SABRE_NDC = 'SABRE_NDC'
    BOOKING_COM = 'BOOKING_COM'


class GenesysConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    enabled: bool = Field(
        ..., description='Whether genesys is enabled or not.', examples=[True]
    )
    dataUrl: str = Field(..., description='Data url for genesys.')


class ZendeskConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    enabled: bool = Field(
        ..., description='Whether Zendesk Chat is enabled or not.', examples=[True]
    )
    integrationId: str = Field(
        ...,
        description='Integration ID for Zendesk Chat for Web.',
        examples=['**********'],
    )
    androidIntegrationId: str | None = Field(
        None,
        description='Integration ID for Zendesk Chat for Android.',
        examples=['**********'],
    )
    iosIntegrationId: str | None = Field(
        None,
        description='Integration ID for Zendesk Chat for iOS.',
        examples=['**********'],
    )


class GenesysCloudConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    enabled: bool = Field(
        ..., description='Whether Genesys Cloud is enabled or not.', examples=[True]
    )
    deploymentId: str = Field(
        ...,
        description='DeploymentId for Genesys Cloud.',
        examples=['52dc2a6f-772a-4edf-96d3-b3f0c49400a7'],
    )
    environment: str = Field(
        ..., description='Environment for Genesys Cloud.', examples=['usa-1']
    )
    genesysScriptUrl: str = Field(
        ...,
        description='Url for Genesys Cloud Script.',
        examples=['https://test.test.com'],
    )


class TwilioChatConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    enabled: bool = Field(..., description='Whether Twilio Chat is enabled or not.')


class CognigyChatConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    enabled: bool | None = Field(
        None, description='Whether Cognigy chat is enabled or not.'
    )
    scriptUrl: str | None = Field(
        None,
        description='Url for Cognigy Script.',
        examples=['https://your-cognigy-instance.com/script/abcdef123456'],
    )
    initWebchatEndpoint: str | None = Field(
        None,
        description='Endpoint for Cognigy initWebchat method.',
        examples=['https://your-cognigy-instance.com/initWebchat'],
    )
    sessionTimeout: int | None = Field(
        None,
        description='Session timeout (in minutes) for Cognigy Chat.',
        examples=[30],
    )
    cssUrl: str | None = Field(
        None,
        description='Url for Cognigy Chat CSS.',
        examples=['https://your-server.com/css/chat.css'],
    )


class ThirdPartySource(Enum):
    UNKNOWN_SOURCE = 'UNKNOWN_SOURCE'
    SABRE = 'SABRE'
    TRAVEL_FUSION = 'TRAVEL_FUSION'
    AVIA = 'AVIA'
    NDC = 'NDC'
    TRAINLINE = 'TRAINLINE'
    ATPCO_NDC = 'ATPCO_NDC'
    FARELOGIX_NDC = 'FARELOGIX_NDC'
    OFFLINE = 'OFFLINE'
    CONNEXUS = 'CONNEXUS'
    ROUTEHAPPY = 'ROUTEHAPPY'
    AMADEUS = 'AMADEUS'
    GIATA = 'GIATA'
    QBR = 'QBR'
    BCD = 'BCD'
    QANTAS_HOTELS = 'QANTAS_HOTELS'
    SOUTHWEST = 'SOUTHWEST'
    EXPEDIA = 'EXPEDIA'
    HOTEL_HUB = 'HOTEL_HUB'
    MARRIOTT = 'MARRIOTT'
    CLEARTRIP = 'CLEARTRIP'
    KYTE = 'KYTE'
    GROUNDSPAN = 'GROUNDSPAN'
    SABRE_NDC = 'SABRE_NDC'
    BOOKING_COM = 'BOOKING_COM'


class Tier(Enum):
    BASIC = 'BASIC'
    SEAT1A = 'SEAT1A'


class TierDefinition(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Tier name.')
    icon: str | None = Field(None, description='URL representing tier icon.')
    tier: Tier


class TitledColor(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    title: ColorTitle
    color: str = Field(..., examples=['#FF0000'])


class TmcCalculatorInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    calculatorType: str = Field(
        ..., description='TMC service charge calculator type', examples=['SPOTNANA']
    )
    chargeProcessorType: Literal['TMC_CALCULATOR_INFO'] = Field(
        'TMC_CALCULATOR_INFO',
        description='Charge processor type',
        examples=['TMC_CALCULATOR_INFO'],
    )


class CountryWiseBookingTmc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    countryCode: str = Field(
        ...,
        description='The ISO 2-character country code for which booking TMCs needs to be overriden.',
        examples=['US'],
    )
    bookingTmcId: UUID = Field(
        ...,
        description='Booking TMC for above country code.',
        examples=['734f4ea2-e9ed-4c90-853c-9eed62f1254b'],
    )


class TmcDefaultConfiguration(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    defaultBookingTmcId: UUID | None = Field(
        None,
        description='Default booking TMC to be used. If not set, then TMC itself will be default booking tmc.',
        examples=['734f4ea2-e9ed-4c90-853c-9eed62f1254b'],
    )
    countryWiseBookingTmcs: Sequence[CountryWiseBookingTmc] | None = Field(
        None, description='Override for booking TMCs per country.'
    )


class TmcPartnerRoleMapping(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    partnerRole: str = Field(..., description="User's role at partner.")
    spotnanaRole: RoleType


class TokenizedExpiry(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    expiryMonth: str = Field(
        ..., description='Tokenized Expiry month', examples=['KvAuPANQWCpjwRQxcC8EXg==']
    )
    expiryYear: str = Field(
        ..., description='Tokenized Expiry year', examples=['fPBm0OWrKwPyIrCVcbg4cA==']
    )


class TokenizedExpiryWrapper(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tokenizedExpiry: TokenizedExpiry | None = None


class TravelType(Enum):
    AIR = 'AIR'
    HOTEL = 'HOTEL'
    CAR = 'CAR'
    RAIL = 'RAIL'
    LIMO = 'LIMO'
    MISC = 'MISC'
    ALL = 'ALL'


class UAPassPlusMetadataExternal(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    uatpInfo: Mapping[str, Any] | None = Field(
        None, description='UATP card information for UAPassPlus', title='uatpInfo'
    )


class UAPassPlusMetadataWrapperExternal(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    uaPassPlusMetadata: UAPassPlusMetadataExternal


class UpdateFeatureConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: FeatureNameEnum
    value: str = Field(
        ..., description='Value associated with feature converted in String format.'
    )


class UpdateFeaturesRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    featuresToUpdate: Sequence[UpdateFeatureConfig] | None = Field(
        None, description='Features that has to be updated.'
    )
    featuresToDelete: Sequence[FeatureNameEnum] | None = Field(
        None, description='Features that has to be deleted.'
    )


class UserId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID


class UserIdentifierType(Enum):
    PID = 'PID'
    EMAIL = 'EMAIL'


class Name1(Enum):
    PID = 'PID'
    EMAIL = 'EMAIL'


class UserInfoEndpointResponseAttribute(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: Name1 = Field(..., description='Attribute name', examples=['PID'])
    jsonXpath: str = Field(
        ...,
        description='Json xpath of the Attribute/Parameter value location',
        examples=['$.user.email'],
    )


class ValueAddedServiceFeeType(Enum):
    VIRTUAL_CARD_PAYMENT = 'VIRTUAL_CARD_PAYMENT'
    PRICE_OPTIMIZATION = 'PRICE_OPTIMIZATION'
    UNUSED_CREDIT_APPLICATION = 'UNUSED_CREDIT_APPLICATION'


class VendorProgramPaymentMetadata2(
    RootModel[DirectBillingWrapper2 | UAPassPlusMetadataWrapperExternal]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: DirectBillingWrapper2 | UAPassPlusMetadataWrapperExternal = Field(
        ...,
        description='Metadata related to vendor program payment method',
        title='VendorProgramPaymentMetadata',
    )


class VerifierDetails(RootModel[OktaJwtVerifierWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: OktaJwtVerifierWrapper = Field(..., title='VerifierDetails')


class VirtualCardVendor(RootModel[Literal['CONFERMA']]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Literal['CONFERMA'] = Field(
        'CONFERMA', description='Type of Virtual card vendor', examples=['CONFERMA']
    )


class VirtualCardVendorInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendor: VirtualCardVendor | None = None
    vendorCardId: str = Field(
        ..., description='Virtual card id.', examples=['68793680']
    )
    virtualCardVendorCardPoolId: str = Field(
        ..., description='Card pool id of virtual card vendor.', examples=['51907']
    )
    confermaInfo: ConfermaInfo | None = None


class WebLinksConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    logoHeader: str | None = Field(
        None,
        examples=[
            'https://duploservices-stage-email-assets-************.s3.us-west-2.amazonaws.com/confirmation/spotnana3.png'
        ],
    )
    logoFooter: str | None = Field(
        None,
        examples=[
            'https://duploservices-stage-email-assets-************.s3-us-west-2.amazonaws.com/approval/spotnana-s-logo.png'
        ],
    )
    companyWebsiteAddress: str | None = Field(
        None, examples=['https://www.spotnana.com']
    )
    homePage: str | None = Field(
        None, examples=['https://stage-app.spotnana.com/flights']
    )
    itineraryPage: str | None = Field(
        None, examples=['https://stage-app.spotnana.com/trips/3375488451']
    )
    disclaimerPage: str | None = Field(
        None, examples=['https://www.spotnana.com/terms']
    )
    privacyPage: str | None = Field(
        None, examples=['https://www.spotnana.com/privacy-policy']
    )
    hardApprovalUrl: str | None = Field(
        None,
        examples=[
            'https://stage-app.spotnana.com/hardapproval?approvalId=******************************************************************%3D%3D&approverName=Pihu%20Gupta&travelerName=Pihu%20Gupta&tripName=TESting%201&tripId=2418132231&voidDate=Tuesday,%2021%20Jun%20at%2002:00%20UTC'
        ],
    )
    itineraryFlightStatsUrl: str | None = Field(
        None, examples=['https://stage-app.spotnana.com/trips/4694735622']
    )
    appEmbedScriptUrl: str | None = Field(
        None, examples=['https://customjs.partner.com']
    )
    logoNavigationLink: str | None = Field(None, examples=['http://www.spotnana.com'])
    illustration: str | None = Field(
        None,
        examples=[
            'https://duploservices-stage-email-assets-************.s3-us-west-2.amazonaws.com/approval/spotnana-s-logo.png'
        ],
    )
    poweredBySpotnana: bool | None = Field(True, examples=[False])
    logoutRedirectUrl: str | None = Field(
        None, examples=['https://www.spotnana.com/signout']
    )
    faviconUrl: str | None = Field(
        None,
        description='Display logo on the browser title.',
        examples=['http://www.spotnana.com'],
    )
    appDownloadLinks: AppDownloadLinksConfig | None = None
    socialMediaLinks: SocialMediaLinksConfig | None = None
    termsOfUse: str | None = Field(
        None,
        description='Terms of service/legal agreement between a service provider and a person who wants to use service.',
        examples=['https://www.spotnana.com/terms/'],
    )
    illustrationActionUrl: str | None = Field(
        None, examples=['https://www.spotnana.com/login']
    )


class Unit(Enum):
    KG = 'KG'
    LBS = 'LBS'


class Weight(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    weight: float = Field(..., description='Amount of weight.', examples=[150])
    unit: Unit = Field(..., description='Unit of weight.', examples=['KG'])


class ArrayOfReference(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    length: int | None = None
    elements: Sequence[Reference] | None = Field(
        None, description='List of references containing id and name.'
    )
    totalNumResults: int | None = Field(None, description='Total number of results')


class BookingDetails(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    pnrId: str | None = Field(None, description='PNR Id')
    pnrVersion: int | None = Field(
        None, description='Version number associated with the PNR'
    )
    tripUsageType: TripUsageType | None = Field(None, description='Trip Usage Type')
    source: ThirdPartySource | None = Field(None, description='Third party source')
    sourcePnrId: str | None = Field(
        None, description='Source PNR Id from thirdparty source'
    )


class CardAccessEntity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    entityId: str = Field(
        ...,
        description='Holds the id for for the user who can access the card or organization id or legal entity',
    )
    centralCardAccessLevel: CentralCardAccessLevel | None = None


class CardExpiry(RootModel[TokenizedExpiryWrapper | ExpiryWrapper]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: TokenizedExpiryWrapper | ExpiryWrapper = Field(
        ..., description='Contains the expiry of a Card.', title='CardExpiry'
    )


class ChargeProcessorInfo(RootModel[RuleBasedInfo | TmcCalculatorInfo]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: RuleBasedInfo | TmcCalculatorInfo = Field(
        ...,
        description='Information about the processor that calculated the charge',
        discriminator='chargeProcessorType',
        title='ChargeProcessorInfo',
    )


class CompanyRef(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CompanyId
    name: str | None = None
    logo: Image | None = Field(None, description='Company logo')


class CostCenter(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CostCenterId
    name: str = Field(..., examples=['CostCenter'])
    externalId: str | None = Field(None, examples=['external-id'])


class CreateOfficeRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress | None = None
    name: str | None = Field(None, examples=['Office name'])
    latlng: Latlng | None = None
    externalId: str | None = Field(None, examples=['external-id'])
    taxId: str | None = Field(None, examples=['123232'])


class CreditCardAccess(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    accessType: CreditCardAccessType
    entityIds: Sequence[str] = Field(
        ...,
        description='Holds the ids for for all users who can access the card or organization id',
    )
    entities: Sequence[CardAccessEntity] | None = Field(
        None,
        description='A list of cardAccessEntity consisting of central card access level if present and entity id.',
    )


class DateRange(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    startDate: DateTimeLocal
    endDate: DateTimeLocal


class EmailClientCredentials(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    emailClientSecretKey: EmailClientSecretKey


class EmailConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    personalCardConfig: PersonalCardConfig | None = None
    centralCardExpenseEmail: str | None = Field(
        None,
        description='Central email for bookings done with central card.',
        examples=['<EMAIL>'],
    )
    expensePartnerEmail: str = Field(
        ...,
        description='The expense partner email where expense receipts are sent to.',
        examples=['<EMAIL>'],
    )
    bccEmail: str | None = Field(
        None,
        description='Email to which we bcc expensify emails',
        examples=['<EMAIL>'],
    )


class ExpensePartnerConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    partner: ExpensePartner
    emailConfig: EmailConfig | None = None
    travelType: Sequence[TravelType] | None = Field(
        None, description='Applicable segments'
    )
    isExpensePartnerEnabled: bool | None = Field(
        True,
        description='A flag denoting is the expense partner config is enabled or not.',
        examples=[True],
    )
    reportCustomFields: bool | None = Field(
        None,
        description='Send custom fields to the expense partner if enabled.',
        examples=[True],
    )
    personalCardExpenseOwner: ExpenseOwner | None = None
    centralCardExpenseOwner: ExpenseOwner | None = None
    partnerReferralId: str | None = Field(
        None,
        description='Referral id used for this company/legal entity during expense partner setup',
        examples=['1234hgd'],
    )
    itineraryConfig: ItineraryConfig | None = None


class ExternalEndpoint(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    url: str = Field(..., description='Url from where user can be fetched.')
    authHeaderName: str = Field(
        ...,
        description='Auth header where we have to send authentication/authorization details.',
    )
    userIdentifierType: UserIdentifierType | None = Field(
        None,
        description='Either use pid or email as the identifier for auth code exchange process',
    )
    responseAttributes: Sequence[UserInfoEndpointResponseAttribute] | None = Field(
        None,
        description='List of relevant attribute names & their json xpath from response',
    )


class FareCategory(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    ngsCategory: NGSFareCategory | None = 'UNKNOWN_NGS_CATEGORY'
    cabinViewCategory: CabinViewFareCategory | None = 'UNKNOWN_CABIN_CATEGORY'


class FareCategoryOptionConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fareCategory: FareCategory
    disabled: bool = Field(
        ..., description='true if a fare category is disabled', examples=[True]
    )


class FeatureConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: FeatureNameEnum
    enabled: bool = Field(
        ..., description='Whether feature is enabled or not.', examples=[True]
    )
    valueType: ValueType | None = Field(
        None, description='Value Type of Feature.', examples=['BOOL']
    )
    value: str | None = Field(None, description='Value associated with Feature.')


class FeatureConfigs(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    length: int | None = Field(None, description='Number of features.', examples=[1])
    features: Sequence[FeatureConfig] | None = Field(
        None, description='Features related to company.'
    )


class FreshdeskConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    enabled: bool = Field(
        ..., description='Whether freshdesk is enabled or not.', examples=[True]
    )
    hasCredentials: bool = Field(
        ...,
        description='Whether the company has its own freshdesk credentials.',
        examples=[False],
    )
    credentials: FreshdeskCredentials | None = Field(
        None,
        description='Credentials for the fresh desk. This field has no impact in create/update APIs. The \nprocess to store / update the credentials are manual.\n',
    )


class LegalEntity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID | None = Field(None, description='Unique identifier of the legal entity.')
    name: str | None = Field(None, description='Name of the legal entity')
    address: PostalAddress | None = Field(
        None, description='Address of the legal entity.'
    )
    billingCurrency: str | None = Field(
        None, description='Billing currency for the legal entity.'
    )
    dba: str | None = Field(
        None, description='Business name, sort form of Doing Business As'
    )
    ein: str | None = Field(None, description='Tax number')
    phoneNumbers: Sequence[PhoneNumber] | None = None
    isDelayedInvoicingEnabled: bool | None = Field(
        None,
        description="Whether delayed invoicing is enabled for the legal entity. If enabled, travelers of the\nlegal entity won't be charged for trip on checkout, rather the bills would be sent to\nthe company later on based on the billing cycle.\n",
    )
    bookingTmcRef: Reference | None = Field(
        None, description='Booking TMC of the legal entity.'
    )
    expensePartnerConfig: ExpensePartnerConfig | None = Field(
        None, description='Configured details of expense partner.'
    )
    ccEmailAddresses: Sequence[str] | None = Field(
        None,
        description="List of email addresses on which all confirmation emails will be CCed. This is applicable only for company roles 'ORG'",
    )
    bccEmailAddresses: Sequence[str] | None = Field(
        None,
        description="List of email addresses on which all confirmation emails will be BCCed. This is applicable only for company roles 'ORG'",
    )
    externalId: str | None = Field(
        None,
        description='External id for the legal entity.',
        examples=['my-external-id'],
    )
    companySpecifiedAttributes: (
        Sequence[CompanySpecifiedAttributeLegalEntity] | None
    ) = Field(
        None, description='Company specified attributes or fields for the legal entity.'
    )


class LegalEntityCreateRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Name of the legal entity')
    address: PostalAddress = Field(..., description='Address of the legal entity.')
    billingCurrency: str = Field(
        ..., description='Billing currency for the legal entity.'
    )
    dba: str | None = Field(
        None, description='Business name, sort form of Doing Business As'
    )
    ein: str | None = Field(None, description='Tax number')
    phoneNumbers: Sequence[PhoneNumber] | None = None
    isDelayedInvoicingEnabled: bool | None = Field(
        None,
        description="Whether delayed invoicing is enabled for the legal entity. If enabled, travelers of the\nlegal entity won't be charged for trip on checkout, rather the bills would be sent to\nthe company later on based on the billing cycle.\n",
    )
    ccEmailAddresses: Sequence[str] | None = Field(
        None,
        description="List of email addresses on which all confirmation emails for this LE will be cc'ed. This is applicable only for company roles 'ORG'",
    )
    bccEmailAddresses: Sequence[str] | None = Field(
        None,
        description="List of email addresses on which all confirmation emails for this LE will be bcc'ed. This is applicable only for company roles 'ORG'",
    )
    externalId: str | None = Field(
        None,
        description='External id for legal entity.',
        examples=['legal-entity-external-id'],
    )
    companySpecifiedAttributes: (
        Sequence[CompanySpecifiedAttributeLegalEntity] | None
    ) = Field(
        None, description='Company specified attributes or fields for the legal entity.'
    )


class LegalEntityUpdateRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID | None = Field(None, description='Unique identifier of the legal entity.')
    name: str | None = Field(None, description='Name of the legal entity')
    address: PostalAddress | None = Field(
        None, description='Address of the legal entity.'
    )
    billingCurrency: str | None = Field(
        None, description='Billing currency for the legal entity.'
    )
    dba: str | None = Field(
        None, description='Business name, sort form of Doing Business As'
    )
    ein: str | None = Field(None, description='Tax number')
    phoneNumbers: Sequence[PhoneNumber] | None = None
    isDelayedInvoicingEnabled: bool | None = Field(
        None,
        description="Whether delayed invoicing is enabled for the legal entity. If enabled, travelers of the\nlegal entity won't be charged for trip on checkout, rather the bills would be sent to\nthe company later on based on the billing cycle.\n",
    )
    expensePartnerConfig: ExpensePartnerConfig | None = Field(
        None, description='Configured details of expense partner.'
    )
    ccEmailAddresses: Sequence[str] | None = Field(
        None,
        description="List of email addresses on which all confirmation emails will be CCed. This is applicable only for company roles 'ORG'",
    )
    bccEmailAddresses: Sequence[str] | None = Field(
        None,
        description="List of email addresses on which all confirmation emails will be BCCed. This is applicable only for company roles 'ORG'",
    )
    externalId: str | None = Field(
        None,
        description='External id for the legal entity.',
        examples=['my-external-id'],
    )
    companySpecifiedAttributes: (
        Sequence[CompanySpecifiedAttributeLegalEntity] | None
    ) = Field(
        None, description='Company specified attributes or fields for the legal entity.'
    )


class ListServiceChargesRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    serviceType: ServiceType | None = Field(
        None, description='Type of service charge (Optional).'
    )


class LocationMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: LocationType = Field(..., description='Type of the object.')
    code: str = Field(..., description='Unique code to represent the object.')


class OtherCoinageItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    coinageCode: PaymentMethod | None = Field(None, description='Payment method')
    amount: float | None = Field(None, examples=[1000])
    conversionRate: float | None = Field(
        None,
        description='1 coin in this system equals to how many currency value',
        examples=[0.01],
    )
    preferredCurrencyConversionRate: float | None = Field(
        None,
        description='1 coin in this system equals to how many currency value',
        examples=[0.01],
    )


class Money(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: float = Field(
        ..., description='The numeric value for the amount of money.', examples=[510]
    )
    currencyCode: str = Field(
        ...,
        description='The 3-letter currency code for the money amount (defined using ISO 4217 standard).',
        examples=['GBP'],
    )
    convertedAmount: float | None = Field(
        None,
        description="The converted currency and amount that has been converted (if a currency conversion has been requested).\nFor example, if the call requests that money be sent in a specified currency (because the frontend requested\nthe backend to send money in the user's preferred currency).\n",
        examples=[715.42],
    )
    convertedCurrency: str | None = Field(
        None,
        description='The 3-letter currency code for the converted currency (defined using ISO 4217 standard).',
        examples=['USD'],
    )
    otherCoinage: Sequence[OtherCoinageItem] | None = Field(
        None,
        description='List of the dollar amount in other coinage systems like reward points, cryptocurrency etc.',
        title='OtherCoinage',
    )


class Name(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    family1: str = Field(..., description='Last (family) name.', examples=['Gandas'])
    family2: str | None = Field(None, examples=['FamilyTwo'])
    given: str = Field(..., description='First (given) name.', examples=['Vichitr'])
    middle: str | None = Field(None, description='Middle name.', examples=['Kumar'])
    suffix: NameSuffix | None = Field(
        None,
        description='Suffix used with the name. For example SR or JR.',
        examples=['SR'],
    )
    preferred: str | None = Field(
        None,
        description='Informal preferred name added by traveler. This is not used on any PNR or tickets',
        examples=['Don'],
    )


class NonBillableReason(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    reasonCode: NonBillableReasonCode = Field(
        ...,
        description='Reason code for non-billable agent contact.',
        examples=['MINOR_REQUEST'],
    )
    notes: str | None = Field(
        None,
        description='Notes for non-billable agent contact.',
        examples=['Waived off'],
    )


class OfficeV2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress | None = None
    id: UUID | None = None
    name: str | None = Field(None, examples=['Office'])
    latlng: Latlng | None = None
    externalId: str | None = Field(None, examples=['external-id'])
    taxId: str | None = Field(None, examples=['123232'])


class OtherFeeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingFeeType: str = Field(
        ..., description='Type of the booking fee', examples=['OTHER_FEE']
    )
    calculatedAmount: Money | None = None
    chargeProcessorInfo: ChargeProcessorInfo | None = None
    otherFeeType: OtherFeeType
    feeName: str | None = Field(
        None,
        description='Name of the fee to be charged.',
        examples=['TMC Standard Fee'],
    )


class ProfileColors(RootModel[Sequence[TitledColor]]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: Sequence[TitledColor] = Field(
        ..., description='The list of colors relating to profile.'
    )


class RewardsProgram(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    allowedUserRoles: Sequence[RoleType] | None = Field(
        None, description="Roles entitled to redeem company's reward points."
    )
    conversionRate: ConversionRate | None = Field(
        None,
        description='Conversion rate of points to specified currency.',
        title='ConversionRate',
    )
    redeemablePaymentMethods: Sequence[PaymentMethod] | None = Field(
        None, description="Payment methods to redeem company's reward points."
    )
    backingCurrency: Sequence[str] | None = Field(
        None, description='The currencies backing up this rewards program.'
    )


class SecondaryServiceProviderTmc(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tmcId: CompanyId = Field(..., description='Id of the service provider TMC.')
    supplier: SupplierType = Field(
        ..., description='Supplier for which this service provider should be used.'
    )
    travelType: TravelType = Field(
        ..., description='Travel type for which this service provider should be used.'
    )


class ServiceChargeEntity(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    entityType: ServiceChargeEntityType = Field(
        ..., description='Entity type for service charge.', examples=['TRIP']
    )
    entityId: str = Field(
        ...,
        description='Entity ID for service charge e.g, trip ID for entityType=TRIP.',
        examples=['**********'],
    )


class SupportConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    contactNumbers: Sequence[PhoneNumber] | None = Field(
        None, description='List of support phone numbers.'
    )
    emailAddresses: Sequence[str] | None = Field(
        None, description='List of the support email addresses.'
    )
    freshdeskConfig: FreshdeskConfig | None = None
    tier: Tier | None = 'BASIC'
    genesysConfig: GenesysConfig | None = Field(
        None, description='Configuration for genesys.', title='GenesysConfig'
    )
    portalUrls: Sequence[str] | None = Field(
        None, description='List of the support portal urls.'
    )
    zendeskConfig: ZendeskConfig | None = Field(
        None, description='Configuration for zendesk.', title='ZendeskConfig'
    )
    genesysCloudConfig: GenesysCloudConfig | None = Field(
        None, description='Configuration for Genesys cloud.', title='GenesysCloudConfig'
    )
    twilioChatConfig: TwilioChatConfig | None = Field(
        None, description='Configuration for Twilio Chat.', title='TwilioChatConfig'
    )
    cognigyChatConfig: CognigyChatConfig | None = Field(
        None, description='Cognigy Chat Config.', title='CognigyChatConfig'
    )


class SupportConfigs(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    length: int | None = None
    elements: Sequence[SupportConfig] | None = Field(
        None, description='Support Configurations List.'
    )


class TaxAmount(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Money
    taxCode: str = Field(..., description='Tax code', examples=['VAT'])
    description: str = Field(..., description='Tax code')
    percentage: float | None = Field(None, description='Amount', examples=[10])


class TierChargeConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tier: Tier
    charges: Sequence[Money] = Field(..., description='Amount in specified currency')


class TierConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    tiers: Sequence[TierDefinition] | None = Field(
        None, description='A list of tiers for this company.'
    )


class TmcBasicInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    contractingTmc: CompanyRef = Field(
        ..., description='Contracting TMC is the TMC the user/organization contracted.'
    )
    bookingTmc: CompanyRef = Field(
        ...,
        description='Booking TMC is the TMC used for the bookings for the user/organization.',
    )


class TmcInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CompanyId = Field(..., description='TMC id.')
    primaryServiceProviderTmc: PrimaryServiceProviderTmc = Field(
        ..., description='Primary service provider TMC for the TMC.'
    )
    secondaryServiceProviderTmcs: Sequence[SecondaryServiceProviderTmc] | None = Field(
        None, description='Secondary service provider TMCs for the TMC.'
    )
    partnerTmcId: CompanyId | None = Field(
        None, description='Useful to identify the clients onboarded by a PARTNER_TMC'
    )


class TokenVerifier(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    doVerifyToken: bool = Field(
        ...,
        description='Token verification rules will be applied only if this is true.',
    )
    type: Literal['OKTA_JWT'] | None = Field(
        None, description='Method to verify the external token sent by partners.'
    )
    verifierDetails: VerifierDetails | None = None


class TransactionFeeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingFeeType: str = Field(
        ..., description='Type of the booking fee', examples=['TRANSACTION_FEE']
    )
    calculatedAmount: Money | None = None
    chargeProcessorInfo: ChargeProcessorInfo | None = None
    transactionFeeType: ServiceFeeTransactionType | None = None


class TripFeeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingFeeType: str = Field(
        ..., description='Type of the booking fee', examples=['TRIP_FEE']
    )
    calculatedAmount: Money | None = None
    chargeProcessorInfo: ChargeProcessorInfo | None = None


class UserOrgId(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    organizationAgencyId: OrganizationAgencyId | None = None
    organizationId: OrganizationId
    userId: UserId
    tmcInfo: TmcInfo | None = None
    tmcBasicInfo: TmcBasicInfo | None = None


class ValueAddedServiceFeeInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    bookingFeeType: str = Field(
        ..., description='Type of the booking fee', examples=['VALUE_ADDED_SERVICE_FEE']
    )
    calculatedAmount: Money | None = None
    chargeProcessorInfo: ChargeProcessorInfo | None = None
    valueAddedServiceFeeType: ValueAddedServiceFeeType | None = None


class VirtualCardInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Money | None = Field(
        None,
        description='Virtual card deployment amount. This amount will drive the maximum authorisation value permitted on the virtual card.',
    )
    dateRange: DateRange | None = Field(
        None, description='Date range within which the virtual card can be charged.'
    )


class VirtualCardPaymentMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    vendorInfo: VirtualCardVendorInfo
    cardInfo: VirtualCardInfo | None = None
    paymentInstructionId: str | None = Field(
        None,
        description='Payment instruction id set during addition of virtual card payment source',
        examples=['1eb8b778-f0a6-4037-865c-4580982fa36e'],
    )
    shouldReveal: bool | None = Field(
        None,
        description='Identifier for when to reveal virtual cards as they are revealed to travellers only 24hrs before check-in.',
        examples=[False],
    )


class AgentContact(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    contactType: AgentContactType = Field(
        ..., description='Mode of agent contact.', examples=['PHONE']
    )
    contactId: str = Field(
        ..., description='Unique ID for agent contact.', examples=['**********']
    )
    contactOp: AgentContactOp = Field(
        ...,
        description='Operation that the agent performed.',
        examples=['AGENT_BOOKING'],
    )
    agentId: UserId | None = Field(None, description='User ID of agent.')
    billable: bool = Field(
        ..., description='Is the agent contact event billable?', examples=[True]
    )
    pnrId: str | None = Field(
        None, description='PNR ID (if applicable).', examples=['**********']
    )
    travelType: TravelType | None = Field(
        None, description='Travel type.', examples=['AIR']
    )
    notes: str | None = Field(
        None, description='Agent notes.', examples=['Requested by customer']
    )
    nonBillableReason: NonBillableReason | None = Field(
        None, description='Reason for non-billable agent contact'
    )


class BookingFeeInfo1(TripFeeInfo):
    model_config = ConfigDict(
        frozen=True,
    )
    feeType: Literal['BOOKING_FEE'] = Field(
        'BOOKING_FEE', description='Type of the fee', examples=['BOOKING_FEE']
    )
    bookingFeeType: Literal['TRIP_FEE'] = 'TRIP_FEE'


class BookingFeeInfo2(TransactionFeeInfo):
    model_config = ConfigDict(
        frozen=True,
    )
    feeType: Literal['BOOKING_FEE'] = Field(
        'BOOKING_FEE', description='Type of the fee', examples=['BOOKING_FEE']
    )
    bookingFeeType: Literal['TRANSACTION_FEE'] = 'TRANSACTION_FEE'


class BookingFeeInfo3(ValueAddedServiceFeeInfo):
    model_config = ConfigDict(
        frozen=True,
    )
    feeType: Literal['BOOKING_FEE'] = Field(
        'BOOKING_FEE', description='Type of the fee', examples=['BOOKING_FEE']
    )
    bookingFeeType: Literal['VALUE_ADDED_SERVICE_FEE'] = 'VALUE_ADDED_SERVICE_FEE'


class BookingFeeInfo4(OtherFeeInfo):
    model_config = ConfigDict(
        frozen=True,
    )
    feeType: Literal['BOOKING_FEE'] = Field(
        'BOOKING_FEE', description='Type of the fee', examples=['BOOKING_FEE']
    )
    bookingFeeType: Literal['OTHER_FEE'] = 'OTHER_FEE'


class BookingFeeInfo(
    RootModel[BookingFeeInfo1 | BookingFeeInfo2 | BookingFeeInfo3 | BookingFeeInfo4]
):
    model_config = ConfigDict(
        frozen=True,
    )
    root: BookingFeeInfo1 | BookingFeeInfo2 | BookingFeeInfo3 | BookingFeeInfo4 = Field(
        ..., discriminator='bookingFeeType', title='BookingFeeInfo'
    )


class CarbonCostConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    price: Money = Field(
        ..., description='The price for the carbon emission per weight.'
    )
    weight: Weight = Field(
        ...,
        description='The weight unit and amount to calculate cost of carbon emission.',
    )


class Card(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: UUID | None = Field(
        None,
        description='Unique identifier for this card',
        examples=['34d536b6-f8ff-11eb-9a61-0242ac180002'],
    )
    type: Type | None = Field(None, description='Type of card', examples=['CREDIT'])
    company: CardCompany | None = None
    name: str | None = Field(
        None, description='Name on card', examples=['Harrison Schwartz']
    )
    address: PostalAddress | None = Field(None, description='Billing address')
    number: str = Field(..., description='Card number', examples=['****************'])
    expiryMonth: conint(ge=1, le=12) | None = Field(
        None, description='Expiry month', examples=[1]
    )
    expiryYear: conint(ge=2000) | None = Field(
        None, description='Expiry year', examples=[2010]
    )
    cvv: str | None = Field(None, description='Card cvv number', examples=['012'])
    label: str | None = Field(None, description='Card Label', examples=['Label amex'])
    currency: str | None = Field(
        None, description='Native currency of the card.', examples=['USD']
    )
    externalId: str | None = Field(
        None,
        description='Spotnana partner card id.',
        examples=['bxt_RNGsNfzgJDaTstKIKqK4xEuhGYAnMdYK8T40'],
    )
    vaultId: UUID | None = Field(
        None,
        description='ID of the vault used for creating the card.',
        examples=['34d536b6-f8ff-11eb-9a61-0242ac180002'],
    )
    expiry: CardExpiry | None = Field(None, description='Card Expiry.')
    ownershipLabel: OwnershipLabel | None = Field(None, examples=['PERSONAL'])


class CardMetadata2(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    card: Card | None = None
    accessType: CreditCardAccess | None = None
    isLodgeCard: bool | None = Field(
        None,
        description='Whether the payment is made using a lodge card',
        examples=[False],
    )
    bta: str | None = Field(
        None,
        description="Whether this is a BTA card. Possible values are 'Y' or 'N'. Relevant only for lodge cards.",
        examples=['Y'],
    )


class ColorConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    profileColors: ProfileColors | None = None


class EmailClientConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    client: EmailClientType = Field(
        ..., description='The email client to be used for pushing out the emails'
    )
    credentials: EmailClientCredentials = Field(
        ..., description='Credentials for connecting to the email client'
    )
    defaultSenderAddress: str = Field(
        ...,
        description='The default sender address to be used for the configured client',
        examples=['<EMAIL>'],
    )
    defaultSenderName: str | None = Field(
        None,
        description='The default sender name to be used for the configured client',
        examples=['Test Sender'],
    )


class EmergencyContactInfo(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    address: PostalAddress | None = None
    designation: str | None = Field(None, examples=['MANAGER'])
    email: EmailStr = Field(..., examples=['<EMAIL>'])
    name: Name | None = None
    phoneNumber: PhoneNumber | None = None
    userOrgId: UserOrgId | None = None


class FeeInfo(RootModel[BookingFeeInfo]):
    model_config = ConfigDict(
        frozen=True,
    )
    root: BookingFeeInfo = Field(..., discriminator='feeType', title='FeeInfo')


class FixedChargeConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    charges: Sequence[Money] = Field(..., description='Amount in specified currency')
    defaultCurrencyCode: str | None = Field(
        None,
        description='The 3-letter currency code defined in ISO 4217.',
        examples=['GBP'],
    )
    tierCharges: Sequence[TierChargeConfig] | None = Field(
        None, description='Charges specified per traveler tier'
    )


class GetCompanyPerTripSrvChargeResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    config: FixedChargeConfig | None = None
    enabled: bool | None = Field(
        None,
        description='True if per trip service charge configuration is enabled',
        examples=[True],
    )
    enableDisableOpSupported: bool | None = Field(
        None,
        description='True if per trip service charge configuration can be enabled/disabled',
        examples=[True],
    )


class LocationItem(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    value: str = Field(..., description='User facing display value.')
    metadata: LocationMetadata = Field(
        ..., description='Metadata such as country code of the @value.'
    )
    infos: Sequence[LocationItem] = Field(
        ...,
        description='This represent other information for the @value.\nE.g. - This will have continent details if value is any country,\nsimilarly country details for city.\n',
    )


class OAuthPartnerConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Name of the partner.')
    userDetailEndpoint: ExternalEndpoint | None = None
    tokenVerifier: TokenVerifier
    doPersistToken: bool = Field(
        ...,
        description='Boolean to define whether to persist(in-memory/disk) token or not.',
    )


class PaymentMetadata(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    customPaymentMethodMetadata: CustomPaymentMethodMetadata | None = Field(
        None, description='Metadata related to custom payment method'
    )
    vendorProgramPaymentMetadata: VendorProgramPaymentMetadata2 | None = None
    virtualCardMetadata: VirtualCardPaymentMetadata | None = None
    cardMetadata: CardMetadata2 | None = None


class ServiceCharge(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    amount: Money = Field(..., description='Service charge amount.')
    taxes: Sequence[TaxAmount] = Field(..., description='Applicable taxes.')
    totalAmount: Money = Field(..., description='Total amount (including taxes).')
    allowedFoP: Sequence[PaymentMethod] | None = Field(
        None, description='List of allowed payment methods for this charge.'
    )
    feeInfo: FeeInfo | None = None


class SetCompanyPerTripSrvChargeRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    config: FixedChargeConfig


class WhiteLabelConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    companyName: str | None = Field(None, examples=['Spotnana'])
    colors: ColorWhiteLabelConfig
    webLinks: WebLinksConfig | None = None
    fromEmailAddress: str | None = Field(None, examples=['<EMAIL>'])
    fontFamily: str | None = Field('Arial', examples=['Arial'])
    emailFontFamily: str | None = Field('Roboto', examples=['Roboto'])
    fontFamilyCss: str | None = Field(
        None,
        examples=[
            'https://duploservices-stage-email-assets-************.s3.us-west-2.amazonaws.com/partners/amazon/emberDisplay.css'
        ],
    )
    emailClientConfig: EmailClientConfig | None = Field(
        None,
        description='Email client configuration representing the email provider to be used.',
    )
    clientRoutingBasePath: str | None = Field(
        None,
        description='Base path used by Spotnana OBT application to invoke API calls. Default path value is /',
    )
    routingConfig: RoutingConfig | None = None
    privacyDisclaimer: PrivacyDisclaimer | None = None
    logoConfig: LogoConfig | None = None


class Company(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    id: CompanyId
    name: str = Field(..., description='Company name', examples=['Spotnana'])
    emailDomains: Sequence[str] | None = Field(
        None, description='List of the company email domains'
    )
    billingCurrency: str = Field(
        ..., description='Billing currency of the company', examples=['INR']
    )
    phoneNumbers: Sequence[PhoneNumber] | None = Field(
        None, description='List of company phone numbers'
    )
    emergencyContactInfos: Sequence[EmergencyContactInfo] | None = Field(
        None, description='List of company emergency contacts'
    )
    isSelfSignUpEnabled: bool | None = Field(
        None,
        description='Boolean to show if company has self sign up enabled.',
        examples=[True],
    )
    announcements: Sequence[Announcement] | None = Field(
        None, description='Company announcements'
    )
    companyLogo: Image | None = Field(None, description='Company logo')
    contractedBy: CompanyId | None = Field(
        None, description='Company ID which have contracted this company.'
    )
    isActive: bool | None = Field(
        None, description='Boolean to show if this is an active company.'
    )
    companyRoles: Sequence[CompanyRole] = Field(..., description='Company roles')
    supportConfig: SupportConfig | None = Field(
        None, description='Support Configuration for company'
    )
    ccEmailAddresses: Sequence[str] | None = Field(
        None,
        description="List of email addresses on which all confirmation emails will be cced. This is applicable only for company roles 'ORG'",
    )
    bccEmailAddresses: Sequence[str] | None = Field(
        None,
        description="List of email addresses on which all confirmation emails will be bcced. This is applicable only for company roles 'ORG'",
    )
    connectors: Sequence[Connector] | None = Field(
        None, description='Connectors related to company.'
    )
    postalAddress: PostalAddress | None = Field(
        None, description='Postal address of the company.'
    )
    redirectUrl: str | None = Field(
        None,
        description="Redirect URL for the company. It is applicable only for company roles 'PARTNER_TMC'",
    )
    rewardsProgram: RewardsProgram | None = Field(
        None,
        description="Reward Program corresponding to partner. It is applicable only for company roles 'PARTNER_TMC'.",
    )
    tmcDefaultConfiguration: TmcDefaultConfiguration | None = Field(
        None,
        description="Default Booking configuration for the TMC. It is applicable only for company roles 'TMC'",
    )
    expensePartnerConfig: ExpensePartnerConfig | None = None
    mobileRedirectUrl: str | None = Field(
        None,
        description="Redirect URL for the company for mobile. It is applicable only for company roles 'PARTNER_TMC'",
    )
    applicableCustomFieldTypes: Sequence[CustomFieldType] | None = Field(
        None, description='List of custom fields supported by the company.'
    )
    oauthPartnerConfig: OAuthPartnerConfig | None = None
    privacySettings: PrivacySettings | None = None
    designationTree: DesignationNode | None = Field(
        None, description='Designation tree for company showing who reports whom.'
    )
    externalId: str | None = Field(
        None,
        description='External id to be linked with this.',
        examples=['MyCompanyId'],
    )
    emailClientConfig: EmailClientConfig | None = Field(
        None,
        description='Email client configuration representing the email provider to be used.',
    )
    isFake: bool | None = Field(
        False,
        description='Whether the company is a fake company for testing. This is for internal use.',
        examples=[False],
    )
    tmcPartnerRoleMappings: Sequence[TmcPartnerRoleMapping] | None = Field(
        None,
        description='list of individual role mappings between spotnana and partner.',
    )
    supportConfigs: SupportConfigs | None = None
    loyaltyBlockedCountries: Sequence[str] | None = Field(
        None,
        description='List of 2 letter country codes where business travelers should not be allowed to add loyalty info.',
        examples=[['US', 'IN']],
    )
    allowDomainBasedAuthConfig: bool | None = Field(
        False,
        description='Whether to allow user to get auth config based on email domain',
    )
    carbonCostConfig: CarbonCostConfig | None = Field(
        None, description='The config is used to calculate carbon cost of CO2 emission.'
    )
    defaultBookingTmcId: CompanyId | None = Field(
        None, description='Id of the default booking TMC for the company.'
    )


class CreateCompanyRequest(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    name: str = Field(..., description='Company name', examples=['Spotnana'])
    emailDomains: Sequence[str] | None = Field(
        None, description='List of the company email domains'
    )
    billingCurrency: str = Field(
        ..., description='Billing currency of the company', examples=['INR']
    )
    phoneNumbers: Sequence[PhoneNumber] | None = Field(
        None, description='List of company phone numbers'
    )
    emergencyContactInfos: Sequence[EmergencyContactInfo] | None = Field(
        None, description='List of company emergency contacts'
    )
    isSelfSignUpEnabled: bool | None = Field(
        None,
        description='Boolean to show if company has self sign up enabled.',
        examples=[True],
    )
    announcements: Sequence[Announcement] | None = Field(
        None, description='Company announcements'
    )
    companyLogo: Image | None = Field(None, description='Company logo')
    contractedBy: CompanyId | None = Field(
        None, description='Company ID which have contracted this company.'
    )
    companyRoles: Sequence[CompanyRole] = Field(..., description='Company roles')
    supportConfig: SupportConfig | None = Field(
        None, description='Support Configuration for company'
    )
    supportConfigs: Sequence[SupportConfig] | None = Field(
        None, description='Support Configs for company'
    )
    ccEmailAddresses: Sequence[str] | None = Field(
        None,
        description="List of email addresses on which all confirmation emails will be cced. This is applicable only for company roles 'ORG'",
    )
    bccEmailAddresses: Sequence[str] | None = Field(
        None,
        description="List of email addresses on which all confirmation emails will be bcced. This is applicable only for company roles 'ORG'",
    )
    postalAddress: PostalAddress | None = Field(
        None, description='Postal address of the company.'
    )
    redirectUrl: str | None = Field(
        None,
        description="Redirect URL for the company. It is applicable only for company roles 'PARTNER_TMC'",
    )
    rewardsProgram: RewardsProgram | None = Field(
        None,
        description="Rewards Program corresponding to partner. It is applicable only for company roles 'PARTNER_TMC'.",
    )
    tmcDefaultConfiguration: TmcDefaultConfiguration | None = Field(
        None,
        description="Default Booking configuration for the TMC. It is applicable only for company roles 'TMC'",
    )
    expensePartnerConfig: ExpensePartnerConfig | None = None
    mobileRedirectUrl: str | None = Field(
        None,
        description="Redirect URL for the company for mobile. It is applicable only for company roles 'PARTNER_TMC'",
    )
    applicableCustomFieldTypes: Sequence[CustomFieldType] | None = Field(
        None, description='List of custom fields supported by the company.'
    )
    oauthPartnerConfig: OAuthPartnerConfig | None = None
    privacySettings: PrivacySettings | None = None
    emailClientConfig: EmailClientConfig | None = Field(
        None,
        description='Email client configuration representing the email provider to be used.',
    )
    externalId: str | None = Field(
        None,
        description='External id for legal entity.',
        examples=['company-external-id'],
    )
    isFake: bool | None = Field(
        False,
        description='Whether the company is a fake company for testing. This is for internal use.',
        examples=[False],
    )
    tmcPartnerRoleMappings: Sequence[TmcPartnerRoleMapping] | None = Field(
        None,
        description='list of individual role mappings between spotnana and partner.',
    )
    loyaltyBlockedCountries: Sequence[str] | None = Field(
        None,
        description='List of 2 letter country codes where business travelers should not be allowed to add loyalty info.',
        examples=[['US', 'IN']],
    )
    allowDomainBasedAuthConfig: bool | None = Field(
        False,
        description='Whether to allow user to get auth config based on email domain',
    )
    carbonCostConfig: CarbonCostConfig | None = Field(
        None, description='The config is used to calculate carbon cost of CO2 emission.'
    )
    defaultBookingTmcId: CompanyId | None = Field(
        None, description='Id of the default booking TMC for the company.'
    )


class FareCategoryFilterConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    priority: int = Field(
        ...,
        description='priority of this filter in case multiple filters are applicable',
        examples=[1],
    )
    origin: LocationItem = Field(
        ...,
        description='origin for which this filter will be valid (* for all origins)',
    )
    destination: LocationItem = Field(
        ...,
        description='destination for which this filter will be valid (* for all destinations)',
    )
    fareCategoryOption: Sequence[FareCategoryOptionConfig]


class FormOfPayment(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    type: Type1 = Field(
        ..., description='Type of payment used', examples=['CARD'], title='PaymentType'
    )
    card: Card | None = Field(
        None,
        description="The payment card to be used to charge customer. This is only set if the payment type is 'CARD'",
    )
    additionalInfo: str | None = Field(
        None, description="Additional info to be added if payment type is 'UNKNOWN'."
    )
    accessType: CreditCardAccess | None = None
    paymentMethod: PaymentMethod | None = Field(
        None, description='Payment method used to pay for this transaction'
    )
    paymentMetadata: PaymentMetadata | None = None
    paymentSourceType: PaymentSourceType | None = None


class ServiceChargeRecord(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    entity: ServiceChargeEntity = Field(..., description='Entity for service charge.')
    tripId: str | None = Field(
        None, description='Trip ID (if applicable).', examples=['**********']
    )
    serviceChargeType: ServiceChargeType = Field(
        ..., description='Type of service charge.', examples=['TMC_CHARGE']
    )
    serviceType: ServiceType = Field(
        ..., description='Type of service.', examples=['AGENT_CONTACT']
    )
    agentContact: AgentContact | None = Field(
        None, description='Agent contact details if serviceType is AGENT_CONTACT.'
    )
    chargeId: str | None = Field(
        None,
        description='Charge Id to uniquely identify service charge.',
        examples=['e2c04834-92eb-4b62-9faa-eb73a70051d3'],
    )
    charge: ServiceCharge = Field(..., description='Service charge.')
    transactionTime: DateTimeOffset = Field(
        ..., description='Transaction date and time.'
    )
    paymentStatus: PaymentStatus = Field(..., description='Payment status.')
    userOrgId: UserOrgId | None = Field(None, description='User org details.')
    legalEntityId: LegalEntityId | None = Field(
        None, description='The ID of the legal entity.'
    )
    bookingDetails: BookingDetails | None = Field(None, description='Booking details.')
    fop: FormOfPayment | None = Field(
        None, description='Form of payment for the service charge.'
    )
    paymentTransaction: PaymentTransaction | None = Field(
        None, description='Payment transaction details.'
    )
    failureDetails: FailureDetails | None = Field(None, description='Failure details.')


class AirConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    fareCategoryFilter: Sequence[FareCategoryFilterConfig] | None = Field(
        None, description='List of fare category filter'
    )


class ListServiceChargesResponse(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    serviceCharges: Sequence[ServiceChargeRecord] | None = Field(
        None, description='List of service charges.'
    )


class TravelContentConfig(BaseModel):
    model_config = ConfigDict(
        frozen=True,
    )
    airConfig: AirConfig | None = Field(None, description='Air content config')


DesignationNode.model_rebuild()
LocationItem.model_rebuild()
