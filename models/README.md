# DTO Models for Otto Travel Platform

This directory implements a comprehensive three-layer DTO (Data Transfer Object) architecture for <PERSON>, providing type-safe, validated data handling across all external API integrations.

## Architecture Overview

### Three-Layer Design

```
┌─────────────────────┐
│   API Layer         │  ← Frontend-optimized responses
│   models/api/       │
├─────────────────────┤
│   Domain Layer      │  ← Unified business logic
│   models/domain/    │
├─────────────────────┤
│   External Layer    │  ← Raw API responses
│   models/external/  │
└─────────────────────┘
         ↕ Converters
```

### Benefits

- **Type Safety**: Catch data access errors at development time
- **Validation**: Automatic validation of all API responses and business rules
- **Maintainability**: Clear data flow and transformation tracking
- **API Change Resilience**: Structured approach to handling external API changes
- **Documentation**: Self-documenting data structures with field descriptions

## Directory Structure

```
models/
├── __init__.py              # Package initialization
├── base.py                  # Base DTO classes and abstract converters
├── validation.py            # Validation utilities and sanitizers
├── config.py               # Feature flags and configuration
├── (tests in tests/dto_models/)
├── README.md               # This documentation
├── external/               # Raw API response models
│   ├── flights/           # Flight API models (Spotnana, SERP)
│   ├── hotels/            # Hotel API models (Booking.com)
│   └── auth/              # Authentication API models
├── domain/                 # Unified business models
│   ├── flights/           # Flight business logic
│   ├── hotels/            # Hotel business logic
│   └── auth/              # User and auth business logic
├── api/                   # Frontend response models
│   ├── flights/           # Flight API responses
│   ├── hotels/            # Hotel API responses
│   └── auth/              # Auth API responses
└── converters/            # Data transformation utilities
    ├── flights/           # Flight data converters
    ├── hotels/            # Hotel data converters
    └── auth/              # Auth data converters
```

## Modern Type Usage

This implementation uses Python 3.10+ modern type syntax:

```python
# Modern union types (instead of Optional/Union)
user_id: str | None = None           # instead of Optional[str]
data: dict[str, Any]                 # instead of Dict[str, Any]
items: list[FlightOption]            # instead of List[FlightOption]

# Modern datetime with timezone awareness
from datetime import datetime, timezone
created_at = datetime.now(timezone.utc)  # instead of datetime.utcnow()

# Modern callable imports
from collections.abc import Callable     # instead of typing.Callable
```

Benefits:
- **Cleaner syntax**: `str | None` is more readable than `Optional[str]`
- **Future-proof**: Follows PEP 604 and PEP 585 standards
- **Performance**: Built-in types are faster than typing generics
- **Timezone-aware**: Explicit UTC timezone instead of naive datetime

## Base Classes

### BaseExternalModel
```python
class BaseExternalModel(BaseModel):
    """Raw API response models with minimal transformation"""
    model_config: ClassVar[ConfigDict] = ConfigDict(
        populate_by_name=True,       # API field aliases for field mapping
        validate_assignment=True,    # Type safety on field assignment
        extra="allow",              # Forward compatibility with API changes
        use_enum_values=True,       # Serialize enums as values
        validate_default=True       # Validate default field values
    )
    
    def model_dump_safe(self) -> dict[str, Any]:
        """Safe serialization with error handling."""
```

### BaseDomainModel
```python
class BaseDomainModel(BaseModel):
    """Unified business logic models"""
    model_config: ClassVar[ConfigDict] = ConfigDict(
        validate_assignment=True,    # Type safety on field assignment
        extra="forbid",             # Strict validation, no extra fields
        use_enum_values=True,       # Consistent enum serialization
        validate_default=True,      # Validate default values
        arbitrary_types_allowed=True # Allow complex business objects
    )
    
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime | None = None
    
    def mark_updated(self) -> None:
        """Update the updated_at timestamp."""
    
    def validate_business_rules(self) -> list[str]:
        """Validate business rules, return list of error messages."""
```

### BaseApiModel
```python
class BaseApiModel(BaseModel):
    """Frontend-optimized response models"""
    model_config: ClassVar[ConfigDict] = ConfigDict(
        validate_assignment=True,    # Type safety on field assignment
        extra="forbid",             # Clean responses, no extra fields
        use_enum_values=True,       # JSON-compatible enum values
        validate_default=True       # Validate default values
    )
    
    def model_dump_for_api(self) -> dict[str, object]:
        """Frontend-optimized serialization."""
```

### BaseConverter
```python
class BaseConverter(ABC, Generic[T, U]):
    """Abstract converter with error handling and logging"""
    
    @abstractmethod
    def convert(self, source: T) -> U:
        """Convert from source model to target model."""
    
    def safe_convert(
        self,
        data: Any,
        converter_func: Callable[[Any], U],
        fallback: U | None = None,
        error_context: dict[str, Any] | None = None,
    ) -> U | None:
        """Safely convert data with error handling and logging."""
    
    @staticmethod
    def safe_get(
        data: Mapping[str, Any], 
        key_path: str, 
        default: object = None, 
        required: bool = False
    ) -> object:
        """Safely extract nested values using dot notation."""
```

## Feature Flags

The implementation uses feature flags for gradual rollout:

```python
from models.config import use_dto_for_flights

# In flight processing code
if use_dto_for_flights():
    # New DTO path
    response_model = SpotnanaFlightSearchResponse.model_validate(data)
    converter = SpotnanaToUnifiedConverter()
    unified_options = converter.convert(response_model)
else:
    # Existing dictionary path
    # ... existing code unchanged ...
```

### Environment Variables

```bash
# Enable specific features
OTTO_DTO_FLIGHTS_EXTERNAL=true
OTTO_DTO_HOTELS_EXTERNAL=true
OTTO_DTO_AUTH_EXTERNAL=true

# Master switches
OTTO_DTO_ENABLE_ALL=true      # Enable all features
OTTO_DTO_DISABLE_ALL=true     # Disable all features (safety)

# Configuration
OTTO_DTO_STRICT_VALIDATION=false    # Strict validation mode
OTTO_DTO_FALLBACK_ENABLED=true      # Fallback to dictionaries on errors
```

## Validation & Sanitization

### Data Sanitizers

```python
from models.validation import DataSanitizer

# Currency normalization
amount = DataSanitizer.normalize_currency_amount("$123.45")  # → 123.45

# Airport codes
code = DataSanitizer.normalize_airport_code("lax")  # → "LAX"

# Flight numbers
flight = DataSanitizer.normalize_flight_number("UA 1234")  # → "UA1234"

# DateTime strings (timezone-aware)
dt = DataSanitizer.normalize_datetime_string("2024-01-15 10:30:00")  # → ISO format
```

### Business Rule Validators

```python
from models.validation import BusinessRuleValidators

# Flight timing validation
BusinessRuleValidators.validate_flight_timing(departure, arrival)

# Price consistency validation
BusinessRuleValidators.validate_price_consistency(base_fare, taxes, total)

# Passenger count validation
BusinessRuleValidators.validate_passenger_count(adults=2, children=1, infants=1)
```

### Pre-configured Fields

```python
from models.validation import CurrencyField, AirportCodeField, FlightNumberField

class FlightSegment(BaseModel):
    price: float = CurrencyField
    origin: str = AirportCodeField
    flight_number: str = FlightNumberField
```

## Usage Examples

### External API Response Processing

```python
from models.external.flights.spotnana_models import SpotnanaFlightSearchResponse

# Raw API response
api_data = spotnana_client.search_flights(params)

# Parse and validate
response = SpotnanaFlightSearchResponse.model_validate(api_data)

# Safe access with type hints
if response.itinerary_details:
    for itinerary in response.itinerary_details.itineraries:
        print(f"Price: {itinerary.fare_info.total_fare.base.amount}")
```

### Data Conversion

```python
from models.converters.flights import SpotnanaToUnifiedConverter

# Create converter instance (required for instance methods)
converter = SpotnanaToUnifiedConverter()

# Convert external to domain models using abstract convert method
unified_flights = converter.convert(spotnana_response)

# Convert domain to API models
api_converter = UnifiedToApiConverter()
api_response = api_converter.convert(unified_flights)
```

### Error Handling

```python
from models.base import BaseConverter
from models.converters.flights import SpotnanaToUnifiedConverter

# Create converter instance
converter = SpotnanaToUnifiedConverter()

# Safe conversion with fallback using instance method
result = converter.safe_convert(
    api_data,
    lambda x: SpotnanaFlightSearchResponse.model_validate(x),
    fallback=None,
    error_context={"source": "spotnana", "endpoint": "search"}
)

# Safe nested access (static method)
price = BaseConverter.safe_get(
    data, 
    "itineraryDetails.itineraries.0.fareInfo.totalFare.base.amount",
    default=0.0
)
```

## Test Organization

The DTO model tests are organized in a parallel structure under `tests/dto_models/`:

```
tests/
└── dto_models/                 # DTO model tests (avoids import conflicts)
    ├── test_base.py           # Foundation tests
    ├── external/              # External model tests
    │   ├── flights/           # Flight external model tests
    │   ├── hotels/            # Hotel external model tests
    │   └── auth/              # Auth external model tests
    ├── domain/                # Domain model tests
    │   ├── flights/           # Flight domain model tests
    │   ├── hotels/            # Hotel domain model tests
    │   └── auth/              # Auth domain model tests
    ├── api/                   # API model tests
    │   ├── flights/           # Flight API model tests
    │   ├── hotels/            # Hotel API model tests
    │   └── auth/              # Auth API model tests
    └── converters/            # Converter tests
        ├── flights/           # Flight converter tests
        ├── hotels/            # Hotel converter tests
        └── auth/              # Auth converter tests
```

**Note**: The tests directory is named `dto_models` instead of `models` to avoid Python import conflicts between `tests.models` and the root `models` package.

## Testing

Run the foundation tests:

```bash
# Run all DTO tests
uv run pytest tests/dto_models/test_base.py -v

# Run with coverage
uv run pytest tests/dto_models/test_base.py --cov=models --cov-report=html

# Type checking
uv run pyright models/

# Linting
uv run ruff check models/
uv run ruff format models/

# Run all tests including models
uv run pytest tests/ -v

# Run just DTO model tests
uv run pytest tests/dto_models/ -v
```

## Implementation Status

### ✅ Phase 1: Foundation (Complete)
Base DTO architecture, classes, validation utilities, feature flags, and comprehensive test suite.

### 🔄 Phase 2: Flight Models (In Progress)
External models for Spotnana, SERP, and webhooks; unified domain models; API response models; data converters.

### ⏳ Phase 3: Hotel Models (Planned)
Booking.com external models, hotel domain models, API models, and converters.

### ⏳ Phase 4: Auth Models (Planned)
OAuth external models, user domain models, auth API models, and converters.

For detailed implementation checklists and progress tracking, see [`docs/dto_phase_1.md`](../docs/dto_phase_1.md).

## Migration Guidelines

1. **Start with External Models**: Define raw API response structures first
2. **Add Domain Models**: Create unified business representations
3. **Build Converters**: Implement transformation logic with error handling
4. **Create API Models**: Design frontend-optimized responses
5. **Test Thoroughly**: Validate with real API response samples
6. **Enable Gradually**: Use feature flags for safe rollout

## Contributing

When adding new DTO models:

1. **Inherit from appropriate base classes**: Use `BaseExternalModel`, `BaseDomainModel`, or `BaseApiModel`
2. **Use validation utilities** for data sanitization and business rule validation
3. **Add comprehensive field documentation** with type hints and descriptions
4. **Implement corresponding converter classes**: Inherit from `BaseConverter[T, U]` and implement the abstract `convert` method
5. **Add unit tests** with real API response samples and error scenarios
6. **Update feature flag configuration** for gradual rollout
7. **Document breaking changes** and migration paths

### Example Converter Implementation:

```python
from typing_extensions import override
from models.base import BaseConverter

class SpotnanaToUnifiedConverter(BaseConverter[SpotnanaFlightResponse, UnifiedFlightOption]):
    @override
    def convert(self, source: SpotnanaFlightResponse) -> UnifiedFlightOption:
        # Implementation with error handling
        return UnifiedFlightOption(
            price=BaseConverter.safe_get(
                source.model_dump(), 
                "fareInfo.totalFare.base.amount", 
                default=0.0
            ),
            # ... other field mappings
        )
```

## Support

For questions or issues with the DTO implementation:

1. Check existing tests for usage patterns
2. Review base class documentation
3. Test with feature flags disabled for comparison
4. Monitor logs for conversion errors
5. Use fallback mechanisms during debugging