"""
DTO Models for Otto Travel Platform

This package implements a three-layer DTO architecture:
- External Layer: Raw API response models
- Domain Layer: Unified business logic models
- API Layer: Frontend-optimized response models

Usage:
    from models.external.flights.spotnana_models import SpotnanaFlightSearchResponse
    from models.domain.flights.unified_models import UnifiedFlightOption
    from models.api.flights.response_models import FlightSearchResponse
"""

__version__ = "1.0.0"
