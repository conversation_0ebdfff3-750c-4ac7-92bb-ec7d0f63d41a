"""
Air-related adapter models for Spotnana Trip API.

Provides type-safe access to air PNR, leg, and flight data without dictionary access.
"""

from functools import cached_property
from typing import Any

import structlog

from models.adapters.base import BaseAdapter
from models.api.spotnana.trip.models import Air, DateTimeLocal, Flight, Leg
from server.utils.enum_helpers import serialize_enum_value

logger = structlog.get_logger(__name__)


class FlightAdapter(BaseAdapter[Flight]):
    """
    Adapter for individual Flight data.

    Provides type-safe access to flight information including
    confirmation numbers, airline codes, and timing details.
    """

    @property
    def vendor_confirmation_number(self) -> str | None:
        """
        Get the vendor confirmation number for this flight.
        Replaces: flight.get("vendorConfirmationNumber")
        """
        return self._source.vendorConfirmationNumber

    @property
    def airline_code(self) -> str:
        """
        Get the airline code for this flight.
        Replaces: flight.get("airlineCode")
        """
        return self._source.marketing.airlineCode

    @property
    def flight_number(self) -> str:
        """
        Get the flight number.
        Replaces: flight.get("num")
        """
        return self._source.marketing.num

    @property
    def departure_datetime(self) -> DateTimeLocal:
        """
        Get the departure date and time.
        Replaces: flight.get("departureDateTime")
        """
        return self._source.departureDateTime

    @property
    def arrival_datetime(self) -> DateTimeLocal:
        """
        Get the arrival date and time.
        Replaces: flight.get("arrivalDateTime")
        """
        return self._source.arrivalDateTime

    @property
    def flight_id(self) -> str | None:
        """
        Get the unique flight identifier.
        Replaces: flight.get("flightId")
        """
        return self._source.flightId

    @property
    def duration(self) -> Any | None:
        """
        Get the flight duration.
        Replaces: flight.get("duration")
        """
        return self._source.duration


class LegAdapter(BaseAdapter[Leg]):
    """
    Adapter for flight Leg data.

    Provides type-safe access to leg information including
    flights, brand name, and leg status.
    """

    @cached_property
    def flights(self) -> list[FlightAdapter]:
        """
        Get the flights in this leg as typed adapters.
        Replaces: leg.get("flights", [])
        """
        return [FlightAdapter(flight) for flight in self._source.flights]

    @property
    def brand_name(self) -> str | None:
        """
        Get the brand name for this leg.
        Replaces: leg.get("brandName")
        """
        return self._source.brandName

    @property
    def validating_airline_code(self) -> str | None:
        """
        Get the validating airline code.
        Replaces: leg.get("validatingAirlineCode")
        """
        return self._source.validatingAirlineCode

    @property
    def leg_status(self) -> str | None:
        """
        Get the user-facing leg status.
        Replaces: leg.get("legStatus")
        """
        return serialize_enum_value(self._source.legStatus)

    def get_vendor_confirmation_number(self) -> str | None:
        """
        Get the vendor confirmation number from the first flight.
        Common access pattern for confirmation numbers.
        """
        if not self.flights:
            return None
        return self.flights[0].vendor_confirmation_number


class AirPnrAdapter(BaseAdapter[Air]):
    """
    Adapter for Air PNR data.

    Provides type-safe access to air booking information including
    legs, traveler information, and booking metadata.
    """

    @cached_property
    def legs(self) -> list[LegAdapter]:
        """
        Get the legs in this air PNR as typed adapters.
        Replaces: air_pnr.get("legs", [])
        """
        return [LegAdapter(leg) for leg in self._source.legs]

    @property
    def air_pnr_remarks(self) -> list[Any]:
        """
        Get the air PNR remarks.
        Replaces: air_pnr.get("airPnrRemarks", [])
        """
        return list(self._source.airPnrRemarks) if self._source.airPnrRemarks else []

    @property
    def traveler_infos(self) -> list[Any]:
        """
        Get the traveler information.
        Replaces: air_pnr.get("travelerInfos", [])
        """
        return list(self._source.travelerInfos)

    def get_vendor_confirmation_number(self) -> str | None:
        """
        Get the vendor confirmation number from the first flight of the first leg.

        Replaces the complex pattern:
        air_pnr.get("legs", [])[0].get("flights", [])[0].get("vendorConfirmationNumber")
        """
        if not self.legs:
            return None
        return self.legs[0].get_vendor_confirmation_number()

    def get_all_flights(self) -> list[FlightAdapter]:
        """
        Get all flights across all legs.
        Convenient method for iterating over all flights.
        """
        all_flights = []
        for leg in self.legs:
            all_flights.extend(leg.flights)
        return all_flights

    def get_airline_codes(self) -> list[str]:
        """
        Get all unique airline codes from all flights.
        Useful for displaying multiple airlines.
        """
        airline_codes = set()
        for flight in self.get_all_flights():
            airline_codes.add(flight.airline_code)
        return list(airline_codes)
