"""
Hotel-related adapter models for Spotnana Trip API.

Provides type-safe access to hotel PNR, room, and hotel info data without dictionary access.
"""

from typing import Any

import structlog

from models.adapters.base import BaseAdapter
from models.api.spotnana.trip.models import DateTimeLocal, Hotel, HotelInfo, Room
from server.utils.enum_helpers import serialize_enum_value

logger = structlog.get_logger(__name__)


class RoomAdapter(BaseAdapter[Room]):
    """
    Adapter for hotel Room data.

    Provides type-safe access to room information including
    cancellation policy, bed details, and rate information.
    """

    @property
    def cancellation_policy(self) -> str | None:
        """
        Get the room cancellation policy as a string.
        Replaces: room.get("cancellationPolicy")

        Handles different types of cancellation policy objects.
        """
        if self._source.cancellationPolicy is None:
            return None

        # Handle different types of cancellation policy
        policy = self._source.cancellationPolicy
        return str(policy) if policy is not None else None

    @property
    def bed_count(self) -> int:
        """
        Get the number of beds in the room.
        Replaces: room.get("bedCount")
        """
        return self._source.bedCount

    @property
    def bed_type(self) -> str:
        """
        Get the bed type.
        Replaces: room.get("bedType")
        """
        return serialize_enum_value(self._source.bedType) or "unknown"

    @property
    def room_name(self) -> str | None:
        """
        Get the room name.
        Replaces: room.get("roomName")
        """
        return self._source.roomName

    @property
    def rate_info(self) -> Any:
        """
        Get the rate information for the room.
        Replaces: room.get("rateInfo")
        """
        return self._source.rateInfo


class HotelInfoAdapter(BaseAdapter[HotelInfo]):
    """
    Adapter for HotelInfo data.

    Provides type-safe access to hotel details including
    name, address, chain information, and contact details.
    """

    @property
    def hotel_name(self) -> str:
        """
        Get the hotel name.
        Replaces: hotel_info.get("hotelName")
        """
        return self._source.name

    @property
    def chain_code(self) -> str | None:
        """
        Get the hotel chain code.
        Replaces: hotel_info.get("chainCode")
        """
        return self._source.chainCode

    @property
    def chain_name(self) -> str | None:
        """
        Get the hotel chain name.
        Replaces: hotel_info.get("chainName")
        """
        return self._source.chainName

    @property
    def hotel_id(self) -> str | None:
        """
        Get the hotel ID.
        Replaces: hotel_info.get("hotelId")
        """
        return self._source.hotelId

    @property
    def email(self) -> str | None:
        """
        Get the hotel email address.
        Replaces: hotel_info.get("email")
        """
        return self._source.email

    @property
    def phone(self) -> str | None:
        """
        Get the hotel phone number.
        Replaces: hotel_info.get("phone")
        """
        return str(self._source.phone) if self._source.phone else None

    @property
    def address(self) -> Any:
        """
        Get the hotel address.
        Replaces: hotel_info.get("address")
        """
        return self._source.address

    @property
    def coordinates(self) -> Any | None:
        """
        Get the hotel coordinates.
        Replaces: hotel_info.get("coordinates")
        """
        return self._source.coordinates


class HotelPnrAdapter(BaseAdapter[Hotel]):
    """
    Adapter for Hotel PNR data.

    Provides type-safe access to hotel booking information including
    check-in/out dates, room details, and confirmation numbers.
    """

    @property
    def vendor_confirmation_number(self) -> str:
        """
        Get the hotel confirmation number.
        Replaces: hotel_pnr.get("vendorConfirmationNumber")
        """
        return self._source.vendorConfirmationNumber

    @property
    def check_in_datetime(self) -> DateTimeLocal:
        """
        Get the check-in date and time.
        Replaces: hotel_pnr.get("checkInDateTime")
        """
        return self._source.checkInDateTime

    @property
    def check_out_datetime(self) -> DateTimeLocal:
        """
        Get the check-out date and time.
        Replaces: hotel_pnr.get("checkOutDateTime")
        """
        return self._source.checkOutDateTime

    @property
    def number_of_rooms(self) -> int:
        """
        Get the number of rooms booked.
        Replaces: hotel_pnr.get("numberOfRooms")
        """
        return self._source.numberOfRooms

    @property
    def pnr_status(self) -> str | None:
        """
        Get the PNR status.
        Replaces: hotel_pnr.get("pnrStatus")
        """
        return serialize_enum_value(self._source.pnrStatus)

    @property
    def hotel_info(self) -> HotelInfoAdapter:
        """
        Get the hotel information as a typed adapter.
        Replaces: hotel_pnr.get("hotelInfo")
        """
        return HotelInfoAdapter(self._source.hotelInfo)

    @property
    def room(self) -> RoomAdapter:
        """
        Get the room information as a typed adapter.
        Replaces: hotel_pnr.get("room")
        """
        return RoomAdapter(self._source.room)

    @property
    def payment(self) -> Any:
        """
        Get the payment information.
        Replaces: hotel_pnr.get("payment")
        """
        return self._source.payment

    @property
    def traveler_infos(self) -> list[Any]:
        """
        Get the traveler information.
        Replaces: hotel_pnr.get("travelerInfos", [])
        """
        return list(self._source.travelerInfos) if self._source.travelerInfos else []

    def get_hotel_name(self) -> str | None:
        """
        Convenience method to get hotel name directly.
        Common access pattern for displaying hotel information.
        """
        return self.hotel_info.hotel_name

    def get_cancellation_policy(self) -> str | None:
        """
        Convenience method to get cancellation policy directly.

        Replaces the complex pattern:
        hotel_pnr.get("room", {}).get("cancellationPolicy")
        """
        return self.room.cancellation_policy

    def get_room_name(self) -> str | None:
        """
        Convenience method to get room name directly.
        Common access pattern for displaying room information.
        """
        return self.room.room_name
