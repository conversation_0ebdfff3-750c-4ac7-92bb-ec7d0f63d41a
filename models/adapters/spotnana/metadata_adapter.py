"""
Metadata-related adapter models for Spotnana Trip API.

Provides type-safe access to additional metadata, airport info, and airline info
without dictionary access.
"""

from functools import cached_property

import structlog

from models.adapters.base import BaseAdapter
from models.api.spotnana.trip.models import AdditionalMetadata, AirlineInfo, AirportInfo

logger = structlog.get_logger(__name__)


class AirportInfoAdapter(BaseAdapter[AirportInfo]):
    """
    Adapter for AirportInfo data.

    Provides type-safe access to airport information including
    codes, names, and location details.
    """

    @property
    def airport_code(self) -> str | None:
        """
        Get the airport code (IATA code).
        Replaces: airport_info.get("airportCode")
        """
        return self._source.airportCode

    @property
    def airport_name(self) -> str | None:
        """
        Get the airport name.
        Replaces: airport_info.get("airportName")
        """
        return self._source.airportName

    @property
    def city_name(self) -> str | None:
        """
        Get the city name.
        Replaces: airport_info.get("cityName")
        """
        return self._source.cityName

    @property
    def country_code(self) -> str | None:
        """
        Get the country code.
        Replaces: airport_info.get("countryCode")
        """
        return self._source.countryCode


class AirlineInfoAdapter(BaseAdapter[AirlineInfo]):
    """
    Adapter for AirlineInfo data.

    Provides type-safe access to airline information including
    codes and names.
    """

    @property
    def airline_code(self) -> str:
        """
        Get the airline code (IATA code).
        Replaces: airline_info.get("airlineCode")
        """
        return self._source.airlineCode

    @property
    def airline_name(self) -> str:
        """
        Get the airline name.
        Replaces: airline_info.get("airlineName")
        """
        return self._source.airlineName


class AdditionalMetadataAdapter(BaseAdapter[AdditionalMetadata]):
    """
    Adapter for AdditionalMetadata from PNR data.

    Provides type-safe access to additional metadata including
    airport information, airline information, and BTA indicators.
    """

    @cached_property
    def airport_info(self) -> list[AirportInfoAdapter]:
        """
        Get airport information as a list of typed adapters.
        Replaces: metadata.get("airportInfo", [])

        Returns:
            List of AirportInfoAdapter instances, empty list if none available
        """
        if not self._source.airportInfo:
            return []
        return [AirportInfoAdapter(info) for info in self._source.airportInfo]

    @cached_property
    def airline_info(self) -> list[AirlineInfoAdapter]:
        """
        Get airline information as a list of typed adapters.
        Replaces: metadata.get("airlineInfo", [])

        Returns:
            List of AirlineInfoAdapter instances, empty list if none available
        """
        if not self._source.airlineInfo:
            return []
        return [AirlineInfoAdapter(info) for info in self._source.airlineInfo]

    @property
    def bta(self) -> str | None:
        """
        Get the BTA (Business Travel Account) indicator.
        Replaces: metadata.get("bta")
        """
        return self._source.bta

    def get_airport_by_code(self, airport_code: str) -> AirportInfoAdapter | None:
        """
        Find airport information by airport code.

        Args:
            airport_code: The IATA airport code to search for

        Returns:
            AirportInfoAdapter if found, None otherwise
        """
        for airport in self.airport_info:
            if airport.airport_code == airport_code:
                return airport
        return None

    def get_airline_by_code(self, airline_code: str) -> AirlineInfoAdapter | None:
        """
        Find airline information by airline code.

        Args:
            airline_code: The IATA airline code to search for

        Returns:
            AirlineInfoAdapter if found, None otherwise
        """
        for airline in self.airline_info:
            if airline.airline_code == airline_code:
                return airline
        return None

    def get_airport_name_by_code(self, airport_code: str) -> str | None:
        """
        Get airport name by airport code.
        Common convenience method for displaying airport names.

        Args:
            airport_code: The IATA airport code

        Returns:
            Airport name if found, None otherwise
        """
        airport = self.get_airport_by_code(airport_code)
        return airport.airport_name if airport else None

    def get_airline_name_by_code(self, airline_code: str) -> str | None:
        """
        Get airline name by airline code.
        Common convenience method for displaying airline names.

        Args:
            airline_code: The IATA airline code

        Returns:
            Airline name if found, None otherwise
        """
        airline = self.get_airline_by_code(airline_code)
        return airline.airline_name if airline else None

    def build_airport_code_to_name_map(self) -> dict[str, str]:
        """
        Build a mapping of airport codes to names.

        Replaces the pattern:
        {info["airportCode"]: info["airportName"] for info in metadata.get("airportInfo", [])}

        Returns:
            Dictionary mapping airport codes to airport names
        """
        return {
            airport.airport_code: airport.airport_name
            for airport in self.airport_info
            if airport.airport_code and airport.airport_name
        }

    def build_airline_code_to_name_map(self) -> dict[str, str]:
        """
        Build a mapping of airline codes to names.

        Replaces the pattern:
        {info["airlineCode"]: info["airlineName"] for info in metadata.get("airlineInfo", [])}

        Returns:
            Dictionary mapping airline codes to airline names
        """
        return {
            airline.airline_code: airline.airline_name
            for airline in self.airline_info
            if airline.airline_code and airline.airline_name
        }
