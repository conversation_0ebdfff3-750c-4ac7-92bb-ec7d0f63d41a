"""
Trip-level adapter models for Spotnana Trip API.

Provides type-safe access patterns for trip responses with zero dictionary returns.
All properties return strongly-typed adapters .
"""

from functools import cached_property
from typing import Any

import structlog

from models.adapters.base import BaseAdapter
from models.adapters.spotnana.air_adapter import AirPnrAdapter
from models.adapters.spotnana.hotel_adapter import HotelPnrAdapter
from models.adapters.spotnana.metadata_adapter import AdditionalMetadataAdapter, AirlineInfoAdapter, AirportInfoAdapter
from models.adapters.spotnana.pnr_adapter import PnrDataAdapter
from models.api.spotnana.trip.models import EntityNonUUIDId, PnrDetailsResponseWithId, TripV3DetailsResponse
from server.utils.enum_helpers import serialize_enum_value

logger = structlog.get_logger(__name__)


class PnrDetailsAdapter(BaseAdapter[PnrDetailsResponseWithId]):
    """
    Adapter for individual PNR details response.

    Provides type-safe access to PNR ID and data.
    """

    @property
    def pnr_id(self) -> str | None:
        """
        Get the PNR ID.
        Replaces: pnr.get("pnrId")
        """
        return self._source.pnrId

    @property
    def data(self) -> PnrDataAdapter | None:
        """
        Get the PNR data as a typed adapter.
        Replaces: pnr.get("data")
        """
        if not self._source.data:
            return None
        return PnrDataAdapter(self._source.data)


class TripDetailsAdapter(BaseAdapter[TripV3DetailsResponse]):
    """
    Adapter for TripV3DetailsResponse providing complete type safety.

    NO dictionary returns - all properties return strongly-typed adapters.
    Replaces all dict[str, Any] patterns with proper type-safe access.
    """

    def _get_primary_pnr_details(self) -> PnrDetailsAdapter | None:
        """
        Helper method to get the first PNR details safely.
        Eliminates code duplication across properties.
        """
        if not self._source.pnrs or not self._source.pnrs[0]:
            return None
        return PnrDetailsAdapter(self._source.pnrs[0])

    def _get_primary_pnr_data(self) -> PnrDataAdapter | None:
        """
        Helper method to get the first PNR's data safely.
        Eliminates code duplication across properties.
        """
        pnr_details = self._get_primary_pnr_details()
        return pnr_details.data if pnr_details else None

    # Basic trip properties

    @property
    def trip_booking_status(self) -> str | None:
        """
        Get the user-facing trip booking status.
        Replaces: trip_details.get("tripBookingStatus")
        """
        return serialize_enum_value(self._source.tripBookingStatus)

    @property
    def trip_status(self) -> str | None:
        """
        Get the internal trip status.
        Replaces: trip_details.get("tripStatus")
        """
        return serialize_enum_value(self._source.tripStatus)

    @property
    def basic_trip_info(self) -> Any:
        """
        Get the basic trip information.
        Replaces: trip_details.get("basicTripInfo")
        """
        return self._source.basicTripInfo

    @property
    def event_summary(self) -> Any | None:
        """
        Get the event summary.
        Replaces: trip_details.get("eventSummary")
        """
        return self._source.eventSummary

    @property
    def trip_payment_info(self) -> Any | None:
        """
        Get the trip payment information.
        Replaces: trip_details.get("tripPaymentInfo")
        """
        return self._source.tripPaymentInfo

    @property
    def additional_info(self) -> Any | None:
        """
        Get the additional trip information.
        Replaces: trip_details.get("additionalInfo")
        """
        return self._source.additionalInfo

    # PNR-related properties (returning typed adapters)

    @property
    def pnr_details_list(self) -> list[PnrDetailsAdapter]:
        """
        Get all PNR details as typed adapters.
        Replaces: trip_details.get("pnrs", [])

        Returns list of PnrDetailsAdapter
        """
        if not self._source.pnrs:
            return []
        return [PnrDetailsAdapter(pnr) for pnr in self._source.pnrs]

    @cached_property
    def primary_pnr_data(self) -> PnrDataAdapter | None:
        """
        Get the first PNR's data as a typed adapter.
        Replaces: trip_details.get("pnrs", [{}])[0].get("data", {})

        Returns PnrDataAdapter
        """
        return self._get_primary_pnr_data()

    @cached_property
    def primary_air_pnr(self) -> AirPnrAdapter | None:
        """
        Get the first PNR's air data as a typed adapter.
        Replaces: trip_details.get("pnrs", [{}])[0].get("data", {}).get("airPnr", {})

        Returns AirPnrAdapter
        """
        pnr_data = self.primary_pnr_data
        return pnr_data.air_pnr if pnr_data else None

    @cached_property
    def primary_hotel_pnr(self) -> HotelPnrAdapter | None:
        """
        Get the first PNR's hotel data as a typed adapter.
        Replaces: trip_details.get("pnrs", [{}])[0].get("data", {}).get("hotelPnr", {})

        Returns HotelPnrAdapter
        """
        pnr_data = self.primary_pnr_data
        return pnr_data.hotel_pnr if pnr_data else None

    @cached_property
    def additional_metadata(self) -> AdditionalMetadataAdapter | None:
        """
        Get additional metadata as a typed adapter.
        Replaces: trip_details.get("pnrs", [{}])[0].get("data", {}).get("additionalMetadata", {})

        Returns AdditionalMetadataAdapter
        """
        pnr_data = self.primary_pnr_data
        return pnr_data.additional_metadata if pnr_data else None

    # Typed list properties (returning typed adapters)

    @cached_property
    def airport_info(self) -> list[AirportInfoAdapter]:
        """
        Get airport information as typed adapters.
        Replaces: trip_details.get("pnrs", [{}])[0].get("data", {}).get("additionalMetadata", {}).get("airportInfo", [])

        Returns list[AirportInfoAdapter]
        """
        metadata = self.additional_metadata
        return metadata.airport_info if metadata else []

    @cached_property
    def airline_info(self) -> list[AirlineInfoAdapter]:
        """
        Get airline information as typed adapters.
        Replaces: trip_details.get("pnrs", [{}])[0].get("data", {}).get("additionalMetadata", {}).get("airlineInfo", [])

        Returns list[AirlineInfoAdapter]
        """
        metadata = self.additional_metadata
        return metadata.airline_info if metadata else []

    # Convenience methods for common access patterns

    def get_hotel_cancellation_policy(self) -> str | None:
        """
        Extract hotel cancellation policy using typed adapters.

        Replaces the complex pattern:
        trip_details.get("pnrs", [{}])[0].get("data", {}).get("hotelPnr", {}).get("room", {}).get("cancellationPolicy")
        """
        try:
            hotel_pnr = self.primary_hotel_pnr
            if not hotel_pnr:
                return None
            return hotel_pnr.get_cancellation_policy()

        except Exception as e:
            logger.warning(
                "Failed to extract hotel cancellation policy",
                error=str(e),
                trip_id=self.get_trip_id() or "unknown",
            )
            return None

    def get_vendor_confirmation_number(self) -> str | None:
        """
        Extract vendor confirmation number using typed adapters.

        Replaces the complex pattern used in FlightSearchTools.get_vendor_confirmation_number():
        trip_details.get("pnrs", [])[0].get("data", {}).get("airPnr", {}).get("legs", [])[0].get("flights", [])[0].get("vendorConfirmationNumber")
        """
        try:
            air_pnr = self.primary_air_pnr
            if not air_pnr:
                return None
            return air_pnr.get_vendor_confirmation_number()

        except Exception as e:
            logger.warning(
                "Failed to extract vendor confirmation number",
                error=str(e),
                trip_id=self.get_trip_id() or "unknown",
            )
            return None

    def get_airport_name_by_code(self, airport_code: str) -> str | None:
        """
        Get airport name by code using typed adapters.

        Replaces manual dictionary lookup patterns.
        """
        metadata = self.additional_metadata
        if not metadata:
            return None
        return metadata.get_airport_name_by_code(airport_code)

    def get_airline_name_by_code(self, airline_code: str) -> str | None:
        """
        Get airline name by code using typed adapters.

        Replaces manual dictionary lookup patterns.
        """
        metadata = self.additional_metadata
        if not metadata:
            return None
        return metadata.get_airline_name_by_code(airline_code)

    def build_airport_code_to_name_map(self) -> dict[str, str]:
        """
        Build a mapping of airport codes to names using typed adapters.

        Replaces:
        {info["airportCode"]: info["airportName"] for info in trip_details.get("pnrs", [{}])[0]...}
        """
        metadata = self.additional_metadata
        if not metadata:
            return {}
        return metadata.build_airport_code_to_name_map()

    def build_airline_code_to_name_map(self) -> dict[str, str]:
        """
        Build a mapping of airline codes to names using typed adapters.

        Replaces:
        {info["airlineCode"]: info["airlineName"] for info in trip_details.get("pnrs", [{}])[0]...}
        """
        metadata = self.additional_metadata
        if not metadata:
            return {}
        return metadata.build_airline_code_to_name_map()

    def get_trip_id(self) -> str | None:
        """
        Get the trip ID from basic trip info.
        """
        if not self.basic_trip_info:
            return None
        # Safe access to tripId field from basic trip info
        return self.basic_trip_info.tripId if hasattr(self.basic_trip_info, "tripId") else None

    # Backward compatibility methods (only for transition period)


class TripCreateAdapter(BaseAdapter[EntityNonUUIDId]):
    """
    Adapter for EntityNonUUIDId (trip creation response).

    Provides type-safe access for trip creation operations.
    """

    @property
    def trip_id(self) -> str | None:
        """
        Get the created trip ID.
        Replaces: trip_response.get("id")
        """
        return self._source.id
