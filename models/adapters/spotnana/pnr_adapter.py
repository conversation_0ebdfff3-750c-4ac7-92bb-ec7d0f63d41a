"""
PNR-related adapter models for Spotnana Trip API.

Provides type-safe access to PNR data without dictionary access.
"""

from typing import Any

import structlog

from models.adapters.base import BaseAdapter
from models.adapters.spotnana.air_adapter import AirPnrAdapter
from models.adapters.spotnana.hotel_adapter import HotelPnrAdapter
from models.adapters.spotnana.metadata_adapter import AdditionalMetadataAdapter
from models.api.spotnana.trip.models import PnrData
from server.utils.enum_helpers import serialize_enum_value

logger = structlog.get_logger(__name__)


class PnrDataAdapter(BaseAdapter[PnrData]):
    """
    Adapter for PnrData.

    Provides type-safe access to PNR data including air, hotel,
    car, rail bookings and additional metadata.
    """

    @property
    def version(self) -> int | None:
        """
        Get the PNR version.
        Replaces: pnr_data.get("version")
        """
        return self._source.version

    @property
    def created_via(self) -> str | None:
        """
        Get how the PNR was created.
        Replaces: pnr_data.get("createdVia")
        """
        return serialize_enum_value(self._source.createdVia)

    @property
    def initial_version_created_via(self) -> str | None:
        """
        Get how the initial version was created.
        Replaces: pnr_data.get("initialVersionCreatedVia")
        """
        return serialize_enum_value(self._source.initialVersionCreatedVia)

    @property
    def invoice_delayed_booking(self) -> bool | None:
        """
        Get whether billing is delayed.
        Replaces: pnr_data.get("invoiceDelayedBooking")
        """
        return self._source.invoiceDelayedBooking

    @property
    def travelers(self) -> list[Any]:
        """
        Get the travelers.
        Replaces: pnr_data.get("travelers", [])
        """
        return list(self._source.travelers) if self._source.travelers else []

    @property
    def pnr_travelers(self) -> list[Any]:
        """
        Get the PNR travelers.
        Replaces: pnr_data.get("pnrTravelers", [])
        """
        return list(self._source.pnrTravelers) if self._source.pnrTravelers else []

    @property
    def air_pnr(self) -> AirPnrAdapter | None:
        """
        Get the air PNR as a typed adapter.
        Replaces: pnr_data.get("airPnr")
        """
        if not self._source.airPnr:
            return None
        return AirPnrAdapter(self._source.airPnr)

    @property
    def hotel_pnr(self) -> HotelPnrAdapter | None:
        """
        Get the hotel PNR as a typed adapter.
        Replaces: pnr_data.get("hotelPnr")
        """
        if not self._source.hotelPnr:
            return None
        return HotelPnrAdapter(self._source.hotelPnr)

    @property
    def car_pnr(self) -> Any | None:
        """
        Get the car PNR.
        Replaces: pnr_data.get("carPnr")
        """
        return self._source.carPnr

    @property
    def rail_pnr(self) -> Any | None:
        """
        Get the rail PNR.
        Replaces: pnr_data.get("railPnr")
        """
        return self._source.railPnr

    @property
    def limo_pnr(self) -> Any | None:
        """
        Get the limo PNR.
        Replaces: pnr_data.get("limoPnr")
        """
        return self._source.limoPnr

    @property
    def misc_pnr(self) -> Any | None:
        """
        Get the miscellaneous PNR.
        Replaces: pnr_data.get("miscPnr")
        """
        return self._source.miscPnr

    @property
    def additional_metadata(self) -> AdditionalMetadataAdapter | None:
        """
        Get the additional metadata as a typed adapter.
        Replaces: pnr_data.get("additionalMetadata")
        """
        if not self._source.additionalMetadata:
            return None
        return AdditionalMetadataAdapter(self._source.additionalMetadata)

    @property
    def policy_info(self) -> Any | None:
        """
        Get the policy information.
        Replaces: pnr_data.get("policyInfo")
        """
        return self._source.policyInfo

    @property
    def source_info(self) -> Any | None:
        """
        Get the source information.
        Replaces: pnr_data.get("sourceInfo")
        """
        return self._source.sourceInfo

    @property
    def pre_book_answers(self) -> Any | None:
        """
        Get the pre-book answers.
        Replaces: pnr_data.get("preBookAnswers")
        """
        return self._source.preBookAnswers

    def has_air_booking(self) -> bool:
        """
        Check if this PNR has an air booking.
        """
        return self.air_pnr is not None

    def has_hotel_booking(self) -> bool:
        """
        Check if this PNR has a hotel booking.
        """
        return self.hotel_pnr is not None

    def has_car_booking(self) -> bool:
        """
        Check if this PNR has a car booking.
        """
        return self.car_pnr is not None

    def has_rail_booking(self) -> bool:
        """
        Check if this PNR has a rail booking.
        """
        return self.rail_pnr is not None

    def get_booking_types(self) -> list[str]:
        """
        Get a list of booking types present in this PNR.

        Returns:
            List of booking types (e.g., ["AIR", "HOTEL"])
        """
        booking_types = []
        if self.has_air_booking():
            booking_types.append("AIR")
        if self.has_hotel_booking():
            booking_types.append("HOTEL")
        if self.has_car_booking():
            booking_types.append("CAR")
        if self.has_rail_booking():
            booking_types.append("RAIL")
        if self.limo_pnr:
            booking_types.append("LIMO")
        if self.misc_pnr:
            booking_types.append("MISC")
        return booking_types
