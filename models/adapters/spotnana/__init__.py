"""
Spotnana API adapter models.

This package contains adapter classes for Spotnana API responses,
providing type-safe access patterns without dictionary access.
"""

# Import all adapters for easy access
from .air_adapter import AirPnrAdapter, FlightAdapter, LegAdapter
from .hotel_adapter import HotelInfoAdapter, HotelPnrAdapter, RoomAdapter
from .metadata_adapter import (
    AdditionalMetadataAdapter,
    AirlineInfoAdapter,
    AirportInfoAdapter,
)
from .pnr_adapter import PnrDataAdapter
from .trip_adapter import Trip<PERSON>reate<PERSON>dapter, TripDetailsAdapter

__all__ = [
    # Air adapters
    "AirPnrAdapter",
    "FlightAdapter",
    "LegAdapter",
    # Hotel adapters
    "HotelPnrAdapter",
    "RoomAdapter",
    "HotelInfoAdapter",
    # Metadata adapters
    "AdditionalMetadataAdapter",
    "AirportInfoAdapter",
    "AirlineInfoAdapter",
    # PNR adapters
    "PnrDataAdapter",
    # Trip adapters
    "TripDetailsAdapter",
    "TripCreateAdapter",
]
