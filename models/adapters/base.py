"""
Base adapter classes for external API integration.

Adapters bridge between external API Pydantic models and internal application needs,
providing backward compatibility and meaningful property access patterns.
"""

from abc import ABC
from typing import Generic, TypeVar

import structlog
from pydantic import BaseModel

logger = structlog.get_logger(__name__)

T = TypeVar("T", bound=BaseModel)


class BaseAdapter(ABC, Generic[T]):
    """
    Abstract base adapter for external API Pydantic models.

    Adapters provide:
    1. Backward compatibility with existing dictionary access patterns
    2. Meaningful property-based access to complex nested data
    3. Helper methods for common access patterns
    4. Safe serialization for different use cases
    """

    def __init__(self, source_model: T) -> None:
        """
        Initialize adapter with source Pydantic model.

        Args:
            source_model: The source Pydantic model to wrap
        """
        self._source = source_model

    @property
    def source_model(self) -> T:
        """Access the underlying source model."""
        return self._source
