"""
Utility functions for adapter classes.

This module provides shared utilities for adapter implementations,
particularly for safe data access and common patterns.
"""

from typing import Any

import structlog

logger = structlog.get_logger(__name__)


def safe_nested_get(data: Any, path: str, default: Any = None, separator: str = ".") -> Any:
    """
    Safely extract nested values from dictionary/object structures.

    This utility function handles complex nested access patterns safely,
    with proper error handling and logging.

    Args:
        data: Source data (dict, object, or list)
        path: Dot-separated path (e.g., "pnrs.0.data.airPnr")
        default: Default value if path not found
        separator: Path separator (default: ".")

    Returns:
        Value at path or default

    Example:
        >>> data = {"user": {"profile": {"name": "<PERSON>"}}}
        >>> safe_nested_get(data, "user.profile.name")
        "John"
        >>> safe_nested_get(data, "user.address.city", "Unknown")
        "Unknown"
        >>> safe_nested_get([{"id": 1}], "0.id")
        1
    """
    try:
        keys = path.split(separator)
        current = data

        for key in keys:
            if current is None:
                return default

            # Handle list indices
            if key.isdigit() and isinstance(current, (list, tuple)):
                index = int(key)
                if 0 <= index < len(current):
                    current = current[index]
                else:
                    return default

            # Handle dictionary access
            elif isinstance(current, dict):
                current = current.get(key, default)
                if current is default:
                    return default

            # Handle object attribute access
            elif hasattr(current, key):
                current = getattr(current, key)

            else:
                return default

        return current

    except Exception as e:
        logger.warning("Failed to extract nested path", path=path, error=str(e), data_type=type(data).__name__)
        return default
