"""
Configuration for DTO models and feature flags.

Manages the gradual rollout of DTO implementation with backward compatibility.
"""

import os
from enum import Enum


class DTOFeatureFlag(str, Enum):
    """Feature flags for DTO implementation rollout."""

    FLIGHTS_EXTERNAL = "dto_flights_external"
    FLIGHTS_DOMAIN = "dto_flights_domain"
    FLIGHTS_API = "dto_flights_api"
    HOTELS_EXTERNAL = "dto_hotels_external"
    HOTELS_DOMAIN = "dto_hotels_domain"
    HOTELS_API = "dto_hotels_api"
    AUTH_EXTERNAL = "dto_auth_external"
    AUTH_DOMAIN = "dto_auth_domain"
    AUTH_API = "dto_auth_api"
    WEBHOOKS = "dto_webhooks"
    CONVERTERS = "dto_converters"


class DTOConfig:
    """
    Configuration class for DTO models with environment-based feature flags.

    Allows gradual rollout of DTO implementation by enabling features
    independently through environment variables.
    """

    def __init__(self):
        """Initialize configuration with environment variables."""
        self._feature_flags: dict[str, bool] = {
            # Flight DTO features
            DTOFeatureFlag.FLIGHTS_EXTERNAL: self._get_env_bool("OTTO_DTO_FLIGHTS_EXTERNAL", False),
            DTOFeatureFlag.FLIGHTS_DOMAIN: self._get_env_bool("OTTO_DTO_FLIGHTS_DOMAIN", False),
            DTOFeatureFlag.FLIGHTS_API: self._get_env_bool("OTTO_DTO_FLIGHTS_API", False),
            # Hotel DTO features
            DTOFeatureFlag.HOTELS_EXTERNAL: self._get_env_bool("OTTO_DTO_HOTELS_EXTERNAL", False),
            DTOFeatureFlag.HOTELS_DOMAIN: self._get_env_bool("OTTO_DTO_HOTELS_DOMAIN", False),
            DTOFeatureFlag.HOTELS_API: self._get_env_bool("OTTO_DTO_HOTELS_API", False),
            # Auth DTO features
            DTOFeatureFlag.AUTH_EXTERNAL: self._get_env_bool("OTTO_DTO_AUTH_EXTERNAL", False),
            DTOFeatureFlag.AUTH_DOMAIN: self._get_env_bool("OTTO_DTO_AUTH_DOMAIN", False),
            DTOFeatureFlag.AUTH_API: self._get_env_bool("OTTO_DTO_AUTH_API", False),
            # Cross-cutting features
            DTOFeatureFlag.WEBHOOKS: self._get_env_bool("OTTO_DTO_WEBHOOKS", False),
            DTOFeatureFlag.CONVERTERS: self._get_env_bool("OTTO_DTO_CONVERTERS", False),
        }

        # Master switches for development
        self.enable_all_dto = self._get_env_bool("OTTO_DTO_ENABLE_ALL", False)
        self.disable_all_dto = self._get_env_bool("OTTO_DTO_DISABLE_ALL", False)

        # Performance and monitoring
        self.dto_performance_monitoring = self._get_env_bool("OTTO_DTO_PERFORMANCE_MONITORING", True)
        self.dto_error_logging = self._get_env_bool("OTTO_DTO_ERROR_LOGGING", True)
        self.dto_fallback_enabled = self._get_env_bool("OTTO_DTO_FALLBACK_ENABLED", True)

        # Validation settings
        self.strict_validation = self._get_env_bool("OTTO_DTO_STRICT_VALIDATION", False)
        self.validation_warnings = self._get_env_bool("OTTO_DTO_VALIDATION_WARNINGS", True)

    def is_enabled(self, feature: DTOFeatureFlag) -> bool:
        """
        Check if a specific DTO feature is enabled.

        Args:
            feature: The DTO feature flag to check

        Returns:
            True if the feature is enabled, False otherwise
        """
        # Master switches override individual flags
        if self.disable_all_dto:
            return False
        if self.enable_all_dto:
            return True

        return self._feature_flags.get(feature, False)

    def enable_feature(self, feature: DTOFeatureFlag) -> None:
        """Enable a specific DTO feature."""
        self._feature_flags[feature] = True

    def disable_feature(self, feature: DTOFeatureFlag) -> None:
        """Disable a specific DTO feature."""
        self._feature_flags[feature] = False

    def get_enabled_features(self) -> dict[str, bool]:
        """Get all currently enabled DTO features."""
        if self.disable_all_dto:
            return {flag: False for flag in self._feature_flags}
        if self.enable_all_dto:
            return {flag: True for flag in self._feature_flags}
        return self._feature_flags.copy()

    @staticmethod
    def _get_env_bool(env_var: str, default: bool = False) -> bool:
        """
        Get boolean value from environment variable.

        Args:
            env_var: Environment variable name
            default: Default value if env var not set

        Returns:
            Boolean value from environment or default
        """
        value = os.getenv(env_var, "").lower()
        if value in ("true", "1", "yes", "on", "enabled"):
            return True
        elif value in ("false", "0", "no", "off", "disabled"):
            return False
        return default


# Global configuration instance
dto_config = DTOConfig()


def use_dto_for_flights() -> bool:
    """Check if DTO models should be used for flight processing."""
    return dto_config.is_enabled(DTOFeatureFlag.FLIGHTS_EXTERNAL)


def use_dto_for_hotels() -> bool:
    """Check if DTO models should be used for hotel processing."""
    return dto_config.is_enabled(DTOFeatureFlag.HOTELS_EXTERNAL)


def use_dto_for_auth() -> bool:
    """Check if DTO models should be used for authentication."""
    return dto_config.is_enabled(DTOFeatureFlag.AUTH_EXTERNAL)


def use_dto_for_webhooks() -> bool:
    """Check if DTO models should be used for webhook processing."""
    return dto_config.is_enabled(DTOFeatureFlag.WEBHOOKS)


def use_dto_converters() -> bool:
    """Check if DTO converters should be used."""
    return dto_config.is_enabled(DTOFeatureFlag.CONVERTERS)


# Validation configuration
class ValidationConfig:
    """Configuration for DTO validation behavior."""

    @property
    def strict_validation(self) -> bool:
        """Whether to use strict validation that raises on any error."""
        return dto_config.strict_validation

    @property
    def validation_warnings(self) -> bool:
        """Whether to log warnings for validation issues."""
        return dto_config.validation_warnings

    @property
    def error_logging(self) -> bool:
        """Whether to log DTO processing errors."""
        return dto_config.dto_error_logging

    @property
    def fallback_enabled(self) -> bool:
        """Whether to fall back to dictionary processing on DTO errors."""
        return dto_config.dto_fallback_enabled


# Global validation configuration
validation_config = ValidationConfig()
