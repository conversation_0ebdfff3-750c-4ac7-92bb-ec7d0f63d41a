# type: ignore
# pyright: reportUnknownVariableType=false
# pyright: reportUnknownArgumentType=false

"""
Base DTO classes for Otto Travel Platform

Provides foundational classes for the three-layer DTO architecture.

TODO: Suppress reportAny for now before we migrate logger
"""

from abc import ABC, abstractmethod
from collections.abc import Callable, Mapping
from datetime import datetime, timezone
from typing import Any, ClassVar, Generic, TypeVar

import structlog
from pydantic import BaseModel, ConfigDict, Field

logger = structlog.get_logger(__name__)

T = TypeVar("T")
U = TypeVar("U")


class BaseExternalModel(BaseModel):
    """
    Base class for all external API response models.

    These models represent raw data from external APIs (Spotnana, Booking.com, etc.)
    with minimal transformation, focusing on validation and type safety.
    """

    model_config: ClassVar[ConfigDict] = ConfigDict(
        # Allow field aliases for API field name mapping
        populate_by_name=True,
        # Validate field assignments for safety
        validate_assignment=True,
        # Allow extra fields for forward compatibility
        extra="allow",
        # Use enum values rather than enum objects for serialization
        use_enum_values=True,
        # Validate default values
        validate_default=True,
    )

    def model_dump_safe(self) -> dict[str, Any]:
        """Safe model dump that handles serialization errors gracefully."""
        try:
            return self.model_dump(by_alias=True, exclude_none=True)
        except Exception as e:
            logger.error("Failed to serialize external model", model_type=self.__class__.__name__, error=str(e))
            return {"error": f"Serialization failed: {str(e)}"}


class BaseDomainModel(BaseModel):
    """
    Base class for all domain/business logic models.

    These models represent unified business entities that may combine data
    from multiple external sources with business logic and validation.
    """

    model_config: ClassVar[ConfigDict] = ConfigDict(
        # Strict validation for business logic models
        validate_assignment=True,
        # No extra fields allowed for domain models
        extra="forbid",
        # Use enum values for consistency
        use_enum_values=True,
        # Validate default values
        validate_default=True,
        # Arbitrary types allowed for complex business objects
        arbitrary_types_allowed=True,
    )

    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime | None = None

    def mark_updated(self) -> None:
        """Mark the model as updated with current timestamp."""
        self.updated_at = datetime.now(timezone.utc)

    def validate_business_rules(self) -> list[str]:
        """
        Validate business rules specific to this domain model.

        Returns:
            List of validation error messages, empty if valid.
        """
        return []


class BaseApiModel(BaseModel):
    """
    Base class for all API response models.

    These models are optimized for frontend consumption with formatted
    fields, UI-specific data, and minimal nesting.
    """

    model_config: ClassVar[ConfigDict] = ConfigDict(
        # Validate assignment for API safety
        validate_assignment=True,
        # No extra fields for clean API responses
        extra="forbid",
        # Use enum values for JSON compatibility
        use_enum_values=True,
        # Validate defaults
        validate_default=True,
    )

    def model_dump_for_api(self) -> dict[str, Any]:
        """
        Dump model for API response with frontend-optimized formatting.

        Returns:
            Dictionary optimized for frontend consumption.
        """
        return self.model_dump(exclude_none=True, by_alias=True)


class BaseConverter(ABC, Generic[T, U]):
    """
    Abstract base class for data converters between DTO layers.

    Provides common error handling, logging, and validation patterns
    for converting between external, domain, and API models.
    """

    def safe_convert(
        self,
        data: Any,
        converter_func: Callable[[Any], U],
        fallback: U | None = None,
        error_context: dict[str, Any] | None = None,
    ) -> U | None:
        """
        Safely convert data using the provided converter function.

        Args:
            data: Input data to convert
            converter_func: Function to perform the conversion
            fallback: Fallback value if conversion fails
            error_context: Additional context for error logging

        Returns:
            Converted data or fallback value
        """
        try:
            if data is None:
                return fallback
            return converter_func(data)
        except Exception as e:
            logger.error(
                "Data conversion failed",
                converter=converter_func.__name__,
                error=str(e),
                data_type=type(data).__name__,
                context=error_context or {},
            )
            return fallback

    @staticmethod
    def safe_get(data: Mapping[str, Any], key_path: str, default: object = None, required: bool = False) -> object:
        """
        Safely extract nested dictionary values using dot notation.

        Args:
            data: Source dictionary
            key_path: Dot-separated key path (e.g., "user.profile.name")
            default: Default value if key not found
            required: Whether to raise exception if key missing

        Returns:
            Value at key path or default

        Raises:
            KeyError: If required=True and key path not found
        """
        try:
            keys = key_path.split(".")
            current: Any = data

            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                elif isinstance(current, list) and key.isdigit():
                    index = int(key)
                    if 0 <= index < len(current):
                        current = current[index]
                    else:
                        raise KeyError(f"List index {index} out of range")
                else:
                    if required:
                        raise KeyError(f"Required key path '{key_path}' not found")
                    return default

            return current

        except Exception as e:
            if required:
                raise KeyError(f"Failed to extract required path '{key_path}': {str(e)}")
            logger.warning("Failed to extract optional key path", key_path=key_path, error=str(e))
            return default

    @abstractmethod
    def convert(self, source: T) -> U:
        """
        Convert from source model to target model.

        Args:
            source: Source model instance

        Returns:
            Converted target model instance
        """
        pass
