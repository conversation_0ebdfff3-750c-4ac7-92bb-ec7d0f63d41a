default_language_version:
  python: python3

exclude: |
  (?x)(
      ^baml_src/
      ^data/
      ^models/api/spotnana/.*/models\.py$
  )
repos:
  - repo: local
    hooks:
      - id: run-just-b
        name: Run `just b` for prepare pyright
        entry: just b
        language: system
        types: [python]
        pass_filenames: false
  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.11.5
    hooks:
      # Run the linter.
      - id: ruff
        args: [--fix]
      # Run the formatter.
      - id: ruff-format
  - repo: https://github.com/RobertCraigie/pyright-python
    rev: v1.1.394
    hooks:
      - id: pyright
