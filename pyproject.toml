[project]
name = "otto-server"
version = "0.1.0"
description = "awesome otto travel agent"
readme = "README.md"
requires-python = "===3.11.10"
dependencies = [
    "aenum==3.1.15",
    "aiofiles==24.1.0",
    "aiohttp==3.9.5",
    "aiosignal==1.3.1",
    "airportsdata==20240415",
    "alembic==1.13.1",
    "annotated-types==0.7.0",
    "async-generator==1.10",
    "async-timeout==4.0.3",
    "asyncpg==0.29.0",
    "attrs==23.2.0",
    "authlib==1.3.0",
    "baml-py==0.201.0",
    "boto3==1.34.113",
    "botocore==1.34.113",
    "cachetools==5.3.3",
    "certifi==2024.2.2",
    "cffi==1.16.0",
    "charset-normalizer==3.3.2",
    "click==8.1.7",
    "cryptography==42.0.7",
    "dataclasses-json==0.6.6",
    "deepgram-sdk==3.7.3",
    "deprecation==2.1.0",
    "distro==1.9.0",
    "dnspython==2.6.1",
    "email-validator==2.1.1",
    "fastapi-cli==0.0.4",
    "fastapi==0.111.0",
    "frozenlist==1.4.1",
    "google-api-core==2.19.1",
    "google-api-python-client==2.135.0",
    "google-auth==2.29.0",
    "google-auth-httplib2==0.2.0",
    "google-auth-oauthlib==1.2.0",
    "googleapis-common-protos==1.63.2",
    "greenlet==3.0.3",
    "h11==0.14.0",
    "httpcore==1.0.5",
    "httplib2==0.22.0",
    "httptools==0.6.1",
    "httpx==0.27.0",
    "idna==3.7",
    "jinja2==3.1.4",
    "jmespath==1.0.1",
    "jsonpatch==1.33",
    "jsonpointer==2.4",
    "mako==1.3.5",
    "markdown-it-py==3.0.0",
    "markupsafe==2.1.5",
    "marshmallow==3.21.2",
    "mdurl==0.1.2",
    "motor==3.4.0",
    "msgpack==1.1.0",
    "multidict==6.0.5",
    "mypy-extensions==1.0.0",
    "numpy==1.26.4",
    "oauthlib==3.2.2",
    "openai==1.33.0",
    "orjson==3.10.3",
    "outcome==1.3.0.post0",
    "packaging==23.2",
    "proto-plus==1.24.0",
    "protobuf==5.27.2",
    "pyasn1==0.6.0",
    "pyasn1-modules==0.4.0",
    "pycparser==2.22",
    "pygments==2.18.0",
    "pyjwt==2.8.0",
    "pymongo==4.7.2",
    "pyparsing==3.1.2",
    "python-dateutil==2.9.0.post0",
    "python-dotenv==1.0.1",
    "python-multipart==0.0.9",
    "pyyaml==6.0.1",
    "regex==2024.5.15",
    "requests==2.32.2",
    "requests-oauthlib==2.0.0",
    "requests-toolbelt==1.0.0",
    "rich==13.7.1",
    "rsa==4.9",
    "s3transfer==0.10.1",
    "shellingham==1.5.4",
    "six==1.16.0",
    "sniffio==1.3.1",
    "sortedcontainers==2.4.0",
    "sqlalchemy==2.0.31",
    "structlog==24.4.0",
    "tenacity==8.3.0",
    "tiktoken==0.7.0",
    "tqdm==4.66.4",
    "trio==0.25.1",
    "typing-inspect==0.9.0",
    "typing-extensions==4.12.0",
    "ujson==5.10.0",
    "uritemplate==4.1.1",
    "urllib3==2.2.1",
    "uuid6==2024.1.12",
    "uvicorn==0.30.0",
    "uvloop==0.19.0",
    "watchfiles==0.22.0",
    "websockets==12.0",
    "yarl==1.9.4",
    "msal==1.31.1",
    "mixpanel>=4.10.1",
    "apscheduler>=3.11.0",
    "psycopg2-binary>=2.9.10",
    "mem0ai==0.1.54",
    "sentry-sdk[fastapi]>=2.20.0",
    "aiohttp-retry>=2.9.1",
    "langgraph>=0.2.69",
    "langgraph-checkpoint>=2.0.10",
    "langsmith>=0.1.147",
    "langchain>=0.2.17",
    "langchain-community>=0.2.19",
    "langchain-core>=0.2.43",
    "langchain-openai>=0.1.20",
    "langchain-text-splitters>=0.2.4",
    "redis>=5.2.1",
    "temporalio>=1.10.0",
    "rapidfuzz>=3.12.2",
    "prometheus-fastapi-instrumentator>=7.1.0",
    "mcp>=1.6.0",
    "anyio>=4.9.0",
    "starlette>=0.37.2",
    "sse-starlette>=2.1.3",
    "pydantic>=2.7.3",
    "pydantic-core>=2.18.4",
    "pydantic-settings>=2.8.1",
    "typer>=0.12.4",
    "emoji>=2.14.1",
    "markdown2>=2.5.3",
    "bs4>=0.0.2",
    "langfuse>=3.0.2",
    "weasyprint>=65.1",
    "datamodel-code-generator>=0.31.2",
]

[tool.ruff]
line-length = 120
exclude = [
    "baml_client",
    "baml_src",
    "venv",
    ".venv",
    "data",
    "models/api/spotnana/*/models.py",
]
lint.ignore = [
    "E712",
]
lint.extend-select = ["I", "F823", "F821"]

[tool.ruff.lint.flake8-bugbear]
extend-immutable-calls = [
    "Depends",
    "fastapi.Depends",
    "fastapi.params.Depends",
]

[tool.pyright]
exclude = [
    "baml_client",
    "baml_src",
    "venv",
    ".venv",
    "data",
    "virtual_travel_agent_tests",
    "scripts",
    "server/database/migrations",
    ".pytype/",
    "models/api/spotnana/*/models.py",
]
venvPath = "."
venv = ".venv"

reportUnusedCoroutine = true

[dependency-groups]
dev = [
    "pre-commit==4.0.1",
    "pytest-rerunfailures>=15.0",
    "pytest==8.3.3",
    "pytest-asyncio==0.24.0",
    "testing.postgresql>=1.3.0",
    "ruff>=0.8.3",
    "pyright>=1.1",
    "matplotlib",
    "mcp[cli]>=1.6.0",
    "airportsdata>=20240415",
    "datamodel-code-generator>=0.31.2",
    "ipykernel>=6.29.5",
]

[tool.pytest.ini_options]
asyncio_mode = "strict"
asyncio_default_fixture_loop_scope = "function"
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
markers = ["manual: marks tests that need to be run manually"]
testpaths = ["tests", "virtual_travel_agent_tests"]
pythonpath = ["."]

[tool.basedpyright]
typeCheckingMode = "standard"
