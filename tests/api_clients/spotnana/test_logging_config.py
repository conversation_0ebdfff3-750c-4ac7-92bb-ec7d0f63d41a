"""Unit tests for Spotnana logging configuration."""

from pathlib import Path
from unittest.mock import Async<PERSON>ock, MagicMock, patch

import pytest

from server.api_clients.spotnana.logging_config import (
    SpotnanaLoggingConfig,
    _safe_int_conversion,
)


class TestSafeIntConversion:
    """Test suite for _safe_int_conversion function."""

    def test_int_input(self):
        """Test integer input returns same value."""
        assert _safe_int_conversion(42) == 42
        assert _safe_int_conversion(0) == 0
        assert _safe_int_conversion(-1) == -1

    def test_string_digit_input(self):
        """Test string digit input converts to int."""
        assert _safe_int_conversion("123") == 123
        assert _safe_int_conversion("0") == 0
        assert _safe_int_conversion("999") == 999

    def test_string_non_digit_input(self):
        """Test string non-digit input returns 0."""
        assert _safe_int_conversion("unknown") == 0
        assert _safe_int_conversion("abc") == 0
        assert _safe_int_conversion("12.34") == 0
        assert _safe_int_conversion("") == 0

    def test_other_types(self):
        """Test other types return 0."""
        assert _safe_int_conversion(None) == 0
        assert _safe_int_conversion([]) == 0
        assert _safe_int_conversion({}) == 0
        assert _safe_int_conversion(12.34) == 0


class TestSpotnanaLoggingConfig:
    """Test suite for SpotnanaLoggingConfig class."""

    def test_parse_url_components_basic(self):
        """Test basic URL parsing."""
        namespace, endpoint = SpotnanaLoggingConfig.parse_url_components("/v2/air/search")
        assert namespace == "air"
        assert endpoint == "search"

    def test_parse_url_components_nested(self):
        """Test nested URL parsing."""
        namespace, endpoint = SpotnanaLoggingConfig.parse_url_components("/v1/user/profile/update")
        assert namespace == "user"
        assert endpoint == "profile_update"

    def test_parse_url_components_no_version(self):
        """Test URL without version returns unknown."""
        namespace, endpoint = SpotnanaLoggingConfig.parse_url_components("/api/search")
        assert namespace == "unknown"
        assert endpoint == "unknown"

    def test_parse_url_components_single_part(self):
        """Test URL with single part after version."""
        namespace, endpoint = SpotnanaLoggingConfig.parse_url_components("/v2/air")
        assert namespace == "air"
        assert endpoint == "unknown"

    def test_parse_url_components_empty_path(self):
        """Test empty path returns unknown."""
        namespace, endpoint = SpotnanaLoggingConfig.parse_url_components("/v2/")
        assert namespace == "unknown"
        assert endpoint == "unknown"

    @patch("server.api_clients.spotnana.logging_config.settings")
    @patch("server.api_clients.spotnana.logging_config.uuid4")
    def test_generate_log_file_path(self, mock_uuid, mock_settings):
        """Test log file path generation."""
        mock_settings.SPOTNANA_LOG_ROOT_DIR = "/logs"
        mock_uuid_instance = MagicMock()
        mock_uuid_instance.__str__ = MagicMock(return_value="12345678-1234-5678-9012-123456789012")
        mock_uuid.return_value = mock_uuid_instance

        with patch("server.api_clients.spotnana.logging_config.datetime") as mock_datetime:
            mock_datetime.now.return_value.strftime.return_value = "20240101_120000"

            path = SpotnanaLoggingConfig.generate_log_file_path("/v2/air/search", "request")

            expected_path = Path("/logs/air/search/air_search_request_20240101_120000_1234.json")
            assert path == expected_path

    @patch("server.api_clients.spotnana.logging_config.structlog")
    def test_extract_context_variables(self, mock_structlog):
        """Test context variable extraction."""
        mock_structlog.contextvars.get_contextvars.return_value = {"trip_id": "123", "user_id": "456"}

        with patch("server.api_clients.spotnana.logging_config.datetime") as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T12:00:00"

            context = SpotnanaLoggingConfig.extract_context_variables()

            assert context["trip_id"] == "123"
            assert context["user_id"] == "456"
            assert context["trip_id_int"] == 123
            assert context["user_id_int"] == 456
            assert context["timestamp"] == "2024-01-01T12:00:00"

    @patch("server.api_clients.spotnana.logging_config.structlog")
    def test_extract_context_variables_unknown_values(self, mock_structlog):
        """Test context variable extraction with unknown values."""
        mock_structlog.contextvars.get_contextvars.return_value = {}

        context = SpotnanaLoggingConfig.extract_context_variables()

        assert context["trip_id"] == "unknown"
        assert context["user_id"] == "unknown"
        assert context["trip_id_int"] == 0
        assert context["user_id_int"] == 0

    def test_extract_method_from_url(self):
        """Test method extraction from URL."""
        method = SpotnanaLoggingConfig.extract_method_from_url("/v2/air/search")
        assert method == "AIR/SEARCH"

        method = SpotnanaLoggingConfig.extract_method_from_url("/v1/user/profile")
        assert method == "USER/PROFILE"

    def test_extract_method_from_url_no_match(self):
        """Test method extraction returns empty string for no match."""
        method = SpotnanaLoggingConfig.extract_method_from_url("/api/search")
        assert method == ""

        method = SpotnanaLoggingConfig.extract_method_from_url("")
        assert method == ""

        method = SpotnanaLoggingConfig.extract_method_from_url(None)
        assert method == ""

    @pytest.mark.asyncio
    async def test_write_log_file_success(self):
        """Test successful log file writing."""
        mock_file_handle = AsyncMock()
        mock_context_manager = AsyncMock()
        mock_context_manager.__aenter__.return_value = mock_file_handle

        with patch("aiofiles.open", return_value=mock_context_manager):
            with patch("pathlib.Path.mkdir") as mock_mkdir:
                await SpotnanaLoggingConfig.write_log_file(Path("/test/path/file.json"), {"test": "data"})

                mock_mkdir.assert_called_once_with(parents=True, exist_ok=True)
                mock_file_handle.write.assert_called_once_with('{\n  "test": "data"\n}')

    @pytest.mark.asyncio
    async def test_write_log_file_exception(self):
        """Test log file writing exception handling."""
        with patch("aiofiles.open", side_effect=Exception("File error")):
            with patch("server.api_clients.spotnana.logging_config.logger") as mock_logger:
                await SpotnanaLoggingConfig.write_log_file(Path("/test/path/file.json"), {"test": "data"})

                mock_logger.error.assert_called_once()
                # Check for the new structured event name
                assert mock_logger.error.call_args[0][0] == "log_file_write_failed"

    @patch("server.api_clients.spotnana.logging_config.settings")
    def test_should_log_to_file_server_context(self, mock_settings):
        """Test file logging disabled in server context."""
        mock_settings.is_local = False

        result = SpotnanaLoggingConfig.should_log_to_file("/v2/air/search")
        assert result is False

    @patch("server.api_clients.spotnana.logging_config.settings")
    def test_should_log_to_file_no_url(self, mock_settings):
        """Test file logging disabled for no URL."""
        mock_settings.is_local = True

        result = SpotnanaLoggingConfig.should_log_to_file(None)
        assert result is False

        result = SpotnanaLoggingConfig.should_log_to_file("")
        assert result is False

    @patch("server.api_clients.spotnana.logging_config.settings")
    @patch("server.api_clients.spotnana.logging_config.FILE_LOGGING_CONFIG")
    def test_should_log_to_file_global_override(self, mock_config, mock_settings):
        """Test file logging with global override."""
        mock_settings.is_local = True
        mock_config.get.return_value = True

        result = SpotnanaLoggingConfig.should_log_to_file("/v2/air/search")
        assert result is True
        mock_config.get.assert_called_with("log_all_endpoints", False)

    @patch("server.api_clients.spotnana.logging_config.settings")
    @patch("server.api_clients.spotnana.logging_config.FILE_LOGGING_CONFIG")
    def test_should_log_to_file_namespace_enabled(self, mock_config, mock_settings):
        """Test file logging with namespace enabled."""
        mock_settings.is_local = True
        mock_config.get.side_effect = lambda key, default=None: {
            "log_all_endpoints": False,
            "namespaces": ["air"],
            "endpoints": [],
            "url_patterns": [],
        }.get(key, default)

        result = SpotnanaLoggingConfig.should_log_to_file("/v2/air/search")
        assert result is True

    @patch("server.api_clients.spotnana.logging_config.settings")
    @patch("server.api_clients.spotnana.logging_config.FILE_LOGGING_CONFIG")
    def test_should_log_to_file_endpoint_enabled(self, mock_config, mock_settings):
        """Test file logging with specific endpoint enabled."""
        mock_settings.is_local = True
        mock_config.get.side_effect = lambda key, default=None: {
            "log_all_endpoints": False,
            "namespaces": [],
            "endpoints": ["air/search"],
            "url_patterns": [],
        }.get(key, default)

        result = SpotnanaLoggingConfig.should_log_to_file("/v2/air/search")
        assert result is True

    @patch("server.api_clients.spotnana.logging_config.settings")
    def test_should_log_to_firehose_server_context(self, mock_settings):
        """Test firehose logging enabled in server context."""
        mock_settings.is_local = False

        result = SpotnanaLoggingConfig.should_log_to_firehose()
        assert result is True

    @patch("server.api_clients.spotnana.logging_config.settings")
    def test_should_log_to_firehose_local_context(self, mock_settings):
        """Test firehose logging disabled in local context."""
        mock_settings.is_local = True

        result = SpotnanaLoggingConfig.should_log_to_firehose()
        assert result is False

    @patch("server.api_clients.spotnana.logging_config.flight_utils")
    def test_log_request_to_firehose_disabled(self, mock_flight_utils):
        """Test firehose logging when disabled."""
        with patch.object(SpotnanaLoggingConfig, "should_log_to_firehose", return_value=False):
            SpotnanaLoggingConfig.log_request_to_firehose(
                "https://api.test.com/search", {"data": "test"}, {"param": "value"}, {"response": "data"}
            )

            mock_flight_utils.log_request_to_firehose.assert_not_called()

    @patch("server.api_clients.spotnana.logging_config.flight_utils")
    def test_log_request_to_firehose_success(self, mock_flight_utils):
        """Test successful firehose logging."""
        with patch.object(SpotnanaLoggingConfig, "should_log_to_firehose", return_value=True):
            with patch.object(SpotnanaLoggingConfig, "extract_context_variables") as mock_context:
                with patch.object(SpotnanaLoggingConfig, "extract_method_from_url") as mock_method:
                    mock_context.return_value = {"user_id_int": 123, "trip_id_int": 456}
                    mock_method.return_value = "AIR/SEARCH"

                    SpotnanaLoggingConfig.log_request_to_firehose(
                        "https://api.test.com/v2/air/search", {"data": "test"}, {"param": "value"}, {"response": "data"}
                    )

                    mock_flight_utils.log_request_to_firehose.assert_called_once_with(
                        source="SPOTNANA",
                        user_id=123,
                        trip_id=456,
                        params={"data": "test"},
                        response={"response": "data"},
                        method="AIR/SEARCH",
                    )

    @patch("server.api_clients.spotnana.logging_config.flight_utils")
    def test_log_request_to_firehose_with_query_params(self, mock_flight_utils):
        """Test firehose logging with query params instead of request data."""
        with patch.object(SpotnanaLoggingConfig, "should_log_to_firehose", return_value=True):
            with patch.object(SpotnanaLoggingConfig, "extract_context_variables") as mock_context:
                with patch.object(SpotnanaLoggingConfig, "extract_method_from_url") as mock_method:
                    mock_context.return_value = {"user_id_int": 123, "trip_id_int": 456}
                    mock_method.return_value = "AIR/SEARCH"

                    SpotnanaLoggingConfig.log_request_to_firehose(
                        "https://api.test.com/v2/air/search",
                        None,  # No request data
                        {"param": "value"},
                        {"response": "data"},
                    )

                    mock_flight_utils.log_request_to_firehose.assert_called_once_with(
                        source="SPOTNANA",
                        user_id=123,
                        trip_id=456,
                        params={"param": "value"},
                        response={"response": "data"},
                        method="AIR/SEARCH",
                    )

    @patch("server.api_clients.spotnana.logging_config.flight_utils")
    def test_log_request_to_firehose_exception(self, mock_flight_utils):
        """Test firehose logging exception handling."""
        mock_flight_utils.log_request_to_firehose.side_effect = Exception("Firehose error")

        with patch.object(SpotnanaLoggingConfig, "should_log_to_firehose", return_value=True):
            with patch.object(SpotnanaLoggingConfig, "extract_context_variables") as mock_context:
                with patch.object(SpotnanaLoggingConfig, "extract_method_from_url") as mock_method:
                    with patch("server.api_clients.spotnana.logging_config.logger") as mock_logger:
                        mock_context.return_value = {"user_id_int": 123, "trip_id_int": 456}
                        mock_method.return_value = "AIR/SEARCH"

                        SpotnanaLoggingConfig.log_request_to_firehose(
                            "https://api.test.com/v2/air/search", {"data": "test"}, None, {"response": "data"}
                        )

                        mock_logger.error.assert_called_once()
                        # Check for the new structured event name
                        assert mock_logger.error.call_args[0][0] == "firehose_logging_failed"

    def test_check_spotnana_errors_no_errors(self):
        """Test no exception when no errors in response."""
        response = {"data": "success"}

        # Should not raise any exception
        SpotnanaLoggingConfig.check_spotnana_errors(response)

    def test_check_spotnana_errors_empty_error_messages(self):
        """Test no exception when errorMessages is empty."""
        response = {"errorMessages": []}

        # Should not raise any exception
        SpotnanaLoggingConfig.check_spotnana_errors(response)

    def test_check_spotnana_errors_with_errors(self):
        """Test exception raised when errors present."""
        response = {
            "errorMessages": [{"errorCode": "INVALID_REQUEST", "errorDetail": "Missing required field"}],
            "debugIdentifier": "debug-123",
        }

        with pytest.raises(Exception) as exc_info:
            SpotnanaLoggingConfig.check_spotnana_errors(response)

        expected_message = (
            "Spotnana Error: INVALID_REQUEST - Missing required field\n\nSpotnana debugIdentifier: debug-123"
        )
        assert str(exc_info.value) == expected_message

    def test_check_spotnana_errors_missing_fields(self):
        """Test exception with missing error fields."""
        response = {
            "errorMessages": [
                {}  # Missing errorCode and errorDetail
            ]
        }

        with pytest.raises(Exception) as exc_info:
            SpotnanaLoggingConfig.check_spotnana_errors(response)

        expected_message = "Spotnana Error: UNKNOWN - Unknown error\n\nSpotnana debugIdentifier: "
        assert str(exc_info.value) == expected_message
