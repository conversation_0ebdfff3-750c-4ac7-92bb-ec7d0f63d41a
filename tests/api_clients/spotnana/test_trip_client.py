"""
Unit tests for simplified Spotnana Trip API client.

Tests the SpotnanaTripsClient implementation focusing on:
- API call patterns and HTTP interactions
- Error handling and edge cases
- Business logic with adapter return types
"""

import uuid
from unittest.mock import AsyncMock, patch

import pytest

from models.adapters.spotnana.trip_adapter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, TripDetailsAdapter
from server.api_clients.spotnana import SpotnanaTripsClient, get_trips_client
from server.api_clients.spotnana.exceptions import SpotnanaException


class TestSpotnanaTripsClient:
    """Test suite for simplified SpotnanaTripsClient."""

    @pytest.fixture
    def mock_base_client(self):
        """Mock base SpotnanaClient."""
        client = AsyncMock()
        return client

    @pytest.fixture
    def trips_client(self, mock_base_client):
        """SpotnanaTripsClient with mocked base client."""
        return SpotnanaTripsClient(base_client=mock_base_client)

    @pytest.fixture
    def sample_trip_details_response(self):
        """Sample trip details response data."""
        return {
            "basicTripInfo": {
                "tripId": "12345",
                "tripName": "Test Trip",
                "tripDescription": "Test Description",
            },
            "tripBookingStatus": "CONFIRMED_STATUS",
            "tripStatus": "ACTIVE",
            "pnrs": [],
            "eventSummary": None,
            "tripPaymentInfo": None,
            "additionalInfo": None,
            "pendingShellPnrs": None,
            "pendingManualFormPnrs": None,
        }

    @pytest.fixture
    def sample_trip_create_response(self):
        """Sample trip create response data."""
        return {"id": "67890"}

    @pytest.mark.asyncio
    async def test_get_details_success(self, trips_client, mock_base_client, sample_trip_details_response):
        """Test successful trip details retrieval."""
        # Arrange
        trip_id = "12345"
        mock_base_client.get.return_value = sample_trip_details_response

        # Act
        result = await trips_client.get_details(trip_id)

        # Assert
        assert isinstance(result, TripDetailsAdapter)
        assert result.get_trip_id() == "12345"
        assert result.basic_trip_info.tripName == "Test Trip"
        mock_base_client.get.assert_called_once_with("v3/trips/12345/detail")

    @pytest.mark.asyncio
    async def test_create_success(self, trips_client, mock_base_client, sample_trip_create_response):
        """Test successful trip creation."""
        # Arrange
        trip_name = "New Trip"
        user_id = uuid.uuid4()
        registrar_id = uuid.uuid4()
        mock_base_client.post.return_value = sample_trip_create_response

        # Act
        result = await trips_client.create(trip_name=trip_name, spotnana_user_id=user_id, registrar_id=registrar_id)

        # Assert
        assert isinstance(result, TripCreateAdapter)
        assert result.trip_id == "67890"
        mock_base_client.post.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_flight_trip_success(self, trips_client, mock_base_client, sample_trip_create_response):
        """Test successful flight trip creation."""
        # Arrange
        flight_params = '{"arrival_airport_code": "LAX"}'
        user_guid = uuid.uuid4()
        mock_base_client.post.return_value = sample_trip_create_response

        # Act
        result = await trips_client.create_flight_trip(flight_params, user_guid)

        # Assert
        assert isinstance(result, TripCreateAdapter)
        assert result.trip_id == "67890"
        mock_base_client.post.assert_called_once()

    @pytest.mark.asyncio
    async def test_error_handling(self, trips_client, mock_base_client):
        """Test error handling in API calls."""
        # Arrange
        trip_id = "12345"
        mock_base_client.get.side_effect = Exception("API Error")

        # Act & Assert
        with pytest.raises(SpotnanaException):
            await trips_client.get_details(trip_id)

    @pytest.mark.asyncio
    async def test_logging(self, trips_client, mock_base_client, sample_trip_details_response):
        """Test that appropriate logging occurs during API calls."""
        # Arrange
        trip_id = "12345"
        mock_base_client.get.return_value = sample_trip_details_response

        # Act
        with patch("server.api_clients.spotnana.trip.logger") as mock_logger:
            result = await trips_client.get_details(trip_id)

        # Assert
        assert isinstance(result, TripDetailsAdapter)
        # Verify logging calls were made
        mock_logger.info.assert_called()  # Should have logged start and success messages


class TestGetTripsClient:
    """Test suite for get_trips_client factory function."""

    @patch("server.api_clients.spotnana.trip.SpotnanaClient")
    def test_singleton_behavior(self, mock_client_class):
        """Test that get_trips_client returns the same instance."""
        # Arrange
        mock_client_instance = AsyncMock()
        mock_client_class.return_value = mock_client_instance

        # Reset the singleton instance to ensure clean test
        import server.api_clients.spotnana.trip as trip_module

        trip_module._trips_client_instance = None

        # Act
        client1 = get_trips_client()
        client2 = get_trips_client()

        # Assert
        assert client1 is client2
        assert isinstance(client1, SpotnanaTripsClient)
        # Verify the base client was only created once due to singleton behavior
        mock_client_class.assert_called_once()
