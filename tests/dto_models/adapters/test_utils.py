"""
Unit tests for models/adapters/utils.py
"""

from models.adapters.utils import safe_nested_get


class TestSafeNestedGet:
    """Test suite for safe_nested_get utility function."""

    def test_basic_dict_access(self):
        """Test basic dictionary access with string keys."""
        data = {"user": {"profile": {"name": "<PERSON>", "age": 30}}}
        assert safe_nested_get(data, "user.profile.name") == "<PERSON>"
        assert safe_nested_get(data, "user.profile.age") == 30

    def test_list_index_access(self):
        """Test accessing list elements by index."""
        data = {"items": [{"id": 1, "name": "Item 1"}, {"id": 2, "name": "Item 2"}]}
        assert safe_nested_get(data, "items.0.id") == 1
        assert safe_nested_get(data, "items.1.name") == "Item 2"

    def test_nested_list_and_dict(self):
        """Test nested structures with both lists and dictionaries."""
        data = {
            "users": [
                {"profile": {"name": "Alice", "settings": {"theme": "dark"}}},
                {"profile": {"name": "<PERSON>", "settings": {"theme": "light"}}},
            ]
        }
        assert safe_nested_get(data, "users.0.profile.name") == "Alice"
        assert safe_nested_get(data, "users.1.profile.settings.theme") == "light"

    def test_default_value_on_missing_key(self):
        """Test default value when key doesn't exist."""
        data = {"user": {"name": "John"}}
        assert safe_nested_get(data, "user.age", default=25) == 25
        assert safe_nested_get(data, "user.address.city", default="Unknown") == "Unknown"

    def test_default_value_none(self):
        """Test None as default value."""
        data = {"user": {"name": "John"}}
        assert safe_nested_get(data, "user.age") is None
        assert safe_nested_get(data, "nonexistent") is None

    def test_empty_dict(self):
        """Test with empty dictionary."""
        data = {}
        assert safe_nested_get(data, "any.path") is None
        assert safe_nested_get(data, "any.path", default="default") == "default"

    def test_none_data(self):
        """Test with None as data."""
        assert safe_nested_get(None, "any.path") is None
        assert safe_nested_get(None, "any.path", default="fallback") == "fallback"

    def test_list_out_of_bounds(self):
        """Test accessing list indices out of bounds."""
        data = {"items": ["a", "b", "c"]}
        assert safe_nested_get(data, "items.5") is None
        assert safe_nested_get(data, "items.-1") is None  # Negative indices not supported

    def test_custom_separator(self):
        """Test using custom separator."""
        data = {"user": {"profile": {"name": "John"}}}
        assert safe_nested_get(data, "user/profile/name", separator="/") == "John"

    def test_object_attribute_access(self):
        """Test accessing object attributes."""

        class Profile:
            def __init__(self):
                self.name = "Alice"
                self.age = 28

        class User:
            def __init__(self):
                self.profile = Profile()

        user = User()
        assert safe_nested_get(user, "profile.name") == "Alice"
        assert safe_nested_get(user, "profile.age") == 28

    def test_mixed_object_and_dict(self):
        """Test mixed object and dictionary access."""

        class Person:
            def __init__(self):
                self.name = "Bob"
                self.data = {"address": {"city": "New York"}}

        person = Person()
        assert safe_nested_get(person, "name") == "Bob"
        assert safe_nested_get(person, "data.address.city") == "New York"

    def test_empty_string_path(self):
        """Test with empty string path."""
        data = {"key": "value"}
        assert safe_nested_get(data, "") is None

    def test_single_key_path(self):
        """Test with single key path."""
        data = {"key": "value", "number": 42}
        assert safe_nested_get(data, "key") == "value"
        assert safe_nested_get(data, "number") == 42

    def test_complex_nested_structure(self):
        """Test with complex nested structure."""
        data = {
            "api": {
                "response": {
                    "data": [
                        {
                            "flight": {
                                "segments": [
                                    {"departure": {"airport": "JFK", "time": "10:00"}},
                                    {"departure": {"airport": "LAX", "time": "13:00"}},
                                ]
                            }
                        }
                    ]
                }
            }
        }
        assert safe_nested_get(data, "api.response.data.0.flight.segments.0.departure.airport") == "JFK"
        assert safe_nested_get(data, "api.response.data.0.flight.segments.1.departure.time") == "13:00"

    def test_numeric_string_keys(self):
        """Test with numeric string keys in dictionaries."""
        data = {"123": "numeric_key", "456": {"789": "nested"}}
        assert safe_nested_get(data, "123") == "numeric_key"
        assert safe_nested_get(data, "456.789") == "nested"

    def test_non_dict_intermediate(self):
        """Test when intermediate value is not dict/list."""
        data = {"user": "string_value"}
        assert safe_nested_get(data, "user.profile.name") is None

    def test_unicode_keys(self):
        """Test with unicode keys."""
        data = {"café": {"résumé": "document"}}
        assert safe_nested_get(data, "café.résumé") == "document"

    def test_boolean_values(self):
        """Test with boolean values."""
        data = {"settings": {"dark_mode": True, "notifications": False}}
        assert safe_nested_get(data, "settings.dark_mode") is True
        assert safe_nested_get(data, "settings.notifications") is False

    def test_zero_index(self):
        """Test zero index specifically."""
        data = {"items": ["first", "second"]}
        assert safe_nested_get(data, "items.0") == "first"
        assert safe_nested_get(data, "items.1") == "second"
