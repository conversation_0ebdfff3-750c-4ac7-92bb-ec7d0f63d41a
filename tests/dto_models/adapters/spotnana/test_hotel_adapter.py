"""
Unit tests for Hotel adapter models.

Tests the HotelPnrAdapter, RoomAdapter, and HotelInfoAdapter implementations.
"""

from unittest.mock import Mock

import pytest

from models.adapters.spotnana.hotel_adapter import HotelInfo<PERSON>dapter, HotelPnrAdapter, RoomAdapter
from models.api.spotnana.trip.models import Hotel, HotelInfo, Room


class TestRoomAdapter:
    """Test suite for RoomAdapter."""

    @pytest.fixture
    def mock_room(self):
        """Create a mock Room for testing."""
        mock = Mock(spec=Room)
        mock.bedCount = 2
        mock.roomName = "Deluxe Suite"
        mock.rateInfo = {"rate": "200.00"}

        # Mock enum-like bed type
        mock_bed_type = Mock()
        mock_bed_type.value = "KING"
        mock.bedType = mock_bed_type

        # Mock cancellation policy (could be string or object)
        mock.cancellationPolicy = "Free cancellation until 24h before check-in"

        return mock

    @pytest.fixture
    def adapter(self, mock_room):
        """Create a RoomAdapter with mock data."""
        return RoomAdapter(mock_room)

    def test_cancellation_policy_string(self, adapter):
        """Test cancellation_policy property with string value."""
        assert adapter.cancellation_policy == "Free cancellation until 24h before check-in"

    def test_cancellation_policy_object_with_value(self):
        """Test cancellation_policy with object having value attribute."""
        mock_room = Mock(spec=Room)
        mock_policy = Mock()
        mock_policy.__str__ = Mock(return_value="Non-refundable")
        mock_room.cancellationPolicy = mock_policy
        adapter = RoomAdapter(mock_room)

        assert adapter.cancellation_policy == "Non-refundable"

    def test_cancellation_policy_object_with_policy(self):
        """Test cancellation_policy with object having policy attribute."""
        mock_room = Mock(spec=Room)
        mock_policy = Mock()
        mock_policy.__str__ = Mock(return_value="Flexible cancellation")
        mock_room.cancellationPolicy = mock_policy
        adapter = RoomAdapter(mock_room)

        assert adapter.cancellation_policy == "Flexible cancellation"

    def test_cancellation_policy_none(self):
        """Test cancellation_policy when None."""
        mock_room = Mock(spec=Room)
        mock_room.cancellationPolicy = None
        adapter = RoomAdapter(mock_room)

        assert adapter.cancellation_policy is None

    def test_bed_count(self, adapter):
        """Test bed_count property."""
        assert adapter.bed_count == 2

    def test_bed_type(self, adapter):
        """Test bed_type property."""
        assert adapter.bed_type == "KING"

    def test_bed_type_string(self):
        """Test bed_type when already a string."""
        mock_room = Mock(spec=Room)
        mock_room.bedType = "QUEEN"
        adapter = RoomAdapter(mock_room)

        assert adapter.bed_type == "QUEEN"

    def test_room_name(self, adapter):
        """Test room_name property."""
        assert adapter.room_name == "Deluxe Suite"

    def test_rate_info(self, adapter):
        """Test rate_info property."""
        assert adapter.rate_info == {"rate": "200.00"}


class TestHotelInfoAdapter:
    """Test suite for HotelInfoAdapter."""

    @pytest.fixture
    def mock_hotel_info(self):
        """Create a mock HotelInfo for testing."""
        mock = Mock(spec=HotelInfo)
        mock.name = "Grand Hotel"  # HotelInfo uses 'name' not 'hotelName'
        mock.chainCode = "MAR"
        mock.chainName = "Marriott"
        mock.hotelId = "12345"
        mock.email = "<EMAIL>"

        # Mock phone as PhoneNumber object
        mock_phone = Mock()
        mock_phone.__str__ = Mock(return_value="******-0123")
        mock.phone = mock_phone

        mock.address = {"street": "123 Main St", "city": "San Francisco"}
        mock.coordinates = {"lat": 37.7749, "lng": -122.4194}
        return mock

    @pytest.fixture
    def adapter(self, mock_hotel_info):
        """Create a HotelInfoAdapter with mock data."""
        return HotelInfoAdapter(mock_hotel_info)

    def test_hotel_name(self, adapter):
        """Test hotel_name property."""
        assert adapter.hotel_name == "Grand Hotel"

    def test_chain_code(self, adapter):
        """Test chain_code property."""
        assert adapter.chain_code == "MAR"

    def test_chain_name(self, adapter):
        """Test chain_name property."""
        assert adapter.chain_name == "Marriott"

    def test_hotel_id(self, adapter):
        """Test hotel_id property."""
        assert adapter.hotel_id == "12345"

    def test_email(self, adapter):
        """Test email property."""
        assert adapter.email == "<EMAIL>"

    def test_phone(self, adapter):
        """Test phone property."""
        assert adapter.phone == "******-0123"

    def test_address(self, adapter):
        """Test address property."""
        assert adapter.address == {"street": "123 Main St", "city": "San Francisco"}

    def test_coordinates(self, adapter):
        """Test coordinates property."""
        assert adapter.coordinates == {"lat": 37.7749, "lng": -122.4194}

    def test_none_values(self):
        """Test properties when values are None."""
        mock_hotel_info = Mock(spec=HotelInfo)
        mock_hotel_info.name = "Test Hotel"  # name is required field
        mock_hotel_info.chainCode = None
        mock_hotel_info.coordinates = None
        mock_hotel_info.phone = None
        adapter = HotelInfoAdapter(mock_hotel_info)

        assert adapter.hotel_name == "Test Hotel"
        assert adapter.chain_code is None
        assert adapter.coordinates is None
        assert adapter.phone is None


class TestHotelPnrAdapter:
    """Test suite for HotelPnrAdapter."""

    @pytest.fixture
    def mock_room(self):
        """Create a mock Room."""
        mock = Mock(spec=Room)
        mock.roomName = "Standard Room"
        mock.cancellationPolicy = "Free cancellation"
        return mock

    @pytest.fixture
    def mock_hotel_info(self):
        """Create a mock HotelInfo."""
        mock = Mock(spec=HotelInfo)
        mock.name = "City Hotel"  # HotelInfo uses 'name' field
        return mock

    @pytest.fixture
    def mock_hotel_pnr(self, mock_room, mock_hotel_info):
        """Create a mock Hotel PNR for testing."""
        mock = Mock(spec=Hotel)
        mock.vendorConfirmationNumber = "HTL789"
        mock.checkInDateTime = "2024-01-01T15:00:00"
        mock.checkOutDateTime = "2024-01-03T11:00:00"
        mock.numberOfRooms = 2
        mock.payment = {"method": "credit_card"}
        mock.travelerInfos = ["Traveler 1", "Traveler 2"]
        mock.hotelInfo = mock_hotel_info
        mock.room = mock_room

        # Mock enum-like status
        mock_status = Mock()
        mock_status.value = "CONFIRMED"
        mock.pnrStatus = mock_status

        return mock

    @pytest.fixture
    def adapter(self, mock_hotel_pnr):
        """Create a HotelPnrAdapter with mock data."""
        return HotelPnrAdapter(mock_hotel_pnr)

    def test_vendor_confirmation_number(self, adapter):
        """Test vendor_confirmation_number property."""
        assert adapter.vendor_confirmation_number == "HTL789"

    def test_check_in_datetime(self, adapter):
        """Test check_in_datetime property."""
        assert adapter.check_in_datetime == "2024-01-01T15:00:00"

    def test_check_out_datetime(self, adapter):
        """Test check_out_datetime property."""
        assert adapter.check_out_datetime == "2024-01-03T11:00:00"

    def test_number_of_rooms(self, adapter):
        """Test number_of_rooms property."""
        assert adapter.number_of_rooms == 2

    def test_pnr_status(self, adapter):
        """Test pnr_status property."""
        assert adapter.pnr_status == "CONFIRMED"

    def test_pnr_status_none(self):
        """Test pnr_status when None."""
        mock_hotel_pnr = Mock(spec=Hotel)
        mock_hotel_pnr.pnrStatus = None
        adapter = HotelPnrAdapter(mock_hotel_pnr)

        assert adapter.pnr_status is None

    def test_hotel_info_property(self, adapter):
        """Test hotel_info property returns HotelInfoAdapter."""
        hotel_info = adapter.hotel_info
        assert isinstance(hotel_info, HotelInfoAdapter)
        assert hotel_info.hotel_name == "City Hotel"

    def test_room_property(self, adapter):
        """Test room property returns RoomAdapter."""
        room = adapter.room
        assert isinstance(room, RoomAdapter)
        assert room.room_name == "Standard Room"

    def test_payment(self, adapter):
        """Test payment property."""
        assert adapter.payment == {"method": "credit_card"}

    def test_traveler_infos(self, adapter):
        """Test traveler_infos property."""
        travelers = adapter.traveler_infos
        assert len(travelers) == 2
        assert travelers == ["Traveler 1", "Traveler 2"]

    def test_traveler_infos_none(self):
        """Test traveler_infos when None."""
        mock_hotel_pnr = Mock(spec=Hotel)
        mock_hotel_pnr.travelerInfos = None
        adapter = HotelPnrAdapter(mock_hotel_pnr)

        assert adapter.traveler_infos == []

    def test_get_hotel_name(self, adapter):
        """Test get_hotel_name convenience method."""
        hotel_name = adapter.get_hotel_name()
        assert hotel_name == "City Hotel"

    def test_get_cancellation_policy(self, adapter):
        """Test get_cancellation_policy convenience method."""
        policy = adapter.get_cancellation_policy()
        assert policy == "Free cancellation"

    def test_get_room_name(self, adapter):
        """Test get_room_name convenience method."""
        room_name = adapter.get_room_name()
        assert room_name == "Standard Room"
