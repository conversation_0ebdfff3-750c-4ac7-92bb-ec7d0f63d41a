"""
Unit tests for Trip adapter models.

Tests the TripDetailsAdapter and TripCreateAdapter implementations with zero dictionary returns.
"""

from unittest.mock import Mock

import pytest

from models.adapters.spotnana.air_adapter import AirPnrAdapter
from models.adapters.spotnana.hotel_adapter import HotelPnrAdapter
from models.adapters.spotnana.metadata_adapter import AdditionalMetadataAdapter, AirlineInfoAdapter, AirportInfoAdapter
from models.adapters.spotnana.pnr_adapter import PnrDataAdapter
from models.adapters.spotnana.trip_adapter import PnrDetailsAdapter, TripCreateAdapter, TripDetailsAdapter
from models.api.spotnana.trip.models import EntityNonUUIDId, PnrDetailsResponseWithId, TripV3DetailsResponse


class TestPnrDetailsAdapter:
    """Test suite for PnrDetailsAdapter."""

    @pytest.fixture
    def mock_pnr_data(self):
        """Create a mock PnrData."""
        mock = Mock()
        mock.airPnr = Mock()
        mock.hotelPnr = None
        return mock

    @pytest.fixture
    def mock_pnr_details(self, mock_pnr_data):
        """Create a mock PnrDetailsResponseWithId for testing."""
        mock = Mock(spec=PnrDetailsResponseWithId)
        mock.pnrId = "pnr-123"
        mock.data = mock_pnr_data
        return mock

    @pytest.fixture
    def adapter(self, mock_pnr_details):
        """Create a PnrDetailsAdapter with mock data."""
        return PnrDetailsAdapter(mock_pnr_details)

    def test_pnr_id(self, adapter):
        """Test pnr_id property."""
        assert adapter.pnr_id == "pnr-123"

    def test_data_property(self, adapter):
        """Test data property returns PnrDataAdapter."""
        data = adapter.data
        assert data is not None
        assert isinstance(data, PnrDataAdapter)

    def test_data_none(self):
        """Test data property when data is None."""
        mock_pnr_details = Mock(spec=PnrDetailsResponseWithId)
        mock_pnr_details.pnrId = "pnr-456"
        mock_pnr_details.data = None
        adapter = PnrDetailsAdapter(mock_pnr_details)

        assert adapter.data is None


class TestTripDetailsAdapter:
    """Test suite for TripDetailsAdapter."""

    @pytest.fixture
    def mock_basic_trip_info(self):
        """Create mock basic trip info."""
        mock = Mock()
        mock.tripId = "trip-789"
        return mock

    @pytest.fixture
    def mock_airport_info(self):
        """Create mock airport info."""
        mock = Mock()
        mock.airportCode = "SFO"
        mock.airportName = "San Francisco Airport"
        return mock

    @pytest.fixture
    def mock_airline_info(self):
        """Create mock airline info."""
        mock = Mock()
        mock.airlineCode = "UA"
        mock.airlineName = "United Airlines"
        return mock

    @pytest.fixture
    def mock_metadata(self, mock_airport_info, mock_airline_info):
        """Create mock additional metadata."""
        mock = Mock()
        mock.airportInfo = [mock_airport_info]
        mock.airlineInfo = [mock_airline_info]
        mock.bta = "YMT"
        return mock

    @pytest.fixture
    def mock_air_pnr(self):
        """Create mock air PNR."""
        mock = Mock()
        mock.legs = []
        mock.travelerInfos = []
        return mock

    @pytest.fixture
    def mock_hotel_pnr(self):
        """Create mock hotel PNR."""
        mock = Mock()
        mock.vendorConfirmationNumber = "HTL123"
        mock.room = Mock()
        mock.room.cancellationPolicy = "Free cancellation"
        return mock

    @pytest.fixture
    def mock_pnr_data(self, mock_air_pnr, mock_hotel_pnr, mock_metadata):
        """Create mock PNR data."""
        mock = Mock()
        mock.airPnr = mock_air_pnr
        mock.hotelPnr = mock_hotel_pnr
        mock.additionalMetadata = mock_metadata
        return mock

    @pytest.fixture
    def mock_pnr_details(self, mock_pnr_data):
        """Create mock PNR details."""
        mock = Mock()
        mock.pnrId = "pnr-456"
        mock.data = mock_pnr_data
        return mock

    @pytest.fixture
    def mock_trip_response(self, mock_basic_trip_info, mock_pnr_details):
        """Create a mock TripV3DetailsResponse for testing."""
        mock = Mock(spec=TripV3DetailsResponse)
        mock.basicTripInfo = mock_basic_trip_info
        mock.pnrs = [mock_pnr_details]

        # Mock enum-like statuses
        mock_booking_status = Mock()
        mock_booking_status.value = "CONFIRMED"
        mock.tripBookingStatus = mock_booking_status

        mock_trip_status = Mock()
        mock_trip_status.value = "ACTIVE"
        mock.tripStatus = mock_trip_status

        mock.eventSummary = {"events": []}
        mock.tripPaymentInfo = {"totalAmount": "500.00"}
        mock.additionalInfo = {"notes": "Business trip"}

        # Mock model_dump for legacy compatibility
        mock.model_dump.return_value = {
            "basicTripInfo": {"tripId": "trip-789"},
            "tripBookingStatus": "CONFIRMED",
            "tripStatus": "ACTIVE",
            "pnrs": [{"pnrId": "pnr-456", "data": {}}],
        }

        return mock

    @pytest.fixture
    def adapter(self, mock_trip_response):
        """Create a TripDetailsAdapter with mock data."""
        return TripDetailsAdapter(mock_trip_response)

    def test_trip_booking_status(self, adapter):
        """Test trip_booking_status property."""
        assert adapter.trip_booking_status == "CONFIRMED"

    def test_trip_booking_status_none(self):
        """Test trip_booking_status when None."""
        mock_response = Mock(spec=TripV3DetailsResponse)
        mock_response.tripBookingStatus = None
        adapter = TripDetailsAdapter(mock_response)

        assert adapter.trip_booking_status is None

    def test_trip_status(self, adapter):
        """Test trip_status property."""
        assert adapter.trip_status == "ACTIVE"

    def test_trip_status_string(self):
        """Test trip_status when already a string."""
        mock_response = Mock(spec=TripV3DetailsResponse)
        mock_response.tripStatus = "COMPLETED"
        adapter = TripDetailsAdapter(mock_response)

        assert adapter.trip_status == "COMPLETED"

    def test_basic_trip_info(self, adapter):
        """Test basic_trip_info property."""
        info = adapter.basic_trip_info
        assert info.tripId == "trip-789"

    def test_event_summary(self, adapter):
        """Test event_summary property."""
        assert adapter.event_summary == {"events": []}

    def test_trip_payment_info(self, adapter):
        """Test trip_payment_info property."""
        assert adapter.trip_payment_info == {"totalAmount": "500.00"}

    def test_additional_info(self, adapter):
        """Test additional_info property."""
        assert adapter.additional_info == {"notes": "Business trip"}

    def test_pnr_details_list(self, adapter):
        """Test pnr_details_list returns list of PnrDetailsAdapter."""
        pnr_list = adapter.pnr_details_list
        assert len(pnr_list) == 1
        assert isinstance(pnr_list[0], PnrDetailsAdapter)
        assert pnr_list[0].pnr_id == "pnr-456"

    def test_pnr_details_list_empty(self):
        """Test pnr_details_list when no PNRs."""
        mock_response = Mock(spec=TripV3DetailsResponse)
        mock_response.pnrs = []
        adapter = TripDetailsAdapter(mock_response)

        assert adapter.pnr_details_list == []

    def test_primary_pnr_data(self, adapter):
        """Test primary_pnr_data returns PnrDataAdapter."""
        pnr_data = adapter.primary_pnr_data
        assert pnr_data is not None
        assert isinstance(pnr_data, PnrDataAdapter)

    def test_primary_pnr_data_no_pnrs(self):
        """Test primary_pnr_data when no PNRs."""
        mock_response = Mock(spec=TripV3DetailsResponse)
        mock_response.pnrs = []
        adapter = TripDetailsAdapter(mock_response)

        assert adapter.primary_pnr_data is None

    def test_primary_air_pnr(self, adapter):
        """Test primary_air_pnr returns AirPnrAdapter."""
        air_pnr = adapter.primary_air_pnr
        assert air_pnr is not None
        assert isinstance(air_pnr, AirPnrAdapter)

    def test_primary_air_pnr_none(self):
        """Test primary_air_pnr when no air PNR."""
        mock_response = Mock(spec=TripV3DetailsResponse)
        mock_response.pnrs = []
        adapter = TripDetailsAdapter(mock_response)

        assert adapter.primary_air_pnr is None

    def test_primary_hotel_pnr(self, adapter):
        """Test primary_hotel_pnr returns HotelPnrAdapter."""
        hotel_pnr = adapter.primary_hotel_pnr
        assert hotel_pnr is not None
        assert isinstance(hotel_pnr, HotelPnrAdapter)

    def test_primary_hotel_pnr_none(self):
        """Test primary_hotel_pnr when no hotel PNR."""
        mock_response = Mock(spec=TripV3DetailsResponse)
        mock_response.pnrs = []
        adapter = TripDetailsAdapter(mock_response)

        assert adapter.primary_hotel_pnr is None

    def test_additional_metadata(self, adapter):
        """Test additional_metadata returns AdditionalMetadataAdapter."""
        metadata = adapter.additional_metadata
        assert metadata is not None
        assert isinstance(metadata, AdditionalMetadataAdapter)

    def test_additional_metadata_none(self):
        """Test additional_metadata when None."""
        mock_response = Mock(spec=TripV3DetailsResponse)
        mock_response.pnrs = []
        adapter = TripDetailsAdapter(mock_response)

        assert adapter.additional_metadata is None

    def test_airport_info(self, adapter):
        """Test airport_info returns list of AirportInfoAdapter."""
        airport_info = adapter.airport_info
        assert len(airport_info) == 1
        assert isinstance(airport_info[0], AirportInfoAdapter)
        assert airport_info[0].airport_code == "SFO"

    def test_airport_info_no_metadata(self):
        """Test airport_info when no metadata."""
        mock_response = Mock(spec=TripV3DetailsResponse)
        mock_response.pnrs = []
        adapter = TripDetailsAdapter(mock_response)

        assert adapter.airport_info == []

    def test_airline_info(self, adapter):
        """Test airline_info returns list of AirlineInfoAdapter."""
        airline_info = adapter.airline_info
        assert len(airline_info) == 1
        assert isinstance(airline_info[0], AirlineInfoAdapter)
        assert airline_info[0].airline_code == "UA"

    def test_airline_info_no_metadata(self):
        """Test airline_info when no metadata."""
        mock_response = Mock(spec=TripV3DetailsResponse)
        mock_response.pnrs = []
        adapter = TripDetailsAdapter(mock_response)

        assert adapter.airline_info == []

    def test_get_hotel_cancellation_policy(self, adapter):
        """Test get_hotel_cancellation_policy method."""
        policy = adapter.get_hotel_cancellation_policy()
        assert policy == "Free cancellation"

    def test_get_hotel_cancellation_policy_no_hotel(self):
        """Test get_hotel_cancellation_policy when no hotel."""
        mock_response = Mock(spec=TripV3DetailsResponse)
        mock_response.pnrs = []
        adapter = TripDetailsAdapter(mock_response)

        assert adapter.get_hotel_cancellation_policy() is None

    def test_get_vendor_confirmation_number_no_air(self):
        """Test get_vendor_confirmation_number when no air PNR."""
        mock_response = Mock(spec=TripV3DetailsResponse)
        mock_response.pnrs = []
        adapter = TripDetailsAdapter(mock_response)

        assert adapter.get_vendor_confirmation_number() is None

    def test_get_airport_name_by_code(self, adapter):
        """Test get_airport_name_by_code method."""
        name = adapter.get_airport_name_by_code("SFO")
        assert name == "San Francisco Airport"

    def test_get_airport_name_by_code_not_found(self, adapter):
        """Test get_airport_name_by_code when not found."""
        name = adapter.get_airport_name_by_code("LAX")
        assert name is None

    def test_get_airline_name_by_code(self, adapter):
        """Test get_airline_name_by_code method."""
        name = adapter.get_airline_name_by_code("UA")
        assert name == "United Airlines"

    def test_get_airline_name_by_code_not_found(self, adapter):
        """Test get_airline_name_by_code when not found."""
        name = adapter.get_airline_name_by_code("AA")
        assert name is None

    def test_build_airport_code_to_name_map(self, adapter):
        """Test build_airport_code_to_name_map method."""
        airport_map = adapter.build_airport_code_to_name_map()
        assert airport_map == {"SFO": "San Francisco Airport"}

    def test_build_airport_code_to_name_map_no_metadata(self):
        """Test build_airport_code_to_name_map when no metadata."""
        mock_response = Mock(spec=TripV3DetailsResponse)
        mock_response.pnrs = []
        adapter = TripDetailsAdapter(mock_response)

        assert adapter.build_airport_code_to_name_map() == {}

    def test_build_airline_code_to_name_map(self, adapter):
        """Test build_airline_code_to_name_map method."""
        airline_map = adapter.build_airline_code_to_name_map()
        assert airline_map == {"UA": "United Airlines"}

    def test_build_airline_code_to_name_map_no_metadata(self):
        """Test build_airline_code_to_name_map when no metadata."""
        mock_response = Mock(spec=TripV3DetailsResponse)
        mock_response.pnrs = []
        adapter = TripDetailsAdapter(mock_response)

        assert adapter.build_airline_code_to_name_map() == {}

    def test_get_trip_id(self, adapter):
        """Test get_trip_id method."""
        trip_id = adapter.get_trip_id()
        assert trip_id == "trip-789"

    def test_get_trip_id_no_basic_info(self):
        """Test get_trip_id when no basic trip info."""
        mock_response = Mock(spec=TripV3DetailsResponse)
        mock_response.basicTripInfo = None
        adapter = TripDetailsAdapter(mock_response)

        assert adapter.get_trip_id() is None


class TestTripCreateAdapter:
    """Test suite for TripCreateAdapter."""

    @pytest.fixture
    def mock_create_response(self):
        """Create a mock EntityNonUUIDId for testing."""
        mock = Mock(spec=EntityNonUUIDId)
        mock.id = "trip-create-123"
        # Mock model_dump for legacy compatibility
        mock.model_dump.return_value = {"id": "trip-create-123"}
        return mock

    @pytest.fixture
    def adapter(self, mock_create_response):
        """Create a TripCreateAdapter with mock data."""
        return TripCreateAdapter(mock_create_response)

    def test_trip_id(self, adapter):
        """Test trip_id property."""
        assert adapter.trip_id == "trip-create-123"

    def test_trip_id_none(self):
        """Test trip_id when None."""
        mock_response = Mock(spec=EntityNonUUIDId)
        mock_response.id = None
        adapter = TripCreateAdapter(mock_response)

        assert adapter.trip_id is None
