"""
Unit tests for Air adapter models.

Tests the AirPnrAdapter, LegAdapter, and FlightAdapter implementations.
"""

from unittest.mock import Mock

import pytest

from models.adapters.spotnana.air_adapter import AirPnrAdapter, FlightAdapter, LegAdapter
from models.api.spotnana.trip.models import Air, Flight, Leg


class TestFlightAdapter:
    """Test suite for FlightAdapter."""

    @pytest.fixture
    def mock_flight(self):
        """Create a mock Flight for testing."""
        mock = Mock(spec=Flight)
        mock.vendorConfirmationNumber = "ABC123"

        # Create mock marketing flight number
        mock_marketing = Mock()
        mock_marketing.airlineCode = "UA"
        mock_marketing.num = "123"
        mock.marketing = mock_marketing

        mock.departureDateTime = "2024-01-01T10:00:00"
        mock.arrivalDateTime = "2024-01-01T12:00:00"
        mock.flightId = "flight-456"
        mock.duration = "2h"
        return mock

    @pytest.fixture
    def adapter(self, mock_flight):
        """Create a FlightAdapter with mock data."""
        return FlightAdapter(mock_flight)

    def test_vendor_confirmation_number(self, adapter):
        """Test vendor_confirmation_number property."""
        assert adapter.vendor_confirmation_number == "ABC123"

    def test_airline_code(self, adapter):
        """Test airline_code property."""
        assert adapter.airline_code == "UA"

    def test_flight_number(self, adapter):
        """Test flight_number property."""
        assert adapter.flight_number == "123"

    def test_departure_datetime(self, adapter):
        """Test departure_datetime property."""
        assert adapter.departure_datetime == "2024-01-01T10:00:00"

    def test_arrival_datetime(self, adapter):
        """Test arrival_datetime property."""
        assert adapter.arrival_datetime == "2024-01-01T12:00:00"

    def test_flight_id(self, adapter):
        """Test flight_id property."""
        assert adapter.flight_id == "flight-456"

    def test_duration(self, adapter):
        """Test duration property."""
        assert adapter.duration == "2h"

    def test_vendor_confirmation_number_none(self):
        """Test vendor_confirmation_number when None."""
        mock_flight = Mock(spec=Flight)
        mock_flight.vendorConfirmationNumber = None
        adapter = FlightAdapter(mock_flight)

        assert adapter.vendor_confirmation_number is None


class TestLegAdapter:
    """Test suite for LegAdapter."""

    @pytest.fixture
    def mock_flight(self):
        """Create a mock Flight."""
        mock = Mock(spec=Flight)
        mock.vendorConfirmationNumber = "DEF456"

        # Create mock marketing flight number
        mock_marketing = Mock()
        mock_marketing.airlineCode = "AA"
        mock.marketing = mock_marketing

        return mock

    @pytest.fixture
    def mock_leg(self, mock_flight):
        """Create a mock Leg for testing."""
        mock = Mock(spec=Leg)
        mock.flights = [mock_flight]
        mock.brandName = "PREMIUM"
        mock.validatingAirlineCode = "AA"

        # Mock enum-like status
        mock_status = Mock()
        mock_status.value = "CONFIRMED"
        mock.legStatus = mock_status

        return mock

    @pytest.fixture
    def adapter(self, mock_leg):
        """Create a LegAdapter with mock data."""
        return LegAdapter(mock_leg)

    def test_flights_property(self, adapter):
        """Test flights property returns list of FlightAdapter."""
        flights = adapter.flights
        assert len(flights) == 1
        assert isinstance(flights[0], FlightAdapter)
        assert flights[0].airline_code == "AA"

    def test_brand_name(self, adapter):
        """Test brand_name property."""
        assert adapter.brand_name == "PREMIUM"

    def test_validating_airline_code(self, adapter):
        """Test validating_airline_code property."""
        assert adapter.validating_airline_code == "AA"

    def test_leg_status(self, adapter):
        """Test leg_status property with enum value."""
        assert adapter.leg_status == "CONFIRMED"

    def test_leg_status_none(self):
        """Test leg_status when None."""
        mock_leg = Mock(spec=Leg)
        mock_leg.flights = []
        mock_leg.legStatus = None
        adapter = LegAdapter(mock_leg)

        assert adapter.leg_status is None

    def test_leg_status_string(self):
        """Test leg_status when already a string."""
        mock_leg = Mock(spec=Leg)
        mock_leg.flights = []
        mock_leg.legStatus = "ACTIVE"
        adapter = LegAdapter(mock_leg)

        assert adapter.leg_status == "ACTIVE"

    def test_get_vendor_confirmation_number(self, adapter):
        """Test get_vendor_confirmation_number method."""
        confirmation = adapter.get_vendor_confirmation_number()
        assert confirmation == "DEF456"

    def test_get_vendor_confirmation_number_no_flights(self):
        """Test get_vendor_confirmation_number when no flights."""
        mock_leg = Mock(spec=Leg)
        mock_leg.flights = []
        adapter = LegAdapter(mock_leg)

        assert adapter.get_vendor_confirmation_number() is None


class TestAirPnrAdapter:
    """Test suite for AirPnrAdapter."""

    @pytest.fixture
    def mock_flight(self):
        """Create a mock Flight."""
        mock = Mock(spec=Flight)
        mock.vendorConfirmationNumber = "GHI789"

        # Create mock marketing flight number
        mock_marketing = Mock()
        mock_marketing.airlineCode = "DL"
        mock.marketing = mock_marketing

        return mock

    @pytest.fixture
    def mock_leg(self, mock_flight):
        """Create a mock Leg."""
        mock = Mock(spec=Leg)
        mock.flights = [mock_flight]
        return mock

    @pytest.fixture
    def mock_air_pnr(self, mock_leg):
        """Create a mock Air PNR for testing."""
        mock = Mock(spec=Air)
        mock.legs = [mock_leg]
        mock.airPnrRemarks = ["Remark 1", "Remark 2"]
        mock.travelerInfos = ["Traveler 1", "Traveler 2"]
        return mock

    @pytest.fixture
    def adapter(self, mock_air_pnr):
        """Create an AirPnrAdapter with mock data."""
        return AirPnrAdapter(mock_air_pnr)

    def test_legs_property(self, adapter):
        """Test legs property returns list of LegAdapter."""
        legs = adapter.legs
        assert len(legs) == 1
        assert isinstance(legs[0], LegAdapter)

    def test_air_pnr_remarks(self, adapter):
        """Test air_pnr_remarks property."""
        remarks = adapter.air_pnr_remarks
        assert len(remarks) == 2
        assert remarks == ["Remark 1", "Remark 2"]

    def test_air_pnr_remarks_none(self):
        """Test air_pnr_remarks when None."""
        mock_air_pnr = Mock(spec=Air)
        mock_air_pnr.legs = []
        mock_air_pnr.airPnrRemarks = None
        mock_air_pnr.travelerInfos = []
        adapter = AirPnrAdapter(mock_air_pnr)

        assert adapter.air_pnr_remarks == []

    def test_traveler_infos(self, adapter):
        """Test traveler_infos property."""
        travelers = adapter.traveler_infos
        assert len(travelers) == 2
        assert travelers == ["Traveler 1", "Traveler 2"]

    def test_get_vendor_confirmation_number(self, adapter):
        """Test get_vendor_confirmation_number method."""
        confirmation = adapter.get_vendor_confirmation_number()
        assert confirmation == "GHI789"

    def test_get_vendor_confirmation_number_no_legs(self):
        """Test get_vendor_confirmation_number when no legs."""
        mock_air_pnr = Mock(spec=Air)
        mock_air_pnr.legs = []
        mock_air_pnr.travelerInfos = []
        adapter = AirPnrAdapter(mock_air_pnr)

        assert adapter.get_vendor_confirmation_number() is None

    def test_get_all_flights(self, adapter):
        """Test get_all_flights method."""
        all_flights = adapter.get_all_flights()
        assert len(all_flights) == 1
        assert isinstance(all_flights[0], FlightAdapter)
        assert all_flights[0].airline_code == "DL"

    def test_get_airline_codes(self, adapter):
        """Test get_airline_codes method."""
        airline_codes = adapter.get_airline_codes()
        assert airline_codes == ["DL"]

    def test_get_airline_codes_multiple(self):
        """Test get_airline_codes with multiple airlines."""
        # Create multiple flights with different airlines
        mock_flight1 = Mock(spec=Flight)
        mock_marketing1 = Mock()
        mock_marketing1.airlineCode = "UA"
        mock_flight1.marketing = mock_marketing1

        mock_flight2 = Mock(spec=Flight)
        mock_marketing2 = Mock()
        mock_marketing2.airlineCode = "DL"
        mock_flight2.marketing = mock_marketing2

        mock_flight3 = Mock(spec=Flight)
        mock_marketing3 = Mock()
        mock_marketing3.airlineCode = "UA"  # Duplicate
        mock_flight3.marketing = mock_marketing3

        mock_leg1 = Mock(spec=Leg)
        mock_leg1.flights = [mock_flight1, mock_flight2]
        mock_leg2 = Mock(spec=Leg)
        mock_leg2.flights = [mock_flight3]

        mock_air_pnr = Mock(spec=Air)
        mock_air_pnr.legs = [mock_leg1, mock_leg2]
        mock_air_pnr.travelerInfos = []

        adapter = AirPnrAdapter(mock_air_pnr)
        airline_codes = adapter.get_airline_codes()

        # Should contain unique codes only
        assert set(airline_codes) == {"UA", "DL"}
        assert len(airline_codes) == 2
