"""
Unit tests for Metadata adapter models.

Tests the AdditionalMetadataAdapter, AirportInfoAdapter, and AirlineInfoAdapter implementations.
"""

from unittest.mock import Mock

import pytest

from models.adapters.spotnana.metadata_adapter import (
    AdditionalMetadataAdapter,
    AirlineInfoAdapter,
    AirportInfoAdapter,
)
from models.api.spotnana.trip.models import AdditionalMetadata, AirlineInfo, AirportInfo


class TestAirportInfoAdapter:
    """Test suite for AirportInfoAdapter."""

    @pytest.fixture
    def mock_airport_info(self):
        """Create a mock AirportInfo for testing."""
        mock = Mock(spec=AirportInfo)
        mock.airportCode = "SFO"
        mock.airportName = "San Francisco International Airport"
        mock.cityName = "San Francisco"
        mock.countryCode = "US"
        return mock

    @pytest.fixture
    def adapter(self, mock_airport_info):
        """Create an AirportInfoAdapter with mock data."""
        return AirportInfoAdapter(mock_airport_info)

    def test_airport_code(self, adapter):
        """Test airport_code property."""
        assert adapter.airport_code == "SFO"

    def test_airport_name(self, adapter):
        """Test airport_name property."""
        assert adapter.airport_name == "San Francisco International Airport"

    def test_city_name(self, adapter):
        """Test city_name property."""
        assert adapter.city_name == "San Francisco"

    def test_country_code(self, adapter):
        """Test country_code property."""
        assert adapter.country_code == "US"

    def test_none_values(self):
        """Test properties when values are None."""
        mock_airport_info = Mock(spec=AirportInfo)
        mock_airport_info.airportCode = None
        mock_airport_info.airportName = None
        adapter = AirportInfoAdapter(mock_airport_info)

        assert adapter.airport_code is None
        assert adapter.airport_name is None

    def test_missing_attributes(self):
        """Test properties when attributes are None."""
        mock_airport_info = Mock(spec=AirportInfo)
        mock_airport_info.airportCode = "LAX"
        mock_airport_info.airportName = "Los Angeles International"
        # Set cityName to None to test null handling
        mock_airport_info.cityName = None
        mock_airport_info.countryCode = None
        adapter = AirportInfoAdapter(mock_airport_info)

        assert adapter.airport_code == "LAX"
        assert adapter.city_name is None  # Should handle None values gracefully
        assert adapter.country_code is None


class TestAirlineInfoAdapter:
    """Test suite for AirlineInfoAdapter."""

    @pytest.fixture
    def mock_airline_info(self):
        """Create a mock AirlineInfo for testing."""
        mock = Mock(spec=AirlineInfo)
        mock.airlineCode = "UA"
        mock.airlineName = "United Airlines"
        return mock

    @pytest.fixture
    def adapter(self, mock_airline_info):
        """Create an AirlineInfoAdapter with mock data."""
        return AirlineInfoAdapter(mock_airline_info)

    def test_airline_code(self, adapter):
        """Test airline_code property."""
        assert adapter.airline_code == "UA"

    def test_airline_name(self, adapter):
        """Test airline_name property."""
        assert adapter.airline_name == "United Airlines"


class TestAdditionalMetadataAdapter:
    """Test suite for AdditionalMetadataAdapter."""

    @pytest.fixture
    def mock_airport_info1(self):
        """Create first mock AirportInfo."""
        mock = Mock(spec=AirportInfo)
        mock.airportCode = "SFO"
        mock.airportName = "San Francisco International Airport"
        return mock

    @pytest.fixture
    def mock_airport_info2(self):
        """Create second mock AirportInfo."""
        mock = Mock(spec=AirportInfo)
        mock.airportCode = "LAX"
        mock.airportName = "Los Angeles International Airport"
        return mock

    @pytest.fixture
    def mock_airline_info1(self):
        """Create first mock AirlineInfo."""
        mock = Mock(spec=AirlineInfo)
        mock.airlineCode = "UA"
        mock.airlineName = "United Airlines"
        return mock

    @pytest.fixture
    def mock_airline_info2(self):
        """Create second mock AirlineInfo."""
        mock = Mock(spec=AirlineInfo)
        mock.airlineCode = "AA"
        mock.airlineName = "American Airlines"
        return mock

    @pytest.fixture
    def mock_metadata(self, mock_airport_info1, mock_airport_info2, mock_airline_info1, mock_airline_info2):
        """Create a mock AdditionalMetadata for testing."""
        mock = Mock(spec=AdditionalMetadata)
        mock.airportInfo = [mock_airport_info1, mock_airport_info2]
        mock.airlineInfo = [mock_airline_info1, mock_airline_info2]
        mock.bta = "YMT"
        return mock

    @pytest.fixture
    def adapter(self, mock_metadata):
        """Create an AdditionalMetadataAdapter with mock data."""
        return AdditionalMetadataAdapter(mock_metadata)

    def test_airport_info_property(self, adapter):
        """Test airport_info property returns list of AirportInfoAdapter."""
        airport_info = adapter.airport_info
        assert len(airport_info) == 2
        assert all(isinstance(info, AirportInfoAdapter) for info in airport_info)
        assert airport_info[0].airport_code == "SFO"
        assert airport_info[1].airport_code == "LAX"

    def test_airport_info_empty(self):
        """Test airport_info when None or empty."""
        mock_metadata = Mock(spec=AdditionalMetadata)
        mock_metadata.airportInfo = None
        mock_metadata.airlineInfo = []
        adapter = AdditionalMetadataAdapter(mock_metadata)

        assert adapter.airport_info == []

    def test_airline_info_property(self, adapter):
        """Test airline_info property returns list of AirlineInfoAdapter."""
        airline_info = adapter.airline_info
        assert len(airline_info) == 2
        assert all(isinstance(info, AirlineInfoAdapter) for info in airline_info)
        assert airline_info[0].airline_code == "UA"
        assert airline_info[1].airline_code == "AA"

    def test_airline_info_empty(self):
        """Test airline_info when None or empty."""
        mock_metadata = Mock(spec=AdditionalMetadata)
        mock_metadata.airportInfo = []
        mock_metadata.airlineInfo = None
        adapter = AdditionalMetadataAdapter(mock_metadata)

        assert adapter.airline_info == []

    def test_bta_property(self, adapter):
        """Test bta property."""
        assert adapter.bta == "YMT"

    def test_get_airport_by_code_found(self, adapter):
        """Test get_airport_by_code when airport exists."""
        airport = adapter.get_airport_by_code("LAX")
        assert airport is not None
        assert isinstance(airport, AirportInfoAdapter)
        assert airport.airport_code == "LAX"

    def test_get_airport_by_code_not_found(self, adapter):
        """Test get_airport_by_code when airport doesn't exist."""
        airport = adapter.get_airport_by_code("JFK")
        assert airport is None

    def test_get_airline_by_code_found(self, adapter):
        """Test get_airline_by_code when airline exists."""
        airline = adapter.get_airline_by_code("AA")
        assert airline is not None
        assert isinstance(airline, AirlineInfoAdapter)
        assert airline.airline_code == "AA"

    def test_get_airline_by_code_not_found(self, adapter):
        """Test get_airline_by_code when airline doesn't exist."""
        airline = adapter.get_airline_by_code("DL")
        assert airline is None

    def test_get_airport_name_by_code_found(self, adapter):
        """Test get_airport_name_by_code when airport exists."""
        name = adapter.get_airport_name_by_code("SFO")
        assert name == "San Francisco International Airport"

    def test_get_airport_name_by_code_not_found(self, adapter):
        """Test get_airport_name_by_code when airport doesn't exist."""
        name = adapter.get_airport_name_by_code("JFK")
        assert name is None

    def test_get_airline_name_by_code_found(self, adapter):
        """Test get_airline_name_by_code when airline exists."""
        name = adapter.get_airline_name_by_code("UA")
        assert name == "United Airlines"

    def test_get_airline_name_by_code_not_found(self, adapter):
        """Test get_airline_name_by_code when airline doesn't exist."""
        name = adapter.get_airline_name_by_code("DL")
        assert name is None

    def test_build_airport_code_to_name_map(self, adapter):
        """Test build_airport_code_to_name_map method."""
        airport_map = adapter.build_airport_code_to_name_map()
        expected = {"SFO": "San Francisco International Airport", "LAX": "Los Angeles International Airport"}
        assert airport_map == expected

    def test_build_airport_code_to_name_map_with_missing_data(self):
        """Test build_airport_code_to_name_map with missing airport code or name."""
        # Create mock airports with missing data
        mock_airport1 = Mock(spec=AirportInfo)
        mock_airport1.airportCode = "SFO"
        mock_airport1.airportName = "San Francisco Airport"

        mock_airport2 = Mock(spec=AirportInfo)
        mock_airport2.airportCode = None  # Missing code
        mock_airport2.airportName = "Unknown Airport"

        mock_airport3 = Mock(spec=AirportInfo)
        mock_airport3.airportCode = "LAX"
        mock_airport3.airportName = None  # Missing name

        mock_metadata = Mock(spec=AdditionalMetadata)
        mock_metadata.airportInfo = [mock_airport1, mock_airport2, mock_airport3]
        adapter = AdditionalMetadataAdapter(mock_metadata)

        airport_map = adapter.build_airport_code_to_name_map()
        # Should only include airports with both code and name
        assert airport_map == {"SFO": "San Francisco Airport"}

    def test_build_airline_code_to_name_map(self, adapter):
        """Test build_airline_code_to_name_map method."""
        airline_map = adapter.build_airline_code_to_name_map()
        expected = {"UA": "United Airlines", "AA": "American Airlines"}
        assert airline_map == expected

    def test_build_airline_code_to_name_map_with_missing_data(self):
        """Test build_airline_code_to_name_map with missing airline code or name."""
        # Create mock airlines with missing data
        mock_airline1 = Mock(spec=AirlineInfo)
        mock_airline1.airlineCode = "UA"
        mock_airline1.airlineName = "United Airlines"

        mock_airline2 = Mock(spec=AirlineInfo)
        mock_airline2.airlineCode = None  # Missing code
        mock_airline2.airlineName = "Unknown Airline"

        mock_airline3 = Mock(spec=AirlineInfo)
        mock_airline3.airlineCode = "AA"
        mock_airline3.airlineName = None  # Missing name

        mock_metadata = Mock(spec=AdditionalMetadata)
        mock_metadata.airlineInfo = [mock_airline1, mock_airline2, mock_airline3]
        adapter = AdditionalMetadataAdapter(mock_metadata)

        airline_map = adapter.build_airline_code_to_name_map()
        # Should only include airlines with both code and name
        assert airline_map == {"UA": "United Airlines"}
