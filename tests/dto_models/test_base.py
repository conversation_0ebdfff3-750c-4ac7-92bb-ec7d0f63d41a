# type: ignore
"""
Basic tests for DTO base classes and utilities.

This module provides smoke tests to verify the foundation works correctly
before implementing specific model classes.
"""

from datetime import datetime
from typing import Any

import pytest
from typing_extensions import override

from models.base import BaseApiModel, BaseConverter, BaseDomainModel, BaseExternalModel


class SampleExternalModel(BaseExternalModel):
    """Sample external model for validation."""

    api_field: str | None = None
    amount: float | None = None


class SampleDomainModel(BaseDomainModel):
    """Sample domain model for validation."""

    name: str
    value: int

    @override
    def validate_business_rules(self) -> list[str]:
        errors: list[str] = []
        if self.value < 0:
            errors.append("Value cannot be negative")
        return errors


class SampleApiModel(BaseApiModel):
    """Sample API model for validation."""

    display_name: str
    formatted_value: str


class SampleConverter(BaseConverter[<PERSON>pleExternalModel, SampleDomainModel]):
    """Test converter implementation."""

    @override
    def convert(self, source: SampleExternalModel) -> SampleDomainModel:
        return SampleDomainModel(name=source.api_field or "default", value=int(source.amount or 0))


class TestBaseClasses:
    """Test base DTO classes functionality."""

    def test_external_model_creation(self):
        """Test external model creation and serialization."""
        model = SampleExternalModel(api_field="test", amount=99.99)
        assert model.api_field == "test"
        assert model.amount == 99.99

        # Test safe serialization
        data = model.model_dump_safe()
        assert data["api_field"] == "test"
        assert data["amount"] == 99.99

    def test_external_model_with_extra_fields(self):
        """Test external model handles extra fields gracefully."""
        # Should not raise due to extra='allow'
        data = {"api_field": "test", "amount": 99.99, "unexpected_field": "should_be_allowed"}
        model = SampleExternalModel.model_validate(data)
        assert model.api_field == "test"

    def test_domain_model_timestamps(self):
        """Test domain model timestamp handling."""
        model = SampleDomainModel(name="test", value=42)
        assert isinstance(model.created_at, datetime)
        assert model.updated_at is None

        model.mark_updated()
        assert model.updated_at is not None
        assert model.updated_at > model.created_at

    def test_domain_model_business_rules(self):
        """Test domain model business rule validation."""
        valid_model = SampleDomainModel(name="test", value=42)
        assert valid_model.validate_business_rules() == []

        invalid_model = SampleDomainModel(name="test", value=-1)
        errors = invalid_model.validate_business_rules()
        assert len(errors) == 1
        assert "negative" in errors[0]

    def test_api_model_serialization(self):
        """Test API model serialization for frontend."""
        model = SampleApiModel(display_name="Test Item", formatted_value="$99.99")

        data = model.model_dump_for_api()
        assert data["display_name"] == "Test Item"
        assert data["formatted_value"] == "$99.99"

    def test_converter_functionality(self):
        """Test base converter functionality."""
        converter = SampleConverter()
        external = SampleExternalModel(api_field="test_item", amount=123.45)

        domain = converter.convert(external)
        assert domain.name == "test_item"
        assert domain.value == 123

    def test_converter_safe_conversion(self):
        """Test safe conversion with error handling."""
        converter = SampleConverter()

        def failing_converter(_: Any) -> SampleDomainModel:
            raise ValueError("Conversion failed")

        fallback_model = SampleDomainModel(name="default", value=0)
        result = converter.safe_convert({"test": "data"}, failing_converter, fallback=fallback_model)
        assert result == fallback_model

    def test_converter_safe_get(self):
        """Test safe dictionary access."""
        data = {"user": {"profile": {"name": "John Doe"}, "preferences": ["pref1", "pref2"]}}

        # Test successful nested access
        name: str = str(BaseConverter.safe_get(data, "user.profile.name"))
        assert name == "John Doe"

        # Test missing key with default
        missing: str = str(BaseConverter.safe_get(data, "user.missing.field", default="not_found"))
        assert missing == "not_found"

        # Test array access
        pref: str = str(BaseConverter.safe_get(data, "user.preferences.0"))
        assert pref == "pref1"

        # Test required field missing (should raise)
        with pytest.raises(KeyError):
            _ = BaseConverter.safe_get(data, "user.missing.field", required=True)


if __name__ == "__main__":
    _ = pytest.main([__file__])
