import asyncio
import csv
import io
import json
import time
from collections import defaultdict
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Tuple, TypeVar

from cachetools import TTLCache
from fastapi import HTTPException
from langchain_core.messages import AIMessage, BaseMessage, FunctionMessage

from baml_client import b
from baml_client.type_builder import TypeBuilder
from baml_client.types import (
    FlightPolicy,
    FlightSearchAdditionalCriteria,
    FlightSearchResponse,
    ResponseAllPreferences,
    SeatMatchingResult,
    SeatSelectionForFlight,
    SelectedFlight,
)
from flight_agent.flight_search_constants import (
    PREMIUM_FLIGHT_SEARCH_DESCRIPTION,
    REGULAR_FLIGHT_SEARCH_DESCRIPTION,
)
from flight_agent.flights_tools import FlightSearchTools, alliance_airlines_mapping
from front_of_house_agent.common_models import (
    FlightOption,
    FlightSearchSource,
    FlightSearchType,
    SerpFlightOption,
    SpotnanaFlightOption,
)
from front_of_house_agent.flight_utils import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>m
from front_of_house_agent.serp_common_models import <PERSON>ing
from front_of_house_agent.serp_flight_helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Travel<PERSON>lass
from llm_utils.llm_utils import (
    csv_from_dict,
    determine_is_red_eye,
    determine_time_category,
    generate_flight_keyword,
    get_flight_detail_from_history,
    json_from_dict,
)
from server.database.models.bookings import Booking as BookingDB
from server.database.models.chat_thread import ChatThread
from server.database.models.user import User as UserDB
from server.database.models.user import UserRole
from server.database.models.user_company_travel_policy import UserCompanyTravelPolicy
from server.schemas.authenticate.user import User
from server.schemas.spotnana.flight_statuses import FlightStatuses
from server.services.memory.trips.memory_modules.bookings_memory import BookingsMemory
from server.services.memory.trips.memory_modules.selections_memory import SelectionsMemory
from server.services.memory.utils import BookingOperation
from server.services.trips.bookings import get_bookings_from_pnr_id
from server.services.trips.flight_card import construct_flight_card_dict
from server.services.trips.flight_credits_api import flight_credits_api
from server.services.user.user_activity import UserActivityType, update_user_activity
from server.services.user_profile.loyalty_programs import get_user_profile_flights_loyalty_programs
from server.utils.analytics.analytics import TrackingEvent, TrackingManager
from server.utils.logger import logger
from server.utils.settings import AgentTypes, settings
from server.utils.spotnana_api import SpotnanaHelper
from virtual_travel_agent.common_models import AgentError
from virtual_travel_agent.helpers import (
    console_masks,
    get_current_date_string,
)
from virtual_travel_agent.timings import Timings

T = TypeVar("T", bound=FlightOption)
flight_log_mask = console_masks["flight"]

# Constants
CABIN_ORDER = ["economy", "premium_economy", "business", "first"]

CONFIRMED_STATUS = "CONFIRMED_STATUS"
AIRLINE_CONTROL_STATUS = "AIRLINE_CONTROL_STATUS"

EXCHANGEABLE_BY_OBT = "EXCHANGEABLE_BY_OBT"
EXCHANGEABLE_BY_SUPPORT = "EXCHANGEABLE_BY_SUPPORT"

OTTO_CONTACT_MESSAGE = "This ticket can be only exchanged by calling us, sorry for any inconvenience. If you need to contact Otto directly, please text/sms Otto at 206-222-7007."


def airline_control_reply_message(airline_name: str = "the airline") -> str:
    return f"I'm sorry, but since the flight is departing within 24 hours, you will have to contact {airline_name} directly to change the flight."


cache = TTLCache(maxsize=1, ttl=timedelta(minutes=15).total_seconds())


class FlightBamlHelper:
    def __init__(
        self,
        user: User,
        thread: ChatThread,
        timezone: str | None,
        existing_user_preferences: ResponseAllPreferences | None,
    ):
        self.input_tokens_counter = 0
        self.output_tokens_counter = 0

        self.user = user
        self.thread = thread
        self.timezone = timezone
        self.existing_user_preferences = existing_user_preferences
        self.bookings_memory = BookingsMemory(user_id=str(user.id), thread_id=str(thread.id))
        self.selections_memory = SelectionsMemory(user_id=str(user.id), thread_id=str(thread.id))

    def _apply_serp_cabin_calculation_logic(
        self,
        fe_display_dict: Dict[str, Any],
        flight_option: FlightOption,
        serp_flight_search_params: Dict[str, Any] | None,
    ) -> None:
        """
        Apply cabin calculation logic to main cabin and all nested cabin values in each stop for SERP flights.
        Sets cabin values to the travel class from SERP search params or defaults to "economy" if None.
        """
        cabin_value = (
            TravelClass.to_name(serp_flight_search_params[SerpRequestParam.TRAVEL_CLASS])
            if serp_flight_search_params and serp_flight_search_params.get(SerpRequestParam.TRAVEL_CLASS)
            else "economy"
        )

        # Apply to main cabin
        if fe_display_dict["cabin"] is None:
            fe_display_dict["cabin"] = cabin_value
            logger.info(
                f"No cabin info found in flight option, set to cabin {cabin_value} from search params",
                mask=flight_log_mask,
            )

        # Apply to all nested cabin values in each stop
        for idx in range(len(flight_option.stops)):
            cabin_key = f"fs{idx}_cabin"
            if cabin_key in fe_display_dict and fe_display_dict[cabin_key] is None:
                fe_display_dict[cabin_key] = cabin_value

    def _get_flight_detail(
        self, messages: List[BaseMessage], flight_id: str
    ) -> Tuple[dict | None, str | None, str | None]:
        return get_flight_detail_from_history(messages, flight_id)

    @staticmethod
    async def get_flight_details_and_exchange_info(
        trip_id: str, confirmation_id: str, get_exchangibiity_func
    ) -> tuple[list, list, dict | None]:
        """Extract flight details and exchange information from trip data."""
        trip_detail = await FlightSearchTools.get_trip_details_spotnana(trip_id)

        flights = trip_detail.get("pnrs", [{}])[0].get("data", {}).get("airPnr", {})

        legs = flights.get("legs", [])

        existing_legs = []
        leg_status_list = []

        for leg in legs:
            exist_leg = ""
            leg_status = leg.get("legStatus", None)
            leg_status_list.append(leg_status)
            first_flight = leg.get("flights", [])[0]
            last_flight = leg.get("flights", [])[-1]
            exist_leg += f"{first_flight.get('origin')} to {last_flight.get('destination')}, Duration: {first_flight.get('departureDateTime', {}).get('iso8601')} to {last_flight.get('arrivalDateTime', {}).get('iso8601')}, This leg is in status {leg_status} \n"
            existing_legs.append(exist_leg)

        exchange_info = None
        # When there is at least one leg with status CONFIRMED_STATUS, we need to check the exchange info.
        if any(status == "CONFIRMED_STATUS" for status in leg_status_list):
            data = await get_exchangibiity_func(confirmation_id)

            # TODO(chengxuan.wang): currenlty we only take the first exchange info.
            exchange_info = data.get("ticketToExchangeInfos", [])[0]
            logger.info(json.dumps(data), mask=flight_log_mask)

        return leg_status_list, existing_legs, exchange_info

    def _generate_exchange_agent_response(
        self, leg_status_list: list, existing_legs: list, exchange_info: dict | None = None
    ) -> str:
        exchange_method_allowables = []
        agent_response = ""
        if any(status == "CONFIRMED_STATUS" for status in leg_status_list):
            if exchange_info:
                if exchange_info.get("exchangeState") == "EXCHANGEABLE_BY_OBT":
                    if exchange_info.get("isRefundable") is True:
                        exchange_method_allowables.append("The ticket is refundable")

                    suppiler_preferred_enabled_fields = exchange_info.get("supplierPreferredEnabledFields", {})
                    if suppiler_preferred_enabled_fields.get("travelDate") is True:
                        exchange_method_allowables.append("The travel date can be changed.")
                    if suppiler_preferred_enabled_fields.get("modifyPartialItinerary") is True:
                        exchange_method_allowables.append("Itinerary can be modified.")
                    if suppiler_preferred_enabled_fields.get("sameEndpointRestriction") is True:
                        exchange_method_allowables.append(
                            "Change the endpoint across legs for the return itinerary is allowed."
                        )
                else:
                    agent_response = OTTO_CONTACT_MESSAGE

        if len(exchange_method_allowables) > 0:
            agent_response = "The ticket can be exchanged."

        agent_response += "The existing flights are: \n"
        for index, existing_leg in enumerate(existing_legs):
            prefix = "Outbound flight:" if index == 0 else "Return flight:"
            agent_response += f"{prefix} {existing_leg} \n"

        return agent_response

    async def verify_flight_exchangeable(self, confirmation_id: str, trip_id: str):
        try:
            leg_status_list, existing_legs, exchange_info = await self.get_flight_details_and_exchange_info(
                trip_id, confirmation_id, self.get_exchangibiity
            )

            agent_response = self._generate_exchange_agent_response(leg_status_list, existing_legs, exchange_info)

            t = Timings("BAML: ProcessExchangeDetailsResults")
            result = await b.ProcessExchangeDetailsResults(
                current_date=get_current_date_string(self.timezone),
                results=agent_response,
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("green")
            logger.log_baml()

            message = FunctionMessage(
                content=agent_response,
                name=result.__class__.__name__,
                additional_kwargs={
                    "agent_classification": AgentTypes.EXCHANGE_FLIGHTS,
                    "function_call": {
                        "name": result.__class__.__name__,
                        "arguments": json.dumps(result.model_dump()),
                    },
                },
            )

            return message
        except Exception as e:
            s = str(e)
            if "does not have any ticket that can be exchanged" in s:
                message = FunctionMessage(
                    content="this ticket does not have any ticket that can be exchanged",
                    name=e.__class__.__name__,
                    additional_kwargs={
                        "function_call": {
                            "name": e.__class__.__name__,
                            "arguments": json.dumps({"confirmation_id": confirmation_id}),
                        }
                    },
                )
                return message
            raise e

    @staticmethod
    async def get_exchangibiity(confirmation_id: str) -> dict[str, Any]:
        if confirmation_id in cache:
            return cache[confirmation_id]
        data = await FlightSearchTools.acall_spotnana_api(
            f"{settings.SPOTNANA_HOST}/v2/air/pnrs/{confirmation_id}/exchange-details",
            None,
            None,
            "exchange_details_request_spotnana.json",
            "exchange_details_response_spotnana.json",
        )
        cache[confirmation_id] = data
        return data

    async def exchange_flight_search(
        self,
        confirmation_id: str,
        trip_id: str,
        params: Dict[str, Any],
        flight_search_additional_criteria: FlightSearchAdditionalCriteria | None,
    ):
        try:
            original_booking = await get_bookings_from_pnr_id(confirmation_id)

            trip_detail = await FlightSearchTools.get_trip_details_spotnana(trip_id)
            pnr_data = trip_detail.get("pnrs", [{}])[0].get("data", {})
            flights = pnr_data.get("airPnr", {})

            legs = flights.get("legs", [])

            leg_index = 0
            if (
                params.get("return_flight_want_change_to") is not None
                and params.get("step") == "CHANGE_RETURN_FLIGHT_SEARCH"
            ):
                leg_index = 1

            if leg_index == 0:
                leg_status = legs[0].get("legStatus")
            else:
                leg_status = legs[-1].get("legStatus")

            def create_exchange_flight_message(content: str) -> tuple[FunctionMessage, None]:
                return FunctionMessage(
                    content=content,
                    name="ExchangeFlightSearchResponse",
                    additional_kwargs={
                        "function_call": {
                            "name": "ExchangeFlightSearchResponse",
                            "arguments": json.dumps({"agent_response": content}),
                        },
                        "agent_classification": AgentTypes.EXCHANGE_FLIGHTS,
                    },
                ), None

            if leg_status == AIRLINE_CONTROL_STATUS:
                airline_name = "the airline"  # default
                additional_metadata = pnr_data.get("additionalMetadata", {})
                airline_info_list = additional_metadata.get("airlineInfo", [])
                if airline_info_list:
                    airline_name = airline_info_list[0].get("airlineName", "the airline")
                return create_exchange_flight_message(airline_control_reply_message(airline_name))
            elif leg_status != CONFIRMED_STATUS:
                leg_type = "Outbound" if leg_index == 0 else "Return"
                content = f"{leg_type} flight is in {leg_status}, which can not be exchanged."
                return create_exchange_flight_message(content)

            exchange_method_allowables = []
            agent_response = ""
            data = await self.get_exchangibiity(confirmation_id)

            # TODO(chengxuan.wang): currenlty we only take the first exchange info.
            exchange_info = data.get("ticketToExchangeInfos", [])[0]
            if exchange_info.get("exchangeState") == EXCHANGEABLE_BY_OBT:
                if exchange_info.get("isRefundable") is True:
                    exchange_method_allowables.append("The ticket is refundable")

                suppiler_preferred_enabled_fields = exchange_info.get("supplierPreferredEnabledFields", {})

                if suppiler_preferred_enabled_fields.get("travelDate") is True:
                    exchange_method_allowables.append("The travel date can be changed")
                if suppiler_preferred_enabled_fields.get("modifyPartialItinerary") is True:
                    exchange_method_allowables.append("itinerary can be modified")
                if suppiler_preferred_enabled_fields.get("sameEndpointRestriction") is True:
                    exchange_method_allowables.append(
                        "change the endpoint across legs for the return itinerary is allowed"
                    )
            else:
                agent_response = OTTO_CONTACT_MESSAGE

            logger.info(json.dumps(data), mask=flight_log_mask)

            if len(exchange_method_allowables) == 0:
                agent_response = "Sorry, The ticket can not be exchanged."
                message = FunctionMessage(
                    content=agent_response,
                    name="ExchangeFlightSearchResponse",
                    additional_kwargs={
                        "function_call": {
                            "name": "ExchangeFlightSearchResponse",
                            "arguments": json.dumps({"agent_response": agent_response}),
                        },
                        "agent_classification": AgentTypes.EXCHANGE_FLIGHTS,
                    },
                )
                return message, None
            params["is_original_one_way"] = (
                len(original_booking.get("legs") or []) == 1 or original_booking.get("flight", {}).get("return") is None
            )

            params["confirmation_id"] = confirmation_id

            response = await FlightSearchTools.exchange_search_flights_spotnana(flight_params=json.dumps(params))

        except Exception as e:
            logger.error(f"Error in exchange_flight_search: {e}", mask=flight_log_mask)
            raise e

        flight_response = json.loads(response)

        # save the search_id away so we don't inundate the llm with it only to
        # put it back later
        search_id = flight_response.get("search_id", None)
        flight_choices: List[dict[str, Any]] = []
        if search_id is None:
            new_flight_response = json.dumps(
                {"error_response": flight_response.get("error_response", "No search_id found in response")}
            )
        else:
            # remove the search_id from the response so the llm doesn't look at
            # it
            flight_response.pop("search_id")
            flight_data = None

            # keep only the keys we absolutely need
            # preferred_cabin = flight_params.get('preferred_cabin', 'ECONOMY')
            for index, flight in enumerate(flight_response["flight_choices"], start=0):
                flight_data = {}
                flight_data["index_id"] = index
                flight_data.update(
                    {
                        key: flight.get(key, "")
                        for key in [
                            "origin",
                            "destination",
                            "departure_time",
                            "arrival_time",
                            "airline_code",
                            "flight_number",
                            "number_of_stops",
                            "duration",
                            "aircraft_iata_code",
                            "aircraft_name",
                            "price",
                            "cabin",
                            "fare_option_name",
                            "cancellation_policy",
                            "exchange_policy",
                            "seat_selection_policy",
                            "boarding_policy",
                            "booking_code",
                            "total_distance_miles",
                            "operating_airline_code",
                            "operating_flight_number",
                            "source",
                        ]
                    }
                )

                flight_choices.append(flight_data)

            new_flight_response = csv_from_dict(flight_choices, "exchangeflightsearch_choices_for_llm.csv")
            await TrackingManager.log_event_in_background(
                event_type=TrackingEvent.FLIGHT_SEARCHED,
                user_id=str(self.user.id),
                user_email=self.user.email,
                event_properties={
                    "origin": flight_data.get("origin") if flight_data else None,
                    "destination": flight_data.get("destination") if flight_data else None,
                    "departure_time": flight_data.get("departure_time") if flight_data else None,
                },
            )

        await update_user_activity(
            user_id=str(self.user.id),
            activity_type=UserActivityType.FLIGHT_SEARCH,
        )

        # Extract original flight data based on leg_index
        original_flight_leg = {}
        if original_booking and "flight" in original_booking:
            flight_info = original_booking.get("flight", {})

            if flight_info:
                # Extract the outbound or return flight based on leg_index
                flight_leg = flight_info.get("legs", [{}])[leg_index] or flight_info.get(
                    "outbound" if leg_index == 0 else "return", {}
                )

                if flight_leg:
                    original_flight_leg["cabin"] = flight_leg.get("cabin")
                    original_flight_leg["net_price"] = flight_leg.get("net_price")
                    original_flight_leg["flight_stops"] = flight_leg.get("flight_segments", [{}])[0].get("flight_stops")
                    original_flight_leg["fare_option_name"] = flight_leg.get("fare_option_name")
                    original_flight_leg["cancellation_policy"] = flight_leg.get("cancellation_policy")

        original_flight_str = json.dumps(original_flight_leg)

        additional_criteria = None
        if flight_search_additional_criteria:
            additional_criteria = flight_search_additional_criteria.model_dump_json(exclude_none=True)

        t = Timings("BAML: FindSimilarFlightForExchange")
        results = await b.FindSimilarFlightForExchange(
            input_flight=original_flight_str,
            candidate_flights=new_flight_response,
            additional_criteria=additional_criteria,
            current_date=get_current_date_string(self.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )
        t.print_timing("green")
        logger.log_baml()

        # Transform FlightSearchResponse to match original structure
        preferred_flights = []
        preferred_flights_reasons = []

        # Extract flight choices and reasons from the structured response
        choices = [getattr(results, attr_name) for attr_name in dir(results) if attr_name.startswith("fc_")]
        selected_choices = [choice for choice in choices if choice is not None]
        for choice in selected_choices:
            preferred_flights.append(choice.index_id)
            preferred_flights_reasons.append(choice.reason)

        arguments_obj = results.model_dump()
        arguments_obj["is_outbound_flight_choices"] = leg_index == 0
        arguments_obj["flight_search_type"] = flight_response.get("flight_search_type")
        arguments_obj["is_change_flight"] = True

        flight_choices_after_llm_recommendation: list[Tuple[dict[str, Any], SelectedFlight]] = []
        dedupe_dict: dict[str, list] = {}

        for choice in selected_choices:
            if choice.index_id >= len(flight_choices):
                continue
            flight_option = flight_response["flight_choices"][choice.index_id]
            flight_dedup_key = flight_option.get("dedup_key")

            dedupe_dict.setdefault(flight_dedup_key, []).append((flight_option, choice))

        for flight_dedup_key, flights in dedupe_dict.items():
            cheapest_flight = min(flights, key=lambda x: x[0].get("price", float("inf")))
            flight_choices_after_llm_recommendation.append(cheapest_flight)

        fe_display_flight_choices = []

        for i, (flight_option, choice) in enumerate(flight_choices_after_llm_recommendation):
            fe_display_dict = flight_option
            fe_display_dict["selection_reason"] = choice.reason
            departure_time = flight_option.get("departure_time")
            arrival_time = flight_option.get("arrival_time")

            fe_display_dict["is_red_eye"] = (
                determine_is_red_eye(departure_time, arrival_time) if departure_time and arrival_time else False
            )

            fe_display_flight_choices.append(fe_display_dict)

        self.process_flight_keywords(fe_display_flight_choices)

        if len(fe_display_flight_choices) != len(flight_choices_after_llm_recommendation):
            logger.warning(
                f"Number of final selected flight options {len(fe_display_flight_choices)} does not match "
                f"number of flight choices LLM recommendation {len(flight_choices_after_llm_recommendation)}. "
                f"We dedup the flight options based on the dedup key, so this is expected"
            )

        arguments_obj["flight_choices"] = fe_display_flight_choices
        function_message = FunctionMessage(
            content="",
            name=results.__class__.__name__,
            additional_kwargs={
                "expire_timestamp": (datetime.now(timezone.utc) + settings.FLIGHT_CARD_EXPIRE_DELTA).isoformat(),
                "function_call": {
                    "name": results.__class__.__name__,
                    "arguments": json.dumps(arguments_obj),
                },
                "agent_classification": AgentTypes.EXCHANGE_FLIGHTS,
            },
        )

        raw_tool_output = []
        if flight_choices:
            for index, flight_choice in enumerate(flight_choices):
                flight_choice["selected_by_otto"] = "Yes" if index in preferred_flights else "No"
                raw_tool_output.append(self.filter_raw_tool_output(flight_choice))
        function_message.additional_kwargs["raw_tool_output"] = json.dumps({"flight_choices": raw_tool_output})
        return function_message, search_id

    def dedupe_flight_choices(self, flight_choices: list[Tuple[T, SelectedFlight]]) -> list[Tuple[T, SelectedFlight]]:
        # Group by dedup key
        grouped: dict[str, list] = {}
        for flight in flight_choices:
            key = flight[0].get_flight_option_dedup_key()
            if key not in grouped:
                grouped[key] = []
            grouped[key].append(flight)

        # Keep cheapest from each group
        deduped = []
        for key in grouped:
            cheapest = min(grouped[key], key=lambda x: x[0].total_price)
            deduped.append(cheapest)

        # Maintain original order
        return [d for d in flight_choices if d in deduped]

    async def process_serp_flight_search_result(
        self,
        response: List[Booking],
        message_buffer_strs: list[str],
        travel_context_dict: dict[str, Any],
        default_airline_codes: list[str],
        preferred_airline_codes: list[str],
        airport_code: str,
        is_return_search: bool,
        flight_search_type: FlightSearchType,
        is_premium_cabin_search: bool,
        duration_suggestion_string: str | None,
        serp_flight_search_params: dict[str, Any] | None = None,
    ):
        new_flight_response, new_flight_response_json, serp_flight_options, _ = (
            SerpFlightSearchHelper.process_flight_search_results(response, is_return_search)
        )

        company_admin = (
            await UserDB.from_organization_id_and_role(self.user.organization_id, UserRole.company_admin)
            if self.user.organization_id
            else None
        )
        policy_user_id = company_admin.id if company_admin else self.user.id

        flight_policy = None
        company_policy = await UserCompanyTravelPolicy.from_user_id(policy_user_id)

        has_company_policy = (
            company_policy is not None
            and company_policy.parsed_travel_policy is not None
            and company_policy.parsed_travel_policy.get("flight_policy") is not None
        )
        if has_company_policy:
            assert company_policy is not None and company_policy.parsed_travel_policy is not None
            parsed_flight_policy = company_policy.parsed_travel_policy.get("flight_policy")
            assert parsed_flight_policy is not None
            flight_policy = FlightPolicy(**parsed_flight_policy)

        flight_cabin_from_policy_str = None if flight_policy is None else flight_policy.default_class

        await update_user_activity(
            user_id=str(self.user.id),
            activity_type=UserActivityType.FLIGHT_SEARCH,
            data={"has_company_policy": has_company_policy, "serp": True},
        )

        travel_context_str = json.dumps(travel_context_dict)

        tb = TypeBuilder()
        baml_property_description: str = (
            PREMIUM_FLIGHT_SEARCH_DESCRIPTION if is_premium_cabin_search else REGULAR_FLIGHT_SEARCH_DESCRIPTION
        )

        tb.FlightSearchResponse.add_property("presentation_message", tb.string().optional()).description(
            baml_property_description
        )

        preferred_flight_cabin = (
            ", ".join(travel_context_dict.get("cabin") or []) or flight_cabin_from_policy_str or "economy"
        )
        results = await self.flight_recommendation(
            default_airline_codes=default_airline_codes,
            preferred_airline_codes=preferred_airline_codes,
            flight_option_csv=new_flight_response,
            flight_option_json=new_flight_response_json,
            flight_options=serp_flight_options,
            travel_context_str=travel_context_str,
            preferred_flight_cabin=preferred_flight_cabin,
            airport_code=airport_code,
            message_buffer_str=message_buffer_strs,
            is_premium_cabin_search=is_premium_cabin_search,
            duration_suggestion_string=duration_suggestion_string,
            tb=tb,
        )

        # Transform FlightSearchResponse to match original structure
        preferred_flights: list[int] = []
        preferred_flights_reasons = []

        # Extract flight choices and reasons from the structured response
        choices = [getattr(results, attr_name) for attr_name in dir(results) if attr_name.startswith("fc_")]
        selected_choices = [choice for choice in choices if choice is not None]
        for choice in selected_choices:
            preferred_flights.append(choice.index_id)
            preferred_flights_reasons.append(choice.reason)

        results_dict = results.model_dump()

        # Create arguments object with transformed data
        arguments_obj = {
            "preferred_flights": preferred_flights,
            "preferred_flights_reasons": preferred_flights_reasons,
            "agent_response": results_dict.get("presentation_message") or None,
            "is_outbound_flight_choices": not is_return_search,
            "flight_search_type": flight_search_type.value,
            "segment_index": None,
        }

        arguments_obj = results.model_dump()
        arguments_obj["is_outbound_flight_choices"] = not is_return_search
        arguments_obj["flight_search_type"] = flight_search_type.value
        credits_map = await self._get_credits_map()

        flight_choices_after_llm_recommendation: list[Tuple[SerpFlightOption, SelectedFlight]] = []

        for choice in selected_choices:
            if choice.index_id >= len(serp_flight_options):
                continue
            flight_option = serp_flight_options[choice.index_id]
            flight_choices_after_llm_recommendation.append((flight_option, choice))

        deduped_flight_choices = self.dedupe_flight_choices(flight_choices_after_llm_recommendation)
        if len(deduped_flight_choices) != len(flight_choices_after_llm_recommendation):
            logger.warning(
                f"Number of final selected flight options {len(deduped_flight_choices)} does not match "
                f"number of flight choices LLM recommendation {len(flight_choices_after_llm_recommendation)}. "
                f"We dedup the flight options based on the dedup key, so this is expected"
            )

        fe_display_flight_choices = []

        tasks = []

        for flight_option, choice in deduped_flight_choices:
            fe_display_dict = flight_option.to_fe_display_dict()
            fe_display_dict["selection_reason"] = choice.reason

            departure_time = flight_option.departure_time
            arrival_time = flight_option.arrival_time

            fe_display_dict["is_red_eye"] = (
                determine_is_red_eye(departure_time, arrival_time) if departure_time and arrival_time else False
            )

            # Apply cabin calculation logic to main cabin and all nested cabin values in each stop
            self._apply_serp_cabin_calculation_logic(fe_display_dict, flight_option, serp_flight_search_params)
            self._calculate_flight_credits(fe_display_dict, credits_map)
            all_fares_for_flight = [
                flight
                for flight in serp_flight_options
                if flight.dedup_by_marketing_airline_number_only()
                == flight_option.dedup_by_marketing_airline_number_only()
            ]

            tasks.append(
                asyncio.create_task(
                    self.populate_all_fares(fe_display_dict, all_fares_for_flight, credits_map, flight_policy)
                )
            )

        results = await asyncio.gather(*tasks)
        for fe_display_dict in results:
            if fe_display_dict is not None:
                fe_display_flight_choices.append(fe_display_dict)

        self.process_flight_keywords(fe_display_flight_choices)

        arguments_obj["flight_choices"] = fe_display_flight_choices

        function_message = FunctionMessage(
            content="",
            name="FlightSearchResponse",
            additional_kwargs={
                "expire_timestamp": (datetime.now(timezone.utc) + settings.FLIGHT_CARD_EXPIRE_DELTA).isoformat(),
                "function_call": {
                    "name": "FlightSearchResponse",
                    "arguments": json.dumps(arguments_obj),
                },
                "agent_classification": AgentTypes.FLIGHTS,
            },
        )

        raw_tool_output = []
        if serp_flight_options:
            for index, flight_option in enumerate(serp_flight_options):
                raw_tool_output.append(
                    flight_option.to_raw_tool_output(index in preferred_flights, is_premium_cabin_search)
                )
        function_message.additional_kwargs["raw_tool_output"] = json.dumps({"flight_choices": raw_tool_output})
        function_message.additional_kwargs["function_call"]["associated_search_source"] = FlightSearchSource.SERP.value

        return function_message

    async def populate_all_fares(
        self,
        fe_display_dict: Dict[str, Any],
        all_fares_for_flight: list[SerpFlightOption],
        credits_map: dict[str, Any],
        flight_policy: FlightPolicy | None,
    ) -> Dict[str, Any] | None:
        if not all_fares_for_flight:
            return None
        all_fares_dicts = [fare.to_fe_display_dict() for fare in all_fares_for_flight]
        fe_display_dict["all_fares"] = all_fares_dicts
        for fare_dict in all_fares_dicts:
            self._calculate_flight_credits(fare_dict, credits_map)
        if flight_policy is not None:
            baml_flight_options = [flight_option.to_baml_flight_option() for flight_option in all_fares_for_flight]
            t = Timings("BAML: DetermineFlightPolicyCompliance")
            policy_compliance_results = await b.DetermineFlightPolicyCompliance(
                flight_options=baml_flight_options,
                company_policy="" if not flight_policy else flight_policy.model_dump_json(),
                baml_options={"collector": logger.collector},
            )
            t.print_timing("yellow")
            logger.log_baml()
            for index, fare_dict in enumerate(all_fares_dicts):
                if policy_compliance_results is None or policy_compliance_results.policy_compliance is None:
                    fare_dict["within_policy"] = None
                elif index < len(policy_compliance_results.policy_compliance or []):
                    fare_dict["within_policy"] = (policy_compliance_results.policy_compliance or [])[index]
                else:
                    fare_dict["within_policy"] = None

                if policy_compliance_results is None or policy_compliance_results.policy_compliance_reasons is None:
                    fare_dict["within_or_out_policy_reason"] = None
                elif index < len(policy_compliance_results.policy_compliance_reasons or []):
                    fare_dict["within_or_out_policy_reason"] = (
                        policy_compliance_results.policy_compliance_reasons or []
                    )[index]
                else:
                    fare_dict["within_or_out_policy_reason"] = None

            for fare_dict in all_fares_dicts:
                if fare_dict["id_token_key"] == fe_display_dict["id_token_key"]:
                    fe_display_dict["within_policy"] = fare_dict.get("within_policy")
                    fe_display_dict["within_or_out_policy_reason"] = fare_dict.get("within_or_out_policy_reason")
                    break

        else:
            fe_display_dict["within_policy"] = None
            fe_display_dict["within_or_out_policy_reason"] = None

        return fe_display_dict

    def _group_flights_for_stage1_ranking(
        self, flight_data_csv: str
    ) -> tuple[list[dict[str, Any]], dict[str, list[dict]]]:
        """
        Group flights by dedup_key for Stage 1 ranking.
        Returns: (grouped_csv_for_stage1, flight_groups_dict)
        """

        csv_reader = csv.DictReader(io.StringIO(flight_data_csv))
        flights = list(csv_reader)

        flight_groups = {}
        for flight in flights:
            dedup_key = flight.get("dedup_key", "")
            if dedup_key not in flight_groups:
                flight_groups[dedup_key] = []
            flight_groups[dedup_key].append(flight)

        representative_flights = []
        for dedup_key, group in flight_groups.items():
            representative = min(group, key=lambda f: float(f.get("price", float("inf"))))

            prices = [float(f.get("price", 0)) for f in group if f.get("price")]
            cabin_classes = list(
                set(
                    (f.get("cabin") or f.get("fare_option_name")).upper()
                    for f in group
                    if f.get("cabin") or f.get("fare_option_name")
                )
            )
            fare_options = list(set(f.get("fare_option_name", "") for f in group if f.get("fare_option_name")))

            fare_summary = {
                "min_price": min(prices) if prices else 0,
                "max_price": max(prices) if prices else 0,
                "price_range": f"${min(prices):.0f}-${max(prices):.0f}"
                if prices and len(prices) > 1
                else f"${min(prices):.0f}"
                if prices
                else "N/A",
                "cabin_classes": sorted(cabin_classes),
                "fare_options": sorted(fare_options),
                "total_fare_count": len(group),
            }

            representative_with_summary = representative.copy()
            representative_with_summary["fare_summary"] = (
                f"Price range: {fare_summary['price_range']}, Cabins: {', '.join(fare_summary['cabin_classes'])}"
            )

            representative_flights.append(representative_with_summary)

        logger.info(f"Flight grouping: {len(flights)} total flights grouped into {len(flight_groups)} unique routes")

        return representative_flights, flight_groups

    @staticmethod
    def process_flight_keywords(flights: list[Dict[str, Any]]):
        if not flights:
            return
        time_keywords = []
        for flight in flights:
            departure_time = flight.get("departure_time")
            if departure_time and not flight.get("keyword"):
                keyword = generate_flight_keyword(str(departure_time))
                time_keywords.append(keyword)

        if len(set(k for k in time_keywords if k is not None)) > 1:
            for flight, keyword in zip(flights, time_keywords):
                flight["keyword"] = keyword

        def get_price(flight):
            if flight.get("net_price") is not None:
                return flight["net_price"]
            return flight.get("price", float("inf"))

        # Find cheapest flight and mark with keyword
        zero_net_price_flights = [f for f in flights if f.get("net_price") == 0.0]
        if zero_net_price_flights:
            cheapest_flight = min(zero_net_price_flights, key=lambda f: f.get("price", float("inf")))
            if not all(
                f.get("price", float("inf")) == cheapest_flight.get("price", float("inf"))
                for f in zero_net_price_flights
            ):
                cheapest_flight["keyword"] = "cheapest"
        else:
            cheapest_flight = min(flights, key=get_price)
            if not all(get_price(flight) == get_price(cheapest_flight) for flight in flights):
                cheapest_flight["keyword"] = "cheapest"

        # Find shortest duration among flights
        def get_duration(f):
            return f.get("flight_duration_in_seconds") or f.get("total_duration") or float("inf")

        shortest_duration = min(get_duration(flight) for flight in flights)

        # Mark first shortest flight that isn't cheapest
        for flight in flights:
            if flight is cheapest_flight:
                continue
            if get_duration(flight) == shortest_duration:
                flight["keyword"] = "shortest"
                break

    async def proccess_flight_search_results(
        self,
        flight_response: dict[str, Any],
        message_buffer_strs: list[str],
        travel_context_dict: dict[str, Any],
        is_return_search: bool,
        is_premium_cabin_search: bool,
        airport_code: str,
        default_airline_codes: list[str],
        preferred_airline_codes: list[str],
        current_segment_index: int | None,
        duration_suggestion_string: str | None,
    ):
        # save the search_id away so we don't inundate the llm with it only to
        # put it back later
        try:
            search_id, new_flight_response, new_flight_response_json, spotnana_flight_choices = (
                self.process_flight_response(flight_response)
            )

            # ================================== company policy ==================================
            flight_policy = None

            company_admin = (
                await UserDB.from_organization_id_and_role(self.user.organization_id, UserRole.company_admin)
                if self.user.organization_id
                else None
            )
            policy_user_id = company_admin.id if company_admin else self.user.id
            company_policy = await UserCompanyTravelPolicy.from_user_id(policy_user_id)

            has_company_policy = (
                company_policy is not None
                and company_policy.parsed_travel_policy is not None
                and company_policy.parsed_travel_policy.get("flight_policy") is not None
            )
            if has_company_policy:
                assert company_policy is not None and company_policy.parsed_travel_policy is not None
                parsed_flight_policy = company_policy.parsed_travel_policy.get("flight_policy")
                assert parsed_flight_policy is not None
                flight_policy = FlightPolicy(**parsed_flight_policy)

            await update_user_activity(
                user_id=str(self.user.id),
                activity_type=UserActivityType.FLIGHT_SEARCH,
                data={"has_company_policy": has_company_policy},
            )

            min_duration_seconds = None
            flight_duration_in_seconds_list = [
                flight_choice.get("flight_duration_in_seconds")
                for flight_choice in flight_response.get("flight_choices") or []
                if flight_choice.get("flight_duration_in_seconds")
            ]
            if flight_duration_in_seconds_list:
                min_duration_seconds = min(flight_duration_in_seconds_list)

            flight_cabin_from_policy_str = None if flight_policy is None else flight_policy.default_class

            if flight_policy is not None and flight_policy.exceptions is not None:
                for exception in flight_policy.exceptions:
                    if exception.title is not None:
                        # TODO: need to capture user's title.
                        flight_cabin_from_policy_str = flight_policy.default_class
                        break
                    if (
                        min_duration_seconds is not None
                        and exception.duration_in_hours
                        and min_duration_seconds >= exception.duration_in_hours * 3600
                    ):
                        flight_cabin_from_policy_str = exception.flight_class

            if is_return_search:
                travel_context_dict.pop("outbound_departure_time", None)
                travel_context_dict.pop("outbound_arrival_time", None)
            else:
                travel_context_dict.pop("return_departure_time", None)
                travel_context_dict.pop("return_arrival_time", None)

            travel_context_str = json.dumps(travel_context_dict)
            tb = TypeBuilder()
            baml_property_description: str = (
                PREMIUM_FLIGHT_SEARCH_DESCRIPTION if is_premium_cabin_search else REGULAR_FLIGHT_SEARCH_DESCRIPTION
            )

            tb.FlightSearchResponse.add_property("presentation_message", tb.string().optional()).description(
                baml_property_description
            )

            preferred_flight_cabin = (
                ", ".join(travel_context_dict.get("trip_cabin") or []) or flight_cabin_from_policy_str or "economy"
            )
            results = await self.flight_recommendation(
                default_airline_codes=default_airline_codes,
                preferred_airline_codes=preferred_airline_codes,
                flight_option_csv=new_flight_response,
                flight_option_json=new_flight_response_json,
                flight_options=spotnana_flight_choices,
                travel_context_str=travel_context_str,
                preferred_flight_cabin=preferred_flight_cabin,
                airport_code=airport_code,
                message_buffer_str=[m for m in message_buffer_strs if m.startswith("user")],
                is_premium_cabin_search=is_premium_cabin_search,
                duration_suggestion_string=duration_suggestion_string,
                tb=tb,
            )
            arguments_obj = results.model_dump()

            # check this, the arguments bineg straight json doesn't make sense or
            # isn't useful in that format
            function_message = FunctionMessage(
                content="",
                name=results.__class__.__name__,
                additional_kwargs={
                    "function_call": {
                        "name": results.__class__.__name__,
                        "arguments": results.model_dump_json(),
                    },
                    "agent_classification": AgentTypes.FLIGHTS,
                },
            )

            # Extract flight choices and reasons from the structured response
            choices = [getattr(results, attr_name) for attr_name in dir(results) if attr_name.startswith("fc_")]
            selected_choices = [choice for choice in choices if choice is not None]
            indicies = [choice.index_id for choice in selected_choices]

            # frontend needs this to clear flight cards skeleton if there are no flights.
            arguments_obj["flight_choices"] = []

            if len(selected_choices) > 0 and results.error_response is None:
                credits_map = await self._get_credits_map()
                new_flights = []

                flight_choices_after_llm_recommendation: list[Tuple[SpotnanaFlightOption, SelectedFlight]] = []

                for choice in selected_choices:
                    if choice.index_id >= len(spotnana_flight_choices):
                        continue
                    flight_option = spotnana_flight_choices[choice.index_id]
                    flight_choices_after_llm_recommendation.append((flight_option, choice))

                deduped_flight_choices = self.dedupe_flight_choices(flight_choices_after_llm_recommendation)

                new_flights = []
                for flight_option, choice in deduped_flight_choices:
                    fe_display_dict = flight_option.to_fe_display_dict()
                    fe_display_dict["selection_reason"] = choice.reason

                    departure_time = flight_option.departure_time
                    arrival_time = flight_option.arrival_time
                    fe_display_dict["is_red_eye"] = (
                        determine_is_red_eye(departure_time, arrival_time) if departure_time and arrival_time else False
                    )
                    self._calculate_flight_credits(fe_display_dict, credits_map)
                    all_fares_for_flight = [
                        flight.to_fe_display_dict()
                        for flight in spotnana_flight_choices
                        if flight.dedup_by_marketing_airline_number_only()
                        == flight_option.dedup_by_marketing_airline_number_only()
                    ]
                    fe_display_dict["all_fares"] = all_fares_for_flight
                    for fare_dict in all_fares_for_flight:
                        self._calculate_flight_credits(fare_dict, credits_map)
                        is_with_in = (
                            None
                            if flight_cabin_from_policy_str is None
                            else CABIN_ORDER.index((fare_dict.get("cabin") or "ECONOMY").lower())
                            <= CABIN_ORDER.index(flight_cabin_from_policy_str.lower())
                        )
                        fare_dict["within_policy"] = is_with_in
                        fare_dict["within_or_out_policy_reason"] = (
                            None
                            if is_with_in is None
                            else f"This flight's cabin: {fare_dict.get('cabin')} is {'' if is_with_in else 'not'} winthin the company flight policy: {flight_cabin_from_policy_str}"
                        )
                    for fare_dict in all_fares_for_flight:
                        if fare_dict["id_token_key"] == fe_display_dict["id_token_key"]:
                            fe_display_dict["within_policy"] = fare_dict.get("within_policy")
                            fe_display_dict["within_or_out_policy_reason"] = fare_dict.get(
                                "within_or_out_policy_reason"
                            )
                            break
                    new_flights.append(fe_display_dict)

                self.process_flight_keywords(new_flights)

                # probably can optimize further by doing a csvFromDict on the
                # new_flights list
                if len(new_flights) > 0:
                    arguments_obj["flight_choices"] = new_flights
                # this shoudln't happen I believe because that would mean the
                # indicies returned don't exist in the flight_choices
                else:
                    arguments_obj["flight_choices"] = []
                    arguments_obj["error_response"] = "No flights found for your preferences."

                raw_tool_output = []
                if spotnana_flight_choices:
                    for index, flight_choice in enumerate(spotnana_flight_choices):
                        raw_tool_output.append(
                            flight_choice.to_raw_tool_output(index in indicies, is_premium_cabin_search)
                        )
                function_message.additional_kwargs["raw_tool_output"] = json.dumps({"flight_choices": raw_tool_output})

            arguments_obj["flight_search_type"] = flight_response.get("flight_search_type")
            if is_return_search:
                arguments_obj["is_outbound_flight_choices"] = False
            else:
                arguments_obj["is_outbound_flight_choices"] = True

            if current_segment_index is not None:
                arguments_obj["segment_index"] = current_segment_index

            function_message.additional_kwargs["function_call"]["arguments"] = json.dumps(arguments_obj)
            function_message.additional_kwargs["function_call"]["associated_search_id"] = search_id
            function_message.additional_kwargs["function_call"]["associated_search_source"] = (
                FlightSearchSource.FALLBACK_SPOTNANA.value
            )

        except Exception as e:
            return self.handle_api_errors(e), None, False

        has_flights = "flight_choices" in arguments_obj and len(arguments_obj.get("flight_choices", [])) > 0
        return function_message, search_id, has_flights

    def standardize_airline_codes(self, airline_codes: list[str]) -> list[str]:
        """
        Standardize airline codes by ensuring AS and HA are always included if present.
        """
        standardized_codes = set(airline_codes)
        if "AS" in standardized_codes or "HA" in standardized_codes:
            standardized_codes.update(["AS", "HA"])
        return list(standardized_codes)

    async def flight_recommendation(
        self,
        *,
        default_airline_codes: list[str],
        preferred_airline_codes: list[str],
        flight_option_csv: str,
        flight_option_json: str,
        flight_options: list[T],
        travel_context_str: str,
        preferred_flight_cabin: str,
        airport_code: str,
        message_buffer_str: list[str],
        is_premium_cabin_search: bool,
        duration_suggestion_string: str | None,
        tb: TypeBuilder,
    ):
        airline_codes = (
            preferred_airline_codes if is_premium_cabin_search else (preferred_airline_codes or default_airline_codes)
        )

        airline_codes = self.standardize_airline_codes(airline_codes)
        preferred_airline_codes = self.standardize_airline_codes(preferred_airline_codes)
        default_airline_codes = self.standardize_airline_codes(default_airline_codes)

        user_profile_flights_loyalty_programs = await get_user_profile_flights_loyalty_programs(self.user.id)
        loyalty_programs_json = None
        if user_profile_flights_loyalty_programs and user_profile_flights_loyalty_programs.get("loyaltyPrograms"):
            for program in user_profile_flights_loyalty_programs.get("loyaltyPrograms", []):
                airline = program.pop("IATACode")
                program["airline_code"] = airline
            loyalty_programs_json = json.dumps(user_profile_flights_loyalty_programs.get("loyaltyPrograms"))

        t = Timings("BAML: ProcessFlightSearchResults")
        results = await b.ProcessFlightSearchResults(
            travel_context=travel_context_str,
            results=flight_option_json,
            current_date=get_current_date_string(self.timezone),
            preferred_flight_cabin=preferred_flight_cabin,
            alliance_airlines=json.dumps(alliance_airlines_mapping),
            user_preferred_airline_codes=preferred_airline_codes,
            airport_default_airline_codes=default_airline_codes,
            airport_code=airport_code,
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            messages=message_buffer_str,
            is_premium_search=is_premium_cabin_search,
            airport_to_destination_duration=duration_suggestion_string,
            loyalty_programs=loyalty_programs_json,
            format="JSON",
            baml_options={"collector": logger.collector, "tb": tb},
        )

        t.print_timing("green")
        logger.log_baml()

        async def csv_shadow_execution(csv_data: str, main_result: FlightSearchResponse):
            t_csv = Timings("BAML: ProcessFlightSearchResults (CSV)")
            csv_results = await b.ProcessFlightSearchResults(
                travel_context=travel_context_str,
                results=flight_option_csv,
                current_date=get_current_date_string(self.timezone),
                preferred_flight_cabin=preferred_flight_cabin,
                alliance_airlines=json.dumps(alliance_airlines_mapping),
                user_preferred_airline_codes=preferred_airline_codes,
                airport_default_airline_codes=default_airline_codes,
                airport_code=airport_code,
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                messages=message_buffer_str,
                is_premium_search=is_premium_cabin_search,
                airport_to_destination_duration=duration_suggestion_string,
                loyalty_programs=loyalty_programs_json,
                format="CSV",
                baml_options={"collector": logger.collector, "tb": tb},
            )
            t_csv.print_timing("blue")
            logger.log_baml()
            main_list = await self.post_recommendation(main_result, flight_options)
            secondary_list = await self.post_recommendation(csv_results, flight_options)
            await self._store_flight_search_evaluation_results(
                main_results=main_list,
                two_stage_results=secondary_list,
                rank_only_results=[],
                travel_context_str=travel_context_str,
                airline_codes=airline_codes,
                flight_option_csv=flight_option_csv,
                flight_option_json=flight_option_json,
            )

        asyncio.create_task(csv_shadow_execution(flight_option_csv, results))

        return results

    async def post_recommendation(
        self,
        results: FlightSearchResponse,
        flight_options: list[T],
    ):
        preferred_flights: list[int] = []
        preferred_flights_reasons = []

        # Extract flight choices and reasons from the structured response
        choices = [getattr(results, attr_name) for attr_name in dir(results) if attr_name.startswith("fc_")]
        selected_choices = [choice for choice in choices if choice is not None]
        for choice in selected_choices:
            preferred_flights.append(choice.index_id)
            preferred_flights_reasons.append(choice.reason)

        credits_map = await self._get_credits_map()

        flight_choices_after_llm_recommendation: list[Tuple[FlightOption, SelectedFlight]] = []

        for choice in selected_choices:
            if choice.index_id >= len(flight_options):
                continue
            flight_option = flight_options[choice.index_id]
            flight_choices_after_llm_recommendation.append((flight_option, choice))

        deduped_flight_choices = self.dedupe_flight_choices(flight_choices_after_llm_recommendation)

        fe_display_flight_choices = []

        for flight_option, choice in deduped_flight_choices:
            fe_display_dict = flight_option.to_fe_display_dict()
            fe_display_dict["selection_reason"] = choice.reason
            departure_time = getattr(choice, "departure_time", None)
            arrival_time = getattr(choice, "arrival_time", None)
            fe_display_dict["is_red_eye"] = (
                determine_is_red_eye(departure_time, arrival_time) if departure_time and arrival_time else False
            )
            self._calculate_flight_credits(fe_display_dict, credits_map)
            fe_display_flight_choices.append(fe_display_dict)

        self.process_flight_keywords(fe_display_flight_choices)

        return list(
            map(
                lambda flight_choice: construct_flight_card_dict(
                    flight_choice,
                ),
                fe_display_flight_choices,
            )
        )

    def process_flight_response(self, flight_response):
        search_id = flight_response.get("search_id", None)
        spotnana_flight_csv_choices: List[dict[str, Any]] = []
        spotnana_flight_choices: list[SpotnanaFlightOption] = []
        if search_id is None:
            new_flight_response_csv = json.dumps(
                {"error_response": flight_response.get("error_response", "No search_id found in response")}
            )
            new_flight_response_json = json.dumps(
                {"error_response": flight_response.get("error_response", "No search_id found in response")}
            )
            return search_id, new_flight_response_csv, new_flight_response_json, spotnana_flight_choices
        else:
            # remove the search_id from the response so the llm doesn't look at it
            flight_response.pop("search_id")

            # keep only the keys we absolutely need
            for index, flight in enumerate(flight_response["flight_choices"], start=0):
                flight_data = {}
                flight_data["index_id"] = index
                flight_data.update(
                    {
                        key: flight.get(key, "")
                        for key in [
                            "origin",
                            "destination",
                            "departure_time",
                            "arrival_time",
                            "airline_code",
                            "flight_number",
                            "number_of_stops",
                            "duration",
                            "aircraft_iata_code",
                            "aircraft_name",
                            "price",
                            "cabin",
                            "fare_option_name",
                            "cancellation_policy",
                            "exchange_policy",
                            "seat_selection_policy",
                            "boarding_policy",
                            "booking_code",
                            "total_distance_miles",
                            "operating_airline_code",
                            "operating_flight_number",
                            "source",
                        ]
                    }
                )
                flight_data["departure_time_category"] = (
                    f"{determine_time_category(flight_data['departure_time'])} departure"
                )
                flight_data["arrival_time_category"] = f"{determine_time_category(flight_data['arrival_time'])} arrival"

                spotnana_flight_option = SpotnanaFlightOption.from_spotnan_dict(flight)
                flight_data["dedup_key"] = spotnana_flight_option.dedup_by_marketing_airline_number_only()
                spotnana_flight_choices.append(spotnana_flight_option)
                if flight_data.get("cabin") != "BASIC_ECONOMY":
                    # for llm recommendation we only want to show flights that are not basic economy
                    spotnana_flight_csv_choices.append(flight_data)

            new_flight_response_csv = csv_from_dict(spotnana_flight_csv_choices, "flightsearch_choices_for_llm.csv")
            new_flight_response_json = json_from_dict(spotnana_flight_csv_choices, "flightsearch_choices_for_llm.json")
        return search_id, new_flight_response_csv, new_flight_response_json, spotnana_flight_choices

    def filter_raw_tool_output(self, flight_choice: dict[str, Any]) -> str:
        """
        In the following format, construct the raw tool output:
        [
        "DL282\tSEA\tSFO... | DL942\tSFO\tLAX...",
        "DL282\tSEA\tSFO... | DL942\tSFO\tLAX...",
        ]
        """
        output = []
        # headers of this leg
        output.append(
            "\t".join(
                [
                    "selected_by_otto",
                    "marketing_flight",
                    "price",
                    "number_of_stops",
                    "duration",
                    "cabin",
                    "fare_option_name",
                    "operating_flight",
                    "booking_code",
                    "source",
                ]
            )
        )
        output.append(
            "\t".join(
                [
                    str(flight_choice.get("selected_by_otto")),
                    str(flight_choice.get("airline_code", "") + " " + flight_choice.get("flight_number", "")),
                    str(flight_choice.get("price")),
                    str(flight_choice.get("duration")),
                    str(flight_choice.get("number_of_stops", 0)),
                    str(flight_choice.get("cabin")),
                    str(flight_choice.get("fare_option_name")),
                    str(
                        flight_choice.get("operating_airline_code", "")
                        + " "
                        + flight_choice.get("operating_flight_number", "")
                    ),
                    str(flight_choice.get("booking_code")),
                    str(flight_choice.get("source")),
                ]
            )
        )
        # headers of segments
        output.append(
            "\t".join(
                [
                    "marketing_flight",
                    "operating_flight",
                    "origin",
                    "destination",
                    "aircraft_name",
                    "duration",
                    "departure_time",
                    "arrival_time",
                    "cabin",
                    "booking_code",
                ]
            )
        )
        for idx in range(0, 3):
            # for each lay over...
            if flight_choice.get(f"fs{idx}_origin"):
                output.append(
                    "\t".join(
                        [
                            str(
                                flight_choice.get(f"fs{idx}_airline_code", "")
                                + flight_choice.get(f"fs{idx}_flight_number")
                            ),
                            str(
                                flight_choice.get(f"fs{idx}_operating_airline_code", "")
                                + flight_choice.get(f"fs{idx}_operating_flight_number")
                            ),
                            str(flight_choice.get(f"fs{idx}_origin")),
                            str(flight_choice.get(f"fs{idx}_destination")),
                            str(flight_choice.get(f"fs{idx}_aircraft_name")),
                            str(flight_choice.get(f"fs{idx}_duration")),
                            str(flight_choice.get(f"fs{idx}_departure_time")),
                            str(flight_choice.get(f"fs{idx}_arrival_time")),
                            str(flight_choice.get(f"fs{idx}_cabin")),
                            str(flight_choice.get(f"fs{idx}_booking_code")),
                        ]
                    )
                )
        return " | ".join(output)

    async def exchange_flight_validate_itinerary(
        self,
        confirmation_id: str,
        trip_id: str,
        params: Dict[str, Any],
        seat_selections: list[SeatSelectionForFlight] | None,
    ):
        # todo: consider moving search_id into the instance of FlightBamlHelper
        params["user_email"] = self.user.email
        params["user_id"] = self.user.id
        params["trip_id"] = trip_id

        company_admin_user: UserDB | None = None
        if self.user.organization_id:
            company_admin_user = await UserDB.from_organization_id_and_role(
                self.user.organization_id, UserRole.company_admin
            )

        # flight_params["search_id"] = state.get('search_id', None)
        try:
            t = Timings("FlightSearchTools: exchangeflight_validation_spotnana")
            response = await FlightSearchTools.exchangeflight_validation_spotnana(
                flight_params=json.dumps(params),
                user=self.user,
                seat_selections=seat_selections,
                admin_user=company_admin_user,
            )
            t.print_timing("green")

        except Exception as e:
            raise e

        booking_response = json.loads(response)

        t = Timings("BAML: ProcessExchangeFlightValidationResults")
        results = await b.ProcessExchangeFlightValidationResults(
            travel_context=params.get("travel_context_str") or "",
            results=response,
            current_date=get_current_date_string(self.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )
        t.print_timing("green")
        logger.log_baml()

        arguments_obj = results.model_dump()

        # stuff tool responses back into model being returned
        arguments_obj["selected_flights"] = booking_response.get("selected_flights", [])

        arguments_obj["error_response"] = booking_response.get("error_response", None)
        arguments_obj["status"] = booking_response.get("status", None)

        function_message = FunctionMessage(
            content="",
            name=results.__class__.__name__,
            additional_kwargs={
                "function_call": {
                    "name": results.__class__.__name__,
                    "arguments": json.dumps(arguments_obj),
                },
                "agent_classification": AgentTypes.EXCHANGE_FLIGHTS,
            },
        )

        return function_message, booking_response.get("valid_change_booking_id")

    async def exchange_flight_booking(self, trip_id: str, params: Dict[str, Any]) -> FunctionMessage:
        try:
            url = f"{settings.SPOTNANA_HOST}/v2/air/modify-book"
            booking_id = params.get("valid_change_flight_booking_id", None)

            payload = {
                "bookingId": booking_id,
                "tripData": {"tripId": {"id": trip_id}},
            }

            t = Timings("FlightSearchTools: modify_request_spotnana")
            data = await FlightSearchTools.acall_spotnana_api(
                url=url,
                data=payload,
                request_filename="modify_request_spotnana.json",
                response_filename="modify_response_spotnana.json",
            )
            t.print_timing("green")

            trip_details = await FlightSearchTools.get_trip_details_spotnana(trip_id)

        except Exception as e:
            raise e

        confirmation_number = FlightSearchTools.get_vendor_confirmation_number(trip_details)

        t = Timings("BAML: ProcessExchangeFlightBookingResults")
        results = await b.ProcessExchangeFlightBookingResults(
            travel_context=params.get("travel_context_str") or "",
            messages=params.get("message_buffer_strs") or [],
            results=json.dumps(data),
            details=json.dumps(trip_details),
            current_date=get_current_date_string(self.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            confirmation_number=confirmation_number or "",
            baml_options={"collector": logger.collector},
        )
        t.print_timing("green")
        logger.log_baml()

        arguments_obj = results.model_dump()

        arguments_obj["error_response"] = data.get("error_response", None)

        function_message = FunctionMessage(
            content="",
            name=results.__class__.__name__,
            additional_kwargs={
                "function_call": {
                    "name": results.__class__.__name__,
                    "arguments": json.dumps(arguments_obj),
                },
                "agent_classification": AgentTypes.EXCHANGE_FLIGHTS,
            },
        )
        return function_message

    # MARK: ================================== Cancel Flight ========================================

    async def verify_ticket_cancellable(self, confirmation_id: str, trip_id: str, message_buffer_strs: List[str]):
        def _parse_fare(option: Dict[Any, Any], option_id: str) -> Dict[str, Any]:
            original_total_price: float = option.get("totalFare", {}).get("base", {}).get("amount", 0.0) + option.get(
                "totalFare", {}
            ).get("tax", {}).get("amount", 0.0)

            total_refund_in_cash: float = option.get("refund", {}).get("base", {}).get("amount", 0.0) + option.get(
                "refund", {}
            ).get("tax", {}).get("amount", 0.0)

            total_refund_in_flight_credits: float = option.get("flightCredits", {}).get("totalCreditAmount", {}).get(
                "base", {}
            ).get("amount", 0.0) + option.get("flightCredits", {}).get("totalCreditAmount", {}).get("tax", {}).get(
                "amount", 0.0
            )

            option_type = ""
            if total_refund_in_cash == 0:
                option_type = "FLIGHT_CREDITS"
            elif total_refund_in_flight_credits == 0:
                option_type = "CASH"

            return {
                # "action": f"I want to choose {option_id} as way to cancel.",
                "option_id": option_id,
                "orginalPrice": {
                    "base": option.get("totalFare", {}).get("base", {}).get("amount", 0.0),
                    "tax": option.get("totalFare", {}).get("tax", {}).get("amount", 0.0),
                    "total": original_total_price,
                },
                "nonRefundable": {
                    "base": option.get("nonRefundable", {}).get("base", {}).get("amount", 0.0),
                    "tax": option.get("nonRefundable", {}).get("tax", {}).get("amount", 0.0),
                    "total": option.get("nonRefundable", {}).get("base", {}).get("amount", 0.0)
                    + option.get("nonRefundable", {}).get("tax", {}).get("amount", 0.0),
                },
                "cancellationFees": {
                    "airline": option.get("cancellationFee", {}).get("base", {}).get("amount", 0.0)
                    + option.get("cancellationFee", {}).get("tax", {}).get("amount", 0.0),
                    "merchant": option.get("merchantFee", {}).get("base", {}).get("amount", 0.0)
                    + option.get("merchantFee", {}).get("tax", {}).get("amount", 0.0),
                    "total": option.get("cancellationFee", {}).get("base", {}).get("amount", 0.0)
                    + option.get("cancellationFee", {}).get("tax", {}).get("amount", 0.0)
                    + option.get("merchantFee", {}).get("base", {}).get("amount", 0.0)
                    + option.get("merchantFee", {}).get("tax", {}).get("amount", 0.0),
                },
                "refund": total_refund_in_cash + total_refund_in_flight_credits,
                "option_type": option_type,
                "deduction": option.get("nonRefundable", {}).get("base", {}).get("amount", 0.0)
                + option.get("nonRefundable", {}).get("tax", {}).get("amount", 0.0)
                + option.get("cancellationFee", {}).get("base", {}).get("amount", 0.0)
                + option.get("cancellationFee", {}).get("tax", {}).get("amount", 0.0)
                + option.get("merchantFee", {}).get("base", {}).get("amount", 0.0)
                + option.get("merchantFee", {}).get("tax", {}).get("amount", 0.0),
            }

        url = f"{settings.SPOTNANA_HOST}/v2/trips/{trip_id}/pnrs/{confirmation_id}/cancellation-details"

        data = await FlightSearchTools.acall_spotnana_api(
            url,
            None,
            None,
            "cancellation_details_request_spotnana.json",
            "cancellation_details_response_spotnana.json",
        )

        air_cancellation_info = data.get("cancellationDetails", {}).get("air", {})

        parsed_options = []

        cancel_state = air_cancellation_info.get("cancellationState", "CANCELLATION_INFO_NOT_AVAILABLE")

        cancellation_options = air_cancellation_info.get("cancellationOptions", [])
        for cancellation_option in cancellation_options:
            option = _parse_fare(cancellation_option.get("fareInfo", {}), cancellation_option.get("optionId"))
            parsed_options.append(option)

        t = Timings("BAML: ProcessCancelOptionResults")
        result = await b.ProcessCancelOptionResults(
            cancel_state=cancel_state,
            cancel_options=json.dumps({"cancel_options": parsed_options}),
            cancel_details=json.dumps(air_cancellation_info),
            current_date=get_current_date_string(self.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )
        t.print_timing("green")
        logger.log_baml()

        argument_obj = result.model_dump()
        argument_obj["cancel_options"] = parsed_options

        message = FunctionMessage(
            content=result.agent_response,
            name=data.__class__.__name__,
            additional_kwargs={
                "function_call": {
                    "name": result.__class__.__name__,
                    "arguments": json.dumps(argument_obj),
                },
                "agent_classification": AgentTypes.CANCEL_FLIGHTS,
            },
        )

        return message

    async def cancel_flight(
        self, trip_id: str, airline_confirmation_id: str, cancel_option_id: str, messages: List[str]
    ):
        try:
            url = f"{settings.SPOTNANA_HOST}/v2/air/cancel-pnr"

            payload = {
                "pnrId": airline_confirmation_id,
                "optionId": cancel_option_id,
            }

            t = Timings("FlightSearchTools: cancel_request_spotnana")
            data = await FlightSearchTools.acall_spotnana_api(
                url,
                payload,
                None,
                "cancelpnr_request_spotnana.json",
                "cancelpnr_response_spotnana.json",
            )
            t.print_timing("green")

            # update the original flight to be cancelled.

            await BookingDB.update_fields(
                {"content.confirmation_id": airline_confirmation_id},
                {"content.status": FlightStatuses.PENDING_CANCEL.value, "status": FlightStatuses.PENDING_CANCEL.name},
            )

            status = data.get("status")
            if not status:
                # booking["error_response"] = arguments_obj["error_response"]
                status = "FAILED"

            # booking["error_response"] = arguments_obj["error_response"]
            # await self.handle_flight_cancel_memory(existing_pnr, booking)

            trip_details = await FlightSearchTools.get_trip_details_spotnana(trip_id=trip_id)

        except Exception as e:
            raise e

        t = Timings("BAML: ProcessCancelResults")
        processed_result = await b.ProcessCancelResults(
            messages=messages,
            current_date=get_current_date_string(self.timezone),
            status=status,
            details=json.dumps(trip_details),
            travel_context="",
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )
        t.print_timing("green")
        logger.log_baml()

        # function_message = FunctionMessage(
        #     content=processed_result.agent_response,
        #     name="CancelFlightResponse",
        #     additional_kwargs={
        #         "function_call": {
        #             "name": "CancelFlightResponse",
        #             "arguments": json.dumps(arguments_obj),
        #         },
        #         "agent_classification": AgentTypes.CANCEL_FLIGHTS,
        #     },
        # )

        return AIMessage(
            processed_result.agent_response,
            additional_kwargs={
                "agent_classification": AgentTypes.CANCEL_FLIGHTS,
            },
        )

    def _has_error_response(self, error_response: Any) -> bool:
        """Check if there is a valid error response.

        Args:
            error_response: The error response to check

        Returns:
            bool: True if there is a valid error response, False otherwise
        """
        if error_response is None:
            return False

        if isinstance(error_response, str):
            # Handle empty string, "None", "none" (case insensitive)
            return error_response.strip().lower() not in ("", "none")

        return True

    async def handle_flight_cancel_memory(self, confirmation_id, booking):
        if self._has_error_response(booking.get("error_response")):
            return

        flight_data = booking.get("flight", {})

        await self.bookings_memory.store_flight_booking_memory(
            confirmation_id,
            booking.get("airline_confirmation_number"),
            flight_data,
            BookingOperation.CANCELLED,
        )

    async def get_matched_preference_available_seats(
        self,
        preferred_seat: List[str],
        confirmation_id: str | None = None,
        trip_id: str | None = None,
        search_id: str | None = None,
        itinerary_id: str | None = None,
        flight_index: int | None = None,
    ):
        try:
            traveler_search = await FlightSearchTools.traveler_search_spotnana(self.user.email)
            traveler_read = await FlightSearchTools.traveler_read_spotnana(traveler_search["results"][0]["id"])
            user_guid = traveler_read.get("traveler", {}).get("userOrgId", {}).get("userId", {}).get("id")

            seat_maps = await FlightSearchTools.seat_map_spotnana(
                searchId=search_id,
                itineraryId=itinerary_id,
                loyaltyInfos=None,
                user_guid=user_guid,
                pnrId=confirmation_id,
            )

            flight_seat_map_ids = seat_maps.get("travelerSeatMaps", [])[0].get("flightSeatMapIds", [])
            if len(flight_seat_map_ids) == 1 and flight_index is None:
                logger.info("Only one flight seat map found, can set flight_index to 0")
                flight_index = 0
            flight_seat_map_id = flight_seat_map_ids[flight_index]

            flights = None
            if trip_id is not None:
                trip_detail = await FlightSearchTools.get_trip_details_spotnana(trip_id)
                flights = FlightSearchTools.flatten_flights_details_in_trip(trip_detail)
            elif search_id is not None and itinerary_id is not None:
                itinerary = await FlightSearchTools.get_selected_itinerary_spotnana(search_id, itinerary_id)
                flights = FlightSearchTools.map_flights_in_itin(itinerary.get("itinerary", {}))

            seat_map_list = [seat for seat in seat_maps.get("seatMaps") if seat.get("seatMapId") == flight_seat_map_id]

            seat_map_csv = ""
            if len(seat_map_list) == 0 or len(seat_map_list[0].get("cabinSections", [])) == 0:
                seat_map_csv = ""
            else:
                seat_map_csv, _ = FlightSearchTools.convert_seat_map_to_csv(seat_map_list[0])
        except Exception as e:
            logger.error(f"Error when getting seat map from Spotnana: {e}")
            raise e

        assert flights, "Flights should not be None or empty"

        t = Timings("BAML: DoAvaiableSeatSelection")
        response = await b.DoAvaiableSeatSelection(
            seat_map=seat_map_csv,
            preferred_seat=",".join(preferred_seat),
            current_flight=json.dumps(flights[flight_index or 0]),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            messages=[],
            baml_options={"collector": logger.collector},
        )
        t.print_timing("green")
        logger.log_baml()

        return FunctionMessage(
            content="",
            name="SeatSelection",
            additional_kwargs={
                "function_call": {
                    "name": "SeatSelection",
                    "arguments": json.dumps(response.model_dump()),
                },
                "agent_classification": AgentTypes.FLIGHTS if itinerary_id else AgentTypes.CHANGE_SEAT,
            },
        )

    # this function is used in FOH agent.
    async def seat_selection_for_booked(
        self,
        confirmation_id: str,
        trip_id: str,
        preferred_seat: List[str],
        is_seat_number_provide_by_traveler: bool,
        selected_seats: List[dict[str, str]],
    ):
        try:
            traveler_search = await FlightSearchTools.traveler_search_spotnana(self.user.email)
            traveler_read = await FlightSearchTools.traveler_read_spotnana(traveler_search["results"][0]["id"])
            user_guid = traveler_read.get("traveler", {}).get("userOrgId", {}).get("userId", {}).get("id")

            seat_maps = await FlightSearchTools.seat_map_spotnana(
                searchId=None,
                itineraryId=None,
                loyaltyInfos=None,
                user_guid=user_guid,
                pnrId=confirmation_id,
            )

            trip_detail = await FlightSearchTools.get_trip_details_spotnana(trip_id)
            flights = FlightSearchTools.flatten_flights_details_in_trip(trip_detail)
            all_flights_in_readable = [FlightSearchTools.flight_to_readable_string(flight) for flight in flights]
            function_message = None

        except Exception as e:
            raise e

        if is_seat_number_provide_by_traveler:
            t = Timings("BAML: SummarizeSeatSelectionForChange")
            result = await b.SummarizeSeatSelectionForChange(
                selected_seats=json.dumps(selected_seats),
                all_flights=json.dumps(flights),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("green")
            logger.log_baml()

            function_message = FunctionMessage(
                content="",
                name="SeatSelection",
                additional_kwargs={
                    "function_call": {
                        "name": "SeatSelection",
                        "arguments": json.dumps(
                            {
                                "agent_response": result,
                                "total_flights": len(flights),
                                "all_flights": ",".join(all_flights_in_readable),
                                "selected_seats": selected_seats,
                            }
                        ),
                    },
                    "agent_classification": AgentTypes.EXCHANGE_FLIGHTS,
                },
            )
            return function_message

        selected_seats_list = []
        for index, flight in enumerate(flights):
            flight_seat_map_ids = seat_maps.get("travelerSeatMaps", [])[0].get("flightSeatMapIds", [])
            flight_seat_map_id = flight_seat_map_ids[index]
            seat_map_list = [seat for seat in seat_maps.get("seatMaps") if seat.get("seatMapId") == flight_seat_map_id]

            seat_map_csv = ""
            if len(seat_map_list) == 0 or len(seat_map_list[0].get("cabinSections", [])) == 0:
                seat_map_csv = ""
            else:
                seat_map_csv, _ = FlightSearchTools.convert_seat_map_to_csv(seat_map_list[0])

            if not seat_map_csv:
                result = SeatMatchingResult(
                    free_seat=None,
                    paid_seat=None,
                    agent_response="or this flight, the airline does not allow people to choose their seats. Do you want to go to next step?",
                    seat_selection_reason=None,
                )
            else:
                t = Timings("BAML: DoSeatSelection")
                result = await b.DoSeatSelection(
                    seat_map=seat_map_csv,
                    preferred_seat=",".join(preferred_seat),
                    current_flight=json.dumps(flight),
                    all_flights=json.dumps(flights),
                    next_flight=None,
                    allow_paid_seat=True,  # Add allow_paid_seat parameter
                    self_intro=settings.OTTO_SELF_INTRO,
                    convo_style=settings.OTTO_CONVO_STYLE,
                    baml_options={"collector": logger.collector},
                )
                t.print_timing("green")
                logger.log_baml()

            selected_seats_list.append(result.model_dump())

        t = Timings("BAML: SummarizeSeatSelectionForChange")
        result = await b.SummarizeSeatSelectionForChange(
            selected_seats=json.dumps(selected_seats_list),
            all_flights=json.dumps(flights),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )
        t.print_timing("green")
        logger.log_baml()

        function_message = FunctionMessage(
            content="",
            name="SeatSelection",
            additional_kwargs={
                "function_call": {
                    "name": "SeatSelection",
                    "arguments": json.dumps(
                        {
                            "agent_response": result,
                            "total_flights": len(flights),
                            "all_flights": ",".join(all_flights_in_readable),
                            "selected_seats": selected_seats_list,
                        }
                    ),
                },
                "agent_classification": AgentTypes.CHANGE_SEAT,
            },
        )

        return function_message

    async def update_seat(
        self,
        confirmation_id: str,
        selected_seats: List[dict[str, str]],
    ):
        try:
            company_admin_user: UserDB | None = None
            if self.user.organization_id:
                company_admin_user = await UserDB.from_organization_id_and_role(
                    self.user.organization_id, UserRole.company_admin
                )

            traveler_tasks = [FlightSearchTools.get_spotnana_traveler_search_and_read(self.user.email)]
            if company_admin_user:
                traveler_tasks.append(FlightSearchTools.get_spotnana_traveler_search_and_read(company_admin_user.email))
            (_, traveler_read), *rest = await asyncio.gather(*traveler_tasks)
            (_, admin_traveler_read) = rest[0] if company_admin_user else (None, None)

            user_guid = traveler_read.get("traveler", {}).get("userOrgId", {}).get("userId", {}).get("id")
            payment_source_id = SpotnanaHelper.get_payment_source_id(traveler_read, admin_traveler_read)

            user_org_id = traveler_read.get("traveler", {}).get("userOrgId", {})

            payload_seats = [
                {
                    "flightIndex": item.get("flight_index", -1),
                    "seatNumber": item.get("seat_number"),
                    "price": item.get("price"),
                }
                for item in selected_seats
            ]

        except Exception as e:
            logger.error(f"Error in update_seat: {e}")
            raise e

        t = Timings("FlightSearchTools: traveler_seat_map spotnana")
        url = f"{settings.SPOTNANA_HOST}/v2/air/pnrs/{confirmation_id}/update"
        data = None
        seat_maps = await FlightSearchTools.seat_map_spotnana(
            searchId=None,
            itineraryId=None,
            loyaltyInfos=None,
            user_guid=user_guid,
            pnrId=confirmation_id,
        )
        for seat in payload_seats:
            seat_price = seat.get("price", 0)
            seat_currency = "USD"  # Default currency

            booking_payment_details = {}
            if seat_price > 0 and payment_source_id:
                booking_payment_details = {
                    "bookingTravelerPaymentDetails": [
                        {
                            "selectedFormOfPayments": [
                                {
                                    "paymentItems": [{"itemType": "SEAT", "fareComponent": ["BASE", "TAX"]}],
                                    "selectedPaymentSources": [
                                        {
                                            "paymentSourceId": payment_source_id,
                                            "amount": {
                                                "amount": seat_price,
                                                "currencyCode": seat_currency,
                                                "convertedAmount": seat_price,
                                                "convertedCurrency": seat_currency,
                                                "otherCoinage": [],
                                            },
                                        }
                                    ],
                                }
                            ]
                        }
                    ]
                }

            payload = {
                "pnrId": confirmation_id,
                "pnrRemarks": [],
                "seatMapResponseId": seat_maps.get("seatMapResponseId"),
                "travelerInfo": [
                    {
                        "traveler": {
                            "travelerId": user_guid,
                            "travelerInfo": {
                                # "isActive": traveler_read.get("traveler", {}).get("isAction", False),
                                # "persona": traveler_read.get("traveler", {}).get("persona", {}),
                                # "tier": traveler_read.get("traveler", {}).get("tier", {}),
                                # "travelerPersonalInfo": traveler_read.get("traveler", {}).get("travelerPersonalInfo", {}),
                                # "user": traveler_read.get("traveler", {}).get("user", {}),
                                "userOrgId": user_org_id,
                                # "userBusinessInfo": traveler_read.get("traveler", {}).get("userBusinessInfo", {}),
                            },
                        },
                        "seats": [seat],
                        "updateTypes": ["SEAT"],
                    }
                ],
                "bookingPaymentDetails": booking_payment_details,
            }
            try:
                data = await FlightSearchTools.acall_spotnana_api(
                    url,
                    payload,
                    None,
                    "update_pnr_request_spotnana.json",
                    "update_pnr_response_spotnana.json",
                )
            except Exception as e:
                logger.error(f"Error calling spotnana API to update PNR: {e}")
                data = str(e)
                break
        t.print_timing("green")

        t = Timings("BAML: SummarizeSeatUpdateResult")
        # `data` could be None. Should we pass in some hint to LLM? or skip summarization as no selected seats?
        response = await b.SummarizeSeatUpdateResult(
            result=json.dumps(data),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )
        t.print_timing("green")
        logger.log_baml()

        return FunctionMessage(
            content="",
            name=response.__class__.__name__,
            additional_kwargs={
                "function_call": {
                    "name": response.__class__.__name__,
                    "arguments": json.dumps(
                        {
                            "agent_response": response,
                        }
                    ),
                },
                "agent_classification": AgentTypes.CHANGE_SEAT,
            },
        )

    def handle_api_errors_and_update_state(self, e: Exception):
        return {"errors": [self.handle_api_errors(e)]}

    def handle_api_errors(self, e: Exception):
        if isinstance(e, HTTPException) and isinstance(e.detail, dict) and "errorMessages" in e.detail:
            return AgentError.from_spotnana_error(from_=__name__, error_response=e.detail)
        else:
            return AgentError.from_exception(from_=__name__, exception=e)

    # ======================= flight credits =======================

    async def _get_credits_map(self) -> dict:
        user_unused_credits = await flight_credits_api.get_user_unused_credits(self.user.email)
        credits_map = defaultdict(list)
        for credits in user_unused_credits:
            credits_map[credits.get("extra", {}).get("airlineCode", "")].append(credits)
        return credits_map

    def _calculate_flight_credits(self, flight: dict[str, Any], credits_map: dict) -> dict[str, Any]:
        if credits_list := credits_map.get(flight["airline_code"], []):
            # TODO: currently only choose the first available ticket.
            credits = credits_list[0].get("credits", {}).get("amount", 0)
            flight["credits"] = credits
            flight["net_price"] = max(flight.get("price", 0) - credits, 0)

        return flight

    async def _store_flight_search_evaluation_results(
        self,
        main_results: list[dict[str, Any]],
        two_stage_results: list[dict[str, Any]],
        rank_only_results: list[dict[str, Any]],
        travel_context_str: str,
        airline_codes: list[str],
        flight_option_csv: str,
        flight_option_json: str,
    ):
        """Store flight search evaluation results in MongoDB for comparison analysis."""

        from server.utils.mongo_connector import flight_search_evaluation_collection
        from server.utils.settings import settings

        try:
            comparison_document = {
                "trip_id": self.thread.id,
                "user_email": self.user.email,
                "environment": settings.OTTO_ENV.upper(),
                "created_at": int(time.time()),
                "metadata": {
                    "travel_context_str": travel_context_str,
                    "airline_codes": airline_codes,
                    "flight_option_csv": flight_option_csv,
                },
                "results": {
                    "main_results": {
                        "raw": main_results,
                        "method": "ProcessFlightSearchResults",
                    },
                    "two_stage_results": {
                        "raw": two_stage_results,
                        "method": "SelectFareOptions",
                    },
                    "rank_only_results": {
                        "raw": rank_only_results,
                        "method": "RankFlightOptions",
                    },
                },
            }
            if flight_search_evaluation_collection is not None:
                await flight_search_evaluation_collection.insert_one(comparison_document)
            logger.info(f"Stored flight search evaluation results for trip_id: {self.thread.id}")
        except Exception as e:
            logger.error(f"Failed to store flight search evaluation results: {e}")
